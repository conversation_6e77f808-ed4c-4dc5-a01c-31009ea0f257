# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-06-26 21:30+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"\n"
"<p>Dear\n"
"% if object.partner_id.is_company and object.child_ids:\n"
"    ${object.partner_id.child_ids[0].name}\n"
"% else :\n"
"    ${object.partner_id.name}\n"
"% endif\n"
",</p><p>\n"
"Here is a ${object.state in ('draft', 'sent') and 'request for quotation' or "
"'purchase order confirmation'} <strong>${object.name}</strong>\n"
"% if object.partner_ref:\n"
"    with reference: ${object.partner_ref}\n"
"% endif\n"
"% if object.origin:\n"
"    (RFQ origin: ${object.origin})\n"
"% endif\n"
"amounting <strong>${object.amount_total} ${object.currency_id.name}</"
"strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"<p>If you have any question, do not hesitate to contact us.</p>\n"
"<p>Best regards,</p>\n"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_module_stock_dropshipping
#, fuzzy
msgid ""
"\n"
"Creates the dropship Route and add more complex tests\n"
"-This installs the module stock_dropshipping."
msgstr ""
"\n"
"Permite crear una ruta de envío triangulado y agregar test mas complejos- "
"Esto instala el módulo stock_dropshipping"

#. module: purchase
#: code:addons/purchase/stock.py:81
#, python-format
msgid " Buy"
msgstr "Comprar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_count
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_count
msgid "# Purchases"
msgstr "# Compras"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_supplier_invoice_count
msgid "# Vendor Bills"
msgstr "# de Facturas del Proveedor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_count
msgid "# of Invoices"
msgstr "# de Facturas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_nbr_lines
msgid "# of Lines"
msgstr "Nº de líneas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_purchase_order_count
msgid "# of Purchase Order"
msgstr "# de Orden de Compra"

#. module: purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase_done
msgid "${object.company_id.name} Order (Ref ${object.name or 'n/a' })"
msgstr "${object.company_id.name} Pedido (Ref ${object.name or 'n/a' })"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Date Req.</strong>"
msgstr "<strong>Fecha de Solicitud.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr "<strong>Descripción</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Expected Date</strong>"
msgstr "<strong>Fecha Esperada</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Net Price</strong>"
msgstr "<strong>Precio Neto</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Fecha de Pedido:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Our Order Reference:</strong>"
msgstr "<strong>Nuestra Referencia de Pedido:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr "<strong>Cantidad</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Dirección de Envío:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr "<strong>Impuestos</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Total Without Taxes</strong>"
msgstr "<strong>Total sin impuestos</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Precio Unitario</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference</strong>"
msgstr "<strong>Su referencia de Pedido</strong>"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_invoice_purchase_id
msgid "Add Purchase Order"
msgstr "Agregar Orden de Compra"

#. module: purchase
#: model:res.groups,name:purchase.group_advance_bidding
msgid "Advance bidding process"
msgstr "Proceso de licitiación anticipada"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_advance_purchase_requisition
msgid "Advanced Calls for Tenders"
msgstr "Llamadas avanzadas para Licitantes"

#. module: purchase
#: selection:purchase.config.settings,group_advance_purchase_requisition:0
msgid "Advanced call for tender (choose products from different RFQ)"
msgstr ""
"Llamadas avanzada para licitante(Escoja los productos de diferentes SDP)"

#. module: purchase
#: selection:purchase.config.settings,module_stock_dropshipping:0
msgid "Allow suppliers to deliver directly to your customers"
msgstr "Permite a los proveedores enviar directamente a sus clientes"

#. module: purchase
#: selection:purchase.config.settings,group_manage_vendor_price:0
msgid "Allow using and importing vendor pricelists"
msgstr "Permite usar e importar listas de precios de proveedores"

#. module: purchase
#: selection:purchase.config.settings,module_purchase_requisition:0
msgid ""
"Allow using call for tenders to get quotes from multiple suppliers (advanced)"
msgstr ""
"Permitir realizar proceso de licitación y solicitar presupuestos a varios "
"proveedores"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_group_costing_method
msgid "Allows you to compute product cost price based on average cost."
msgstr ""
"Permitir calcular el precio costo del producto basado en el costo promedio"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_group_uom
msgid ""
"Allows you to select and maintain different units of measure for products."
msgstr ""
"Permite seleccionar y mantener diferentes unidades de medidas para los "
"productos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"An administrator can set up default Terms and conditions in your Company "
"settings."
msgstr ""
"Un administrador puede asignar los Términos y Condiciones por defecto en la "
"compañía"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_account_analytic_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_account_analytic_id
msgid "Analytic Account"
msgstr "Cuenta analítica"

#. module: purchase
#: model:res.groups,name:purchase.group_analytic_accounting
msgid "Analytic Accounting for Purchases"
msgstr "Cuentas analíticas para Compras"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
msgid "Apply"
msgstr "Aplicar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_date_approve
msgid "Approval Date"
msgstr "Fecha de Aprobación"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Approve Order"
msgstr "Aprobar orden"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_procurement_ids
msgid "Associated Procurements"
msgstr "Abastecimientos Asociados"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_invoice_line_purchase_id
msgid ""
"Associated Purchase Order. Filled in automatically when a PO is chosen on "
"the vendor bill."
msgstr ""
"Ordenes de Compra Asociadas. Llenadas automáticamente cuando una orden de "
"compra es escogida en la factura del proveedor"

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_average_delivery_time
msgid "Average Delivery Time"
msgstr "Tiempo de Entrega Promedio"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_average
msgid "Average Price"
msgstr "Precio Promedio"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_qty_invoiced
msgid "Billed Qty"
msgstr "Cantidad Facturada"

#. module: purchase
#: code:addons/purchase/purchase.py:717 code:addons/purchase/stock.py:75
#: model:stock.location.route,name:purchase.route_warehouse0_buy
#, python-format
msgid "Buy"
msgstr "Comprar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_warehouse_buy_pull_id
msgid "Buy rule"
msgstr "Regla de Compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_calendar
msgid "Calendar View"
msgstr "Vista calendario"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_module_purchase_requisition
msgid "Calls for Tenders"
msgstr "Llamadas para Licitantes"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_module_purchase_requisition
#, fuzzy
msgid ""
"Calls for tenders are used when you want to generate requests for quotations "
"to several vendors for a given set of products.\n"
"You can configure per product if you directly do a Request for Quotation to "
"one vendor or if you want a Call for Tenders to compare offers from several "
"vendors."
msgstr ""
"Llamadas para Licitantes es usado cuando usted quiere generar solicitudes de "
"presupuestos a varios proveedores para un listado de productos\n"
"Usted puede configurar por producto si se requiere directamente una "
"Solicitud de Presupuesto para un Proveedor o si usted desea una licitación "
"para comparar varias ofertas entre varios proveedores"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_ok
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_ok
#: model_terms:ir.ui.view,arch_db:purchase.product_template_search_view_purchase
msgid "Can be Purchased"
msgstr "Puede ser Comprado"

#. module: purchase
#: code:addons/purchase/purchase.py:733
#, python-format
msgid ""
"Can not cancel a procurement related to a purchase order. Please cancel the "
"purchase order first."
msgstr ""
"Usted no puede cancelar un abastecimiento relacionado con una orden de "
"compra. Por favor cancele la orden de compra primero para proceder"

#. module: purchase
#: code:addons/purchase/stock.py:78
#, python-format
msgid "Can't find any generic Buy route."
msgstr "No es posible encontrar ninguna ruta de Compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
msgid "Cancel"
msgstr "Cancelar"

#. module: purchase
#: selection:purchase.order,state:0 selection:purchase.report,state:0
msgid "Cancelled"
msgstr "Cancelado"

#. module: purchase
#: code:addons/purchase/purchase.py:612
#, python-format
msgid "Cannot delete a purchase order line which is in state '%s'."
msgstr "No puede borrar una línea de orden de compra con estado '%s'."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Click here to record a vendor bill."
msgstr "De clic aquí para registrar la factura del proveedor"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_invoice_pending
msgid "Click to create a draft invoice."
msgstr "Clic para crear una factura en borrador"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Click to create a quotation that will be converted into a purchase order."
msgstr ""
"Clic para crear un presupuesto que luego será convertido en una orden de "
"compra"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "Click to create a request for quotation."
msgstr "Clic para crear una solicitud de presupuesto"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid "Click to define a new product."
msgstr "Clic para definir un nuevo producto"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_open_invoice
msgid "Click to record a vendor bill related to this purchase."
msgstr "Clic para registrar a un proveedor relacionado con esta compra"

#. module: purchase
#: model:web.tip,description:purchase.purchase_tip_1
#, fuzzy
msgid "Click to scrap products."
msgstr "Clic para definir un nuevo producto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_commercial_partner_id
msgid "Commercial Entity"
msgstr "Entidad Comercial"

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Compañía"

#. module: purchase
#: code:addons/purchase/purchase.py:254
#, python-format
msgid "Compose Email"
msgstr "Escribir Email"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Configuración"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
msgid "Configure Purchases"
msgstr "Configurar Compras"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr "Confirmar Orden"

#. module: purchase
#: selection:res.company,po_double_validation:0
msgid "Confirm purchase orders in one step"
msgstr "Confirmar ordenes de compra en un solo paso"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_control
msgid "Control"
msgstr "Control"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_method
msgid "Control Purchase Bills"
msgstr "Control de Facturas de Compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_costing_method
msgid "Costing Methods"
msgstr "Métodos de Costeo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_create_uid
msgid "Created by"
msgstr "Creado por:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_create_date
msgid "Created on"
msgstr "Creado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_currency_id
msgid "Currency"
msgstr "Moneda"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_date_approve
msgid "Date Approved"
msgstr "Fecha aprobación"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report_date_order
msgid "Date on which this document has been created"
msgstr "Fecha en la que fue creado este documento."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_delay_pass
msgid "Days to Deliver"
msgstr "Días para entregar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_delay
msgid "Days to Validate"
msgstr "Días a validar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_type_id
msgid "Deliver To"
msgstr "Enviar a"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Deliveries & Invoices"
msgstr "Envíos y Facturas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_date_order
#: model:ir.model.fields,help:purchase.field_purchase_order_line_date_order
msgid ""
"Depicts the date where the Quotation should be validated and converted into "
"a purchase order."
msgstr ""
"Representa la fecha cuando el presupuesto debió ser validado y convertido en "
"una orden de compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_name
msgid "Description"
msgstr "Descripción"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_default_location_dest_id_usage
msgid "Destination Location Type"
msgstr "Tipo de ubicación de destino"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_report_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: purchase
#: selection:purchase.report,state:0
msgid "Done"
msgstr "Realizado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_double_validation_amount
msgid "Double validation amount"
msgstr "Monto para doble validación"

#. module: purchase
#: selection:purchase.order,state:0
msgid "Draft PO"
msgstr "PO Borrador"

#. module: purchase
#: selection:purchase.report,state:0
msgid "Draft RFQ"
msgstr "Borrador SDP"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_dest_address_id
msgid "Drop Ship Address"
msgstr "Dirección de Envío Triangulado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_module_stock_dropshipping
msgid "Dropshipping"
msgstr "Envío Triangulado"

#. module: purchase
#: model:ir.model,name:purchase.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Asistente para composición de Email"

#. module: purchase
#: model:ir.model,name:purchase.model_survey_mail_compose_message
#, fuzzy
msgid "Email composition wizard for Survey"
msgstr "Asistente para composición de Email"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_invoice_purchase_id
msgid ""
"Encoding help. When selected, the associated purchase order lines are added "
"to the vendor bill. Several PO can be selected."
msgstr ""
"Codificación de Ayuda. Cuando usted selecciona, las líneas de orden de "
"compra asociada son agregados a la factura del proveedor. Varias Ordenes de "
"Compra pueden ser seleccionadas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Extended Filters"
msgstr "Filtros Extendidos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_fiscal_position_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_fiscal_position_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Fiscal Position"
msgstr "Tipos de Contribuyentes"

#. module: purchase
#: selection:res.company,po_double_validation:0
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr "Tener 2 niveles de aprobación para confirmar la orden de compra"

#. module: purchase
#: model:web.tip,description:purchase.purchase_tip_3
msgid "Get all the shipments related to this order."
msgstr "Obtener todos los envíos relacionados con esta orden."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_weight
msgid "Gross Weight"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Agrupar por"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr "Ocultar líneas canceladas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_id
msgid "ID"
msgstr "ID"

#. module: purchase
#: model:web.tip,description:purchase.purchase_tip_2
msgid ""
"If a product has been broken or damaged during the transport, you can scrap "
"it with this button."
msgstr ""
"Si un producto se ha roto o dañado durante el transporte, usted puede "
"desecharlo con ete botón."

#. module: purchase
#: code:addons/purchase/purchase.py:186
#, python-format
msgid "In order to delete a purchase order, you must cancel it first."
msgstr ""
"Para poder borrar la orden de compra, usted debe poder cancelarla primero"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_group_advance_purchase_requisition
msgid ""
"In the process of a public tendering, you can compare the tender lines and "
"choose for each requested product which quantity you will buy from each bid."
msgstr ""
"En el proceso de una licitación pública, usted puede comparar entre líneas "
"licitantes y escoger cada producto requerido con la cantidad que comprará de "
"cada oferta"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_action_picking_tree_in_move
msgid "Incoming  Products"
msgstr "Productos Entrantes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Incoming Shipments"
msgstr "Productos Entrantes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_incoterm_id
msgid "Incoterm"
msgstr "Incoterm"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Términos de comercio internacional son una serie de términos comerciales "
"predefinidos usados en las transacciones internacionales"

#. module: purchase
#: model:ir.model,name:purchase.model_account_invoice
msgid "Invoice"
msgstr "Factura"

#. module: purchase
#: model:ir.model,name:purchase.model_account_invoice_line
msgid "Invoice Line"
msgstr "Línea de factura"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_invoice_lines
msgid "Invoice Lines"
msgstr "Detalle de factura"

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "Invoice Received"
msgstr "Factura Recibida"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_status
msgid "Invoice Status"
msgstr "Estado de Factura"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Invoiced"
msgstr "Facturado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_ids
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices"
msgstr "Facturas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices and Incoming Shipments"
msgstr "Facturas y Envíos Entrantes"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_report___last_update
msgid "Last Modified on"
msgstr "Fecha de modificación"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_write_uid
msgid "Last Updated by"
msgstr "Ultima Actualización por"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_write_date
msgid "Last Updated on"
msgstr "Actualizado en"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_double_validation
msgid "Levels of Approvals"
msgstr "Niveles de Aprobación"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
msgid "Location & Warehouse"
msgstr "Ubicación y Bodega"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#, fuzzy
msgid "Lock Bills"
msgstr "Facturas de Proveedor"

#. module: purchase
#: selection:purchase.order,state:0
msgid "Locked"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_manage_vendor_price
msgid "Manage Vendor Price"
msgstr "Administrar precios del proveedor"

#. module: purchase
#: selection:purchase.config.settings,group_manage_vendor_price:0
msgid "Manage vendor price on the product form"
msgstr "Administrar precio de proveedor en el formulario del producto"

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Manager"
msgstr "Gerente"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Manual Invoices"
msgstr "Facturas manuales"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for procuring products, they will be scheduled that many days earlier "
"to cope with unexpected vendor delays."
msgstr ""
"Margen de error para los tiempos de entrega del proveedor. Cuando el sistema "
"genera ordenes de compra por re abastecimiento de productos, estas son "
"programadas con varios días de anterioridad para cubrir demoras no esperadas "
"del proveedor"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_double_validation_amount
msgid "Minimum amount for which a double validation is required"
msgstr "Monto mínimo para realizar doble validación es requerido"

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_monthly_purchases
msgid "Monthly Purchases"
msgstr "Compras Mensuales"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Most propositions of purchase orders are created automatically\n"
"                by Odoo based on inventory needs."
msgstr ""
"La mayor parte de la ordenes de compra son creadas automáticamente por Odoo, "
"basado en las necesidades de inventario"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "New Mail"
msgstr "Nuevo Correo"

#. module: purchase
#: selection:purchase.config.settings,group_product_variant:0
msgid "No variants on products"
msgstr "El producto no tiene variantes"

#. module: purchase
#: code:addons/purchase/purchase.py:893
#, python-format
msgid ""
"No vendor associated to product %s. Please set one to fix this procurement."
msgstr ""
"No existe vendedor asociado al producto %s. Por favor asigne uno para "
"corregir el abastecimiento"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Not Invoiced"
msgstr "No Facturado"

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "Not purchased"
msgstr "No comprado"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Notas"

#. module: purchase
#: selection:product.template,purchase_method:0
msgid "On ordered quantities"
msgstr "Cantidades Ordenadas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_purchase_method
#: model:ir.model.fields,help:purchase.field_product_template_purchase_method
msgid ""
"On ordered quantities: Invoice this product based on ordered quantities.\n"
"On received quantities: Invoice this product based on received quantity."
msgstr ""

#. module: purchase
#: selection:product.template,purchase_method:0
msgid "On received quantities"
msgstr "Cantidades recibidas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_report_date_order
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Order Date"
msgstr "Fecha del Pedido"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_order_line
msgid "Order Lines"
msgstr "Líneas del pedido"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order Month"
msgstr "Orden Mensual"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_name
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Reference"
msgstr "Referencia del pedido"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_state
msgid "Order Status"
msgstr "Estado del pedido"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order of Day"
msgstr "Orden del día"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Orders"
msgstr "Pedidos"

#. module: purchase
#: model:ir.model,name:purchase.model_mail_mail
msgid "Outgoing Mails"
msgstr "Emails salientes"

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase_done
msgid "PO_${(object.name or '').replace('/','_')}"
msgstr "PO_${(object.name or '').replace('/','_')}"

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_partner_id
msgid "Partner"
msgstr "Empresa"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_country_id
msgid "Partner Country"
msgstr "País del Tercero"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Partner's Country"
msgstr "País del Tercero"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_payment_term_id
msgid "Payment Term"
msgstr "Término de pago"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_category_property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase.field_product_product_property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase.field_product_template_property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "Precio de la cuenta diferencia"

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_price_per_supplier
msgid "Price Per Vendor"
msgstr "Precio por Proveedor"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config_pricelist
msgid "Pricelists"
msgstr "Lista de precios"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr "Imprimir SDP"

#. module: purchase
#: model:ir.model,name:purchase.model_procurement_order
msgid "Procurement"
msgstr "Abastecimiento"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_group_id
msgid "Procurement Group"
msgstr "Grupo de Abastecimiento"

#. module: purchase
#: model:ir.model,name:purchase.model_procurement_rule
msgid "Procurement Rule"
msgstr "Regla de Abastecimiento"

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Product"
msgstr "Producto"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_category_config_purchase
msgid "Product Categories"
msgstr "Categorías de productos"

#. module: purchase
#: model:ir.model,name:purchase.model_product_category
#: model:ir.model.fields,field_description:purchase.field_purchase_report_category_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product Category"
msgstr "Categoría de producto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_unit_quantity
msgid "Product Quantity"
msgstr "Cantidad de Producto"

#. module: purchase
#: model:ir.model,name:purchase.model_product_template
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_tmpl_id
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_product_variant
msgid "Product Variants"
msgstr "Variante de Producto"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Products"
msgstr "Productos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_standard
msgid "Products Value"
msgstr "Valor productos"

#. module: purchase
#: selection:purchase.config.settings,group_product_variant:0
msgid ""
"Products can have several attributes, defining variants (Example: size, "
"color,...)"
msgstr ""
"Los productos pueden tener varios atributos, definiendo variantes (Por "
"ejemplo: tamaño, color, ...)"

#. module: purchase
#: selection:purchase.config.settings,group_uom:0
msgid "Products have only one unit of measure (easier)"
msgstr "Los productos tiene una única unidad de medida (Más fácil)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr "Proveer una doble validación como mecanismo para la compra"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
msgid "Purchase"
msgstr "Compra"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_order_report_all
msgid "Purchase Analysis"
msgstr "Análisis de compras"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid ""
"Purchase Analysis allows you to easily check and analyse your company "
"purchase history and performance. From this menu you can track your "
"negotiation performance, the delivery performance of your vendors, etc."
msgstr ""
"El análisis de compras le permite facilitar la verificación y analizar el "
"rendimiento e historial de las compras de la compañía. De este menú usted "
"puede dar seguimiento el rendimiento de sus negociaciones, el cumplimiento "
"de las entregas de sus proveedores, etc"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_lead
msgid "Purchase Lead Time"
msgstr "Plazo de tiempo de compra"

#. module: purchase
#: model:ir.actions.report.xml,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_invoice_line_purchase_id
#: model:ir.model.fields,field_description:purchase.field_procurement_order_purchase_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
#: selection:purchase.order,state:0 selection:purchase.report,state:0
#: model:res.request.link,name:purchase.req_link_purchase_order
msgid "Purchase Order"
msgstr "Orden de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order Confirmation #"
msgstr "Confirmación de Orden de Compra #"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Order Fiscal Position"
msgstr "Posición Fiscal de la Orden de Compra"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase.field_account_invoice_line_purchase_line_id
#: model:ir.model.fields,field_description:purchase.field_procurement_order_purchase_line_id
#: model:ir.model.fields,field_description:purchase.field_stock_move_purchase_line_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Purchase Order Line"
msgstr "Línea pedido de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
msgid "Purchase Order Lines"
msgstr "Líneas pedido de compra"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.model.fields,field_description:purchase.field_stock_picking_purchase_id
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Orders"
msgstr "Pedidos de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Orders Statistics"
msgstr "Estadísticas ordenes de compra"

#. module: purchase
#: code:addons/purchase/purchase.py:614
#, python-format
msgid "Purchase order line deleted."
msgstr "Línea de orden de compra borrada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Purchase orders that include lines not invoiced."
msgstr "Pedidos de compra que incluyen líneas no facturadas."

#. module: purchase
#: selection:purchase.config.settings,module_purchase_requisition:0
msgid ""
"Purchase propositions trigger draft purchase orders to a single supplier"
msgstr ""
"Las sugerencia de compra crean ordenes de compra en borrador para un solo "
"proveedor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_warehouse_buy_to_resupply
msgid "Purchase to resupply this warehouse"
msgstr "Compra para re abastecer esta bodega"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_negociation
msgid "Purchase-Standard Price"
msgstr "Precio compra-estándar"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_line_product_tree
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "Purchases"
msgstr "Compras"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_report
msgid "Purchases Orders"
msgstr "Pedidos de compra"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"Asigne una dirección si usted desea enviar directamente desde el proveedor "
"hacia el cliente. De otra manera mantener sin asignar para que lo envíe su "
"compañía"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Quotations"
msgstr "Cotizaciones"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_approved
msgid "RFQ Approved"
msgstr "SDP Aprobada"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_confirmed
msgid "RFQ Confirmed"
msgstr "SDP Confirmada"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_done
msgid "RFQ Done"
msgstr "SDP Realizado"

#. module: purchase
#: selection:purchase.order,state:0 selection:purchase.report,state:0
msgid "RFQ Sent"
msgstr "SDP Enviada"

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase
msgid "RFQ_${(object.name or '').replace('/','_')}"
msgstr "SDP_${(object.name or '').replace('/','_')}"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr "SDP y Ordenes de Compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Print RFQ"
msgstr "Re-imprimir SDP"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Send RFQ by Email"
msgstr "Re-enviar SDP por correo"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Receive Products"
msgstr "Recibir Productos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_qty_received
msgid "Received Qty"
msgstr "Ctdad. Recibida"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_open_picking
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_count
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_ids
msgid "Receptions"
msgstr "Recepciones"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Reference"
msgstr "Referencia"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_uom
msgid "Reference Unit of Measure"
msgstr "Unidad de Medida de Referencia"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a "
"sale order or an internal procurement request)"
msgstr ""
"Referencia del documento que generó esta orden de compra(p.e. un pedido de "
"venta o un abastecimiento automático)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_partner_ref
msgid ""
"Reference of the sales order or bid sent by the vendor. It's used to do the "
"matching when you receive the products as this reference is usually written "
"on the delivery order sent by your vendor."
msgstr ""
"Referencia del pedido de venta o cotización enviada por el proveedor, Esta "
"es usada para coordinar la recepción de los productos, esta es usualmente "
"escrita en los documentos de envío entregados por el proveedor"

#. module: purchase
#: model:ir.actions.report.xml,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Request for Quotation"
msgstr "Solicitud de presupuesto"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr "Solicitud de Presupuesto #"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
msgid "Requests for Quotation"
msgstr "Solicitudes de presupuesto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_move_ids
msgid "Reservation"
msgstr "Reserva"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_report_user_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Responsible"
msgstr "Responsable"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_date_planned
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_date_planned
msgid "Scheduled Date"
msgstr "Fecha Programada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr "Buscar pedido de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr "Enviar OC por Correo"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send RFQ by Email"
msgstr "Enviar SDP por Correo"

#. module: purchase
#: selection:purchase.config.settings,group_costing_method:0
msgid "Set a fixed cost price on each product"
msgstr "Asignar un precio costo fijo por cada producto"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#, fuzzy
msgid ""
"Set a purchase order as done if you don't want to receive vendor bills "
"anymore for this purchase order."
msgstr ""
"Si una orden de compra esta realizada, usted no puede modificarla, Usted no "
"puede recibir  facturas y asociarlas a esta orden de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set to Draft"
msgstr "Cambiar a borrador"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Configuración"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Shipment"
msgstr "Envíos"

#. module: purchase
#: selection:purchase.config.settings,group_advance_purchase_requisition:0
msgid "Simple call for tender (only choose from one RFQ)"
msgstr "Llamada simple para licititante(se escoge solo de una SDP)"

#. module: purchase
#: selection:purchase.config.settings,group_uom:0
#, fuzzy
msgid ""
"Some products may be sold/puchased in different units of measure (advanced)"
msgstr ""
"Algunos productos pueden ser vendidos/comprados en diferentes unidades de "
"medidas (Avanzado)"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_origin
msgid "Source Document"
msgstr "Doc. Fuente"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Estado"

#. module: purchase
#: model:ir.model,name:purchase.model_stock_move
msgid "Stock Move"
msgstr "Moviemiento de stock"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Stock Moves"
msgstr "Movimientos de stock"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_subtotal
msgid "Subtotal"
msgstr "Subtotal"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Moneda del Proveedor"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Supplier Pricelist"
msgstr "Precio provedor"

#. module: purchase
#: selection:purchase.config.settings,module_stock_dropshipping:0
msgid "Suppliers always deliver to your warehouse(s)"
msgstr "Los proveedores siempre hacen sus envíos a su(s) bodega(s)"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_tax
msgid "Tax"
msgstr "Impuesto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_taxes_id
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Taxes"
msgstr "Impuestos"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr ""
"Campo técnico usado para mostrar la dirección de entrega de venta a través "
"de intermedarios"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_notes
msgid "Terms and Conditions"
msgstr "Términos y Condiciones"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"The product form contains detailed information to improve the\n"
"            purchase process: prices, procurement logistics, accounting "
"data,\n"
"            available vendors, etc."
msgstr ""
"El formulario del producto contiene información detallada para mejorar \n"
"el proceso de compras: precios, abastecimientos, información contable\n"
"proveedores disponibles, etc."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"The quotation contains the history of the discussion/negotiation\n"
"                you had with your vendor. Once confirmed, a request for\n"
"                quotation is converted into a purchase order."
msgstr ""
"El presupuesto contiene la historia de la discusión/negociación\n"
"que usted tiene con el proveedor. Una vez confirmada, la solicitud\n"
"de presupuesto es convertida en una orden de compra"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid ""
"The request for quotation is the first step of the purchases flow. Once\n"
"                    converted into a purchase order, you will be able to "
"control the receipt\n"
"                    of the products and the vendor bill."
msgstr ""
"La solicitud de presupuesto es el primer paso en el flujo del proceso de "
"compras.\n"
"Una vez convertido en una orden de compra, usted tiene la posibilidad de "
"controlar\n"
"la recepción de los productos y la factura del proveedor"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_category_property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr ""
"Esta cuenta debe ser usada para asignar la diferencia de precio entre la "
"compra y el costo contable"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase.field_product_template_property_account_creditor_price_difference
msgid ""
"This account will be used to value price difference between purchase price "
"and cost price."
msgstr ""
"Esta cuenta se utilizará para valorar la diferencia de precios entre el "
"precio de compra y precio de coste"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner_property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""
"Esta moneda debe ser usada, en vez de la asignada por defecto, para las "
"compras del tercero actual"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Click to create a new RfQ."
msgstr "Este proveedor no posee orden de compra, Clic para crear una nueva SDP"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_picking_type_id
msgid "This will determine picking type of incoming shipment"
msgstr ""
"Esto determinará el tipo de documento de transferencia sea de envíos "
"entrantes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: selection:purchase.order,state:0 selection:purchase.report,state:0
msgid "To Approve"
msgstr "Para aprobar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_total
msgid "Total"
msgstr "Total"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_total
msgid "Total Price"
msgstr "Precio total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Total Untaxed amount"
msgstr "Total importe base"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Total amount"
msgstr "Monto Total"

#. module: purchase
#: model:ir.model,name:purchase.model_stock_picking
msgid "Transfer"
msgstr "Transferencia"

#. module: purchase
#: code:addons/purchase/purchase.py:300
#, python-format
msgid ""
"Unable to cancel purchase order %s as some receptions have already been done."
msgstr ""
"No es permitido cancelar la orden de compra %s ya que posee recepciones que "
"se encuentran en estado realizado"

#. module: purchase
#: code:addons/purchase/purchase.py:303
#, fuzzy, python-format
msgid ""
"Unable to cancel this purchase order. You must first cancel related vendor "
"bills."
msgstr ""
"No es permitido cancelar esta orden de compra!. Usted primero debe cancelar "
"las facturas de proveedor relacionadas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_unit
msgid "Unit Price"
msgstr "Precio unitario"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr "Categorías de las Unidades de Medida"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_uom
#: model:ir.ui.menu,name:purchase.menu_purchase_unit_measure_purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_form_action
msgid "Units of Measure"
msgstr "Unidades de medida"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Untaxed"
msgstr "Base Imponible"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_untaxed
msgid "Untaxed Amount"
msgstr "Base imponible"

#. module: purchase
#: selection:purchase.config.settings,group_costing_method:0
msgid "Use a 'Fixed', 'Real' or 'Average' price costing method"
msgstr "Usar 'Fijo', 'Real' o 'Promedio' como método de costeo"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_invoice_pending
msgid ""
"Use this menu to control the invoices to be received from your\n"
"            vendor. When registering a new bill, set the purchase order\n"
"            and Odoo will fill the bill automatically according to ordered\n"
"            or received quantities."
msgstr ""
"Usar este menú para controlar las facturas a ser recibidas de su\n"
"proveedor. Cuando vaya registra una nueva factura, seleccione la orden de "
"compra\n"
"y Odoo llenará la factura automáticamente de acuerdo a lo ordenado\n"
"o las cantidades recibidas"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Use this menu to search within your purchase orders by\n"
"                references, vendor, products, etc. For each purchase order,\n"
"                you can track the related discussion with the vendor, "
"control\n"
"                the products received and control the vendor bills."
msgstr ""
"Use este menú para buscar entre sus ordenes de compra por\n"
"referencias, proveedores, productos, etc. Para cada orden de compra\n"
"usted puede dar seguimiento de las conversaciones relacionadas con\n"
"el proveedor, control de los productos recibidos y de las facturas del "
"proveedor"

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Usuario"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "VAT:"
msgstr "Impuesto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_partner_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr "Proveedor"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_supplier_invoices
#: model:ir.actions.act_window,name:purchase.action_invoice_pending
#: model:ir.actions.act_window,name:purchase.purchase_open_invoice
#: model:ir.ui.menu,name:purchase.menu_procurement_management_pending_invoice
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_account_buttons
msgid "Vendor Bills"
msgstr "Facturas de Proveedor"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_account_buttons
msgid "Vendor Bills."
msgstr "Facturas de Proveedor."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_manage_vendor_price
msgid "Vendor Price"
msgstr "Precio del Proveedor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_partner_ref
msgid "Vendor Reference"
msgstr "Referencia del Proveedor"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid ""
"Vendors bills can be pre-generated based on purchase\n"
"                    orders or receipts. This allows you to control bills\n"
"                    you receive from your vendor according to the draft\n"
"                    document in Odoo."
msgstr ""
"La facturas del proveedor pueden ser pre-generadas basadas en las\n"
"ordenes de compra o las recepciones. Esto permite que usted controle\n"
"las facturas que usted recibe de su proveedor acorde al documento en\n"
"borrador en Odoo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_volume
msgid "Volume"
msgstr ""

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "Waiting Invoices"
msgstr "Facturas pendientes"

#. module: purchase
#: model:ir.model,name:purchase.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase.field_purchase_report_picking_type_id
msgid "Warehouse"
msgstr "Gestión Inventario"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_stock_warehouse_buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr "Cuando un producto es borrado, ellos pueden ser enviados a esta bodega"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""
"Trabajar con variantes de productos, le permite definir variantes de un "
"mismo producto, una facilidad de la gestión del producto en el comercio "
"electrónico por ejemplo"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_open_invoice
msgid ""
"You can control the invoice from your vendor according to\n"
"            what you purchased (services) or received (products)."
msgstr ""
"Usted puede controlar la factura de su proveedor de acuerdo a\n"
"que compró(Servicios) o recibió(Productos)"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
#, fuzzy
msgid ""
"You must define a product for everything you purchase, whether\n"
"            it's a physical product, a consumable or services you buy to\n"
"            subcontractors."
msgstr ""
"Usted debe definir un producto para todo lo que compre, esto es así\n"
"si es un producto físico, un consumible o un servicio que usted compre a\n"
"subcontratistas"

#. module: purchase
#: code:addons/purchase/purchase.py:335
#, python-format
msgid "You must set a Vendor Location for this partner %s"
msgstr "Usted debe configurar una Ubicación de Compras para esta empresa %s"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_config_settings
msgid "purchase.config.settings"
msgstr "purchase.config.settings"

#~ msgid ""
#~ "\n"
#~ "<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-"
#~ "serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; "
#~ "\">\n"
#~ "\n"
#~ "    <p>Hello ${object.partner_id.name},</p>\n"
#~ "\n"
#~ "    <p>Here is a ${object.state in ('draft', 'sent') and 'request for "
#~ "quotation' or 'purchase order confirmation'} from ${object.company_id."
#~ "name}: </p>\n"
#~ "\n"
#~ "    <p style=\"border-left: 1px solid #8e0000; margin-left: 30px;\">\n"
#~ "       &nbsp;&nbsp;<strong>REFERENCES</strong><br />\n"
#~ "       &nbsp;&nbsp;Order number: <strong>${object.name}</strong><br />\n"
#~ "       &nbsp;&nbsp;Order total: <strong>${object.amount_total} ${object."
#~ "currency_id.name}</strong><br />\n"
#~ "       &nbsp;&nbsp;Order date: ${object.date_order}<br />\n"
#~ "       % if object.origin:\n"
#~ "       &nbsp;&nbsp;Order reference: ${object.origin}<br />\n"
#~ "       % endif\n"
#~ "       % if object.partner_ref:\n"
#~ "       &nbsp;&nbsp;Your reference: ${object.partner_ref}<br />\n"
#~ "       % endif\n"
#~ "       % if object.create_uid:\n"
#~ "       &nbsp;&nbsp;Your contact: <a href=\"mailto:${object.create_uid."
#~ "email or ''}?subject=Order%20${object.name}\">${object.create_uid.name}</"
#~ "a>\n"
#~ "       % endif\n"
#~ "    </p>\n"
#~ "\n"
#~ "    <br/>\n"
#~ "    <p>If you have any question, do not hesitate to contact us.</p>\n"
#~ "    <p>Thank you!</p>\n"
#~ "    <br/>\n"
#~ "    <br/>\n"
#~ "    <div style=\"width: 375px; margin: 0px; padding: 0px; background-"
#~ "color: #8E0000; border-top-left-radius: 5px 5px; border-top-right-radius: "
#~ "5px 5px; background-repeat: repeat no-repeat;\">\n"
#~ "        <h3 style=\"margin: 0px; padding: 2px 14px; font-size: 12px; "
#~ "color: #DDD;\">\n"
#~ "            <strong style=\"text-transform:uppercase;\">${object."
#~ "company_id.name}</strong></h3>\n"
#~ "    </div>\n"
#~ "    <div style=\"width: 347px; margin: 0px; padding: 5px 14px; line-"
#~ "height: 16px; background-color: #F2F2F2;\">\n"
#~ "        <span style=\"color: #222; margin-bottom: 5px; display: block; "
#~ "\">\n"
#~ "            ${object.company_id.partner_id.sudo()."
#~ "with_context(show_address=True, html_format=True).name_get()[0][1] | "
#~ "safe}\n"
#~ "        </span>\n"
#~ "        % if object.company_id.phone:\n"
#~ "            <div style=\"margin-top: 0px; margin-right: 0px; margin-"
#~ "bottom: 0px; margin-left: 0px; padding-top: 0px; padding-right: 0px; "
#~ "padding-bottom: 0px; padding-left: 0px; \">\n"
#~ "                Phone:&nbsp; ${object.company_id.phone}\n"
#~ "            </div>\n"
#~ "        % endif\n"
#~ "        % if object.company_id.website:\n"
#~ "            <div>\n"
#~ "                Web :&nbsp;<a href=\"${object.company_id.website}\">"
#~ "${object.company_id.website}</a>\n"
#~ "            </div>\n"
#~ "        %endif\n"
#~ "        <p></p>\n"
#~ "    </div>\n"
#~ "</div>\n"
#~ "            "
#~ msgstr ""
#~ "\n"
#~ "<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-"
#~ "serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; "
#~ "\">\n"
#~ "\n"
#~ "    <p>Estimado ${object.partner_id.name},</p>\n"
#~ "\n"
#~ "    <p>La siguiente ${object.state in ('draft', 'sent') and 'solicitud de "
#~ "presupuesto' or 'confirmación de orden de compra'} de ${object.company_id."
#~ "name}: </p>\n"
#~ "\n"
#~ "    <p style=\"border-left: 1px solid #8e0000; margin-left: 30px;\">\n"
#~ "       &nbsp;&nbsp;<strong>REFERENCIAS</strong><br />\n"
#~ "       &nbsp;&nbsp;Número de Orden: <strong>${object.name}</strong><br /"
#~ ">\n"
#~ "       &nbsp;&nbsp;Total Orden: <strong>${object.amount_total} ${object."
#~ "currency_id.name}</strong><br />\n"
#~ "       &nbsp;&nbsp;Fecha de Orden: ${object.date_order}<br />\n"
#~ "       % if object.origin:\n"
#~ "       &nbsp;&nbsp;Referencia de Orden: ${object.origin}<br />\n"
#~ "       % endif\n"
#~ "       % if object.partner_ref:\n"
#~ "       &nbsp;&nbsp;Su referencia: ${object.partner_ref}<br />\n"
#~ "       % endif\n"
#~ "       % if object.create_uid:\n"
#~ "       &nbsp;&nbsp;Su contacto es: <a href=\"mailto:${object.create_uid."
#~ "email or ''}?subject=Orden de Compra%20${object.name}\">${object."
#~ "create_uid.name}</a>\n"
#~ "       % endif\n"
#~ "    </p>\n"
#~ "\n"
#~ "    <br/>\n"
#~ "    <p>Si usted tiene alguna duda, por favor comuníquese con nosotros.</"
#~ "p>\n"
#~ "    <p>Gracias!</p>\n"
#~ "    <br/>\n"
#~ "    <br/>\n"
#~ "    <div style=\"width: 375px; margin: 0px; padding: 0px; background-"
#~ "color: #8E0000; border-top-left-radius: 5px 5px; border-top-right-radius: "
#~ "5px 5px; background-repeat: repeat no-repeat;\">\n"
#~ "        <h3 style=\"margin: 0px; padding: 2px 14px; font-size: 12px; "
#~ "color: #DDD;\">\n"
#~ "            <strong style=\"text-transform:uppercase;\">${object."
#~ "company_id.name}</strong></h3>\n"
#~ "    </div>\n"
#~ "    <div style=\"width: 347px; margin: 0px; padding: 5px 14px; line-"
#~ "height: 16px; background-color: #F2F2F2;\">\n"
#~ "        <span style=\"color: #222; margin-bottom: 5px; display: block; "
#~ "\">\n"
#~ "            ${object.company_id.partner_id.sudo()."
#~ "with_context(show_address=True, html_format=True).name_get()[0][1] | "
#~ "safe}\n"
#~ "        </span>\n"
#~ "        % if object.company_id.phone:\n"
#~ "            <div style=\"margin-top: 0px; margin-right: 0px; margin-"
#~ "bottom: 0px; margin-left: 0px; padding-top: 0px; padding-right: 0px; "
#~ "padding-bottom: 0px; padding-left: 0px; \">\n"
#~ "                Teléfono:&nbsp; ${object.company_id.phone}\n"
#~ "            </div>\n"
#~ "        % endif\n"
#~ "        % if object.company_id.website:\n"
#~ "            <div>\n"
#~ "                Website :&nbsp;<a href=\"${object.company_id.website}\">"
#~ "${object.company_id.website}</a>\n"
#~ "            </div>\n"
#~ "        %endif\n"
#~ "        <p></p>\n"
#~ "    </div>\n"
#~ "</div>\n"
#~ "            "

#~ msgid ""
#~ "\n"
#~ "<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-"
#~ "serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; "
#~ "\">\n"
#~ "\n"
#~ "    <p>Hello ${object.partner_id.name},</p>\n"
#~ "\n"
#~ "    <p>Here is a ${object.state in ('draft', 'sent') and 'request for "
#~ "quotation' or 'purchase order confirmation'} from ${object.company_id."
#~ "name}: </p>\n"
#~ "\n"
#~ "    <p style=\"border-left: 1px solid #8e0000; margin-left: 30px;\">\n"
#~ "       &nbsp;&nbsp;<strong>REFERENCES</strong><br />\n"
#~ "       &nbsp;&nbsp;RFQ number: <strong>${object.name}</strong><br />\n"
#~ "       &nbsp;&nbsp;RFQ date: ${object.date_order}<br />\n"
#~ "       % if object.origin:\n"
#~ "       &nbsp;&nbsp;RFQ reference: ${object.origin}<br />\n"
#~ "       % endif\n"
#~ "       % if object.partner_ref:\n"
#~ "       &nbsp;&nbsp;Your reference: ${object.partner_ref}<br />\n"
#~ "       % endif\n"
#~ "       % if object.create_uid:\n"
#~ "       &nbsp;&nbsp;Your contact: <a href=\"mailto:${object.create_uid."
#~ "email or ''}?subject=Order%20${object.name}\">${object.create_uid.name}</"
#~ "a>\n"
#~ "       % endif\n"
#~ "    </p>\n"
#~ "\n"
#~ "    <br/>\n"
#~ "    <p>If you have any question, do not hesitate to contact us.</p>\n"
#~ "    <p>Thank you!</p>\n"
#~ "    <br/>\n"
#~ "    <br/>\n"
#~ "    <div style=\"width: 375px; margin: 0px; padding: 0px; background-"
#~ "color: #8E0000; border-top-left-radius: 5px 5px; border-top-right-radius: "
#~ "5px 5px; background-repeat: repeat no-repeat;\">\n"
#~ "        <h3 style=\"margin: 0px; padding: 2px 14px; font-size: 12px; "
#~ "color: #DDD;\">\n"
#~ "            <strong style=\"text-transform:uppercase;\">${object."
#~ "company_id.name}</strong></h3>\n"
#~ "    </div>\n"
#~ "    <div style=\"width: 347px; margin: 0px; padding: 5px 14px; line-"
#~ "height: 16px; background-color: #F2F2F2;\">\n"
#~ "        <span style=\"color: #222; margin-bottom: 5px; display: block; "
#~ "\">\n"
#~ "            ${object.company_id.partner_id.sudo()."
#~ "with_context(show_address=True, html_format=True).name_get()[0][1] | "
#~ "safe}\n"
#~ "        </span>\n"
#~ "        % if object.company_id.phone:\n"
#~ "            <div style=\"margin-top: 0px; margin-right: 0px; margin-"
#~ "bottom: 0px; margin-left: 0px; padding-top: 0px; padding-right: 0px; "
#~ "padding-bottom: 0px; padding-left: 0px; \">\n"
#~ "                Phone:&nbsp; ${object.company_id.phone}\n"
#~ "            </div>\n"
#~ "        % endif\n"
#~ "        % if object.company_id.website:\n"
#~ "            <div>\n"
#~ "                Web :&nbsp;<a href=\"${object.company_id.website}\">"
#~ "${object.company_id.website}</a>\n"
#~ "            </div>\n"
#~ "        %endif\n"
#~ "        <p></p>\n"
#~ "    </div>\n"
#~ "</div>\n"
#~ "            "
#~ msgstr ""
#~ "\n"
#~ "<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-"
#~ "serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; "
#~ "\">\n"
#~ "\n"
#~ "    <p>Saludos ${object.partner_id.name},</p>\n"
#~ "\n"
#~ "    <p>Adjunto encontrará  ${object.state in ('draft', 'sent') and "
#~ "'request for quotation' or 'purchase order confirmation'} de ${object."
#~ "company_id.name}: </p>\n"
#~ "\n"
#~ "    <p style=\"border-left: 1px solid #8e0000; margin-left: 30px;\">\n"
#~ "       &nbsp;&nbsp;<strong>REFERENCIA</strong><br />\n"
#~ "       &nbsp;&nbsp;RFQ número: <strong>${object.name}</strong><br />\n"
#~ "       &nbsp;&nbsp;RFQ fecha: ${object.date_order}<br />\n"
#~ "       % if object.origin:\n"
#~ "       &nbsp;&nbsp;RFQ referencia: ${object.origin}<br />\n"
#~ "       % endif\n"
#~ "       % if object.partner_ref:\n"
#~ "       &nbsp;&nbsp;Su referencia: ${object.partner_ref}<br />\n"
#~ "       % endif\n"
#~ "       % if object.create_uid:\n"
#~ "       &nbsp;&nbsp;Su contacto: <a href=\"mailto:${object.create_uid."
#~ "email or ''}?subject=Order%20${object.name}\">${object.create_uid.name}</"
#~ "a>\n"
#~ "       % endif\n"
#~ "    </p>\n"
#~ "\n"
#~ "    <br/>\n"
#~ "    <p>Si usted tiene alguna pregunta, no dude en contactarnos.</p>\n"
#~ "    <p>Muchas gracias</p>\n"
#~ "    <br/>\n"
#~ "    <br/>\n"
#~ "    <div style=\"width: 375px; margin: 0px; padding: 0px; background-"
#~ "color: #8E0000; border-top-left-radius: 5px 5px; border-top-right-radius: "
#~ "5px 5px; background-repeat: repeat no-repeat;\">\n"
#~ "        <h3 style=\"margin: 0px; padding: 2px 14px; font-size: 12px; "
#~ "color: #DDD;\">\n"
#~ "            <strong style=\"text-transform:uppercase;\">${object."
#~ "company_id.name}</strong></h3>\n"
#~ "    </div>\n"
#~ "    <div style=\"width: 347px; margin: 0px; padding: 5px 14px; line-"
#~ "height: 16px; background-color: #F2F2F2;\">\n"
#~ "        <span style=\"color: #222; margin-bottom: 5px; display: block; "
#~ "\">\n"
#~ "            ${object.company_id.partner_id.sudo()."
#~ "with_context(show_address=True, html_format=True).name_get()[0][1] | "
#~ "safe}\n"
#~ "        </span>\n"
#~ "        % if object.company_id.phone:\n"
#~ "            <div style=\"margin-top: 0px; margin-right: 0px; margin-"
#~ "bottom: 0px; margin-left: 0px; padding-top: 0px; padding-right: 0px; "
#~ "padding-bottom: 0px; padding-left: 0px; \">\n"
#~ "                Teléfono:&nbsp; ${object.company_id.phone}\n"
#~ "            </div>\n"
#~ "        % endif\n"
#~ "        % if object.company_id.website:\n"
#~ "            <div>\n"
#~ "                Web :&nbsp;<a href=\"${object.company_id.website}\">"
#~ "${object.company_id.website}</a>\n"
#~ "            </div>\n"
#~ "        %endif\n"
#~ "        <p></p>\n"
#~ "    </div>\n"
#~ "</div>\n"
#~ "            "

#~ msgid "${object.company_id.name|safe} Order (Ref ${object.name or 'n/a' })"
#~ msgstr ""
#~ "${object.company_id.name|safe} Pedido (Ref ${object.name or 'n/a' })"

#~ msgid "Action Needed"
#~ msgstr "Acción Requerida"

#~ msgid "Allows you to specify an analytic account on purchase order lines."
#~ msgstr ""
#~ "Permite especificar una cuenta analítica en las lineas del pedido de "
#~ "compra"

#~ msgid "Analytic accounting for purchases"
#~ msgstr "Cuentas analíticas para Compras"

#~ msgid "Click to mark products as received."
#~ msgstr "Presione para marcar los productos como recibidos."

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha de el último mensaje publicado en el registro"

#~ msgid "Expected Date"
#~ msgstr "Fecha Prevista"

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Terceros)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si esta habilitado, los nuevos mensajes deben ser revisados"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si esta habilitado, los nuevos mensajes deben ser revisados"

#~ msgid "Incoming Shipments to Invoice"
#~ msgstr "Envíos por Recibir"

#~ msgid "Is Follower"
#~ msgstr "Es seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha de último mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Historial de Comunicación y Mensajes"

#~ msgid "Number of Actions"
#~ msgstr "Número de acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leídos"

#~ msgid "Open Purchase Menu"
#~ msgstr "Abrir menú de compras"

#~ msgid "Published"
#~ msgstr "Publicado"

#~ msgid "Set to Done"
#~ msgstr "Marcar como Terminado"

#~ msgid "Unit of Measures"
#~ msgstr "Unidades de Medida"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes no leídos"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de Mensajes no Leídos"

#~ msgid "Visible on the website as a comment"
#~ msgstr "Visible en el sitio web como un comentario"

#~ msgid "Website Messages"
#~ msgstr "Mensajes del Portal Web"

#~ msgid "Website communication history"
#~ msgstr "Historia de comunicación en Portal Web"

#~ msgid "account.config.settings"
#~ msgstr "account.config.settings"

#~ msgid "unknown"
#~ msgstr "desconocido"
