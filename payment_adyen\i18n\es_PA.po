# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_adyen
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-19 08:17+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Panama) (http://www.transifex.com/odoo/odoo-9/"
"language/es_PA/)\n"
"Language: es_PA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_adyen
#: code:addons/payment_adyen/models/adyen.py:192
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/adyen.py:190
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_adyen
#: model_terms:payment.acquirer,cancel_msg:payment_adyen.payment_acquirer_adyen
msgid "<span><i>Cancel,</i> Your payment has been cancelled.</span>"
msgstr ""

#. module: payment_adyen
#: model_terms:payment.acquirer,done_msg:payment_adyen.payment_acquirer_adyen
msgid ""
"<span><i>Done,</i> Your online payment has been successfully processed. "
"Thank you for your order.</span>"
msgstr ""

#. module: payment_adyen
#: model_terms:payment.acquirer,error_msg:payment_adyen.payment_acquirer_adyen
msgid ""
"<span><i>Error,</i> Please be aware that an error occurred during the "
"transaction. The order has been confirmed but won't be paid. Don't hesitate "
"to contact us if you have any questions on the status of your order.</span>"
msgstr ""

#. module: payment_adyen
#: model_terms:payment.acquirer,pending_msg:payment_adyen.payment_acquirer_adyen
msgid ""
"<span><i>Pending,</i> Your online payment has been successfully processed. "
"But your order is not validated yet.</span>"
msgstr ""

#. module: payment_adyen
#: model:payment.acquirer,name:payment_adyen.payment_acquirer_adyen
msgid "Adyen"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/adyen.py:241
#, python-format
msgid "Adyen: feedback error"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/adyen.py:203
#, python-format
msgid "Adyen: invalid merchantSig, received %s, computed %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/adyen.py:188
#, python-format
msgid "Adyen: received data for reference %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/adyen.py:181
#, python-format
msgid ""
"Adyen: received data with missing reference (%s) or missing pspReference (%s)"
msgstr ""

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.acquirer_form_adyen
msgid "How to configure your Adyen account?"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer_adyen_merchant_account
msgid "Merchant Account"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Método de pago"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer_adyen_skin_code
msgid "Skin Code"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer_adyen_skin_hmac_key
msgid "Skin HMAC Key"
msgstr ""

#. module: payment_adyen
#: model_terms:payment.acquirer,pre_msg:payment_adyen.payment_acquirer_adyen
msgid ""
"You will be redirected to the Adyen website after clicking on the payment "
"button."
msgstr ""
