<?xml version="1.0" encoding='UTF-8'?>
<odoo>

	<record id="health_expiry_notification" model="ir.cron">
        <field name="name">HR : Employees Health Certificate Notification</field>
        <field name="model_id" ref="model_hr_employee"/>
        <field name="state">code</field>
        <field name="code">model.cron_health_certificate_expiry_notification()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
    </record>

    <record id="mail_activity_health_expiry" model="mail.activity.type">
        <field name="name">HR : Employees Health Certificate Notification</field>
        <field name="res_model">hr.employee</field>
    </record>

</odoo>