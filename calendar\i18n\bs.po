# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * calendar
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_reminder
msgid "${object.event_id.name} - Reminder"
msgstr ""

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_changedate
msgid "${object.event_id.name}: Date updated"
msgstr ""

#. module: calendar
#: code:addons/calendar/models/calendar.py:741
#, python-format
msgid ""
"%s at %s To\n"
" %s at %s (%s)"
msgstr ""

#. module: calendar
#: code:addons/calendar/models/calendar.py:732
#, python-format
msgid "%s at (%s To %s) (%s)"
msgstr ""

#. module: calendar
#: code:addons/calendar/models/calendar.py:229
#, python-format
msgid "%s has accepted invitation"
msgstr ""

#. module: calendar
#: code:addons/calendar/models/calendar.py:237
#, python-format
msgid "%s has declined invitation"
msgstr ""

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_5
msgid "1 Day(s)"
msgstr "1 Dan(a)"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_3
msgid "1 Hour(s)"
msgstr ""

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_1
msgid "15 Minute(s)"
msgstr ""

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_4
msgid "2 Hour(s)"
msgstr ""

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_1
msgid "3 Hour(s), by e-mail"
msgstr ""

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_2
msgid "30 Minute(s)"
msgstr ""

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_2
msgid "6 Hour(s), by e-mail"
msgstr ""

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_invitation
msgid ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        ${object.event_id.user_id.partner_id.name} invited you for the ${object.event_id.name} meeting of ${object.event_id.user_id.company_id.name}.\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/meeting/decline?db=${'dbname' in ctx and ctx['dbname'] or '' }&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "
msgstr ""

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_changedate
msgid ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        The date of the meeting has been updated. The meeting ${object.event_id.name} created by ${object.event_id.user_id.partner_id.name} is now scheduled for ${object.event_id.get_display_time_tz(tz=object.partner_id.tz)}.\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/meeting/decline?db=${'dbname' in ctx and ctx['dbname'] or '' }&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: ${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "
msgstr ""

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_reminder
msgid ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        This is a reminder for the below event :\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/meeting/decline?db=${'dbname' in ctx and ctx['dbname'] or '' }&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_popup
msgid "<span> hours</span>"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Accept"
msgstr "Prihvati"

#. module: calendar
#: selection:calendar.attendee,state:0
#: selection:calendar.event,attendee_status:0
msgid "Accepted"
msgstr "Prihvaćeno"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__active
#: model:ir.model.fields,field_description:calendar.field_calendar_event__active
msgid "Active"
msgstr "Aktivan"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity
msgid "Activity"
msgstr "Aktivnost"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_type
msgid "Activity Type"
msgstr "Tip aktivnosti"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:36
#: model:ir.model.fields,field_description:calendar.field_calendar_event__allday
#, python-format
msgid "All Day"
msgstr "Cijeli dan"

#. module: calendar
#: code:addons/calendar/models/calendar.py:728
#, python-format
msgid "AllDay , %s"
msgstr ""

#. module: calendar
#: sql_constraint:calendar.contacts:0
msgid "An user cannot have twice the same contact."
msgstr ""

#. module: calendar
#: model:ir.model,name:calendar.model_ir_attachment
msgid "Attachment"
msgstr "Zakačka"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_attendee
msgid "Attendee"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_status
msgid "Attendee Status"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_popup
msgid "Attendees"
msgstr "Prisutni"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Availability"
msgstr "Raspoloživost"

#. module: calendar
#: code:addons/calendar/models/calendar.py:1693
#: selection:calendar.attendee,availability:0
#: selection:calendar.event,show_as:0
#, python-format
msgid "Busy"
msgstr "Zauzet"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__byday
msgid "By day"
msgstr "Po danu"

#. module: calendar
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
#: model:ir.ui.menu,name:calendar.menu_calendar_configuration
msgid "Calendar"
msgstr "Kalendar"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_alarm
#: model:ir.ui.menu,name:calendar.menu_calendar_alarm
#: model_terms:ir.ui.view,arch_db:calendar.calendar_alarm_view_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_alarm_tree
msgid "Calendar Alarm"
msgstr "Kalendarski alarm"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr ""

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_contacts
msgid "Calendar Contacts"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Calendar Invitation"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity__calendar_event_id
msgid "Calendar Meeting"
msgstr ""

#. module: calendar
#: model:ir.actions.server,name:calendar.ir_cron_scheduler_alarm_ir_actions_server
#: model:ir.cron,cron_name:calendar.ir_cron_scheduler_alarm
#: model:ir.cron,name:calendar.ir_cron_scheduler_alarm
msgid "Calendar: Event Reminder"
msgstr ""

#. module: calendar
#: model:ir.model.fields,help:calendar.field_mail_activity_type__category
msgid "Categories may trigger specific behavior like opening calendar view"
msgstr ""
"Kategorije mogu okinuti specifična ponašanja kao što je otvaranje kalendar "
"pogleda"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__category
msgid "Category"
msgstr "Kategorija"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Click here to update only this instance and not all recurrences."
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__common_name
msgid "Common name"
msgstr "Uobičajeno Ime"

#. module: calendar
#: selection:calendar.event,state:0
msgid "Confirmed"
msgstr "Potvrđeno"

#. module: calendar
#: model:ir.model,name:calendar.model_res_partner
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_start
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Date"
msgstr "Datum"

#. module: calendar
#: selection:calendar.event,month_by:0
#: model:ir.model.fields,field_description:calendar.field_calendar_event__day
msgid "Date of month"
msgstr "Dan u mjesecu"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Day of Month"
msgstr "Dan u mjesecu"

#. module: calendar
#: selection:calendar.event,month_by:0
msgid "Day of month"
msgstr "Dan u mjesecu"

#. module: calendar
#: selection:calendar.alarm,interval:0 selection:calendar.event,rrule_type:0
msgid "Day(s)"
msgstr "Dan(i)"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Decline"
msgstr "Odbij"

#. module: calendar
#: selection:calendar.attendee,state:0
#: selection:calendar.event,attendee_status:0
msgid "Declined"
msgstr "Odbijeno"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__description
msgid "Description"
msgstr "Opis"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:18
#, python-format
msgid "Details"
msgstr "Detalji"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm_manager__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_popup
msgid "Document"
msgstr "Dokument"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_id
msgid "Document ID"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_id
msgid "Document Model"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model
msgid "Document Model Name"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__duration
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Duration"
msgstr "Trajanje"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration_minutes
#: model:ir.model.fields,help:calendar.field_calendar_alarm__duration_minutes
msgid "Duration in minutes"
msgstr ""

#. module: calendar
#: selection:calendar.alarm,type:0
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__email
msgid "Email"
msgstr "E-Mail"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__email
msgid "Email of Invited Person"
msgstr "E-pošta pozvane osobe"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__partner_id
msgid "Employee"
msgstr "Zaposleni"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "End Date"
msgstr "Datum Završetka"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_datetime
msgid "End Datetime"
msgstr ""

#. module: calendar
#: selection:calendar.event,end_type:0
msgid "End date"
msgstr "Završni datum"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Ending at"
msgstr ""

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event
msgid "Event"
msgstr "Dogadaj"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm
msgid "Event Alarm"
msgstr ""

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr ""

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event_type
msgid "Event Meeting Type"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_time
msgid "Event Time"
msgstr ""

#. module: calendar
#: code:addons/calendar/models/calendar.py:1234
#, python-format
msgid "Event recurrence interval cannot be negative."
msgstr ""

#. module: calendar
#: selection:calendar.event,privacy:0
msgid "Everyone"
msgstr "Svi"

#. module: calendar
#: code:addons/calendar/models/mail_activity.py:38
#, python-format
msgid "Feedback: "
msgstr "Povratna informacija:"

#. module: calendar
#: selection:calendar.event,byday:0
msgid "Fifth"
msgstr "Peti"

#. module: calendar
#: selection:calendar.event,byday:0
msgid "First"
msgstr "Prvi"

#. module: calendar
#: code:addons/calendar/models/calendar.py:998
#, python-format
msgid "First you have to specify the date of the invitation."
msgstr "Prvo morate da navedete datum pozivnice."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: calendar
#: selection:calendar.event,byday:0
msgid "Fourth"
msgstr "Četvrti"

#. module: calendar
#: selection:calendar.attendee,availability:0
#: selection:calendar.event,show_as:0
msgid "Free"
msgstr "Slobodno"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__availability
msgid "Free/Busy"
msgstr "Slobodan/Zauzet"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__fr
msgid "Fri"
msgstr "Pet"

#. module: calendar
#: selection:calendar.event,week_list:0
msgid "Friday"
msgstr "Petak"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Group By"
msgstr "Grupiši po"

#. module: calendar
#: code:addons/calendar/models/calendar.py:1636
#, python-format
msgid "Group by date is not supported, use the calendar view instead."
msgstr "Grupiranje po datumu nije podržano. Koristite kalendar."

#. module: calendar
#: model:ir.model,name:calendar.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: calendar
#: selection:calendar.alarm,interval:0
msgid "Hour(s)"
msgstr "Sat(i)"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__id
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm_manager__id
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__id
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__id
msgid "ID"
msgstr "ID"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""

#. module: calendar
#: model:mail.message.subtype,name:calendar.subtype_invitation
msgid "Invitation"
msgstr "Pozivnica"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__access_token
msgid "Invitation Token"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitation details"
msgstr "Detalji poziva"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Invitation for"
msgstr ""

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_invitation
msgid "Invitation to ${object.event_id.name}"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitations"
msgstr "Pozivnice"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Čarobnjak za pozivanje"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_highlighted
msgid "Is the Event Highlighted"
msgstr ""

#. module: calendar
#: selection:calendar.event,byday:0
msgid "Last"
msgstr "Zadnji"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm_manager____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__calendar_last_notif_ack
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr ""

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type
msgid "Let the event automatically repeat at that interval"
msgstr "Neka se događaj automatski ponavlja u ovim intervalima"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__location
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Location"
msgstr "Lokacija"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__location
msgid "Location of Event"
msgstr "Lokacija događaja"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Logo"
msgstr "Logo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__user_id
msgid "Me"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
#: selection:mail.activity.type,category:0
msgid "Meeting"
msgstr "Sastanak"

#. module: calendar
#: code:addons/calendar/models/calendar.py:942
#: code:addons/calendar/models/calendar.py:947
#, python-format
msgid "Meeting '%s' starts '%s' and ends '%s'"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Meeting Details"
msgstr "Detalji sastanka"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__name
msgid "Meeting Subject"
msgstr "Tema sastanka"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event_type
#: model:ir.ui.menu,name:calendar.menu_calendar_event_type
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_type_tree
msgid "Meeting Types"
msgstr "Tipovi sastanka"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__event_id
msgid "Meeting linked"
msgstr ""

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event
#: model:ir.actions.act_window,name:calendar.action_calendar_event_notify
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_popup
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Meetings"
msgstr "Sastanci"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_message
msgid "Message"
msgstr "Poruka"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: calendar
#: selection:calendar.alarm,interval:0
msgid "Minute(s)"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Misc"
msgstr "Razno"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__mo
msgid "Mon"
msgstr "Pon"

#. module: calendar
#: selection:calendar.event,week_list:0
msgid "Monday"
msgstr "Ponedjeljak"

#. module: calendar
#: selection:calendar.event,rrule_type:0
msgid "Month(s)"
msgstr "Mjesec(i)"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "My Meetings"
msgstr "Moji sastanci"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__name
msgid "Name"
msgstr "Naziv:"

#. module: calendar
#: selection:calendar.attendee,state:0
#: selection:calendar.event,attendee_status:0
msgid "Needs Action"
msgstr "Zahtjeva akciju"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No I'm not going."
msgstr ""

#. module: calendar
#: selection:calendar.alarm,type:0
msgid "Notification"
msgstr "Obavještenje"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: calendar
#: selection:calendar.event,end_type:0
msgid "Number of repetitions"
msgstr "Broj ponavljanja"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:17
#, python-format
msgid "OK"
msgstr "OK"

#. module: calendar
#: selection:calendar.event,privacy:0
msgid "Only internal users"
msgstr ""

#. module: calendar
#: selection:calendar.event,privacy:0
msgid "Only me"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_view_form_popup
msgid "Open Calendar"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__month_by
msgid "Option"
msgstr "Opcija"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Options"
msgstr "Opcije"

#. module: calendar
#: selection:mail.activity.type,category:0
msgid "Other"
msgstr "Drugo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Owner"
msgstr "Vlasnik"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_ids
msgid "Participant"
msgstr ""

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__partner_id
msgid "Partner-related data of the user"
msgstr "Podatci vezani za partnera na korisniku"

#. module: calendar
#: code:addons/calendar/models/calendar.py:1247
#, python-format
msgid "Please select a proper day of the month."
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__privacy
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Privacy"
msgstr "Privatnost"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type
msgid "Recurrence"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__end_type
msgid "Recurrence Termination"
msgstr "Prekid ponavljanja"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrency
msgid "Recurrent"
msgstr "Ponavljajući"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrent_id
msgid "Recurrent ID"
msgstr "Ponavljajući ID"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrent_id_date
msgid "Recurrent ID date"
msgstr "Ponavljajući ID datuma"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrency
msgid "Recurrent Meeting"
msgstr "Ponavljajući sastanak"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule
msgid "Recurrent Rule"
msgstr "Ponavljajuće pravilo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration
msgid "Remind Before"
msgstr ""

#. module: calendar
#: selection:mail.activity.type,category:0
msgid "Reminder"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__alarm_ids
msgid "Reminders"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__count
msgid "Repeat"
msgstr "Ponavljaj"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__interval
msgid "Repeat Every"
msgstr "Ponavljaj svakih"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__final_date
msgid "Repeat Until"
msgstr "Ponavljaj do"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__interval
msgid "Repeat every (Days/Week/Month/Year)"
msgstr "Ponavljaj svaki (Dan/Sedmicu/Mjesec/Godinu)"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__count
msgid "Repeat x times"
msgstr "Ponovi x puta"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Responsible"
msgstr "Odgovoran"

#. module: calendar
#: selection:calendar.alarm,type:0
msgid "SMS Text Message"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sa
msgid "Sat"
msgstr "Sub"

#. module: calendar
#: selection:calendar.event,week_list:0
msgid "Saturday"
msgstr "Subota"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid "Schedule a new meeting"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Search Meetings"
msgstr "Pretraži sastanke"

#. module: calendar
#: selection:calendar.event,byday:0
msgid "Second"
msgstr "Sekunda"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Select attendees..."
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send mail"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__show_as
msgid "Show Time as"
msgstr "Prikaži vrijeme kao"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:19
#, python-format
msgid "Snooze"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start
msgid "Start"
msgstr "Započni"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Start Date"
msgstr "Datum početka"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_datetime
msgid "Start DateTime"
msgstr ""

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__start
msgid "Start date of an event, without time for full days events"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_popup
msgid "Starting at"
msgstr "Počinje"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__state
#: model:ir.model.fields,field_description:calendar.field_calendar_event__state
msgid "Status"
msgstr "Status"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__state
msgid "Status of the attendee's participation"
msgstr "Status učestvovanja prisutnih"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop
msgid "Stop"
msgstr "Zaustavi"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__stop
msgid "Stop date of an event, without time for full days events"
msgstr ""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Subject"
msgstr "Tema"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__su
msgid "Sun"
msgstr "Ned"

#. module: calendar
#: selection:calendar.event,week_list:0
msgid "Sunday"
msgstr "Nedjelja"

#. module: calendar
#: sql_constraint:calendar.event.type:0
msgid "Tag name already exists !"
msgstr "Naziv oznake već postoji!"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__categ_ids
msgid "Tags"
msgstr "Oznake"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "The"
msgstr "!"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/mail_activity.js:42
#, python-format
msgid ""
"The activity is linked to a meeting. Deleting it will remove the meeting as "
"well. Do you want to proceed ?"
msgstr ""

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid ""
"The calendar is shared between employees and fully integrated with\n"
"            other applications such as the employee leaves or the business\n"
"            opportunities."
msgstr ""

#. module: calendar
#: code:addons/calendar/models/calendar.py:941
#, python-format
msgid ""
"The ending date and time cannot be earlier than the starting date and time."
msgstr ""

#. module: calendar
#: code:addons/calendar/models/calendar.py:946
#, python-format
msgid "The ending date cannot be earlier than the starting date."
msgstr ""

#. module: calendar
#: selection:calendar.event,byday:0
msgid "Third"
msgstr "Treći"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "This event is linked to a recurrence...<br/>"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__th
msgid "Thu"
msgstr "Čet"

#. module: calendar
#: selection:calendar.event,week_list:0
msgid "Thursday"
msgstr "Četvrtak"

#. module: calendar
#: code:addons/calendar/models/res_users.py:41
#, python-format
msgid "Today's Meetings"
msgstr ""

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tu
msgid "Tue"
msgstr "Uto"

#. module: calendar
#: selection:calendar.event,week_list:0
msgid "Tuesday"
msgstr "Utorak"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__type
msgid "Type"
msgstr "Tip"

#. module: calendar
#: selection:calendar.attendee,state:0
#: selection:calendar.event,attendee_status:0
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Uncertain"
msgstr "Nesigurno"

#. module: calendar
#: selection:calendar.event,state:0
msgid "Unconfirmed"
msgstr "Nepotvrđen"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__interval
msgid "Unit"
msgstr "Jedinica"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Until"
msgstr "Dok"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Update only this instance"
msgstr ""

#. module: calendar
#: model:ir.model,name:calendar.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__we
msgid "Wed"
msgstr "Sri"

#. module: calendar
#: selection:calendar.event,week_list:0
msgid "Wednesday"
msgstr "Srijeda"

#. module: calendar
#: selection:calendar.event,rrule_type:0
msgid "Week(s)"
msgstr "Sedmica"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__week_list
msgid "Weekday"
msgstr "Dan u sedmici"

#. module: calendar
#: selection:calendar.event,rrule_type:0
msgid "Year(s)"
msgstr "Godin(e)a"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Yes I'm going."
msgstr ""

#. module: calendar
#: code:addons/calendar/models/calendar.py:159
#, python-format
msgid "You cannot duplicate a calendar attendee."
msgstr "Ne možete duplicirati prisutne na kalendaru"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g. Business Lunch"
msgstr ""

#. module: calendar
#: code:addons/calendar/models/calendar.py:1232
#, python-format
msgid "interval cannot be negative."
msgstr ""
