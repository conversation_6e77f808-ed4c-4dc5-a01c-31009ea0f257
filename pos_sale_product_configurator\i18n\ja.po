# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_sale_product_configurator
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: 江口和志 <<EMAIL>>, 2022\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_sale_product_configurator
#: model:ir.model.fields,help:pos_sale_product_configurator.field_pos_config__iface_open_product_info
#: model_terms:ir.ui.view,arch_db:pos_sale_product_configurator.pos_config_view_form
msgid ""
"Display the 'Product Info' page when a product with optional products are "
"added in the customer cart"
msgstr "オプション商品を含む商品が顧客カートに追加された場合、「商品情報」ページを表示する。"

#. module: pos_sale_product_configurator
#: model:ir.model.fields,field_description:pos_sale_product_configurator.field_pos_config__iface_open_product_info
msgid "Open Product Info"
msgstr "製品情報を見る"

#. module: pos_sale_product_configurator
#. openerp-web
#: code:addons/pos_sale_product_configurator/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Optional Products"
msgstr "オプションプロダクト"

#. module: pos_sale_product_configurator
#: model:ir.model,name:pos_sale_product_configurator.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS設定"

#. module: pos_sale_product_configurator
#: model:ir.model,name:pos_sale_product_configurator.model_product_product
msgid "Product"
msgstr "プロダクト"

#. module: pos_sale_product_configurator
#. openerp-web
#: code:addons/pos_sale_product_configurator/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "from"
msgstr "自"
