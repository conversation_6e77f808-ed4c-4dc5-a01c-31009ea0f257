<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="ch_br_3_01_03_01_01_03_00" model="account.account.template">
            <field name="code">3.01.03.01.01.03.00</field>
            <field name="name">ganho cambial</field>
            <field name="user_type_id" ref="account.data_account_type_other_income"/>
            <field name="chart_template_id" ref="l10n_br_account_chart_template"/>
         </record>

         <record id="ch_br_3_01_03_01_03_03_00" model="account.account.template">
            <field name="code">3.01.03.01.03.03.00</field>
            <field name="name">Perda cambial</field>
            <field name="user_type_id" ref="account.data_account_type_expenses"/>
            <field name="chart_template_id" ref="l10n_br_account_chart_template"/>
         </record>

		<record id="l10n_br_account_chart_template" model="account.chart.template">
			<field name="property_account_receivable_id" ref="account_template_101050200"/>
			<field name="property_account_payable_id" ref="account_template_201010100" />
			<field name="property_account_expense_categ_id" ref="account_template_3010103010000" />
			<field name="property_account_income_categ_id" ref="account_template_3010101010200" />
            <field name="income_currency_exchange_account_id" ref="ch_br_3_01_03_01_01_03_00"/>
            <field name="expense_currency_exchange_account_id" ref="ch_br_3_01_03_01_03_03_00"/>
			<field name="default_pos_receivable_account_id" ref="account_template_101050201" />
		</record>

		<record id="tax_template_out_ipi" model="account.tax.template">
			<field name="description">IPI </field>
			<field name="name">IPI Saída</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi2" model="account.tax.template">
			<field name="description">IPI 2%</field>
			<field name="name">IPI Saída 2%</field>
			<field name="amount">2</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_2"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi3" model="account.tax.template">
			<field name="description">IPI 3%</field>
			<field name="name">IPI Saída 3%</field>
			<field name="amount">3</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_3"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi4" model="account.tax.template">
			<field name="description">IPI 4%</field>
			<field name="name">IPI Saída 4%</field>
			<field name="amount">4</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_4"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi5" model="account.tax.template">
			<field name="description">IPI 5%</field>
			<field name="name">IPI Saída 5%</field>
			<field name="amount">5</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_5"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi7" model="account.tax.template">
			<field name="description">IPI 7%</field>
			<field name="name">IPI Saída 7%</field>
			<field name="amount">7</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_7"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
        </record>

		<record id="tax_template_out_ipi8" model="account.tax.template">
			<field name="description">IPI 8%</field>
			<field name="name">IPI Saída 8%</field>
			<field name="amount">8</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_8"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi10" model="account.tax.template">
			<field name="description">IPI 10%</field>
			<field name="name">IPI Saída 10%</field>
			<field name="amount">10</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_10"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi12" model="account.tax.template">
			<field name="description">IPI 12%</field>
			<field name="name">IPI Saída 12%</field>
			<field name="amount">12</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_12"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi13" model="account.tax.template">
			<field name="description">IPI 13%</field>
			<field name="name">IPI Saída 13%</field>
			<field name="amount">13</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_13"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi15" model="account.tax.template">
			<field name="description">IPI 15%</field>
			<field name="name">IPI Saída 15%</field>
			<field name="amount">15</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_15"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi16" model="account.tax.template">
			<field name="description">IPI 16%</field>
			<field name="name">IPI Saída 16%</field>
			<field name="amount">16</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_16"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi18" model="account.tax.template">
			<field name="description">IPI 18%</field>
			<field name="name">IPI Saída 18%</field>
			<field name="amount">18</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_18"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi20" model="account.tax.template">
			<field name="description">IPI 20%</field>
			<field name="name">IPI Saída 20%</field>
			<field name="amount">20</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_20"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
		</record>

		<record id="tax_template_out_ipi22" model="account.tax.template">
			<field name="description">IPI 22%</field>
			<field name="name">IPI Saída 22%</field>
			<field name="amount">22</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_22"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi24" model="account.tax.template">
			<field name="description">IPI 24%</field>
			<field name="name">IPI Saída 24%</field>
			<field name="amount">24</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_24"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi25" model="account.tax.template">
			<field name="description">IPI 25%</field>
			<field name="name">IPI Saída 25%</field>
			<field name="amount">25</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_25"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi27" model="account.tax.template">
			<field name="description">IPI 27%</field>
			<field name="name">IPI Saída 27%</field>
			<field name="amount">27</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_27"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi30" model="account.tax.template">
			<field name="description">IPI 30%</field>
			<field name="name">IPI Saída 30%</field>
			<field name="amount">30</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_30"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi35" model="account.tax.template">
			<field name="description">IPI 35%</field>
			<field name="name">IPI Saída 35%</field>
			<field name="amount">35</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_35"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
		</record>

		<record id="tax_template_out_ipi40" model="account.tax.template">
			<field name="description">IPI 40%</field>
			<field name="name">IPI Saída 40%</field>
			<field name="amount">40</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_40"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi42" model="account.tax.template">
			<field name="description">IPI 42%</field>
			<field name="name">IPI Saída 42%</field>
			<field name="amount">42</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_42"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi45" model="account.tax.template">
			<field name="description">IPI 45%</field>
			<field name="name">IPI Saída 45%</field>
			<field name="amount">45</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_45"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi50" model="account.tax.template">
			<field name="description">IPI 50%</field>
			<field name="name">IPI Saída 50%</field>
			<field name="amount">50</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_50"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi60" model="account.tax.template">
			<field name="description">IPI 60%</field>
			<field name="name">IPI Saída 60%</field>
			<field name="amount">60</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_60"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi300" model="account.tax.template">
			<field name="description">IPI 300%</field>
			<field name="name">IPI Saída 300%</field>
			<field name="amount">300</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_300"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_ipi330" model="account.tax.template">
			<field name="description">IPI 330%</field>
			<field name="name">IPI Saída 330%</field>
			<field name="amount">330</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_330"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

       	<record id="tax_template_in_ipi" model="account.tax.template">
			<field name="description">IPI</field>
			<field name="name">IPI Entrada</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi2" model="account.tax.template">
			<field name="description">IPI 2%</field>
			<field name="name">IPI Entrada 2%</field>
			<field name="amount">2</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_2"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi3" model="account.tax.template">
			<field name="description">IPI 3%</field>
			<field name="name">IPI Entrada 3%</field>
			<field name="amount">3</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_3"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi4" model="account.tax.template">
			<field name="description">IPI 4%</field>
			<field name="name">IPI Entrada 4%</field>
			<field name="amount">4</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_4"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi5" model="account.tax.template">
			<field name="description">IPI 5%</field>
			<field name="name">IPI Entrada 5%</field>
			<field name="amount">5</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_5"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi7" model="account.tax.template">
			<field name="description">IPI 7%</field>
			<field name="name">IPI Entrada 7%</field>
			<field name="amount">7</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_7"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
        	</record>

		<record id="tax_template_in_ipi8" model="account.tax.template">
			<field name="description">IPI 8%</field>
			<field name="name">IPI Entrada 8%</field>
			<field name="amount">8</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_8"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi10" model="account.tax.template">
			<field name="description">IPI 10%</field>
			<field name="name">IPI Entrada 10%</field>
			<field name="amount">10</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_10"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi12" model="account.tax.template">
			<field name="description">IPI 12%</field>
			<field name="name">IPI Entrada 12%</field>
			<field name="amount">12</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_12"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi13" model="account.tax.template">
			<field name="description">IPI 13%</field>
			<field name="name">IPI Entrada 13%</field>
			<field name="amount">13</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_13"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi15" model="account.tax.template">
			<field name="description">IPI 15%</field>
			<field name="name">IPI Entrada 15%</field>
			<field name="amount">15</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_15"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi16" model="account.tax.template">
			<field name="description">IPI 16%</field>
			<field name="name">IPI Entrada 16%</field>
			<field name="amount">16</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_16"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi18" model="account.tax.template">
			<field name="description">IPI 18%</field>
			<field name="name">IPI Entrada 18%</field>
			<field name="amount">18</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_18"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi20" model="account.tax.template">
			<field name="description">IPI 20%</field>
			<field name="name">IPI Entrada 20%</field>
			<field name="amount">20</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_20"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
        </record>

		<record id="tax_template_in_ipi22" model="account.tax.template">
			<field name="description">IPI 22%</field>
			<field name="name">IPI Entrada 22%</field>
			<field name="amount">22</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_22"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi24" model="account.tax.template">
			<field name="description">IPI 24%</field>
			<field name="name">IPI Entrada 24%</field>
			<field name="amount">24</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_24"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi25" model="account.tax.template">
			<field name="description">IPI 25%</field>
			<field name="name">IPI Entrada 25%</field>
			<field name="amount">25</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_25"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi27" model="account.tax.template">
			<field name="description">IPI 27%</field>
			<field name="name">IPI Entrada 27%</field>
			<field name="amount">27</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_27"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi30" model="account.tax.template">
			<field name="description">IPI 30%</field>
			<field name="name">IPI Entrada 30%</field>
			<field name="amount">30</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_30"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi35" model="account.tax.template">
			<field name="description">IPI 35%</field>
			<field name="name">IPI Entrada 35%</field>
			<field name="amount">35</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_35"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
		</record>

		<record id="tax_template_in_ipi40" model="account.tax.template">
			<field name="description">IPI 40%</field>
			<field name="name">IPI Entrada 40%</field>
			<field name="amount">40</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_40"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi42" model="account.tax.template">
			<field name="description">IPI 42%</field>
			<field name="name">IPI Entrada 42%</field>
			<field name="amount">42</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_42"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi45" model="account.tax.template">
			<field name="description">IPI 45%</field>
			<field name="name">IPI Entrada 45%</field>
			<field name="amount">45</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_45"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi50" model="account.tax.template">
			<field name="description">IPI 50%</field>
			<field name="name">IPI Entrada 50%</field>
			<field name="amount">50</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_50"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi60" model="account.tax.template">
			<field name="description">IPI 60%</field>
			<field name="name">IPI Entrada 60%</field>
			<field name="amount">60</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_60"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi300" model="account.tax.template">
			<field name="description">IPI 300%</field>
			<field name="name">IPI Entrada 300%</field>
			<field name="amount">300</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_300"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_ipi330" model="account.tax.template">
			<field name="description">IPI 330%</field>
			<field name="name">IPI Entrada 330%</field>
			<field name="amount">330</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ipi_330"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050502'),
	                'minus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ipi_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010301'),
	                'plus_report_line_ids': [ref('tax_report_ipi_2')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_icms_interno" model="account.tax.template">
			<field name="description">ICMS Interno</field>
			<field name="name">ICMS Saida Interno</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_icms_externo" model="account.tax.template">
			<field name="description">ICMS Externo</field>
			<field name="name">ICMS Saida Externo</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_icms_subist" model="account.tax.template">
			<field name="description">ICMS Subist</field>
			<field name="name">ICMS Saida Subist</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icmsst_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icmsst_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_icms_externo7" model="account.tax.template">
			<field name="description">ICMS Externo 7%</field>
			<field name="name">ICMS Saida Externo 7%</field>
			<field name="amount">7</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_7"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010302'),
	                'plus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050505'),
	                'minus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_icms_externo12" model="account.tax.template">
			<field name="description">ICMS Externo 12%</field>
			<field name="name">ICMS Saida Externo 12%</field>
			<field name="amount">12</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_12"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010302'),
	                'plus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050505'),
	                'minus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_icms_interno19" model="account.tax.template">
			<field name="description">ICMS Interno 19%</field>
			<field name="name">ICMS Saida Interno 19%</field>
			<field name="amount">19</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_19"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010302'),
	                'plus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050505'),
	                'minus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_icms_interno26" model="account.tax.template">
			<field name="description">ICMS Interno 26%</field>
			<field name="name">ICMS Saida Interno 26%</field>
			<field name="amount">26</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_26"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010302'),
	                'plus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050505'),
	                'minus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
       	</record>

       	<record id="tax_template_in_icms_interno" model="account.tax.template">
			<field name="description">ICMS Interno</field>
			<field name="name">ICMS Entrada Interno</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_icms_externo" model="account.tax.template">
			<field name="description">ICMS Externo</field>
			<field name="name">ICMS Entrada Externo</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_icms_subist" model="account.tax.template">
			<field name="description">ICMS Subist</field>
			<field name="name">ICMS Entrada Subist</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="0" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icmsst_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icmsst_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_icms_externo7" model="account.tax.template">
			<field name="description">ICMS Externo 7%</field>
			<field name="name">ICMS Entrada Externo 7%</field>
			<field name="amount">7</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_7"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050505'),
	                'minus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010302'),
	                'plus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_icms_externo12" model="account.tax.template">
			<field name="description">ICMS Externo 12%</field>
			<field name="name">ICMS Entrada Externo 12%</field>
			<field name="amount">12</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_12"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050505'),
	                'minus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010302'),
	                'plus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_icms_interno19" model="account.tax.template">
			<field name="description">ICMS Interno 19%</field>
			<field name="name">ICMS Entrada Interno 19%</field>
			<field name="amount">19</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_19"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050505'),
	                'minus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010302'),
	                'plus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_icms_interno26" model="account.tax.template">
			<field name="description">ICMS Interno 26%</field>
			<field name="name">ICMS Entrada Interno 26%</field>
			<field name="amount">26</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_icms_26"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050505'),
	                'minus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_icms_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010302'),
	                'plus_report_line_ids': [ref('tax_report_icms_1')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_pis" model="account.tax.template">
			<field name="description">PIS</field>
			<field name="name">PIS Saida</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_pis_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_pis_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_pis_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_pis065" model="account.tax.template">
			<field name="description">PIS 0,65%</field>
			<field name="name">PIS Saida 0,65%</field>
			<field name="amount">0.65</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_pis_065"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_pis_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010500'),
	                'plus_report_line_ids': [ref('tax_report_pis_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_pis_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050503'),
	                'minus_report_line_ids': [ref('tax_report_pis_1')],
	            }),
	        ]"/>
       	</record>

       	<record id="tax_template_in_pis" model="account.tax.template">
			<field name="description">PIS</field>
			<field name="name">PIS Entrada</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_pis_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_pis_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_pis_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_pis065" model="account.tax.template">
			<field name="description">PIS 0,65%</field>
			<field name="name">PIS Entrada 0,65%</field>
			<field name="amount">0.65</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_pis_065"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_pis_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050503'),
	                'minus_report_line_ids': [ref('tax_report_pis_1')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_pis_2')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010500'),
	                'plus_report_line_ids': [ref('tax_report_pis_1')],
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_cofins" model="account.tax.template">
			<field name="description">COFINS</field>
			<field name="name">COFINS Saida</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_cofins_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_cofins_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_cofins_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_out_cofins3" model="account.tax.template">
			<field name="description">COFINS 3%</field>
			<field name="name">COFINS Saida 3%</field>
			<field name="amount">3</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_cofins_3"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_cofins_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010500'),
	                'plus_report_line_ids': [ref('tax_report_cofins_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_cofins_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050503'),
	                'minus_report_line_ids': [ref('tax_report_cofins_2')],
	            }),
	        ]"/>
        </record>

        <record id="tax_template_in_cofins" model="account.tax.template">
			<field name="description">COFINS</field>
			<field name="name">COFINS Entrada</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_cofins_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_cofins_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_cofins_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
       	</record>

		<record id="tax_template_in_cofins3" model="account.tax.template">
			<field name="description">COFINS 3%</field>
			<field name="name">COFINS Entrada 3%</field>
			<field name="amount">3</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_cofins_3"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_cofins_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_101050503'),
	                'minus_report_line_ids': [ref('tax_report_cofins_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_cofins_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'account_id': ref('account_template_201010500'),
	                'plus_report_line_ids': [ref('tax_report_cofins_2')],
	            }),
	        ]"/>
        </record>

		<record id="tax_template_out_irpj" model="account.tax.template">
			<field name="description">IRPJ</field>
			<field name="name">IRPJ</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_irpj_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_irpj_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_irpj_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
        </record>

		<record id="tax_template_out_ir" model="account.tax.template">
			<field name="description">IR</field>
			<field name="name">IR</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_ir_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_ir_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_ir_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
        </record>

		<record id="tax_template_out_issqn" model="account.tax.template">
			<field name="description">ISSQN</field>
			<field name="name">ISSQN Saida</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_issqn_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
        </record>

        <record id="tax_template_out_issqn1" model="account.tax.template">
			<field name="description">ISSQN 1%</field>
			<field name="name">ISSQN Saida 1%</field>
			<field name="amount">1</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_issqn_1"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'plus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'minus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
        </record>

        <record id="tax_template_out_issqn2" model="account.tax.template">
			<field name="description">ISSQN 2%</field>
			<field name="name">ISSQN Saida 2%</field>
			<field name="amount">2</field>
            <field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_issqn_2"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'plus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'minus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
        </record>
		<record id="tax_template_in_issqn2" model="account.tax.template">
			<field name="description">ISSQN 2%</field>
			<field name="name">ISSQN Entrada 2%</field>
			<field name="amount">2</field>
			<field name="type_tax_use">purchase</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_issqn_2"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'minus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'plus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
		</record>

        <record id="tax_template_out_issqn3" model="account.tax.template">
			<field name="description">ISSQN 3%</field>
			<field name="name">ISSQN Saida 3%</field>
			<field name="amount">3</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_issqn_3"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'plus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'minus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
        </record>

        <record id="tax_template_out_issqn4" model="account.tax.template">
			<field name="description">ISS 4%</field>
			<field name="name">ISSQN Saida 4%</field>
			<field name="amount">4</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_issqn_4"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'plus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'minus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
        </record>

        <record id="tax_template_out_issqn5" model="account.tax.template">
			<field name="description">ISSQN 5%</field>
			<field name="name">ISSQN Saida 5%</field>
			<field name="amount">5</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_issqn_5"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'plus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_issqn_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	                'minus_report_line_ids': [ref('tax_report_issqn_2')],
	            }),
	        ]"/>
        </record>

		<record id="tax_template_out_csll" model="account.tax.template">
			<field name="description">CSLL</field>
			<field name="name">CSLL</field>
			<field name="amount">0.00</field>
			<field name="type_tax_use">sale</field>
			<field eval="0" name="price_include"/>
			<field eval="1" name="tax_discount"/>
			<field ref="l10n_br_account_chart_template" name="chart_template_id"/>
			<field name="tax_group_id" ref="tax_group_csll_0"/>
			<field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'plus_report_line_ids': [ref('tax_report_csll_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
	        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'base',
	                'minus_report_line_ids': [ref('tax_report_csll_1')],
	            }),
	            (0,0, {
	                'factor_percent': 100,
	                'repartition_type': 'tax',
	            }),
	        ]"/>
        </record>

        <data noupdate="1">
            <function model="account.chart.template" name="try_loading">
                <value eval="[ref('l10n_br.l10n_br_account_chart_template')]"/>
            </function>
        </data>
</odoo>
