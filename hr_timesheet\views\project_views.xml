<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="act_hr_timesheet_line_by_project" model="ir.actions.act_window">
            <field name="name">Timesheets</field>
            <field name="res_model">account.analytic.line</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="timesheet_view_tree_user"/>
            <field name="domain">[('project_id', '!=', False)]</field>
            <field name="context">{"default_project_id": active_id, "search_default_project_id": [active_id]}</field>
            <field name="search_view_id" ref="hr_timesheet_line_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Record a new activity
              </p><p>
                Track your working hours by projects every day and invoice this time to your customers.
              </p>
            </field>
        </record>

        <record id="project_project_view_form_simplified_inherit_timesheet" model="ir.ui.view">
            <field name="name">project.project.view.form.simplified.inherit.timesheet</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="project.project_project_view_form_simplified"/>
            <field name="priority">24</field>
            <field name="arch" type="xml">
                <field name="user_id" position="before">
                    <field name="allow_timesheets"/>
                </field>
            </field>
        </record>

        <record id="project_invoice_form" model="ir.ui.view">
            <field name="name">Inherit project form : Invoicing Data</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="project.edit_project"/>
            <field name="priority">24</field>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='%(project.project_milestone_all)d']" position="after">
                    <button class="oe_stat_button" name="action_show_timesheets_by_employee_invoice_type" type="object" icon="fa-clock-o" attrs="{'invisible': [('allow_timesheets', '=', False)]}" groups="hr_timesheet.group_hr_timesheet_user">
                        <div class="o_field_widget o_stat_info">
                            <div class="oe_inline">
                                <span class="o_stat_value mr-1">
                                    <field name="total_timesheet_time" widget="statinfo" nolabel="1"/>
                                </span>
                                <span class="o_stat_value">
                                    <field name="timesheet_encode_uom_id" class="o_stat_text" options="{'no_open' : True}"/>
                                </span>
                            </div>
                            <span class="o_stat_text">Recorded</span>
                        </div>
                    </button>
                </xpath>
                <xpath expr="//div[@id='rating_settings']" position="before">
                    <div class="col-lg-6 o_setting_box"  id="timesheet_settings">
                        <div class="o_setting_left_pane">
                            <field name="allow_timesheets"/>
                        </div>
                        <div class="o_setting_right_pane">
                            <label for="allow_timesheets" string="Timesheets"/>
                            <div class="text-muted">
                                Log time on tasks
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

        <record model="ir.ui.view" id="view_task_form2_inherited">
            <field name="name">project.task.form.inherited</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_form2" />
            <field name="groups_id" eval="[(6,0, (ref('hr_timesheet.group_hr_timesheet_user'),))]"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='child_ids']/tree/field[@name='kanban_state']" position="after">
                    <field name="planned_hours" widget="timesheet_uom_no_toggle" sum="Initially Planned Hours" optional="hide"/>
                    <field name="effective_hours" widget="timesheet_uom" sum="Effective Hours" optional="hide"/>
                    <field name="remaining_hours" widget="timesheet_uom" sum="Remaining Hours" optional="hide" decoration-danger="progress &gt;= 100" decoration-warning="progress &gt;= 80 and progress &lt; 100"/>
                    <field name="progress" widget="progressbar" optional="hide"/>
                </xpath>
                <xpath expr="//notebook/page[@name='description_page']" position="after">
                    <field name="analytic_account_active" invisible="1"/>
                    <field name="allow_timesheets" invisible="1"/>
                    <field name="allow_subtasks" invisible="1"/>
                    <field name="encode_uom_in_days" invisible="1"/>
                    <field name="subtask_count" invisible="1"/>
                    <page string="Timesheets" id="timesheets_tab" attrs="{'invisible': [('allow_timesheets', '=', False)]}">
                        <group>
                            <group>
                                <div class="o_td_label">
                                    <label for="planned_hours" string="Initially Planned Hours" attrs="{'invisible': [('encode_uom_in_days', '=', True)]}"/>
                                    <label for="planned_hours" string="Initially Planned Days" attrs="{'invisible': [('encode_uom_in_days', '=', False)]}"/>
                                </div>
                                <field name="planned_hours" widget="timesheet_uom_no_toggle" nolabel="1"/>
                                <div class="o_td_label" groups="project.group_subtask_project" attrs="{'invisible': ['|', ('allow_subtasks', '=', False), ('subtask_count', '=', 0)]}">
                                    <label for="subtask_planned_hours" string="Sub-tasks Planned Hours" attrs="{'invisible': [('encode_uom_in_days', '=', True)]}"/>
                                    <label for="subtask_planned_hours" string="Sub-tasks Planned Days" attrs="{'invisible': [('encode_uom_in_days', '=', False)]}"/>
                                </div>
                                <field name="subtask_planned_hours" widget="timesheet_uom_no_toggle" nolabel="1" groups="project.group_subtask_project" attrs="{'invisible': ['|', ('allow_subtasks', '=', False), ('subtask_count', '=', 0)]}"/>
                            </group>
                            <group>
                                <field name="progress" widget="progressbar"/>
                            </group>
                        </group>
                        <group name="timesheet_error" attrs="{'invisible': [('analytic_account_active', '!=', False)]}">
                            <div class="alert alert-warning" role="alert">
                                You cannot log timesheets on this project since it is linked to an inactive analytic account. Please change this account, or reactivate the current one to timesheet on the project.
                            </div>
                        </group>
                    <field name="timesheet_ids" mode="tree,kanban" attrs="{'invisible': [('analytic_account_active', '=', False)]}" context="{'default_project_id': project_id, 'default_name':''}">
                        <tree editable="bottom" string="Timesheet Activities" default_order="date">
                            <field name="date"/>
                            <field name="user_id" invisible="1"/>
                            <field name="employee_id" required="1" widget="many2one_avatar_employee" context="{'active_test': True}"/>
                            <field name="name" required="0"/>
                            <field name="unit_amount" widget="timesheet_uom" decoration-danger="unit_amount &gt; 24"/>
                            <field name="project_id" invisible="1"/>
                            <field name="task_id" invisible="1"/>
                            <field name="company_id" invisible="1"/>
                        </tree>
                        <kanban class="o_kanban_mobile">
                            <field name="date"/>
                            <field name="user_id"/>
                            <field name="employee_id" widget="many2one_avatar_employee" context="{'active_test': True}"/>
                            <field name="name"/>
                            <field name="unit_amount" decoration-danger="unit_amount &gt; 24"/>
                            <field name="project_id"/>
                            <field name="task_id" invisible="1"/>
                            <templates>
                                <t t-name="kanban-box">
                                    <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong><span><t t-esc="record.employee_id.value"/></span></strong>
                                            </div>
                                            <div class="col-6 pull-right text-right">
                                                <strong><t t-esc="record.date.value"/></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6 text-muted">
                                                <span><t t-esc="record.name.value"/></span>
                                            </div>
                                            <div class="col-6">
                                                <span class="pull-right text-right">
                                                    <field name="unit_amount" widget="float_time"/>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </templates>
                        </kanban>
                        <form  string="Timesheet Activities">
                            <sheet>
                                 <group>
                                    <field name="date"/>
                                    <field name="user_id" invisible="1"/>
                                    <field name="employee_id" required="1" widget="many2one_avatar_employee" context="{'active_test': True}"/>
                                    <field name="name" required="0"/>
                                    <field name="unit_amount" string="Duration" widget="float_time" decoration-danger="unit_amount &gt; 24"/>
                                    <field name="project_id" invisible="1"/>
                                    <field name="task_id" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                </group>
                            </sheet>
                        </form>
                    </field>
                    <group attrs="{'invisible': [('analytic_account_active', '=', False)]}">
                        <group class="oe_subtotal_footer oe_right" name="project_hours">
                            <span>
                                <label class="font-weight-bold" for="effective_hours" string="Hours Spent" attrs="{'invisible': [('encode_uom_in_days', '=', True)]}"/>
                                <label class="font-weight-bold" for="effective_hours" string="Days Spent" attrs="{'invisible': [('encode_uom_in_days', '=', False)]}"/>
                            </span>
                            <field name="effective_hours" widget="timesheet_uom" nolabel="1"/>

                            <button name="action_view_subtask_timesheet" type="object" class="o_form_subtask_button oe_inline oe_link" attrs="{'invisible' : ['|', ('allow_subtasks', '=', False), ('subtask_effective_hours', '=', 0.0)]}">
                                <span class="text-nowrap" attrs="{'invisible' : [('encode_uom_in_days', '=', True)]}">Hours Spent on Sub-tasks:</span>
                                <span class="text-nowrap" attrs="{'invisible' : [('encode_uom_in_days', '=', False)]}">Days Spent on Sub-tasks:</span>
                            </button>
                            <field name="subtask_effective_hours" class="mt-2" widget="timesheet_uom"
                                   attrs="{'invisible' : ['|', ('allow_subtasks', '=', False), ('subtask_effective_hours', '=', 0.0)]}" nolabel="1"/>
                            <span>
                                <label class="font-weight-bold" for="total_hours_spent" string="Total Hours"
                                       attrs="{'invisible': ['|', '|', ('allow_subtasks', '=', False), ('subtask_effective_hours', '=', 0.0), ('encode_uom_in_days', '=', True)]}"/>
                                <label class="font-weight-bold" for="total_hours_spent" string="Total Days"
                                       attrs="{'invisible': ['|', '|', ('allow_subtasks', '=', False), ('subtask_effective_hours', '=', 0.0), ('encode_uom_in_days', '=', False)]}"/>
                            </span>
                            <field name="total_hours_spent" widget="timesheet_uom" class="oe_subtotal_footer_separator" nolabel="1"
                                   attrs="{'invisible' : ['|', ('allow_subtasks', '=', False), ('subtask_effective_hours', '=', 0.0)]}" />
                            <span>
                                <label class="font-weight-bold" for="remaining_hours" string="Remaining Hours"
                                       attrs="{'invisible': ['|', ('planned_hours', '=', 0.0), ('encode_uom_in_days', '=', True)]}"/>
                                <label class="font-weight-bold" for="remaining_hours" string="Remaining Days"
                                       attrs="{'invisible': ['|', ('planned_hours', '=', 0.0), ('encode_uom_in_days', '=', False)]}"/>
                            </span>
                            <field name="remaining_hours" widget="timesheet_uom" class="oe_subtotal_footer_separator"
                                   attrs="{'invisible' : [('planned_hours', '=', 0.0)]}" nolabel="1"/>
                        </group>
                    </group>
                </page>
                </xpath>
                <xpath expr="//field[@name='depend_on_ids']/tree//field[@name='company_id']" position="after">
                    <field name="allow_subtasks" invisible="1" />
                    <field name="planned_hours" widget="timesheet_uom_no_toggle" sum="Initially Planned Hours" optional="hide"/>
                    <field name="effective_hours" widget="timesheet_uom" sum="Effective Hours" optional="hide"/>
                    <field name="remaining_hours" widget="timesheet_uom" sum="Remaining Hours" optional="hide" decoration-danger="progress &gt;= 100" decoration-warning="progress &gt;= 80 and progress &lt; 100"/>
                    <field name="subtask_effective_hours" widget="timesheet_uom" attrs="{'invisible' : [('allow_subtasks', '=', False)]}" optional="hide"/>
                    <field name="total_hours_spent" widget="timesheet_uom" attrs="{'invisible' : [('allow_subtasks', '=', False)]}" optional="hide"/>
                    <field name="progress" widget="progressbar" optional="hide" groups="hr_timesheet.group_hr_timesheet_user"/>
                </xpath>
            </field>
        </record>

        <record id="view_task_tree2_inherited" model="ir.ui.view">
            <field name="name">project.task.tree.inherited</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_tree2" />
            <field eval="2" name="priority"/>
            <field name="arch" type="xml">
                <field name="company_id" position="after">
                    <field name="allow_subtasks" invisible="1"/>
                    <field name="planned_hours" widget="timesheet_uom_no_toggle" sum="Initially Planned Hours" optional="hide"/>
                    <field name="effective_hours" widget="timesheet_uom" sum="Effective Hours" optional="show"/>
                    <field name="subtask_effective_hours" widget="timesheet_uom" attrs="{'invisible' : [('allow_subtasks', '=', False)]}" optional="hide"/>
                    <field name="total_hours_spent" widget="timesheet_uom" attrs="{'invisible' : [('allow_subtasks', '=', False)]}" optional="hide"/>
                    <field name="remaining_hours" widget="timesheet_uom" sum="Remaining Hours" optional="hide" decoration-danger="progress &gt;= 100" decoration-warning="progress &gt;= 80 and progress &lt; 100"/>
                    <field name="progress" widget="progressbar" optional="show" groups="hr_timesheet.group_hr_timesheet_user"/>
                </field>
            </field>
        </record>

        <record id="view_project_kanban_inherited" model="ir.ui.view">
            <field name="name">project.project.timesheet.kanban.inherited</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="project.view_project_kanban"/>
            <field name="priority">24</field>
            <field name="arch" type="xml">
                <field name="partner_id" position="after">
                    <field name="allow_timesheets" invisible="1"/>
                    <field name="timesheet_count" invisible="1"/>
                    <field name="remaining_hours" invisible="1"/>
                    <field name="has_planned_hours_tasks" invisible="1"/>
                    <field name="encode_uom_in_days" invisible="1"/>
                </field>
                <xpath expr="//div[hasclass('o_kanban_manage_view')]" position="inside">
                    <div role="menuitem" t-if="record.allow_timesheets.raw_value and record.timesheet_count.raw_value" groups="hr_timesheet.group_hr_timesheet_user">
                        <a name="%(act_hr_timesheet_line_by_project)d" type="action">Timesheets</a>
                    </div>
                </xpath>
                <xpath expr="//div[hasclass('o_project_kanban_boxes')]" position="after">
                    <t t-set="badge" t-value=""/>
                    <t t-set="badge" t-value="'badge-danger'" t-if="record.remaining_hours.raw_value &lt; 0"/>
                    <t t-set="title" t-value="'Remaining days'" t-if="record.encode_uom_in_days.raw_value"/>
                    <t t-set="title" t-value="'Remaining hours'" t-else=""/>
                    <div t-if="record.has_planned_hours_tasks.raw_value"
                        t-attf-class="oe_kanban_align badge {{ badge }}" t-att-title="title" groups="hr_timesheet.group_hr_timesheet_user">
                        <field name="remaining_hours" widget="timesheet_uom"/>
                    </div>
                </xpath>
            </field>
        </record>

        <record id="view_task_kanban_inherited_progress" model="ir.ui.view">
            <field name="name">project.task.timesheet.kanban.inherited.progress</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_kanban"/>
            <field name="arch" type="xml">
                <templates position="before">
                    <field name="progress" />
                    <field name="remaining_hours" />
                    <field name="planned_hours" />
                    <field name="allow_timesheets"/>
                    <field name="encode_uom_in_days" invisible="1"/>
                </templates>
                <div class="oe_kanban_bottom_left" position="inside">
                   <t name="planned_hours" t-if="record.planned_hours.raw_value &gt; 0 and record.allow_timesheets.raw_value" groups="hr_timesheet.group_hr_timesheet_user">
                        <t t-set="badge" t-value=""/>
                        <t t-set="badge" t-value="'badge-warning'" t-if="record.progress.raw_value &gt;= 80 and record.progress.raw_value &lt;= 100"/>
                        <t t-set="badge" t-value="'badge-danger'" t-if="record.remaining_hours.raw_value &lt; 0"/>
                        <t t-set="title" t-value="'Remaining days'" t-if="record.encode_uom_in_days.raw_value"/>
                        <t t-set="title" t-value="'Remaining hours'" t-else=""/>
                        <div t-attf-class="oe_kanban_align badge {{ badge }}" t-att-title="title">
                            <field name="remaining_hours" widget="timesheet_uom" />
                        </div>
                   </t>
                </div>
             </field>
         </record>

        <record id="project_task_view_search" model="ir.ui.view">
            <field name="name">project.task.view.search.inherit.sale.timesheet.enterprise</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_search_form"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='my_tasks']" position='after'>
                    <filter string="My Team's Tasks" name="my_team_tasks" domain="[('user_ids.employee_parent_id.user_id', '=', uid)]"/>
                </xpath>
                <xpath expr="//filter[@name='late']" position='after'>
                    <filter string="Tasks in Overtime" name="overtime" domain="[('overtime', '&gt;', 0)]"/>
                </xpath>
            </field>
        </record>

        <record id="project_task_view_graph" model="ir.ui.view">
            <field name="name">project.task.view.graph.inherited</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_project_task_graph"/>
            <field name="arch" type="xml">
                <xpath expr="//graph" position="attributes">
                    <attribute name="js_class">hr_timesheet_graphview</attribute>
                </xpath>
                <xpath expr="//field[@name='stage_id']" position='after'>
                    <field name="planned_hours" widget="timesheet_uom" type="measure"/>
                    <field name="remaining_hours" widget="timesheet_uom" type="measure"/>
                    <field name="effective_hours" widget="timesheet_uom" type="measure"/>
                    <field name="total_hours_spent" widget="timesheet_uom" type="measure"/>
                    <field name="overtime" widget="timesheet_uom" type="measure"/>
                    <field name="subtask_effective_hours" widget="timesheet_uom" type="measure"/>
                </xpath>
            </field>
        </record>

        <record id="project_task_view_pivot" model="ir.ui.view">
            <field name="name">project.task.view.pivot.inherited</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_project_task_pivot"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='stage_id']" position='after'>
                    <field name="planned_hours" widget="timesheet_uom" type="measure"/>
                    <field name="remaining_hours" widget="timesheet_uom" type="measure"/>
                    <field name="effective_hours" widget="timesheet_uom" type="measure"/>
                    <field name="total_hours_spent" widget="timesheet_uom" type="measure"/>
                    <field name="overtime" widget="timesheet_uom" type="measure"/>
                    <field name="subtask_effective_hours" widget="timesheet_uom" type="measure"/>
                </xpath>
            </field>
        </record>

        <record id="project.open_view_project_all" model="ir.actions.act_window">
            <field name="domain">[('is_internal_project', '=', False)]</field>
        </record>

        <record id="project.open_view_project_all_group_stage" model="ir.actions.act_window">
            <field name="domain">[('is_internal_project', '=', False)]</field>
        </record>

    </data>
</odoo>
