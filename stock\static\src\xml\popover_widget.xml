<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="stock.popoverButton">
        <a tabindex="0" t-attf-class="p-1 fa #{ icon || 'fa-info-circle'} #{ color || 'text-primary'}"/>
    </t>

    <div t-name="stock.popoverContent">
        <t t-esc="msg"/>
    </div>

    <div t-name="stock.PopoverStockRescheduling">
        <p>Preceding operations
        <t t-foreach="late_elements" t-as="late_element">
            <a t-esc="late_element.name" href="#" t-att-element-id="late_element.id" t-att-element-model="late_element.model"/>,
        </t>
        planned on <t t-esc="delay_alert_date"/>.</p>
    </div>
</templates>
