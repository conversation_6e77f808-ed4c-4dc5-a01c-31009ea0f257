<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <record id="action_account_move_bir_2307" model="ir.actions.server">
        <field name="name">Download BIR 2307 XLS</field>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="binding_view_types">list,form</field>
        <field name="state">code</field>
        <field name="code">action = records.action_open_l10n_ph_2307_wizard()</field>
    </record>
</odoo>
