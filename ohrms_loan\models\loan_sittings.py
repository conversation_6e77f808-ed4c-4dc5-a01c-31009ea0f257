# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from datetime import datetime
from dateutil.relativedelta import relativedelta
from odoo.exceptions import ValidationError, UserError


class HrLoanSittings(models.Model):
    _name = 'hr.loan.sittings'
    _description = "Loan Request Sittings"

    name = fields.Char(string="الاسم", required=True)
    number_of_installments = fields.Integer(string="عدد الدفعات", required=True)
    max_amount = fields.Float(string="أعلى قيمة", required=True)
    min_amount = fields.Float(string="أقل قيمة", required=True)