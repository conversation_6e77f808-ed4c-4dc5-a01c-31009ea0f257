// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_ThreadPreview {
    @include o-mail-notification-list-item-layout();

    &:hover .o_ThreadPreview_markAsRead {
        opacity: 1;
    }
}

.o_ThreadPreview_content {
    @include o-mail-notification-list-item-content-layout();
}

.o_ThreadPreview_core {
    @include o-mail-notification-list-item-core-layout();
}

.o_ThreadPreview_coreItem {
    @include o-mail-notification-list-item-core-item-layout();
}

.o_ThreadPreview_counter {
    @include o-mail-notification-list-item-counter-layout();
}

.o_ThreadPreview_date {
    @include o-mail-notification-list-item-date-layout();
}

.o_ThreadPreview_header {
    @include o-mail-notification-list-item-header-layout();
}

.o_ThreadPreview_image {
    @include o-mail-notification-list-item-image-layout();
}

.o_ThreadPreview_imageContainer {
    @include o-mail-notification-list-item-image-container-layout();
}

.o_ThreadPreview_inlineText {
    @include o-mail-notification-list-item-inline-text-layout();
}

.o_ThreadPreview_markAsRead {
    @include o-mail-notification-list-item-mark-as-read-layout();
}

.o_ThreadPreview_name {
    @include o-mail-notification-list-item-name-layout();
}

.o_ThreadPreview_partnerImStatusIcon {
    @include o-mail-notification-list-item-partner-im-status-icon-layout();
}

.o_ThreadPreview_sidebar {
    @include o-mail-notification-list-item-sidebar-layout();
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_ThreadPreview {
    @include o-mail-notification-list-item-style();

    &:hover {
        .o_ThreadPreview_partnerImStatusIcon {
            @include o-mail-notification-list-item-hover-partner-im-status-icon-style();
        }
    }

    &.o-muted {
        &:hover {
            .o_ThreadPreview_partnerImStatusIcon {
                @include o-mail-notification-list-item-muted-hover-partner-im-status-icon-style();
            }
        }
    }
}

.o_ThreadPreview_callIndicator.o-isCalling {
    color: red;
}

.o_ThreadPreview_core {
    @include o-mail-notification-list-item-core-style();
}

.o_ThreadPreview_counter {
    @include o-mail-notification-list-item-bold-style();
}

.o_ThreadPreview_date {
    @include o-mail-notification-list-item-date-style();
}

.o_ThreadPreview_image {
    @include o-mail-notification-list-item-image-style();
}

.o_ThreadPreview_markAsRead {
    @include o-mail-notification-list-item-mark-as-read-style();
}

.o_ThreadPreview_name {
    @include o-mail-notification-list-item-bold-style();
}

.o_ThreadPreview_partnerImStatusIcon {
    @include o-mail-notification-list-item-partner-im-status-icon-style();
}
