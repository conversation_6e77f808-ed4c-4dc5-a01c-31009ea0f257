# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <judyta.ka<PERSON><PERSON><PERSON><PERSON>@openglobe.pl>, 2021
# <PERSON><PERSON><PERSON> <sz<PERSON><PERSON><PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <piotr.w.c<PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"- Grupa podatków: Podatek jest zbiorem podatków cząstkowych.\n"
"- Stały: Kwota podatku pozostaje taka sama niezależnie od ceny.\n"
"- Procent ceny: Kwota podatku jest % ceny:\n"
"np. 100 * (1 + 10%) = 110 (bez ceny)\n"
"np. 110 / (1 + 10%) = 100 (cena wliczona)\n"
"- Procent ceny wliczony w cenę: Kwota podatku jest podziałem ceny:\n"
"np. 180 / (1 - 10%) = 200 (bez ceny wliczonej)\n"
"np. 200 * (1 - 10%) = 180 (cena wliczona)"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_applicable
msgid "Applicable Code"
msgstr "Odpowiedni kod"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Obliczyć kwotę podatku przez ustawienie zmiennej \"wynik\".\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Oblicz wartość podatku poprzez ustawienie zmiennej 'wynik'.\n"
"\n"
":param base_amount: float, rzeczywista kwota od której podatek jest naliczony\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Ustal, czy podatek będzie stosowany przez ustawienie zmiennej \"wynik\" True lub False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Ustal, czy podatek będzie stosowany przez ustawienie zmiennej \"wynik\" True lub False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax_template__amount_type__code
msgid "Python Code"
msgstr "Kod Python"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Podatek"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Obliczenie podatku"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Szablony podatków"

#. module: account_tax_python
#: code:addons/account_tax_python/models/account_tax.py:0
#: code:addons/account_tax_python/models/account_tax.py:0
#, python-format
msgid ""
"You entered invalid code %r in %r taxes\n"
"\n"
"Error : %s"
msgstr ""
"Wprowadzono nieprawidłowy kod %r w %r podatkach\n"
"\n"
"Błąd : %s"
