# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_mail
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_mail
#: model:ir.model.fields,field_description:website_mail.field_account_invoice_send__description
#: model:ir.model.fields,field_description:website_mail.field_mail_compose_message__description
#: model:ir.model.fields,field_description:website_mail.field_mail_mail__description
#: model:ir.model.fields,field_description:website_mail.field_mail_message__description
msgid "Description"
msgstr "Opis"

#. module: website_mail
#: model:mail.channel,name:website_mail.channel_public
msgid "Discussion Group"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_mail_message
msgid "Message"
msgstr "Poruka"

#. module: website_mail
#: model:ir.model.fields,help:website_mail.field_account_invoice_send__description
#: model:ir.model.fields,help:website_mail.field_mail_compose_message__description
#: model:ir.model.fields,help:website_mail.field_mail_mail__description
#: model:ir.model.fields,help:website_mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr ""

#. module: website_mail
#. openerp-web
#: code:addons/website_mail/static/src/xml/website_mail.xml:7
#: model:ir.model.fields,field_description:website_mail.field_account_invoice_send__website_published
#: model:ir.model.fields,field_description:website_mail.field_mail_compose_message__website_published
#: model:ir.model.fields,field_description:website_mail.field_mail_mail__website_published
#: model:ir.model.fields,field_description:website_mail.field_mail_message__website_published
#, python-format
msgid "Published"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Subscribe"
msgstr "Pretplati se"

#. module: website_mail
#: code:addons/website_mail/models/mail_message.py:57
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""

#. module: website_mail
#. openerp-web
#: code:addons/website_mail/static/src/xml/website_mail.xml:6
#, python-format
msgid "Unpublished"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Unsubscribe"
msgstr ""

#. module: website_mail
#: model:ir.model.fields,help:website_mail.field_account_invoice_send__website_published
#: model:ir.model.fields,help:website_mail.field_mail_compose_message__website_published
#: model:ir.model.fields,help:website_mail.field_mail_mail__website_published
#: model:ir.model.fields,help:website_mail.field_mail_message__website_published
msgid "Visible on the website as a comment"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "your email..."
msgstr ""
