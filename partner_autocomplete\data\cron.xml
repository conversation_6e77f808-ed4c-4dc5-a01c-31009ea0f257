<odoo>
    <record id="ir_cron_partner_autocomplete" model="ir.cron">
        <field name="name">Partner Autocomplete : Sync with remote DB</field>
        <field name="model_id" ref="model_res_partner_autocomplete_sync"/>
        <field name="state">code</field>
        <field name="code">model.start_sync()</field>
        <field name="interval_number">60</field>
        <field name="interval_type">minutes</field>
        <field name="numbercall">-1</field>
        <field eval="False" name="doall"/>
    </record>
</odoo>
