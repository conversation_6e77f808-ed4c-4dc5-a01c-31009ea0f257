<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_assign_serial_numbers_production" model="ir.ui.view">
        <field name="name">mrp_assign_serial_numbers</field>
        <field name="model">stock.assign.serial</field>
        <field name="arch" type="xml">
            <form string="Serial Mass Produce">
                <group>
                    <field name="production_id" readonly="True"/>
                </group>
                <group>
                    <group>
                        <field name="next_serial_number"/>
                    </group>
                    <group>
                        <label for="next_serial_count"/>
                        <div class="o_row">
                            <span><field name="next_serial_count"/></span>
                            <button name="generate_serial_numbers_production" type="object" class="btn btn-secondary" title="Generate Serial Numbers">
                                <span>Generate</span>
                            </button>
                        </div>
                    </group>
                </group>
                <group>
                    <field name="serial_numbers" placeholder="copy paste a list and/or use Generate"/>
                </group>
                <field name="show_apply" invisible="1" />
                <field name="show_backorders" invisible="1" />
                <group>
                    <group>
                        <field name="produced_qty" readonly="True" force_save="True"/>
                    </group>
                    <group>
                        <field name="expected_qty" readonly="True"/>
                    </group>
                    <p class="o_form_label oe_inline" attrs="{'invisible': [('show_backorders', '=', False)]}">
                        You have entered less serial numbers than the quantity to produce.<br/>
                        Create a backorder if you expect to process the remaining quantities later.<br/>
                        Do not create a backorder if you will not process the remaining products.
                    </p>
                </group>
                <footer>
                    <button name="apply" string="Apply" type="object" class="btn-primary" attrs="{'invisible': [('show_apply', '=', False)]}"/>
                    <button name="create_backorder" string="Create Backorder" type="object" class="btn-primary" attrs="{'invisible': [('show_backorders', '=', False)]}"/>
                    <button name="no_backorder" string="No Backorder" type="object" class="btn-primary" attrs="{'invisible': [('show_backorders', '=', False)]}"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

    <record id="act_assign_serial_numbers_production" model="ir.actions.act_window">
        <field name="name">Assign Serial Numbers</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">stock.assign.serial</field>
        <field name="view_id" ref="view_assign_serial_numbers_production"/>
        <field name="view_mode">form</field>
        <field name="context">{}</field>
        <field name="target">new</field>
    </record>

</odoo>
