# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2021
# שהאב <PERSON>יין <<EMAIL>>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# da<PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# MichaelHadar, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# NoaFarkash, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# yael terner, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-10 09:48+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: yael terner, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid " / Month"
msgstr "/ חודש אחד"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "הנתונים נאספו"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sale_order_count
msgid "# Sale Orders"
msgstr "# הזמנות לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "מס' שורות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr "מס' הזמנות לקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr "'פרו-פורמה - %s' % (object.name)"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'הצעת מחיר - %s' % (object.name)) or "
"'הזמנה - %s' % (object.name)"

#. module: sale
#: model:product.product,description_sale:sale.product_product_4e
#: model:product.product,description_sale:sale.product_product_4f
msgid "160x80cm, with large legs."
msgstr "160X80 ס\"מ, עם רגליים גדולות."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"<b>Send the quote</b> to yourself and check what the customer will receive."
msgstr "<b>שלח</b> לעצמך את הצעת המחיר ובדוק מה הלקוח יקבל."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "<b>Set a price</b>."
msgstr "<b>קבע.י מחיר</b>"

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br/><br/>\n"
"        <t t-set=\"transaction\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Your order <strong t-out=\"object.name or ''\">S00049</strong> amounting in <strong t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</strong>\n"
"        <t t-if=\"object.state == 'sale' or (transaction and transaction.state in ('done', 'authorized'))\">\n"
"            has been confirmed.<br/>\n"
"            Thank you for your trust!\n"
"        </t>\n"
"        <t t-elif=\"transaction and transaction.state == 'pending'\">\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            <t t-if=\"object.reference\">\n"
"                Your payment reference is <strong t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/><br/>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><strong>Products</strong></td>\n"
"                <td/>\n"
"                <td width=\"15%\" align=\"center\"><strong>Quantity</strong></td>\n"
"                <td width=\"20%\" align=\"right\"><strong>\n"
"                <t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\">\n"
"                    VAT Excl.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    VAT Incl.\n"
"                </t>\n"
"                </strong></td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and line.display_type in ['line_section', 'line_note']\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section'\">\n"
"                                <strong t-out=\"line.name or ''\">Taking care of Trees Course</strong>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><strong>\n"
"                        <t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </strong></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Delivery:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>SubTotal:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>SubTotal:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>Taxes:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Total:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <strong>Bill to:</strong>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Payment Method:</strong>\n"
"                    <t t-if=\"transaction.token_id\">\n"
"                        <t t-out=\"transaction.token_id.name or ''\"/>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"transaction.acquirer_id.sudo().name or ''\"/>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(transaction.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <strong>Ship to:</strong>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Shipping Method:</strong>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                    <t t-if=\"object.carrier_id.fixed_price == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.carrier_id.fixed_price, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"<p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 12px;\">\n"
"שלום,\n"
"<br><br>\n"
"<t t-set=\"transaction\" t-value=\"object.get_portal_last_transaction()\" data-oe-t-inline=\"true\"></t>\n"
"הזמנתך <strong t-out=\"object.name or ''\" style=\"box-sizing:border-box;font-weight:500;\">S00049</strong> על סך <strong t-out=\"format_amount(object.amount_total, object.currency_id) or ''\" style=\"box-sizing:border-box;font-weight:500;\">$ 10.00</strong>\n"
"<t t-if=\"object.state == 'sale' or (transaction and transaction.state in ('done', 'authorized'))\" data-oe-t-group-active=\"true\">\n"
"אושרה בהצלחה.<br>\n"
"תודה שבחרת בנו!\n"
"</t>\n"
"<t t-elif=\"transaction and transaction.state == 'pending'\">\n"
"ממתינה. היא תאושר בקבלת התשלום.\n"
"<t t-if=\"object.reference\" data-oe-t-group-active=\"true\">\n"
"מזהה התשלום שלך הינו <strong t-out=\"object.reference or ''\" style=\"box-sizing:border-box;font-weight:500;\"></strong>.\n"
"</t>\n"
"</t>\n"
"<br><br>\n"
"אל תהסס\\י ליצור עמנו קשר במידה ויש לך שאלות.\n"
"<br><br>\n"
"</p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\" data-oe-t-group-active=\"true\">\n"
"<div style=\"margin: 0px; padding: 0px;\">\n"
"<table width=\"100%\" style=\"box-sizing:border-box;color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"<tbody><tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"<td style=\"width: 150px;\"><strong style=\"box-sizing:border-box;font-weight:500;\">מוצרים</strong></td>\n"
"<td></td>\n"
"<td width=\"15%\" align=\"center\"><strong style=\"box-sizing:border-box;font-weight:500;\">כמות</strong></td>\n"
"<td width=\"20%\" align=\"right\"><strong style=\"box-sizing:border-box;font-weight:500;\">\n"
"<t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\" data-oe-t-group-active=\"true\">\n"
"לפני מע\"מ\n"
"</t>\n"
"<t t-else=\"\">\n"
"כולל מע\"מ\n"
"</t>\n"
"</strong></td>\n"
"</tr>\n"
"</tbody></table>\n"
"<t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"<t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and line.display_type in ['line_section', 'line_note']\" data-oe-t-group-active=\"true\">\n"
"<t t-set=\"loop_cycle_number\" t-value=\"0\" data-oe-t-inline=\"true\"></t><t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\" data-oe-t-inline=\"true\"></t><table width=\"100%\" style=\"box-sizing:border-box;color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"\n"
"<tbody><tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"\n"
"<td colspan=\"4\">\n"
"<t t-if=\"line.display_type == 'line_section'\" data-oe-t-group-active=\"true\">\n"
"<strong t-out=\"line.name or ''\" style=\"box-sizing:border-box;font-weight:500;\">Taking care of Trees Course</strong>\n"
"</t>\n"
"<t t-elif=\"line.display_type == 'line_note'\">\n"
"<i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"</t>\n"
"</td>\n"
"</tr>\n"
"</tbody></table>\n"
"</t>\n"
"<t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"<t t-set=\"loop_cycle_number\" t-value=\"0\" data-oe-t-inline=\"true\"></t><t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\" data-oe-t-inline=\"true\"></t><table width=\"100%\" style=\"box-sizing:border-box;color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"\n"
"<tbody><tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"\n"
"<td style=\"width: 150px;\">\n"
"<img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"border-style:none;box-sizing:border-box;vertical-align:middle;width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\" width=\"64px\" height=\"64\">\n"
"</td>\n"
"<td align=\"left\" t-out=\"line.product_id.name or ''\"> Taking care of Trees Course</td>\n"
"<td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"<td width=\"20%\" align=\"right\"><strong style=\"box-sizing:border-box;font-weight:500;\">\n"
"<t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\" data-oe-t-group-active=\"true\">\n"
"<t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\" data-oe-t-inline=\"true\">$ 10.00</t>\n"
"</t>\n"
"<t t-else=\"\">\n"
"<t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\" data-oe-t-inline=\"true\">$ 10.00</t>\n"
"</t>\n"
"</strong></td>\n"
"</tr>\n"
"</tbody></table>\n"
"</t>\n"
"</t>\n"
"</div>\n"
"<div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" data-oe-t-group-active=\"true\">\n"
"<table width=\"100%\" style=\"box-sizing:border-box;-webkit-border-vertical-spacing:4px;-webkit-border-horizontal-spacing:0px;border-collapse:collapse;color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"<tbody><tr>\n"
"<td style=\"width: 60%\"></td>\n"
"<td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong style=\"box-sizing:border-box;font-weight:500;\">משלוח:</strong></td>\n"
"<td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"</tr>\n"
"<tr>\n"
"<td style=\"width: 60%\"></td>\n"
"<td style=\"width: 30%;\" align=\"right\"><strong style=\"box-sizing:border-box;font-weight:500;\">סכום ביניים:</strong></td>\n"
"<td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"</tr>\n"
"</tbody></table>\n"
"</div>\n"
"<div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"<table width=\"100%\" style=\"box-sizing:border-box;-webkit-border-vertical-spacing:4px;-webkit-border-horizontal-spacing:0px;border-collapse:collapse;color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"<tbody><tr>\n"
"<td style=\"width: 60%\"></td>\n"
"<td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong style=\"box-sizing:border-box;font-weight:500;\">SubTotal:</strong></td>\n"
"<td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"</tr>\n"
"</tbody></table>\n"
"</div>\n"
"<div style=\"margin: 0px; padding: 0px;\">\n"
"<table width=\"100%\" style=\"box-sizing:border-box;-webkit-border-vertical-spacing:4px;-webkit-border-horizontal-spacing:0px;border-collapse:collapse;color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"<tbody><tr>\n"
"<td style=\"width: 60%\"></td>\n"
"<td style=\"width: 30%;\" align=\"right\"><strong style=\"box-sizing:border-box;font-weight:500;\">מיסים:</strong></td>\n"
"<td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"</tr>\n"
"<tr>\n"
"<td style=\"width: 60%\"></td>\n"
"<td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong style=\"box-sizing:border-box;font-weight:500;\">סה\"כ:</strong></td>\n"
"<td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"</tr>\n"
"</tbody></table>\n"
"</div>\n"
"<div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\" data-oe-t-group-active=\"true\">\n"
"<table width=\"100%\" style=\"box-sizing:border-box;border-collapse:collapse;color: #454748; font-size: 12px;\">\n"
"<tbody><tr>\n"
"<td style=\"padding-top: 10px;\">\n"
"<strong style=\"box-sizing:border-box;font-weight:500;\">כתובת לחיוב:</strong>\n"
"<t t-out=\"object.partner_invoice_id.street or ''\" data-oe-t-inline=\"true\">1201 S Figueroa St</t>\n"
"<t t-out=\"object.partner_invoice_id.city or ''\" data-oe-t-inline=\"true\">Los Angeles</t>\n"
"<t t-out=\"object.partner_invoice_id.state_id.name or ''\" data-oe-t-inline=\"true\">California</t>\n"
"<t t-out=\"object.partner_invoice_id.zip or ''\" data-oe-t-inline=\"true\">90015</t>\n"
"<t t-out=\"object.partner_invoice_id.country_id.name or ''\" data-oe-t-inline=\"true\">United States</t>\n"
"</td>\n"
"</tr>\n"
"<tr>\n"
"<td>\n"
"<strong style=\"box-sizing:border-box;font-weight:500;\">אמצעי תשלום:</strong>\n"
"<t t-if=\"transaction.token_id\" data-oe-t-group-active=\"true\">\n"
"<t t-out=\"transaction.token_id.name or ''\" data-oe-t-inline=\"true\"></t>\n"
"</t>\n"
"<t t-else=\"\">\n"
"<t t-out=\"transaction.acquirer_id.sudo().name or ''\" data-oe-t-inline=\"true\"></t>\n"
"</t>\n"
"(<t t-out=\"format_amount(transaction.amount, object.currency_id) or ''\" data-oe-t-inline=\"true\">$ 10.00</t>)\n"
"</td>\n"
"</tr>\n"
"</tbody></table>\n"
"</div>\n"
"<div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\" data-oe-t-group-active=\"true\">\n"
"<table width=\"100%\" style=\"box-sizing:border-box;border-collapse:collapse;color: #454748; font-size: 12px;\">\n"
"<tbody><tr>\n"
"<td>\n"
"<br>\n"
"<strong style=\"box-sizing:border-box;font-weight:500;\">כתובת למשלוח:</strong>\n"
"<t t-out=\"object.partner_shipping_id.street or ''\" data-oe-t-inline=\"true\">1201 S Figueroa St</t>\n"
"<t t-out=\"object.partner_shipping_id.city or ''\" data-oe-t-inline=\"true\">Los Angeles</t>\n"
"<t t-out=\"object.partner_shipping_id.state_id.name or ''\" data-oe-t-inline=\"true\">California</t>\n"
"<t t-out=\"object.partner_shipping_id.zip or ''\" data-oe-t-inline=\"true\">90015</t>\n"
"<t t-out=\"object.partner_shipping_id.country_id.name or ''\" data-oe-t-inline=\"true\">United States</t>\n"
"</td>\n"
"</tr>\n"
"</tbody></table>\n"
"<table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"box-sizing:border-box;border-collapse:collapse;color: #454748; font-size: 12px;\" data-oe-t-group-active=\"true\">\n"
"<tbody><tr>\n"
"<td>\n"
"<strong style=\"box-sizing:border-box;font-weight:500;\">סוג משלוח:</strong>\n"
"<t t-out=\"object.carrier_id.name or ''\" data-oe-t-inline=\"true\"></t>\n"
"<t t-if=\"object.carrier_id.fixed_price == 0.0\" data-oe-t-group-active=\"true\">\n"
"(Free)\n"
"</t>\n"
"<t t-else=\"\">\n"
"(<t t-out=\"format_amount(object.carrier_id.fixed_price, object.currency_id) or ''\" data-oe-t-inline=\"true\">$ 10.00</t>)\n"
"</t>\n"
"</td>\n"
"</tr>\n"
"</tbody></table>\n"
"</div>\n"
"</t>\n"
"</div>"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        Your\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            amounting in <strong t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 10.00</strong> is available.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\"/>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            amounting in <strong t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 10.00</strong> is ready for review.\n"
"        </t>\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        % set doc_name = 'quotation' if object.state in ('draft', 'sent') else 'order'\n"
"        שלום,\n"
"        <br/><br/>\n"
"        חשבונית\n"
"        % if ctx.get('proforma'):\n"
"            פרו פורמה שלך עבור ${doc_name} <strong>${object.name}</strong>\n"
"            % if object.origin:\n"
"                (עם מזהה: ${object.origin} )\n"
"            % endif\n"
"            בסכום שלך <strong>${format_amount(object.amount_total, object.pricelist_id.currency_id)}</strong> זמינה.\n"
"        % else:\n"
"            ${שם מסמך} <strong>${object.name}</strong>\n"
"            % if object.origin:\n"
"                (עם מזהה: ${object.origin} )\n"
"            % endif\n"
"            בסכום של <strong>${format_amount(object.amount_total, object.pricelist_id.currency_id)}</strong> מוכן לבדיקה.\n"
"        % endif\n"
"        <br/><br/>\n"
"        אל תהסס לפנות אלינו אם יש לך שאלות.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr "<i class=\"fa fa-comment\"/> צור קשר לקבלת הצעת מחיר חדשה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> משוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Send message"
msgstr "<i class=\"fa fa-comment\"/> שלח הודעה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> הורד"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Done\"/>Done"
msgstr "<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Done\"/>בוצע"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Paid</b>"
msgstr "<i class=\"fa fa-fw fa-check\"/> <b>שולם</b>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Waiting Payment</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>תשלום ממתין</b>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> לא בתוקף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-remove\"/> בוטל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> הדפס"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> דחה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">This offer expires on</b></small>"
msgstr "<small><b class=\"text-muted\">תוקף ההצעה הזה יפוג בתאריך</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr "<small><b class=\"text-muted\">היתרון שלך</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span attrs=\"{'invisible': [('advance_payment_method', '!=', "
"'percentage')]}\" class=\"oe_inline\">%</span>"
msgstr ""
"<span attrs=\"{'invisible': [('advance_payment_method', '!=', "
"'percentage')]}\" class=\"oe_inline\">%</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">מס' הזמנות לקוח</span>\n"
"                            <span class=\"d-block d-md-none\">מזהה</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Confirmation Email</span>"
msgstr "<span class=\"o_form_label\">דוא\"ל אישור</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Down Payments</span>"
msgstr "<span class=\"o_form_label\">מקדמה</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<span class=\"o_stat_text\">Customer</span>\n"
"                                <span class=\"o_stat_text\">Preview</span>"
msgstr ""
"<span class=\"o_stat_text\">לקוח</span>\n"
"                                <span class=\"o_stat_text\">תצוגה מקדימה</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">נמכר</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "<span class=\"oe_read_only\">/ Month</span>"
msgstr "<span class=\"oe_read_only\"> /חודש</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">סכום</span>\n"
"                                <span groups=\"account.group_show_line_subtotals_tax_included\">מחיר כולל</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                            <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">סכום מחיר</span>\n"
" <span groups=\"account.group_show_line_subtotals_tax_included\">כולל</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>Accepted on the behalf of:</span>"
msgstr "<span>התקבל בשם:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By paying this proposal, I agree to the following terms:</span>"
msgstr "<span>בתשלום הצעה זו אני מסכים לתנאים הבאים:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By signing this proposal, I agree to the following terms:</span>"
msgstr "<span>בחתימה על הצעה זו אני מסכים לתנאים הבאים:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr "<span>% הנחה</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>For an amount of:</span>"
msgstr "<span>לסכום של:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<span>Pro-Forma Invoice # </span>"
msgstr "<span>מספר חשבונית פרו-פורמה </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>מיסים</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>With payment terms:</span>"
msgstr "<span>עם תנאי תשלום:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">חשבוניות</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Shipping Address:</strong>"
msgstr "<strong class=\"d-block mb-1\">כתובת למשלוח:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">סכום ביניים</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong class=\"text-muted\">Salesperson</strong>"
msgstr "<strong class=\"text-muted\">איש מכירות</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Expiration Date:</strong>"
msgstr "<strong>תאריך תוקף:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration:</strong>"
msgstr "<strong>תוקף:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>הערת מעמד פיסקלי:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                        If you believe that it is an error, please contact the website administrator."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Order Date:</strong>"
msgstr "<strong>תאריך הזמנה:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Quotation Date:</strong>"
msgstr "<strong>תאריך הצעת מחיר:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>איש מכירות:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_document_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>כתובת לאספקה:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr "<strong>חתימה</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr "<strong>תודה לך!</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr "<strong>הצעה זו לא בתוקף!</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been canceled.</strong>"
msgstr "<strong>הצעה זו בוטלה.</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr "<strong>מזהה שלך:</strong>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr "הזמנת לקוח מאושרת דורשת תאריך אישור."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid ""
"A standard invoice is issued with all the order lines ready for invoicing,"
"         according to their invoicing policy (based on ordered or delivered "
"quantity)."
msgstr ""
"חשבונית סטנדרטית מונפקת עם כל שורות ההזמנה המוכנות לחשבונית, בהתאם למדיניות "
"החשבונית שלהם (בהתבסס על כמות שהוזמנה או נמסרה)."

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "ניתן להגדיר אזהרה עבור מוצר או לקוח (מכירה)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"היכולת לבחור סוג חבילה בהזמנות לקוח ולאלץ כמות שהיא כפולה של מספר היחידות "
"בחבילה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr "קבל ושלם"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr "קבל וחתום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr "אזהרת גישה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"על פי תצורת המוצר,  הכמות הנשלחת יכולה להיות מחושבת באופן אוטומטי על ידי מנגנון:\n"
"  - ידני: הכמות נקבעת ידנית בשורה\n"
"  - אנליטית מהוצאות: הכמות היא הסכום הכמותי מההוצאות שנרשמו\n"
"  - גליון זמנים: הכמות היא מספר השעות שנרשמו במשימות הקשורות לשורת מכירה זו\n"
"  - תנועות מלאי: הכמות מגיעה מליקוטים שאושרו\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "מספר חשבון"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Account used for deposits"
msgstr "חשבון המשמש להפקדות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_accrued_revenue_entry
msgid "Accrued Revenue Entry"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "סוגי פעילויות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr "הוסף הערה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr "הוסף מוצר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr "הוסף סעיף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr "הוסף מספר וריאנטים להזמנה מתצוגת רשת"

#. module: sale
#: model:res.groups,name:sale.group_delivery_invoice_address
msgid "Addresses in Sales Orders"
msgstr "כתובות בהזמנות לקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr "מאפשר לך לשלוח חשבונית פרו-פורמה ללקוחות שלך"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "מאפשר לך לשלוח חשבונית פרו-פורמה."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr "סנכרון לאמזון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "סכום לפני הנחה"

#. module: sale
#: code:addons/sale/models/payment_transaction.py:0
#, python-format
msgid "Amount Mismatch (%s)"
msgstr "סכום לא תואם (%s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr "סכום הצעות מחיר לחיוב"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"An order is to upsell when delivered quantities are above initially\n"
"                ordered quantities, and the invoicing policy is based on ordered quantities."
msgstr ""
"הזמנה היא מסוג Upsell כאשר הכמויות שנמסרו גבוהות מהכמויות שהוזמנו בהתחלה, "
"ומדיניות החשבוניות מבוססת על כמויות שהוזמנו."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_order__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_report__analytic_account_id
msgid "Analytic Account"
msgstr "חשבון אנליטי"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr "ניתוח מהוצאות "

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "שורה אנליטית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "תגיות אנליטיות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "שורות אנליטיות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"החל הנחות ידניות על שורות הזמנת לקוח או הצג הנחות המחושבות ממחירונים (אפשרות"
" להפעלה בתצורת מחירון)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr "האם אתה בטוח שברצונך לבטל את העסקה המורשית? לא ניתן לבטל פעולה זו."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"As an example, if you sell pre-paid hours of services, Odoo recommends you\n"
"                to sell extra hours when all ordered hours have been consumed."
msgstr ""
"כדוגמה, אם אתה מוכר שעות שירותים בתשלום מראש, אודו ממליצה לך למכור שעות "
"נוספות כאשר כל השעות שהוזמנו נצרכו."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "בעלות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_value
msgid "Attribute Value"
msgstr "ערך תכונה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "תכונות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "עסקאות מורשות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "חשבונית אוטומטית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Automatic email sent after the customer has signed or paid online"
msgstr "דוא\"ל אוטומטי שנשלח לאחר שהלקוח חתם או שילם באופן מקוון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "שם בנק"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_acquirer__so_reference_type__partner
msgid "Based on Customer ID"
msgstr "מבוסס על מזהה לקוח"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_acquirer__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr "מבוסס על מזהה מסמך"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "הודעת חסימה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""
"הגדל את המכירות שלך עם שני סוגים של תוכניות הנחה: מבצעים וקודי קופונים. ניתן"
" לקבוע תנאים מסוימים (מוצרים, לקוחות, סכום רכישה מינימלי, תקופה). הטבות "
"יכולות להיות הנחות (אחוז או סכום) או מוצרים בחינם."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr "קמפיין"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr "יכול לערוך מוצר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "בטל"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Cancel Sales Order"
msgstr "בטל הזמנת לקוח"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "בוטל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "לכוד עסקה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr "קטגוריה"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__use_quotations
msgid ""
"Check this box if you send quotations to your customers rather than "
"confirming orders straight away."
msgstr ""
"סמן תיבה זו אם אתה שולח הצעות מחיר ללקוחות שלך במקום לאשר הזמנות מייד."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Choose between electronic signatures or online payments."
msgstr "בחר בין חתימות אלקטרוניות או תשלומים מקוונים."

#. module: sale
#: model:ir.actions.act_window,name:sale.action_open_sale_onboarding_payment_acquirer_wizard
msgid "Choose how to confirm quotations"
msgstr "בחר כיצד לאשר הצעות מחיר"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Click here to add some products or services to your quotation."
msgstr "לחץ כאן כדי להוסיף מספר מוצרים או שירותים להצעה המחיר שלך."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Click to define an invoicing target"
msgstr "לחץ להגדרת יעד לחיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "סגור"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__closed
msgid "Closed"
msgstr "סגור"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_acquirer__so_reference_type
msgid "Communication"
msgstr "אסמכתה"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "חברה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "חשב עלויות המשלוח ושלח באמצעות  DHL"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "חשב עלויות המשלוח ושלח באמצעות Easypost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "חשב עלויות המשלוח ושלח באמצעות FedEx"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "חשב עלויות המשלוח ושלח באמצעות UPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "חשב עלויות המשלוח ושלח באמצעות USPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "חשב עלויות המשלוח ושלח באמצעות bpost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "חשב עלויות המשלוח בהזמנות"

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "תצורה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "אשר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__confirmation_mail_template_id
msgid "Confirmation Email Template"
msgstr "תבנית דוא\"ל אישור"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr "חיבורים"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Contact"
msgstr "צור קשר"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid "Contact Us"
msgstr "צור קשר"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"המרה בין יחידות מידה יכולה להתרחש רק אם הן שייכות לאותה קטגוריה. ההמרה תיעשה"
" על בסיס היחס בניהן."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_coupon
msgid "Coupons & Promotions"
msgstr "קופונים ומבצעים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Create Date"
msgstr "תאריך יצירה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Invoice"
msgstr "צור חשבונית"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create a customer invoice"
msgstr "צור חשבונית לקוח"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "צור מוצר חדש"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
#: model_terms:ir.actions.act_window,help:sale.action_sale_order_form_view
msgid "Create a new quotation, the first step of a new sale!"
msgstr "צור הצעת מחיר חדשה, השלב הראשון במכירה חדשה!"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create and View Invoice"
msgstr "צור וצפה בחשבונית"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoices"
msgstr "צור חשבוניות"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr "צור חשבוניות, רשום תשלומים ועקוב אחר השיחות עם הלקוחות שלך."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Creation Date"
msgstr "תאריך יצירה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""
"תאריך יצירה של הזמנות טיוטה / שנשלחו,\n"
"תאריך אישור להזמנות שאושרו."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "שער מטבע"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
#: model:product.template.attribute.value,name:sale.product_4_attribute_1_value_3
msgid "Custom"
msgstr "מותאם"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "ערכים מותאמים אישית"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "הוראות תשלום מותאמות אישית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "חשבון לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_sale_delivery_address
msgid "Customer Addresses"
msgstr "כתובות לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr "ארץ לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr "יישות לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr "תעשיית לקוח"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr "כתובת אתר של פורטל לקוחות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "מספר הזמנת לקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr "חתימת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Customer Taxes"
msgstr "מיסי לקוח"

#. module: sale
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "לקוחות"

#. module: sale
#: model:product.product,name:sale.product_product_4e
#: model:product.product,name:sale.product_product_4f
msgid "Customizable Desk"
msgstr "שולחן הניתן להתאמה אישית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize"
msgstr "התאמה אישית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize the look of your quotations."
msgstr "התאם אישית את מראה הצעות המחיר שלך."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Customize your quotes and orders."
msgstr "התאם אישית את הצעות המחיר וההזמנות שלך."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Expressחיבור ל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "תאריך"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__date_order
msgid "Date Order"
msgstr "תאריך הזמנה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signed_on
msgid "Date of the signature."
msgstr "תאריך החתימה."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__create_date
msgid "Date on which sales order is created."
msgstr "התאריך שבו נוצרה ההזמנה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "תאריך:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr "קיזוז המקדמות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Default Limit:"
msgstr "מגבלת ברירת מחדל:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__use_quotation_validity_days
msgid "Default Quotation Validity"
msgstr "תוקף ברירת מחדל להצעת מחיר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity (Days)"
msgstr "תוקף ברירת מחדל להצעת מחיר (ימים)"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__deposit_default_product_id
msgid "Default product used for payment advances"
msgstr "מוצר ברירת מחדל המשמש למקדמות תשלום"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr "שלח תוכן באמצעות דוא\"ל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr "נמסר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_manual
msgid "Delivered Manually"
msgstr "נשלח ידנית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
msgid "Delivered Quantity"
msgstr "כמות שנשלחה"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Delivered Quantity: %s"
msgstr "כמות שנשלחה %s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "כמויות שנשלחו"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_account_move__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_account_payment__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "כתובת לאספקה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr "תאריך משלוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "שיטות משלוח"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__partner_shipping_id
#: model:ir.model.fields,help:sale.field_account_move__partner_shipping_id
#: model:ir.model.fields,help:sale.field_account_payment__partner_shipping_id
msgid "Delivery address for current invoice."
msgstr "כתובת אספקה עבור חשבונית נוכחית."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"תאריך משלוח אותו אתה יכול להבטיח ללקוח, מחושב ממשך הזמן המינימלי של שורות "
"ההזמנה במקרה של מוצרי שירות. במקרה של משלוח, מדיניות המשלוח של ההזמנה תובא "
"בחשבון כדי להשתמש בזמן המשלוח המינימלי או המקסימלי של שורות ההזמנה."

#. module: sale
#: model:product.product,name:sale.advance_product_0
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "פקדון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__deposit_default_product_id
msgid "Deposit Product"
msgstr "מוצר פקדון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "תיאור"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr "הנחה %"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr " % הנחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "הנחה (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr "סכום הנחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__display_name
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr "סוג תצוגה"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale_order_view.js:0
#, python-format
msgid "Do you want to apply this discount to all order lines?"
msgstr "האם ברצונך להחיל הנחה זו על כל מרכיבי (שורות) ההזמנה?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Documentation"
msgstr "תיעוד"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__done
msgid "Done"
msgstr "בוצע"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment"
msgstr "מקדמה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment Amount"
msgstr "סכום המקדמה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount (Fixed)"
msgstr "סכום מקדמה (קבוע)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__product_id
msgid "Down Payment Product"
msgstr "מוצר מקדמה"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment: %s"
msgstr "מקדמה: %s"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Down Payments"
msgstr "מקדמות"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment"
msgstr "מקדמה"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "מקדמה (סכום קבוע)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "מקדמה (אחוזים)"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment of %s%%"
msgstr "מקדמה של %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""
"המקדמות נוצרות בעת יצירת חשבוניות מהזמנת לקוח. הן לא מועתקות בעת שכפול הזמנת"
" לקוח."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Download"
msgstr "הורד"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
msgid "Draft Quotation"
msgstr "טיוטת הצעת מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Draft invoices for this order will be cancelled."
msgstr "טיוטת חשבוניות עבור הזמנה זו תבוטל."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "חיבור ל Easypost"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#, python-format
msgid "Edit Configuration"
msgstr "ערוך תצורה"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__digital_signature
msgid "Electronic signature"
msgstr "חתימה אלקטרונית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr "דוא\"ל"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "אשף יצירת דוא\"ל"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__confirmation_mail_template_id
msgid "Email sent to the customer once the order is paid."
msgstr "דוא\"ל שנשלח ללקוח ברגע ששילם על ההזמנה."

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid "Ergonomic"
msgstr "ארגונומי"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "תאריך צפוי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr "צפוי:"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Expenses and vendor bills can be re-invoiced to a customer.With this option,"
" a validated expense can be re-invoice to a customer at its cost or sales "
"price."
msgstr ""
"הוצאות וחשבוניות ספק ניתנות לחיוב מחדש ללקוח. עם אפשרות, הוצאה מאומתת ניתנת "
"לחיוב מחדש כעלות או כמחיר מכירה."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr "תוקף הצעת מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "מסננים מורחבים"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#, python-format
msgid "External Link"
msgstr "קישור חיצוני"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "ערכים נוספים"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Extra line with %s"
msgstr "שורת נוספת עם %s "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "חיבור ל FedEx"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "מעמד פיסקלי"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr ""
"מעמד פיסקלי משמש להתאמת מיסים וחשבונות עבור לקוחות מסוימים או הזמנות לקוח / "
"חשבוניות. ערך ברירת המחדל מגיע מהלקוח."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr "ערכים אסורים בשורת הזמנת לקוח שאינה חייבת רישום."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "הרשמה חינם"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"מתוך דוח זה תוכל לקבל סקירה כללית של הסכום בו חויב הלקוח שלך. ניתן להשתמש "
"בכלי החיפוש גם להתאמה אישית של דוחות החשבוניות וכך להתאים ניתוח זה לצרכים "
"שלך."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
msgid "Fully Invoiced"
msgstr "חויבה במלואה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "צור קישור לתשלום מכירות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr "צור קישור לתשלום"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "הפק חשבונית אוטומטית לאחר אישור התשלום המקוון"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr "קבל אזהרות בהזמנות על מוצרים או לקוחות"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Good job, let's continue."
msgstr "כל הכבוד, בואו נמשיך."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "הענק הנחות בשורות הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "משקל ברוטו"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "קבץ לפי"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_pricelist
msgid "Has Pricelist Changed"
msgstr "האם המחירון השתנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr "יש מקדמות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "History"
msgstr "היסטוריה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__id
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
#: model:ir.model.fields,help:sale.field_sale_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""
"אם המכירה נעולה, אינך יכול לשנות אותה יותר. עם זאת, עדיין תוכל לבצע חיוב או "
"משלוח."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_packaging__sales
msgid "If true, the packaging can be used for sales orders"
msgstr "אם זה נכון, ניתן להשתמש באריזה להזמנות מכירה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr "אם תשנה את המחירון, רק השורות החדשות שנוספו יושפעו."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr "ייבא הזמנות של אמזון וסנכרן משלוחים"

#. module: sale
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid "Import Template for Products"
msgstr "ייבא תבנית למוצרים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr "כולל מע\"מ)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Income Account"
msgstr "חשבון הכנסות"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid order."
msgstr "הזמנה לא חוקית."

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr "נתוני חתימה לא חוקיים."

#. module: sale
#: code:addons/sale/models/account_move.py:0
#, python-format
msgid "Invoice %s paid"
msgstr "חשבונית %s שולמה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "כתובת לחשבונית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_invoice_alert
msgid "Invoice Alert"
msgstr "התראת חשבונית"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "חשבונית אושרה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr "כמות חשבוניות"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "חשבונית נוצרה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "תבנית דוא\"ל חשבונית"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "שורות חשבונית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "חשבונית הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
msgid "Invoice Status"
msgstr "סטטוס חשבונית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_sale_form_view
msgid "Invoice after delivery, based on quantities delivered, not ordered."
msgstr "חשבונית לאחר משלוח, לפי כמויות שנמסרו, לא אלו שהוזמנו."

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"הכנסות מחשבוניות לחודש הנוכחי. זה הסכום בו חייב ערוץ המכירות החודש. הוא משמש"
" לחישוב יחס ההתקדמות של הכנסות הנוכחיות לעומת יעד ההכנסות בתצוגת הקאנבן."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr "חייב מה שנשלח"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr "חייב מה שהוזמן"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "חויב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "כמות שחויבה"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Invoiced Quantity: %s"
msgstr "כמות שחויבה: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "חויב החודש"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "חשבוניות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "ניתוח נתוני חשבוניות"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "סטטיסטיקות חשבוניות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"Invoices will be created in draft so that you can review\n"
"                        them before validation."
msgstr ""
"חשבוניות ייווצרו בטיוטה כך שתוכלו לבדוק\n"
"אותן לפני האימות.                     "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "חיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address:"
msgstr "כתובת לחיוב:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "מדיניות חיוב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr "יעד חיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address:"
msgstr "כתובת לחיוב ולאספקה:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr "היא מקדמה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr "היא הוצאה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is expired"
msgstr "היא פגת תוקף"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr "נכון אם שורת הזמנת הלקוח מגיעה מהוצאה או מחשבונית ספק"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""
"חל איסור לשנות את השדות הבאים בהזמנה נעולה:\n"
"%s"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "It is not allowed to confirm an order in the following states: %s"
msgstr "אסור לאשר הזמנה במצבים הבאים: %s"

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr "פקודת יומן"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__just_done
msgid "Just done"
msgstr "הרגע בוצע"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales____last_update
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:sale.field_sale_report____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "זמן אספקה "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "אפשר ללקוחות שלך להתחבר כדי לראות את המסמכים שלהם"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Let's send the quote."
msgstr "בוא נשלח את הצעת המחיר."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Lets keep electronic signature for now."
msgstr "בוא נשמור לעת עתה את החתימה האלקטרונית."

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid "Locally handmade"
msgstr "ייצור מקומי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Lock"
msgstr "נעל"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr "נעל הזמנות מאושרות"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__done
msgid "Locked"
msgstr "נעולה"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"Looking for a custom bamboo stain to match existing furniture? Contact us "
"for a quote."
msgstr ""
"מחפשים כתם במבוק בהתאמה אישית לריהוט הקיים? צור איתנו קשר לקבלת הצעת מחיר."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Looks good. Let's continue."
msgstr "נראה טוב, בואו נמשיך."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Looks great!"
msgstr "נראה נהדר!"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "קובץ ראשי מצורף "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "נהל תוכניות קידום מכירות וקופונים"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "ידני"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr "תשלום ידני"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "קבע כמויות ידנית בהזמנה"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"הגדר ידנית כמויות בהזמנה: חיוב המבוסס על הכמות שהוזנה ידנית, ללא יצירת חשבון אנליטי.\n"
"דיווחי שעות בחוזה: חיוב המבוסס על שעות בדיווח השעות הקשור.\n"
"צור משימה ושעות למעקב: צור משימה באימות הזמנת לקוח ועקוב אחר שעות העבודה."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "שולי רווח"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr "סמן הצעת מחיר כנשלחה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr "שיווק"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr "בינוני"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "מזהה חשבון סוחר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "הודעה עבור הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "הודעה עבור שורת הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "אמצעי תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "שיטה לעדכון הכמות שנשלחה"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr "חסרים שדות חובה בשורת הזמנת מכירה."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "ההזמנות שלי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr "הצעות המחיר שלי"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "שורות הזמנת לקוח שלי"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signed_by
msgid "Name of the person that signed the SO."
msgstr "שם האדם שחתם על הזמנת הלקוח."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "New"
msgstr "חדש"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "הצעת מחיר חדשה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "לא"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "אין הודעה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr "לא ניתן לערוך הזמנות לאחר שאושרו"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr "לא נמצאו הזמנות לחיוב"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found."
msgstr "לא נמצאו הזמנות ל Upsell"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/variant_mixin.js:0
#, python-format
msgid "Not available with %s"
msgstr "לא זמין עם %s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__not_done
msgid "Not done"
msgstr "לא בוצע"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "הערה"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
msgid "Nothing to Invoice"
msgstr "אין מה לחייב"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Now, we'll create a sample quote."
msgstr "עכשיו, ניצור הצעת מחיר לדוגמא."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Number"
msgstr "מספר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr "מספר ימים בין אישור ההזמנה לבין שליחת המוצרים ללקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "מספר הודעות המחייבות פעולה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr "מספר הצעות המחיר לחיוב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr "מספר המכירות לחיוב"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_unread_counter
msgid "Number of unread messages"
msgstr "מספר ההודעות שלא נקראו"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "בהזמנה"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr ""
"לאחר אישור הצעת המחיר על ידי הלקוח, היא הופכת להזמנת לקוח.<br> תוכל ליצור "
"חשבונית ולגבות את התשלום."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_sale_order_form_view
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"לאחר שהצעת המחיר מאושרת, היא הופכת להזמנת לקוח.<br> תוכל ליצור חשבונית "
"ולגבות את התשלום."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online Payment"
msgstr "תשלום מקוון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online Signature"
msgstr "חתימה מקוונת"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Only draft orders can be marked as sent directly."
msgstr "ניתן לסמן רק הזמנות במצב טיוטה כנשלחו ישירות."

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid ""
"Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr "רק ערך מותאם אישית אחד מותר לכל ערך מאפיין בשורת הזמנת לקוח."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Open Sales app to send your first quotation in a few clicks."
msgstr ""
"פתח את יישום המכירות כדי לשלוח את הצעת המחיר הראשונה שלך במספר לחיצות."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__order_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "מס' הזמנה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Order Confirmation"
msgstr "אישור הזמנה "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr "כמות הזמנות"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#, python-format
msgid "Order Date"
msgstr "תאריך הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "שורות הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__name
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "מזהה הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "סטטוס הזמנה"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr "Upsell להזמנה"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Order signed by %s"
msgstr "הזמנה נחתמה ע\"י %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Order to Invoice"
msgstr "הזמנה לחיוב"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Ordered Quantity: %(old_qty)s -> %(new_qty)s"
msgstr "כמות שהוזמנה: %(old_qty)s -> %(new_qty)s"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""
"כמות שהוזמנה: חייב כמויות שהוזמנו על ידי הלקוח.\n"
"כמות שנשלחה: חייב כמויות שנשלחו ללקוח."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "כמויות שהוזמנו"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "הזמנות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Orders to Invoice"
msgstr "הזמנות לחיוב"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "הזמנות ל Upsell"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr "מידע נוסף"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr "ספק שירות תשלומים אחר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "אסימון מזהה PDT"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr "חשבונית פרו-פורמה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_id
msgid "Packaging"
msgstr "אריזה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "כמות אריזה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__partner_id
msgid "Partner"
msgstr "לקוח/ספק"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__country_id
msgid "Partner Country"
msgstr "ארץ לקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay Now"
msgstr "שלם עכשיו"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay with"
msgstr "שלם עם"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment acquirer"
msgstr "שלם באמצעות ספק תשלומים מקוון אחר"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr "PayPal "

#. module: sale
#: model:ir.model,name:sale.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "ספק שירות תשלומים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Acquirers"
msgstr "חיבור לתשלומים מקוונים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "הוראות תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "אמצעי תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr "מזהה תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "תנאי תשלום"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "עסקת תשלום"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr "תנאי תשלום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "סוג משתמש Paypal "

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Please define an accounting sales journal for the company %s (%s)."
msgstr "אנא הגדר יומן מכירות חשבונאי לחברה %s (%s)."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid "Please enter an integer value"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr "כתובת גישה לפורטל"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"Press a button and watch your desk glide effortlessly from sitting to "
"standing height in seconds."
msgstr "לחץ על כפתור וצפה במחשב שלך גולש ללא מאמץ מישיבה לגובה עמידה בשניות."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce
msgid "Price Reduce"
msgstr "הורדת מחיר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "הורדת מחיר מיסים לא כלולים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "הורדת מחיר מיסים כלולים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_subtotal
msgid "Price Subtotal"
msgstr "מחיר ביניים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "מחירון"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "מחירונים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Pricing"
msgstr "תמחור"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Print"
msgstr "הדפס"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr "חשבונית פרו-פורמה"

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "חשבוניות פרו-פורמה"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "מוצר"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute
msgid "Product Attribute"
msgstr "תכונת מוצר"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "ערך מותאם אישית של תכונת מוצר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr "קטלוג מוצרים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__categ_id
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "קטגורית מוצר"

#. module: sale
#: model:ir.model,name:sale.model_product_packaging
msgid "Product Packaging"
msgstr "אריזת מוצר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_qty
msgid "Product Quantity"
msgstr "כמות מוצרים"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_tmpl_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
msgid "Product Template"
msgstr "תבנית מוצר "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_readonly
msgid "Product Uom Readonly"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
msgid "Product Variant"
msgstr "וריאנט מוצר"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "וריאנטים של מוצר"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Product prices have been recomputed according to pricelist <b>%s<b> "
msgstr "מחירי המוצרים חושבו מחדש בהתאם למחירון <b>%s<b> "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product used for down payments"
msgstr "מוצר המשמש למקדמות"

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "מוצרים"

#. module: sale
#: model:ir.model,name:sale.model_report_sale_report_saleproforma
msgid "Proforma Report"
msgstr "דוח פרו-פורמה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "כמות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "כמות שנשלחה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "כמות שחויבה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr "כמות שהוזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "כמות למשלוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "כמות לחיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "כמויות לחיוב מהזמנות לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Quantity"
msgstr "כמות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_qty_configurator
#: model:ir.model.fields,field_description:sale.field_product_template__visible_qty_configurator
msgid "Quantity visible in configurator"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr "כמות:"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
#, python-format
msgid "Quotation"
msgstr "הצעת מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "הצעת מחיר מס'"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "Quotation / Order"
msgstr "הצעת מחיר/הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr "כמות הצעות מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr "תאריך הצעת מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Quotation Layout"
msgstr "עיצוב הצעת מחיר"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "הצעת המחיר נשלחה"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid "Quotation Validity is required and must be greater than 0."
msgstr "נדרש תוקף הצעת מחיר ועליו להיות גדול מ- 0."

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "ההצעה אושרה"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "הצעת מחיר נשלחה"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Quotation viewed by customer %s"
msgstr "הצעת המחיר נצפתה על ידי הלקוח %s"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.model.fields,field_description:sale.field_crm_team__use_quotations
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Quotations"
msgstr "הצעות מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations &amp; Orders"
msgstr "הצעות מחיר והזמנות"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "ניתוח נתוני הצעות מחיר"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "הצעות מחיר ומכירות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Expenses"
msgstr "חיוב הוצאות מחדש"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr "מדיניות חיוב מחדש גלויה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all prices based on this pricelist"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Reference"
msgstr "מזהה"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request."
msgstr "מזהה למסמך שייצר בקשה להזמנת לקוח זו."

#. module: sale
#: model:ir.model,name:sale.model_account_payment_register
msgid "Register Payment"
msgstr "רשום תשלום"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr "חשבונית רגילה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr "דחה הצעת מחיר זו"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
msgid "Reporting"
msgstr "דו\"חות"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr "בקש חתימה מקוונת מהלקוח על מנת לאשר הזמנות אוטומטית."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online payment to confirm orders"
msgstr "בקש תשלום מקוון כדי לאשר הזמנות"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr "בקש תשלום מקוון מלקוח על מנת לאשר הזמנות באופן אוטומטי."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online signature to confirm orders"
msgstr "בקש חתימה מקוונת כדי לאשר הזמנות"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Requested date is too soon."
msgstr "תאריך הדרישה קרוב מדי."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid ""
"Revenue target for the current month (untaxed total of confirmed invoices)."
msgstr "יעד הכנסות לחודש הנוכחי (סה\"כ ללא מע\"מ מחשבוניות שאושרו)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr "הכנסות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr "הכנסות שנוצרו מהקמפיין"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Review, Accept &amp; Pay Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Review, Accept &amp; Sign Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Review, Sign &amp; Pay Quotation"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__order_id
msgid "Sale Order"
msgstr "הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr "כמות הזמנות לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr "אזהרות בהזמנת לקוח"

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_acquirer_onboarding_wizard
msgid "Sale Payment acquire onboarding wizard"
msgstr "אשף תהליך תשלום"

#. module: sale
#: model:utm.source,name:sale.utm_source_sale_order_0
msgid "Sale Promotion 1"
msgstr "קידום מכירות 1"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr "אזהרות מכירה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr "אנא בחר אמצעי תשלום."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_packaging__sales
#: model:ir.ui.menu,name:sale.menu_report_product_all
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "מכירות"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "חשבונית מכירה מראש"

#. module: sale
#: code:addons/sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#, python-format
msgid "Sales Analysis"
msgstr "ניתוח נתוני מכירות"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "דוח נתוני מכירות"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__done
msgid "Sales Done"
msgstr "מכירות שבוצעו"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: model:ir.actions.act_window,name:sale.action_sale_order_form_view
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Sales Order"
msgstr "הזמנת לקוח"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "הזמנת לקוח בוטלה"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "הזמנת לקוח אושרה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr "פריט הזמנת לקוח"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "שורת הזמנת לקוח"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "שורות הזמנת לקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "שורות הזמנת לקוח מוכנות לחיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "שורות הזמנת לקוח הקשורות להזמנת לקוח שלי"

#. module: sale
#: code:addons/sale/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
#, python-format
msgid "Sales Order(s)"
msgstr "הזמנה(ות) לקוח"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_confirmation
msgid "Sales Order: Confirmation Email"
msgstr "הזמנת לקוח: דוא\"ל אישור"

#. module: sale
#: model:mail.template,name:sale.email_template_edi_sale
msgid "Sales Order: Send by email"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "הזמנות לקוח"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_account_payment__team_id
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "צוות מכירות"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "צוותי מכירות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr "אזהרות מכירה"

#. module: sale
#: model:ir.model,name:sale.model_report_all_channels_sales
msgid "Sales by Channel (All in One)"
msgstr "מכירות עפ\"י ערוץ (הכל באחד)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr "מחיר מכירה"

#. module: sale
#: code:addons/sale/models/crm_team.py:0
#, python-format
msgid "Sales: Untaxed Total"
msgstr "מכירות: סה\"כ ללא מע\"מ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "איש מכירות"

#. module: sale
#: code:addons/sale/models/res_company.py:0
#, python-format
msgid "Sample Order Line"
msgstr "שורת הזמנה לדוגמה"

#. module: sale
#: code:addons/sale/models/res_company.py:0
#, python-format
msgid "Sample Product"
msgstr "מוצר לדוגמה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Sample Quotation"
msgstr "הצעת מחיר לדוגמה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "חפש הזמנת לקוח"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "סעיף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "שם פסקה (למשל מוצרים, שירותים)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "אסימון אבטחה"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Select a product, or create a new one on the fly."
msgstr "בחר מוצר או צור אחד חדש תוך כדי תנועה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Select product attributes and optional products from the sales order"
msgstr "בחר תכונות מוצר ומוצרים אופציונליים מהזמנת הלקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Select specific invoice and delivery addresses"
msgstr "בחר כתובות חיוב ואספקה מסוימות"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"בחירה באפשרות \"אזהרה\" תודיע למשתמש על ידי הודעה,בחירת \"הודעת חסימה\" "
"תתריע על חריגה בהודעה ותחסום את ההתקדמות בתהליך. ההודעה צריכה להיכתב בשדה "
"הבא."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "מכור ורכוש מוצרים ביחידות מידה שונות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr "מכור מוצרים לפי מס' יחידות בחבילה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr "מכור וריאנטים של מוצר באמצעות תכונות (גודל, צבע וכו')"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr "שלח חשבונית פרו-פורמה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr "שלח דוא\"ל מסוים למוצר לאחר אישור החשבונית"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send a quotation to test the customer portal."
msgstr "שלח הצעת מחיר לבדיקת פורטל הלקוחות."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "שלח בדוא\"ל"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send sample"
msgstr "שלח דוגמה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Sales tab)."
msgstr ""
"שליחת דוא\"ל מועילה אם אתה צריך לשתף מידע או תוכן מסוים אודות מוצר (הוראות, "
"כללים, קישורים, מדיה וכו'). צור והגדר את תבנית הדוא\"ל מטופס פרטי המוצר "
"(בכרטיסיית מכירות)."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "רצף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set a default validity on your quotations"
msgstr "הגדר תוקף ברירת מחדל להצעות המחיר שלך"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid "Set an invoicing target: "
msgstr "הגדר יעד לחיוב:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "קבע מחירים מרובים למוצר, הנחות אוטומטיות וכו '."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Set payments"
msgstr "הגדר אמצעי תשלום"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "הגדר להצעת מחיר"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "הגדרות"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "שתף"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Shipping"
msgstr "משלוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr "הצג שולי רווח בהזמנות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr "חתום ושלם"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr "חתום באופן מקוון"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "חתימה"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr "חתימה חסרה."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signature
msgid "Signature received through the portal."
msgstr "חתימה התקבלה דרך הפורטל."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr "נחתם ע\"י"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr "נחתם ב-"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr "נמכר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr "נמכר ב -365 הימים האחרונים"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__source_id
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr "מקור"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "מסמך מקור"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr "דוא\"ל מסוים"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Stage"
msgstr "שלב"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Start by checking your company's data."
msgstr "התחל בבדיקת הנתונים של החברה שלך."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_order_confirmation_state
msgid "State of the onboarding confirmation order step"
msgstr "מצב שלב אישור רישום הזמנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_sample_quotation_state
msgid "State of the onboarding sample quotation step"
msgstr "מצב שלב אישור רישום הצעת מחיר"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_quotation_onboarding_state
msgid "State of the sale onboarding panel"
msgstr "מצב תהליך ההטמעה של המכירה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "סטטוס"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "מפתח Stripe שניתן לפרסם"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "מפתח סודי של Stripe "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "סיכום ביניים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Total"
msgstr "סך סכומי הביניים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "סכום כולל ללא מע\"מ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tag_ids
#: model:ir.ui.menu,name:sale.menu_tag_config
msgid "Tags"
msgstr "תגיות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_country_id
msgid "Tax Country"
msgstr "מס לפי ארץ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Tax Total"
msgstr "סך המס"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_totals_json
msgid "Tax Totals Json"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "מיסים"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Taxes used for deposits"
msgstr "מיסים המשמשים להפקדות"

#. module: sale
#: code:addons/sale/models/crm_team.py:0
#, python-format
msgid ""
"Team %(team_name)s has %(sale_order_count)s active sale orders. Consider "
"canceling them or archiving the team instead."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__show_update_pricelist
msgid ""
"Technical Field, True if the pricelist was changed;\n"
" this will then display a recomputation button"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "שדה טכני למטרת חווית משתמש."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__tax_country_id
msgid ""
"Technical field to filter the available taxes depending on the fiscal "
"country and fiscal position."
msgstr "שדה טכני לסינון המסים הקיימים לפי מדינה פיסקלית ומצב פיסקלי."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr ""
"ספר לנו מדוע אתה מסרב להצעת מחיר זו, הדבר יסייע לנו בשיפור השירותים שלנו."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__terms_type
msgid "Terms & Conditions format"
msgstr "פורמט תנאים והגדרות"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Terms & Conditions: %s"
msgstr "תנאים והגבלות: %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "תנאים והגבלות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions:"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "תנאים והגבלות"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr "תנאים והגבלות..."

#. module: sale
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is cancelled. You "
"cannot register an expense on a cancelled Sales Order."
msgstr ""
"הזמנת הלקוח %s קשורה לחשבון אנליטי %s מבוטלת. אינך יכול לרשום הוצאה על הזמנת"
" לקוח שבוטלה."

#. module: sale
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is currently locked. "
"You cannot register an expense on a locked Sales Order. Please create a new "
"SO linked to this Analytic Account."
msgstr ""
"הזמנת הלקוח %s קשורה לחשבון אנליטי %s נעולה כרגע. אינך יכול לרשום הוצאה על "
"הזמנת לקוח נעולה. אנא צור הזמנת לקוח חדשה המקושרת לחשבון אנליטי זה."

#. module: sale
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s must be validated "
"before registering expenses."
msgstr ""
"הזמנת הלקוח %s קשורה לחשבון אנליטי %s חייבת להיות מאומתת לפני רישום הוצאות."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__analytic_account_id
msgid "The analytic account related to a sales order."
msgstr "החשבון האנליטי שקשור להזמנת הלקוח."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"The delivery date is sooner than the expected date.You may be unable to "
"honor the delivery date."
msgstr ""
"תאריך המשלוח מוקדם יותר מהתאריך הצפוי. יתכן שלא תוכל לעמוד במועד המשלוח."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance, taxes excluded."
msgstr "הסכום הקבוע שיש לחייב מראש, מיסים לא נכללים."

#. module: sale
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"The following products cannot be restricted to the company %s because they have already been used in quotations or sales orders in another company:\n"
"%s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""
"לא ניתן להגביל את המוצרים הבאים לחברה %s מכיוון שכבר נעשה בהם שימוש בהצעות מחיר או הזמנות לקוח בחברה אחרת:\n"
"%s\n"
"באפשרותך להעביר אותם לארכיון וליצור אותם מחדש עם הגבלת החברה שלך או להשאיר אותם כמוצרים משותפים."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment acquirer.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment acquirer.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""
"החשבונית מופקת באופן אוטומטי וזמינה בפורטל הלקוחות כאשר העסקה מאושרת על ידי ספק שירות התשלומים.\n"
"החשבונית מסומנת כשולמה והתשלום נרשם ביומן התשלומים שהוגדר בתצורה של ספק שירות התשלומים.\n"
"מומלץ להשתמש במצב זה אם הנפקת החשבונית הסופית בעת ההזמנה ולא לאחר המסירה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr "שולי הרווח מחושבים כסכום מחיר המכירה של המוצר פחות עלות המוצר."

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"The minimum height is 65 cm, and for standing work the maximum height "
"position is 125 cm."
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The order is not in a state requiring customer signature."
msgstr "ההזמנה אינה במצב המחייב חתימה של לקוח."

#. module: sale
#: code:addons/sale/models/payment_transaction.py:0
#, python-format
msgid ""
"The order was not confirmed despite response from the acquirer (%s): order "
"total is %r but acquirer replied with %r."
msgstr ""
"ההזמנה לא אושרה למרות תגובת ספק שירות התשלומים(%s): סך ההזמנה %r אך ספק "
"שירות התשלומים הגיב עם %r."

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "The ordered quantity has been updated."
msgstr "הכמות שהוזמנה עודכנה."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr "תקשורת התשלומים של הזמנת לקוח זו."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance, taxes excluded."
msgstr "אחוז הסכום שיש לחייב מראש, לא כולל מיסים."

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr ""
"מוצר המשמש לחיוב מקדמה צריך להיות מסוג 'שירות'. אנא השתמש במוצר אחר או עדכן "
"מוצר זה."

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policy set"
" to \"Ordered quantities\". Please update your deposit product to be able to"
" create a deposit invoice."
msgstr ""
"על המוצר המשמש לחשבונית למקדמה צריכה להיות מוגדרת מדיניות חשבונית \"כמויות "
"שהוזמנו\". אנא עדכן את מוצר הפקדון שלך כדי שתוכל ליצור חשבונית פקדון."

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The provided parameters are invalid."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate 1 applicable at the date of"
" the order"
msgstr "שער המטבע למטבע של שער 1 החל ביום ההזמנה"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr "הערך של סכום המקדמה חייב להיות חיובי."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no orders for your account."
msgstr "כרגע אין הזמנות בחשבונך."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr "אין כרגע הצעות מחיר בחשבונך."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"There is nothing to invoice!\n"
"\n"
"Reason(s) of this behavior could be:\n"
"- You should deliver your products before invoicing them.\n"
"- You should modify the invoicing policy of your product: Open the product, go to the \"Sales\" tab and modify invoicing policy from \"delivered quantities\" to \"ordered quantities\". For Services, you should modify the Service Invoicing Policy to 'Prepaid'."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"ערך ברירת מחדל זה חל על כל מוצר חדש שנוצר. ניתן לשנות זאת בטופס פרטי המוצר."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__campaign_id
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"זהו שם שעוזר לך לעקוב אחר מאמצי הקמפיין השונים שלך, למשל Fall_Drive, "
"Christmas_Special"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""
"זהו מועד האספקה ​​שהובטח ללקוח. אם הוא מוגדר, הזמנת המשלוח תתוזמן על פי "
"תאריך זה ולא על פי זמני אספקת המוצר."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__medium_id
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "זוהי שיטת המשלוח, למשל גלויה, דוא\"ל או מודעת באנר"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__source_id
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "זהו מקור הקישור, למשל מנוע חיפוש, דומיין אחר או שם של רשימת דוא\"ל"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should sell "
"%(quantity).2f %(unit)s."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"דוח זה מבצע ניתוח של הזמנות הלקוח והצעות המחיר שלך. ניתוח זה בודק את הכנסות "
"המכירות שלך וממיין אותן לפי קריטריונים קבוצתיים שונים (איש מכירות, שותף, "
"מוצר וכו'). השתמש בדוח זה לניתוח מכירות שטרם חויבו. אם אתה רוצה לנתח את "
"המחזור שלך, עליך להשתמש בדוח ניתוח חשבוניות ביישום הנהלת חשבונות."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"דוח זה מבצע ניתוח של הצעות המחיר שלך. ניתוח זה בודק את הכנסות המכירות שלך "
"וממיין אותן לפי קריטריונים קבוצתיים שונים (איש מכירות, שותף, מוצר וכו'). "
"השתמש בדוח זה לניתוח מכירות שטרם חויבו. אם אתה רוצה לנתח את המחזור שלך, עליך"
" להשתמש בדוח ניתוח חשבוניות ביישום הנהלת חשבונות."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"דוח זה מבצע ניתוח של הזמנות הלקוח שלך. ניתוח זה בודק את הכנסות המכירות שלך "
"וממיין אותן לפי קריטריונים קבוצתיים שונים (איש מכירות, שותף, מוצר וכו'). "
"השתמש בדוח זה לניתוח מכירות שטרם חויבו. אם אתה רוצה לנתח את המחזור שלך, עליך"
" להשתמש בדוח ניתוח חשבוניות ביישום הנהלת חשבונות."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "This will update all unit prices based on the currently set pricelist."
msgstr "פעולה זו תעדכן את כל מחירי היחידות בהתבסס על המחירון שנקבע כעת."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "לחיוב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "כמות לחיוב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr "שדרוג מכירה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"לשליחת הזמנות במצב B2B, פתח איש קשר או בחר מספר כאלה בתצוגת רשימה ולחץ על "
"האפשרות 'ניהול גישה לפורטל' בתפריט הנפתח * פעולה*."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"To speed up order confirmation, we can activate electronic signatures or "
"payments."
msgstr "כדי להאיץ את אישור ההזמנה, נוכל להפעיל חתימות או תשלומים אלקטרוניים."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_total
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "סה\"כ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr "סה\"כ מיסים"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Excluded"
msgstr "סה\"כ מע\"מ לא כלול"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Included"
msgstr "סה\"כ כולל מע\"מ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "שירות מעקב"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tracking"
msgstr "מעקב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "עסקאות"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr "סוג שם"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "חיבור ל UPS"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "חיבור ל USPS"

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr "קמפיין UTM"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "מחיר יחידה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr "מחיר יחידה:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: sale
#: model:product.product,uom_name:sale.advance_product_0
#: model:product.product,uom_name:sale.product_product_4e
#: model:product.product,uom_name:sale.product_product_4f
#: model:product.template,uom_name:sale.advance_product_0_product_template
msgid "Units"
msgstr "יחידה"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "יחידות מידה"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "קטגוריות יחידות מידה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Unlock"
msgstr "שחרר נעילה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread
msgid "Unread Messages"
msgstr "הודעות שלא נקראו"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "מספר ההודעות שלא נקראו"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "סכום ללא מע\"מ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "סכום ללא מע\"מ שחויב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "סכום ללא מע\"מ לחיוב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr "סכום ללא מע\"מ שחויב"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "סכום כולל ללא מע\"מ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr "יחידת מידה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Prices"
msgstr "עדכון מחירים"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Upsell %(order)s for customer %(customer)s"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "הזדמנות ל - Upsell"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "בתוקף עד"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr "אשר הזמנה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr "וריאנט מתצוגת רשת"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "View Quotation"
msgstr "צפה בהצעת מחיר"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr "עסקה מבוטלת"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "נפח"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
#, python-format
msgid "Warning"
msgstr "אזהרה"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Warning for %s"
msgstr "אזהרה עבור %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "אזהרה בהזמנת הלקוח"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Warning when Selling this Product"
msgstr "אזהרה בעת מכירת מוצר זה"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"We pay special attention to detail, which is why our desks are of a superior"
" quality."
msgstr ""
"אנו מקדישים תשומת לב מיוחדת לפרטים, וזו הסיבה ששולחנות הכתיבה שלנו הם באיכות"
" מעולה."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Write a company name to create one, or see suggestions."
msgstr "כתוב שם חברה כדי ליצור אחד, או ראה המלצות."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_sale_form_view
msgid "You can invoice them before they are delivered."
msgstr "ניתן להוציא עליהם חשבונית לפני השילוח."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr ""
"אינך יכול למחוק הצעת מחיר שנשלחה או הזמנת לקוח מאושרת. תחילה עליך לבטל אותה."

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"You can not remove an order line once the sales order is confirmed.\n"
"You should rather set the quantity to 0."
msgstr ""
"אינך יכול להסיר שורת הזמנה לאחר אישור הזמנת הלקוח.\n"
"עליך להגדיר את הכמות ל- 0."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"                or check every order and invoice them one by one."
msgstr ""
"אתה יכול לבחור את כל ההזמנות ולחייב אותן בקבוצה,<br>\n"
"                או לבדוק כל הזמנה ולחייב בנפרד."

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_acquirer__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr ""
"באפשרותך לקבוע כאן את סוג התקשורת שיופיע בהזמנות לקוח. התקשורת תינתן ללקוח "
"כאשר יבחר את אמצעי התשלום."

#. module: sale
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"You cannot change the product's type because it is already used in sales "
"orders."
msgstr ""
"לא ניתן לשנות את סוג המוצר מכיוון שהוא כבר בשימוש בהזמנות מכירה קיימות."

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"You cannot change the type of a sale order line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"אינך יכול לשנות את סוג שורת הזמנת הלקוח. במקום זאת עליך למחוק את השורה "
"הנוכחית וליצור שורה חדשה מהסוג המתאים."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                    whether it's a storable product, a consumable or a service."
msgstr ""
"עליך להגדיר מוצר לכל מה שאתה מוכר או רוכש,\n"
"                    בין אם מדובר במוצר מנוהל מלאי, במוצר לא מנוהל מלאי או בשירות."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr "משוב שלך..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr "ההזמנה שלך אושרה"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr "ההזמנה שלך נחתמה אך עדיין יש לשלם כדי לאשר אותה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr "ההזמנה שלך נחתמה."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr "ההזמנה שלך אינה במצב שידחה."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"הצעת המחיר מכילה מוצרים של חברת  %(product_company)s ואילו הצעת המחיר שייכת לחברה %(quote_company)s. \n"
"אנא שנה את החברה בהצעת המחיר או הסר את המוצרים מחברות אחרות  (%(bad_products)s)."

#. module: sale
#: model:ir.actions.server,name:sale.send_invoice_cron_ir_actions_server
#: model:ir.cron,cron_name:sale.send_invoice_cron
#: model:ir.cron,name:sale.send_invoice_cron
msgid "automatic invoicing: send ready invoice"
msgstr "חשבונית אוטומטית: שלח חשבונית מוכנה"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "חיבור ל bpost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "סגור"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "ימים"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "sale order"
msgstr "הזמנת לקוח"

#. module: sale
#: model:mail.template,report_name:sale.email_template_edi_sale
#: model:mail.template,report_name:sale.mail_template_sale_confirmation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
msgid ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"
msgstr ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'הזמנה ממתינה' or 'הזמנה' }} (מספר {{ object.name or 'לא "
"קיים' } })"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'פרו-פורמה' or 'הצעת מחיר') or 'הזמנה' }} ( מספר {{"
" object.name or 'לא קיים' }})"
