# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_survey
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: hr_recruitment_survey
#: model:survey.survey,description:hr_recruitment_survey.survey_recruitment_form
msgid ""
"<p>\n"
"    Please answer those questions to help recruitment officers to preprocess your application.\n"
"</p>"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1
msgid ""
"<p>Please fill information about you: who you are, what are your education, experience, and activities.\n"
"    It will help us managing your application.</p>"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid ""
"<p>Please summarize your education history: schools, location, diplomas, "
"...</p>"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid ""
"<p>Please tell us a bit more about yourself: what are your main activities, "
"...</p>"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid ""
"<p>What are your main knowledge regarding the job you are applying to ?</p>"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid ""
"<span class=\"o_stat_text\">Consult</span>\n"
"                        <span class=\"o_stat_text\">Interview</span>"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1
msgid "About you"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid "Activities"
msgstr "Aktivnosti"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_invite__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_user_input__applicant_id
msgid "Applicant"
msgstr "Kandidat"

#. module: hr_recruitment_survey
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_applicant__survey_id
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_job__survey_id
msgid ""
"Choose an interview form for this job position and you will be able to "
"print/answer this interview from all applicants who apply for this job"
msgstr ""
"Izbira obrazca intervjuja za to delovno mesto omogoči tiskanje/odgovarjanje "
"tega obrazca za vse kandidate, ki se prijavljajo na to delovno mesto"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Create Interview Form"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_job_survey_inherit
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Display Interview Form"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q4
msgid "Education"
msgstr "Izobraževanje"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q2
msgid "From which university did or will you graduate ?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row2
msgid "Getting on with colleagues"
msgstr "Razumevanje s kolegi"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row8
msgid "Getting perks such as free parking, gym passes"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row1
msgid "Having a good pay"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row3
msgid "Having a nice office environment"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row7
msgid "Having freebies such as tea, coffee and stationery"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "If other, please specify:"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col2
msgid "Important"
msgstr "Pomembno"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_job__survey_id
msgid "Interview Form"
msgstr "Obrazec intervjuja"

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
#, python-format
msgid "Interview Form : %s"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.res_config_settings_view_form
msgid "Interview Survey"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_job
msgid "Job Position"
msgstr "Delovno mesto"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "Knowledge"
msgstr "Znanje"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row6
msgid "Management quality"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col1
msgid "Not important"
msgstr "Nepomembno"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row5
msgid "Office location"
msgstr "Lokacija pisarne"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid "Past work experiences"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Preview Interview"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.survey,title:hr_recruitment_survey.survey_recruitment_form
msgid "Recruitment Form"
msgstr "Obrazec zaposlovanja"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__response_id
msgid "Response"
msgstr "Odziv"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "SEND INTERVIEW"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "See interview report"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__response_state
msgid "Status"
msgstr "Status"

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__survey_id
#, python-format
msgid "Survey"
msgstr "Vprašalnik"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Anketni uporabniški vnos"

#. module: hr_recruitment_survey
#: model_terms:survey.survey,description_done:hr_recruitment_survey.survey_recruitment_form
msgid "Thank you for answering this survey. We will come back to you soon."
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "The answer you entered is not valid."
msgstr ""

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/survey_invite.py:0
#, python-format
msgid "The applicant \"%s\" has finished the survey."
msgstr ""

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/survey_invite.py:0
#, python-format
msgid "The survey %(survey_link)s has been sent to %(partner_link)s"
msgstr ""

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/survey_invite.py:0
#, python-format
msgid "The survey has been sent to \"%s\"."
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "This question requires an answer."
msgstr "To vprašanje zahteva odgovor."

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col3
msgid "Very important"
msgstr "Zelo pomembno"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q3
msgid "Were you referred by an employee?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "What is important for you ?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q1
msgid "Which country are you from ?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row4
msgid "Working with state of the art technology"
msgstr ""

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/hr_applicant.py:0
#, python-format
msgid "You must define a Contact Name for this applicant."
msgstr ""
