# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* repair
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <armaged<PERSON><EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# R<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# JanaAvalah, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: JanaAvalah, 2023\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: repair
#: model:ir.actions.report,print_report_name:repair.action_report_repair_order
msgid ""
"(\n"
"                object.state == 'draft' and 'Repair Quotation - %s' % (object.name) or\n"
"                'Repair Order - %s' % (object.name))"
msgstr ""
"(\n"
"                object.state == 'Mustand' ja 'Paranduspakkumine - %s' % (object.name) or\n"
"                'Parandustellimus - %s' % (object.name))"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr "(<i>Eemalda</i>)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "(update)"
msgstr "(uuenda)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'Draft' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Ready to Repair' status is used to start to repairing, user can start repairing only after repair order is confirmed.\n"
"* The 'Under Repair' status is used when the repair is ongoing.\n"
"* The 'To be Invoiced' status is used to generate the invoice before or after repairing done.\n"
"* The 'Done' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""
"* Olekut \"Mustand\" kasutatakse siis, kui kasutaja kodeerib uut ja kinnitamata parandustellimust.\n"
"* Olekut \"Kinnitatud\" kasutatakse siis, kui kasutaja kinnitab parandustellimuse.\n"
"* Paranduse alustamiseks kasutatakse olekut „Paranduseks valmis”, kasutaja saab parandamist alustada alles pärast parandustellimuse kinnitamist.\n"
"* Kui parandamine on pooleli, kasutatakse olekut „Parandamises”.\n"
"* Olekut \"Arveldada\" kasutatakse arve koostamiseks enne või pärast paranduse tegemist.\n"
"* Olek 'Valmis' määratakse, kui parandus on lõpetatud.\n"
"* Olekut \"Tühistatud\" kasutatakse siis, kui kasutaja tühistab parandustellimuse."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ": Insufficient Quantity To Repair"
msgstr ": Ebapiisav kogus parandamiseks"

#. module: repair
#: model:mail.template,body_html:repair.mail_template_repair_quotation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px;font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        Here is your repair order <strong t-out=\"object.name or ''\">RO/00004</strong>\n"
"        <t t-if=\"object.invoice_method != 'none'\">\n"
"            amounting in <strong><t t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 100.00</t>.</strong><br/>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            .<br/>\n"
"        </t>\n"
"        You can reply to this email if you have any questions.\n"
"        <br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px;font-size: 13px;\">\n"
"        Tere <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        Siin on Teie parandustellimus <strong t-out=\"object.name or ''\">RO/00004</strong>\n"
"        <t t-if=\"object.invoice_method != 'none'\">\n"
"            summas <strong><t t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 100.00</t>.</strong><br/>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            .<br/>\n"
"        </t>\n"
"        Võite sellele kirjale vastata, kui Teil on küsimusi.\n"
"        <br/><br/>\n"
"        Tänades,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr "<i>(Lisa)</i>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"<span class=\"o_stat_text\">1</span>\n"
"                                <span class=\"o_stat_text\">Invoices</span>"
msgstr ""
"<span class=\"o_stat_text\">1</span>\n"
"                                <span class=\"o_stat_text\">Arved</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Repairs</span>"
msgstr "<span class=\"o_stat_text\">Parandused</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial Number:</strong>"
msgstr "<strong>Partii/seerianumber:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Operations</strong>"
msgstr "<strong>Operatsioonid</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Parts</strong>"
msgstr "<strong>Osad</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Printing Date:</strong>"
msgstr "<strong>Printimise kuupäev:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product to Repair:</strong>"
msgstr "<strong>Toode parandamiseks:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Shipping address :</strong>"
msgstr "<strong>Tarneaadress:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total Without Taxes</strong>"
msgstr "<strong>Kokku maksudeta</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total</strong>"
msgstr "<strong>Kokku</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Warranty:</strong>"
msgstr "<strong>Garantii:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? See võib tekitada teie inventuuris ebakõla."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "Vajalik toiming"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "Tegevused"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tegevuse erandlik kohendus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "Tegevuse seis"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tegevuse liigi sümbol"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__type__add
msgid "Add"
msgstr "Lisa"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes."
msgstr "Lisa sisemised märkmed"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add quotation notes."
msgstr "Lisa pakkumisele märkmeid."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__after_repair
msgid "After Repair"
msgstr "Parandusjärgne"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr "Manuste arv"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__b4repair
msgid "Before Repair"
msgstr "Paranduseelne"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Cancel"
msgstr "Tühista"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr "Tühista parandustöö"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__cancel
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__cancel
msgid "Cancelled"
msgstr "Tühistatud"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_category_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_category_id
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom_category_id
msgid "Category"
msgstr "Kategooria"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""
"Valige partner, kellele tellimus esitatakse ja tarnitakse. Partneri leiate "
"tema nime, e-posti või sisemise viite järgi."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__color
msgid "Color Index"
msgstr "Värvikood"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__company_id
#: model:ir.model.fields,field_description:repair.field_repair_line__company_id
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "Ettevõte"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_config
msgid "Configuration"
msgstr "Seadistus"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "Kinnita parandustöö"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__confirmed
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Confirmed"
msgstr "Kinnitatud"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_fee__product_uom_category_id
#: model:ir.model.fields,help:repair.field_repair_line__product_uom_category_id
#: model:ir.model.fields,help:repair.field_repair_order__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Ühikute vaheline konversioon on võimalik ainult siis, kui ühikud kuuluvad "
"samasse kategooriasse. Konverteerimist tehakse määrade järgi."

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"Couldn't find a pricelist line matching this product and quantity.\n"
"You have to change either the product, the quantity or the pricelist."
msgstr ""
"Couldn't find a pricelist line matching this product and quantity.\n"
"You have to change either the product, the quantity or the pricelist."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Invoice"
msgstr "Loo arve"

#. module: repair
#: model:ir.model,name:repair.model_repair_order_make_invoice
msgid "Create Mass Invoice (repair)"
msgstr "Create Mass Invoice (repair)"

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tag
msgid "Create a new tag"
msgstr "Loo uus silt"

#. module: repair
#: model:ir.actions.act_window,name:repair.act_repair_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Create invoices"
msgstr "Loo arve"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_date
#: model:ir.model.fields,field_description:repair.field_repair_line__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__currency_id
#: model:ir.model.fields,field_description:repair.field_repair_line__currency_id
#: model:ir.model.fields,field_description:repair.field_repair_order__currency_id
msgid "Currency"
msgstr "Valuuta"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Customer"
msgstr "Klient"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__default_address_id
msgid "Default Address"
msgstr "Aadress vaikimisi"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__address_id
msgid "Delivery Address"
msgstr "Tarneaadress"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__name
#: model:ir.model.fields,field_description:repair.field_repair_line__name
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Description"
msgstr "Kirjeldus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_dest_id
msgid "Dest. Location"
msgstr "Sihtkoht"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__display_name
#: model:ir.model.fields,field_description:repair.field_repair_line__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__display_name
#: model:ir.model.fields,field_description:repair.field_repair_tags__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "Do you confirm you want to repair"
msgstr "Kas kinnitate, et soovite parandada?"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Do you really want to create the invoice(s)?"
msgstr "Kas soovite tõesti arve(d) luua?"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__done
msgid "Done"
msgstr "Tehtud"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__draft
msgid "Draft"
msgstr "Mustand"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"Draft invoices for this order will be cancelled. Do you confirm the action?"
msgstr ""
"Selle tellimuse arvete mustandid tühistatakse. Kas kinnitate toimingu?"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr "Parandustööde lõpp"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__tracking
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Tagage oma lattu ladustava toote jälgitavus."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Extra Info"
msgstr "Täiendav info"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Fees"
msgstr "Kulud"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "Jälgijad"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Jälgijad (partnerid)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon nt. fa-tasks"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "Tulevased tegevused"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "Grupeeri"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__group
msgid "Group by partner invoice address"
msgstr "Rühmita partneri arve aadressi järgi"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_message
msgid "Has Message"
msgstr "On sõnum"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "History"
msgstr "Ajalugu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__id
#: model:ir.model.fields,field_description:repair.field_repair_line__id
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__id
#: model:ir.model.fields,field_description:repair.field_repair_tags__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "ID"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_icon
msgid "Icon"
msgstr "Ikoon"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoon, mis näitab erandi tegevust"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
#: model:ir.model.fields,help:repair.field_repair_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "Kui märgitud, siis uued sõnumid nõuavad Su tähelepanu."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
#: model:ir.model.fields,help:repair.field_repair_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Kui valitud, on mõningatel sõnumitel saatmiserror."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__priority
msgid "Important repair order"
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "Ettevõttesisesed märkmed"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__move_id
msgid "Inventory Move"
msgstr "Laoliikumine"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_move_lines
msgid "Inventory Moves"
msgstr "Laoliikumised"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_id
msgid "Invoice"
msgstr "Arve"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoice_line_id
#: model:ir.model.fields,field_description:repair.field_repair_line__invoice_line_id
msgid "Invoice Line"
msgstr "Arve rida"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_method
msgid "Invoice Method"
msgstr "Arve meetod"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_state
msgid "Invoice State"
msgstr "Arve staatus"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice address:"
msgstr "Arve aadress:"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice and shipping address:"
msgstr "Arve ja tarne aadress:"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Invoice created"
msgstr "Arve loodud"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_line__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_order__invoiced
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Invoiced"
msgstr "Arveldatud"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_invoice_id
msgid "Invoicing Address"
msgstr "Arveaadress"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "Jälgija"

#. module: repair
#: model:ir.model,name:repair.model_account_move
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: repair
#: model:ir.model,name:repair.model_account_move_line
msgid "Journal Item"
msgstr "Andmiku kanderida"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee____last_update
#: model:ir.model.fields,field_description:repair.field_repair_line____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice____last_update
#: model:ir.model.fields,field_description:repair.field_repair_tags____last_update
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_date
#: model:ir.model.fields,field_description:repair.field_repair_line__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "Hilinenud tegevused"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "Asukoht"

#. module: repair
#: model:ir.model,name:repair.model_stock_production_lot
#: model:ir.model.fields,field_description:repair.field_repair_line__lot_id
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "Partii/seerianumber"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Peamine manus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr "Sõnumi edastamise veateade"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "Sõnumid"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Move"
msgstr "Kanne"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__move_id
msgid "Move created by the repair order"
msgstr "Parandustellimusega loodud liikumine"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Minu tegevuse tähtaeg"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Järgmine tegevus kalendris"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Järgmise tegevuse kuupäev"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "Järgmise tegevuse kokkuvõte"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "Järgmise tegevuse tüüp"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__none
msgid "No Invoice"
msgstr "Arve puudub"

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No account defined for product \"%s\"."
msgstr "Partnerile pole määratud kontot \"%s\"."

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No pricelist found."
msgstr "Hinnakirja ei leitud"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "No product defined on fees."
msgstr "Tasusid ei ole määratletud."

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "No repair order found. Let's create one!"
msgstr "Hooldustellimust ei leitud. Loome ühe!"

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No valid pricelist line found."
msgstr "Ühtegi kehtivat hinnakirja ei leitud."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__0
msgid "Normal"
msgstr "Tavaline"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimingute arv"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of errors"
msgstr "Veateadete arv"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Tegutsemist nõudvate sõnumite arv"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kohaletoimetamise veateatega sõnumite arv"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Lugemata sõnumite arv"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Only draft repairs can be confirmed."
msgstr "Kinnitada saab ainult \"Mustand\" parandamisi."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__fees_lines
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "Tegevused"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__operations
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr "Osad"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Please define an accounting sales journal for the company %s (%s)."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Price"
msgstr "Hind"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__pricelist_id
msgid "Pricelist"
msgstr "Hinnakiri"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__pricelist_id
msgid "Pricelist of the selected partner."
msgstr "Valitud partneri hinnakiri."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Print Quotation"
msgstr "Prindi pakkumine"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__priority
msgid "Priority"
msgstr "Prioriteet"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "Toode"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Product Moves"
msgstr "Toote siirded"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "Toote kogus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__tracking
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Product Tracking"
msgstr "Toote jälgimine"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "Toote mõõtühik"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "Parandatav toode"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr "Kõik remonditud tooted kuuluvad sellele partiile"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr "Koguseosa"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quantity
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Quantity"
msgstr "Tehtud kogused"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__draft
msgid "Quotation"
msgstr "Pakkumine"

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
msgid "Quotation / Order"
msgstr "Pakkumine / tellimus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__quotation_notes
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Quotation Notes"
msgstr "Hinnapakkumise märkused"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Quotations"
msgstr "Pakkumised"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready To Repair"
msgstr "Valmis parandustöödeks"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__ready
msgid "Ready to Repair"
msgstr "Valmis parandustöödeks"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__type__remove
msgid "Remove"
msgstr "Eemalda"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_bank_statement_line__repair_ids
#: model:ir.model.fields,field_description:repair.field_account_move__repair_ids
#: model:ir.model.fields,field_description:repair.field_account_payment__repair_ids
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
msgid "Repair"
msgstr "Paranda"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__description
msgid "Repair Description"
msgstr "Paranduse kirjeldus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_move_line__repair_fee_ids
msgid "Repair Fee"
msgstr "Paranduse tasu"

#. module: repair
#: model:ir.model,name:repair.model_repair_fee
msgid "Repair Fees"
msgstr "Paranduse tasud"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_move_line__repair_line_ids
msgid "Repair Line"
msgstr "Parandustööde järjekord"

#. module: repair
#: model:ir.model,name:repair.model_repair_line
msgid "Repair Line (parts)"
msgstr "Parandustööde järjekord (osad)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Notes"
msgstr "Parandustöö märkmed"

#. module: repair
#: model:ir.model,name:repair.model_repair_order
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Order"
msgstr "Parandustellimus"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Order #:"
msgstr "Paranda tellimust #:"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__repair_id
#: model:ir.model.fields,field_description:repair.field_repair_line__repair_id
msgid "Repair Order Reference"
msgstr "Paranda tellimusviide"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model:ir.model.fields,field_description:repair.field_stock_production_lot__repair_order_ids
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_graph
#: model_terms:ir.ui.view,arch_db:repair.view_repair_pivot
msgid "Repair Orders"
msgstr "Parandustellimused"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_tag
msgid "Repair Orders Tags"
msgstr "Parandustöö tellimuste sildid"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Quotation #:"
msgstr "Paranda hinnapakkumist #:"

#. module: repair
#: model:mail.template,name:repair.mail_template_repair_quotation
msgid "Repair Quotation: Send by email"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr "Paranda viide"

#. module: repair
#: model:product.product,name:repair.product_service_order_repair
#: model:product.template,name:repair.product_service_order_repair_product_template
msgid "Repair Services"
msgstr "Parandusteenused"

#. module: repair
#: model:ir.model,name:repair.model_repair_tags
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_form
msgid "Repair Tags"
msgstr "Parandustöö sildid"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be canceled in order to reset it to draft."
msgstr "Parandustöö peab olema tühistatud, et seda saaks muuta mustandiks."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be confirmed before starting reparation."
msgstr "Parandustöö tuleb enne remondi alustamist kinnitada."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be repaired in order to make the product moves."
msgstr "Repair must be repaired in order to make the product moves."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be under repair in order to end reparation."
msgstr "Repair must be under repair in order to end reparation."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_production_lot__repair_order_count
msgid "Repair order count"
msgstr "Parandustöö tellimuste arv"

#. module: repair
#: code:addons/repair/models/stock_production_lot.py:0
#, python-format
msgid "Repair orders of %s"
msgstr "%s parandustöö tellimused"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repaired
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__done
msgid "Repaired"
msgstr "Parandatud"

#. module: repair
#: model:ir.ui.menu,name:repair.menu_repair_order
#: model:ir.ui.menu,name:repair.repair_menu
msgid "Repairs"
msgstr "Parandustööd"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "Parandustellimused"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_reporting
msgid "Reporting"
msgstr "Aruandlus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__user_id
msgid "Responsible"
msgstr "Vastutaja"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "Vastutav kasutaja"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sõnumi kohaletoimetamise veateade"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_id
msgid "Sale Order"
msgstr "Müügitellimus"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_id
msgid "Sale Order from which the product to be repaired comes from."
msgstr "Müügitellimus, kust parandatav toode pärineb."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__schedule_date
msgid "Scheduled Date"
msgstr "Planeeritud saatmise kuupäev"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr "Otsi parandustellimusi"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__invoice_method
msgid ""
"Selecting 'Before Repair' or 'After Repair' will allow you to generate "
"invoice before or after the repair is done respectively. 'No invoice' means "
"you don't want to generate invoice for this repair order."
msgstr ""
"Valiku „Enne parandamist” või „Peale parandamist” valimine võimaldab teil "
"luua arve enne või pärast parandustööde teostamist. „Ei ole arve” tähendab, "
"et te ei soovi selle parandustöö jaoks arvet genereerida."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Send Quotation"
msgstr "Saada pakkumine"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Serial number is required for operation lines with products: %s"
msgstr "Seerianumber on vajalik järgmiste toodetega tööliinide jaoks: %s"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "Määra mustandiks"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Näita kõiki andmeid, mille järgmise tegevuse kuupäev on enne tänast kuupäeva"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_id
msgid "Source Location"
msgstr "Lähtekoht"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr "Alusta parandustööd"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__state
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "Staatus"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tegevuspõhised staatused\n"
"Üle aja: Tähtaeg on juba möödas\n"
"Täna: Tegevuse tähtaeg on täna\n"
"Planeeritud: Tulevased tegevused"

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "Laoliikumine"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_subtotal
#: model:ir.model.fields,field_description:repair.field_repair_line__price_subtotal
msgid "Subtotal"
msgstr "Vahesumma"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__name
msgid "Tag Name"
msgstr "Sildi nimi"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_tags_name_uniq
msgid "Tag name already exists!"
msgstr "Sildi nimi on juba loodud!"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tag
#: model:ir.model.fields,field_description:repair.field_repair_order__tag_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_search
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_tree
msgid "Tags"
msgstr "Sildid"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Tax"
msgstr "Maks"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_line__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_tax
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Taxes"
msgstr "Maksud"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_order_name
msgid "The name of the Repair Order must be unique!"
msgstr "Parandustöö tellimuse nimi peab olema unikaalne!"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr "Teie valitud toote mõõtühiku kategooria on toote mõõtühikust erinev."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__state
msgid ""
"The status of a repair line is set automatically to the one of the linked "
"repair order."
msgstr ""
"The status of a repair line is set automatically to the one of the linked "
"repair order."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid "This is the location where the product to repair is located."
msgstr "See on koht, kus parandatav toode asub."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__2binvoiced
msgid "To be Invoiced"
msgstr "Tehakse arve"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "Tänased tegevused"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_total
#: model:ir.model.fields,field_description:repair.field_repair_line__price_total
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_total
msgid "Total"
msgstr "Kokku"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Total amount"
msgstr "Kokku"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Jälitatavuse aruanne"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__type
msgid "Type"
msgstr "Tüüp"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kirjel oleva erandtegevuse tüüp."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__under_repair
msgid "Under Repair"
msgstr "Parandamises"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_unit
#: model:ir.model.fields,field_description:repair.field_repair_line__price_unit
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Unit Price"
msgstr "Ühiku hind"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_uom_name
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Unit of Measure"
msgstr "Mõõtühik"

#. module: repair
#: model:product.product,uom_name:repair.product_service_order_repair
#: model:product.template,uom_name:repair.product_service_order_repair_product_template
msgid "Units"
msgstr "tk"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread
msgid "Unread Messages"
msgstr "Lugemata sõnumid"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Lugemata sõnumite loendur"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Maksudeta summa"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Untaxed amount"
msgstr "Maksudeta summa"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "UoM"
msgstr "Mõõtühikud"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__1
msgid "Urgent"
msgstr "Kiireloomuline"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr "Ebapiisava parandustöö koguse hoiatus"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Warning"
msgstr "Hoiatus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__guarantee_limit
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Warranty Expiration"
msgstr "Garantii aegumine"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You can not delete a repair order once it has been confirmed. You must first"
" cancel it."
msgstr ""
"Kinnitatud parandustellimust ei saa te kustutada. Esmalt peate selle "
"tühistama."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You can not delete a repair order which is linked to an invoice which has "
"been posted once."
msgstr ""
"Te ei saa kustutada parandustellimust, mis on seotud postitatud arvega."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You can not enter negative quantities."
msgstr "Negatiivseid koguseid ei saa sisestada."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You cannot cancel a completed repair order."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You cannot delete a completed repair order."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You have to select a pricelist in the Repair form !\n"
" Please set one before choosing a product."
msgstr ""
"Te peate valima hinnakirja parandusvormis !\n"
" Palun seadistage üks enne toote valimist."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You have to select an invoice address in the repair form."
msgstr "Parandustöö vormis tuleb valida arve aadress."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "from location"
msgstr "asukohast"

#. module: repair
#: model:mail.template,report_name:repair.mail_template_repair_quotation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr "{{ (object.name or '').asenda('/','_') }}"

#. module: repair
#: model:mail.template,subject:repair.mail_template_repair_quotation
msgid ""
"{{ object.partner_id.name }} Repair Orders (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.partner_id.name }} Parandustöö tellimusi (Ref {{ object.name or "
"'n/a' }})"
