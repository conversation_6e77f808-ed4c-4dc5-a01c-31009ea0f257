# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from pytz import timezone
from odoo import models, fields, api, exceptions, _
from odoo.tools import format_datetime


class HrAttendance(models.Model):
    _inherit = "hr.attendance"
    _description = "Attendance"

    ### Will be removed
    attendance_state = fields.Selection([('latency', 'Latency'), ('approved', 'Approved Latency')], string='State', store=False)
    # latency_minutes = fields.Float(string='Latency Min', default=0.0, store=False)

    attendance_type_id = fields.Many2one('resource.calendar', related='employee_id.contract_id.resource_calendar_id',store=True, readonly=True)

    latency_allawence_approved = fields.Integer(string="Latency Approved by Manager In Minutes",
                                               related="attendance_type_id.latency_allawence_approved",
                                               store=True)

    there_is_letancy = fields.Boolean(related='attendance_type_id.there_is_letancy',string='يخضع لضوابط')
    checkin_tolarence = fields.Integer(related='attendance_type_id.checkin_tolarence', string='سماحية الدخول المتأخر (دقائق)')
    checkout_tolarence = fields.Integer(related='attendance_type_id.checkout_tolarence',string='سماحية الخروج المبكر (دقائق)')
    total_working_hour = fields.Float(related='attendance_type_id.total_working_hour',string='مجموع ساعات العمل الاجبارية')

    attendance_date = fields.Date(string='التاريخ', compute='_get_attendance_date', store=True)

    checkin_latency = fields.Integer(string='تأخير دخول',compute='get_checkin_latency')
    checkout_early = fields.Integer(string='خروج مبكر',compute='get_checkout_early')
    less_than_workinghour = fields.Integer(string='اقل من ساعات الدوام',compute='get_less_than_workinghour')

    latency_minutes = fields.Float(string='Latency Min', default=0.0, store=False)

    globle_time_off = fields.Boolean(compute="get_globle_time_off",str="اجازة رسمية")

    dayofweek = fields.Integer(compute='get_dayofweek')
    dayofweek_is_workingday = fields.Boolean(compute='get_dayofweek')

    hr_justify = fields.Boolean()
    hr_justify_user = fields.Char()

    computed_latency_note = fields.Char(compute='get_computed_latency',string="ملاحظة", store=True)
    computed_latency = fields.Integer(compute='get_computed_latency', string='إجمالي التأخير المحتسب')

    def convert_TZ_UTC(self, TZ_datetime):
        fmt = "%Y-%m-%d %H:%M:%S"
        now_utc = datetime.now(timezone('UTC'))
        now_timezone = now_utc.astimezone(timezone(self.env.user.tz))
        UTC_OFFSET_TIMEDELTA = datetime.strptime(now_utc.strftime(fmt), fmt) - datetime.strptime(now_timezone.strftime(fmt), fmt)
        local_datetime = datetime.strptime(str(TZ_datetime), fmt)
        result_utc_datetime = local_datetime - UTC_OFFSET_TIMEDELTA
        return result_utc_datetime

    def get_required_check(self, attendance_type_id,attendance_date,type='in'):

        #######3 Custom Attendance like Ramadan
        if attendance_type_id.custom_calender and attendance_date >= attendance_type_id.custom_calender_start and attendance_date <= attendance_type_id.custom_calender_end:
            requered_check = [item for item in attendance_type_id.custom_attendance_ids if item['dayofweek'] == str(attendance_date.weekday())]
            if requered_check:
                if type == 'in':
                    return requered_check[0].hour_from
                else:
                    return requered_check[0].hour_to
        #######################

        requered_check = [item for item in attendance_type_id.attendance_ids if item['dayofweek'] == str(attendance_date.weekday())]
        if requered_check:
            if type == 'in':
                return requered_check[0].hour_from
            else:
                return requered_check[0].hour_to

    def get_dayofweek(self):
        for elem in self:
            elem.dayofweek = elem.check_in.weekday()
            if str(elem.dayofweek) in elem.attendance_type_id.attendance_ids.mapped('dayofweek'):
                elem.dayofweek_is_workingday = True
            else:
                elem.dayofweek_is_workingday = False

    @api.depends('check_in')
    def _get_attendance_date(self):
        for elem in self:
            if elem.check_in:
                elem.attendance_date = self.convert_TZ_UTC(elem.check_in).date()

    def get_globle_time_off(self):
        for elem in self:
            search_date = str(elem.attendance_date)+" 00:00:00"
            check_if_in_global_leave = self.env['resource.calendar.leaves'].search_count([
                ('date_from','<=',search_date),
                ('date_to','>=',search_date),
                ('resource_id', '=', False),
                ('calendar_id','=',elem.attendance_type_id.id)
            ])
            if check_if_in_global_leave:
                elem.globle_time_off = True
            else:
                elem.globle_time_off = False

    def get_time_diff_in(self,time1, time2,tolarence):
        time2 = self.convert_TZ_UTC(time2)
        time2 = time2.hour + (time2.minute/60) + (time2.second/3600)
        if type(time1) != float or not time2:
            return 0
        if (time2 - time1) * 60 > tolarence:
            return int((time2 - time1) * 60)
        else:
            return 0

    def get_time_diff_out(self,time1, time2,tolarence):
        time2 = self.convert_TZ_UTC(time2)
        time2 = time2.hour + (time2.minute/60) + (time2.second/3600)
        if type(time1) != float or not time2:
            return 0
        if (time1 - time2) * 60 > tolarence:
            return int((time1 - time2) * 60)
        else:
            return 0

    def get_checkout_early(self):
        for elem in self:
            elem.checkout_early = 0
            if elem.there_is_letancy and elem.dayofweek_is_workingday and elem.check_out and not elem.globle_time_off:
                requered_checkout = elem.get_required_check(elem.attendance_type_id,elem.attendance_date,'out')
                elem.checkout_early = elem.get_time_diff_out(requered_checkout, elem.check_out, elem.checkout_tolarence)
            ############ Half leave
            check_for_halfday_holiday = self.env['hr.leave'].search(
                [('employee_id', '=', elem.employee_id.id), ('request_unit_half', '=', True),
                 ('state', 'in', ('validate1', 'validate')),
                 ('request_date_from', '=', str(elem.check_in.date()))], limit=1)

            if elem.there_is_letancy and elem.check_out and elem.dayofweek_is_workingday and not elem.globle_time_off and check_for_halfday_holiday and check_for_halfday_holiday.request_date_from_period == 'pm' and elem.attendance_type_id.half_day_leave_start:
                requered_checkout = elem.attendance_type_id.half_day_leave_start
                elem.checkout_early = elem.get_time_diff_out(requered_checkout, elem.check_out, 0)

            ############ Exit Permision
            check_for_aprovale_exit = self.env['hr.masarat.exit.permission'].search([
                ('employee_id', '=', elem.employee_id.id),
                ('start_date', '=', str(elem.check_in)[:10]),
                ('state', 'in', ['hr_approval', 'manager_approval'])], limit=1)

            if check_for_aprovale_exit and elem.checkout_early:
                #requered_checkout = [item for item in elem.attendance_type_id.attendance_ids if item['dayofweek'] == str(elem.dayofweek)][0]
                #requered_checkout = requered_checkout.hour_to
                requered_checkout = elem.get_required_check(elem.attendance_type_id, elem.attendance_date, 'out')

                exit_end_time = self.convert_TZ_UTC (check_for_aprovale_exit.end_time)
                exit_end_time = exit_end_time.hour + (exit_end_time.minute/60)
                if requered_checkout <= exit_end_time:
                    exit_start_time = self.convert_TZ_UTC(check_for_aprovale_exit.start_time)
                    exit_start_time = exit_start_time.hour + (exit_start_time.minute / 60)
                    elem.checkout_early = self.get_time_diff_out(exit_start_time, elem.check_out, 0)



    def get_checkin_latency(self):
        for elem in self:
            elem.checkin_latency = 0
            if elem.there_is_letancy and elem.dayofweek_is_workingday and not elem.globle_time_off:
                #requered_checkin = [item for item in elem.attendance_type_id.attendance_ids if item['dayofweek'] == str(elem.dayofweek)][0]
                #requered_checkin = requered_checkin.hour_from
                requered_checkin = elem.get_required_check(elem.attendance_type_id, elem.attendance_date, 'in')
                elem.checkin_latency = self.get_time_diff_in(requered_checkin, elem.check_in, elem.checkin_tolarence)

            ############ Half leave
            check_for_halfday_holiday = self.env['hr.leave'].search(
                [('employee_id', '=', elem.employee_id.id), ('request_unit_half', '=', True),
                 ('state', 'in', ('validate1', 'validate')),
                 ('request_date_from', '=', str(elem.check_in.date()))], limit=1)
            if elem.there_is_letancy and elem.dayofweek_is_workingday and not elem.globle_time_off and check_for_halfday_holiday and check_for_halfday_holiday.request_date_from_period == 'am' and elem.attendance_type_id.half_day_leave_start:
                requered_checkin = elem.attendance_type_id.half_day_leave_start
                elem.checkin_latency = self.get_time_diff_in(requered_checkin, elem.check_in, 0)

            ############ latency Approvale
            check_for_latency_aprovale = self.env['hr.masarat.latency'].search([('check_in_attendacy', '=', elem.id),('state', 'in', ['hr_approval', 'manager_approval'])], limit=1)
            if elem.checkin_latency and check_for_latency_aprovale:
                elem.checkin_latency = 0 if elem.latency_allawence_approved > elem.checkin_latency else elem.checkin_latency - elem.latency_allawence_approved


    def get_less_than_workinghour(self):
        for elem in self:
            elem.less_than_workinghour = 0
            if elem.there_is_letancy and elem.dayofweek_is_workingday and not elem.globle_time_off:
                if elem.worked_hours < elem.total_working_hour:
                    elem.less_than_workinghour = (elem.total_working_hour - elem.worked_hours) * 60

            ############ Half leave
            check_for_halfday_holiday = self.env['hr.leave'].search(
                [('employee_id', '=', elem.employee_id.id), ('request_unit_half', '=', True),
                 ('state', 'in', ('validate1', 'validate')),
                 ('request_date_from', '=', str(elem.check_in.date()))], limit=1)
            if elem.there_is_letancy and elem.dayofweek_is_workingday and not elem.globle_time_off and check_for_halfday_holiday and check_for_halfday_holiday and elem.attendance_type_id.half_day_leave_start:
                if elem.worked_hours < (elem.total_working_hour/2):
                    elem.less_than_workinghour = ((elem.total_working_hour/2) - elem.worked_hours) * 60

            ############ latency Approvale
            check_for_latency_aprovale = self.env['hr.masarat.latency'].search([('check_in_attendacy', '=', elem.id), ('state', 'in', ['hr_approval', 'manager_approval'])], limit=1)
            if elem.less_than_workinghour and check_for_latency_aprovale:
                elem.less_than_workinghour = 0 if elem.latency_allawence_approved > elem.less_than_workinghour else elem.less_than_workinghour - elem.latency_allawence_approved

            ############ Exit Permision
            check_for_aprovale_exit = self.env['hr.masarat.exit.permission'].search([
                ('employee_id', '=', elem.employee_id.id),
                ('start_date', '=', str(elem.check_in)[:10]),
                ('state', 'in', ['hr_approval', 'manager_approval'])], limit=1)

            if check_for_aprovale_exit and elem.less_than_workinghour:
                elem.less_than_workinghour = 0


    def get_computed_latency(self):
        for elem in self:
            elem.computed_latency = 0
            elem.computed_latency_note = ''
            multiplied = elem.attendance_type_id.penalty

            ######## No Latency is computed
            if not elem.there_is_letancy or not elem.dayofweek_is_workingday or elem.globle_time_off:
                continue
            ####################33

            ######## Check For HR Justify
            if elem.hr_justify:
                elem.computed_latency_note = "تبرير موارد بشرية بواسطة : "+str(elem.hr_justify_user)
                continue

            ########## Full leave check
            check_for_holiday_leave = self.env['hr.leave'].search(
                [('state', 'in', ('validate1', 'validate')), ('employee_id', '=', elem.employee_id.id),
                 ('request_unit_half', '=', False),
                 ('date_from', '<=', str(elem.check_in.date())), ('date_to', '>=', str(elem.check_in.date()))])
            if check_for_holiday_leave:
                elem.computed_latency_note += " الموظف في اجازة "
                continue

            ########## Absence leave check
            check_for_absence_leave = self.env['hr.masarat.absence'].search(
                [('employee_id', '=', elem.employee_id.id),
                 ('state', 'in', ['hr_approval', 'manager_approval']),
                 ('absence_start_at', '<=', str(elem.check_in.date())), ('absence_end_at', '>=', str(elem.check_in.date()))])
            if check_for_absence_leave:
                elem.computed_latency_note += " الموظف لديه اذن غياب "
                continue

            ########## work_assignment check
            check_for_work_assignment = self.env['hr.masarat.work.assignment'].search(
                [('employee_id', '=', elem.employee_id.id),
                 ('state', 'in', ['hr_approval', 'manager_approval']),
                 ('start_date', '<=', str(elem.check_in.date())), ('end_date', '>=', str(elem.check_in.date()))],limit=1)
            if check_for_work_assignment:
                if check_for_work_assignment.assignment_type:
                    elem.computed_latency_note += dict(check_for_work_assignment._fields['assignment_type'].selection).get(check_for_work_assignment.assignment_type)
                continue

            ################## Check Forget
            if (elem.check_in == elem.check_out) and not check_for_holiday_leave and elem.dayofweek_is_workingday:
                elem.computed_latency_note += " نسيان بصمة "
                elem.computed_latency = 120
                continue

            ############ Half leave
            check_for_halfday_holiday = self.env['hr.leave'].search(
                [('employee_id', '=', elem.employee_id.id), ('request_unit_half', '=', True),
                 ('state', 'in', ('validate1', 'validate')),
                 ('request_date_from', '=', str(elem.check_in.date()))], limit=1)

            if check_for_halfday_holiday:
                if check_for_halfday_holiday.request_date_from_period == 'am':
                    elem.computed_latency_note += " اجازة نصف يوم صباحية "
                elif check_for_halfday_holiday.request_date_from_period == 'pm':
                    elem.computed_latency_note += " اجازة نصف يوم مسائية "
                elem.computed_latency = (elem.checkout_early + elem.checkin_latency + elem.less_than_workinghour)*multiplied


            ############ latency Approvale
            check_for_latency_aprovale = self.env['hr.masarat.latency'].search([('check_in_attendacy', '=', elem.id)])
            if check_for_latency_aprovale and check_for_latency_aprovale.state in ('manager_approval', 'hr_approval'):
                elem.computed_latency = (elem.checkout_early + elem.checkin_latency + elem.less_than_workinghour) * multiplied
                elem.computed_latency_note += " تبرير تأخير "

            ############ Exit Permision
            check_for_aprovale_exit = self.env['hr.masarat.exit.permission'].search([
                ('employee_id', '=', elem.employee_id.id),
                ('start_date', '=', str(elem.check_in)[:10]),
                ('state', 'in', ['hr_approval', 'manager_approval'])], limit=1)

            if check_for_aprovale_exit:
                elem.computed_latency = (elem.checkout_early + elem.checkin_latency + elem.less_than_workinghour) * multiplied
                elem.computed_latency_note += " إذن خروج "

            else:
                elem.computed_latency = (elem.checkout_early + elem.checkin_latency + elem.less_than_workinghour) * multiplied


class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'

