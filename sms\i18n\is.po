# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sms
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-08-24 09:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/sms_widget.js:93
#, python-format
msgid "%s chars, fits in %s SMS (%s) "
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.send_sms_view_form
msgid "Cancel"
msgstr "Hætta við"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
msgid "Contact"
msgstr "Tengiliður"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_api__display_name
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr "Email Thread"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_api__id
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__id
msgid "ID"
msgstr "Auðkenni"

#. module: sms
#: code:addons/sms/models/mail_thread.py:53
#, python-format
msgid "Insufficient credit, unable to send SMS message: %s"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_api____last_update
#: model:ir.model.fields,field_description:sms.field_sms_send_sms____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__message
msgid "Message"
msgstr "Skilaboð"

#. module: sms
#: code:addons/sms/wizard/send_sms.py:82
#, python-format
msgid "Missing mobile number for %s."
msgstr ""

#. module: sms
#: code:addons/sms/models/mail_thread.py:55
#, python-format
msgid "No mobile number defined, unable to send SMS message: %s"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__recipients
msgid "Recipients"
msgstr "Viðtakendurf"

#. module: sms
#: model:ir.model,name:sms.model_sms_api
msgid "SMS API"
msgstr ""

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/xml/sms_widget.xml:4
#, python-format
msgid "SMS Pricing"
msgstr ""

#. module: sms
#: code:addons/sms/models/mail_thread.py:48
#, python-format
msgid "SMS message sent: %s"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.send_sms_view_form
msgid "Send"
msgstr ""

#. module: sms
#: model:ir.actions.act_window,name:sms.send_sms_action
#: model:ir.actions.act_window,name:sms.send_sms_form_action
#: model:ir.model,name:sms.model_sms_send_sms
#: model_terms:ir.ui.view,arch_db:sms.partner_form_send_sms_form_view
msgid "Send SMS"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.send_sms_view_form
msgid "Send an SMS"
msgstr ""
