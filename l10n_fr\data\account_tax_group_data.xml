<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="tax_group_tva_0" model="account.tax.group">
            <field name="name">TVA 0%</field>
            <field name="country_id" ref="base.fr"/>
        </record>
        <record id="tax_group_tva_20" model="account.tax.group">
            <field name="name">TVA 20%</field>
            <field name="country_id" ref="base.fr"/>
        </record>
        <record id="tax_group_tva_85" model="account.tax.group">
            <field name="name">TVA 8.5%</field>
            <field name="country_id" ref="base.fr"/>
        </record>
        <record id="tax_group_tva_55" model="account.tax.group">
            <field name="name">TVA 5.5%</field>
            <field name="country_id" ref="base.fr"/>
        </record>
        <record id="tax_group_tva_10" model="account.tax.group">
            <field name="name">TVA 10%</field>
            <field name="country_id" ref="base.fr"/>
        </record>
        <record id="tax_group_tva_21" model="account.tax.group">
            <field name="name">TVA 2.1%</field>
            <field name="country_id" ref="base.fr"/>
        </record>
    </data>

    <data noupdate="0">
        <record id="display_name_in_footer_param" model="ir.config_parameter">
            <field name="key">account.display_name_in_footer</field>
            <field name="value" eval="True"/>
        </record>
    </data>
</odoo>
