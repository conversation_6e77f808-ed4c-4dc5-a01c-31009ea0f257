# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_calendar
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-15 14:03+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: French (Belgium) (http://www.transifex.com/odoo/odoo-9/"
"language/fr_BE/)\n"
"Language: fr_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid ""
"<br/> You can now click on <b>'Create Client ID'</b>\n"
"                                    <br/><br/>"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid ""
"<br/> You should now configure the allowed pages on which you will be "
"redirected. To do it, you need to complete the field <b>\"Authorized "
"redirect URI\"</b>\n"
"                                    and set as value  (your own domain "
"followed by <i>'/google_account/authentication'</i>):\n"
"                                    <br/>==&gt;"
msgstr ""

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.action_config_settings_google_calendar
msgid "API Configuration"
msgstr ""

#. module: google_calendar
#: model:ir.ui.menu,name:google_calendar.menu_calendar_google_tech_config
msgid "API Credentials"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:77
#, python-format
msgid "Accounts"
msgstr "Comptes"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:67
#, python-format
msgid ""
"All events have been disconnected from your previous account. You can now "
"restart the synchronization"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:49
#, python-format
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:71
#, python-format
msgid ""
"An error occured while disconnecting events from your previous account. "
"Please retry or contact your administrator."
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Attendee information"
msgstr "Informations du participant"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Calendar"
msgstr "Calendrier"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_cal_id
msgid "Calendar ID"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid ""
"Check that the Application type is set on <b>'Web Application'</b>, then "
"click on <b>'Configure consent screen'</b>.\n"
"                                    <br/> Specify an email address and a "
"product name, then save.\n"
"                                    <br/><br/>"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid ""
"Click on <b>'Create a project...'</b> and enter a project name and change "
"your id if you want. Don't forget to accept the Terms of Services\n"
"                                    <br/><br/>"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_base_config_settings_cal_client_id
msgid "Client_id"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_base_config_settings_cal_client_secret
msgid "Client_key"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:46
#: code:addons/google_calendar/static/src/js/calendar_sync.js:50
#, python-format
msgid "Configuration"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid "Connect on your google account and go to"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_display_name
msgid "Display Name"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:58
#, python-format
msgid "Do you want to do this now?"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Event"
msgstr "Evènement"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:72
#, python-format
msgid "Event disconnection error"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:68
#, python-format
msgid "Event disconnection success"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_attendee_google_internal_event_id
msgid "Google Calendar Event Id"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid "Google Client ID"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid "Google Client Secret"
msgstr ""

#. module: google_calendar
#: sql_constraint:calendar.attendee:0
msgid "Google ID should be unique!"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/google_calendar.py:718
#, python-format
msgid ""
"Google is lost... the next synchro will be a full synchro. \n"
"\n"
" %s"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_id
msgid "ID"
msgstr "ID"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:57
#, python-format
msgid ""
"In order to do this, you first need to disconnect all existing events from "
"the old account."
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid ""
"In the menu on left side, select the sub menu <b>'Credentials'</b> (from "
"menu APIs and auth) and click on button <b>'Create new Client ID'</b>\n"
"                                    <br/><br/>"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid ""
"In the menu on left side, select the sub menu APIs (from menu APIs and auth) "
"and click on <b>'Calendar API'</b>.\n"
"                                    <br/> Activate the Calendar API by "
"clicking on the blue button <b>'Enable API'</b>.\n"
"                                    <br/> When it's done, the Calendar API "
"overview will be available\n"
"                                    <br/><br/>"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_users_google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar___last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_last_sync_date
msgid "Last synchro date"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_attendee_oe_synchro_date
msgid "Odoo Synchro Date"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event_oe_update_date
msgid "Odoo Update Date"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid ""
"Once done, you will have the both informations (<b>Client ID</b> and "
"<b>Client Secret</b>) that you need to insert in the 2 fields below!\n"
"                                    <br/><br/>"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:38
#, python-format
msgid "Redirection"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_rtoken
msgid "Refresh Token"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid "Return at Top"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_base_config_settings_google_cal_sync
msgid "Show Tutorial"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:88
#, python-format
msgid "Sync with <b>Google</b>"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:42
#, python-format
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:56
#, python-format
msgid ""
"The account you are trying to synchronize (%s) is not the same as the last "
"one used (%s)!"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid ""
"To setup the signin process with Google, first you have to perform the "
"following steps"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_token_validity
msgid "Token Validity"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_base_config_settings_server_uri
msgid "URI for tuto"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_token
msgid "User token"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/calendar_sync.js:34
#, python-format
msgid "You will be redirected to Google to authorize access to your calendar!"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/google_calendar.py:298
#, python-format
msgid "Your token is invalid or has been revoked !"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_base_config_settings
msgid "base.config.settings"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar
msgid "google.calendar"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_calendar_config_settings
msgid "https://console.developers.google.com/"
msgstr ""
