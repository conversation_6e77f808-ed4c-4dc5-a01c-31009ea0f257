# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <eric<PERSON><PERSON>@yahoo.com>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email_amount
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email_amount
msgid "# emails to send"
msgstr "# e-mails te verzenden"

#. module: hr
#: code:addons/hr/models/hr_job.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: hr
#: model:ir.actions.report,print_report_name:hr.hr_employee_print_badge
msgid "'Print Badge - %s' % (object.name).replace('/', '')"
msgstr "'Print badge - %s' % (object.name).replace('/', '')"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 dagen per jaar, waarvan <br>6 naar keuze."

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"<b>Congratulations!</b> May I recommend you to setup an <a "
"href=\"%s\">onboarding plan?</a>"
msgstr ""
"<b>Gefeliciteerd!</b> Ik raad je aan om een <a href=\"%s\">onboarding plan "
"op te stellen?</a>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Beheren\" title=\"Beheren\"/>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<small><b>READ</b></small>"
msgstr "<small><b>LEES</b></small>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle text-success\" role=\"img\" aria-label=\"Present\" title=\"Present\" name=\"presence_present\">\n"
"                                                </span>"
msgstr ""
"<span class=\"fa fa-circle text-success\" role=\"img\" aria-label=\"Aanwezig\" title=\"Aanwezig\" name=\"presence_present\">\n"
"                                                 </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle text-warning\" role=\"img\" aria-label=\"To define\" title=\"To define\" name=\"presence_to_define\">\n"
"                                                </span>"
msgstr ""
"<span class=\"fa fa-circle text-warning\" role=\"img\" aria-label=\"Te bepalen\" title=\"Te bepalen\" name=\"presence_to_define\">\n"
"                                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle-o text-muted\" role=\"img\" aria-label=\"Absent\" title=\"Absent\" name=\"presence_absent\">\n"
"                                                </span>"
msgstr ""
"<span class=\"fa fa-circle-o text-muted\" role=\"img\" aria-label=\"Afwezig\" title=\"Afwezig\" name=\"presence_absent\">\n"
"                                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle-o text-success\" role=\"img\" aria-label=\"Present but not active\" title=\"Present but not active\" name=\"presence_absent_active\">\n"
"                                                </span>"
msgstr ""
"<span class=\"fa fa-circle-o text-success\" role=\"img\" aria-label=\"Aanwezig maar niet actief\" title=\"Aanwezig maar niet actief\" name=\"presence_absent_active\">\n"
"                                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"De waarden die hier worden "
"ingesteld, zijn bedrijfsspecifiek.\" role=\"img\" aria-label=\"De waarden "
"die hier worden ingesteld, zijn bedrijfsspecifiek.\" "
"groups=\"base.group_multi_company\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"ml8 mr-2\">IP Addresses (comma-separated)</span>"
msgstr "<span class=\"ml8 mr-2\">IP adressen (gesplitst met komma's)</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"ml8 mr-2\">Minimum number of emails to sent </span>"
msgstr "<span class=\"ml8 mr-2\">Minimum aantal e-mails te versturen</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label\">Close Activities</span>"
msgstr "<span class=\"o_form_label o_hr_form_label\">Activiteiten sluiten</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label\">Detailed Reason</span>"
msgstr "<span class=\"o_form_label o_hr_form_label\">Gedetailleerde reden</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label\">Personal Info</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label\">Persoonlijke informatie</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Presence Control</span>"
msgstr "<span class=\"o_form_label\">Aanwezigheidscontrole</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                        Not Connected\n"
"                                    </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                        Niet verbonden\n"
"                                    </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Not Connected\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Niet verbonden\n"
"                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "<span class=\"o_stat_text\">Connected Since</span>"
msgstr "<span class=\"o_stat_text\">Verbonden sinds</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_form
msgid "<span class=\"o_stat_text\">Employee(s)</span>"
msgstr "<span class=\"o_stat_text\">Werknemer(s)</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<span class=\"o_stat_text\">Present Since</span>"
msgstr "<span class=\"o_stat_text\">Aanwezig sinds</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<span>Km</span>"
msgstr "<span>Km</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<strong><span>Reporting</span></strong>"
msgstr "<strong><span>Reportage</span></strong>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "A full-time position <br>Attractive salary package."
msgstr "Een fulltime functie, <br>Aantrekkelijk salarispakket."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_user_uniq
msgid "A user cannot be linked to multiple employees in the same company."
msgstr ""
"Een gebruiker kan niet gekoppeld zijn aan meerdere werknemers binnen "
"hetzelfde bedrijf."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__absent
msgid "Absent"
msgstr "Afwezig"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Achieve monthly sales objectives"
msgstr "Bereik maandelijkse verkoop doelstellingen"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__active
#: model:ir.model.fields,field_description:hr.field_hr_employee__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__active
#: model:ir.model.fields,field_description:hr.field_hr_plan__active
#: model:ir.model.fields,field_description:hr.field_hr_work_location__active
msgid "Active"
msgstr "Actief"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_ids
#: model:ir.model.fields,field_description:hr.field_hr_plan__plan_activity_type_ids
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
msgid "Activities"
msgstr "Activiteiten"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_activity_type_view_form
msgid "Activity"
msgstr "Activiteit"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activiteit uitzondering decoratie"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_plan
msgid "Activity Planning"
msgstr "Activititeitsplanning"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_state
msgid "Activity State"
msgstr "Activiteitsfase"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__activity_type_id
msgid "Activity Type"
msgstr "Soort activiteit"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_icon
msgid "Activity Type Icon"
msgstr "Activiteitensoort icoon"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Activity by"
msgstr "Activiteit door"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid "Add a new employee"
msgstr "Nieuwe werknemer toevoegen"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_plan_action
msgid "Add a new plan"
msgstr "Een nieuw abonnement toevoegen"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_plan_activity_type_action
msgid "Add a new planning activity"
msgstr "Een nieuwe planningsactiviteit toevoegen"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_description
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_description
msgid "Additional Information"
msgstr "Aanvullende informatie"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__additional_note
#: model:ir.model.fields,field_description:hr.field_res_users__additional_note
msgid "Additional Note"
msgstr "Extra notitie"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Additional languages"
msgstr "Aanvullende talen"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_home_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_home_id
msgid "Address"
msgstr "Adres"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Administrative Work"
msgstr "Administratief werk"

#. module: hr
#: model:res.groups,name:hr.group_hr_manager
msgid "Administrator"
msgstr "Beheerder"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_presence
msgid "Advanced Presence Control"
msgstr "Geavanceerde aanwezigheidscontrole"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Advanced presence of employees"
msgstr "Geavanceerde aanwezigheid van werknemers"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:hr.field_mail_channel__alias_contact
msgid "Alias Contact Security"
msgstr "Alias contact beveiliging"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__lang
#: model:ir.model.fields,help:hr.field_res_users__private_lang
msgid ""
"All the emails and documents sent to this contact will be translated in this"
" language."
msgstr ""
"Al de e-mails en documenten verzonden naar dit contact worden vertaald in "
"deze taal."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data"
msgstr "Sta werknemers toe om hun eigen gegevens bij te werken"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data."
msgstr "Sta werknemers toe om hun eigen gegevens bij te werken."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Apply"
msgstr "Toepassen"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Approvers"
msgstr "Goedkeurders"

#. module: hr
#. openerp-web
#: code:addons/hr/static/src/js/hr_employee.js:0
#: code:addons/hr/static/src/js/hr_employee.js:0
#, python-format
msgid "Archive"
msgstr "Archiveren"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__archive_private_address
msgid "Archive Private Address"
msgstr "Archiveer privé adres"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Archived"
msgstr "Gearchiveerd"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"As an employee of our company, you will <b>collaborate with each department to create and deploy\n"
"                                disruptive products.</b> Come work at a growing company that offers great benefits with opportunities to\n"
"                                moving forward and learn alongside accomplished leaders. We're seeking an experienced and outstanding member of staff.\n"
"                                <br><br>\n"
"                                This position is both <b>creative and rigorous</b> by nature you need to think outside the box.\n"
"                                We expect the candidate to be proactive and have a \"get it done\" spirit. To be successful,\n"
"                                you will have solid solving problem skills."
msgstr ""
"Als werknemer van ons bedrijf <b>werk je samen met elke afdeling om ontwrichtende producten te maken\n"
"                                  en in te zetten.</b> Kom werken bij een groeiend bedrijf dat grote voordelen biedt met kansen om\n"
"                                  vooruit te gaan en te leren samen met ervaren leiders. We zoeken een ervaren en uitmuntend personeelslid.\n"
"                                  <br><br>\n"
"                                  Deze positie is zowel <b>creatief als rigoureus</b> van aard; je moet buiten de gebaande paden denken.\n"
"                                 We verwachten van de kandidaat dat hij proactief is en een \"get it done\" -mentaliteit heeft. Succesvol zijn,\n"
"                                 je beschikt over solide probleemoplossende vaardigheden."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_job__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Attendance"
msgstr "Aanwezigheid"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Attendance/Point of Sale"
msgstr "Aanwezigheid/kassa"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__mail_alias__alias_contact__employees
msgid "Authenticated Employees"
msgstr "Goedgekeurde werknemers"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.mail_channel_view_form_
msgid "Auto Subscribe Departments"
msgstr "Automatisch afdelingen volgen"

#. module: hr
#: model:ir.model.fields,help:hr.field_mail_channel__subscription_department_ids
msgid "Automatically subscribe members of those departments to the channel."
msgstr "Laat leden van deze afdelingen het kanaal automatisch volgen."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Autonomy"
msgstr "Autonomie"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Available"
msgstr "Beschikbaar"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1920
msgid "Avatar"
msgstr "Avatar"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Away"
msgstr "Afwezig"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__bachelor
msgid "Bachelor"
msgstr "Bachelor"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Bachelor Degree or Higher"
msgstr "Bachelor diploma of hoger"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__barcode
#: model:ir.model.fields,field_description:hr.field_res_users__barcode
msgid "Badge ID"
msgstr "Badge ID"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__bank_account_id
msgid "Bank Account Number"
msgstr "Bankrekeningnummer"

#. module: hr
#: model:ir.model,name:hr.model_base
msgid "Base"
msgstr "Basis"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip
msgid "Based on IP Address"
msgstr "Gebaseerd op IP adres"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_attendance
msgid "Based on attendances"
msgstr "Gebaseerd op aanwezigheden"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email
msgid "Based on number of emails sent"
msgstr "Gebaseerd op aantal verzonden e-mails"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_login
msgid "Based on user status in system"
msgstr "Gebaseerd op de status van de gebruiker in het systeem"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_base
msgid "Basic Employee"
msgstr "Basis werknemer"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__can_edit
msgid "Can Edit"
msgstr "Kan wijzigen"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.plan_wizard
msgid "Cancel"
msgstr "Annuleren"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__certificate
#: model:ir.model.fields,field_description:hr.field_res_users__certificate
msgid "Certificate Level"
msgstr "Certificaat niveau"

#. module: hr
#: model:ir.actions.act_window,name:hr.res_users_action_my
msgid "Change my Preferences"
msgstr "Wijzig mijn voorkeuren"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Chat"
msgstr "Chat"

#. module: hr
#: model:hr.job,name:hr.job_ceo
msgid "Chief Executive Officer"
msgstr "Algemeen directeur"

#. module: hr
#: model:hr.job,name:hr.job_cto
msgid "Chief Technical Officer"
msgstr "Technische directeur"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__child_ids
msgid "Child Departments"
msgstr "Sub-afdelingen"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Citizenship"
msgstr "Burgerschap"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "City"
msgstr "Plaats"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,field_description:hr.field_res_users__coach_id
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__coach
msgid "Coach"
msgstr "Coach"

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "Coach of employee %s is not set."
msgstr "Coach van werknemer %s is niet ingesteld."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__color
#: model:ir.model.fields,field_description:hr.field_hr_employee__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__color
msgid "Color Index"
msgstr "Kleurindex"

#. module: hr
#: model:ir.model,name:hr.model_res_company
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Companies"
msgstr "Bedrijven"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__company_id
#: model:ir.model.fields,field_description:hr.field_hr_job__company_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__company_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Company"
msgstr "Bedrijf"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_id
msgid "Company Country"
msgstr "Land van bedrijf"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Company Logo"
msgstr "Bedrijfslogo"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__resource_calendar_id
msgid "Company Working Hours"
msgstr "Werkuren bedrijf"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_id
msgid "Company employee"
msgstr "Bedrijfswerknemer"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__complete_name
msgid "Complete Name"
msgstr "Volledige naam"

#. module: hr
#: model:ir.model,name:hr.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Configuration"
msgstr "Configuratie"

#. module: hr
#: model:hr.job,name:hr.job_consultant
msgid "Consultant"
msgstr "Consultant"

#. module: hr
#: model:ir.model,name:hr.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Contact Information"
msgstr "Contactgegevens"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__contractor
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__contractor
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__contractor
msgid "Contractor"
msgstr "Aannemer"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Country"
msgstr "Land"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_code
msgid "Country Code"
msgstr "Landcode"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__country_of_birth
msgid "Country of Birth"
msgstr "Geboorteland"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_date
msgid "Create Date"
msgstr "Aanmaakdatum"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid "Create a new department"
msgstr "Maak een nieuw afdeling aan"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Create content that will help our users on a daily basis"
msgstr "Maak inhoud die onze gebruikers dagelijks zal helpen."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Create employee"
msgstr "Werknemer aanmaken"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_date
#: model:ir.model.fields,field_description:hr.field_hr_job__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_employee
msgid "Current Number of Employees"
msgstr "Huidig aantal werknemers"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Customer Relationship"
msgstr "Klantrelatie"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__birthday
#: model:ir.model.fields,field_description:hr.field_res_users__birthday
msgid "Date of Birth"
msgstr "Geboortedatum"

#. module: hr
#: code:addons/hr/models/hr_departure_reason.py:0
#, python-format
msgid "Default departure reasons cannot be deleted."
msgstr "Standaard vertrekredenen kunnen niet worden verwijderd."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Definieert de planning van de resource"

#. module: hr
#: model:ir.model,name:hr.model_hr_department
#: model:ir.model.fields,field_description:hr.field_hr_employee__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__department_id
#: model:ir.model.fields,field_description:hr.field_hr_job__department_id
#: model:ir.model.fields,field_description:hr.field_res_users__department_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Department"
msgstr "Afdeling"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__name
msgid "Department Name"
msgstr "Naam afdeling"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_department_kanban_action
#: model:ir.actions.act_window,name:hr.hr_department_tree_action
#: model:ir.ui.menu,name:hr.menu_hr_department_kanban
#: model:ir.ui.menu,name:hr.menu_hr_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
msgid "Departments"
msgstr "Afdelingen"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Departure"
msgstr "Vertrek"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_date
msgid "Departure Date"
msgstr "Vertrekdatum"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_reason
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_reason_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_reason_id
msgid "Departure Reason"
msgstr "Reden vertrek"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_departure_reason_action
#: model:ir.ui.menu,name:hr.menu_hr_departure_reason_tree
msgid "Departure Reasons"
msgstr "Redenen van vertrek"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Vertrek wizard"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Dependant"
msgstr "Afhankelijk"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__child_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__child_ids
msgid "Direct subordinates"
msgstr "Directe werknemers"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_employee
msgid "Directory"
msgstr "Folder"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Discard"
msgstr "Negeren"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Discover our products."
msgstr "Ontdek onze producten."

#. module: hr
#: model:ir.model,name:hr.model_mail_channel
msgid "Discussion Channel"
msgstr "Discussiekanaal"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr.field_hr_job__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_work_location__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__divorced
msgid "Divorced"
msgstr "Gescheiden"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__doctor
msgid "Doctor"
msgstr "Dokter"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__driving_license
msgid "Driving License"
msgstr "Rijbewijs"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                        You can make a real contribution to the success of the company.\n"
"                        <br>\n"
"                        Several activities are often organized all over the year, such as weekly\n"
"                        sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Elke werknemer krijgt de kans om de impact van zijn werk te zien.\n"
"                        Je kunt een wezenlijke bijdrage leveren aan het succes van het bedrijf.\n"
"                        <br>\n"
"                        Er worden vaak het hele jaar door verschillende activiteiten georganiseerd, zoals wekelijks\n"
"                        sportsessies, teambuilding-evenementen, maandelijkse borrel en nog veel meer."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Eat &amp; Drink"
msgstr "Eten &amp; Drinken"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Education"
msgstr "Opleiding"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Email"
msgstr "E-mail"

#. module: hr
#: model:ir.model,name:hr.model_mail_alias
msgid "Email Aliases"
msgstr "E-mail aliassen"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Emergency"
msgstr "Noodgeval"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_contact
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_contact
msgid "Emergency Contact"
msgstr "Nood contactpersoon"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_phone
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_phone
msgid "Emergency Phone"
msgstr "Nood telefoonnummer"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__employee_id
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__employee
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration_employee
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee"
msgstr "Werknemer"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_category
msgid "Employee Category"
msgstr "Categorie werknemer"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_count
msgid "Employee Count"
msgstr "Werknemer aantal"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_employee_self_edit
msgid "Employee Editing"
msgstr "Wijzigen gegevens"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Employee Image"
msgstr "Afbeelding werknemer"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_lang
msgid "Employee Lang"
msgstr "Werknemer taal"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__name
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "Employee Name"
msgstr "Naam werknemer"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_categ_form
#: model:ir.model.fields,field_description:hr.field_res_users__category_ids
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_form
msgid "Employee Tags"
msgstr "Werknemerlabels"

#. module: hr
#. openerp-web
#: code:addons/hr/static/src/js/hr_employee.js:0
#, python-format
msgid "Employee Termination"
msgstr "Beëindiging werknemer"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__employee_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__employee_type
#: model:ir.model.fields,field_description:hr.field_res_users__employee_type
msgid "Employee Type"
msgstr "Werknemerstype"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Employee Update Rights"
msgstr "Werknemer update rechten"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__bank_account_id
#: model:ir.model.fields,help:hr.field_res_users__employee_bank_account_id
msgid "Employee bank salary account"
msgstr "Werknemers salaris bankrekening"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_bank_account_id
msgid "Employee's Bank Account Number"
msgstr "Bankrekeningnummer werknemer"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_country_id
msgid "Employee's Country"
msgstr "Werknemer zijn land"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee's Name"
msgstr "Naam werknemer"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Employee(s)"
msgstr "Werknemer(s)"

#. module: hr
#: model:ir.actions.act_window,name:hr.act_employee_from_department
#: model:ir.actions.act_window,name:hr.hr_employee_action_from_user
#: model:ir.actions.act_window,name:hr.hr_employee_public_action
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__employee_ids
#: model:ir.model.fields,field_description:hr.field_res_partner__employee_ids
#: model:ir.ui.menu,name:hr.menu_hr_employee_payroll
#: model:ir.ui.menu,name:hr.menu_hr_employee_user
#: model:ir.ui.menu,name:hr.menu_hr_root
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_activity
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
#: model_terms:ir.ui.view,arch_db:hr.view_partner_tree2
msgid "Employees"
msgstr "Werknemers"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_partner__employees_count
#: model:ir.model.fields,field_description:hr.field_res_users__employees_count
msgid "Employees Count"
msgstr "Aantal werknemers"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_employee_tree
msgid "Employees Structure"
msgstr "Organigram"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_list
msgid "Employees Tags"
msgstr "Werknemerlabels"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Enrich employee profiles with skills and resumes"
msgstr "Verrijk werknemers hun profielen met vaardigheden en CV's"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__address_home_id
#: model:ir.model.fields,help:hr.field_res_users__address_home_id
msgid ""
"Enter here the private address of the employee, not the one linked to your "
"company."
msgstr ""
"Geef het privéadres van de werknemer in, niet het adres gelinkt aan je "
"bedrijf."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Expand your knowledge of various business industries"
msgstr "Vergroot je kennis van verschillende bedrijfssectoren."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_recruitment
msgid "Expected New Employees"
msgstr "Verwachte nieuwe werknemers"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__expected_employees
msgid ""
"Expected number of employees for this job position after new recruitment."
msgstr "Verwachte aantal werknemers voor deze functie na de nieuwe werving."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Experience in writing online content"
msgstr "Ervaring in online content schrijven"

#. module: hr
#: model:hr.job,name:hr.job_developer
msgid "Experienced Developer"
msgstr "Ervaren ontwikkelaar"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__female
msgid "Female"
msgstr "Vrouw"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_field
#: model:ir.model.fields,field_description:hr.field_res_users__study_field
msgid "Field of Study"
msgstr "Vakgebieden"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_fired
msgid "Fired"
msgstr "Ontslagen"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Relaties)"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icoon bijv. fa-tasks"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__freelance
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__freelance
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__freelance
msgid "Freelancer"
msgstr "Freelancer"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Fruit, coffee and <br>snacks provided."
msgstr "Fruit, koffie en <br>snacks zijn aanwezig."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Future Activities"
msgstr "Toekomstige activiteiten"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__gender
#: model:ir.model.fields,field_description:hr.field_res_users__gender
msgid "Gender"
msgstr "Geslacht"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Generate"
msgstr "Genereren"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Google Adwords experience"
msgstr "Google Adwords ervaring"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__graduate
msgid "Graduate"
msgstr "Gediplomeerd"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Great team of smart people, in a friendly and open culture"
msgstr "Geweldig team van slimme mensen, in een vriendelijke en open cultuur."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Group By"
msgstr "Groeperen op"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_channel__subscription_department_ids
msgid "HR Departments"
msgstr "HR afdelingen"

#. module: hr
#: model:ir.actions.server,name:hr.ir_cron_data_check_work_permit_validity_ir_actions_server
#: model:ir.cron,cron_name:hr.ir_cron_data_check_work_permit_validity
#: model:ir.cron,name:hr.ir_cron_data_check_work_permit_validity
msgid "HR Employee: check work permit validity"
msgstr "HR Werknemer: check geldigheid werkvergunning"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "HR Settings"
msgstr "Personeelsbeheer instellingen"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__has_message
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_message
#: model:ir.model.fields,field_description:hr.field_hr_job__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Highly creative and autonomous"
msgstr "Zeer creatief en autonoom."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_hired_employee
msgid "Hired Employees"
msgstr "Aangenomen werknemers"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__km_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__km_home_work
msgid "Home-Work Distance"
msgstr "Afstand thuis-werk"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_icon_display
msgid "Hr Icon Display"
msgstr "HR-pictogramweergave"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_res_users__hr_presence_state
msgid "Hr Presence State"
msgstr "HR aanwezigheidsstatus"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_main
msgid "Human Resources"
msgstr "Personeelszaken"

#. module: hr
#: model:hr.job,name:hr.job_hrm
msgid "Human Resources Manager"
msgstr "Personeelsbeheer manager"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_employee__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr.field_hr_job__id
#: model:ir.model.fields,field_description:hr.field_hr_plan__id
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__id
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__id
msgid "ID"
msgstr "ID"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__id_card
msgid "ID Card Copy"
msgstr "Identiteitskaart kopie"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__barcode
#: model:ir.model.fields,help:hr.field_res_users__barcode
msgid "ID used for employee identification."
msgstr "ID gebruikt voor werknemer identificatie. "

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_icon
msgid "Icon"
msgstr "Icoon"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icoon om uitzondering op activiteit aan te geven."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__identification_id
#: model:ir.model.fields,field_description:hr.field_res_users__identification_id
msgid "Identification No"
msgstr "Identificatienr."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction
#: model:ir.model.fields,help:hr.field_hr_department__message_unread
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,help:hr.field_hr_employee__message_unread
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction
#: model:ir.model.fields,help:hr.field_hr_job__message_unread
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "indien aangevinkt hebben sommige leveringen een fout."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Als het actief veld uitstaat, kun je het resource record verbergen zonder "
"deze te verwijderen."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1920
msgid "Image"
msgstr "Afbeelding"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1024
msgid "Image 1024"
msgstr "Afbeelding 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_128
msgid "Image 128"
msgstr "Afbeelding 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_256
msgid "Image 256"
msgstr "Afbeelding 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_512
msgid "Image 512"
msgstr "Afbeelding 512"

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "Import Template for Employees"
msgstr "Importeer sjabloon voor werknemers"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "In Position"
msgstr "Is vervuld"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "In Recruitment"
msgstr "Wordt geworven"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_job__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__is_system
msgid "Is System"
msgstr "Is systeem"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Job"
msgstr "Functie"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__description
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Job Description"
msgstr "Functieomschrijving"

#. module: hr
#: model:ir.model,name:hr.model_hr_job
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_id
#: model:ir.model.fields,field_description:hr.field_hr_job__name
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Job Position"
msgstr "Functie"

#. module: hr
#: model:ir.actions.act_window,name:hr.action_hr_job
#: model:ir.ui.menu,name:hr.menu_view_hr_job
msgid "Job Positions"
msgstr "Functies"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_title
#: model:ir.model.fields,field_description:hr.field_res_users__job_title
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
msgid "Job Title"
msgstr "Functietitel"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__jobs_ids
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Jobs"
msgstr "Functies"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__lang
msgid "Lang"
msgstr "Lang"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Language"
msgstr "Taal"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity
msgid "Last Activity"
msgstr "Laatste activiteit"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity_time
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity_time
msgid "Last Activity Time"
msgstr "Laatste activiteit"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department____last_update
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason____last_update
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard____last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee____last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee_category____last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee_public____last_update
#: model:ir.model.fields,field_description:hr.field_hr_job____last_update
#: model:ir.model.fields,field_description:hr.field_hr_plan____last_update
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type____last_update
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard____last_update
#: model:ir.model.fields,field_description:hr.field_hr_work_location____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_date
#: model:ir.model.fields,field_description:hr.field_hr_job__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Late Activities"
msgstr "Te late activiteiten"

#. module: hr
#: model:ir.actions.act_window,name:hr.plan_wizard_action
#: model_terms:ir.ui.view,arch_db:hr.plan_wizard
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Launch Plan"
msgstr "Lanceer plan"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Lead the entire sales cycle"
msgstr "Leid de volledige verkoopcyclus."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__cohabitant
msgid "Legal Cohabitant"
msgstr "Samenwonend"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Let's create a job position."
msgstr "Laten we een functie aanmaken."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Location"
msgstr "Locatie"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_number
msgid "Location Number"
msgstr "Locatienummer"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_main_attachment_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_main_attachment_id
#: model:ir.model.fields,field_description:hr.field_hr_job__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hoofdbijlage"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__male
msgid "Male"
msgstr "Man"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__manager_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__parent_id
#: model:ir.model.fields,field_description:hr.field_res_users__employee_parent_id
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__manager
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Manager"
msgstr "Manager"

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "Manager of employee %s is not set."
msgstr "Manager van werknemer %s is niet ingesteld."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__marital
#: model:ir.model.fields,field_description:hr.field_res_users__marital
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Marital Status"
msgstr "Burgerlijke staat"

#. module: hr
#: model:hr.job,name:hr.job_marketing
msgid "Marketing and Community Manager"
msgstr "Marketing en communicatie manager"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__married
msgid "Married"
msgstr "Gehuwd"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__master
msgid "Master"
msgstr "Master"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Master demos of our software"
msgstr "Beheers demo's van je software"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__member_ids
msgid "Members"
msgstr "Leden"

#. module: hr
#: model:ir.model,name:hr.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Must Have"
msgstr "Vereist"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mijn activiteit deadline"

#. module: hr
#. openerp-web
#: code:addons/hr/static/src/xml/hr_templates.xml:0
#, python-format
msgid "My Profile"
msgstr "Mijn profiel"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__name
#: model:ir.model.fields,field_description:hr.field_hr_plan__name
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__responsible_id
msgid "Name"
msgstr "Naam"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_id
msgid "Nationality (Country)"
msgstr "Nationaliteit (Land)"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Negotiate and contract"
msgstr "Onderhandel en contracteer."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Volgende activiteitenafspraak"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Volgende activiteit deadline"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_summary
msgid "Next Activity Summary"
msgstr "Omschrijving volgende actie"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_id
msgid "Next Activity Type"
msgstr "Volgende activiteit type"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Nice to have"
msgstr "Leuk om te hebben"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Geen domme managers, geen domme tools om te gebruiken, geen rigide "
"werktijden."

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "No specific user given on activity %s."
msgstr "Geen specifieke gebruiker opgegeven bij activiteit %s."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"Geen tijdverspilling in bedrijfsprocessen, echte verantwoordelijkheden en "
"autonomie."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_job__state__open
msgid "Not Recruiting"
msgstr "Niet aan het werven"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Not available"
msgstr "Niet beschikbaar"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__note
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__note
msgid "Note"
msgstr "Notitie"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__notes
msgid "Notes"
msgstr "Notities"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__children
#: model:ir.model.fields,field_description:hr.field_res_users__children
msgid "Number of Children"
msgstr "Aantal kinderen"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_employee
msgid "Number of employees currently occupying this job position."
msgstr "Huidig aantal werknemers welke deze functie bekleden."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_hired_employee
msgid ""
"Number of hired employees for this job position during recruitment phase."
msgstr ""
"Aantal aan te nemen werknemers voor deze functie tijdens de recruitment "
"fase."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Aantal berichten die actie vereisen"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_recruitment
msgid "Number of new employees you expect to recruit."
msgstr "Verwachte aantal te werven nieuwe werknemers."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_unread_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_unread_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_unread_counter
msgid "Number of unread messages"
msgstr "Aantal ongelezen berichten"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                    related to employees by departments: expenses, timesheets,\n"
"                    leaves, recruitments, etc."
msgstr ""
"Odoo's afdelingsstructuur wordt gebruikt om alle documenten te beheren\n"
"                    gerelateerd aan werknemers per afdeling: declaraties,  urenstaten,\n"
"                    verlof, werking en selectie, etc.          "

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                related to employees by departments: expenses, timesheets,\n"
"                time off, recruitments, etc."
msgstr ""
"Odoo's afdelingsstructuur wordt gebruikt om alle documenten te beheren\n"
"                 gerelateerd aan werknemers per afdeling: declaraties, urenstaten,\n"
"                 verlof, rekruteringen, etc. "

#. module: hr
#: model:res.groups,name:hr.group_hr_user
msgid "Officer"
msgstr "Functionaris"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__other
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__other
msgid "Other"
msgstr "Overige"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Our Product"
msgstr "Ons product"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__pin
#: model:ir.model.fields,field_description:hr.field_res_users__pin
msgid "PIN"
msgstr "PIN"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "PIN Code"
msgstr "PIN code"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__pin
#: model:ir.model.fields,help:hr.field_res_users__pin
msgid ""
"PIN used to Check In/Out in the Kiosk Mode of the Attendance application (if"
" enabled in Configuration) and to change the cashier in the Point of Sale "
"application."
msgstr ""
"Pincode die wordt gebruikt om in/uit te checken in de Kiosk-modus van de "
"Aanwezigheidstoepassing (indien ingeschakeld in Configuratie) en om de "
"caissière te wijzigen in de Kassa-toepassing."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_id
msgid "Parent Department"
msgstr "Hoofdafdeling"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__user_partner_id
msgid "Partner-related data of the user"
msgstr "Relatie gerelateerde gegevens van de gebruiker"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Passion for software products"
msgstr "Passie voor software producten"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__passport_id
#: model:ir.model.fields,field_description:hr.field_res_users__passport_id
msgid "Passport No"
msgstr "Paspoortnummer"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Payroll"
msgstr "Loonadministratie"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perfect written English"
msgstr "Perfect geschreven Engels"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perks"
msgstr "Voordelen"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Personal Evolution"
msgstr "Persoonlijke evolutie"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Phone"
msgstr "Telefoon"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__place_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__place_of_birth
msgid "Place of Birth"
msgstr "Geboorteplaats"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__plan_id
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_search
msgid "Plan"
msgstr "Plan"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
msgid "Plan Name"
msgstr "Naam van plan"

#. module: hr
#: model:ir.model,name:hr.model_hr_plan_wizard
msgid "Plan Wizard"
msgstr "Plan wizard"

#. module: hr
#: model:ir.model,name:hr.model_hr_plan_activity_type
msgid "Plan activity type"
msgstr "Plan activiteitssoort"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_plan_action
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_tree
msgid "Planning"
msgstr "Planning"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_plan_activity_type_action
#: model:ir.ui.menu,name:hr.menu_config_plan_types
msgid "Planning Types"
msgstr "Planningssoorten"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_plan_plan
msgid "Plans"
msgstr "Plannen"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr "Samen met collega's sporten, <br>de rekening is gedekt."

#. module: hr
#: model:ir.model.fields,help:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,help:hr.field_mail_channel__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Beleid om een bericht te versturen bij een document door gebruik te maken van de mail gateway.\n"
"-iedereen: iedereen mag versturen\n"
"-relaties: alleen geautoriseerde relaties\n"
"-volgers: allen volgers van een bijbehorend document of volgers van gevolgde kanalen\n"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence of employees"
msgstr "Aanwezigheid van werknemers"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence reporting screen, email and IP address control."
msgstr "Aanwezigheidsrapportagescherm, e-mail- en IP-adrescontrole."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__present
msgid "Present"
msgstr "Huidig"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent_active
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent_active
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent_active
msgid "Present but not active"
msgstr "Aanwezig maar niet actief"

#. module: hr
#: model:ir.actions.report,name:hr.hr_employee_print_badge
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Print Badge"
msgstr "Badge afdrukken"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Private Address"
msgstr "Privé adres"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_city
msgid "Private City"
msgstr "Privé plaats"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Contact"
msgstr "Privé contact"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_country_id
msgid "Private Country"
msgstr "Privéland"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_email
#: model:ir.model.fields,field_description:hr.field_res_users__private_email
msgid "Private Email"
msgstr "Privé e-mail"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Information"
msgstr "Privé informatie"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__phone
#: model:ir.model.fields,field_description:hr.field_res_users__employee_phone
msgid "Private Phone"
msgstr "Privé telefoon"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_state_id
msgid "Private State"
msgstr "Privé provincie/staat staat"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_street
msgid "Private Street"
msgstr "Privéstraat"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_street2
msgid "Private Street2"
msgstr "Privéstraat2"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_zip
msgid "Private Zip"
msgstr "Privé postcode"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_public
msgid "Public Employee"
msgstr "Openbare werknemer"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Qualify the customer needs"
msgstr "Kwalificeer de behoeften van de klant"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Ready to recruit more efficiently?"
msgstr "Klaar om efficiënter re recruiten?"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr ""
"Echte verantwoordelijkheden en uitdagingen in een snel groeiend bedrijf"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__name
msgid "Reason"
msgstr "Reden"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Recruitment"
msgstr "Werving & Selectie"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_job__state__recruit
msgid "Recruitment in Progress"
msgstr "Werving in behandeling"

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.actions.act_window,name:hr.hr_departure_wizard_action
#, python-format
msgid "Register Departure"
msgstr "Registreer vertrek"

#. module: hr
#: code:addons/hr/models/res_partner.py:0
#, python-format
msgid "Related Employees"
msgstr "Gerelateerde werknemers"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Related User"
msgstr "Gekoppelde gebruiker"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_ids
msgid "Related employee"
msgstr "Gerelateerde werknemer"

#. module: hr
#: model:ir.model.fields,help:hr.field_res_partner__employee_ids
msgid "Related employees based on their private address"
msgstr "Gerelateerde werknemers op basis van hun privéadres"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_id
#: model:ir.model.fields,help:hr.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr ""
"Gekoppelde gebruikersnaam voor de resource om zijn toegang te beheren."

#. module: hr
#: model:ir.ui.menu,name:hr.hr_menu_hr_reports
#: model:ir.ui.menu,name:hr.menu_hr_reporting_timesheet
msgid "Reporting"
msgstr "Rapportages"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__requirements
msgid "Requirements"
msgstr "Vereisten"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_resigned
msgid "Resigned"
msgstr "Ontslag genomen"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_id
msgid "Resource"
msgstr "Resource"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_calendar_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_calendar_id
msgid "Resource Calendar"
msgstr "Resource agenda"

#. module: hr
#: model:ir.model,name:hr.model_resource_resource
msgid "Resources"
msgstr "Resources"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Responsibilities"
msgstr "Verantwoordelijkheden"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__responsible
msgid "Responsible"
msgstr "Verantwoordelijke"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_user_id
msgid "Responsible User"
msgstr "Verantwoordelijke gebruiker"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_retired
msgid "Retired"
msgstr "Met pensioen"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__sinid
msgid "SIN No"
msgstr "Verzekeringsnummer"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__ssnid
msgid "SSN No"
msgstr "BSN nummer"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Schedule"
msgstr "Planning"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_school
#: model:ir.model.fields,field_description:hr.field_res_users__study_school
msgid "School"
msgstr "School"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,help:hr.field_res_users__coach_id
msgid ""
"Select the \"Employee\" who is the coach of this employee.\n"
"The \"Coach\" has no specific rights or responsibilities by default."
msgstr ""
"Selecteer de 'werknemer' die de coach is van deze werknemer.\n"
"De 'coach' heeft standaard geen specifieke rechten of verantwoordelijkheden."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__sequence
#: model:ir.model.fields,field_description:hr.field_hr_job__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Set default company schedule to manage your employees working time"
msgstr ""
"Stel standaard bedrijfsschema in om je werknemers hun werktijd te beheren"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__state
msgid ""
"Set whether the recruitment process is open or closed for this job position."
msgstr "Stel in of het wervingsproces voor deze vacature open of gesloten is."

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_config_settings_action
#: model:ir.ui.menu,name:hr.hr_menu_configuration
msgid "Settings"
msgstr "Instellingen"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__single
msgid "Single"
msgstr "Ongehuwd"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_skills
msgid "Skills Management"
msgstr "Vaardighedenbeheer"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__sinid
msgid "Social Insurance Number"
msgstr "BSN nummer"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__ssnid
msgid "Social Security Number"
msgstr "BSN nummer"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_plan_activity_type__responsible_id
msgid "Specific responsible of activity if not linked to the employee."
msgstr ""
"Specifieke verantwoordelijke van activiteit indien niet gekoppeld aan de "
"werknemer."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Sport Activity"
msgstr "Sport activiteit"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_birthdate
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_birthdate
msgid "Spouse Birthdate"
msgstr "Geboortedatum partner:"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_complete_name
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_complete_name
msgid "Spouse Complete Name"
msgstr "Volledige naam partner:"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Start Recruitment"
msgstr "Start werving"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "State"
msgstr "Status"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__state
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Status"
msgstr "Status"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status gebaseerd op activiteiten\n"
"Te laat: Datum is al gepasseerd\n"
"Vandaag: Activiteit datum is vandaag\n"
"Gepland: Toekomstige activiteiten."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Stop Recruitment"
msgstr "Werving en selectie stoppen"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Street 2..."
msgstr "Straat 2..."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Street..."
msgstr "Straat..."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Strong analytical skills"
msgstr "Sterke analytische skills"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__student
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__student
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__student
msgid "Student"
msgstr "Student"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__summary
msgid "Summary"
msgstr "Samenvatting"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__name
msgid "Tag Name"
msgstr "Labelnaam"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_category_name_uniq
msgid "Tag name already exists !"
msgstr "Labelnaam bestaat al!"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__category_ids
#: model:ir.ui.menu,name:hr.menu_view_employee_category_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Tags"
msgstr "Labels"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Technical Expertise"
msgstr "Technische expertise"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_barcode_uniq
msgid ""
"The Badge ID must be unique, this one is already assigned to another "
"employee."
msgstr ""
"De badge ID moet uniek zijn, deze is al toegewezen aan de andere werknemer."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"De ISO-landcode in twee letters.\n"
"Je kunt dit veld gebruiken voor snelzoeken."

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "The PIN must be a sequence of digits."
msgstr "De PIN moet een reeks van cijfers zijn."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_address_home_a_company
#: model:ir.model.fields,field_description:hr.field_res_users__is_address_home_a_company
msgid "The employee address has a company linked"
msgstr "Het adres van de werknemer is gekoppeld aan een bedrijfsadres"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__employee_type
#: model:ir.model.fields,help:hr.field_hr_employee_base__employee_type
#: model:ir.model.fields,help:hr.field_hr_employee_public__employee_type
#: model:ir.model.fields,help:hr.field_res_users__employee_type
msgid ""
"The employee type. Although the primary purpose may seem to categorize "
"employees, this field has also an impact in the Contract History. Only "
"Employee type is supposed to be under contract and will have a Contract "
"History."
msgstr ""
"Het werknemerstype. Hoewel het primaire doel het categoriseren van "
"werknemers lijkt te zijn, heeft dit veld ook invloed op de "
"Contractgeschiedenis. Alleen het type werknemer hoort onder contract te "
"staan en heeft een contractgeschiedenis."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_no_of_recruitment_positive
msgid "The expected number of new employees must be positive."
msgstr "Het verwachte aantal nieuwe werknemers moet positief zijn."

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"The fields \"%s\" you try to read is not available on the public employee "
"profile."
msgstr ""
"Het veld \"%s\" dat je probeert te lezen is niet beschikbaar op het openbare"
" profiel van de werknemer."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_name_company_uniq
msgid "The name of the job position must be unique per department in company!"
msgstr "De naam van de functie moet uniek zijn per afdeling in het bedrijf!"

#. module: hr
#: model:res.groups,comment:hr.group_hr_user
msgid "The user will be able to approve document created by employees."
msgstr ""
"De gebruiker kan het document goedkeuren dat aangemaakt is door werknemers."

#. module: hr
#: model:res.groups,comment:hr.group_hr_manager
msgid ""
"The user will have access to the human resources configuration as well as "
"statistic reports."
msgstr ""
"De gebruiker heeft toegang tot de HR instellingen en de statische "
"rapportages."

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "The work permit of %(employee)s expires at %(date)s."
msgstr "De werkvergunning van %(employee)s verloopt op %(date)s."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__tz
#: model:ir.model.fields,help:hr.field_hr_employee_base__tz
#: model:ir.model.fields,help:hr.field_hr_employee_public__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Dit veld wordt gebruikt in de order om te definiëren in welke tijdzone de "
"resource zal werken."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__tz
msgid "Timezone"
msgstr "Tijdzone"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__to_define
msgid "To Define"
msgstr "Te definiëren"

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"To avoid multi company issues (loosing the access to your previous "
"contracts, leaves, ...), you should create another employee in the new "
"company instead."
msgstr ""
"Om problemen met meerdere bedrijven te vermijden (de toegang verliezen tot "
"je vorige contracten, verlof, ...), moet je in plaats daarvan een andere "
"werknemer in het nieuwe bedrijf creëren."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_to_define
msgid "To define"
msgstr "Te bepalen"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Today Activities"
msgstr "Activiteiten van vandaag"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__total_employee
msgid "Total Employee"
msgstr "Totaal voor deze werknemer"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__expected_employees
msgid "Total Forecasted Employees"
msgstr "Totaal aantal verwachte werknemers"

#. module: hr
#: model:hr.job,name:hr.job_trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__trainee
msgid "Trainee"
msgstr "Stagiair"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Trainings"
msgstr "Trainings"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type van activiteit uitzondering op record."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_undetermined
msgid "Undetermined"
msgstr "Onbepaald"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_unread
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_unread
#: model:ir.model.fields,field_description:hr.field_hr_job__message_unread
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Unread Messages"
msgstr "Ongelezen berichten"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_unread_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_unread_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Aantal ongelezen berichten"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__user_id
msgid "User"
msgstr "Gebruiker"

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "User linked to employee %s is required."
msgstr "Gebruiker gekoppeld aan werknemer %s is vereist."

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "User of coach of employee %s is not set."
msgstr "Gebruiker van coach van werknemer %s is niet ingesteld."

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "User of manager of employee %s is not set."
msgstr "Gebruiker van manager van werknemer %s is niet ingesteld."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_partner_id
msgid "User's partner"
msgstr "Gebruikers relatie"

#. module: hr
#: model:ir.model,name:hr.model_res_users
msgid "Users"
msgstr "Gebruikers"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "Vacancies :"
msgstr "Vacatures :"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip_list
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip_list
msgid "Valid IP addresses"
msgstr "Geldig IP adres"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Valid work permit for Belgium"
msgstr "Geldig werkvisum voor België"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_expire
#: model:ir.model.fields,field_description:hr.field_res_users__visa_expire
msgid "Visa Expiration Date"
msgstr "Vervaldatum visum"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_no
#: model:ir.model.fields,field_description:hr.field_res_users__visa_no
msgid "Visa No"
msgstr "Visa nr."

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "Warning"
msgstr "Waarschuwing"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What We Offer"
msgstr "Wat wij aanbieden"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What's great in the job?"
msgstr "Wat is geweldig aan de baan?"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__widower
msgid "Widower"
msgstr "Weduwe/Weduwnaar"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""
"Met slechts een snelle blik op het Odoo werknemer scherm kun je\n"
"gemakkelijk alle informatie vinden die je nodig heeft voor elke persoon;\n"
"contact data, functie, beschikbaarheid, enz."

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"               can easily find all the information you need for each person;\n"
"               contact data, job position, availability, etc."
msgstr ""
"Met slechts een snelle blik op het Odoo werknemers-scherm kun je\n"
"               gemakkelijk alle informatie vinden die je nodig heeft over elk persoon;\n"
"               contactgegevens, functie, beschikbaarheid, etc."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__address_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__address_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_id
msgid "Work Address"
msgstr "Werkadres"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_email
#: model:ir.model.fields,field_description:hr.field_res_users__work_email
msgid "Work Email"
msgstr "E-mail werk"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Information"
msgstr "Werk informatie"

#. module: hr
#: model:ir.model,name:hr.model_hr_work_location
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_id
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_form_view
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_tree_view
msgid "Work Location"
msgstr "Werklocatie"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_work_location_action
#: model:ir.ui.menu,name:hr.menu_hr_work_location_tree
msgid "Work Locations"
msgstr "Werklocaties"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__mobile_phone
#: model:ir.model.fields,field_description:hr.field_res_users__mobile_phone
msgid "Work Mobile"
msgstr "Mobiel nummer werk"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Work Organization"
msgstr "Werk organisatie"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_work_permit
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr "Werkvergunning"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_expiration_date
msgid "Work Permit Expiration Date"
msgstr "Vervaldatum werkvergunning"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
#: model:ir.model.fields,field_description:hr.field_res_users__permit_no
msgid "Work Permit No"
msgstr "Werkvergunning nr."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_scheduled_activity
msgid "Work Permit Scheduled Activity"
msgstr "Werkvergunning geplande activiteit"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_phone
#: model:ir.model.fields,field_description:hr.field_res_users__work_phone
msgid "Work Phone"
msgstr "Telefoon werk"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_calendar_id
msgid "Working Hours"
msgstr "Werkuren"

#. module: hr
#: code:addons/hr/models/res_users.py:0
#, python-format
msgid ""
"You are only allowed to update your preferences. Please contact a HR officer"
" to update other information."
msgstr ""
"Je mag alleen je voorkeuren aanpassen. Neem contact op met een HR-"
"functionaris om andere informatie bij te werken."

#. module: hr
#. openerp-web
#: code:addons/hr/static/src/models/employee/employee.js:0
#, python-format
msgid "You can only chat with employees that have a dedicated user."
msgstr ""
"Je kunt alleen chatten met werknemers die een gekoppelde gebruiker hebben."

#. module: hr
#: code:addons/hr/models/hr_department.py:0
#, python-format
msgid "You cannot create recursive departments."
msgstr "Je kunt geen recursieve afdelingen maken."

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "You do not have access to this document."
msgstr "Je hebt geen toegang tot dit document."

#. module: hr
#: code:addons/hr/models/res_config_settings.py:0
#, python-format
msgid "You should select at least one Advanced Presence Control option."
msgstr ""
"Je moet minstens één optie voor geavanceerde aanwezigheidscontrole "
"selecteren."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "ZIP"
msgstr "Postcode"

#. module: hr
#: code:addons/hr/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered employees"
msgstr "adressen gekoppeld aan geregistreerde werknemers"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "department"
msgstr "afdeling"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "e.g. John Doe"
msgstr "b.v. John Doe"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
msgid "e.g. Onboarding"
msgstr "bijv. Onboarding"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Sales Manager"
msgstr "Bijv. Verkoopmanager"

#. module: hr
#: model:ir.model,name:hr.model_hr_plan
msgid "plan"
msgstr "plan"

#. module: hr
#: code:addons/hr/models/models.py:0
#, python-format
msgid "restricted to employees"
msgstr "beperkt tot werknemers"
