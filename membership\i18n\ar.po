# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* membership
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_invoiced
msgid "# Invoiced"
msgstr "عدد المفوترين "

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_paid
msgid "# Paid"
msgstr "عدد المدفوعين "

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_waiting
msgid "# Waiting"
msgstr "عدد المنتظرين "

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" "
"title=\"Period\"/><strong> From: </strong>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"فترة "
"\"/><strong> من: </strong>"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Price\" title=\"Price\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Price\" title=\"السعر \"/>"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<strong> To:</strong>"
msgstr "<strong> إلى:</strong>"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__associate_member
#: model:ir.model.fields,help:membership.field_res_users__associate_member
msgid ""
"A member with whom you want to associate your membership.It will consider "
"the membership state of the associated member."
msgstr "عضو تريد ربط عضويتك معه. سينظر في حالة العضوية للعضو المرتبط."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__account_invoice_line
msgid "Account Invoice line"
msgstr "بند فاتورة الحساب"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Add a description..."
msgstr "إضافة وصف..."

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid "Add a new member"
msgstr "إضافة عضو جديد"

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__member_price
msgid "Amount for the membership"
msgstr "مبلغ العضوية "

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__associate_member_id
#: model:ir.model.fields,field_description:membership.field_res_partner__associate_member
#: model:ir.model.fields,field_description:membership.field_res_users__associate_member
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Associate Member"
msgstr "ربط العضو"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Associated Partner"
msgstr "شريك مرتبط "

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Buy Membership"
msgstr "شراء عضوية"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Cancel"
msgstr "إلغاء "

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_cancel
#: model:ir.model.fields,field_description:membership.field_res_users__membership_cancel
msgid "Cancel Membership Date"
msgstr "تاريخ إلغاء العضوية"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_cancel
msgid "Cancel date"
msgstr "تاريخ الإلغاء"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__canceled
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__canceled
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__canceled
msgid "Cancelled Member"
msgstr "عضو ملغي"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Category"
msgstr "فئة"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership
#: model:ir.model.fields,help:membership.field_product_template__membership
msgid "Check if the product is eligible for membership."
msgstr "حدد هذا المربع إذا كان المنتج يستأهل العضوية."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__company_id
#: model:ir.model.fields,field_description:membership.field_report_membership__company_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Company"
msgstr "الشركة "

#. module: membership
#: model:ir.ui.menu,name:membership.menu_marketing_config_association
msgid "Configuration"
msgstr "التهيئة "

#. module: membership
#: model:ir.model,name:membership.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_tree
msgid "Contacts"
msgstr "جهات الاتصال"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__create_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__create_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__membership_state
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Current Membership State"
msgstr "حالة العضوية الحالية"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_state
#: model:ir.model.fields,field_description:membership.field_res_users__membership_state
msgid "Current Membership Status"
msgstr "حالة العضوية الحالية"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Customers"
msgstr "العملاء"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership_date_from
#: model:ir.model.fields,help:membership.field_product_template__membership_date_from
#: model:ir.model.fields,help:membership.field_res_partner__membership_start
#: model:ir.model.fields,help:membership.field_res_users__membership_start
msgid "Date from which membership becomes active."
msgstr "تاريخ تنشيط العضوية."

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__date
msgid "Date on which member has joined the membership"
msgstr "تاريخ تسجيل العضو لعضويته"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_cancel
#: model:ir.model.fields,help:membership.field_res_users__membership_cancel
msgid "Date on which membership has been cancelled"
msgstr "تاريخ إلغاء العضوية"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership_date_to
#: model:ir.model.fields,help:membership.field_product_template__membership_date_to
#: model:ir.model.fields,help:membership.field_res_partner__membership_stop
#: model:ir.model.fields,help:membership.field_res_users__membership_stop
msgid "Date until which membership remains active."
msgstr "تاريخ انتهاء فعالية العضوية."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__display_name
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__display_name
#: model:ir.model.fields,field_description:membership.field_report_membership__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__tot_earned
msgid "Earned Amount"
msgstr "قيمة الربح"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__date_to
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Membership Date"
msgstr "تاريخ انتهاء العضوية"

#. module: membership
#: model:ir.model.fields,help:membership.field_report_membership__date_to
msgid "End membership date"
msgstr "تاريخ انتهاء العضوية"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Ending Date Of Membership"
msgstr "تاريخ انتهاء العضوية"

#. module: membership
#: model:ir.model.constraint,message:membership.constraint_product_template_membership_date_greater
msgid "Error ! Ending Date cannot be set before Beginning Date."
msgstr "خطأ! لا يمكن لتاريخ الانتهاء أن يسبق تاريخ البدء."

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Forecast"
msgstr "التوقع"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__free_member
#: model:ir.model.fields,field_description:membership.field_res_users__free_member
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__free
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__free
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__free
msgid "Free Member"
msgstr "عضو مجاني"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_from
msgid "From"
msgstr "من"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Group By"
msgstr "التجميع حسب "

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Group by..."
msgstr "التجميع حسب... "

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__id
#: model:ir.model.fields,field_description:membership.field_report_membership__id
msgid "ID"
msgstr "المُعرف"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Inactive"
msgstr "غير نشط"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__account_invoice_id
msgid "Invoice"
msgstr "فاتورة"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Invoice Membership"
msgstr "فوترة العضوية "

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__invoiced
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__invoiced
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__invoiced
msgid "Invoiced Member"
msgstr "عضو مفوتر"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Invoiced/Paid/Free"
msgstr "الأعضاء المفوترين/الدافعين/المجانيين"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_state
#: model:ir.model.fields,help:membership.field_res_users__membership_state
msgid ""
"It indicates the membership state.\n"
"-Non Member: A partner who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paying member: A member who has paid the membership fee."
msgstr ""
"يشير إلى حالة العضوية.\n"
"- غير عضو: شريك لم يتقدم بطلب عضوية.\n"
"- عضو ملغي: عضو ألغى عضويته.\n"
"- عضو قديم: عضو انتهت فترة عضويته.\n"
"- عضو منتظر: عضو تقدم بطلب عضوية وفاتورته قيد الإنشاء.\n"
"- عضو مفوتر: عضو تم إنشاء فاتورته.\n"
"- عضو دافع: عضو قام بسداد رسوم العضوية."

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__state
msgid ""
"It indicates the membership status.\n"
"-Non Member: A member who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paid Member: A member who has paid the membership amount."
msgstr ""
"يشير إلى حالة العضوية.\n"
"- غير عضو: شريك لم يتقدم بطلب عضوية.\n"
"- عضو ملغي: عضو ألغى عضويته.\n"
"- عضو قديم: عضو انتهت فترة عضويته.\n"
"- عضو منتظر: عضو تقدم بطلب عضوية وفاتورته قيد الإنشاء.\n"
"- عضو مفوتر: عضو تم إنشاء فاتورته.\n"
"- عضو دافع: عضو قام بسداد رسوم العضوية."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date
msgid "Join Date"
msgstr "تاريخ الانضمام"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_invoice_view
msgid "Join Membership"
msgstr "الانضمام إلى العضوية "

#. module: membership
#: model:ir.model,name:membership.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: membership
#: model:ir.model,name:membership.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice____last_update
#: model:ir.model.fields,field_description:membership.field_membership_membership_line____last_update
#: model:ir.model.fields,field_description:membership.field_report_membership____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__write_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__write_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__partner_id
msgid "Member"
msgstr "العضو"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__member_price
msgid "Member Price"
msgstr "سعر العضو"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_members
#: model:ir.ui.menu,name:membership.menu_association
#: model:ir.ui.menu,name:membership.menu_membership
#: model_terms:ir.ui.view,arch_db:membership.membership_members_tree
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Members"
msgstr "الأعضاء"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_report_membership_tree
msgid "Members Analysis"
msgstr "تحليل الأعضاء"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__product_id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__membership_id
#: model:ir.model.fields,field_description:membership.field_product_product__membership
#: model:ir.model.fields,field_description:membership.field_product_template__membership
#: model:ir.model.fields,field_description:membership.field_res_partner__member_lines
#: model:ir.model.fields,field_description:membership.field_res_users__member_lines
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_graph1
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_pivot
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership"
msgstr "العضوية"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_amount
#: model:ir.model.fields,field_description:membership.field_res_users__membership_amount
msgid "Membership Amount"
msgstr "مبلغ العضوية "

#. module: membership
#: model:ir.model,name:membership.model_report_membership
msgid "Membership Analysis"
msgstr "تحليل العضوية"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Membership Duration"
msgstr "مدة العضوية"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product__membership_date_to
#: model:ir.model.fields,field_description:membership.field_product_template__membership_date_to
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_stop
#: model:ir.model.fields,field_description:membership.field_res_users__membership_stop
msgid "Membership End Date"
msgstr "موعد نهاية العضوية"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__member_price
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership Fee"
msgstr "رسوم العضوية"

#. module: membership
#: model:ir.model,name:membership.model_membership_invoice
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Membership Invoice"
msgstr "فاتورة العضوية"

#. module: membership
#: model:ir.model,name:membership.model_membership_membership_line
msgid "Membership Line"
msgstr "بند العضوية"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership Partners"
msgstr "شركاء العضوية"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__membership_id
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership Product"
msgstr "منتج العضوية"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_products
#: model:ir.ui.menu,name:membership.menu_membership_products
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Membership Products"
msgstr "منتجات العضوية"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product__membership_date_from
#: model:ir.model.fields,field_description:membership.field_product_template__membership_date_from
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_start
#: model:ir.model.fields,field_description:membership.field_res_users__membership_start
msgid "Membership Start Date"
msgstr "تاريخ بداية العضوية"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership State"
msgstr "حالة العضوية"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__state
msgid "Membership Status"
msgstr "حالة العضوية"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership products"
msgstr "منتجات العضوية"

#. module: membership
#: model:ir.actions.server,name:membership.ir_cron_update_membership_ir_actions_server
#: model:ir.cron,cron_name:membership.ir_cron_update_membership
#: model:ir.cron,name:membership.ir_cron_update_membership
msgid "Membership: update memberships"
msgstr "العضوية: تحديث العضويات"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Memberships"
msgstr "العضويات"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Month"
msgstr "الشهر"

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_report_membership_tree
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__none
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__none
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__none
msgid "Non Member"
msgstr "غير عضو"

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid ""
"Odoo helps you easily track all activities related to a member: \n"
"                  Current Membership Status, Discussions and History of Membership, etc."
msgstr ""
"يساعدك أودو على تتبع كافة أنشطة الأعضاء بكل سهولة:\n"
"                  حالة العضوية الحالية، مناقشات وسجلات العضوية، إلخ."

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__old
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__old
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__old
msgid "Old Member"
msgstr "عضو قديم"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__paid
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__paid
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__paid
msgid "Paid Member"
msgstr "عضو دافع"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__partner
msgid "Partner"
msgstr "الشريك"

#. module: membership
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "Partner doesn't have an address to make the invoice."
msgstr "لا يملك الشريك عنواناً لإصدار الفاتورة. "

#. module: membership
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "Partner is a free Member."
msgstr "الشريك عضو مجاني."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__tot_pending
msgid "Pending Amount"
msgstr "مبلغ معلق"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Product Name"
msgstr "اسم المنتج"

#. module: membership
#: model:ir.model,name:membership.model_product_template
msgid "Product Template"
msgstr "قالب المنتج"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__quantity
msgid "Quantity"
msgstr "الكمية"

#. module: membership
#: model:ir.ui.menu,name:membership.menu_report_membership
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Revenue Done"
msgstr "الإيرادات المحققة "

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__user_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Salesperson"
msgstr "مندوب المبيعات "

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__free_member
#: model:ir.model.fields,help:membership.field_res_users__free_member
msgid "Select if you want to give free membership."
msgstr "اختر هذا الخيار إن كنت تريد منح عضويات مجانية."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__start_date
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Starting Date Of Membership"
msgstr "تاريخ بدء العضوية"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of # Invoiced"
msgstr "إجمالي عدد العضويات المفوترة "

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of # Paid"
msgstr "إجمالي عدد العضويات المدفوعة "

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of Earned Amount"
msgstr "إجمالي الإيرادات المكتسبة "

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of Quantity"
msgstr "إجمالي الكمية "

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Taxes"
msgstr "الضرائب"

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__account_invoice_id
msgid "The move of this entry line."
msgstr "حركة بند القيد هذا."

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_amount
#: model:ir.model.fields,help:membership.field_res_users__membership_amount
msgid "The price negotiated by the partner"
msgstr "السعر بعد تفاوض الشريك"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "This note will be displayed on quotations..."
msgstr "ستُعرض هذه الملاحظة على عروض الأسعار..."

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display paid, old and total earned columns"
msgstr "سيعرض هذا أعمدة الأعضاء الدافعين، والأعضاء القدامى، وإجمالي الربح "

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display waiting, invoiced and total pending columns"
msgstr "سيعرض هذا أعمدة الأعضاء المنتظرين، والمفوترين، وإجمالي القيمة المعلقة"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_to
msgid "To"
msgstr "إلى"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Vendors"
msgstr "الموردين "

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__waiting
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__waiting
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__waiting
msgid "Waiting Member"
msgstr "عضو منتظر"

#. module: membership
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "You cannot create recursive associated members."
msgstr "لا يمكنك إنشاء أعضاء مقترنين متداخلين. "
