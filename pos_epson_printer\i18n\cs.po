# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid "Cashdrawer"
msgstr "Pokladní zásuvka"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""
"Zkontrolujte v nastavení tiskárny nastavení 'ID zařízení'. Mělo by být "
"nastaveno: "

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Připojení k tiskárně selhalo"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "IP tiskárny Epson"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "IP adresa tiskárny Epson na tisk stvrzenek"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s"
msgstr ""
"Pokud jste na zabezpečeném serveru (HTTPS), ujistěte se prosím, že jste "
"certifikát přijali přístupem %s"

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Místní IP adresa tiskárny Epson na tisk stvrzenek."

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "No paper was detected by the printer"
msgstr "Tiskárnou nebyl detekován žádný papír"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer has enough paper and is ready to print."
msgstr ""
"Zkontrolujte prosím, zda má tiskárna dostatek papíru a je připravena k "
"tisku."

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr "Zkontrolujte, zda je tiskárna stále připojená."

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Nastavení prodejního místa"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Printing failed"
msgstr "Tisk se nezdařil"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""
"Místo tiskárny účtenek připojené k IoT Boxu bude použita tiskárna účtenek "
"Epson."

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The following error code was given by the printer:"
msgstr "Tiskárna vydala následující kód chyby:"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr "Tiskárna byla úspěšně oslovena, ale nemohla tisknout."

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "To find more details on the error reason, please search online for:"
msgstr "Chcete-li zjistit více podrobností o důvodu chyby, hledejte online:"
