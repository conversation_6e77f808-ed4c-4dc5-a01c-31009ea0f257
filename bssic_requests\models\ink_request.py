from odoo import models, fields, api, _
from odoo.exceptions import UserError


class InkRequest(models.Model):
    """Ink Request Model - now inherits from ink base"""
    _name = 'bssic.ink.request'
    _description = 'Ink Request'
    _inherit = 'bssic.ink.base'

    @api.model
    def create(self, vals):
        """Override create to set sequence number"""
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('bssic.ink.request') or _('New')
        return super(InkRequest, self).create(vals)

    def action_submit(self):
        """Submit ink request for approval"""
        # Check if there are any ink items selected
        if not self.printer_ink_line_ids and not self.copier_ink_line_ids:
            raise UserError(_('You cannot submit an empty request. Please select at least one ink item.'))

        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.ink.request', self.id, 'submitted',
                notes=_('Ink request submitted for approval'),
                old_state='draft', new_state='direct_manager'
            )

        # Record submission information
        self.submission_user_id = self.env.user.id
        self.submission_date = fields.Datetime.now()

        # First set state to submitted
        self.state = 'submitted'
        # Then move to direct manager approval
        self.state = 'direct_manager'

        # Notify direct manager
        direct_manager = self.employee_id.parent_id
        if direct_manager and direct_manager.user_id:
            self.message_subscribe(partner_ids=[direct_manager.user_id.partner_id.id])
            self.message_post(
                body=_('A new ink request has been submitted for your approval.'),
                partner_ids=[direct_manager.user_id.partner_id.id]
            )

    def action_approve_direct_manager(self):
        """Approve request by direct manager"""
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.ink.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Direct Manager'),
                old_state='direct_manager', new_state='warehouse_approval'
            )

        # Record approval information
        self.direct_manager_approval_user_id = self.env.user.id
        self.direct_manager_approval_date = fields.Datetime.now()

        # Update state to warehouse approval
        self.state = 'warehouse_approval'

        # Find Warehouse manager and notify
        warehouse_group = self.env.ref('bssic_requests.group_bssic_warehouse_manager')
        warehouse_users = warehouse_group.users
        partner_ids = warehouse_users.mapped('partner_id.id')
        if partner_ids:
            self.message_subscribe(partner_ids=partner_ids)
            self.message_post(
                body=_('This ink request has been approved by the direct manager and requires warehouse verification.'),
                partner_ids=partner_ids
            )

    def action_approve_warehouse(self):
        # Check if all items have stock quantities specified
        for line in self.printer_ink_line_ids:
            if line.stock_quantity < 0:
                raise UserError(_('You must specify the stock quantity for all printer ink items before approving the request. Item "%s" has no stock quantity.') % line.ink_number)
        
        for line in self.copier_ink_line_ids:
            if line.stock_quantity < 0:
                raise UserError(_('You must specify the stock quantity for all copier ink items before approving the request. Item "%s" has no stock quantity.') % line.ink_number)

        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.ink.request', self.id, 'warehouse_approved',
                notes=_('Approved by Warehouse Manager'),
                old_state='warehouse_approval', new_state='hr_approval'
            )

        # Record approval information
        self.warehouse_approval_user_id = self.env.user.id
        self.warehouse_approval_date = fields.Datetime.now()

        # Update state to HR approval
        self.state = 'hr_approval'

        # Find HR manager and notify
        hr_group = self.env.ref('bssic_requests.group_bssic_hr_manager')
        hr_users = hr_group.users
        partner_ids = hr_users.mapped('partner_id.id')
        if partner_ids:
            self.message_subscribe(partner_ids=partner_ids)
            self.message_post(
                body=_('This ink request has been verified by the warehouse manager and requires your approval.'),
                partner_ids=partner_ids
            )

    def action_approve_hr(self):
        """Approve request by HR manager"""
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.ink.request', self.id, 'hr_approved',
                notes=_('Approved by HR Manager'),
                old_state='hr_approval', new_state='pending_receipt'
            )

        # Record approval information
        self.hr_approval_user_id = self.env.user.id
        self.hr_approval_date = fields.Datetime.now()

        # Update state to pending receipt confirmation
        self.state = 'pending_receipt'

        # Notify employee to confirm receipt
        if self.employee_id.user_id:
            self.message_subscribe(partner_ids=[self.employee_id.user_id.partner_id.id])
            self.message_post(
                body=_('Your ink request has been approved by HR. Please confirm receipt of the items once you receive them.'),
                partner_ids=[self.employee_id.user_id.partner_id.id]
            )

    def action_confirm_receipt_wizard(self):
        """Open the receipt confirmation wizard"""
        return {
            'name': _('Confirm Receipt'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.receipt.confirmation.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_ink_request_id': self.id}
        }

    def action_confirm_receipt(self):
        """Confirm receipt of ink items"""
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.ink.request', self.id, 'receipt_confirmed',
                notes=_('Receipt confirmed by employee. Notes: %s') % (self.receipt_notes or _('No notes')),
                old_state='pending_receipt', new_state='completed'
            )

        # Record receipt confirmation information
        self.receipt_confirmation_user_id = self.env.user.id
        self.receipt_confirmation_date = fields.Datetime.now()

        # Update state to completed
        self.state = 'completed'

        # Notify managers about completion
        managers = []
        if self.direct_manager_id and self.direct_manager_id.user_id:
            managers.append(self.direct_manager_id.user_id.partner_id.id)
        
        warehouse_group = self.env.ref('bssic_requests.group_bssic_warehouse_manager')
        warehouse_users = warehouse_group.users
        managers.extend(warehouse_users.mapped('partner_id.id'))
        
        hr_group = self.env.ref('bssic_requests.group_bssic_hr_manager')
        hr_users = hr_group.users
        managers.extend(hr_users.mapped('partner_id.id'))

        if managers:
            self.message_post(
                body=_('The ink request has been completed. Receipt confirmed by employee.'),
                partner_ids=managers
            )

    def action_reject(self):
        return {
            'name': _('Reject Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.request.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_request_id': self.id, 'default_model': 'bssic.ink.request'}
        }

    # Note: onchange methods are inherited from ink_base.py
