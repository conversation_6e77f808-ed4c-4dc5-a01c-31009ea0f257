<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.project</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="50"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form" />
            <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" data-string="Project" string="Project" data-key="project" groups="project.group_project_manager">
                        <h2>Tasks Management</h2>
                        <div class="row mt16 o_settings_container" id="tasks_management">
                            <div id="use_collaborative_pad" class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="module_pad"/>
                                </div>
                                <div class="o_setting_right_pane" name="pad_project_right_pane">
                                    <label for="module_pad"/>
                                    <div class="text-muted">
                                        Edit tasks' description collaboratively in real time. See each author's text in a distinct color.
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="group_subtask_project"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="group_subtask_project"/>
                                    <div class="text-muted">
                                        Split your tasks to organize your work into sub-milestones
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="recurring_tasks_setting">
                                <div class="o_setting_left_pane">
                                    <field name="group_project_recurring_tasks"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="group_project_recurring_tasks"/>
                                    <div class="text-muted">
                                        Auto-generate tasks for regular activities
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="task_dependencies_setting">
                                <div class="o_setting_left_pane">
                                    <field name="group_project_task_dependencies"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="group_project_task_dependencies"/>
                                    <div class="text-muted">
                                        Determine the order in which to perform tasks
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="project_stages">
                                <div class="o_setting_left_pane">
                                    <field name="group_project_stages"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="group_project_stages"/>
                                    <div class="text-muted">
                                        Track the progress of your projects
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('group_project_stages', '=', False)]}">
                                        <div class="mt8">
                                            <button name="%(project.project_project_stage_configure)d" icon="fa-arrow-right" type="action" string="Configure Stages" class="btn-link"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h2>Time Management</h2>
                        <div class="row mt16 o_settings_container" name="project_time">
                            <div class="col-12 col-lg-6 o_setting_box" name="project_time_management">
                                <div class="o_setting_left_pane">
                                    <field name="module_project_forecast" widget="upgrade_boolean"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="module_project_forecast"/>
                                    <div class="text-muted" name="project_forecast_msg">
                                        Plan resource allocation across projets and tasks, and estimate deadlines more accurately
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="log_time_tasks_setting">
                                <div class="o_setting_left_pane">
                                    <field name="module_hr_timesheet"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="module_hr_timesheet"/>
                                    <div class="text-muted">
                                        Track time spent on projects and tasks
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h2>Analytics</h2>
                        <div class="row mt16 o_settings_container" name="analytic">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="group_analytic_accounting"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="group_analytic_accounting" string="Profitability"/>
                                    <div class="text-muted">
                                        Track the costs and revenues linked to your projects
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="track_customer_satisfaction_setting">
                                <div class="o_setting_left_pane">
                                    <field name="group_project_rating"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="group_project_rating"/>
                                    <div class="text-muted">
                                        Track customer satisfaction on tasks
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('group_project_rating', '=', False)]}">
                                        <div class="mt8">
                                            <button name="%(project.open_task_type_form)d" icon="fa-arrow-right" type="action" string="Set a Rating Email Template on Stages" class="btn-link"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

        <record id="project_config_settings_action" model="ir.actions.act_window">
            <field name="name">Settings</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module' : 'project', 'bin_size': False}</field>
        </record>

        <menuitem id="project_config_settings_menu_action" name="Settings" parent="menu_project_config"
            sequence="0" action="project_config_settings_action" groups="base.group_system"/>
</odoo>
