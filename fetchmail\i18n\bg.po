# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * fetchmail
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Bulgarian (https://www.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "# of emails"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_active
msgid "Active"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced Options"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_configuration
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Configuration"
msgstr ""

#. module: fetchmail
#: selection:fetchmail.server,state:0
msgid "Confirmed"
msgstr ""

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:137
#, python-format
msgid "Connection test failed: %s"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server_is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_object_id
msgid "Create a New Record"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_create_uid
msgid "Created by"
msgstr "Създадено от"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_create_date
msgid "Created on"
msgstr "Създадено на"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server_priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Fetch Now"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server_server
msgid "Hostname or IP of the mail server"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_id
msgid "ID"
msgstr "ID"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "IMAP"
msgstr ""

#. module: fetchmail
#: selection:fetchmail.server,type:0
msgid "IMAP Server"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "If SSL required."
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_mail_mail_fetchmail_server_id
msgid "Inbound Mail Server"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Incoming Mail Server"
msgstr ""

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.action_email_server_tree
#: model:ir.ui.menu,name:fetchmail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_attach
msgid "Keep Attachments"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_original
msgid "Keep Original"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_date
msgid "Last Fetch Date"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server___last_update
msgid "Last Modified on"
msgstr "Последно променено на"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_write_uid
msgid "Last Updated by"
msgstr "Последно обновено от"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_write_date
msgid "Last Updated on"
msgstr "Последно обновено на"

#. module: fetchmail
#: selection:fetchmail.server,type:0
msgid "Local Server"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Login Information"
msgstr ""

#. module: fetchmail
#: model:ir.actions.server,name:fetchmail.ir_cron_mail_gateway_action_ir_actions_server
#: model:ir.cron,cron_name:fetchmail.ir_cron_mail_gateway_action
#: model:ir.cron,name:fetchmail.ir_cron_mail_gateway_action
msgid "Mail: Fetchmail Service"
msgstr ""

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.act_server_history
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_message_ids
msgid "Messages"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_name
msgid "Name"
msgstr ""

#. module: fetchmail
#: selection:fetchmail.server,state:0
msgid "Not Confirmed"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server_action_id
msgid ""
"Optional custom server action to trigger for each incoming mail, on the "
"record that was created or updated by this mail"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr ""

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_mail_mail
msgid "Outgoing Mails"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "POP"
msgstr ""

#. module: fetchmail
#: selection:fetchmail.server,type:0
msgid "POP Server"
msgstr ""

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_fetchmail_server
msgid "POP/IMAP Server"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_password
msgid "Password"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_port
msgid "Port"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server_object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Reset Confirmation"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "SSL"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_is_ssl
msgid "SSL/TLS"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_script
msgid "Script"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server & Login"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_action_id
msgid "Server Action"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server Information"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_server
msgid "Server Name"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_priority
msgid "Server Priority"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_type
msgid "Server Type"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type IMAP."
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type POP."
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_state
msgid "Status"
msgstr "Състояние"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Test & Confirm"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server_user
msgid "Username"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server_original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server_attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
