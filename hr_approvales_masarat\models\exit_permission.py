# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, date, timedelta
from dateutil.relativedelta import relativedelta
from pytz import timezone
from odoo.exceptions import ValidationError


class HrEmployeeExitPermission(models.Model):

    _name = "hr.masarat.exit.permission"

    _inherit = ['mail.thread', 'mail.activity.mixin']



    name = fields.Char(compute='get_name', store=True)

    state = fields.Selection(selection=[('draft', 'Draft'),
                                        ('manager_approval', 'Manager Approval'),
                                        ('manager_refused', 'Manager Refused'),
                                        ('hr_approval', 'HR Approval'),
                                        ('hr_refused', 'HR Refused')], default='draft', string="State")

    request_date = fields.Date(string="Date of Request", readonly=True,
                               default=lambda self: fields.Date.to_string(date.today()))
    check_in_attendacy = fields.Many2one('hr.attendance',
                                         domain="[('employee_id','=',employee_id),('attendance_date','=',request_date)]")

    employee_id = fields.Many2one('hr.employee', string="Employee")
    manager_id = fields.Many2one('hr.employee', readonly=True, related='employee_id.parent_id', string="Manager")

    start_time = fields.Datetime(string='Start Time', required=True)
    start_date = fields.Date(compute='get_start_date',store=True)
    end_time = fields.Datetime(string='End Time', required=True)

    total_leave_minutes=fields.Integer(string='Total Leave Minutes', compute='_get_total_leave_minutes',store=True)
    Note = fields.Text(string="Details and Reasons for Latency")

    is_manager = fields.Char(compute='call_with_sudo_is_manager')
    is_hr_group = fields.Char(compute='call_with_sudo_is_hr_group')

    @api.depends('start_time')
    def get_start_date(self):
        for elem in self:
            elem.start_date = False
            if elem.start_time:
                elem.start_date = str(elem.start_time)[:10]

    def get_if_hr_group(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for rec in self:
            if hr_group:
                rec.is_hr_group = 'yes'
            else:
                rec.is_hr_group = 'no'

    def compute_button_visible(self):
        for rec in self:
            if rec.manager_id.user_id.id == self._uid:
                rec.is_manager = '1'
            else:
                rec.is_manager = '0'

    @api.onchange('employee_id')
    def call_with_sudo_is_manager(self):
        self.sudo().compute_button_visible()

    @api.depends('is_hr_group')
    def call_with_sudo_is_hr_group(self):
        self.sudo().get_if_hr_group()


    @api.depends('employee_id', 'request_date')
    def get_name(self):
        for elem in self:
            elem.name = False
            if elem.employee_id and elem.request_date:
                elem.name = elem.employee_id.name + '-Exit Permission-' + str(elem.request_date)[:10]

    @api.constrains('start_time','end_time')
    def check_times_validations(self):
        for elem in self:
            if elem.start_time and elem.end_time and elem.request_date:
                if str(elem.end_time) <= str(elem.start_time):
                    raise ValidationError('End Time Should be greater than Start Time!')
                if str(elem.end_time.date()) != str(elem.start_time.date()):
                    raise ValidationError('End Time and Start Time should be the same date!')
                deff = (elem.request_date - elem.end_time.date()).days
                if (elem.is_hr_group == 'no') and ((deff > 4 ) or (deff < 0 )):
                    raise ValidationError('Start Time and End Time should be within 4 days  of request date!')

    @api.depends('start_time','end_time')
    def _get_total_leave_minutes(self):
        for elem in self:
            elem.total_leave_minutes=0
            if elem.start_time and elem.end_time:
                elem.total_leave_minutes=int((elem.end_time-elem.start_time).total_seconds())/60

    @api.model
    def default_get(self, fields):
        res = super(HrEmployeeExitPermission, self).default_get(fields)
        user_id = self._context.get('uid')
        employee_id = self.env['hr.employee'].search([('user_id', '=', user_id)])
        res['employee_id'] = employee_id.id
        ## Check For Hr Group
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        if hr_group:
            res['is_hr_group'] = 'yes'
        else:
            res['is_hr_group'] = 'no'
        ########################
        return res

    def make_cancel_approval(self):
        self.state = 'draft'
    def make_manager_approval(self):
        self.state = 'manager_approval'
    def make_manager_refused(self):
        self.state = 'manager_refused'
    def make_hr_approval(self):
        self.state = 'hr_approval'
    def make_hr_refused(self):
        self.state = 'hr_refused'

    @api.depends('employee_id', 'check_in')
    def get_latency_name(self):
        for elem in self:
            elem.latency_name = False
            if elem.employee_id and elem.check_in:
                elem.latency_name = elem.employee_id.name + '-Latency Approval-' + str(elem.check_in)[:10]


    def unlink(self):
        for elem in self:
            if elem.state !='draft':
                raise ValidationError('You cannot delete a Latency request which is not in draft state')
            return super(HrEmployeeExitPermission, self).unlink()

    # def action_send_notification_to_maneger(self):
    #     template_id = self.env.ref('hr_approvales_masarat.exit_permission_approval_template').id
    #     self.env['mail.template'].browse(template_id).send_mail(self.id, force_send=True)
    #
    # @api.model
    # def create(self, vals_list):
    #     obj = super(HrEmployeeExitPermission, self).create(vals_list)
    #     self.sudo().action_send_notification_to_maneger()
    #     return obj

    def action_send_notification_to_maneger(self, employee_id, recode_id):
        employee = self.env['hr.employee'].search([('id', '=', employee_id)])
        email_to = employee.parent_id.work_email
        email_from = employee.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)
        body = """
        <div dir="rtl">
            <p><font style="font-size: 14px;">Your Employee """ + employee.name + """, requested exit permission approval, </font></p>
            <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            <a href="%s">Request Link</a>
        </div>""" % (web_base_url)
        template_id = self.env['mail.mail'].create({
            'subject': 'أذن خروج',
            'email_from': email_from,
            'email_to': email_to,
            'body_html': body})
        template_id.send()

    @api.model
    def create(self, vals_list):
        obj = super(HrEmployeeExitPermission, self).create(vals_list)
        #### check if there is 2 exit permision this month
        get_employee_allawance = obj.sudo().employee_id.contract_id.resource_calendar_id.latency_approval_count
        date_from = str(datetime.strptime(vals_list['start_time'], '%Y-%m-%d %H:%M:%S').date() .replace(day=1))
        date_to =str((datetime.strptime(vals_list['start_time'], '%Y-%m-%d %H:%M:%S') + relativedelta(months=+1, day=1, days=-1)).date())
        search_for_exits = self.env['hr.masarat.exit.permission'].search_count([('employee_id','=',obj.employee_id.id),('start_date','>=',date_from),('start_date','<=',date_to)])
        if search_for_exits > get_employee_allawance+1:
            raise ValidationError(' لقد تجاوزت الحد المسموح به للطلبات الحد الأقصى : '+str(get_employee_allawance))

        # print('get_employee_allawance',get_employee_allawance, 'search_for_exits',search_for_exits, date_from, date_to)


        recode_id = obj.id
        employee_id = obj.employee_id.id
        self.sudo().action_send_notification_to_maneger(employee_id, recode_id)
        return obj
