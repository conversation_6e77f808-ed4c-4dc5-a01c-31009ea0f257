<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.ui.view" id="utm_campaign_view_kanban">
        <field name="name">utm.campaign.view.kanban</field>
        <field name="model">utm.campaign</field>
        <field name="inherit_id" ref="utm.utm_campaign_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='user_id']" position="after">
                <field name="use_leads"/>
            </xpath>
            <xpath expr="//div[@id='utm_statistics']" position="inside">
                <div class="mr-3"
                    groups="sales_team.group_sale_salesman"
                    t-att-title="record.use_leads.raw_value ? 'Leads' : 'Opportunities'">
                    <i class="fa fa-star text-muted"></i>
                    <small class="font-weight-bold"><field name="crm_lead_count"/></small>
                </div>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="utm_campaign_view_form">
        <field name="name">utm.campaign.view.form</field>
        <field name="model">utm.campaign</field>
        <field name="inherit_id" ref="utm.utm_campaign_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_button_box')]" position="inside">
                <button name="action_redirect_to_leads_opportunities"
                    type="object"
                    class="oe_stat_button order-3"
                    icon="fa-star"
                    groups="sales_team.group_sale_salesman">
                    <div class="o_field_widget o_stat_info">
                        <field name="use_leads" invisible="1"/>
                        <span class="o_stat_value"><field nolabel="1" name="crm_lead_count"/></span>
                        <span class="o_stat_text" attrs="{'invisible': [('use_leads', '=', False)]}">Leads</span>
                        <span class="o_stat_text" attrs="{'invisible': [('use_leads', '=', True)]}">Opportunities</span>
                    </div>
                </button>
            </xpath>
        </field>
    </record>
</odoo>
