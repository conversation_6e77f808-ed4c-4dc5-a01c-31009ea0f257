# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_recaptcha
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Generate reCAPTCHA v3 keys"
msgstr ""

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_ir_http
msgid "HTTP Routing"
msgstr "مسیریابی HTTP"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid "If no keys are provided, no checks will be done."
msgstr ""

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid "Minimum score"
msgstr ""

#. module: google_recaptcha
#. openerp-web
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
#, python-format
msgid "No recaptcha site key set."
msgstr ""

#. module: google_recaptcha
#. openerp-web
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
#, python-format
msgid "Privacy Policy"
msgstr ""

#. module: google_recaptcha
#. openerp-web
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
#, python-format
msgid "Protected by reCAPTCHA,"
msgstr ""

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_private_key
msgid "Secret Key"
msgstr ""

#. module: google_recaptcha
#: model:ir.model.fields,help:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid ""
"Should be between 0.0 and 1.0.\n"
"1.0 is very likely a good interaction, 0.0 is very likely a bot"
msgstr ""

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_public_key
msgid "Site Key"
msgstr ""

#. module: google_recaptcha
#. openerp-web
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
#, python-format
msgid "Terms of Service"
msgstr ""

#. module: google_recaptcha
#: code:addons/google_recaptcha/models/ir_http.py:0
#, python-format
msgid "The reCaptcha private key is invalid."
msgstr ""

#. module: google_recaptcha
#: code:addons/google_recaptcha/models/ir_http.py:0
#, python-format
msgid "The reCaptcha token is invalid."
msgstr ""

#. module: google_recaptcha
#. openerp-web
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
#, python-format
msgid "The recaptcha site key is invalid."
msgstr ""

#. module: google_recaptcha
#: code:addons/google_recaptcha/models/ir_http.py:0
#, python-format
msgid "The request is invalid or malformed."
msgstr ""

#. module: google_recaptcha
#: code:addons/google_recaptcha/models/ir_http.py:0
#, python-format
msgid "Your request has timed out, please retry."
msgstr ""

#. module: google_recaptcha
#. openerp-web
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
#, python-format
msgid "apply."
msgstr "اعمال."
