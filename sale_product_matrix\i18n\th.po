# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_matrix
# 
# Translators:
# <PERSON><PERSON>ano<PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_template__product_add_mode
msgid "Add product mode"
msgstr "เพิ่มโหมดสินค้า"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,help:sale_product_matrix.field_product_template__product_add_mode
msgid ""
"Configurator: choose attribute values to add the matching         product variant to the order.\n"
"Grid: add several variants at once from the grid of attribute values"
msgstr ""
"ตัวกำหนดค่า: เลือกค่าคุณลักษณะเพื่อเพิ่มตัวเลือกสินค้าที่ตรงกันในคำสั่ง\n"
"ตาราง: เพิ่มสินค้าที่แตกต่างกันจากตารางค่าคุณลักษณะ"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "เทมเพลตตารางสินค้า"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_update
msgid "Grid Update"
msgstr "อัปเดตตาราง"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__report_grids
msgid ""
"If set, the matrix of the products configurable by matrix will be shown on "
"the report of the order."
msgstr ""
"หากตั้งค่าไว้ "
"เมทริกซ์ของสินค้าที่กำหนดค่าได้ด้วยเมทริกซ์จะแสดงในรายงานของคำสั่ง"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid
msgid "Matrix local storage"
msgstr "เมทริกซ์จัดเก็บข้อมูลท้องถิ่น"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__matrix
msgid "Order Grid Entry"
msgstr "ตารางรายการคำสั่ง"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__report_grids
msgid "Print Variant Grids"
msgstr "พิมพ์ตารางความแตกต่าง"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__configurator
msgid "Product Configurator"
msgstr "การกำหนดค่าสินค้า"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_product_template
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_sale_order
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: sale_product_matrix
#: model_terms:ir.ui.view,arch_db:sale_product_matrix.product_template_grid_view_form
msgid "Sales Variant Selection"
msgstr "การเลือกรูปแบบการขาย"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr "ฟิลด์เทคนิคสำหรับ product_matrix functionalities"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid
msgid ""
"Technical local storage of grid. \n"
"If grid_update, will be loaded on the SO. \n"
"If not, represents the matrix to open."
msgstr ""
"การจัดเก็บท้องถิ่นทางเทคนิคของตาราง\n"
"ถ้า grid_update จะถูกโหลดบน SO\n"
"ถ้าไม่ จะแสดงเมทริกซ์ที่จะเปิด"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr "ช่องตารางมีเมทริกซ์ใหม่ที่จะใช้หรือไม่"

#. module: sale_product_matrix
#: code:addons/sale_product_matrix/models/sale_order.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple sale lines."
msgstr ""
"คุณไม่สามารถเปลี่ยนแปลงจำนวนของสินค้าที่แสดงอยู่ในไลน์การขายหลายรายการ"
