# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_gmail
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>-Nesselbosch, 2022
# Wil <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-25 14:36+0000\n"
"PO-Revision-Date: 2022-03-03 13:58+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Gmail account"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Verbinden Sie Ihr Gmail-Konto"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-cog\"/>\n"
"                        Edit Settings"
msgstr "Einstellungen ändern"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}\" class=\"badge badge-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}\" class=\"badge badge-success\">\n"
"                        Gmail-Token gültig\n"
"                    </span>"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Gmail Credentials</span>"
msgstr "<span class=\"o_form_label\">Gmail Anmeldedaten</span>"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token
msgid "Access Token"
msgstr "Zugriffstoken"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token_expiration
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token_expiration
msgid "Access Token Expiration Timestamp"
msgstr "Ablaufdatum des Outlook-Zugriffstokens"

#. module: google_gmail
#: code:addons/google_gmail/controllers/main.py:0
#: code:addons/google_gmail/controllers/main.py:0
#, python-format
msgid "An error occur during the authentication process."
msgstr "Während des Authentifizierungsverfahrens ist ein Fehler aufgetreten."

#. module: google_gmail
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
#, python-format
msgid "An error occurred when fetching the access token."
msgstr "Beim Abrufen des Zugriffstokens ist ein Fehler aufgetreten."

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_authorization_code
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_authorization_code
msgid "Authorization Code"
msgstr "Autorisierungscode"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Client ID"
msgstr "Client ID"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Client Secret"
msgstr "Client Secret"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "Gmail"
msgstr "Gmail"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__use_google_gmail_service
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__use_google_gmail_service
msgid "Gmail Authentication"
msgstr "Gmail Authentifizierung"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_identifier
msgid "Gmail Client Id"
msgstr "Gmail-Client-ID"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_secret
msgid "Gmail Client Secret"
msgstr "Gmail-Client-Geheimnis"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_google_gmail_mixin
msgid "Google Gmail Mixin"
msgstr "Google-Gmail-Mixin"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__id
msgid "ID"
msgstr "ID"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_ir_mail_server
msgid "Mail Server"
msgstr "Mailserver"

#. module: google_gmail
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
#, python-format
msgid "Only the administrator can link a Gmail mail server."
msgstr "Nur der Administrator kann einen Gmail-Mailserver verbinden."

#. module: google_gmail
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
#, python-format
msgid "Please configure your Gmail credentials."
msgstr "Bitte konfigurieren Sie Ihre Gmail-Anmeldedaten."

#. module: google_gmail
#: code:addons/google_gmail/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please fill the \"Username\" field with your Gmail username (your email "
"address). This should be the same account as the one used for the Gmail "
"OAuthentication Token."
msgstr ""
"Bitte geben Sie in das Feld „Benutzername“ Ihren Gmail-Benutzernamen (Ihre "
"E-Mail-Adresse) ein. Dies sollte dasselbe Konto sein, das auch für das "
"Gmail-OAuthentication-Token verwendet wird."

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_refresh_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_refresh_token
msgid "Refresh Token"
msgstr "Token aktualisieren"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Send and receive email with your Gmail account."
msgstr "Senden und empfangen Sie E-Mails mit Ihrem Gmail-Konto."

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"Setup your Gmail API credentials in the general settings to link a Gmail "
"account."
msgstr ""
"Richten Sie Ihre Gmail-API-Anmeldedaten in den allgemeinen Einstellungen "
"ein, um ein Gmail-Konto zu verknüpfen."

#. module: google_gmail
#: model:ir.model.fields,help:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,help:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "The URL to generate the authorization code from Google"
msgstr "Die URL, um den Autorisierungscode von Google zu generieren"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "URI"
msgstr "URI"
