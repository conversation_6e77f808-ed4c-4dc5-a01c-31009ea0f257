# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* link_tracker
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>esselbosch, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__absolute_url
msgid "Absolute URL"
msgstr "URL assoluto"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__label
msgid "Button label"
msgstr "Etichetta pulsante"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__campaign_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Campaign"
msgstr "Campagna"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_click_action_statistics
msgid "Click Statistics"
msgstr "Statistiche clic"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_click_ids
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_kanban
msgid "Clicks"
msgstr "Clic"

#. module: link_tracker
#: model:ir.model.constraint,message:link_tracker.constraint_link_tracker_code_code
msgid "Code must be unique."
msgstr "Il codice deve essere univoco."

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_code_ids
msgid "Codes"
msgstr "Codici"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__country_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Country"
msgstr "Nazione"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid "Create a link tracker"
msgstr "Crea un monitoraggio link"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:0
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid "Creating a Link Tracker without URL is not possible"
msgstr "Creare un Link Tracker senza URL non è possibile"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url_host
msgid "Host of the short URL"
msgstr "Host URL breve"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__id
msgid "ID"
msgstr "ID"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__ip
msgid "Internet Protocol"
msgstr "Protocollo internet"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker____last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click____last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__link_id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__link_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Link"
msgstr "Link"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_form
msgid "Link Click"
msgstr "Clic su link"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_graph
msgid "Link Clicks"
msgstr "Clic su link"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action
#: model:ir.model,name:link_tracker.model_link_tracker
#: model:ir.ui.menu,name:link_tracker.link_tracker_menu_main
msgid "Link Tracker"
msgstr "Monitoraggio link"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_click
msgid "Link Tracker Click"
msgstr "Clic monitoraggio link"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_code
msgid "Link Tracker Code"
msgstr "Codice monitoraggio link"

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid ""
"Link Tracker values (URL, campaign, medium and source) must be unique (%s, "
"%s, %s, %s)."
msgstr ""
"I valori del Link Tracker (URL, campagna, mezzo e fonte) devono essere unici"
" (%s, %s , %s , %s)."

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_graph
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Links"
msgstr "Link"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_tree
msgid "Links Clicks"
msgstr "Clic sui link"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Mixin rappresentazione e-mail"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__medium_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Medium"
msgstr "Mezzo"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_click_action_statistics
msgid "No data yet!"
msgstr "Ancora nessun dato."

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__count
msgid "Number of Clicks"
msgstr "Numero di clic"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_utm_campaign__click_count
msgid "Number of clicks generated by the campaign"
msgstr "Numero di clic generati dalla campagna"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__title
msgid "Page Title"
msgstr "Titolo pagina"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__redirected_url
msgid "Redirected URL"
msgstr "URL reindirizzato"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__code
msgid "Short URL Code"
msgstr "Codice breve URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__code
msgid "Short URL code"
msgstr "Codice breve URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__source_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Source"
msgstr "Origine"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action_campaign
msgid "Statistics of Clicks"
msgstr "Statistiche dei clic"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__url
msgid "Target URL"
msgstr "URL obiettivo"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__campaign_id
#: model:ir.model.fields,help:link_tracker.field_link_tracker_click__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Questo nome aiuta a tenere traccia delle diverse campagne, es. "
"Saldi_autunnali, Sconti_natalizi ecc."

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "Metodo di consegna: es. lettera, e-mail o banner pubblicitario"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Origine del link: es. motore di ricerca, altro dominio o nome dell'elenco "
"e-mail"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Title and URL"
msgstr "Titolo e URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url
msgid "Tracked URL"
msgstr "URL monitorato"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid ""
"Trackers are used to collect count stat about click on links and generate "
"short URLs."
msgstr ""
"I monitoraggi servono a raccogliere statistiche riguardo il numero di clic "
"sui link e a generare URL brevi."

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "URL"
msgstr "URL"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "UTM"
msgstr "UTM"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_utm_campaign
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__campaign_id
msgid "UTM Campaign"
msgstr "Campagna UTM"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Visit Page"
msgstr "Visita pagina"

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid "Visit Webpage"
msgstr "Visita pagina web"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "Website Link"
msgstr "Link sito web"
