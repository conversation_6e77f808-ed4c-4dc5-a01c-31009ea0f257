# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * link_tracker
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__campaign_id
msgid "Campaign"
msgstr "Kampanja"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_click_ids
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_form
msgid "Clicks"
msgstr ""

#. module: link_tracker
#: sql_constraint:link.tracker.code:0
msgid "Code must be unique."
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_code_ids
msgid "Codes"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__country_id
msgid "Country"
msgstr "Zemlja"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__click_date
msgid "Create Date"
msgstr "Kreiraj Datum"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.action_link_tracker
msgid "Create a new link tracker"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_uid
msgid "Created by"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__display_name
msgid "Display Name"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__favicon
msgid "Favicon"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__icon_src
msgid "Favicon Source"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url_host
msgid "Host of the short URL"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__id
msgid "ID"
msgstr "ID"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__ip
msgid "Internet Protocol"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker____last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click____last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code____last_update
msgid "Last Modified on"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_uid
msgid "Last Updated by"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_date
msgid "Last Updated on"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__link_id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__link_id
msgid "Link"
msgstr "Veza"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.action_link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker
#: model:ir.ui.menu,name:link_tracker.menu_url_shortener_main
msgid "Link Tracker"
msgstr ""

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_click
msgid "Link Tracker Click"
msgstr ""

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_code
msgid "Link Tracker Code"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__medium_id
msgid "Medium"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__count
msgid "Number of Clicks"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__title
msgid "Page Title"
msgstr "Naslov stranice"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__redirected_url
msgid "Redirected URL"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__code
msgid "Short URL Code"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__code
msgid "Short URL code"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__source_id
msgid "Source"
msgstr "Izvorno"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.action_view_click_statistics
msgid "Statistics of Clicks"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__url
msgid "Target URL"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_filter
msgid "Title and URL"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_filter
msgid "Tracked Link"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url
msgid "Tracked URL"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_form
msgid "Visit Page"
msgstr ""

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:142
#, python-format
msgid "Visit Webpage"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_form
msgid "Website Link"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_click_form
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_click_graph
msgid "Website Link Clicks"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_form_stats
msgid "Website Link Graph"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_graph
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_tree
msgid "Website Links"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_click_tree
msgid "Website Links Clicks"
msgstr ""

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.action_link_tracker_stats
msgid "link.tracker.form.graph.action"
msgstr ""
