///
/// This file regroups the variables that style odoo components.
/// They are available in every asset bundle.
///

// Font sizes
$o-root-font-size: 12px !default;
$o-font-size-base: 13rem * (1px / $o-root-font-size) !default;
$o-line-height-base: 1.5 !default; // This is BS default

// Colors
$o-community-color: #71639e !default;
$o-enterprise-color: #714B67 !default;
$o-enterprise-primary-color: #017e84 !default;

$o-brand-odoo: $o-community-color !default;
$o-brand-primary: $o-community-color !default;

$o-brand-secondary: #f0eeee !default;
$o-brand-lightsecondary: #e2e2e0 !default;

// This is BS default
$o-gray-100: #f8f9fa !default;
$o-gray-200: #e9ecef !default;
$o-gray-300: #dee2e6 !default;
$o-gray-400: #ced4da !default;
$o-gray-500: #adb5bd !default;
$o-gray-600: #6c757d !default;
$o-gray-700: #495057 !default;
$o-gray-800: #343a40 !default;
$o-gray-900: #212529 !default;

$o-success: #28a745 !default;
$o-info: #17a2b8 !default;
$o-warning: #ffac00 !default;
$o-danger: #dc3545 !default;

// Fine-tune contextual text colors.
$o-theme-text-colors: (
    "success": #008818,
    "info": #0180a5,
    "warning": #9a6b01,
    "danger": #d23f3a,
)!default;

$o-main-text-color: $o-gray-700 !default;
$o-main-color-muted: rgba($o-main-text-color, .76) !default;
$o-main-headings-color: $o-gray-900 !default;
$o-main-link-color: darken($o-brand-primary, 5%) !default;
$o-main-favorite-color: #f3cc00 !default;
$o-main-code-color: #d2317b !default;

$o-view-background-color: white !default;
$o-shadow-color: #303030 !default;

$o-form-lightsecondary: #ccc !default;

$o-list-footer-bg-color: #eee !default;
$o-list-footer-font-weight: bold !default;

$o-tooltip-background-color: white !default;
$o-tooltip-color: #666666 !default;
$o-tooltip-arrow-color: white !default;
$o-tooltip-text-color: #777777 !default;
$o-tooltip-title-text-color: black !default;
$o-tooltip-title-background-color: #F7F7F7 !default;

// Forms

// o-inputs
$o-input-padding-y: 2px !default;
$o-input-padding-x: 4px !default;

$o-input-border-required: $o-brand-primary !default;

// Bootstrap clone, we just change the icon color
$o-custom-checkbox-indicator-icon-checked-disabled: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$o-main-color-muted}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/></svg>") !default;

// Layout
//
// Extension of BS4. This is not redefining the BS4 variable directly as we only
// need the extra ones for media queries (not creating new breakpoint classes).
// Note: default BS4 values are hardcoded here while it should be possible to
// merge with the default BS variable (but we would have to take care of
// ordering & cie).
$o-extra-grid-breakpoints: (
    xs: 0,
    vsm: 475px,
    sm: 576px,
    md: 768px,
    lg: 992px,
    xl: 1200px,
    xxl: 1534px,
) !default;
$o-form-group-cols: 12 !default;
$o-form-spacing-unit: 5px !default;
$o-horizontal-padding: 16px !default;
$o-innergroup-rpadding: 45px !default;
$o-dropdown-hpadding: 20px !default;
$o-dropdown-vpadding: 3px !default;
$o-dropdown-box-shadow: 0 1rem 1.1rem rgba(#000, .1) !default;

$o-sheet-vpadding: 24px !default;

$o-statbutton-height: 44px !default;
$o-statbutton-vpadding: 0px !default;
$o-statbutton-spacing: 6px !default;

$o-modal-lg: 980px !default;
$o-modal-md: 650px !default;

// Needed for having no spacing between sheet and mail body in mass_mailing:
// Different required cancel paddings between web and web_enterprise
$o-sheet-cancel-tpadding: 0px !default;

$o-avatar-size: 90px !default;

$o-statusbar-height: 33px !default;

$o-label-font-size-factor: 0.8 !default;
$o-navbar-height: 46px !default;

$o-nb-calendar-colors: 24 !default;

$o-base-settings-mobile-tabs-height: 40px !default;
$o-base-settings-mobile-tabs-overflow-gap: 3% !default;

$o-cp-breadcrumb-height: 30px !default;

$o-datepicker-week-color: #8f8f8f !default;

$o-card-body-bg-opacity: 0.9 !default;

// Kanban

$o-kanban-default-record-width: 300px !default;
$o-kanban-small-record-width: 240px !default;

$o-kanban-header-title-height: 50px !default;

$o-kanban-image-width: 64px !default;
$o-kanban-image-fill-width: 95px !default;
$o-kanban-inside-vgutter: 8px !default;
$o-kanban-inside-hgutter: 8px !default;
$o-kanban-color-border-width: 2px !default;
$o-kanban-inner-hmargin: 5px !default;
$o-kanban-progressbar-height: 20px !default;

$o-kanban-mobile-tabs-height: 40px !default;
$o-kanban-mobile-empty-height: $o-kanban-image-width !default;
// ------- Kanban dashboard variables -------

// Used to manage spacing in complex dropdown menu
$o-kanban-dashboard-dropdown-complex-gap: 5px !default;

// Form view

$o-form-view-sheet-max-width: 1140px !default;
