<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_pe" model="res.partner">
        <field name="name">PE Company</field>
        <field name="vat">10071037199</field>
        <field name="street">Calle Las Tortugas</field>
        <field name="city">Chorrillos</field>
        <field name="country_id" ref="base.pe"/>
        <field name="state_id" ref="base.state_pe_25"/>
        <field name="zip">15067</field>
        <field name="phone">+51 ***********</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.peexample.com</field>
        <field name="l10n_latam_identification_type_id" ref="it_RUC"/>
    </record>

    <record id="demo_company_pe" model="res.company">
        <field name="name">PE Company</field>
        <field name="partner_id" ref="partner_demo_company_pe"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_pe')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_pe.demo_company_pe'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_pe.pe_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_pe.demo_company_pe')"/>
    </function>
</odoo>
