# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_extended
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-05-19 06:01+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_extended
#: code:addons/product_extended/wizard/wizard_price.py:24
#: code:addons/product_extended/wizard/wizard_price.py:40
#, python-format
msgid "Active ID is not set in Context."
msgstr "El id. activo no se ha establecido en el contexto."

#. module: product_extended
#: model:ir.model,name:product_extended.model_mrp_bom
msgid "Bill of Material"
msgstr "Lista de materiales"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Cancel"
msgstr "Cancelar"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Change Price"
msgstr "Cambiar precio"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Change Standard Price"
msgstr "Cambiar precio estándar"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_recursive
msgid "Change prices of child BoMs too"
msgstr "Cambio de precios de hijo de la LDM"

#. module: product_extended
#: model:ir.actions.act_window,name:product_extended.action_view_compute_price_wizard
#: model:ir.model,name:product_extended.model_wizard_price
msgid "Compute Price Wizard"
msgstr "Asistente de Calculo de  precio"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_ext_form_view2
msgid "Compute from BOM"
msgstr "Calcular desde LDM"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_ext_form_view2
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"Calcular el precio del producto usando productos y operaciones relacionados "
"con lista de materiales, sólo de productos manufacturados."

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_create_date
msgid "Created on"
msgstr "Creado en"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_real_time_accounting
msgid "Generate accounting entries when real-time"
msgstr "Generar asientos contables en tiempo real"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_id
msgid "ID"
msgstr "ID"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_info_field
msgid "Info"
msgstr "Información"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_mrp_bom_get_variant_count
msgid "Number of variant for the product"
msgstr ""

#. module: product_extended
#: model:ir.model,name:product_extended.model_product_template
msgid "Product Template"
msgstr "Plantilla producto"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Set price on BoM"
msgstr "Fijar precio de lista de materiales"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_mrp_bom_standard_price
msgid "Standard Price"
msgstr "Precio estándar"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid ""
"The price is computed from the bill of material lines which are not variant "
"specific"
msgstr ""
"El precio se calcula de la  lista de materiales que no son específicas de "
"variante"

#. module: product_extended
#: code:addons/product_extended/wizard/wizard_price.py:38
#, fuzzy, python-format
msgid ""
"This wizard is built for product templates, while you are currently running "
"it from a product variant."
msgstr ""
"Este asistente es para las plantillas de producto, mientras que se están "
"ejecutando de una variante de producto."

#~ msgid "Compute price wizard"
#~ msgstr "Asistente de Calculo de  precio"
