<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_immediate_production" model="ir.ui.view">
        <field name="name">mrp.immediate.production.view.form</field>
        <field name="model">mrp.immediate.production</field>
        <field name="arch" type="xml">
            <form string="Immediate production?">
                <group>
                    <p>You have not recorded <i>produced</i> quantities yet, by clicking on <i>apply</i> Odoo will produce all the finished products and consume all components.</p>
                </group>

                <field name="show_productions" invisible="1"/>
                <field name="immediate_production_line_ids" nolabel="1" attrs="{'invisible': [('show_productions', '=', False)]}">
                    <tree create="0" delete="0" editable="top">
                        <field name="production_id"/>
                        <field name="to_immediate" widget="boolean_toggle"/>
                    </tree>
                </field>

                <footer>
                    <button name="process" string="Apply" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z" />
                </footer>
            </form>
        </field>
    </record>
</odoo>
