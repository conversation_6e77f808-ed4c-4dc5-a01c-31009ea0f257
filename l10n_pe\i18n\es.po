# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_pe
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-26 17:44+0000\n"
"PO-Revision-Date: 2021-02-26 17:44+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_pe
#: model:ir.model,name:l10n_pe.model_res_city
#: model:ir.model.fields,field_description:l10n_pe.field_l10n_pe_res_city_district__city_id
msgid "City"
msgstr "Ciudad"

#. module: l10n_pe
#: model:ir.model.fields,field_description:l10n_pe.field_l10n_pe_res_city_district__code
#: model:ir.model.fields,field_description:l10n_pe.field_res_city__l10n_pe_code
msgid "Code"
msgstr "Código"

#. module: l10n_pe
#: model:l10n_latam.identification.type,name:l10n_pe.it_DIC
msgid "Diplomatic Identity Card"
msgstr "Cédula Diplomática de identidad"

#. module: l10n_pe
#: model:ir.model.fields,field_description:l10n_pe.field_account_move_line__display_name
#: model:ir.model.fields,field_description:l10n_pe.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_pe.field_account_tax_template__display_name
#: model:ir.model.fields,field_description:l10n_pe.field_l10n_latam_identification_type__display_name
#: model:ir.model.fields,field_description:l10n_pe.field_l10n_pe_res_city_district__display_name
#: model:ir.model.fields,field_description:l10n_pe.field_res_city__display_name
#: model:ir.model.fields,field_description:l10n_pe.field_res_partner__display_name
msgid "Display Name"
msgstr "Nombre Mostrado"

#. module: l10n_pe
#: model:ir.model,name:l10n_pe.model_l10n_pe_res_city_district
#: model:ir.model.fields,field_description:l10n_pe.field_res_partner__l10n_pe_district
#: model:ir.model.fields,field_description:l10n_pe.field_res_users__l10n_pe_district
msgid "District"
msgstr "Distrito"

#. module: l10n_pe
#: model_terms:ir.ui.view,arch_db:l10n_pe.pe_partner_address_form
msgid "District..."
msgstr "Distrito..."

#. module: l10n_pe
#: model:ir.model.fields,help:l10n_pe.field_res_partner__l10n_pe_district
#: model:ir.model.fields,help:l10n_pe.field_res_users__l10n_pe_district
msgid "Districts are part of a province or city."
msgstr "Los distritos forman parte de una ciudad o provincia"

#. module: l10n_pe
#: model:ir.model.fields,field_description:l10n_pe.field_account_tax__l10n_pe_edi_unece_category
#: model:ir.model.fields,field_description:l10n_pe.field_account_tax_template__l10n_pe_edi_unece_category
msgid "EDI UNECE code"
msgstr "Código UNECE"

#. module: l10n_pe
#: model:ir.model.fields,field_description:l10n_pe.field_account_tax__l10n_pe_edi_tax_code
#: model:ir.model.fields,field_description:l10n_pe.field_account_tax_template__l10n_pe_edi_tax_code
msgid "EDI peruvian code"
msgstr "Código de Tributo"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_tax_code__9997
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_tax_code__9997
msgid "EXO - Exonerated"
msgstr "EXO - Exonerado"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_tax_code__9995
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_tax_code__9995
msgid "EXP - Exportation"
msgstr "EXP - Exportación"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_unece_category__e
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_unece_category__e
msgid "Exempt from tax"
msgstr "Exento de impuestos"

#. module: l10n_pe
#: model:ir.model.fields,help:l10n_pe.field_account_tax_template__l10n_pe_edi_unece_category
msgid ""
"Follow the UN/ECE 5305 standard from the United Nations Economic Commission "
"for Europe for more information  "
"http://www.unece.org/trade/untdid/d08a/tred/tred5305.htm"
msgstr ""
"Estandard UN/ECE 5305 de las Comisión Económica de las Naciones Unidas para Europa"
"para mas información consulta este enlace"
"http://www.unece.org/trade/untdid/d08a/tred/tred5305.htm"

#. module: l10n_pe
#: model:ir.model.fields,help:l10n_pe.field_account_tax__l10n_pe_edi_unece_category
msgid ""
"Follow the UN/ECE 5305 standard from the United Nations Economic Commission "
"for Europe for more information "
"http://www.unece.org/trade/untdid/d08a/tred/tred5305.htm"
msgstr ""
"Estándar UN/ECE 5305 de las Comisión Económica de las Naciones Unidas para Europa"
"para mas información consulta este enlace"
"http://www.unece.org/trade/untdid/d08a/tred/tred5305.htm"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_unece_category__g
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_unece_category__g
msgid "Free export item, tax not charged"
msgstr "Articulo de exportación, libre de impuestos."

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_tax_code__9996
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_tax_code__9996
msgid "GRA - Free"
msgstr "GRA - Gratuito"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_tax_code__7152
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_tax_code__7152
msgid "ICBPER - Plastic bag tax"
msgstr "ICBPER - Impuesto a la bolsa plastica"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_tax_code__1000
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_tax_code__1000
msgid "IGV - General Sales Tax"
msgstr "IGV - IGV Impuesto General a las Ventas"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_tax_code__9998
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_tax_code__9998
msgid "INA - Unaffected"
msgstr "INA - Inafecto"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_tax_code__2000
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_tax_code__2000
msgid "ISC - Selective Excise Tax"
msgstr "ISC - Impuesto Selectivo al Consumo"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_tax_code__1016
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_tax_code__1016
msgid "IVAP - Tax on Sale Paddy Rice"
msgstr "IVAP - Impuesto a la Venta Arroz Pilado"

#. module: l10n_pe
#: model:l10n_latam.identification.type,name:l10n_pe.it_IDCR
msgid "Identity document of the country of residence"
msgstr "Documento de identidad del país de residencia"

#. module: l10n_pe
#: model:l10n_latam.identification.type,name:l10n_pe.it_SP
msgid "Safe Passage"
msgstr "Salvoconducto"

#. module: l10n_pe
#: model:ir.model.fields,field_description:l10n_pe.field_l10n_latam_identification_type__l10n_pe_vat_code
msgid "L10N Pe Vat Code"
msgstr "Código de tipo de documento de identidad"

#. module: l10n_pe
#: model:ir.model.fields,field_description:l10n_pe.field_account_move_line____last_update
#: model:ir.model.fields,field_description:l10n_pe.field_account_tax____last_update
#: model:ir.model.fields,field_description:l10n_pe.field_account_tax_template____last_update
#: model:ir.model.fields,field_description:l10n_pe.field_l10n_latam_identification_type____last_update
#: model:ir.model.fields,field_description:l10n_pe.field_l10n_pe_res_city_district____last_update
#: model:ir.model.fields,field_description:l10n_pe.field_res_city____last_update
#: model:ir.model.fields,field_description:l10n_pe.field_res_partner____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: l10n_pe
#: model:ir.model.fields,field_description:l10n_pe.field_l10n_pe_res_city_district__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_pe
#: model:ir.model.fields,field_description:l10n_pe.field_l10n_pe_res_city_district__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_pe
#: model:l10n_latam.identification.type,name:l10n_pe.it_CPP
msgid "License Permit Temp. Perman."
msgstr "Carné Permiso Temp. Perman."

#. module: l10n_pe
#: model:l10n_latam.identification.type,name:l10n_pe.it_NDTD
msgid "Non-Domiciled Tax Document"
msgstr "Documento tributario no domiciliado, sin RUC"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_tax_code__9999
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_tax_code__9999
msgid "OTROS - Other taxes"
msgstr "OTROS - Otros tributos"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_unece_category__o
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_unece_category__o
msgid "Services outside scope of tax"
msgstr "Servicios fuera del ámbito fiscal"

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_unece_category__s
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_unece_category__s
msgid "Standard rate"
msgstr "Tarifa estándar"

#. module: l10n_pe
#: model_terms:ir.ui.view,arch_db:l10n_pe.pe_partner_address_form
msgid "State..."
msgstr "Estado..."

#. module: l10n_pe
#: model_terms:ir.ui.view,arch_db:l10n_pe.pe_partner_address_form
msgid "Street"
msgstr "Calle"

#. module: l10n_pe
#: model_terms:ir.ui.view,arch_db:l10n_pe.pe_partner_address_form
msgid "Street Name..."
msgstr "Nombre de la calle..."

#. module: l10n_pe
#: model:ir.model,name:l10n_pe.model_account_tax
msgid "Tax"
msgstr "Impuesto"

#. module: l10n_pe
#: model:ir.model.fields,help:l10n_pe.field_res_city__l10n_pe_code
msgid "This code will help with the identification of each city in Peru."
msgstr "Este código ayudará con la identificación de cada ciudad en Perú."

#. module: l10n_pe
#: model:ir.model.fields,help:l10n_pe.field_l10n_pe_res_city_district__code
msgid "This code will help with the identification of each district in Peru."
msgstr "Este código ayudará con la identificación de cada distrito en Perú."

#. module: l10n_pe
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax__l10n_pe_edi_unece_category__z
#: model:ir.model.fields.selection,name:l10n_pe.selection__account_tax_template__l10n_pe_edi_unece_category__z
msgid "Zero rated goods"
msgstr "Bienes libres de impuestos"

