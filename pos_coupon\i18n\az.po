# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_coupon
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-08 06:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2023\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_coupon
#: model:coupon.program,name:pos_coupon.15_pc_on_next_order
msgid "15% on next order"
msgstr ""

#. module: pos_coupon
#: model:mail.template,body_html:pos_coupon.mail_coupon_template
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object.partner_id.name\">\n"
"            Congratulations <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        </t>\n"
"\n"
"        Here is your reward from <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>.<br/>\n"
"\n"
"        <t t-if=\"object.program_id.reward_type == 'discount'\">\n"
"            <t t-if=\"object.program_id.discount_type == 'fixed_amount'\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-out=\"'%s' % format_amount(object.program_id.discount_fixed_amount, object.program_id.currency_id) or ''\">$ 10.0</span><br/>\n"
"                <strong style=\"font-size: 24px;\">off on your next order</strong><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\">\n"
"                    <t t-out=\"object.program_id.discount_percentage or ''\">10.0</t> %\n"
"                </span>\n"
"                <t t-if=\"object.program_id.discount_apply_on == 'specific_products'\">\n"
"                    <br/>\n"
"                    <t t-if=\"len(object.program_id.discount_specific_product_ids) != 1\">\n"
"                        <t t-set=\"display_specific_products\" t-value=\"True\"/>\n"
"                        <strong style=\"font-size: 24px;\">\n"
"                            on some products*\n"
"                        </strong>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <strong style=\"font-size: 24px;\" t-out=\"'on %s' % object.program_id.discount_specific_product_ids.name or ''\">Chair floor protection</strong>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-elif=\"object.program_id.discount_apply_on == 'cheapest_product'\">\n"
"                    <br/><strong style=\"font-size: 24px;\">\n"
"                        off on the cheapest product\n"
"                    </strong>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <br/><strong style=\"font-size: 24px;\">\n"
"                        off on your next order\n"
"                    </strong>\n"
"                </t>\n"
"                <br/>\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'product'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\" t-out=\"'get %s free %s' % (object.program_id.reward_product_quantity, object.program_id.reward_product_id.name) or ''\">Chair floor protection</span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'free_shipping'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\">\n"
"                get free shipping\n"
"            </span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        </t>\n"
"    </td></tr>\n"
"    <tr style=\"margin-top: 16px\"><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use this promo code\n"
"        <t t-if=\"object.expiration_date\">\n"
"            before <t t-out=\"object.expiration_date or ''\">2021-06-05</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">13996301932606901095</strong>\n"
"        </p>\n"
"        <t t-if=\"object.program_id.rule_min_quantity not in [0, 1]\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Minimum purchase of <t t-out=\"object.program_id.rule_min_quantity or ''\">10</t> products\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"object.program_id.rule_minimum_amount != 0.00\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Valid for purchase above <t t-out=\"object.program_id.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(object.program_id.rule_minimum_amount) or ''\">10.00</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"display_specific_products\">\n"
"            <span>\n"
"                *Valid for following products: <t t-out=\"', '.join(object.program_id.discount_specific_product_ids.mapped('name')) or ''\">Office Chair Black</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <br/>\n"
"        Thank you,\n"
"        <t t-if=\"object.source_pos_order_id.user_id.signature\">\n"
"            <br/>\n"
"            <t t-out=\"object.source_pos_order_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""

#. module: pos_coupon
#: code:addons/pos_coupon/models/coupon.py:0
#, python-format
msgid ""
"A coupon from the same program has already been reserved for this order."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__promo_barcode
msgid ""
"A technical field used as an alternative to the promo_code. This is "
"automatically generated when promo_code is changed."
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/ActivePrograms.xml:0
#, python-format
msgid "Active Programs"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order__applied_program_ids
msgid "Applied Programs"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_coupon__pos_order_id
msgid "Applied on PoS Order"
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "Are you sure you want to deactivate %s in this order?"
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__promo_barcode
#, python-format
msgid "Barcode"
msgstr "Barkod"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_barcode_rule
msgid "Barcode Rule"
msgstr "Barkod Qaydası"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order__used_coupon_ids
msgid "Consumed Coupons"
msgstr ""

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_coupon_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order_line__coupon_id
#: model:ir.model.fields.selection,name:pos_coupon.selection__barcode_rule__type__coupon
msgid "Coupon"
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Coupon Codes"
msgstr ""

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_coupon_program
msgid "Coupon Program"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__coupon_program_ids
#: model:ir.ui.menu,name:pos_coupon.menu_coupon_type_config
#: model_terms:ir.ui.view,arch_db:pos_coupon.res_config_view_form_inherit_pos_coupon
msgid "Coupon Programs"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order_line__coupon_id
msgid "Coupon that generated this reward."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__use_coupon_programs
msgid "Coupons & Promotions"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__program_ids
msgid "Coupons and Promotions"
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "Deactivating program"
msgstr ""

#. module: pos_coupon
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_pos_config_view_form
msgid "Define the coupon and promotion programs you can use in this PoS."
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/ControlButtons/PromoCodeButton.xml:0
#, python-format
msgid "Enter Code"
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ControlButtons/PromoCodeButton.js:0
#, python-format
msgid "Enter Promotion or Coupon Code"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order_line__is_program_reward
msgid ""
"Flag indicating that this order line is a result of coupon/promo program."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order__generated_coupon_ids
msgid "Generated Coupons"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order_line__is_program_reward
msgid "Is reward line"
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "No"
msgstr "Tapılmayanlar"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__pos_order_line_ids
msgid "Order lines where this program is applied."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_order_count
msgid "PoS Order Count"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_order_line_ids
msgid "PoS Order Lines"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_coupon__source_pos_order_id
msgid "PoS Order Reference"
msgstr ""

#. module: pos_coupon
#: code:addons/pos_coupon/models/coupon_program.py:0
#, python-format
msgid "PoS Orders"
msgstr "PoS Sifarişləri"

#. module: pos_coupon
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_program_view_promo_program_form
msgid "PoS Sales"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_coupon__pos_order_id
msgid "PoS order where this coupon is consumed/booked."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_coupon__source_pos_order_id
msgid "PoS order where this coupon is generated."
msgstr ""

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Satış Nöqtəsi Konfiqurasiyası"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Satış Nöqtəsi Sifariş Sətrləri"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_pos_order
msgid "Point of Sale Orders"
msgstr "Satış Nöqtəsi Sifarişləri"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_config_ids
msgid "Point of Sales"
msgstr "Satış Nöqtəsi"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_order_ids
msgid "Pos Order"
msgstr "Pos Sifariş"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order_line__program_id
msgid "Program"
msgstr ""

#. module: pos_coupon
#: code:addons/pos_coupon/models/pos_config.py:0
#, python-format
msgid "Program: %(name)s (%(type)s), Reward Product: `%(reward_product)s`"
msgstr ""

#. module: pos_coupon
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_pos_config_view_form
msgid "Promotion & coupon programs to use."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__promo_program_ids
#: model:ir.ui.menu,name:pos_coupon.menu_promotion_type_config
#: model_terms:ir.ui.view,arch_db:pos_coupon.res_config_view_form_inherit_pos_coupon
msgid "Promotion Programs"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order_line__program_id
msgid "Promotion/Coupon Program where this reward line is based."
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/ControlButtons/ResetProgramsButton.xml:0
#, python-format
msgid "Reset Programs"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order__applied_program_ids
msgid ""
"Technical field. This is set when the order is validated. We normally get "
"this value thru the `program_id` of the reward lines."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__pos_order_ids
msgid "The PoS orders where this program is applied."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__valid_partner_ids
msgid "These are the partners that can avail this program."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__valid_product_ids
msgid "These are the products that are valid in this program."
msgstr ""

#. module: pos_coupon
#: code:addons/pos_coupon/models/pos_config.py:0
#, python-format
msgid "This coupon is invalid (%s)."
msgstr ""

#. module: pos_coupon
#: code:addons/pos_coupon/models/pos_config.py:0
#, python-format
msgid ""
"To continue, make the following reward products to be available in Point of "
"Sale."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_barcode_rule__type
msgid "Type"
msgstr "Tip"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_config__use_coupon_programs
msgid "Use coupon and promotion programs in this PoS configuration."
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__valid_partner_ids
msgid "Valid Partner"
msgstr ""

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__valid_product_ids
msgid "Valid Product"
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Valid until:"
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr "Bəli"

#. module: pos_coupon
#: model:mail.template,report_name:pos_coupon.mail_coupon_template
msgid "Your Coupon Code"
msgstr ""

#. module: pos_coupon
#: model:mail.template,subject:pos_coupon.mail_coupon_template
msgid "Your reward coupon from {{ object.program_id.company_id.name }} "
msgstr ""

#. module: pos_coupon
#: model:mail.template,name:pos_coupon.mail_coupon_template
msgid "[POS] Coupon: Send by Email"
msgstr ""

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "no expiration"
msgstr ""
