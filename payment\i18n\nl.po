# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-10 14:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Gegevens opgehaald"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Amount:</b>"
msgstr "<b>Bedrag:</b>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Reference:</b>"
msgstr "<b>Referentie:</b>"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""
"<h3>Maak een betaling naar: </h3><ul><li>Bank: %s</li><li>Rekeningnummer: "
"%s</li><li>Rekeninghouder: %s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr "<i class=\"fa fa-arrow-circle-right\"/> Terug naar mijn account"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "<i class=\"fa fa-trash\"/> Delete"
msgstr "<i class=\"fa fa-trash\"/> Verwijder"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"
msgstr ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">Opgeslagen betaalmethoden</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "<span><i class=\"fa fa-arrow-right\"/> Get my Stripe keys</span>"
msgstr ""
"<span><i class=\"fa fa-arrow-right\"/> Mijn Stripe-sleutels ophalen</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span><i class=\"fa fa-arrow-right\"/> How to configure your PayPal "
"account</span>"
msgstr ""
"<span><i class=\"fa fa-arrow-right\"/> Je PayPal-account configureren</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span>Start selling directly without an account; an email will be sent by "
"Paypal to create your new account and collect your payments.</span>"
msgstr ""
"<span>Begin direct met verkopen zonder een account; Er wordt een e-mail "
"verzonden door Paypal om je nieuwe account te maken en je betalingen te "
"innen.</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid "<strong>No suitable payment acquirer could be found.</strong>"
msgstr "<strong>Er is geen geschikte betaalprovider gevonden.</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Geen geschikte betalingsoptie gevonden.</strong><br/>\n"
"                                 Als je denkt dat het een fout is, neem dan contact op met de beheerder van de website."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment acquirer configuration."
msgstr ""
"<strong>Waarschuwing!</strong> Er is een terugbetaling in behandeling voor deze betaling.\n"
"                        Wacht even totdat het is verwerkt. Als de terugbetaling nog steeds in behandeling is over een\n"
"                        enkele minuten, controleer de configuratie van je betaalprovider."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid ""
"<strong>Warning</strong> Creating a payment acquirer from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>Waarschuwing</strong> Het maken van een betaalprovider via de knop <em>CREATE</em> wordt niet ondersteund.\n"
"                        Gebruik in plaats daarvan de actie <em>Dupliceren</em>."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure your are logged in as the right partner "
"before making this payment."
msgstr ""
"<strong>Waarschuwing</strong> Zorg ervoor dat je bent ingelogd als de juiste"
" partner voordat je deze betaling uitvoert."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>Waarschuwing</strong> De valuta ontbreekt of is onjuist."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>Waarschuwing</strong> Je moet ingelogd zijn om te betalen."

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction with reference %s already exists."
msgstr "Er bestaat al een betalingstransactie met referentie %s."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(acq_name)s)."
msgstr ""
"Er is een teruggaveverzoek van %(amount)s verzonden. De betaling wordt "
"binnenkort aangemaakt. Referentie transactie teruggave: %(ref)s "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "Een token is vereist om een betaaltransactie uit te voeren."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated (%(acq_name)s)."
msgstr "Er is een transactie gestart met referentie %(ref)s (%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token_name)s (%(acq_name)s)."
msgstr ""
"Een transactie met referentie %(ref)s is gestart met de betaalmethode "
"%(token_name)s (%(acq_name)s)."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__access_token
msgid "Access Token"
msgstr "Toegangstoken"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Account"
msgstr "Rekening"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Rekeningnummer"

#. module: payment
#: code:addons/payment/models/account_payment_method.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
#, python-format
msgid "Acquirer"
msgstr "Provider"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_id
msgid "Acquirer Account"
msgstr "Betaalprovider"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_reference
msgid "Acquirer Reference"
msgstr "Referentie betaalprovider"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__acquirer_ids
msgid "Acquirers"
msgstr "Providers"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Acquirers list"
msgstr "Providerlijst"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Activate"
msgstr "Activeer"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Activate Stripe"
msgstr "Activeer Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Actief"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_active
msgid "Add Extra Fees"
msgstr "Voeg extra kosten toe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "Adres"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "Opslaan van betalingsmethoden toestaan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Amount"
msgstr "Bedrag"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "Bedrag beschikbaar voor terugbetaling"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "Maximum bedrag"

#. module: payment
#. openerp-web
#: code:addons/payment/controllers/portal.py:0
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "An error occurred during the processing of this payment."
msgstr "Er is een fout opgetreden tijdens de verwerking van deze betaling."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Apply"
msgstr "Toepassen"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Are you sure you want to delete this payment method?"
msgstr "Weet je zeker dat je deze betaalmethode wilt verwijderen?"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Weet je zeker dat je de geautoriseerde transactie ongeldig wilt maken? Deze "
"actie is definitief."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_authorization
msgid "Authorize Mechanism Supported"
msgstr "Autorisatie mechanisme ondersteund"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__auth_msg
msgid "Authorize Message"
msgstr "Authorisatie bericht"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "Geautoriseerd "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Geautoriseerde transacties"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Availability"
msgstr "Beschikbaarheid"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Bank"
msgstr "Bank"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Bank Accounts"
msgstr "Bankrekeningen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Naam bank"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr "Callback documentmodel"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_is_done
msgid "Callback Done"
msgstr "Terugbellen gereed"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr "Callback hash"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr "Callback methode"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Record ID"
msgstr "ID terugbelopname"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
#, python-format
msgid "Cancel"
msgstr "Annuleren"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "Geannuleerd"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__cancel_msg
msgid "Canceled Message"
msgstr "Geannuleerd bericht"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Cancelled payments"
msgstr "Geannuleerde betalingen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__capture_manually
msgid "Capture Amount Manually"
msgstr "Bepaal bedrag handmatig"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "Afvangen transactie"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"Leg het bedrag vast van Odoo, wanneer de levering is voltooid.\n"
"Gebruik dit als je je klantenkaarten alleen wilt opladen wanneer\n"
"je zeker weet dat je de goederen naar hen kunt verzenden."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Check here"
msgstr "Controleer hier"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_open_payment_onboarding_payment_acquirer_wizard
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "Kies een betalinsgmethode"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "Plaats"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Click here to be redirected to the confirmation page."
msgstr "Klik hier om doorverwezen te worden naar de bevestigingspagina."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid "Close"
msgstr "Sluiten"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "Code"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__color
msgid "Color"
msgstr "Kleur"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Communication"
msgstr "Communicatie"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__company_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "Bedrijf"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Configuration"
msgstr "Configuratie"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Configure"
msgstr "Configureren"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Confirm Deletion"
msgstr "Bevestig verwijdering"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "Bevestigd"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_id
msgid "Corresponding Module"
msgstr "Overeenkomende module"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__country_ids
msgid "Countries"
msgstr "Landen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "Land"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "Token maken"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_acquirer
msgid "Create a new payment acquirer"
msgstr "Maak een nieuwe betaalprovider aan"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "Create a new payment token"
msgstr "Een nieuwe betalingstoken maken"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_icon
msgid "Create a payment icon"
msgstr "Maak een betaal icoon aan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Credentials"
msgstr "Inloggegevens"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_stripe
msgid "Credit & Debit Card"
msgstr "Creditcard / betaalkaart"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_adyen
msgid "Credit Card (powered by Adyen)"
msgstr "Credit Card (aangeboden via Adyen)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_alipay
msgid "Credit Card (powered by Alipay)"
msgstr "Credit Card (mogelijk gemaakt door Alipay)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_authorize
msgid "Credit Card (powered by Authorize)"
msgstr "Credit Card (mogelijk gemaakt door Authorize)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_buckaroo
msgid "Credit Card (powered by Buckaroo)"
msgstr "Credit Card (aangeboden via Buckaroo)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_ogone
msgid "Credit Card (powered by Ogone)"
msgstr "Credit Card (aangedreven door Ogone)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payulatam
msgid "Credit Card (powered by PayU Latam)"
msgstr "Credit Card (aangeboden door PayU Latam)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payumoney
msgid "Credit Card (powered by PayUmoney)"
msgstr "Credit Card (mogelijk gemaakt door PayUmoney)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_sips
msgid "Credit Card (powered by Sips)"
msgstr "Credit Card (mogelijk gemaakt door Sips)"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit card (via Stripe)"
msgstr "Credit Card (via Stripe)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Aangepaste betaalinstructies"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Klant"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__sequence
msgid "Define the display order"
msgstr "Definieer de weergavevolgorde"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__description
msgid "Description"
msgstr "Omschrijving"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__display_as
msgid "Description of the acquirer for customers"
msgstr "Beschrijving van de provider voor klanten"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Disabled"
msgstr "Uitgeschakeld"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "Dismiss"
msgstr "Afwijzen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_icon__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_as
msgid "Displayed as"
msgstr "Weergegeven als"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__done
msgid "Done"
msgstr "Gereed"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__done_msg
msgid "Done Message"
msgstr "Gedaan bericht"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "Concept"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Email"
msgstr "E-mail"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Enable credit &amp; debit card payments supported by Stripe"
msgstr "Inschakelen creditcard &amp; betaalkaart betalingen door Stripe"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__enabled
msgid "Enabled"
msgstr "Ingeschakeld"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "Fout"

#. module: payment
#. openerp-web
#: code:addons/payment/models/payment_transaction.py:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Error: %s"
msgstr "Fout: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__fees
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Fees"
msgstr "Vergoedingen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_fees_computation
msgid "Fees Computation Supported"
msgstr "Vergoedingen berekening ondersteund"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr "Vaste huishoudelijke toeslagen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr "Vaste internationale vergoedingen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__acquirer_id
msgid "Force Payment Acquirer"
msgstr "Betalingsverwerver afdwingen"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_link_wizard__acquirer_id
msgid ""
"Force the customer to pay via the specified payment acquirer. Leave empty to"
" allow the customer to choose among all acquirers."
msgstr ""
"Dwing de klant om te betalen via de opgegeven betaalprovider. Laat leeg "
"zodat de klant kan kiezen uit alle acquirers."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "From"
msgstr "Van"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__full_only
msgid "Full Only"
msgstr "Alleen volledig"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "Genereer betaallink"

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Gegenereerde verkoop betaallink"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "Genereer een betaallink"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "Groeperen op"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP routing"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__has_multiple_acquirers
msgid "Has Multiple Acquirers"
msgstr "Heeft meerdere verkrijgers"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "Heeft een terugbetaling in behandeling"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "Is de betaling nabewerkt?"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pre_msg
msgid "Help Message"
msgstr "Help berichten"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__new_user
msgid "I don't have a Paypal account"
msgstr "Ik heb geen Paypal account"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__existing_user
msgid "I have a Paypal account"
msgstr "Ik heb een Paypal account"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_icon__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "If not defined, the acquirer name will be used."
msgstr "Indien niet gedefinieerd, wordt de provider-naam gebruikt."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "If the payment hasn't been confirmed you can contact us."
msgstr "Indien de betaling niet bevestigd is kun je ons contacteren."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr ""
"Als je denkt dat het een fout is, neem dan contact op met de beheerder van "
"de website."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image_128
#: model:ir.model.fields,field_description:payment.field_payment_icon__image
msgid "Image"
msgstr "Afbeelding"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__image_payment_form
msgid "Image displayed on the payment form"
msgstr "Afbeelding dat wordt weergegeven in het betaalformulier"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,help:payment.field_payment_acquirer__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the acquirer."
msgstr ""
"In de testmodus wordt een nepbetaling verwerkt via een testbetalingsinterface.\n"
"Deze modus wordt geadviseerd bij het instellen van de acquirer."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__inline_form_view_id
msgid "Inline Form Template"
msgstr "Inline formuliersjabloon"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Install"
msgstr "Installeren"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_state
msgid "Installation State"
msgstr "Installatiestatus"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Installed"
msgstr "Geïnstalleerd"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Internal server error"
msgstr "Interne server fout"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "Factu(u)r(en)"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "Facturen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "Facturen tellen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "Is nabewerkt"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "It is currently linked to the following documents:"
msgstr "Het is momenteel gekoppeld aan de volgende documenten:"

#. module: payment
#: model:ir.model,name:payment.model_account_journal
msgid "Journal"
msgstr "Dagboek"

#. module: payment
#: model:ir.model,name:payment.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__just_done
msgid "Just done"
msgstr "Net gedaan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "Landingsroute"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Taal"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_icon____last_update
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "Datum laatste staatswijziging"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Leave empty to allow all acquirers"
msgstr "Laat leeg om alle acquirers toe te staan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_meth_link
msgid "Manage payment methods"
msgstr "Beheer betaalmethodes"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "Handmatig"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "Maximaal toegestane restitutie"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "Handelaarsaccount ID"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "Bericht"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Messages"
msgstr "Berichten"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Methode"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Multiple payment options selected"
msgstr "Meerdere betalingsopties geselecteerd"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__name
#: model:ir.model.fields,field_description:payment.field_payment_icon__name
#: model:ir.model.fields,field_description:payment.field_payment_token__name
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Name"
msgstr "Naam"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__provider__none
msgid "No Provider Set"
msgstr "Geen provider ingesteld"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Acquirer menu."
msgstr ""
"Er kon geen handmatige betaalwijze worden gevonden voor dit bedrijf. Je "
"dient er een aan te maken via het betaalprovider menu."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "No payment has been processed."
msgstr "Geen betaling is verwerkt."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "No payment option selected"
msgstr "Geen betalingsoptie geselecteerd"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__not_done
msgid "Not done"
msgstr "Niet gedaan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "Not verified"
msgstr "niet geverifieerd"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from acquirers allowing to capture the amount are "
"available."
msgstr ""
"Houd er rekening mee dat alleen tokens van acquirers beschikbaar zijn die "
"het mogelijk maken om het bedrag vast te leggen."

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"Let op dat tokens van verwerkers ingesteld op alleen geautoriseerde "
"transacties (in plaats van het vastleggen van het bedrag) niet beschikbaar "
"zijn."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Odoo Enterprise module"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "Offline betaling per token"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online Payments"
msgstr "Online betalingen"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "Online directe betaling"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "Online betaling per token"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "Online betalen met omleiding"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online payments enabled"
msgstr "Online betalingen ingeschakeld"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Only administrators can access this data."
msgstr "Alleen beheerders hebben toegang tot deze gegevens."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be captured."
msgstr "Alleen geautoriseerde transacties kunnen worden vastgelegd."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be voided."
msgstr "Alleen geautoriseerde transacties kunnen worden geannuleerd."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only confirmed transactions can be refunded."
msgstr "Alleen bevestigde transacties kunnen worden terugbetaald."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "Handeling"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "Overige"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr "Andere betaling verwervers"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDT identiteitstoken"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__partial
msgid "Partial"
msgstr "Gedeeltelijk"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "Relatie"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "Relatienaam"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Pay"
msgstr "Betaal"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:payment.acquirer,name:payment.payment_acquirer_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "Betaling"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Acquirer"
msgstr "Betaalprovider"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_list
msgid "Payment Acquirers"
msgstr "Betaalproviders"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__available_acquirer_ids
msgid "Payment Acquirers Available"
msgstr "Betaalprovider beschikbaar"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "Betaalbedrag"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Followup"
msgstr "Betaling opvolging"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Form"
msgstr "Betaalformulier"

#. module: payment
#: model:ir.model,name:payment.model_payment_icon
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Payment Icon"
msgstr "Betaal icoon"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_icon
#: model:ir.ui.menu,name:payment.payment_icon_menu
msgid "Payment Icons"
msgstr "Betaaliconen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Betalingsinstructies"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__journal_id
msgid "Payment Journal"
msgstr "Betaaldagboek"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "Betaal ink"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Betalingsmethode"

#. module: payment
#: model:ir.model,name:payment.model_account_payment_method_line
msgid "Payment Methods"
msgstr "Betaalwijzes"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__description
msgid "Payment Ref"
msgstr "Betalingsreferentie"

#. module: payment
#: model:ir.model,name:payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "Wizard voor teruggave van betalingen"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "Betalingstoken"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "Aantal betaaltokens"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model:ir.ui.menu,name:payment.payment_token_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "Betaaltokens"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__transaction_id
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
msgid "Payment Transaction"
msgstr "Betalingstransactie"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "Betalingstransacties"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "Betalingstransacties gekoppeld aan token"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer_onboarding_wizard
msgid "Payment acquire onboarding wizard"
msgstr "Betaalprovider onboarding wizard"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__payment_acquirer_selection
msgid "Payment acquirer selected"
msgstr "Betaalprovider geselecteerd"

#. module: payment
#: model:ir.model,name:payment.model_account_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "Betalingen"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments failed"
msgstr "Betalingen mislukt"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments received"
msgstr "Betalingen ontvangen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "Paypal soort gebruiker"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "In afwachting"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pending_msg
msgid "Pending Message"
msgstr "Bericht in afwachting"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Telefoon"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select a payment option."
msgstr "Selecteer een betalingsoptie."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select only one payment option."
msgstr "Selecteer slechts één betalingsoptie."

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set an amount smaller than %s."
msgstr "Stel een bedrag in dat kleiner is dan %s."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "Please switch to company '%s' to make this payment."
msgstr "Schakel over naar bedrijf '%s' om deze betaling te doen."

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the following transfer details"
msgstr "Gebruik de volgende tranfer details."

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the order name as communication reference."
msgstr ""
"Je dient het ordernummer als referentie te gebruiken in alle communicatie."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Please wait ..."
msgstr "Even geduld ..."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "Verwerkt door"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment.field_payment_token__provider
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Provider"
msgstr "Provider"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Reason:"
msgstr "Reden:"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Reason: %s"
msgstr "Reden: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "Formuliersjabloon omleiden"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Reference"
msgstr "Referentie"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "Referentie moet uniek zijn!"

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
#, python-format
msgid "Refund"
msgstr "Creditfactuur"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "Restitutiebedrag"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "Terugbetaald bedrag"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "Creditfacturen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__refunds_count
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "Terugbetalingen tellen"

#. module: payment
#: model:ir.model,name:payment.model_account_payment_register
msgid "Register Payment"
msgstr "Betaling registreren"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "Gerelateerde document ID"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "Gerelateerde documentmodel"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "SEPA automatische incasso"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_account_journal_form
msgid "SETUP"
msgstr "INSTELLEN"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "Save Payment Method"
msgstr "Betaalmethode opslaan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Save my payment details"
msgstr "Bewaar mijn betalingsgegevens"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr "Opgeslagen betalingstoken"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "Bewaarde betalingstoken"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Select countries. Leave empty to use everywhere."
msgstr "Selecteer landen. Laat leeg om alle landen te gebruiken."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "Selecteer onboarding betaalmethode"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__sequence
#: model:ir.model.fields,field_description:payment.field_payment_icon__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Server Error"
msgstr "Serverfout"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Server error:"
msgstr "Serverfout:"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_allow_tokenization
msgid "Show Allow Tokenization"
msgstr "Toon Tokenisatie toestaan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_auth_msg
msgid "Show Auth Msg"
msgstr "Auth-bericht weergeven"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_cancel_msg
msgid "Show Cancel Msg"
msgstr "Toon Annuleer bericht"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_credentials_page
msgid "Show Credentials Page"
msgstr "Pagina met inloggegevens weergeven"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_done_msg
msgid "Show Done Msg"
msgstr "Toon Klaar bericht"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_payment_icon_ids
msgid "Show Payment Icon"
msgstr "Betalingspictogram weergeven"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pending_msg
msgid "Show Pending Msg"
msgstr "Bericht in behandeling tonen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pre_msg
msgid "Show Pre Msg"
msgstr "Pre-bericht weergeven"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sips
msgid "Sips"
msgstr "Sips"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "Bronbetaling"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "Brontransactie"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "Status"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_acquirer_onboarding_state
msgid "State of the onboarding payment acquirer step"
msgstr "Status van de onboarding betaalprovider stap"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "Status"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.acquirer,name:payment.payment_acquirer_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Stripe publiceerbare sleutel"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Stripe geheime sleutel"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "Geschikte betalingstoken"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_icon_ids
msgid "Supported Payment Icons"
msgstr "Ondersteunde betaaliconen"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,help:payment.field_account_payment_register__use_electronic_payment_method
msgid "Technical field used to hide or show the payment_token_id if needed."
msgstr ""
"Technisch veld dat wordt gebruikt om de payment_token_id indien nodig te "
"verbergen of weer te geven."

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_test
msgid "Test"
msgstr "Test"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__test
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.manage
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Test Mode"
msgstr "Testmodus"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__provider
#: model:ir.model.fields,help:payment.field_payment_token__provider
#: model:ir.model.fields,help:payment.field_payment_transaction__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"De betalingsdienstaanbieder die bij deze acquirer moet worden gebruikt"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "The access token is invalid."
msgstr "Het toegangstoken is ongeldig."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__acquirer_ref
msgid "The acquirer reference of the token of the transaction"
msgstr "De provider-referentie van het token van de transactie"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__acquirer_reference
msgid "The acquirer reference of the transaction"
msgstr "De provider-referentie van de transactie"

#. module: payment
#: code:addons/payment/wizards/payment_refund_wizard.py:0
#, python-format
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr ""
"Het terug te betalen bedrag moet positief zijn en mag niet hoger zijn dan "
"%s."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__name
msgid "The anonymized acquirer reference of the payment method"
msgstr "De geanonimiseerde provider-referentie van de betaalmethode"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__color
msgid "The color of the card in kanban view"
msgstr "De kleur van de kaart in kanbanweergave"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "Het aanvullende informatieve bericht over de staat"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__country_ids
msgid ""
"The countries for which this payment acquirer is available.\n"
"If none is set, it is available for all countries."
msgstr ""
"De landen waarvoor deze betaalprovider beschikbaar is.\n"
"Als er geen is ingesteld, is deze beschikbaar voor alle landen."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__description
msgid "The description shown in the card in kanban view "
msgstr "De beschrijving op de kaart in kanbanweergave "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__fees
msgid "The fees amount; set by the system as it depends on the acquirer"
msgstr ""
"Het bedrag van de vergoedingen; ingesteld door het systeem omdat het afhangt"
" van de acquirer"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The following fields must be filled: %s"
msgstr "De volgende velden moeten worden ingevuld: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "De interne referentie van de transactie"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__journal_id
msgid "The journal in which the successful transactions are posted"
msgstr "Het dagboek waarin de succesvolle transacties worden geboekt"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__acquirer_ids
msgid "The list of acquirers supporting this payment icon"
msgstr "De lijst met providers die dit betalingspictogram ondersteunen"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "Het bericht dat wordt weergegeven als de betaling is geautoriseerd"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__cancel_msg
msgid ""
"The message displayed if the order is canceled during the payment process"
msgstr ""
"Het bericht dat wordt weergegeven als de bestelling wordt geannuleerd "
"tijdens het betalingsproces"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr ""
"Het bericht dat wordt weergegeven als de bestelling is voltooid na het "
"betalingsproces"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr ""
"Het bericht dat wordt weergegeven als de bestelling in behandeling is na het"
" betalingsproces"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr ""
"Het weergegeven bericht om het betalingsproces uit te leggen en te helpen"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""
"De betaling moet ofwel direct zijn, met omleiding, ofwel met een token."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "The related payment is posted: %s"
msgstr "De gekoppelde betaling is geboekt: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "De route waarnaar de gebruiker wordt omgeleid na de transactie"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "De bronbetaling van gerelateerde restitutiebetalingen"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of related refund transactions"
msgstr "De brontransactie van gerelateerde terugbetalingstransacties"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr ""
"De sjabloon die een formulier weergeeft dat is ingediend om de gebruiker om "
"te leiden bij het doen van een betaling"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr ""
"De sjabloon die het inline betalingsformulier weergeeft bij een directe "
"betaling"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(acq_name)s)."
msgstr ""
"Er is een fout opgetreden bij de transactie met referentie %(ref)s voor "
"%(amount)s (%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(acq_name)s)."
msgstr ""
"De transactie met referentie %(ref)s voor %(amount)s is geautoriseerd "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(acq_name)s)."
msgstr ""
"De transactie met referentie %(ref)s voor %(amount)s is bevestigd "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is canceled "
"(%(acq_name)s)."
msgstr ""
"De transactie met referentie %(ref)s voor %(amount)s is geannuleerd "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is pending "
"(%(acq_name)s)."
msgstr ""
"De transactie met referentie %(ref)s voor %(amount)s is in behandeling "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "The value of the payment amount must be positive."
msgstr "Het bedrag van de betaling moet positief zijn."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "Er zijn geen transacties om weer te geven"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "Er valt niets te betalen."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"acquirer's database, allowing the customer to reuse it for a next purchase."
msgstr ""
"Hiermee wordt bepaald of klanten hun betaalmethoden kunnen opslaan als betaaltokens.\n"
"Een betalingstoken is een anonieme link naar de details van de betalingsmethode die zijn opgeslagen in de\n"
"de database van de acquirer, zodat de klant deze kan hergebruiken voor een volgende aankoop."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__image
#: model:ir.model.fields,help:payment.field_payment_icon__image_payment_form
msgid ""
"This field holds the image used for this payment icon, limited to 64x64 px"
msgstr ""
"Dit veld bevat de afbeelding die wordt gebruikt voor dit betalingspictogram,"
" beperkt tot 64x64 px"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment "
"acquirers. Setting an email for this partner is advised."
msgstr ""
"Deze partner heeft geen e-mailadres, wat problemen kan veroorzaken bij "
"sommige betaalproviders. Het instellen van een e-mail voor deze partner "
"wordt aangeraden."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "This payment has not been processed yet."
msgstr "Deze betaling is nog niet verwerkt."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has been verified by our system."
msgstr "Deze betaalmethode is geverifieerd door ons systeem."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has not been verified by our system."
msgstr "Deze betaalmethode is niet geverifieerd door ons systeem."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "This transaction has been cancelled."
msgstr "De transactie is geannuleerd"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_tokenization
msgid "Tokenization Supported"
msgstr "Tokenisatie ondersteund"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"Transaction authorization is not supported by the following payment "
"acquirers: %s"
msgstr ""
"Transactieautorisatie wordt niet ondersteund door de volgende "
"betaalproviders: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "Transacties"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__support_refund
msgid "Type of Refund Supported"
msgstr "Ondersteund type restitutie"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Unable to contact the Odoo server."
msgstr "Niet mogelijk om met de Odoo server contact op te nemen."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Upgrade"
msgstr "Bijwerken"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "Elektronische betaalmethode gebruiken"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "Validatie van de betaalmethode"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr "Variabele binnenlandse kosten (in percentage)"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Variable fees must always be positive and below 100%."
msgstr "Variabele vergoedingen moeten altijd positief zijn en lager dan 100%."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr "Variabele internationale vergoedingen (in percentage)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__verified
msgid "Verified"
msgstr "Geverifieerd"

#. module: payment
#: model:ir.model,name:payment.model_ir_ui_view
msgid "View"
msgstr "Weergave"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "Ongeldige transactie"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Waiting for payment"
msgstr "Wacht op betaling"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Warning!"
msgstr "Waarschuwing!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to delete your payment method."
msgstr "We kunnen je betaalmethode niet verwijderen."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr "We konden je betaling niet vinden, maar maak je geen zorgen."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr "We kunnen je betaling niet verwerken."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to save your payment method."
msgstr "We kunnen je betaalmethode niet opslaan."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/post_processing.js:0
#, python-format
msgid "We are processing your payment, please wait ..."
msgstr "We verwerken je betaling, een moment geduld..."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are waiting for the payment acquirer to confirm the payment."
msgstr "We wachten op de betaalprovider om de betaling te bevestigen."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr ""
"Of er een betalingstoken moet worden aangemaakt bij de naverwerking van de "
"transactie"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__callback_is_done
msgid "Whether the callback has already been executed"
msgstr "Of de callback al is uitgevoerd"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_transfer
msgid "Wire Transfer"
msgstr "Overschrijving"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You can click here to be redirected to the confirmation page."
msgstr ""
"Je kunt hier klikken om doorverwezen te worden naar de bevestigingspagina."

#. module: payment
#: code:addons/payment/models/account_journal.py:0
#, python-format
msgid ""
"You can't delete a payment method that is linked to an acquirer in the enabled or test state.\n"
"Linked acquirer(s): %s"
msgstr ""
"Je kunt een betaalmethode die is gekoppeld aan een acquirer in de ingeschakelde of teststatus niet verwijderen.\n"
"Gelinkte verkrijger(s): %s"

#. module: payment
#: code:addons/payment/models/ir_ui_view.py:0
#, python-format
msgid "You cannot delete a view that is used by a payment acquirer."
msgstr ""
"Je kunt een weergave die wordt gebruikt door een betaalprovider niet "
"verwijderen."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "You do not have access to this payment token."
msgstr "Je hebt geen toegang tot deze betaaltoken."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr ""
"Je zou binnen een paar minuten een e-mail moeten ontvangen die je betaling "
"bevestigd."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is confirmed."
msgstr "Je wordt verwittigd wanneer de betaling bevestigd is."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is fully confirmed."
msgstr "Je ontvangt een bericht wanneer de betaling volledig bevestigd is"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order has been processed."
msgstr "Je bestelling is verwerkt"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order is being processed, please wait ..."
msgstr "Je bestelling wordt verwerkt, even geduld ..."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been authorized."
msgstr "Jouw betaling is geauthoriseerd."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been cancelled."
msgstr "Jouw betaling is geannuleerd."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment has been received but need to be confirmed manually."
msgstr "Jouw betaling is ontvangen maar moet handmatig bevestigd worden."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_test
#, python-format
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "Jouw betaling is succesvol verwerkt maar wacht op goedkeuring."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been successfully processed. Thank you!"
msgstr "Jouw betaling is succesvol verwerkt. Bedankt!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment is in pending state."
msgstr "Jouw betaling is in afwachting"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "Postcode"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "Postcode"

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
#: model:ir.cron,cron_name:payment.cron_post_process_payment_tx
#: model:ir.cron,name:payment.cron_post_process_payment_tx
msgid "payment: post-process transactions"
msgstr "Betaling: transacties na verwerking"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show less"
msgstr "laat minder zien"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show more"
msgstr "laat meer zien"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "to choose another payment method."
msgstr "om een andere betalingsmethodes kiezen."
