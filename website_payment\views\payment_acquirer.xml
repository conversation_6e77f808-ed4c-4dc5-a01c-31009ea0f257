<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="acquirer_form_website" model="ir.ui.view">
            <field name="name">acquirer.form.inherit.website</field>
            <field name="model">payment.acquirer</field>
            <field name="inherit_id" ref="payment.payment_acquirer_form"/>
            <field name="arch" type="xml">
                <field name='company_id' position='after'>
                    <field name="website_id" options="{'no_open': True, 'no_create_edit': True}" groups="website.group_multi_website"/>
                </field>
            </field>
        </record>

    </data>
</odoo>
