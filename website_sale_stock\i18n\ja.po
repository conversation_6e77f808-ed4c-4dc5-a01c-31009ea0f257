# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_payment
msgid "<strong>Warning!</strong>"
msgstr "<strong>警告!</strong>"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__cart_qty
msgid "Cart Qty"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Continue Selling"
msgstr "販売継続"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__allow_out_of_stock_order
msgid "Continue selling when out-of-stock"
msgstr "欠品時も続けて販売"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created storable products. This can "
"be changed at the product level."
msgstr "新規作成された保管可能な商品に設定したデフォルト可用性モード。"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Default visibility for custom messages."
msgstr "カスタムメッセージのデフォルト可視性。"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Inventory"
msgstr "在庫"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Only"
msgstr "只今"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Out of Stock"
msgstr "在庫切れ"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Out-of-Stock"
msgstr "欠品時販売"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__out_of_stock_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__out_of_stock_message
msgid "Out-of-Stock Message"
msgstr "欠品時メッセージ"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product"
msgstr "プロダクト"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_template
msgid "Product Template"
msgstr "プロダクトテンプレート"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Sales Order"
msgstr "販売オーダ"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "販売オーダ明細"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Show Available Qty"
msgstr "利用可能数量を表示"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__available_threshold
msgid "Show Threshold"
msgstr "閾値を表示"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__show_availability
msgid "Show availability Qty"
msgstr "入手可能数量を表示"

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry"
" for the inconvenience."
msgstr "カートを更新いただきましたが、商品が販売不可になりました。ご期待にそえず申し訳ございません。"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_stock_picking
msgid "Transfer"
msgstr "運送"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Units"
msgstr "個"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__website_warehouse_id
#: model:ir.model.fields,field_description:website_sale_stock.field_website__warehouse_id
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Warehouse"
msgstr "倉庫"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order__warning_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order_line__warning_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_payment
msgid "Warning"
msgstr "警告"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
#: model:ir.model.fields,field_description:website_sale_stock.field_stock_picking__website_id
msgid "Website"
msgstr "ウェブサイト"

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_stock_picking__website_id
msgid "Website this picking belongs to."
msgstr "このピッキングが属するウェブサイト"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added"
msgstr "カートに既に"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added all the available product in your cart."
msgstr "カートに全て入手可能な商品が追加されました。"

#. module: website_sale_stock
#: code:addons/website_sale_stock/controllers/main.py:0
#, python-format
msgid ""
"You ask for %(quantity)s products but only %(available_qty)s is available"
msgstr ""

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid "You ask for %s products but only %s is available"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "in your cart."
msgstr "を追加しました。"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "left in stock."
msgstr "在庫が残っています。"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "only if below"
msgstr "次の数量以下の時"
