<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_sharing_inherit_project_task_view_form" model="ir.ui.view">
        <field name="name">project.task.form.inherit.timesheet</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project.project_sharing_project_task_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='timesheet_ids']/tree" position="attributes">
                <attribute name="decoration-muted">timesheet_invoice_id != False</attribute>
            </xpath>
            <xpath expr="//field[@name='timesheet_ids']/tree/field[@name='unit_amount']" position="before">
                <field name="timesheet_invoice_id" invisible="1"/>
                <field name="so_line"
                    attrs="{'column_invisible': [('parent.allow_billable', '=', False)]}"
                    context="{'with_remaining_hours': True, 'with_price_unit': True}" options="{'no_create': True, 'no_open': True}"
                    optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='remaining_hours']" position="after">
                <field name="allow_billable" invisible="1" />
                <field name="remaining_hours_available" invisible="1"/>
                <span id="remaining_hours_so_label" attrs="{'invisible': ['|', '|', '|', '|', ('allow_billable', '=', False), ('sale_order_id', '=', False), ('partner_id', '=', False), ('sale_line_id', '=', False), ('remaining_hours_available', '=', False)]}">
                    <label class="font-weight-bold" for="remaining_hours_so" string="Remaining Hours on SO"
                            attrs="{'invisible': ['|', ('encode_uom_in_days', '=', True), ('remaining_hours_so', '&lt;', 0)]}"/>
                    <label class="font-weight-bold" for="remaining_hours_so" string="Remaining Days on SO"
                            attrs="{'invisible': ['|', ('encode_uom_in_days', '=', False), ('remaining_hours_so', '&lt;', 0)]}"/>
                    <label class="font-weight-bold text-danger" for="remaining_hours_so" string="Remaining Hours on SO"
                            attrs="{'invisible': ['|', ('encode_uom_in_days', '=', True), ('remaining_hours_so', '&gt;=', 0)]}"/>
                    <label class="font-weight-bold text-danger" for="remaining_hours_so" string="Remaining Days on SO"
                            attrs="{'invisible': ['|', ('encode_uom_in_days', '=', False), ('remaining_hours_so', '&gt;=', 0)]}"/>
                </span>
                <field name="remaining_hours_so" nolabel="1" widget="timesheet_uom" attrs="{'invisible': ['|', '|', '|', '|', ('allow_billable', '=', False), ('sale_order_id', '=', False), ('partner_id', '=', False), ('sale_line_id', '=', False), ('remaining_hours_available', '=', False)]}"></field>
            </xpath>
        </field>
    </record>

</odoo>
