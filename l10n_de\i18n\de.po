# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_de
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-06-30 07:07+0000\n"
"PO-Revision-Date: 2021-07-29 13:09+0200\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.3\n"
"Last-Translator: \n"
"Language: de\n"

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.din5008_css
msgid ""
"&amp;.din_page {\n"
"                        &amp;.header {\n"
"                            .company_header {\n"
"                                .name_container {\n"
"                                    color:"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,help:l10n_de.field_account_tax__l10n_de_datev_code
msgid "2 digits code use by Datev"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.din5008_css
msgid ""
";\n"
"                                    }\n"
"                                }\n"
"                            }\n"
"                            h2 {\n"
"                                color:"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.din5008_css
msgid ""
";\n"
"                                }\n"
"                            }\n"
"                        }\n"
"                        &amp;.invoice_note {\n"
"                            td {\n"
"                                .address {\n"
"                                    &gt; span {\n"
"                                        color:"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.din5008_css
msgid ""
";\n"
"                            }\n"
"                            .page {\n"
"                                [name=invoice_line_table], "
"[name=stock_move_table], .o_main_table {\n"
"                                    th {\n"
"                                        color:"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/datev.py:0
#, python-format
msgid ""
"Account %s does not authorize to have tax %s specified on the "
"line.                                 Change the tax used in this invoice or "
"remove all taxes from the account"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontenplan Vorlage"

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.external_layout_din5008
msgid "BIC:"
msgstr "BIC:"

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__bank_ids
msgid "Bank Accounts"
msgstr "Bankkonten"

#. module: l10n_de
#: model:ir.model.fields,help:l10n_de.field_base_document_layout__bank_ids
msgid "Bank accounts related to this company"
msgstr "Bankkonten mit Bezug zu dieser Firma"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Cancelled Invoice"
msgstr "Stornierte Rechnung"

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__city
msgid "City"
msgstr "Stadt"

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_base_document_layout
msgid "Company Document Layout"
msgstr "Geschäftsvorlagen Layout"

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__company_registry
msgid "Company Registry"
msgstr "Unternehmensregister"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Credit Note"
msgstr "Gutschrift"

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:l10n_de.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_de.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_de.field_account_tax_template__display_name
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__display_name
#: model:ir.model.fields,field_description:l10n_de.field_ir_actions_report__display_name
#: model:ir.model.fields,field_description:l10n_de.field_product_template__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Draft Invoice"
msgstr "Rechnungsentwurf"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#: code:addons/l10n_de/models/base_document_layout.py:0
#, python-format
msgid "Due Date"
msgstr "Fälligkeitsdatum"

#. module: l10n_de
#: model:ir.ui.menu,name:l10n_de.account_reports_de_statements_menu
msgid "Germany"
msgstr "Deutschland"

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.external_layout_din5008
msgid "HRB Nr:"
msgstr "HRB Nr.:"

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.external_layout_din5008
msgid "IBAN:"
msgstr "IBAN:"

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_chart_template__id
#: model:ir.model.fields,field_description:l10n_de.field_account_move__id
#: model:ir.model.fields,field_description:l10n_de.field_account_tax__id
#: model:ir.model.fields,field_description:l10n_de.field_account_tax_template__id
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__id
#: model:ir.model.fields,field_description:l10n_de.field_ir_actions_report__id
#: model:ir.model.fields,field_description:l10n_de.field_product_template__id
msgid "ID"
msgstr "ID"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Invoice"
msgstr "Rechnung"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#: code:addons/l10n_de/models/base_document_layout.py:0
#, python-format
msgid "Invoice Date"
msgstr "Rechnungsdatum"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#: code:addons/l10n_de/models/base_document_layout.py:0
#, python-format
msgid "Invoice No."
msgstr "Rechnungsnummer"

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_move
msgid "Journal Entry"
msgstr "Buchungseintrag"

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_tax__l10n_de_datev_code
#: model:ir.model.fields,field_description:l10n_de.field_account_tax_template__l10n_de_datev_code
msgid "L10N De Datev Code"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_bank_statement_line__l10n_de_document_title
#: model:ir.model.fields,field_description:l10n_de.field_account_move__l10n_de_document_title
#: model:ir.model.fields,field_description:l10n_de.field_account_payment__l10n_de_document_title
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__l10n_de_document_title
msgid "L10N De Document Title"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_bank_statement_line__l10n_de_template_data
#: model:ir.model.fields,field_description:l10n_de.field_account_move__l10n_de_template_data
#: model:ir.model.fields,field_description:l10n_de.field_account_payment__l10n_de_template_data
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__l10n_de_template_data
msgid "L10N De Template Data"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:l10n_de.field_account_move____last_update
#: model:ir.model.fields,field_description:l10n_de.field_account_tax____last_update
#: model:ir.model.fields,field_description:l10n_de.field_account_tax_template____last_update
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout____last_update
#: model:ir.model.fields,field_description:l10n_de.field_ir_actions_report____last_update
#: model:ir.model.fields,field_description:l10n_de.field_product_template____last_update
msgid "Last Modified on"
msgstr "Zuletzt bearbeitet am"

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.external_layout_din5008
msgid "Page: <span class=\"page\"/> of <span class=\"topage\"/>"
msgstr "Seite: <span class=\"page\"/> von <span class=\"topage\"/>"

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_product_template
msgid "Product Template"
msgstr "Produktvorlage"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#: code:addons/l10n_de/models/base_document_layout.py:0
#, python-format
msgid "Reference"
msgstr "Referenz"

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Source"
msgstr "Quelle"

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__street
msgid "Street"
msgstr "Straße"

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__street2
msgid "Street2"
msgstr "Straße 2"

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_tax
msgid "Tax"
msgstr "Steuer"

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Steuervorlage"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Vendor Bill"
msgstr "Lieferantenrechnung"

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Vendor Credit Note"
msgstr "Lieferantengutschrift"

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__zip
msgid "Zip"
msgstr "Postleitzahl"
