<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_hk" model="res.partner">
        <field name="name">HK Company</field>
        <field name="vat"></field>
        <field name="street">118 Connaught Road West</field>
        <field name="street2">Unit 07 - 10, 38/F Yat Chau International Plaza</field>
        <field name="city">Sai Ying Pun</field>
        <field name="country_id" ref="base.hk"/>

        <field name="zip"></field>
        <field name="phone">+852 5123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.hkexample.com</field>
    </record>

    <record id="demo_company_hk" model="res.company">
        <field name="name">HK Company</field>
        <field name="partner_id" ref="partner_demo_company_hk"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_hk')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_hk.demo_company_hk'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_hk.l10n_hk_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_hk.demo_company_hk')"/>
    </function>
</odoo>
