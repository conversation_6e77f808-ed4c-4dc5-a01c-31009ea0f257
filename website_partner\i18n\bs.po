# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_partner
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_partner
#: model:ir.model,name:website_partner.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__is_published
#: model:ir.model.fields,field_description:website_partner.field_res_users__is_published
msgid "Is published"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__is_seo_optimized
#: model:ir.model.fields,field_description:website_partner.field_res_users__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_partner
#: model_terms:ir.ui.view,arch_db:website_partner.partner_detail
msgid "Short Description for List View"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,help:website_partner.field_res_partner__website_url
#: model:ir.model.fields,help:website_partner.field_res_users__website_url
msgid "The full URL to access the document through the website."
msgstr "Kompletan URL za pristup dokumentu putem website-a."

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_published
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_description
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_description
msgid "Website Partner Full Description"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_short_description
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_short_description
msgid "Website Partner Short Description"
msgstr ""

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_url
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_url
msgid "Website URL"
msgstr "Website URL"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_meta_description
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_meta_description
msgid "Website meta description"
msgstr "Website meta opis"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_meta_keywords
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website meta ključne riječi"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_meta_title
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_meta_title
msgid "Website meta title"
msgstr "Website meta naslov"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_meta_og_img
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_meta_og_img
msgid "Website opengraph image"
msgstr ""
