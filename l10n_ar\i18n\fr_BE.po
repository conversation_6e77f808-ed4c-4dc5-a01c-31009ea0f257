# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ar
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.3alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-03-30 18:36+0000\n"
"PO-Revision-Date: 2020-03-30 18:36+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "- Activities Start:"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "- CUIT:"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__01
msgid "01 - National Taxes"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__02
msgid "02 - Provincial Taxes"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__03
msgid "03 - Municipal Taxes"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__04
msgid "04 - Internal Taxes"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__06
msgid "06 - VAT perception"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__07
msgid "07 - IIBB perception"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__08
msgid "08 - Municipal Taxes Perceptions"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__09
msgid "09 - Other Perceptions"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__4
msgid "10.5%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__9
msgid "2,5%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__5
msgid "21%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__6
msgid "27%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__99
msgid "99 - Others"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>CBU for payment: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Currency: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Exchange rate: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Incoterm:</strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Payment Terms: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Reference:</strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Source:</strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<span groups=\"account.group_show_line_subtotals_tax_included\">Amount</span>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<span>% VAT</span>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<span>NCM</span>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Customer: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Due Date: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Invoiced period: </strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>VAT Cond: </strong>"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_concept
#: model:ir.model.fields,help:l10n_ar.field_account_move__l10n_ar_afip_concept
#: model:ir.model.fields,help:l10n_ar.field_account_payment__l10n_ar_afip_concept
msgid "A concept is suggested regarding the type of the products on the invoice but it is allowed to force a different type if required."
msgstr ""

#. module: l10n_ar
#: model:ir.ui.menu,name:l10n_ar.menu_afip_config
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_move_form
msgid "AFIP"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_identification_type__l10n_ar_afip_code
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_afip_code
#: model:ir.model.fields,field_description:l10n_ar.field_res_currency__l10n_ar_afip_code
#: model:ir.model.fields,field_description:l10n_ar.field_uom_uom__l10n_ar_afip_code
msgid "AFIP Code"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_concept
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_concept
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_concept
msgid "AFIP Concept"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_partner_id
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_afip_pos_partner_id
msgid "AFIP POS Address"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_number
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_afip_pos_number
msgid "AFIP POS Number"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_system
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_afip_pos_system
msgid "AFIP POS System"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.base_view_partner_form
msgid "AFIP Responsibility"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_l10n_ar_afip_responsibility_type
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_afip_responsibility_type_id
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_afip_responsibility_type_form
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_afip_responsibility_type_tree
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_res_partner_filter
msgid "AFIP Responsibility Type"
msgstr ""

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_afip_responsibility_type
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position__l10n_ar_afip_responsibility_type_ids
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position_template__l10n_ar_afip_responsibility_type_ids
msgid "AFIP Responsibility Types"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_service_end
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_service_end
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_service_end
msgid "AFIP Service End Date"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_service_start
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_service_start
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_service_start
msgid "AFIP Service Start Date"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_AN
msgid "AN"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "Account"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__date
msgid "Accounting Date"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_partner_property_form
msgid "Accounting Documents"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__active
msgid "Active"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_afip_start_date
msgid "Activities Start"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_document_type_filter
msgid "Argentinean Documents"
msgstr ""

#. module: l10n_ar
#: model:ir.ui.menu,name:l10n_ar.account_reports_ar_statements_menu
msgid "Argentinean Statements"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_partner_bank
msgid "Bank Accounts"
msgstr ""

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_exento
#: model:product.template,name:l10n_ar.product_product_exento_product_template
msgid "Book \"Development in Odoo\" (VAT Exempt)"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CBA
msgid "CBA"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner_bank.py:0
#, python-format
msgid "CBU"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CCat
msgid "CCat"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CCor
msgid "CCor"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CCorr
msgid "CCorr"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CDI
msgid "CDI"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIBAR
msgid "CIBAR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CICha
msgid "CICha"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIChu
msgid "CIChu"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIER
msgid "CIER"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIF
msgid "CIF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIJ
msgid "CIJ"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CILP
msgid "CILP"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CILR
msgid "CILR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIMen
msgid "CIMen"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIMis
msgid "CIMis"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIN
msgid "CIN"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIRN
msgid "CIRN"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIS
msgid "CIS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISC
msgid "CISC"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISF
msgid "CISF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISJ
msgid "CISJ"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISL
msgid "CISL"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISdE
msgid "CISdE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIT
msgid "CIT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CITdF
msgid "CITdF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CPF
msgid "CPF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CUIL
msgid "CUIL"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_cuit
msgid "CUIT"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Can not change the POS number, you can only change the first number for document type that you are creating in odoo"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Can not create chart of account until you configure your company AFIP Responsibility and VAT."
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CdM
msgid "CdM"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__code
msgid "Code"
msgstr ""

#. module: l10n_ar
#: model:ir.model.constraint,message:l10n_ar.constraint_l10n_ar_afip_responsibility_type_code
msgid "Code must be unique!"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_company_requires_vat
msgid "Company Requires Vat?"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_vat
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_vat
msgid "Computed field that returns VAT or nothing if this one is not set for the partner"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_formatted_vat
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_formatted_vat
msgid "Computed field that will convert the given VAT number to the format {person_category:2}-{number:10}-{validation_number:1}"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_company.py:0
#, python-format
msgid "Could not change the AFIP Responsibility of this company because there are already accounting entries."
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_country
msgid "Country"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__create_date
msgid "Created on"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_currency
msgid "Currency"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_currency_rate
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_currency_rate
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_currency_rate
msgid "Currency Rate"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_dni
msgid "DNI"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "Date:"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_account_move__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_account_payment__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_res_company__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_afip_responsibility_type_id
msgid "Defined by AFIP to identify the type of responsibilities that a person or a legal entity could have and that impacts in the type of operations and requirements they need."
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position_template__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_identification_type__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_currency__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner_bank__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_uom_uom__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_document_type_filter
msgid "Document Letter"
msgstr ""

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_document_type_argentina
#: model:ir.ui.menu,name:l10n_ar.menu_document_type_argentina
msgid "Document Types"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_ET
msgid "ET"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Electronic Fiscal Bond - Online Invoice"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__2
#: model:ir.model.fields.selection,name:l10n_ar.selection__res_partner__l10n_ar_gross_income_type__exempt
msgid "Exempt"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Export Voucher - Billing Plus"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Export Voucher - Online Invoice"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_fiscal_position
msgid "Fiscal Position"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_formatted_vat
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_formatted_vat
msgid "Formatted VAT"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_legal_entity_vat
msgid "Generic VAT number defined by AFIP in order to recognize partners from this country that are legal entity"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_natural_vat
msgid "Generic VAT number defined by AFIP in order to recognize partners from this country that are natural persons"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_other_vat
msgid "Generic VAT number defined by AFIP in order to recognize partners from this country that are not natural persons or legal entities"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Go to Companies"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Go to Journals"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_gross_income_type
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_company_form
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_partner_property_form
msgid "Gross Income"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_gross_income_number
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_gross_income_number
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_gross_income_number
msgid "Gross Income Number"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_gross_income_type
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_gross_income_type
msgid "Gross Income Type"
msgstr ""

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_servicio_de_guarda
#: model:product.template,name:l10n_ar.product_product_servicio_de_guarda_product_template
msgid "Guard Service"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_chart_template__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position_template__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__id
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__id
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__id
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_identification_type__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_currency__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner_bank__id
#: model:ir.model.fields,field_description:l10n_ar.field_uom_uom__id
msgid "ID"
msgstr ""

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_iibb_purchases_by_state_and_account_pivot
#: model:ir.ui.menu,name:l10n_ar.menu_iibb_purchases_by_state_and_account
msgid "IIBB - Purchases by jurisdiction"
msgstr ""

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_iibb_sales_by_state_and_account_pivot
#: model:ir.ui.menu,name:l10n_ar.menu_iibb_sales_by_state_and_account
msgid "IIBB - Sales by jurisdiction"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "IIBB:"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_l10n_latam_identification_type
msgid "Identification Types"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_fiscal_position.py:0
#, python-format
msgid "If use AFIP Responsibility then the country / zip codes will be not take into account"
msgstr ""

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_quote_despacho
#: model:product.template,name:l10n_ar.product_product_quote_despacho_product_template
msgid "Import Clearance"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_impuestos_internos
msgid "Internal Taxes"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "Invalid length for \"%s\""
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_invoice_report
msgid "Invoices Statistics"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_LC
msgid "LC"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_LE
msgid "LE"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position_template____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_move____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_identification_type____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_company____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_country____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_currency____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner_bank____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_uom_uom____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_legal_entity_vat
msgid "Legal Entity VAT"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__l10n_ar_letter
msgid "Letters"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_l10n_latam_document_type__l10n_ar_letter
msgid "Letters defined by the AFIP that can be used to identify the documents presented to the government and that depends on the operation type, the responsibility of both the issuer and the receptor of the document"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_fiscal_position__l10n_ar_afip_responsibility_type_ids
msgid "List of AFIP responsibilities where this fiscal position should be auto-detected"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__res_partner__l10n_ar_gross_income_type__local
msgid "Local"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "Logo"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Missing Partner Configuration"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__res_partner__l10n_ar_gross_income_type__multilateral
msgid "Multilateral"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_percepcion_municipal
msgid "Municipal Taxes Perceptions"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__name
msgid "Name"
msgstr ""

#. module: l10n_ar
#: model:ir.model.constraint,message:l10n_ar.constraint_l10n_ar_afip_responsibility_type_name
msgid "Name must be unique!"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_natural_vat
msgid "Natural Person VAT"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "No VAT configured for partner [%i] %s"
msgstr ""

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_cero
#: model:product.template,name:l10n_ar.product_product_cero_product_template
msgid "Non-industrialized animals and vegetables (VAT Zero)"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__0
msgid "Not Applicable"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__l10n_latam_document_type__purchase_aliquots__not_zero
msgid "Not Zero"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "Nro:"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_company_form
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_partner_property_form
msgid "Number..."
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "On invoice id \"%s\" you must use VAT Not Applicable on every line."
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "On invoice id \"%s\" you must use VAT taxes different than VAT Not Applicable."
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Online Invoice"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "Only numbers allowed for \"%s\""
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_otras_percepciones
msgid "Other Perceptions"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_special_purchase_document_type_ids
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_special_purchase_document_type_ids
msgid "Other Purchase Documents"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_other_vat
msgid "Other VAT"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__company_partner
msgid "Partner"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Please configure the AFIP Responsibility for \"%s\" in order to continue"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Please define a valid AFIP POS number (5 digits max)"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Please define an AFIP POS number"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Pre-printed Invoice"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Product Coding - Online Voucher"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_percepcion_ganancias
msgid "Profit Perceptions"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__purchase_aliquots
msgid "Purchase Aliquots"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_l10n_latam_document_type__purchase_aliquots
msgid "Raise an error if a vendor bill is miss encoded. \"Not Zero\" means the VAT taxes are required for the invoices related to this document type, and those with \"Zero\" means that only \"VAT Not Applicable\" tax is allowed."
msgstr ""

#. module: l10n_ar
#: model:ir.ui.menu,name:l10n_ar.menu_afip_responsibility_type
msgid "Responsibility Types"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__sequence
msgid "Sequence"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_sequence_ids
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_sequence_ids
msgid "Sequences"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_move_form
msgid "Service Date"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_special_purchase_document_type_ids
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_special_purchase_document_type_ids
msgid "Set here if this partner can issue other documents further than invoices, credit notes and debit notes"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_Sigd
msgid "Sigd"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__l10n_ar_state_id
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "State"
msgstr ""

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_tasa_estadistica
#: model:product.template,name:l10n_ar.product_product_tasa_estadistica_product_template
msgid "Statistics Rate"
msgstr ""

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_arancel
#: model:product.template,name:l10n_ar.product_product_arancel_product_template
msgid "Tariff"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_tax_group
msgid "Tax Group"
msgstr ""

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_telefonia
#: model:product.template,name:l10n_ar.product_product_telefonia_product_template
msgid "Telephone service (VAT 27)"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "The document number can not be changed for this journal, you can only modify the POS number if there is not posted (or posted before) invoices"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/l10n_latam_document_type.py:0
#, python-format
msgid ""
"The document number must be entered with a dash (-) and a maximum of 5 characters for the first partand 8 for the second. The following are examples of valid numbers:\n"
"* 1-1\n"
"* 0001-********\n"
"* 00001-********"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/l10n_latam_document_type.py:0
#, python-format
msgid "The number of import Dispatch must be 16 characters"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "The selected Journal can't be used in this transaction, please select one that doesn't use documents as these are just for Invoices."
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "The validation digit is not valid for \"%s\""
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "There must be one and only one VAT tax per line. Check line \"%s\""
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_afip_code
#: model:ir.model.fields,help:l10n_ar.field_res_currency__l10n_ar_afip_code
#: model:ir.model.fields,help:l10n_ar.field_uom_uom__l10n_ar_afip_code
msgid "This code will be used on electronic invoice"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_company__l10n_ar_gross_income_number
#: model:ir.model.fields,help:l10n_ar.field_res_company__l10n_ar_gross_income_type
msgid "This field is required in order to print the invoice report properly"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_partner_id
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_afip_pos_partner_id
msgid "This is the address used for invoice reports of this POS"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_number
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_afip_pos_number
msgid "This is the point of sale number assigned by AFIP in order to generate invoices"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__l10n_ar_tribute_afip_code
msgid "Tribute AFIP Code"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_gross_income_type
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_gross_income_type
msgid "Type of gross income: exempt, local, multilateral"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_share_sequences
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_share_sequences
msgid "Unified Book"
msgstr ""

#. module: l10n_ar
#: model:product.product,uom_name:l10n_ar.product_product_arancel
#: model:product.product,uom_name:l10n_ar.product_product_cero
#: model:product.product,uom_name:l10n_ar.product_product_exento
#: model:product.product,uom_name:l10n_ar.product_product_no_gravado
#: model:product.product,uom_name:l10n_ar.product_product_quote_despacho
#: model:product.product,uom_name:l10n_ar.product_product_servicio_de_guarda
#: model:product.product,uom_name:l10n_ar.product_product_tasa_estadistica
#: model:product.product,uom_name:l10n_ar.product_product_telefonia
#: model:product.template,uom_name:l10n_ar.product_product_arancel_product_template
#: model:product.template,uom_name:l10n_ar.product_product_cero_product_template
#: model:product.template,uom_name:l10n_ar.product_product_exento_product_template
#: model:product.template,uom_name:l10n_ar.product_product_no_gravado_product_template
#: model:product.template,uom_name:l10n_ar.product_product_quote_despacho_product_template
#: model:product.template,uom_name:l10n_ar.product_product_servicio_de_guarda_product_template
#: model:product.template,uom_name:l10n_ar.product_product_tasa_estadistica_product_template
#: model:product.template,uom_name:l10n_ar.product_product_telefonia_product_template
msgid "Units"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__1
msgid "Untaxed"
msgstr ""

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_no_gravado
#: model:product.template,name:l10n_ar.product_product_no_gravado_product_template
msgid "Untaxed concepts (VAT NT)"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_UpApP
msgid "UpApP"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_share_sequences
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_share_sequences
msgid "Use same sequence for documents with the same letter"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_vat
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_vat
msgid "VAT"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_0
msgid "VAT 0%"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_105
msgid "VAT 10.5%"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_025
msgid "VAT 2,5%"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_21
msgid "VAT 21%"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_27
msgid "VAT 27%"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_5
msgid "VAT 5%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__l10n_ar_vat_afip_code
msgid "VAT AFIP Code"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_exento
msgid "VAT Exempt"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_no_corresponde
msgid "VAT Not Applicable"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_percepcion_iva
msgid "VAT Perception"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_no_gravado
msgid "VAT Untaxed"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_fiscal_position.py:0
#, python-format
msgid "Warning"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "We were not able to sanitize the identification number"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "With Document"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "You are trying to create an invoice for domestic partner but you don't have a domestic market journal"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "You are trying to create an invoice for foreign partner but you don't have an exportation journal"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_chart_template.py:0
#, python-format
msgid "You are trying to install a chart of account for the %s responsibility but your company is configured as %s type"
msgstr ""

#. module: l10n_ar
#, python-format
msgid "You can not change the journal's configuration if it already has validated invoices"
msgstr ""

#. module: l10n_ar
#, python-format
msgid "You can not change the journal's configuration if journal already have validated invoices"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__l10n_latam_document_type__purchase_aliquots__zero
msgid "Zero"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/l10n_latam_document_type.py:0
#, python-format
msgid "is not a valid value for"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_move_form
msgid "to"
msgstr ""
