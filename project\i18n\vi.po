# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>g <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>hi <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Vo Thanh Thuy, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project
#. openerp-web
#: code:addons/project/static/src/project_sharing/components/chatter.xml:0
#, python-format
msgid "!!widget.options.res_id && widget.get('messages') || []"
msgstr "!!widget.options.res_id && widget.get('messages') || []"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"Refused\""
msgstr "\"Đã từ chối\""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"This Month\""
msgstr "\"Tháng này\""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"This Week\""
msgstr "\"Tuần này\""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"Today\""
msgstr "\"Hôm nay\""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_count
msgid "# Collaborators"
msgstr "# Cộng tác viên"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "# Đánh giá"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner__task_count
#: model:ir.model.fields,field_description:project.field_res_users__task_count
msgid "# Tasks"
msgstr "SL nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__nb_tasks
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
msgid "# of Tasks"
msgstr "Số nhiệm vụ"

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (bản sao)"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "%s Use the %s icon to organize your daily activities."
msgstr "%s Dùng biểu tượng %s để tổ chức công việc hàng ngày của bạn."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ %(child_count)s tasks)"
msgstr "(+ %(child_count)s nhiệm vụ)"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ 1 task)"
msgstr "(+ 1 nhiệm vụ)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(tới hạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(cập nhật dự án cuối cùng),"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- đạt được vào"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__10
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__10
msgid "10"
msgstr "10"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__11
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__11
msgid "11"
msgstr "11"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__12
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__12
msgid "12"
msgstr "12"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__13
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__13
msgid "13"
msgstr "13"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__14
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__14
msgid "14"
msgstr "14"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__15
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__15
msgid "15"
msgstr "15"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__16
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__16
msgid "16"
msgstr "16"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__17
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__17
msgid "17"
msgstr "17"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__18
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__18
msgid "18"
msgstr "18"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__19
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__19
msgid "19"
msgstr "19"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__20
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__20
msgid "20"
msgstr "20"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__21
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__21
msgid "21"
msgstr "21"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__22
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__22
msgid "22"
msgstr "22"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__23
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__23
msgid "23"
msgstr "23"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__24
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__24
msgid "24"
msgstr "24"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__25
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__25
msgid "25"
msgstr "25"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__26
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__26
msgid "26"
msgstr "26"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__27
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__27
msgid "27"
msgstr "27"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__28
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__28
msgid "28"
msgstr "28"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__29
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__29
msgid "29"
msgstr "29"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__30
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__30
msgid "30"
msgstr "30"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__31
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__31
msgid "31"
msgstr "31"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr "<b>Kéo &amp; thả</b> các thẻ để thay đổi nhiệm vụ giữa các giai đoạn."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this task won't be notified \n"
"    of the note you are logging unless you specifically tag them)</i>. Use @ <b>mentions</b> to ping a colleague \n"
"    or # <b>mentions</b> to reach an entire team."
msgstr ""
"<b>Tạo ghi chú</b> để giao tiếp nội bộ <i>(những người theo dõi nhiệm vụ này sẽ không được thông báo \n"
"    về ghi chú bạn đã lưu trừ khi bạn gắn thẻ cho họ)</i>. Dùng @ <b>đề cập</b> để thông báo cho đồng nghiệp \n"
"    hoặc # <b>đề cập</b> để thông báo cho cả đội."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid ""
"<br/>\n"
"                                            <span class=\"fa fa-lock text-muted\"/><span class=\"text-muted\"> Private</span>"
msgstr ""
"<br/>\n"
"                                            <span class=\"fa fa-lock text-muted\"/><span class=\"text-muted\"> Riêng tư</span>"

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object.rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object.rating_get_partner_id()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/><br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the task \"<strong t-out=\"object.name or ''\">Planning and budget</strong>\"\n"
"            <t t-if=\"object.rating_get_rated_partner_id().name\">\n"
"                assigned to <strong t-out=\"object.rating_get_rated_partner_id().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Tell us how you feel about our service</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us to improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your task has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In progress</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">Weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object.rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object.rating_get_partner_id()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Xin chào,<br/><br/>\n"
"            </t>\n"
"            Vui lòng dành chút thời gian để đánh giá dịch vụ liên quan tới nhiệm vụ \"<strong t-out=\"object.name or ''\">Kế hoạch và ngân sách</strong>\"\n"
"            <t t-if=\"object.rating_get_rated_partner_id().name\">\n"
"                được phân công cho <strong t-out=\"object.rating_get_rated_partner_id().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Hãy cho chúng tôi biết cảm nghĩ của bạn về nhiệm vụ</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(bấm vào một trong các biểu tượng mặt ngườ này)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Hài lòng\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Ok\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Không hài lòng\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            Chúng tôi rất trân trọng phản hồi của bạn vì nhờ đó chúng tôi có thể liên tục cải tiến dịch vụ của mình.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">Khảo sát khách hàng được gửi vì nhiệm vụ của bạn đã được chuyển tới giai đoạn <b t-out=\"object.stage_id.name or ''\">Đang tiến hành</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">Khảo sát khách hàng được gửi <b t-out=\"object.project_id.rating_status_period or ''\">Hàng tuần</b> nếu nhiệm vụ ở giai đoạn <b t-out=\"object.stage_id.name or ''\">Đang tiến hành</b>.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"    Thank you for your enquiry.<br/>\n"
"    If you have any questions, please let us know.\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    Thân gửi <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"    Cảm ơn bạn đã gửi thắc mắc.<br/>\n"
"    Nếu có bất kỳ câu hỏi nào, hãy liên hệ với chúng tôi.\n"
"    <br/><br/>\n"
"    Xin cảm ơn,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Quản lý\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Arrow\" attrs=\"{'invisible': [('date_start', '=', False), ('date', '=', False)]}\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"Mũi tên\"/>\n"
"                                <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Mũi tên\" attrs=\"{'invisible': [('date_start', '=', False), ('date', '=', False)]}\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentage of satisfaction\"/>"
msgstr ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Phần trăm hài lòng\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Khách hàng bị tắt trong dự án\"/><b> Đánh"
" giá của khách</b> bị tắt cho (các) dự án sau : <br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"o_project_task_project_field text-danger\" attrs=\"{'invisible': "
"[('project_id', '!=', False)]}\">Private</i>"
msgstr ""
"<i class=\"o_project_task_project_field text-danger\" attrs=\"{'invisible': "
"[('project_id', '!=', False)]}\">Riêng tư</i>"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "<p><em>Number of tasks: %(tasks_count)s</em></p>"
msgstr "<p><em>Số lượng nhiệm vụ: %(tasks_count)s</em></p>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-right\">Stage:</small>"
msgstr "<small class=\"text-right\">Giai đoạn:</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<span attrs=\"{'invisible': ['|', ('repeat_show_week', '=', False), "
"('repeat_show_month', '=', False)]}\">of</span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('repeat_show_week', '=', False), "
"('repeat_show_month', '=', False)]}\">của</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o mr-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o mr-2\" title=\"Ngày\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o mr-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""
"<span class=\"fa fa-envelope-o mr-2\" aria-label=\"Domain Alias\" title=\"Bí"
" danh miền\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user mr-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user mr-2\" aria-label=\"Partner\" title=\"Đối tác\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Collaborators\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Cộng tác viên\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Customer Satisfaction\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Sự hài lòng của khách hàng\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Milestones\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Mốc thời gian\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Burndown Chart\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Biểu đồ chi tiết\n"
"                            </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Blocking</span>"
msgstr "<span class=\"o_stat_text\">Đang chặn</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr "<span class=\"o_stat_text\">Tổng biên</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">in Recurrence</span>"
msgstr "<span class=\"o_stat_text\">trong Định kỳ</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"oe_read_only\" attrs=\"{'invisible': [('alias_name', '!=', False)]}\">Create tasks by sending an email to </span>\n"
"                                        <span class=\"font-weight-bold oe_read_only\" attrs=\"{'invisible': [('alias_name', '=', False)]}\">Create tasks by sending an email to </span>\n"
"                                        <span class=\"font-weight-bold oe_edit_only\">Create tasks by sending an email to </span>"
msgstr ""
"<span class=\"oe_read_only\" attrs=\"{'invisible': [('alias_name', '!=', False)]}\">Tạo nhiệm vụ bằng cách gửi email tới </span>\n"
"                                        <span class=\"font-weight-bold oe_read_only\" attrs=\"{'invisible': [('alias_name', '=', False)]}\">Tạo nhiệm vụ bằng cách gửi email tới </span>\n"
"                                        <span class=\"font-weight-bold oe_edit_only\">Tạo nhiệm vụ bằng cách gửi email tới </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "<span>Are you sure you want to delete this project ?</span>"
msgstr "<span>Bạn có chắc chắn muốn xóa dự án này không?</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Báo cáo</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>Xem</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid ""
"<span>You cannot delete a project containing tasks. You can either archive "
"it or first delete all of its tasks.</span>"
msgstr ""
"<span>Bạn không thể xóa một dự án chứa các nhiệm vụ. Bạn có thể lưu trữ hoặc"
" trước tiên xóa tất cả các nhiệm vụ của dự án.</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr "<strong class=\"d-block mb-2\">Đính kèm</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Assignees</strong>"
msgstr "<strong>Người được phân công</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Customer</strong>"
msgstr "<strong>Khách hàng</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>Hạn chót:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Description</strong>"
msgstr "<strong>Mô tả</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Message and communication history</strong>"
msgstr "<strong>Lịch sử tin nhắn và liên lạc</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_planned_hours_template
msgid "<strong>Planned Hours:</strong>"
msgstr "<strong>Số giờ dự kiến:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>Dự án:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>Mốc thời gian</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Một từ điển Python sẽ được đánh giá để cung cấp giá trị mặc định khi tạo hồ "
"sơ mới cho bí danh này. "

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""
"Không thể chọn một cộng tác viên hai lần trong truy cập chia sẻ dự án. Vui "
"lòng xóa trùng lặp và thử lại."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr ""
"Không thể liên kết một giai đoạn cá nhân với một dự án vì nó chỉ hiển thị "
"với người dùng tương ứng."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "Một nhiệm vụ chỉ được có một giai đoạn cá nhân cho mỗi người dùng."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "Chấp nhận email đến từ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_mode
msgid "Access Mode"
msgstr "Chế độ truy cập"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "Cảnh báo truy cập"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "Cần tác vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__active
#: model:ir.model.fields,field_description:project.field_project_project_stage__active
#: model:ir.model.fields,field_description:project.field_project_task__active
#: model:ir.model.fields,field_description:project.field_project_task_type__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "Hoạt động"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Thể hiện hoạt động ngoại lệ "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng kiểu hoạt động"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "Kiểu hoạt động"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#, python-format
msgid "Add Milestone"
msgstr "Thêm mốc thời gian"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Add a description..."
msgstr "Thêm mô tả..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add a note"
msgstr "Thêm ghi chú"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""
"Thêm các cột để sắp xếp các nhiệm vụ của bạn thành <b>các giai đoạn</b> "
"<i>Ví dụ: Mới - Đang tiến hành - Đã xong</i>."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add contacts to share the project..."
msgstr "Thêm liên hệ để chia sẻ dự án..."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "Thêm nội dung để hiển thị trong email"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Add your task once it is ready."
msgstr "Thêm nhiệm vụ của bạn khi đã sẵn sàng."

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "Quản trị viên"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Agile Scrum"
msgstr "Agile Scrum"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "Bí danh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "Bảo mật bí danh liên hệ "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "Tên bí danh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias domain"
msgstr "Tên miền bí danh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_value
msgid "Alias email"
msgstr "Email bí danh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "Mô hình bí danh"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Tất cả"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All employees"
msgstr "Tất cả nhân viên"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__all
msgid "All tasks"
msgstr "Tất cả nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allow_subtasks
msgid "Allow Sub-tasks"
msgstr "Cho phép nhiệm vụ phụ"

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_id
#: model:ir.model.fields,field_description:project.field_project_task__analytic_account_id
msgid "Analytic Account"
msgstr "Tài khoản phân tích"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__analytic_tag_ids
msgid "Analytic Tag"
msgstr "Thẻ phân tích"

#. module: project
#: model:ir.model,name:project.model_account_analytic_tag
msgid "Analytic Tags"
msgstr "Thẻ phân tích"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__analytic_account_id
#: model:ir.model.fields,help:project.field_project_task__project_analytic_account_id
msgid ""
"Analytic account to which this project is linked for financial management. "
"Use an analytic account to record cost and revenue on your project."
msgstr ""
"Tài khoản phân tích liên kết với dự án này để quản lý tài chính. Sử dụng tài"
" khoản phân tích để ghi lại chi phí và doanh thu của dự án."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__analytic_account_id
msgid ""
"Analytic account to which this task is linked for financial management. Use "
"an analytic account to record cost and revenue on your task. If empty, the "
"analytic account of the project will be used."
msgstr ""
"Tài khoản phân tích liên kết với nhiệm vụ này để quản lý tài chính. Sử dụng "
"tài khoản phân tích để ghi lại chi phí và doanh thu của nhiệm vụ. Nếu để "
"trống, tài khoản phân tích của dự án sẽ được sử dụng."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "Phân tích"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "Analyze the performance of your tasks and your workers."
msgstr "Phân tích hiệu suất của nhiệm vụ và của nhân viên."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__april
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__april
msgid "April"
msgstr "Tháng Tư"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Archive"
msgstr "Lưu trữ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "Lưu trữ các giai đoạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "Archive project"
msgstr "Lưu trữ dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Archived"
msgstr "Đã lưu"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "Bạn có chắc chắn muốn tiếp tục không?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete those stages ?"
msgstr "Bạn có chắc chắn muốn xóa các giai đoạn đó không?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "Mũi tên"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "Biểu tượng mũi tên"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assembling"
msgstr "Lắp ráp"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Assign to Me"
msgstr "Phân công cho tôi"

#. module: project
#: model_terms:ir.ui.view,help:project.project_sharing_project_task_view_kanban
msgid " assignee"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,help:project.project_sharing_project_task_view_kanban
msgid " assignees"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assigned"
msgstr "Đã phân công"

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr "Nhiệm vụ đã được phân công"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "Phân công cho"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Assignees"
msgstr "Người được phân công"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "Ngày phân công"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "Ngày phân công"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
msgid "At Risk"
msgstr "Rủi ro"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"At each stage, employees can block tasks or mark them as ready for the next step.\n"
"                                    You can customize here the labels for each state."
msgstr ""
"Ở mỗi giai đoạn, nhân viên có thể chặn nhiệm vụ hoặc đánh dấu là sẵn sàng cho bước tiếp theo.\n"
"                                    Bạn có thể tùy chỉnh nhãn cho mỗi trạng thái tại đây."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tập tin đính kèm"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message."
msgstr "Tệp đính kèm không gắn với tin nhắn."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__august
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__august
msgid "August"
msgstr "Tháng Tám"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "Tác giả"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "Tự động tạo nhiệm vụ cho các hoạt động thường xuyên"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_kanban_state
msgid "Automatic kanban status"
msgstr "Trạng thái kanban tự động"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_kanban_state
msgid ""
"Automatically modify the kanban state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the kanban state to 'ready for the new stage' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'blocked' (red bullet).\n"
msgstr ""
"Tự động điều chỉnh trạng thái kanban khi khách hàng trả lời phản hồi cho giai đoạn này.\n"
" * Phản hồi tốt từ khách hàng sẽ cập nhật trạng thái kanban thành 'sẵn sàng cho giai đoạn kế tiếp' (nút xanh lá).\n"
" * Phản hồi trung tính hoặc xấu sẽ đặt trạng thái kanban thành 'bị chặn' (nút đỏ).\n"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Backlog"
msgstr "Chưa thực hiện"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "Số dư"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__dependent_ids
msgid "Block"
msgstr "Chặn"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__blocked
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__blocked
#: model:project.task,legend_blocked:project.project_task_1
#: model:project.task,legend_blocked:project.project_task_12
#: model:project.task,legend_blocked:project.project_task_2
#: model:project.task,legend_blocked:project.project_task_20
#: model:project.task,legend_blocked:project.project_task_24
#: model:project.task,legend_blocked:project.project_task_25
#: model:project.task,legend_blocked:project.project_task_26
#: model:project.task,legend_blocked:project.project_task_3
#: model:project.task,legend_blocked:project.project_task_30
#: model:project.task,legend_blocked:project.project_task_31
#: model:project.task,legend_blocked:project.project_task_32
#: model:project.task,legend_blocked:project.project_task_33
#: model:project.task,legend_blocked:project.project_task_8
#: model:project.task,legend_blocked:project.project_task_9
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_6
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_6
#: model:project.task.type,legend_blocked:project.project_stage_0
#: model:project.task.type,legend_blocked:project.project_stage_2
#: model:project.task.type,legend_blocked:project.project_stage_3
#, python-format
msgid "Blocked"
msgstr "Đã chặn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "Bị chặn bởi"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Brainstorm"
msgstr "Động não"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_pivot
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Burndown Chart"
msgstr "Sơ đồ chi tiết"

#. module: project
#: model:project.task,legend_done:project.project_task_10
#: model:project.task,legend_done:project.project_task_11
#: model:project.task,legend_done:project.project_task_19
#: model:project.task,legend_done:project.project_task_21
#: model:project.task,legend_done:project.project_task_22
#: model:project.task,legend_done:project.project_task_27
#: model:project.task,legend_done:project.project_task_28
#: model:project.task,legend_done:project.project_task_29
#: model:project.task,legend_done:project.project_task_34
#: model:project.task,legend_done:project.project_task_35
#: model:project.task,legend_done:project.project_task_36
#: model:project.task,legend_done:project.project_task_4
#: model:project.task,legend_done:project.project_task_5
#: model:project.task,legend_done:project.project_task_6
#: model:project.task,legend_done:project.project_task_7
#: model:project.task.type,legend_done:project.project_stage_1
msgid "Buzz or set as done"
msgstr "Buzz hoặc đặt là hoàn tất"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Cancel"
msgstr "Hủy"

#. module: project
#: code:addons/project/models/project.py:0
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#, python-format
msgid "Canceled"
msgstr "Đã huỷ"

#. module: project
#: model:project.task.type,name:project.project_stage_3
msgid "Cancelled"
msgstr "Đã hủy"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__partner_is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr "Đánh dấu nếu liên hệ này là một công ty, nếu không sẽ coi là cá nhân"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__child_text
msgid "Child Text"
msgstr "Văn bản con"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the name of a customer,\n"
"     of a product, of a team, of a construction site, etc.</i>"
msgstr ""
"Chọn một <b>tên</b> cho dự án. <i>Có thể là bất kỳ tên nào bạn muốn: tên của khách hàng,\n"
"     sản phẩm, một đội, hay tên một công trường, v.v.</i>"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr "Chọn <b>tên</b> nhiệm vụ <i>(VD: Thiết kế web, Mua đồ...)</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_city
msgid "City"
msgstr "Thành phố"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Client Review"
msgstr "Đánh giá của khách hàng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_closed
#: model:ir.model.fields,field_description:project.field_project_task_type__is_closed
msgid "Closing Stage"
msgstr "Giai đoạn kết thúc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
msgid "Collaborator"
msgstr "Cộng tác viên"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_ids
#, python-format
msgid "Collaborators"
msgstr "Cộng tác viên"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "Cộng tác viên dự án đã chia sẻ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
#: model:ir.model.fields,field_description:project.field_project_update__color
msgid "Color"
msgstr "Màu sắc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__color
#: model:ir.model.fields,field_description:project.field_project_task__color
msgid "Color Index"
msgstr "Mã màu"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__commercial_partner_id
#: model:ir.model.fields,field_description:project.field_project_task__commercial_partner_id
msgid "Commercial Entity"
msgstr "Thực thể thương mại"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo "
"designs to the task, so that information flows from designers to the workers"
" who print the t-shirt. Organize priorities amongst orders using the %s "
"icon. %s"
msgstr ""
"Giao tiếp với khách hàng về nhiệm vụ bằng cách dùng cổng thông tin email. "
"Gắn thiết kế logo vào nhiệm vụ để thông tin được thông suốt từ nhà thiết kế "
"tới nhân viên xưởng in áo phông. Tổ chức đơn hàng theo độ ưu tiên bằng cách "
"dùng biểu tượng %s. %s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
msgid "Company"
msgstr "Công ty"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "Cấu hình"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "Định cấu hình giai đoạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Confirm"
msgstr "Xác nhận"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#, python-format
msgid "Confirmation"
msgstr "Xác nhận"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Consulting"
msgstr "Tư vấn"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "Liên hệ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Continue Recurrence"
msgstr "Tiếp tục định kỳ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "Viết quảng cáo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "Ảnh bìa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create"
msgstr "Tạo"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "Ngày tạo"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "Tạo một dự án"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "Tạo một giai đoạn mới trong quy trình thực hiện nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "Tạo công việc bằng cách gửi email tới"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "Được tạo vào"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Creation Date"
msgstr "Ngày tạo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "Dự án hiện tại của nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current stage of the task"
msgstr "Giai đoạn hiện tại của nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "Giai đoạn hiện tại của nhiệm vụ này"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Tin nhắn bị trả lại tùy chỉnh"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Customer"
msgstr "Khách hàng"

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customer Email"
msgstr "Email khách hàng"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Customer Feedback"
msgstr "Phản hồi khách hàng"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "URL cổng thông tin khách hàng"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "Đánh giá khách hàng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "Trạng thái đánh giá khách hàng"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
msgid "Customer Ratings on Task"
msgstr "Đánh giá của khách hàng về nhiệm vụ"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customer Satisfaction"
msgstr "Sự hài lòng của khách hàng"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and "
"you can communicate on the task directly. Your managers decide which "
"feedback is accepted %s and which feedback is moved to the %s column. %s"
msgstr ""
"Khách hàng đề xuất phản hồi qua email; Odoo tạo nhiệm vụ tự động và bạn có "
"thể giao tiếp trực tiếp về nhiệm vụ. Người quản lý của bạn quyết định phản "
"hồi nào được chấp nhận %s và phản hồi được chuyển đến cột %s. %s"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Customize how tasks are named according to the project and create tailor "
"made status messages for each step of the workflow. It helps to document "
"your workflow: what should be done at which step."
msgstr ""
"Tùy chỉnh cách đặt tên nhiệm vụ theo dự án và tạo thông báo trạng thái được "
"thiết kế riêng cho từng bước của quy trình làm việc. Việc này giúp ghi lại "
"quy trình làm việc của bạn: những gì nên làm ở bước nào."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "Hàng ngày"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
msgid "Date"
msgstr "Ngày"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_group_by
msgid "Date Group By"
msgstr "Ngày nhóm theo"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__date
msgid "Date of the Month"
msgstr "Ngày trong tháng"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__date
msgid "Date of the Year"
msgstr "Ngày trong năm"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Dates"
msgstr "Ngày"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_weekday
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_weekday
msgid "Day Of The Week"
msgstr "Thứ trong tuần"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__day
msgid "Day of the Month"
msgstr "Thứ trong tháng"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__day
msgid "Day of the Year"
msgstr "Thứ trong năm"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "Thứ"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "Số ngày tới hạn"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Deadline"
msgstr "Hạn chót"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "Gửi"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__december
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__december
msgid "December"
msgstr "Tháng mười hai"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "Giá trị mặc định"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"Xác định các bước sẽ sử dụng trong dự án\n"
"                từ tạo nhiệm vụ tới đóng nhiệm vụ hoặc sự cố.\n"
"                Bạn sẽ sử dụng giai đoạn để theo dõi tiến trình\n"
"                hoặc xử lý nhiệm vụ hoặc sự cố."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"Những người có thể thấy được dự án này và các nhiệm vụ dự án.\n"
"\n"
"- Người dùng nội bộ được mời: khi theo dõi một dự án, người dùng nội bộ sẽ có quyền truy cập vào tất cả nhiệm vụ dự án mà không có sự phân biệt. Nếu không, họ sẽ chỉ có quyền truy cập vào các nhiệm vụ nhất định mà họ đang theo dõi.\n"
"Người dùng có cấp độ quyền truy cập quản trị viên dự án vẫn có thể truy cập dự án này và các nhiệm vụ dự án, ngay cả khi rõ ràng họ không phải là một người theo dõi.\n"
"\n"
"- Tất cả người dùng nội bộ: tất cả người dùng nội bộ đều có thể truy cập vào dự án và tất cả nhiệm vụ dự án mà không có sự phân biệt.\n"
"\n"
"- Người dùng cổng thông tin được mời và tất cả người dùng nội bộ: tất cả người dùng nội bộ có thể truy cập dự án và tất cả nhiệm vụ dự án mà không có sự phân biệt.\n"
"Khi theo dõi một dự án, người dùng cổng thông tin sẽ có quyền truy cập vào tất cả nhiệm vụ dự án mà không có sự phân biệt. Nếu không, họ sẽ chỉ có quyền truy cập vào các nhiệm vụ nhất định mà họ đang theo dõi.\n"
"\n"
"Khi một dự án được chia sẻ ở chế độ chỉ đọc, người dùng cổng thông tin sẽ được chuyển hướng đến cổng thông tin của họ. Họ có thể xem nhiệm vụ nhưng không thể chỉnh sửa chúng.\n"
"Khi một dự án được chia sẻ trong chế độ chỉnh sửa, người dùng cổng thông tin sẽ được chuyển hướng đến chế độ xem kanban và chế độ xem danh sách của nhiệm vụ. Họ có thể sửa đổi một số trường đã chọn trong nhiệm vụ.\n"
"\n"
"Trong mọi trường hợp, nếu người dùng nội bộ không có quyền truy cập dự án nhưng được cung cấp URL nhiệm vụ (và họ là một người theo dõi, nếu dự án ở chế độ riêng tư), thì họ vẫn có thể truy cập nhiệm vụ tương ứng."

#. module: project
#: model:ir.actions.server,name:project.unlink_project_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Delete"
msgstr "Xoá"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#, python-format
msgid "Delete Milestone"
msgstr "Xóa mốc"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "Delete Project"
msgstr "Xóa dự án"

#. module: project
#: code:addons/project/models/project.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Delete Stage"
msgstr "Xoá giai đoạn"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Delivered"
msgstr "Đã giao"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__dependent_tasks_count
#, python-format
msgid "Dependent Tasks"
msgstr "Nhiệm vụ phụ thuộc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_task_type__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "Mô tả"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Design"
msgstr "Thiết kế"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "Xác định thứ tự thực hiện nhiệm vụ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Development"
msgstr "Phát triển"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "Tóm tắt"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Digital Marketing"
msgstr "Tiếp thị số"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__disabled_rating_warning
msgid "Disabled Rating Warning"
msgstr "Tắt cảnh báo xếp hạng"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_access_mode
msgid "Display Access Mode"
msgstr "Hiển thị chế độ truy cập"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__display_project_id
msgid "Display Project"
msgstr "Hiển thị dự án"

#. module: project
#: code:addons/project/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Không có quyền truy cập, bỏ qua dữ liệu này cho email tóm tắt của người dùng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Documents"
msgstr "Tài liệu"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
#, python-format
msgid "Done"
msgstr "Hoàn thành"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Draft"
msgstr "Nháp"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Dropdown menu"
msgstr "Menu thả xuống"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "E.g: Product Launch"
msgstr "VD: Phát hành sản phẩm"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__edit
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Edit"
msgstr "Sửa"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_kanban.js:0
#, python-format
msgid "Edit Personal Stage"
msgstr "Chỉnh sửa giai đoạn cá nhân"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Edit recurring task"
msgstr "Sửa nhiệm vụ định kỳ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Edit tasks' description collaboratively in real time. See each author's text"
" in a distinct color."
msgstr ""
"Cộng tác chỉnh sửa mô tả nhiệm vụ cùng lúc. Văn bản của mỗi tác giả được "
"hiển thị một màu riêng biệt."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Editing"
msgstr "Đang chỉnh sửa"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__partner_email
#: model:ir.model.fields,field_description:project.field_project_task__partner_email
msgid "Email"
msgstr "Email"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_from
msgid "Email From"
msgstr "Email từ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "Mẫu email"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "Email CC"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "Ngày kết thúc"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "Error! Project start date must be before project end date."
msgstr "Lỗi! Ngày bắt đầu dự án phải trước ngày kết thúc dự án."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "Lỗi! Bạn không thể tạo một hệ thống phân cấp nhiệm vụ đệ quy."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Everyone can propose ideas, and the Editor marks the best ones as %s. Attach"
" all documents or links to the task directly, to have all research "
"information centralized. %s"
msgstr ""
"Mọi người đều có thể nêu ý kiến, và Biên tập sẽ đánh dấu ý kiến hay nhất là "
"%s. Đính kèm tất cả tài liệu hoặc link trực tiếp với nhiệm vụ để mọi thông "
"tin nghiên cứu được tập trung tại một nơi. %s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "Ngày hết hạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Extended Filters"
msgstr "Bộ lọc mở rộng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "Thông tin thêm"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__february
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__february
msgid "February"
msgstr "Tháng Hai"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Fill your Inbox easily with the email gateway. Periodically review your "
"Inbox and schedule tasks by moving them to other columns. Every day, you "
"review the %s column to move important tasks %s. Every Monday, you review "
"the %s column. %s"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Final Document"
msgstr "Tài liệu cuối cùng"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__first
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__first
msgid "First"
msgstr "Đầu tiên"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "Thu gọn trong kanban"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr ""
"Theo dõi dự án này để tự động theo dõi các sự kiện liên quan đến nhiệm vụ và"
" các vấn đề phát sinh của dự án."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Followed"
msgstr "Đã theo dõi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "Followed Projects"
msgstr "Dự án được theo dõi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Biểu tượng Font awesome v.d: fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "Mãi mãi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__fri
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__fri
msgid "Fri"
msgstr "T6"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__fri
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__fri
msgid "Friday"
msgstr "Thứ sáu"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "Hoạt động tương lai"

#. module: project
#: model:ir.ui.menu,name:project.menu_tasks_config
msgid "GTD"
msgstr "GTD"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr ""
"Chụp ảnh trạng thái của dự án và chia sẻ tiến trình với các bên liên quan "
"chủ chốt."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback"
msgstr "Nhận phản hồi của khách hàng"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Getting Things Done (GTD)"
msgstr "Hoàn thành công việc"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__sequence
msgid "Gives the sequence order when displaying a list of Projects."
msgstr "Chỉ ra trình tự sắp xếp khi hiển thị danh sách dự án."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__sequence
msgid "Gives the sequence order when displaying a list of tasks."
msgstr "Chỉ ra trình tự sắp xếp khi hiển thị danh sách nhiệm vụ."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_done
msgid "Green Kanban Label"
msgstr "Nhãn kanban xanh"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_normal
msgid "Grey Kanban Label"
msgstr "Nhãn kanban xám"

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "Gross Margin"
msgstr "Tổng biên"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Group By"
msgstr "Nhóm theo"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks. Use the %s and %s to signalize what is the "
"current status of your Idea. %s"
msgstr ""
"Xử lý việc thu thập ý tưởng của bạn trong Nhiệm vụ của Dự án mới của bạn và "
"thảo luận chúng trong mục chatter về nhiệm vụ. Sử dụng %s và %s để báo hiệu "
"trạng thái hiện tại của ý tưởng của bạn. %s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Handoff"
msgstr "Ra tay"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "Mặt vui vẻ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__2
msgid "High"
msgstr "Cao"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "Giờ"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"How to get customer feedback?\n"
"- Rating when changing stage: an email will be sent when a task is pulled to another stage.\n"
"- Periodic rating: an email will be sent periodically.\n"
"\n"
"Don't forget to set up the email templates on the stages for which you want to get customer feedback."
msgstr ""
"Cách lấy phản hồi từ khách hàng?\n"
"- Đánh giá khi thay đổi giai đoạn: một email sẽ được gửi tới khi nhiệm vụ được kéo sang giai đoạn khác.\n"
"- Đánh giá định kỳ: một email sẽ được gửi định kỳ.\n"
"\n"
"Đừng quên thiết lập mẫu email cho các giai đoạn bạn muốn nhận phản hồi từ khách hàng."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "Dự án đang diễn ra như thế nào?"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "ID"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID của hồ sơ gốc chứa bí danh (ví dụ: dự án chứa bí danh để tạo nhiệm vụ)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng để chỉ ra một hoạt động ngoại lệ."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "Các ý tưởng"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_milestone__message_unread
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_unread
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_unread
#: model:ir.model.fields,help:project.field_project_update__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_unread
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, các tin nhắn mới yêu cầu sự chú ý của bạn. "

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu đánh dấu, một số tin nhắn bị lỗi khi gửi. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set and if the project's rating configuration is 'Rating when changing "
"stage', then an email will be sent to the customer when the task reaches "
"this step."
msgstr ""
"Nếu được đánh dấu và nếu cấu hình đánh giá của dự án là 'Đánh giá khi thay "
"đổi giai đoạn', thì email sẽ được gửi tới khách hàng khi nhiệm vụ đạt đến "
"bước này."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be sent to the customer when the project reaches this "
"step."
msgstr ""
"Nếu đặt, một email sẽ được gửi tới khách hàng khi dự án chuyển tới bước này."
" "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be sent to the customer when the task or issue reaches"
" this step."
msgstr ""
"Nếu đặt, một email sẽ được gửi tới khách hàng khi nhiệm vụ hoặc sự cố chuyển"
" tới bước này. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Nếu được đặt, nội dung này sẽ tự động được gửi đến người dùng chưa được cấp "
"quyền thay vì tin nhắn mặc định."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr ""
"Nếu trường hoạt động được đặt là Sai, bạn sẽ được phép ẩn dự án mà không cần"
" xóa."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
msgid "Important"
msgstr "Quan trọng"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__normal
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task,legend_normal:project.project_task_1
#: model:project.task,legend_normal:project.project_task_10
#: model:project.task,legend_normal:project.project_task_11
#: model:project.task,legend_normal:project.project_task_12
#: model:project.task,legend_normal:project.project_task_19
#: model:project.task,legend_normal:project.project_task_2
#: model:project.task,legend_normal:project.project_task_20
#: model:project.task,legend_normal:project.project_task_21
#: model:project.task,legend_normal:project.project_task_22
#: model:project.task,legend_normal:project.project_task_24
#: model:project.task,legend_normal:project.project_task_25
#: model:project.task,legend_normal:project.project_task_26
#: model:project.task,legend_normal:project.project_task_27
#: model:project.task,legend_normal:project.project_task_28
#: model:project.task,legend_normal:project.project_task_29
#: model:project.task,legend_normal:project.project_task_3
#: model:project.task,legend_normal:project.project_task_30
#: model:project.task,legend_normal:project.project_task_31
#: model:project.task,legend_normal:project.project_task_32
#: model:project.task,legend_normal:project.project_task_33
#: model:project.task,legend_normal:project.project_task_34
#: model:project.task,legend_normal:project.project_task_35
#: model:project.task,legend_normal:project.project_task_36
#: model:project.task,legend_normal:project.project_task_4
#: model:project.task,legend_normal:project.project_task_5
#: model:project.task,legend_normal:project.project_task_6
#: model:project.task,legend_normal:project.project_task_7
#: model:project.task,legend_normal:project.project_task_8
#: model:project.task,legend_normal:project.project_task_9
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_0
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_1
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_2
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_3
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_4
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_5
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_6
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_0
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_1
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_2
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_3
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_4
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_5
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_6
#: model:project.task.type,legend_normal:project.project_stage_0
#: model:project.task.type,legend_normal:project.project_stage_1
#: model:project.task.type,legend_normal:project.project_stage_2
#: model:project.task.type,legend_normal:project.project_stage_3
#: model:project.task.type,name:project.project_stage_1
#, python-format
msgid "In Progress"
msgstr "Đang tiến hành"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "In development"
msgstr "Đang phát triển"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
#, python-format
msgid "Inbox"
msgstr "Hộp thư đến"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__planned_hours
msgid "Initially Planned Hours"
msgstr "Số giờ dự kiến ban đầu"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"Email nội bộ liên kết với dự án này. Các email gửi đến sẽ được tự động đồng "
"bộ với các Nhiệm vụ (hoặc Sự cố nếu có cài đặt mô-đun Theo dõi sự cố)."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Invite People"
msgstr "Mời người"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited employees"
msgstr "Nhân viên được mời"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all employees"
msgstr "Người dùng cổng thông tin được mời và tất cả nhân viên"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_exceeded
msgid "Is Deadline Exceeded"
msgstr "Là quá hạn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_future
msgid "Is Deadline Future"
msgstr "Chưa tới hạn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_private
msgid "Is Private"
msgstr "Là riêng tư"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_is_company
msgid "Is a Company"
msgstr "Là một công ty"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "Phiên bản Phát sinh"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "It seems that some tasks are part of a recurrence."
msgstr "Có vẻ như một số nhiệm vụ thuộc thuộc nhóm định kỳ."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "It seems that this task is part of a recurrence."
msgstr "Có vẻ như nhiệm vụ này thuộc nhóm định kỳ."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__january
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__january
msgid "January"
msgstr "Tháng Một"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__july
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__july
msgid "July"
msgstr "Tháng Bảy"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__june
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__june
msgid "June"
msgstr "Tháng Sáu"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Giải thích kanban bị chặn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Giải thích kanban đang diễn ra"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Kanban State"
msgstr "Giai đoạn Kanban"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state_label
msgid "Kanban State Label"
msgstr "Nhãn trạng thái kanban"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_done
msgid "Kanban Valid Explanation"
msgstr "Giải thích kanban hợp lệ"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened_value
msgid "Kpi Project Task Opened Value"
msgstr "Giá trị Kpi của các việc trong dự án đã mở"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid "Label used for the tasks of the project."
msgstr "Nhãn được dùng cho các công việc trong dự án."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__last
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__last
msgid "Last"
msgstr "Gần nhất"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Last 30 Days"
msgstr "30 ngày gần đây"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator____last_update
#: model:ir.model.fields,field_description:project.field_project_delete_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_milestone____last_update
#: model:ir.model.fields,field_description:project.field_project_project____last_update
#: model:ir.model.fields,field_description:project.field_project_project_stage____last_update
#: model:ir.model.fields,field_description:project.field_project_share_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_tags____last_update
#: model:ir.model.fields,field_description:project.field_project_task____last_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report____last_update
#: model:ir.model.fields,field_description:project.field_project_task_recurrence____last_update
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_update____last_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "Tháng trước"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#, python-format
msgid "Last Stage Update"
msgstr "Cập nhật giai đoạn lần cuối"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_id
msgid "Last Update"
msgstr "Cập nhật gần nhất"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_color
msgid "Last Update Color"
msgstr "Màu cập nhật lần cuối"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_status
msgid "Last Update Status"
msgstr "Trạng thái cập nhật lần cuối"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "Cập nhật lần cuối vào"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "Hoạt động trễ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Tasks"
msgstr "Nhiệm vụ trễ"

#. module: project
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
#, python-format
msgid "Later"
msgstr "Để sau"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Dissatisfied"
msgstr "Đánh giá mới nhất: Không hài lòng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Okay"
msgstr "Đánh giá mới nhất: OK"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Satisfied"
msgstr "Đánh giá mới nhất: Hài lòng"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>project</b>."
msgstr "Hãy tạo <b>dự án</b> đầu tiên của bạn."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "Hãy tạo <b>nhiệm vụ</b> đầu tiên của bạn."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""
"Hãy quay lại <b>dạng xem kanban</b> để có cái nhìn tổng quan về các nhiệm vụ"
" tiếp theo."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's start working on your task."
msgstr "Hãy bắt tay vào thực hiện nhiệm vụ của bạn."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "Hãy đợi khách hàng của bạn tự biểu hiện."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
msgid "Link"
msgstr "Liên kết"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
#: model:ir.model.fields,help:project.field_project_update__email_cc
msgid "List of cc from incoming emails."
msgstr "Danh sách cc từ email đến. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Live"
msgstr "Trực tiếp"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Logo Design"
msgstr "Thiết kế logo"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Long Term"
msgstr "Dài hạn"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "Thấp"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_project__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_task__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_update__message_main_attachment_id
msgid "Main Attachment"
msgstr "Tệp đính kèm chính"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__attachment_ids
msgid "Main Attachments"
msgstr "Tệp đính kèm chính"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly "
"acquired projects, assign them and use the %s and %s to define if the "
"project is ready for the next step. %s"
msgstr ""
"Quản lý vòng đời dự án bằng dạng xem kanban. Thêm dự án mới nhận, phân công "
"và sử dụng %s và %s để xác định liệu dự án đã sẵn sàng cho bước tiếp theo "
"chưa. %s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Manufacturing"
msgstr "Sản xuất"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__march
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__march
msgid "March"
msgstr "Tháng Ba"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Material Sourcing"
msgstr "Nguồn lực"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__may
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__may
msgid "May"
msgstr "Tháng Năm"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__favorite_user_ids
msgid "Members"
msgstr "Thành viên"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "Gửi tin nhắn bị lỗi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/right_panel/project_utils.js:0
#: model:ir.model.fields,field_description:project.field_project_project__milestone_ids
#, python-format
msgid "Milestone"
msgstr "Mốc thời gian"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count
msgid "Milestone Count"
msgstr "Số mốc thời gian"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#: model:ir.actions.act_window,name:project.project_milestone_all
#, python-format
msgid "Milestones"
msgstr "Mốc thời gian"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Mixing"
msgstr "Phối hợp"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__mon
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__mon
msgid "Mon"
msgstr "T2"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__mon
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__mon
msgid "Monday"
msgstr "Thứ Hai"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "Tháng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Hạn chót hoạt động"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Favorite Projects"
msgstr "Dự án yêu thích"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "Mục yêu thích của tôi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Projects"
msgstr "Dự án của tôi"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "My Tasks"
msgstr "Nhiệm vụ của tôi"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#, python-format
msgid "Name"
msgstr "Tên"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__name_cropped
msgid "Name Cropped"
msgstr "Tên đã rút gọn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the tasks"
msgstr "Tên nhiệm vụ"

#. module: project
#: model:project.task,legend_blocked:project.project_task_10
#: model:project.task,legend_blocked:project.project_task_11
#: model:project.task,legend_blocked:project.project_task_19
#: model:project.task,legend_blocked:project.project_task_21
#: model:project.task,legend_blocked:project.project_task_22
#: model:project.task,legend_blocked:project.project_task_27
#: model:project.task,legend_blocked:project.project_task_28
#: model:project.task,legend_blocked:project.project_task_29
#: model:project.task,legend_blocked:project.project_task_34
#: model:project.task,legend_blocked:project.project_task_35
#: model:project.task,legend_blocked:project.project_task_36
#: model:project.task,legend_blocked:project.project_task_4
#: model:project.task,legend_blocked:project.project_task_5
#: model:project.task,legend_blocked:project.project_task_6
#: model:project.task,legend_blocked:project.project_task_7
#: model:project.task.type,legend_blocked:project.project_stage_1
msgid "Need functional or technical help"
msgstr "Cần trợ giúp về tính năng hoặc kỹ thuật"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "Mặt trung tính"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#, python-format
msgid "New"
msgstr "Mới"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/right_panel/project_utils.js:0
#, python-format
msgid "New Milestone"
msgstr "Mốc mới"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Orders"
msgstr "Đơn đặt hàng mới"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Projects"
msgstr "Dự án mới"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Request"
msgstr "Đề nghị mới"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Mới nhất"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_activity
msgid "Next Activities"
msgstr "Hoạt động tiếp theo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện lịch hoạt động kế tiếp"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Hạn chót hoạt động kế tiếp"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động kế tiếp"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "Kiểu hoạt động kế tiếp"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Next Occurrences:"
msgstr "Lần lặp kế tiếp:"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__next_recurrence_date
msgid "Next Recurrence Date"
msgstr "Ngày lặp lại tiếp theo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_message
msgid "Next Recurrencies"
msgstr "Các kỳ tiếp theo"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "No Subject"
msgstr "Không chủ đề"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "Chưa có đánh giá của khách hàng"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "Chưa có dữ liệu nào!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_milestone_all
msgid "No milestones found. Let's create one!"
msgstr "Không tìm thấy mốc thời gian. Hãy tạo một mốc!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "Không tìm thấy dự án nào. Hãy tạo một dự án!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "Không tìm thấy giai đoạn nào. Hãy tạo một giai đoạn!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "Không tìm thấy thẻ. Hãy tạo một thẻ!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid "No tasks found. Let's create one!"
msgstr "Không tìm thấy nhiệm vụ nào. Hãy tạo một nhiệm vụ!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "Không tìm thấy cập nhật. Hãy tạo mới!"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Không"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "Normal"
msgstr "Thông thường"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "Ghi chú"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__november
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__november
msgid "November"
msgstr "Tháng Mười một"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "Số tác vụ"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__after
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__after
msgid "Number of Repetitions"
msgstr "Số lần lặp lại"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__tasks_count
msgid "Number of Tasks"
msgstr "Số nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__recurrence_left
msgid "Number of Tasks Left to Create"
msgstr "Số nhiệm vụ còn lại cần tạo"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_days_close
msgid "Number of Working Days to close the task"
msgstr "Số ngày làm việc để đóng nhiệm vụ"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_days_open
msgid "Number of Working Days to open the task"
msgstr "Số ngày làm việc để mở nhiệm vụ"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_hours_close
msgid "Number of Working Hours to close the task"
msgstr "Sô giờ làm việc để đóng nhiệm vụ"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_hours_open
msgid "Number of Working Hours to open the task"
msgstr "Số ngày làm việc để mở nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__doc_count
msgid "Number of documents attached"
msgstr "Số tài liệu được đính kèm"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "Số lỗi"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Số tin nhắn cần xử lý"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số lượng tin gửi đi bị lỗi"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_unread_counter
#: model:ir.model.fields,help:project.field_project_project__message_unread_counter
#: model:ir.model.fields,help:project.field_project_task__message_unread_counter
#: model:ir.model.fields,help:project.field_project_update__message_unread_counter
msgid "Number of unread messages"
msgstr "Số tin nhắn chưa đọc"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__october
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__october
msgid "October"
msgstr "Tháng Mười"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
msgid "Off Track"
msgstr "Không đúng tiến độ"

#. module: project
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "Thiết kế văn phòng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "Ok"
msgstr "Đồng ý"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Old Completed Sprint"
msgstr "Sprint cũ hoàn thành"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
msgid "On Hold"
msgstr "Tạm dừng"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
msgid "On Track"
msgstr "Đúng tiến độ"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "Mỗi tháng một lần"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Open"
msgstr "Mở"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
msgid "Open Tasks"
msgstr "Mở nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Open tasks"
msgstr "Mở nhiệm vụ"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID tùy chọn của một luồng (hồ sơ) tập hợp tất cả tin nhắn nhận được, thậm "
"chí nếu đó là tin nhắn không có phản hồi. Nếu đặt, việc này sẽ tắt hoàn toàn"
" tạo hồ sơ mới. "

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Others"
msgstr "Khác"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "Nhiệm vụ quá hạn"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_blocked
#: model:ir.model.fields,help:project.field_project_task_type__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection when the task or issue is in that stage."
msgstr ""
"Ghi đè giá trị mặc định được hiển thị cho trạng thái bị chặn cho lựa chọn "
"kanban khi nhiệm vụ hoặc sự cố ở giai đoạn đó."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_done
#: model:ir.model.fields,help:project.field_project_task_type__legend_done
msgid ""
"Override the default value displayed for the done state for kanban selection"
" when the task or issue is in that stage."
msgstr ""
"Ghi đè giá trị mặc định được hiển thị cho trạng thái hoàn thành cho lựa chọn"
" kanban khi nhiệm vụ hoặc sự cố ở giai đoạn đó."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_normal
#: model:ir.model.fields,help:project.field_project_task_type__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection when the task or issue is in that stage."
msgstr ""
"Ghi đè giá trị mặc định được hiển thị cho trạng thái bình thường cho lựa "
"chọn kanban khi nhiệm vụ hoặc sự cố ở giai đoạn đó."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_user_id
msgid "Owner"
msgstr "Người sở hữu"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Page Ideas"
msgstr "Ý tưởng trang"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "Mô hình gốc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID luồng hồ sơ gốc"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Parent Task"
msgstr "Nhiệm vụ chính"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Mô hình gốc chứa bí danh này. Mô hình chứa tham chiếu bí danh không nhất "
"thiết phải là mô hình đưa ra bởi alias_model_id (Ví dụ: dự án (parent_model)"
" và nhiệm vụ (model))"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Phần trăm mức độ hài lòng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Percentage of happy ratings over the past 30 days."
msgstr "Phần trăm xếp hạng hài lòng trong 30 ngày qua."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "Periodic rating"
msgstr "Đánh giá định kỳ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Personal Stage"
msgstr "Giai đoạn cá nhân"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "Trạng thái giai đoạn cá nhân"

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "Trạng thái nhiệm vụ cá nhân"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
msgid "Personal User Stage"
msgstr "Trạng thái người dùng cá nhân"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__partner_phone
#: model:ir.model.fields,field_description:project.field_project_task__partner_phone
msgid "Phone"
msgstr "Điện thoại"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Plan resource allocation across projets and tasks, and estimate deadlines "
"more accurately"
msgstr ""
"Lập kế hoạch phân công nguồn lực cho các dự án và nhiệm vụ, và ước tính hạn "
"chót chính xác hơn."

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_project_forecast
msgid "Planning"
msgstr "Kế hoạch"

#. module: project
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""
"Vui lòng xoá các nhiệm vụ hiện có trong dự án được liên kết đến các tài "
"khoản mà bạn muốn xoá."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Podcast and Video Production"
msgstr "Sản xuất Podcast và Video"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Chính sách cho phép đăng tin nhắn lên tài liệu sử dụng cổng email.\n"
"- mọi người: mọi người có thể đăng\n"
"- đối tác: chỉ các đối tác đã xác thực\n"
"- người theo dõi: chỉ những người theo dõi của tài liệu liên quan hoặc thành viên của kênh đang theo dõi\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "URL truy cập cổng thông tin"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__portal_user_names
msgid "Portal User Names"
msgstr "Tên người dùng cổng thông tin"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Prioritize Tasks by using the %s icon.%s Use the %s button to inform your "
"colleagues that a task is ready for the next stage.%s Use the %s to indicate"
" a problem or a need for discussion on a task.%s"
msgstr ""
"Ưu tiên nhiệm vụ bằng cách sử dụng biểu tượng %s.%s Sử dụng nút %s để thông "
"báo cho đồng nghiệp về một nhiệm vụ đã sẵn sàng cho giai đoạn tiếp theo.%s "
"Sử dụng %s để chỉ một vấn đề hoặc nhu cầu thảo luận về một nhiệm vụ.%s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Prioritize Tasks by using the %s icon.%s Use the %s button to signalize to "
"your colleagues that a task is ready for the next stage.%s Use the %s to "
"signalize a problem or a need for discussion on a task.%s"
msgstr ""
"Ưu tiên nhiệm vụ bằng cách sử dụng biểu tượng %s.%s Sử dụng nút %s để báo "
"hiệu cho đồng nghiệp về một nhiệm vụ đã sẵn sàng cho giai đoạn tiếp theo.%s "
"Sử dụng %s để báo hiệu vấn đề hoặc nhu cầu thảo luận về một nhiệm vụ.%s"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#, python-format
msgid "Priority"
msgstr "Độ ưu tiên"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Private"
msgstr "Riêng tư"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Profitability"
msgstr "Lợi nhuận"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "Tiến trình"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress_percentage
msgid "Progress Percentage"
msgstr "Phần trăm tiến trình"

#. module: project
#. openerp-web
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_project_update__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Project"
msgstr "Dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_analytic_account_id
msgid "Project Analytic Account"
msgstr "Tài khoản phân tích dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_form
msgid "Project Collaborator"
msgstr "Cộng tác viên dự án"

#. module: project
#: model:ir.actions.act_window,name:project.project_collaborator_action
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Project Collaborators"
msgstr "Cộng tác viên dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_count
msgid "Project Count"
msgstr "Số dự án"

#. module: project
#: model:ir.model,name:project.model_project_delete_wizard
msgid "Project Delete Wizard"
msgstr "Hướng dẫn xóa dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model:ir.model.fields,field_description:project.field_project_task__manager_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "Quản lý dự án"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "Mốc thời gian dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "Tên dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__project_id
msgid "Project Shared"
msgstr "Dự án đã chia sẻ"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "Chia sẻ dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "Chia sẻ dự án: Nhiệm vụ"

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "Giai đoạn dự án"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "Giai đoạn dự án đã thay đổi"

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "Hướng dẫn xóa giai đoạn dự án"

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "Giai đoạn dự án"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "Thẻ dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "Nhiệm vụ dự án"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "Cập nhật dự án"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Project Updates"
msgstr "Cập nhật dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "Hiển thị dự án"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "Nhiệm vụ của dự án"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_recurring_tasks_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_recurring_tasks
#: model:ir.cron,name:project.ir_cron_recurring_tasks
msgid "Project: Create Recurring Tasks"
msgstr "Dự án: Tạo nhiệm vụ định kỳ"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_rating_project
#: model:ir.cron,name:project.ir_cron_rating_project
msgid "Project: Send rating"
msgstr "Dự án: Gửi đánh giá"

#. module: project
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_ids
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Projects"
msgstr "Dự án"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__projects_archived
msgid "Projects Archived"
msgstr "Dự án đã lưu trữ"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "Projects regroup tasks on the same topic, and each has its dashboard."
msgstr ""
"Dự án nhóm lại nhiệm vụ thuộc cùng một chủ đề và mỗi chủ đề có bảng thông "
"tin riêng."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Published"
msgstr "Đã đăng"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Publishing"
msgstr "Xuất bản"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "Hàng Quý"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Rated Tasks"
msgstr "Nhiệm vụ liên quan"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "Đánh giá"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
msgid "Rating (/5)"
msgstr "Đánh giá (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
msgid "Rating Average"
msgstr "Điểm đánh giá trung bình"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "Mẫu Email đánh giá"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "Tần suất đánh giá"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Đánh giá phản hồi cuối cùng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "Đánh giá ảnh cuối cùng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "Đánh giá giá trị cuối cùng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_request_deadline
msgid "Rating Request Deadline"
msgstr "Thời hạn yêu cầu đánh giá"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Đánh giá độ hài lòng"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
#, python-format
msgid "Rating Value (/5)"
msgstr "Giá trị đánh giá (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "Số đánh giá"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "Rating when changing stage"
msgstr "Đánh giá khi thay đổi giai đoạn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
msgid "Ratings"
msgstr "Đánh giá"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Ratings of %s"
msgstr "Đánh giá của %s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "Đã đạt"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__reached_date
msgid "Reached Date"
msgstr "Ngày đạt"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__read
msgid "Readonly"
msgstr "Chỉ đọc"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__done
#: model:project.task,legend_done:project.project_task_1
#: model:project.task,legend_done:project.project_task_12
#: model:project.task,legend_done:project.project_task_2
#: model:project.task,legend_done:project.project_task_20
#: model:project.task,legend_done:project.project_task_24
#: model:project.task,legend_done:project.project_task_25
#: model:project.task,legend_done:project.project_task_26
#: model:project.task,legend_done:project.project_task_3
#: model:project.task,legend_done:project.project_task_30
#: model:project.task,legend_done:project.project_task_31
#: model:project.task,legend_done:project.project_task_32
#: model:project.task,legend_done:project.project_task_33
#: model:project.task,legend_done:project.project_task_8
#: model:project.task,legend_done:project.project_task_9
#: model:project.task.type,legend_done:project.project_personal_stage_admin_0
#: model:project.task.type,legend_done:project.project_personal_stage_admin_1
#: model:project.task.type,legend_done:project.project_personal_stage_admin_2
#: model:project.task.type,legend_done:project.project_personal_stage_admin_3
#: model:project.task.type,legend_done:project.project_personal_stage_admin_4
#: model:project.task.type,legend_done:project.project_personal_stage_admin_5
#: model:project.task.type,legend_done:project.project_personal_stage_admin_6
#: model:project.task.type,legend_done:project.project_personal_stage_demo_0
#: model:project.task.type,legend_done:project.project_personal_stage_demo_1
#: model:project.task.type,legend_done:project.project_personal_stage_demo_2
#: model:project.task.type,legend_done:project.project_personal_stage_demo_3
#: model:project.task.type,legend_done:project.project_personal_stage_demo_4
#: model:project.task.type,legend_done:project.project_personal_stage_demo_5
#: model:project.task.type,legend_done:project.project_personal_stage_demo_6
#: model:project.task.type,legend_done:project.project_stage_0
#: model:project.task.type,legend_done:project.project_stage_2
#, python-format
msgid "Ready"
msgstr "Sẵn sàng"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__done
msgid "Ready for Next Stage"
msgstr "Sẵn sàng cho trạng thái kế tiếp"

#. module: project
#: model:project.task.type,legend_done:project.project_stage_3
msgid "Ready to reopen"
msgstr "Sẵn sàng mở lại"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__rating_last_feedback
msgid "Reason of the rating"
msgstr "Lí do đánh giá"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "Tiếp nhận {{ object.name }}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "Người nhận"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID luồng hồ sơ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Recording"
msgstr "Ghi nhận"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurrence"
msgstr "Định kỳ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_update
msgid "Recurrence Update"
msgstr "Cập nhật định kỳ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "Lặp lại"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_project_task__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
msgid "Recurring Tasks"
msgstr "Nhiệm vụ định kỳ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_blocked
msgid "Red Kanban Label"
msgstr "Nhãn kanban đỏ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Ref"
msgstr "Tham chiếu"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Refused"
msgstr "Bị từ chối"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "Tài liệu liên quan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "ID tài liệu liên quan"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "Mô hình tài liệu liên quan"

#. module: project
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "Cải tạo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_day
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_day
msgid "Repeat Day"
msgstr "Lặp lại ngày"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "Lặp lại mỗi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_month
msgid "Repeat Month"
msgstr "Lặp lại tháng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Repeat On"
msgstr "Lặp lại vào"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_month
msgid "Repeat On Month"
msgstr "Lặp vào tháng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_year
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_year
msgid "Repeat On Year"
msgstr "Lặp vào năm"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_day
msgid "Repeat Show Day"
msgstr "Lặp lại hiện ngày"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_dow
msgid "Repeat Show Dow"
msgstr "Lặp lại giám xuống"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_month
msgid "Repeat Show Month"
msgstr "Lặp lại hiện tháng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_week
msgid "Repeat Show Week"
msgstr "Lặp lại hiện tuần"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_unit
msgid "Repeat Unit"
msgstr "Đơn vị tính lặp lại"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_week
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_week
msgid "Repeat Week"
msgstr "Lặp theo tuần"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_number
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_number
msgid "Repetitions"
msgstr "Số lần lặp lại"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "Báo cáo"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research"
msgstr "Nghiên cứu"

#. module: project
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "Nghiên cứu & Phát triển"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research Project"
msgstr "Nghiên cứu dự án"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Researching"
msgstr "Đang nghiên cứu"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Resources Allocation"
msgstr "Phân bổ nguồn lực"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "Mặt buồn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sat
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sat
msgid "Sat"
msgstr "T7"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sat
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sat
msgid "Saturday"
msgstr "Thứ Bảy"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Script"
msgstr "Script"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr "Tìm <span class=\"nolabel\"> (trong Nội dung)</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "Tìm kiếm dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "Tìm kiếm cập nhật"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Tìm tất cả"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Assignees"
msgstr "Tìm kiếm trong người được phân công"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr "Tìm trong tin nhắn"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Priority"
msgstr "Tìm kiếm trong ưu tiên"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "Tìm trong dự án"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Ref"
msgstr "Tìm kiếm trong mã tham chiếu"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Stages"
msgstr "Tìm trong giai đoạn"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Status"
msgstr "Tìm kiếm trong trạng thái"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__second
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__second
msgid "Second"
msgstr "Giây"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "Mã token bảo mật"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Send"
msgstr "Gửi"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__september
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__september
msgid "September"
msgstr "Tháng Chín"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__sequence
#: model:ir.model.fields,field_description:project.field_project_project_stage__sequence
#: model:ir.model.fields,field_description:project.field_project_task__sequence
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "Đặt hình đại diện"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr "Đặt mẫu email đánh giá cho các giai đoạn"

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Settings"
msgstr "Cài đặt"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share"
msgstr "Chia sẻ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Editable"
msgstr "Chia sẻ có thể sửa"

#. module: project
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Share Project"
msgstr "Chia sẻ dự án"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Readonly"
msgstr "Chia sẻ chỉ đọc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_favorite
msgid "Show Project on Dashboard"
msgstr "Hiện dự án trên bảng thông tin"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr "Hiển thị tất cả hồ sơ có ngày tác vụ kế tiếp trước hôm nay"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "Kể từ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Software Development"
msgstr "Phát triển phần mềm"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Sorry. You can't set a task as its parent task."
msgstr "Rất tiếc. Bạn không thể đặt một nhiệm vụ làm nhiệm vụ chính của nó."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Specifications"
msgstr "Đặc điểm"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Split your tasks to organize your work into sub-milestones"
msgstr ""
"Phân chia công việc của bạn để tổ chức công việc thành các mốc nhỏ hơn"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Backlog"
msgstr "Sprint Backlog"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Complete"
msgstr "Sprint Hoàn tất"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Sprint Summary"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint in Progress"
msgstr "Sprint đang thực hiện"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Stage"
msgstr "Giai đoạn"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "Giai đoạn đã thay đổi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Stage Description and Tooltips"
msgstr "Chú thích và mô tả giai đoạn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "Người sở hữu giai đoạn"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "Giai đoạn đã thay đổi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stages_active
msgid "Stages Active"
msgstr "Các giai đoạn hoạt động"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "Các giai đoạn cần xóa"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Starred"
msgstr "Được gắn sao"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state
#: model:ir.model.fields,field_description:project.field_project_update__status
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#, python-format
msgid "Status"
msgstr "Trạng thái"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Đã quá hạn chót\n"
"Hôm nay: Hôm nay là ngày hoạt động\n"
"Kế hoạch: Các hoạt động trong tương lai."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Stop Recurrence"
msgstr "Dừng định kỳ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_count
msgid "Sub-task Count"
msgstr "Số nhiệm vụ phụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_subtasks
#: model:ir.model.fields,field_description:project.field_project_task__child_ids
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_subtask_project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "Nhiệm vụ phụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_planned_hours
msgid "Sub-tasks Planned Hours"
msgstr "Số giờ nhiệm vụ phụ dự kiến"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_planned_hours
msgid ""
"Sum of the time planned of all the sub-tasks linked to this task. Usually "
"less than or equal to the initially planned time of this task."
msgstr ""
"Tổng thời gian dự tính của tất cả nhiệm vụ phụ liên quan tới nhiệm vụ này. "
"Thông thường ít hơn hoặc bằng thời gian dự kiến ban đầu của nhiệm vụ này."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sun
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sun
msgid "Sun"
msgstr "CN"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sun
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sun
msgid "Sunday"
msgstr "Chủ nhật"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "T-shirt Printing"
msgstr "In áo thun"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "Tag name already exists!"
msgstr "Tên thẻ đã tồn tại!"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tags"
msgstr "Thẻ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__task_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Task"
msgstr "Nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "Các hoạt động nhiệm vụ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_blocked
#: model:mail.message.subtype,name:project.mt_task_blocked
msgid "Task Blocked"
msgstr "Nhiệm vụ bị chặn"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__task_count
#: model:ir.model.fields,field_description:project.field_project_project__task_count
msgid "Task Count"
msgstr "Số nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count_with_subtasks
msgid "Task Count With Subtasks"
msgstr "Số nhiệm vụ có nhiệm vụ phụ"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "Nhiệm vụ đã được tạo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_project_task__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "Tính phụ thuộc nhiệm vụ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_dependency_change
#: model:mail.message.subtype,name:project.mt_task_dependency_change
msgid "Task Dependency Changes"
msgstr "Thay đổi tính phụ thuộc nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "Nhật kí công việc"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "Đánh giá nhiệm vụ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_ready
#: model:mail.message.subtype,name:project.mt_task_ready
msgid "Task Ready"
msgstr "Nhiệm vụ sẵn sàng"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Nhiệm vụ định kỳ"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "Giai đoạn nhiệm vụ"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "Giai đoạn nhiệm vụ đã thay đổi"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "Giai đoạn nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "Tiêu đề nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "Tiêu đề nhiệm vụ..."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_blocked
msgid "Task blocked"
msgstr "Nhiệm vụ bị chặn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task in progress. Click to block or set as done."
msgstr "Nhiệm vụ đang thực hiện. Bấm để chặn hoặc đặt là xong. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task is blocked. Click to unblock or set as done."
msgstr "Nhiệm vụ đã chặn. Bấm để bỏ chặn hoặc đặt là xong. "

#. module: project
#: model:mail.message.subtype,description:project.mt_task_ready
msgid "Task ready for Next Stage"
msgstr "Nhiệm vụ sẵn sàng chuyển giai đoạn tiếp"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "Nhiệm vụ:"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Task: Rating Request"
msgstr "Nhiệm vụ: Yêu cầu đánh giá"

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Task: Reception Acknowledgment"
msgstr "Nhiệm vụ: Xác nhận đã nhận được"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
#: model:ir.model.fields,field_description:project.field_account_analytic_tag__task_ids
#: model:ir.model.fields,field_description:project.field_project_project__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.model.fields,field_description:project.field_res_partner__task_ids
#: model:ir.model.fields,field_description:project.field_res_users__task_ids
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
#, python-format
msgid "Tasks"
msgstr "Nhiệm vụ"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.report_project_task_user_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "Phân tích nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Tasks In Progress"
msgstr "Nhiệm vụ đang tiến hành"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Late"
msgstr "Nhiệm vụ trễ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "Quản lý nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "Giai đoạn nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
msgid "Tasks in Recurrence"
msgstr "Nhiệm vụ lặp lại"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__is_closed
#: model:ir.model.fields,help:project.field_project_task_type__is_closed
msgid "Tasks in this stage are considered as closed."
msgstr "Nhiệm vụ trong giai đoạn này được coi là đóng."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Tests"
msgstr "Chạy thử"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "Giai đoạn cá nhân của người dùng hiện tại."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "Giai đoạn nhiệm vụ cá nhân của người dùng hiện tại."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "Mốc thời gian sau đã được thêm:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "Các mốc thời gian sau đã được thêm:"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Mô hình (Kiểu tài liệu Odoo) mà bí danh này tương tác. Bất kỳ email nào nhận"
" được mà không trả lời một hồ sơ cụ thể sẽ tạo ra hồ sơ mới trong mô hình "
"này. (ví dụ: Nhiệm vụ dự án) "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Tên của bí danh email, ví dụ: 'jobs' nếu bạn muốn nhận email gửi đến địa chỉ"
" <<EMAIL>>"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Người sở hữu của các hồ sơ được tạo khi nhận email gửi tới bí danh này. Nếu "
"trường này không được đặt, hệ thống sẽ cố gắng tìm đúng người sở hữu dựa vào"
" địa chỉ người gửi (Từ), hoặc sẽ dùng tài khoản Quản trị viên nếu không tìm "
"thấy người dùng hệ thống nào cho địa chỉ đó. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The project cannot be shared with the recipient(s) because the privacy of "
"the project is too restricted. Set the privacy to 'Visible by following "
"customers' in order to make it accessible by the recipient(s)."
msgstr ""
"Không thể chia sẻ dự án với (các) người nhận vì quyền riêng tư của dự án quá"
" chặt chẽ. Đặt quyền riêng tư thành 'Hiển thị bằng cách theo dõi khách hàng'"
" để người nhận có thể truy cập."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to 'Visible by "
"following customers' in order to make it accessible by the recipient(s)."
msgstr ""
"Không thể chia sẻ nhiệm vụ với (các) người nhận vì quyền riêng tư của dự án "
"quá chặt chẽ. Đặt quyền riêng tư của dự án thành 'Hiển thị bằng cách theo "
"dõi khách hàng' để người nhận có thể truy cập."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "There are no more occurrences."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "Không có dự án nào."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "Không có xếp hạng nào cho dự án này vào lúc này"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "Không có nhiệm vụ."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_from
msgid "These people will receive email."
msgstr "Những người này sẽ nhận được email."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__third
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__third
msgid "Third"
msgstr "Thứ 3"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
#, python-format
msgid "This Month"
msgstr "Tháng này"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#, python-format
msgid "This Week"
msgstr "Tuần này"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__subsequent
msgid "This and following tasks"
msgstr "Nhiệm vụ này và nhiệm vụ sau đây"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Giai đoạn này được thu gọn trong dạng xem kanban khi không có hồ sơ trong "
"giai đoạn này để hiển thị. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid "This stage is folded in the kanban view."
msgstr "Giai đoạn này được thu gọn trong dạng xem kanban."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "This step is done. Click to block or set in progress."
msgstr "Bước này đã hoàn tất. Bấm để chặn hoặc đặt là đang tiến hành."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__this
msgid "This task"
msgstr "Nhiệm vụ này"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr ""
"Điều này sẽ lưu trữ các giai đoạn và tất cả các nhiệm vụ mà chúng chứa từ "
"các dự án sau:"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__thu
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__thu
msgid "Thu"
msgstr "T5"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__thu
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__thu
msgid "Thursday"
msgstr "Thứ năm"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "Quản lý thời gian"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__planned_hours
msgid "Time planned to achieve this task (including its sub-tasks)."
msgstr ""
"Thời gian dự kiến để hoàn thành nhiệm vụ này (bao gồm cả nhiệm vụ phụ)."

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Customize tasks and stages according to the project"
msgstr "Mẹo: Tùy chỉnh các nhiệm vụ và giai đoạn theo dự án"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#, python-format
msgid "Title"
msgstr "Xưng hô"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Title of the Update"
msgstr "Tiêu đề của cập nhật"

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "Cần làm"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "To Print"
msgstr "In"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                    Chat in real-time or by email to collaborate efficiently."
msgstr ""
"Để hoàn thành công việc, sử dụng hoạt động và trạng thái cho nhiệm vụ.<br>\n"
"                    Chat trực tiếp hoặc qua email để hợp tác hiệu quả."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"Để hoàn thành công việc, hãy sử dụng các hoạt động và trạng thái trên nhiệm vụ.<br>\n"
"                Trò chuyện theo thời gian thực hoặc qua email để cộng tác hiệu quả."

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#, python-format
msgid "Today"
msgstr "Hôm nay"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "Hoạt động hôm nay"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "Theo dõi mức độ hài lòng của khách hàng đối với các nhiệm vụ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#: model_terms:ir.actions.act_window,help:project.project_milestone_all
#, python-format
msgid "Track major progress points that must be reached to achieve success."
msgstr ""
"Theo dõi các điểm tiến trình quan trọng cần phải đạt được để thực hiện dự án"
" thành công."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the costs and revenues linked to your projects"
msgstr "Theo dõi chi phí và doanh thu liên kết với dự án của bạn"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "Theo dõi tiến trình của dự án"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Track the progress of your projects from their creation to their closing."
msgstr "Theo dõi tiến trình của dự án từ lúc tạo tới lúc đóng."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Track the progress of your tasks from their creation to their closing."
msgstr "Theo dõi tiến trình của nhiệm vụ từ lúc tạo tới lúc đóng."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "Theo dõi thời gian dành cho các dự án và nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__tue
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__tue
msgid "Tue"
msgstr "T3"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__tue
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__tue
msgid "Tuesday"
msgstr "Thứ Ba"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "Hai lần một tháng"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kiểu hoạt động ngoại lệ trên hồ sơ."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Unarchive"
msgstr "Bỏ lưu trữ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Unassign Me"
msgstr "Bỏ phân công cho tôi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unassigned"
msgstr "Chưa phân công"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Unassigned Tasks"
msgstr "Nhiệm vụ chưa phân công"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Unknown Analytic Account"
msgstr "Tài khoản phân tích không rõ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_unread
#: model:ir.model.fields,field_description:project.field_project_project__message_unread
#: model:ir.model.fields,field_description:project.field_project_task__message_unread
#: model:ir.model.fields,field_description:project.field_project_update__message_unread
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "Tin chưa đọc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Bộ đếm tin chưa đọc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
msgid "Until"
msgstr "Cho đến"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__update_ids
msgid "Update"
msgstr "Cập nhật"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Use %s and %s bullets to indicate the status of a task. %s"
msgstr "Dùng nút %s và %s để chỉ trạng thái của nhiệm vụ. %s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Use <b>activities</b> to organize your daily work."
msgstr "Sử dụng <b>hoạt động</b> để sắp xếp công việc hằng ngày."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_enabled
msgid "Use Email Alias"
msgstr "Dùng bí danh email"

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "Sử dụng đánh giá cho dự án"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "Sử dụng nhiệm vụ định kỳ"

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "Dùng giai đoạn cho dự án"

#. module: project
#: model:res.groups,name:project.group_subtask_project
msgid "Use Subtasks"
msgstr "Sử dụng nhiệm vụ phụ"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "Sử dụng tính phụ thuộc của nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "Sử dụng nhiệm vụ như là"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Use This For My Project"
msgstr "Sử dụng cho dự án của tôi"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "Dùng thẻ để phân loại nhiệm vụ."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your customers. \n"
"    Add new people to the followers' list to make them aware of the main changes about this task."
msgstr ""
"Dùng cửa sổ chat để <b>gửi email</b> và giao tiếp hiệu quả với khách hàng. \n"
"    Thêm người mới vào danh sách người theo dõi để họ biết được những thay đổi chính của nhiệm vụ này."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__user_id
#: model:res.groups,name:project.group_project_user
msgid "User"
msgstr "Người dùng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "View"
msgstr "Xem"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "View Task"
msgstr "Xem nhiệm vụ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "Tính hiển thị"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr ""
"Muốn có cách thức tốt hơn để <b>quản lý các dự án</b>? <i>Bắt đầu từ "
"đây.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_task__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "Tin nhắn website"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Website Redesign"
msgstr "Thiết kế lại website"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử liên lạc website"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__wed
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__wed
msgid "Wed"
msgstr "T4"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__wed
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__wed
msgid "Wednesday"
msgstr "Thứ Tư"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "Hàng tuần"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "Tuần"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__is_favorite
msgid "Whether this project should be displayed on your dashboard."
msgstr "Có hiển thị dự án này ở bảng thông tin hay không."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "Số ngày làm việc để phân công"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "Số ngày làm việc để đóng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "Số giờ làm việc để phân công"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "Số giờ làm việc để đóng"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__resource_calendar_id
msgid "Working Time"
msgstr "Thời gian làm việc"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "Thời gian để phân công"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "Thời gian làm việc để đóng"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Writing"
msgstr "Đang viết"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "Hàng năm"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "Năm"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"Bạn cũng có thể thêm mô tả để giúp đỡ đồng nghiệp hiểu ý nghĩa và mục đích "
"của giai đoạn. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot archive recurring tasks. Please disable the recurrence first."
msgstr ""

#. module: project
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"You cannot change the company of an analytic account if it is related to a "
"project."
msgstr ""

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot create cyclic dependency."
msgstr "Bạn không thể tạo hạng mục phụ thuộc tuần hoàn."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot delete a project containing tasks. You can either archive it or "
"first delete all of its tasks."
msgstr ""
"Bạn không thể xóa một dự án có chứa các nhiệm vụ. Bạn có thể lưu trữ hoặc "
"trước tiên xóa tất cả các nhiệm vụ của dự án."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot delete recurring tasks. Please disable the recurrence first."
msgstr "Bạn không thể xóa nhiệm vụ định kỳ. Hãy tắt định kỳ trước."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""
"Bạn không thể xóa các giai đoạn chứa nhiệm vụ. Bạn có thể lưu trữ chúng hoặc"
" trước tiên xóa tất cả các tác vụ của chúng."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr ""
"Bạn không thể xóa các giai đoạn chứa nhiệm vụ. Trước tiên, bạn nên xóa tất "
"cả các nhiệm vụ."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot read %s fields in task."
msgstr "Bạn không thể đọc các trường %s trong nhiệm vụ."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot write on %s fields in task."
msgstr "Bạn không thể ghi lên trường %s trong nhiệm vụ."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "Bạn đã được phân công cho %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "Bạn đã được phân công cho"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have not write access of %s field."
msgstr "Bạn không có quyền viết trong trường %s."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You should at least have one personal stage. Create a new stage to which the"
" tasks can be transferred after this one is deleted."
msgstr ""
"Ít nhất bạn nên có một giai đoạn cá nhân. Tạo một giai đoạn mới để chuyển "
"các nhiệm vụ sang sau khi giai đoạn này bị xóa."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. New Design"
msgstr "VD: Thiết kế mới"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. Office Party"
msgstr "VD: Tiệc văn phòng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "VD: tiệc-văn-phòng"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "for customer:"
msgstr "cho khách hàng:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "for project:"
msgstr "cho dự án:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "in priority:"
msgstr "trong ưu tiên:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "in stage:"
msgstr "trong giai đoạn:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "in status:"
msgstr "trong trạng thái:"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "task"
msgstr "nhiệm vụ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline of the following milestone has been updated:"
msgstr "hạn chót của các mốc sau đã được cập nhật:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline of the following milestones has been updated:"
msgstr "hạn chót của các mốc sau đã được cập nhật:"

#. module: project
#. openerp-web
#: code:addons/project/static/src/burndown_chart/burndown_chart_view.xml:0
#, python-format
msgid "true"
msgstr "đúng"

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid "{{ object.project_id.company_id.name }}: Satisfaction Survey"
msgstr "{{ object.project_id.company_id.name }}: Khảo sát độ hài lòng"
