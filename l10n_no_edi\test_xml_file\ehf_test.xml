<?xml version='1.0' encoding='UTF-8'?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
    xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0</cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>TOSL108</cbc:ID>
    <cbc:IssueDate>2013-06-30</cbc:IssueDate>
    <cbc:DueDate>2013-07-20</cbc:DueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
    <cbc:Note>Ordered in our booth at the convention.</cbc:Note>
    <cbc:TaxPointDate>2013-06-30</cbc:TaxPointDate>
    <cbc:DocumentCurrencyCode>NOK</cbc:DocumentCurrencyCode>
    <cbc:AccountingCost>Project cost code 123</cbc:AccountingCost>
    <cbc:BuyerReference>3150bdn</cbc:BuyerReference>
    <cac:InvoicePeriod>
        <cbc:StartDate>2013-06-01</cbc:StartDate>
        <cbc:EndDate>2013-06-30</cbc:EndDate>
    </cac:InvoicePeriod>
    <cac:OrderReference>
        <cbc:ID>123</cbc:ID>
    </cac:OrderReference>
    <cac:ContractDocumentReference>
        <cbc:ID>Contract321</cbc:ID>
    </cac:ContractDocumentReference>
    <cac:AdditionalDocumentReference>
        <cbc:ID>Doc1</cbc:ID>
        <cbc:DocumentDescription>Timesheet</cbc:DocumentDescription>
        <cac:Attachment>
            <cac:ExternalReference>
                <cbc:URI>http://www.suppliersite.eu/sheet001.html</cbc:URI>
            </cac:ExternalReference>
        </cac:Attachment>
    </cac:AdditionalDocumentReference>
    <cac:AdditionalDocumentReference>
        <cbc:ID>Doc2</cbc:ID>
        <cbc:DocumentDescription>EHF specification</cbc:DocumentDescription>
        <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject mimeCode="application/pdf" filename="Hours-spent.csv">VGVzdCBiYXNlIDY0IGVuY29kaW5n</cbc:EmbeddedDocumentBinaryObject>
        </cac:Attachment>
    </cac:AdditionalDocumentReference>
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0192">*********</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID schemeID="0088">*************</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>partner_a</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Main street 34</cbc:StreetName>
                <cbc:AdditionalStreetName>Suite 123</cbc:AdditionalStreetName>
                <cbc:CityName>Big city</cbc:CityName>
                <cbc:PostalZone>54321</cbc:PostalZone>
                <cbc:CountrySubentity>RegionA</cbc:CountrySubentity>
                <cac:Country>
                    <cbc:IdentificationCode>NO</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>NO*********MVA</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>Foretaksregisteret</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>TAX</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>The Sellercompany ASA</cbc:RegistrationName>
                <cbc:CompanyID schemeID="0192">*********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>Antonio Salesmacher</cbc:Name>
                <cbc:Telephone>********</cbc:Telephone>
                <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingSupplierParty>
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0192">*********</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID schemeID="0088">*************</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>The Buyercompany</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Anystreet 8</cbc:StreetName>
                <cbc:AdditionalStreetName>Back door</cbc:AdditionalStreetName>
                <cbc:CityName>Anytown</cbc:CityName>
                <cbc:PostalZone>101</cbc:PostalZone>
                <cbc:CountrySubentity>RegionB</cbc:CountrySubentity>
                <cac:Country>
                    <cbc:IdentificationCode>NO</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>NO*********MVA</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>Buyercompany ASA</cbc:RegistrationName>
                <cbc:CompanyID schemeID="0192">*********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>John Doe</cbc:Name>
                <cbc:Telephone>5121230</cbc:Telephone>
                <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>
    <cac:PayeeParty>
        <cac:PartyIdentification>
            <cbc:ID schemeID="0088">*************</cbc:ID>
        </cac:PartyIdentification>
        <cac:PartyName>
            <cbc:Name>Ebeneser Scrooge AS</cbc:Name>
        </cac:PartyName>
        <cac:PartyLegalEntity>
            <cbc:CompanyID schemeID="0192">*********</cbc:CompanyID>
        </cac:PartyLegalEntity>
    </cac:PayeeParty>
    <cac:TaxRepresentativeParty>
        <cac:PartyName>
            <cbc:Name>Tax handling company AS</cbc:Name>
        </cac:PartyName>
        <cac:PostalAddress>
            <cbc:StreetName>Regent street</cbc:StreetName>
            <cbc:AdditionalStreetName>Front door</cbc:AdditionalStreetName>
            <cbc:CityName>Newtown</cbc:CityName>
            <cbc:PostalZone>101</cbc:PostalZone>
            <cbc:CountrySubentity>RegionC</cbc:CountrySubentity>
            <cac:Country>
                <cbc:IdentificationCode>NO</cbc:IdentificationCode>
            </cac:Country>
        </cac:PostalAddress>
        <cac:PartyTaxScheme>
            <cbc:CompanyID>NO*********MVA</cbc:CompanyID>
            <cac:TaxScheme>
                <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:PartyTaxScheme>
    </cac:TaxRepresentativeParty>
    <cac:Delivery>
        <cbc:ActualDeliveryDate>2013-06-15</cbc:ActualDeliveryDate>
        <cac:DeliveryLocation>
            <cbc:ID schemeID="0088">*************</cbc:ID>
        </cac:DeliveryLocation>
    </cac:Delivery>
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode>31</cbc:PaymentMeansCode>
        <cbc:PaymentID>****************</cbc:PaymentID>
        <cac:PayeeFinancialAccount>
            <cbc:ID>***********</cbc:ID>
            <cac:FinancialInstitutionBranch>
                <cbc:ID>DNBANOKK</cbc:ID>
            </cac:FinancialInstitutionBranch>
        </cac:PayeeFinancialAccount>
    </cac:PaymentMeans>
    <cac:PaymentTerms>
        <cbc:Note>2 % discount if paid within 2 days. Penalty percentage 10% from due date</cbc:Note>
    </cac:PaymentTerms>
    <cac:AllowanceCharge>
        <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
        <cbc:AllowanceChargeReasonCode>FC</cbc:AllowanceChargeReasonCode>
        <cbc:AllowanceChargeReason>Freight</cbc:AllowanceChargeReason>
        <cbc:Amount currencyID="NOK">100</cbc:Amount>
        <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>25</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:TaxCategory>
    </cac:AllowanceCharge>
    <cac:AllowanceCharge>
        <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
        <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
        <cbc:AllowanceChargeReason>Promotion discount</cbc:AllowanceChargeReason>
        <cbc:Amount currencyID="NOK">100</cbc:Amount>
        <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>25</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:TaxCategory>
    </cac:AllowanceCharge>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="NOK">365.28</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="NOK">1460.5</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="NOK">365.13</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>25</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="NOK">1</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="NOK">0.15</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>15</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="NOK">-25</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="NOK">0</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>E</cbc:ID>
                <cbc:Percent>0</cbc:Percent>
                <cbc:TaxExemptionReason>Exempt New Means of Transport</cbc:TaxExemptionReason>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="NOK">1436.5</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="NOK">1436.5</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="NOK">1801.78</cbc:TaxInclusiveAmount>
        <cbc:AllowanceTotalAmount currencyID="NOK">100</cbc:AllowanceTotalAmount>
        <cbc:ChargeTotalAmount currencyID="NOK">100</cbc:ChargeTotalAmount>
        <cbc:PrepaidAmount currencyID="NOK">1000</cbc:PrepaidAmount>
        <cbc:PayableRoundingAmount currencyID="NOK">0.22</cbc:PayableRoundingAmount>
        <cbc:PayableAmount currencyID="NOK">802.00</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    <cac:InvoiceLine>
        <cbc:ID>1</cbc:ID>
        <cbc:Note>Scratch on box</cbc:Note>
        <cbc:InvoicedQuantity unitCode="NAR">1</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="NOK">1273</cbc:LineExtensionAmount>
        <cbc:AccountingCost>BookingCode001</cbc:AccountingCost>
        <cac:InvoicePeriod>
            <cbc:StartDate>2013-06-01</cbc:StartDate>
            <cbc:EndDate>2013-06-30</cbc:EndDate>
        </cac:InvoicePeriod>
        <cac:OrderLineReference>
            <cbc:LineID>1</cbc:LineID>
        </cac:OrderLineReference>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReason>Damage</cbc:AllowanceChargeReason>
            <cbc:Amount currencyID="NOK">12</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReason>Testing</cbc:AllowanceChargeReason>
            <cbc:Amount currencyID="NOK">12</cbc:Amount>
        </cac:AllowanceCharge>
        <cac:Item>
            <cbc:Description>Processor: Intel Core 2 Duo SU9400 LV (1.4GHz). RAM: 3MB. Screen 1440x900</cbc:Description>
            <cbc:Name>Laptop computer</cbc:Name>
            <cac:SellersItemIdentification>
                <cbc:ID>JB007</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:StandardItemIdentification>
                <cbc:ID schemeID="0088">1234567890124</cbc:ID>
            </cac:StandardItemIdentification>
            <cac:OriginCountry>
                <cbc:IdentificationCode>DE</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="MP">12344321</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="STI">65434568</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>25</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
            <cac:AdditionalItemProperty>
                <cbc:Name>Color</cbc:Name>
                <cbc:Value>Black</cbc:Value>
            </cac:AdditionalItemProperty>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="NOK">1273</cbc:PriceAmount>
            <cbc:BaseQuantity>1</cbc:BaseQuantity>
            <cac:AllowanceCharge>
                <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
                <cbc:Amount currencyID="NOK">227</cbc:Amount>
                <cbc:BaseAmount currencyID="NOK">1500</cbc:BaseAmount>
            </cac:AllowanceCharge>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>2</cbc:ID>
        <cbc:Note>Cover is slightly damaged.</cbc:Note>
        <cbc:InvoicedQuantity unitCode="NAR">-1</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="NOK">-3.96</cbc:LineExtensionAmount>
        <cbc:AccountingCost>BookingCode002</cbc:AccountingCost>
        <cac:OrderLineReference>
            <cbc:LineID>5</cbc:LineID>
        </cac:OrderLineReference>
        <cac:Item>
            <cbc:Name>Returned "Advanced computing" book</cbc:Name>
            <cac:SellersItemIdentification>
                <cbc:ID>JB008</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:StandardItemIdentification>
                <cbc:ID schemeID="0088">*************</cbc:ID>
            </cac:StandardItemIdentification>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="MP">********</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="STI">********</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
                <cbc:ID schemeID="UNCL5305">S</cbc:ID>
                <cbc:Percent>15</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="NOK">3.96</cbc:PriceAmount>
            <cbc:BaseQuantity>1</cbc:BaseQuantity>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>3</cbc:ID>
        <cbc:InvoicedQuantity unitCode="NAR">2</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="NOK">4.96</cbc:LineExtensionAmount>
        <cbc:AccountingCost>BookingCode003</cbc:AccountingCost>
        <cac:OrderLineReference>
            <cbc:LineID>3</cbc:LineID>
        </cac:OrderLineReference>
        <cac:Item>
            <cbc:Name>"Computing for dummies" book</cbc:Name>
            <cac:SellersItemIdentification>
                <cbc:ID>JB009</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:StandardItemIdentification>
                <cbc:ID schemeID="0088">*************</cbc:ID>
            </cac:StandardItemIdentification>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="MP">********</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="STI">********</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
                <cbc:ID schemeID="UNCL5305">S</cbc:ID>
                <cbc:Percent>15</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="NOK">2.48</cbc:PriceAmount>
            <cbc:BaseQuantity>1</cbc:BaseQuantity>
            <cac:AllowanceCharge>
                <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
                <cbc:Amount currencyID="NOK">0.27</cbc:Amount>
                <cbc:BaseAmount currencyID="NOK">2.75</cbc:BaseAmount>
            </cac:AllowanceCharge>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>4</cbc:ID>
        <cbc:InvoicedQuantity unitCode="NAR">-1</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="NOK">-25</cbc:LineExtensionAmount>
        <cbc:AccountingCost>BookingCode004</cbc:AccountingCost>
        <cac:OrderLineReference>
            <cbc:LineID>2</cbc:LineID>
        </cac:OrderLineReference>
        <cac:Item>
            <cbc:Name>Returned IBM 5150 desktop</cbc:Name>
            <cac:SellersItemIdentification>
                <cbc:ID>JB010</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:StandardItemIdentification>
                <cbc:ID schemeID="0088">*************</cbc:ID>
            </cac:StandardItemIdentification>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="MP">********</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="STI">********</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
                <cbc:ID schemeID="UNCL5305">E</cbc:ID>
                <cbc:Percent>0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="NOK">25</cbc:PriceAmount>
            <cbc:BaseQuantity>1</cbc:BaseQuantity>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>5</cbc:ID>
        <cbc:InvoicedQuantity unitCode="MTR">250</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="NOK">187.5</cbc:LineExtensionAmount>
        <cbc:AccountingCost>BookingCode005</cbc:AccountingCost>
        <cac:OrderLineReference>
            <cbc:LineID>4</cbc:LineID>
        </cac:OrderLineReference>
        <cac:Item>
            <cbc:Name>Network cable</cbc:Name>
            <cac:SellersItemIdentification>
                <cbc:ID>JB011</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:StandardItemIdentification>
                <cbc:ID schemeID="0088">*************</cbc:ID>
            </cac:StandardItemIdentification>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="MP">********</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="STI">********</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
                <cbc:ID schemeID="UNCL5305">S</cbc:ID>
                <cbc:Percent>25</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
            <cac:AdditionalItemProperty>
                <cbc:Name>Type</cbc:Name>
                <cbc:Value>Cat5</cbc:Value>
            </cac:AdditionalItemProperty>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="NOK">0.75</cbc:PriceAmount>
            <cbc:BaseQuantity>1</cbc:BaseQuantity>
        </cac:Price>
    </cac:InvoiceLine>
</Invoice>