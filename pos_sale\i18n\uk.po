# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_sale
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "(left:"
msgstr "(залишок:"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
#, python-format
msgid "(tax incl.)"
msgstr "(з ПДВ)"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "<span class=\"o_form_label\">Down Payment Product</span>"
msgstr "<span class=\"o_form_label\">Товар авансу</span>"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "<span class=\"o_form_label\">Sales Team</span>"
msgstr "<span class=\"o_form_label\">Команда продажу</span>"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
msgid "<span style=\"margin: 0px 5px;\">:</span>"
msgstr "<span style=\"margin: 0px 5px;\">:</span>"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Apply a down payment"
msgstr "Застосувати авансовий платіж"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "Back"
msgstr "Назад"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/MobileSaleOrderManagementScreen.xml:0
#, python-format
msgid "Back to list"
msgstr "Повернутися до списку"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Cancelled"
msgstr "Скасовано"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/SetSaleOrderButton.js:0
#, python-format
msgid "Cannot access order management screen if offline."
msgstr "Не можна отримати доступ до екрану керування замовленнями в офлайні."

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Курс валюти"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Customer"
msgstr "Клієнт"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Customer loading error"
msgstr "Помилка завантаження клієнта"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Date"
msgstr "Дата"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.message_body
msgid "Delivered from"
msgstr "Доставлено з"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Do you want to load the SN/Lots linked to the Sales Order?"
msgstr ""
"Ви хочете завантажити серійний/партійний номер, пов'язаний із Замовленням на"
" продаж?"

#. module: pos_sale
#: model:product.product,name:pos_sale.default_downpayment_product
#: model:product.template,name:pos_sale.default_downpayment_product_product_template
msgid "Down Payment (POS)"
msgstr "Авансовий платіж (POS)"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__down_payment_details
msgid "Down Payment Details"
msgstr "Деталі авансового платежу"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__down_payment_product_id
msgid "Down Payment Product"
msgstr "Товар попередньої оплати"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "E.g. customer: Steward, date: 2020-05-09"
msgstr "Напр., клієнт: Стюарт, дата: 2020-05-09"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/ReceiptScreen/OrderReceipt.xml:0
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
#, python-format
msgid "From"
msgstr "Від"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__invoiced
msgid "Invoiced"
msgstr "Виставлено рахунок"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid ""
"It seems that you didn't configure a down payment product in your point of sale.\n"
"                        You can go to your point of sale configuration to choose one."
msgstr ""
"Схоже, ви не налаштували товар авансового платежу у вашій точці продажу.\n"
"                       Ви можете перейти до налаштувань вашої точки продажу, щоб вибрати його."

#. module: pos_sale
#: code:addons/pos_sale/models/sale_order.py:0
#, python-format
msgid "Linked POS Orders"
msgstr "Пов'язані замовлення точки продажу"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_origin_id
msgid "Linked Sale Order"
msgstr "Пов'язане замовлення на продаж"

#. module: pos_sale
#: code:addons/pos_sale/models/pos_order.py:0
#, python-format
msgid "Linked Sale Orders"
msgstr "Пов'язані замовлення на продаж"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Locked"
msgstr "Заблокований"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderFetcher.js:0
#: code:addons/pos_sale/static/src/js/SetSaleOrderButton.js:0
#, python-format
msgid "Network Error"
msgstr "Помилка мережі"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__pos_draft
msgid "New"
msgstr "Новий"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "Next Order List"
msgstr "Список наступних замовлень"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "No"
msgstr "Ні"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "No down payment product"
msgstr "Немає товару авансового платежу"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_sessions_open_count
msgid "Open POS Sessions"
msgstr "Відкрити сесію точки продажу"

#. module: pos_sale
#: model:ir.actions.act_window,name:pos_sale.pos_session_action_from_crm_team
msgid "Open Sessions"
msgstr "Відкрити сесії"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Order"
msgstr "Замовлення"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_line_ids
#: model:ir.model.fields,field_description:pos_sale.field_sale_order_line__pos_order_line_ids
msgid "Order lines Transfered to Point of Sale"
msgstr "Рядки замовлення перенесені в точку продажу"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__paid
msgid "Paid"
msgstr "Оплачено"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Percentage of %s"
msgstr "Відсоток %s"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Налаштування точки продажу"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Рядки замовлення точки продажу"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order
msgid "Point of Sale Orders"
msgstr "Замовлення точки продажу"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_session
msgid "Point of Sale Session"
msgstr "Сесія точки продажу"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_config_ids
msgid "Point of Sales"
msgstr "Точка продажу"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_count
msgid "Pos Order Count"
msgstr "Розрахунок замовлення точки продажу"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__pos_done
msgid "Posted"
msgstr "Опубліковано"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "Previous Order List"
msgstr "Список попередніх замовлень"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Products not available in POS"
msgstr "Товари не доступні в POS"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Quotation"
msgstr "Комерційна пропозиція"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Quotation Sent"
msgstr "Комерційну пропозицію відправлено"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
#, python-format
msgid "Quotation/Order"
msgstr "Пропозиція/Замовлення"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "SN/Lots Loading"
msgstr "Завантаження серійного/партійного номерів"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
#, python-format
msgid "SO"
msgstr "SO"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__sale_order_count
msgid "Sale Order Count"
msgstr "Підрахунок замовлення на продаж"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "Sales"
msgstr "Продажі"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Звіт аналізу продажів"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#: model:ir.model,name:pos_sale.model_sale_order
#, python-format
msgid "Sales Order"
msgstr "Замовлення на продаж"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Рядок замовлення на продаж"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_crm_team
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_session__crm_team_id
msgid "Sales Team"
msgstr "Команда продажу"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "Sales are reported to the following sales team"
msgstr "Продажі звітуються наступній команді продажів"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Salesman"
msgstr "Продавець"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#, python-format
msgid "Salesperson"
msgstr "Продавець"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Session Running"
msgstr "Запуск сесії"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_order_amount_total
msgid "Session Sale Amount"
msgstr "Сума продажної сесії"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Sessions Running"
msgstr "Запуск сесій"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
#, python-format
msgid "Set Sale Order"
msgstr "Встановіть замовлення на продаж"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Settle the order"
msgstr "Узгодити замовлення"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid ""
"Some of the products in your Sale Order are not available in POS, do you "
"want to import them?"
msgstr ""
"Деякі товари у вашому замовленні на продаж недоступні в POS, ви хочете їх "
"імпортувати?"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_line_id
msgid "Source Sale Order Line"
msgstr "Джерело рядка замовлення на продаж"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "State"
msgstr "Область"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_report__state
msgid "Status"
msgstr "Статус"

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "Курс валюти, що застосовується на дату замовлення"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "There was a problem in loading the %s customer."
msgstr "Була проблема із завантаженням клієнта %s."

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,help:pos_sale.field_pos_session__crm_team_id
msgid "This Point of sale's sales will be related to this Sales Team."
msgstr "Продажі цієї точки продажу будуть пов'язані з цією командою продажів."

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "This product will be applied when down payment is made"
msgstr "Цей товар буде застосовано при внесенні авансового платежу"

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__down_payment_product_id
msgid "This product will be used as down payment on a sale order."
msgstr ""
"Цей товар буде використовуватися як авансовий платіж при замовленні на "
"продаж."

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Total"
msgstr "Разом"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_pos_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                from Sale"
msgstr ""
"Переміщено<br/>\n"
"                                із Продажу"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                to POS"
msgstr ""
"Переміщено<br/>\n"
"                                в POS"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderFetcher.js:0
#, python-format
msgid "Unable to fetch orders if offline."
msgstr "Неможливо отримати замовлення, якщо вони в автономному режимі."

#. module: pos_sale
#: model:product.product,uom_name:pos_sale.default_downpayment_product
#: model:product.template,uom_name:pos_sale.default_downpayment_product_product_template
msgid "Units"
msgstr "Одиниці"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "What do you want to do?"
msgstr "Що би ви хотіли зробити?"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Yes"
msgstr "Так"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid ""
"You have tried to charge a down payment of %s but only %s remains to be "
"paid, %s will be applied to the purchase order line."
msgstr ""
"Ви намагалися зняти оплату за попередній платіж %s але залишилось сплатити "
"лише %s, %s буде застосовано до рядка замовлення на купівлю."
