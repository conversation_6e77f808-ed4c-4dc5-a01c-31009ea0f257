// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_AttachmentList {
    display: flex;
    flex-flow: column;
    justify-content: flex-start;
}

/* Avoid overflow of long attachment text */
.o_AttachmentList_attachment {
    margin-bottom: map-get($spacers, 1);
    margin-top: map-get($spacers, 1);
    margin-inline-end: map-get($spacers, 1);
    margin-inline-start: map-get($spacers, 0);
    max-width: 100%;
}

.o_AttachmentList_partialList {
    display: flex;
    flex: 1;
    flex-flow: wrap;
}

.o_AttachmentList_partialListNonImages {
    margin: map-get($spacers, 1);
    justify-content: flex-start;
}
