<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record
            id="account_tag_l10n_at_AAI1"
            model="account.account.tag">
            <field name="name">Bilanz AAI1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAI2"
            model="account.account.tag">
            <field name="name">Bilanz AAI2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAI3"
            model="account.account.tag">
            <field name="name">Bilanz AAI3</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAII1"
            model="account.account.tag">
            <field name="name">Bilanz AAII1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAII2"
            model="account.account.tag">
            <field name="name">Bilanz AAII2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAII3"
            model="account.account.tag">
            <field name="name">Bilanz AAII3</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAII4"
            model="account.account.tag">
            <field name="name">Bilanz AAII4</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAIII"
            model="account.account.tag">
            <field name="name">Bilanz AAIII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAIII1"
            model="account.account.tag">
            <field name="name">Bilanz AAIII1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAIII2"
            model="account.account.tag">
            <field name="name">Bilanz AAIII2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAIII3"
            model="account.account.tag">
            <field name="name">Bilanz AAIII3</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAIII4"
            model="account.account.tag">
            <field name="name">Bilanz AAIII4</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAIII5"
            model="account.account.tag">
            <field name="name">Bilanz AAIII5</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AAIII6"
            model="account.account.tag">
            <field name="name">Bilanz AAIII6</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABI1"
            model="account.account.tag">
            <field name="name">Bilanz ABI1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABI2"
            model="account.account.tag">
            <field name="name">Bilanz ABI2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABI3"
            model="account.account.tag">
            <field name="name">Bilanz ABI3</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABI4"
            model="account.account.tag">
            <field name="name">Bilanz ABI4</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABI5"
            model="account.account.tag">
            <field name="name">Bilanz ABI5</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABII1"
            model="account.account.tag">
            <field name="name">Bilanz ABII1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABII2"
            model="account.account.tag">
            <field name="name">Bilanz ABII2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABII3"
            model="account.account.tag">
            <field name="name">Bilanz ABII3</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABII4"
            model="account.account.tag">
            <field name="name">Bilanz ABII4</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABIII"
            model="account.account.tag">
            <field name="name">Bilanz ABIII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABIII1"
            model="account.account.tag">
            <field name="name">Bilanz ABIII1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABIII2"
            model="account.account.tag">
            <field name="name">Bilanz ABIII2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ABIV"
            model="account.account.tag">
            <field name="name">Bilanz ABIV</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AC"
            model="account.account.tag">
            <field name="name">Bilanz AC</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_AD"
            model="account.account.tag">
            <field name="name">Bilanz AD</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PAI"
            model="account.account.tag">
            <field name="name">Bilanz PAI</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PAII"
            model="account.account.tag">
            <field name="name">Bilanz PAII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PAII1"
            model="account.account.tag">
            <field name="name">Bilanz PAII1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PAII2"
            model="account.account.tag">
            <field name="name">Bilanz PAII2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PAIII"
            model="account.account.tag">
            <field name="name">Bilanz PAIII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PAIII1"
            model="account.account.tag">
            <field name="name">Bilanz PAIII1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PAIII2"
            model="account.account.tag">
            <field name="name">Bilanz PAIII2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PAIII3"
            model="account.account.tag">
            <field name="name">Bilanz PAIII3</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PAIV"
            model="account.account.tag">
            <field name="name">Bilanz PAIV</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PBI"
            model="account.account.tag">
            <field name="name">Bilanz PBI</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PBII"
            model="account.account.tag">
            <field name="name">Bilanz PBII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PBIII"
            model="account.account.tag">
            <field name="name">Bilanz PBIII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PBIV"
            model="account.account.tag">
            <field name="name">Bilanz PBIV</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCI"
            model="account.account.tag">
            <field name="name">Bilanz PCI</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCII"
            model="account.account.tag">
            <field name="name">Bilanz PCII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCIII"
            model="account.account.tag">
            <field name="name">Bilanz PCIII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCIV"
            model="account.account.tag">
            <field name="name">Bilanz PCIV</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCV"
            model="account.account.tag">
            <field name="name">Bilanz PCV</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCVI"
            model="account.account.tag">
            <field name="name">Bilanz PCVI</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCVII"
            model="account.account.tag">
            <field name="name">Bilanz PCVII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCVIII"
            model="account.account.tag">
            <field name="name">Bilanz PCVIII</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCVIII1"
            model="account.account.tag">
            <field name="name">Bilanz PCVIII1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCVIII2"
            model="account.account.tag">
            <field name="name">Bilanz PCVIII2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCVIII3"
            model="account.account.tag">
            <field name="name">Bilanz PCVIII3</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PCVIII4"
            model="account.account.tag">
            <field name="name">Bilanz PCVIII4</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_PD"
            model="account.account.tag">
            <field name="name">Bilanz PD</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>

        <record
            id="account_tag_l10n_at_EBIT1"
            model="account.account.tag">
            <field name="name">GuV EBIT1</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT2"
            model="account.account.tag">
            <field name="name">GuV EBIT2</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT3"
            model="account.account.tag">
            <field name="name">GuV EBIT3</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT4"
            model="account.account.tag">
            <field name="name">GuV EBIT4</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT4I"
            model="account.account.tag">
            <field name="name">GuV EBIT4I</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT4II"
            model="account.account.tag">
            <field name="name">GuV EBIT4II</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT4III"
            model="account.account.tag">
            <field name="name">GuV EBIT4III</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT5I"
            model="account.account.tag">
            <field name="name">GuV EBIT5I</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT5II"
            model="account.account.tag">
            <field name="name">GuV EBIT5II</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT6I"
            model="account.account.tag">
            <field name="name">GuV EBIT6I</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT6II"
            model="account.account.tag">
            <field name="name">GuV EBIT6II</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT7I"
            model="account.account.tag">
            <field name="name">GuV EBIT7I</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT7II"
            model="account.account.tag">
            <field name="name">GuV EBIT7II</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_EBIT8"
            model="account.account.tag">
            <field name="name">GuV EBIT8</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_FIN10"
            model="account.account.tag">
            <field name="name">GuV FIN10</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_FIN11"
            model="account.account.tag">
            <field name="name">GuV FIN11</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_FIN12"
            model="account.account.tag">
            <field name="name">GuV FIN12</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_FIN13"
            model="account.account.tag">
            <field name="name">GuV FIN13</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_FIN14"
            model="account.account.tag">
            <field name="name">GuV FIN14</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_FIN15"
            model="account.account.tag">
            <field name="name">GuV FIN15</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_TAX"
            model="account.account.tag">
            <field name="name">GuV TAX</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_MTAX"
            model="account.account.tag">
            <field name="name">GuV MTAX</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_RCR"
            model="account.account.tag">
            <field name="name">GuV RCR</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_RRR"
            model="account.account.tag">
            <field name="name">GuV RRR</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_ARR"
            model="account.account.tag">
            <field name="name">GuV ARR</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
        <record
            id="account_tag_l10n_at_RL"
            model="account.account.tag">
            <field name="name">GuV RL</field>
            <field name="applicability">accounts</field>
            <field name="color" eval="1" />
        </record>
    </data>
</odoo>
