<?xml version="1.0" encoding="utf-8" ?>
<!--
    Copyright 2017 LasLabs Inc.
    Copyright 2018 <PERSON>
    Copyright 2018 Tecnativa - Jairo Llopis
    Copyright 2021 ITerra - <PERSON>
    License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl.html).
-->
<templates id="form_view" xml:space="preserve">
    <!-- Template for buttons that display only the icon in xs -->
    <t t-name="web_responsive.icon_button">
        <i t-attf-class="fa fa-#{icon}" t-att-title="label" />
        <span class="d-none d-sm-inline" t-esc="label" />
    </t>
    <t t-name="web_responsive.MenuStatusbarButtons">
        <div class="dropdown">
            <button
                class="o_statusbar_buttons_dropdown btn btn-secondary dropdown-toggle"
                type="button"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
            >
                <t t-call="web_responsive.icon_button">
                    <t t-set="icon" t-value="'cogs'" />
                    <t t-set="label">Quick actions</t>
                </t>
            </button>
            <!-- A div.o_statusbar_buttons.dropdown-menu
                 is appended here from JS -->
        </div>
    </t>
    <t t-extend="FormView.buttons">
        <!-- Change "Edit" button hotkey to "E" -->
        <t t-jquery=".o_form_button_edit" t-operation="attributes">
            <attribute name="accesskey">e</attribute>
        </t>
        <!-- Change "Discard" button hotkey to "D" -->
        <t t-jquery=".o_form_button_cancel" t-operation="attributes">
            <attribute name="accesskey">d</attribute>
        </t>
        <!-- Add responsive icons to buttons -->
        <t t-jquery=".o_form_button_edit" t-operation="inner">
            <t t-call="web_responsive.icon_button">
                <t t-set="icon" t-value="'pencil'" />
                <t t-set="label">Edit</t>
            </t>
        </t>
        <t t-jquery=".o_form_button_create" t-operation="inner">
            <t t-call="web_responsive.icon_button">
                <t t-set="icon" t-value="'plus'" />
                <t t-set="label">Create</t>
            </t>
        </t>
        <t t-jquery=".o_form_button_save" t-operation="inner">
            <t t-call="web_responsive.icon_button">
                <t t-set="icon" t-value="'check'" />
                <t t-set="label">Save</t>
            </t>
        </t>
        <t t-jquery=".o_form_button_cancel" t-operation="inner">
            <t t-call="web_responsive.icon_button">
                <t t-set="icon" t-value="'times'" />
                <t t-set="label">Discard</t>
            </t>
        </t>
    </t>
    <t t-extend="KanbanView.buttons">
        <!-- Add responsive icons to buttons -->
        <t t-jquery="button" t-operation="inner">
            <t t-call="web_responsive.icon_button">
                <t t-set="icon" t-value="'plus'" />
                <t t-set="label" t-value="create_text || _t('Create')" />
            </t>
        </t>
    </t>
    <t t-extend="ListView.buttons">
        <!-- Change "Discard" button hotkey to "D" -->
        <t t-jquery=".o_list_button_discard" t-operation="attributes">
            <attribute name="accesskey">d</attribute>
        </t>
        <!-- Add responsive icons to buttons -->
        <t t-jquery=".o_list_button_add" t-operation="inner">
            <t t-call="web_responsive.icon_button">
                <t t-set="icon" t-value="'plus'" />
                <t t-set="label">Create</t>
            </t>
        </t>
        <t t-jquery=".o_list_button_save" t-operation="inner">
            <t t-call="web_responsive.icon_button">
                <t t-set="icon" t-value="'check'" />
                <t t-set="label">Save</t>
            </t>
        </t>
        <t t-jquery=".o_list_button_discard" t-operation="inner">
            <t t-call="web_responsive.icon_button">
                <t t-set="icon" t-value="'times'" />
                <t t-set="label">Discard</t>
            </t>
        </t>
    </t>
    <t t-extend="CalendarView.navigation_buttons">
        <!-- Add responsive icons to buttons -->
        <t t-jquery=".o_calendar_button_today" t-operation="inner">
            <t t-call="web_responsive.icon_button">
                <t t-set="icon" t-value="'calendar-check-o'" />
                <t t-set="label">Today</t>
            </t>
        </t>
    </t>
</templates>
