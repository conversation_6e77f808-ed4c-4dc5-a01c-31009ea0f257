
.o_list_view {
    thead {
        background-color: #eee;
    }

    tbody > tr.o_group_header {
        background-image: linear-gradient(to bottom, #fcfcfc, #dedede);

        &:focus-within {
           background-image: linear-gradient(to bottom, #fcfcfc, $o-form-lightsecondary);
        }
    }

    .o_list_table .o_data_row.o_selected_row > .o_data_cell:not(.o_readonly_modifier):not(.o_invisible_modifier):not(.o_remaining_days_cell) {
        padding: .15rem;
        .o_input {
            border: none;
            padding: .15rem;
            .o_input {
                box-shadow: none;
                padding: 0;
            }
        }
        select.o_input {
            padding: .25rem 0;
        }
        .o_field_boolean {
            padding-top: .15rem;
            padding-bottom: .15rem;
        }
        &.o_invalid_cell .o_input {
            box-shadow: 0 0 0 1px theme-color('danger');
        }
        &.o_list_button {
            padding: .25rem;
        }
        &.o_color_cell > .o_field_widget {
            padding: .15rem;
        }
    }
}
