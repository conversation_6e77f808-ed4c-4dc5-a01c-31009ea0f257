<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <path id="path-1" d="M52 0v17H0V0h52zM14.391 2H4.61a.59.59 0 0 0-.43.179.586.586 0 0 0-.179.43v9.782c0 .167.06.31.179.43.12.12.263.179.43.179h5.263V8.739H8.44V7.077h1.432V5.853c0-.697.195-1.237.584-1.619.39-.382.908-.573 1.558-.573a9.6 9.6 0 0 1 1.274.065v1.482l-.873.007c-.325 0-.543.067-.656.201-.112.134-.168.334-.168.602v1.06h1.64l-.215 1.66h-1.425V13h2.8a.59.59 0 0 0 .43-.179.586.586 0 0 0 .179-.43V2.61a.59.59 0 0 0-.179-.43.586.586 0 0 0-.43-.179z"/>
    <filter id="filter-2" width="101.9%" height="111.8%" x="-1%" y="-2.9%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <rect id="path-3" width="22" height="2" x="21" y="3"/>
    <filter id="filter-4" width="104.5%" height="200%" x="-2.3%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0.259587944   0 0 0 0 0.259629577   0 0 0 0 0.259574831  0 0 0 0.525895979 0"/>
    </filter>
    <path id="path-5" d="M37 11v1H21v-1h16zm4-3v1H21V8h20z"/>
    <filter id="filter-6" width="105%" height="150%" x="-2.5%" y="-12.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_facebook_page">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 22)">
        <g class="rectangle_2">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-1"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#000" fill-opacity=".697" xlink:href="#path-3"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
          <use fill="#000" fill-opacity=".348" xlink:href="#path-5"/>
        </g>
      </g>
    </g>
  </g>
</svg>
