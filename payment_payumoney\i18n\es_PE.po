# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_payumoney
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Spanish (Peru) (https://www.transifex.com/odoo/teams/41243/es_PE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_PE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer_payumoney_merchant_key
msgid "Merchant Key"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer_payumoney_merchant_salt
msgid "Merchant Salt"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:146
#, python-format
msgid "PayUmoney: feedback error"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:108
#, python-format
msgid "PayUmoney: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:102
#, python-format
msgid "PayUmoney: received data for reference %s; multiple orders found"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:99
#, python-format
msgid "PayUmoney: received data for reference %s; no order found"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:94
#, python-format
msgid ""
"PayUmoney: received data with missing reference (%s) or pay_id (%s) or "
"shashign (%s)"
msgstr ""

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr ""
