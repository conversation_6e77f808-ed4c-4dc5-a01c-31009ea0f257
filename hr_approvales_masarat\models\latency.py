# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, date, timedelta
from dateutil.relativedelta import relativedelta
from pytz import timezone
from odoo.exceptions import ValidationError


class HrEmployeeLatency(models.Model):

    _name = "hr.masarat.latency"

    _inherit = ['mail.thread', 'mail.activity.mixin']

    _rec_name = 'latency_name'


    @api.model
    def default_get(self, fields):
        res = super(HrEmployeeLatency, self).default_get(fields)
        user_id = self._context.get('uid')
        employee_id = self.env['hr.employee'].search([('user_id','=',user_id)])
        res['employee_id'] = employee_id.id
        ## Check For Hr Group
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        if hr_group:
            res['is_hr_group'] = 'yes'
        else:
            res['is_hr_group'] = 'no'
        ########################
        return res

    latency_name = fields.Char(compute='get_latency_name', store=True)

    state = fields.Selection(selection=[('draft', 'Draft'),
                                        ('manager_approval', 'Manager Approval'),
                                        ('manager_refused', 'Manager Refused'),
                                        ('hr_approval', 'HR Approval'),
                                        ('hr_refused', 'HR Refused')], default='draft', string="State")

    request_date = fields.Date(string="Date of Request", readonly=True, default=lambda self: fields.Date.to_string(date.today()))
    request_date_3days = fields.Date(string="5 days of Request", readonly=True,
                               default=lambda self: fields.Date.to_string(date.today()-timedelta(days=100)))
    employee_id = fields.Many2one('hr.employee', string="Employee")
    manager_id = fields.Many2one('hr.employee',readonly=True, related='employee_id.parent_id',string="Manager")
    #manager_id = fields.Char()
    #start_work_day = fields.Char(string='Start Work-Day',readonly=True, store=True , related='employee_id.start_work')
    start_work_day = fields.Char(string='Start Work-Day',readonly=True, store=True , compute="_get_work_day")
    # end_work_day = fields.Char(string='End Work-Day',readonly=True, store=True , compute="_get_work_day")

    absence_type = fields.Selection(selection=[('work_outside','تكليف بعمل'),
                                               ('emmergency', 'ظرف طارئ'),
                                               ('other_reason', 'أسباب أخرى')],string="Latency Type", required=True)

    # choosen_day = fields.Date(string="Date",default=False)
    # check_in = fields.Datetime(string="Chick-In Time Stamp",readonly=True,compute="get_date_time")

    check_in = fields.Datetime(string="Chick-In Time Stamp", readonly=True, related='check_in_attendacy.check_in')
    check_in_attendacy = fields.Many2one('hr.attendance',required=True, domain="[('employee_id','=',employee_id),('attendance_date','<',request_date),('attendance_date','>',request_date_3days)]")

    latency = fields.Integer(string="Latency In Minutes", readonly=True,related='check_in_attendacy.checkin_latency')
    #latency = fields.Float(string="Latency In Minutes",readonly=True,compute="get_letency")
    # approved_latency_by_manager = fields.Float(string="Latency Approved by Manager In Minutes",related="check_in_attendacy.attendance_type_id.latency_allawence_approved", store=True)

    Note = fields.Text(string="Details and Reasons for Latency")

    is_manager = fields.Char(compute='call_with_sudo_is_manager')
    is_hr_group = fields.Char(compute='call_with_sudo_is_hr_group')

    def get_if_hr_group(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for rec in self:
            if hr_group:
                rec.is_hr_group = 'yes'
            else:
                rec.is_hr_group = 'no'

    def compute_button_visible(self):
        for rec in self:
            if rec.manager_id.user_id.id == self._uid:
                rec.is_manager = '1'
            else:
                rec.is_manager = '0'

    @api.onchange('employee_id')
    def call_with_sudo_is_manager(self):
        self.sudo().compute_button_visible()

    @api.depends('is_hr_group')
    def call_with_sudo_is_hr_group(self):
        self.sudo().get_if_hr_group()

    def convert_TZ_UTC(self, TZ_datetime):
        fmt = "%Y-%m-%d %H:%M:%S"
        # Current time in UTC
        now_utc = datetime.now(timezone('UTC'))
        # Convert to current user time zone
        now_timezone = now_utc.astimezone(timezone(self.env.user.tz))
        UTC_OFFSET_TIMEDELTA = datetime.strptime(now_utc.strftime(fmt), fmt) - datetime.strptime(
            now_timezone.strftime(fmt), fmt)
        local_datetime = datetime.strptime(str(TZ_datetime), fmt)
        result_utc_datetime = local_datetime - UTC_OFFSET_TIMEDELTA
        # return result_utc_datetime.strftime(fmt)
        return result_utc_datetime


    @api.depends('check_in_attendacy')
    def _get_work_day(self):
        for elem in self:
            elem.start_work_day = False
            if elem.check_in_attendacy:
                resource_calendar_id = elem.employee_id.contract_id.resource_calendar_id.id
                today=elem.check_in.weekday()
                # print(resource_calendar_id,str(today))
                calender = self.env['resource.calendar.attendance'].search([('calendar_id','=',resource_calendar_id),('dayofweek','=',str(today))])
                # print(calender, calender.hour_from, calender.hour_to)
                elem.start_work_day='{0:02.0f}:{1:02.0f}'.format(*divmod(calender.hour_from * 60, 60))+':00'


    @api.depends('employee_id', 'check_in')
    def get_latency_name(self):
        for elem in self:
            elem.latency_name = False
            if elem.employee_id and elem.check_in:
                elem.latency_name = elem.employee_id.name + '-Latency Approval-' + str(elem.check_in)[:10]

    def make_cancel_approval(self):
        self.state = 'draft'
    def make_manager_approval(self):
        self.state = 'manager_approval'
    def make_manager_refused(self):
        self.state = 'manager_refused'
    def make_hr_approval(self):
        self.state = 'hr_approval'
    def make_hr_refused(self):
        self.state = 'hr_refused'


    # @api.onchange('choosen_day')
    # def check_availability(self):
    #     for elem in self:
    #         # if elem.choosen_day and datetime.strptime(str(elem.choosen_day),'%Y-%m-%d').weekday() in (4,5):
    #         #     raise ValidationError("It's a Weekend :" + str(elem.choosen_day))
    #         leave_obj = self.env['hr.leave.report'].search_count([('employee_id','=',elem.employee_id.id),('state','=','validate'), ('leave_type', '=', 'request'),('date_to','>=',elem.choosen_day),('date_from','<=',elem.choosen_day)])
    #         if leave_obj:
    #             raise ValidationError("You have Approved Holidays in the Chosen Day")

    @api.constrains('check_in_attendacy')
    def constrains_get_letency(self):
        for elem in self:
            if elem.check_in_attendacy and not elem.latency:
                raise ValidationError("There is No latency!")

    # @api.depends('choosen_day')
    # def get_date_time(self):
    #     for elem in self:
    #         if elem.choosen_day and elem.employee_id:
    #             self.env.cr.execute("select check_in from hr_attendance where employee_id = %s and substring(check_in::VARCHAR,1,10) = %s limit 1",(elem.employee_id.id,str(elem.choosen_day)))
    #             attens = self.env.cr.fetchall()
    #             try:
    #                 elem.check_in = attens[0][0]
    #             except:
    #                 raise ValidationError("There's No Check-IN Records at:"+str(elem.choosen_day))
    #         else:
    #             elem.check_in = False
    
    def unlink(self):
        for elem in self:
            if elem.state !='draft':
                raise ValidationError('You cannot delete a Latency request which is not in draft state')
            return super(HrEmployeeLatency, self).unlink()

    @api.constrains('check_in_attendacy')
    def make_sure_unique_attendance(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for elem in self:
            count = self.env['hr.masarat.latency'].search_count([('check_in_attendacy','=',elem.check_in_attendacy.id)])
            if count >1:
                raise ValidationError('This CheckIn is Already under process!')
            days_count = (elem.request_date - elem.check_in_attendacy.attendance_date).days
            if days_count >= 4 and (not hr_group):
                raise ValidationError('You Exceeded 4 days for the request!')


    # def action_send_notification_to_maneger(self):
    #     template_id = self.env.ref('hr_approvales_masarat.latency_approval_template').id
    #     self.env['mail.template'].browse(template_id).send_mail(self.id, force_send=True)
    #
    #
    # @api.model
    # def create(self, vals_list):
    #     obj = super(HrEmployeeLatency, self).create(vals_list)
    #     self.sudo().action_send_notification_to_maneger()
    #     return obj

    def action_send_notification_to_maneger(self, employee_id, recode_id):
        employee = self.env['hr.employee'].search([('id', '=', employee_id)])
        email_to = employee.parent_id.work_email
        email_from = employee.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)
        body = """
        <div dir="rtl">
            <p><font style="font-size: 14px;">Your Employee """ + employee.name + """, requested latency approval, </font></p>
            <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            <a href="%s">Request Link</a>
        </div>""" % (web_base_url)
        template_id = self.env['mail.mail'].create({
            'subject': 'أذن تأخير',
            'email_from': email_from,
            'email_to': email_to,
            'body_html': body})
        template_id.send()

    @api.model
    def create(self, vals_list):
        obj = super(HrEmployeeLatency, self).create(vals_list)
        #### check if there is 2 exit permision this month
        get_attendancy_date = self.env['hr.attendance'].search([('id','=',vals_list['check_in_attendacy'])]).attendance_date
        get_employee_allawance = obj.sudo().employee_id.contract_id.resource_calendar_id.latency_approval_count
        # get_employee_allawance = obj.employee_id.contract_id.resource_calendar_id.latency_approval_count
        date_from = str(datetime.strptime(str(get_attendancy_date), '%Y-%m-%d').replace(day=1))
        date_to = str((datetime.strptime(str(get_attendancy_date), '%Y-%m-%d') + relativedelta(months=+1, day=1, days=-1)))
        search_for_requist = self.env['hr.masarat.latency'].search_count([('employee_id', '=', obj.employee_id.id), ('check_in', '>=', date_from),('check_in', '<=', date_to)])
        if search_for_requist  > get_employee_allawance+1:
            raise ValidationError(' لقد تجاوزت الحد المسموح به للطلبات الحد الأقصى : ' + str(get_employee_allawance))
        #######################################

        recode_id = obj.id
        employee_id = obj.employee_id.id
        self.sudo().action_send_notification_to_maneger(employee_id, recode_id)
        return obj



class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'

    unapproved_latency_minutes = fields.Integer(string='Un-Approved Latency', compute='get_unapproved_latency_minutes', store=True)


    @api.depends('employee_id','date_to','date_from')
    def get_unapproved_latency_minutes(self):
        for payslip in self:
            payslip.unapproved_latency_minutes=0
            if not payslip.employee_id.contract_id.resource_calendar_id.there_is_letancy:
                continue
            if payslip.date_from and payslip.date_to:
                fmt = '%Y-%m-%d'
                pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'
                if pre_date:
                    attendance_date_from = (datetime.strptime(pre_date, fmt))
                    attendance_date_to = (datetime.strptime(pre_date, fmt) + relativedelta(months=1, day=1, days=-1)).strftime(fmt)
                latencies = self.env['hr.attendance'].search([('employee_id', '=', payslip.employee_id.id), ('attendance_date','>=',attendance_date_from), ('attendance_date','<=',attendance_date_to)])
                for elem in latencies:
                    payslip.unapproved_latency_minutes+=int(elem.computed_latency)
