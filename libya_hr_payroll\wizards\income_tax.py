# -*- coding: utf-8 -*-

from odoo import fields, models
from dateutil.relativedelta import relativedelta
from datetime import date, datetime


class CreateWizard(models.TransientModel):
    _name = 'income.tax'
    _description = 'Income Taxes'

    date_from = fields.Date("Date From",default=lambda self: fields.Date.to_string(date.today().replace(day=1)),required=True)
    date_to = fields.Date("Date To",default=lambda self: fields.Date.to_string((datetime.now() + relativedelta(months=+1, day=1, days=-1)).date()),required=True)

    def _get_report_pdf(self):
        exist_payslip = self.env['hr.payslip'].search([('date_from','>=',self.date_from),('date_to','<=',self.date_to),('struct_id.name', '=','يخضع للضمان والضرائب' )])
        date_month = datetime.strptime(
            self.date_from.strftime('%Y-%m-%d'), '%Y-%m-%d')
        payslip_ids = []
        ZAMALA = 0
        GDED = 0
        TXDED = 0
        DMGHA = 0
        NET = 0
        for payslip in exist_payslip:
            ZAMALA += payslip.line_ids.filtered(lambda line: line.code == 'ZAMALA').total
            GDED += payslip.line_ids.filtered(lambda line: line.code == 'GDED').total
            TXDED += payslip.line_ids.filtered(lambda line: line.code == 'TXDED').total
            DMGHA += payslip.line_ids.filtered(lambda line: line.code == 'DMGHA').total
            NET += payslip.line_ids.filtered(lambda line: line.code == 'NETSALARY').total

        total = ZAMALA + GDED + TXDED + DMGHA
        total_tax = 0.005 * total
        totals = total + total_tax

        return {
            'ZAMALA': ZAMALA,
            'GDED': GDED,
            'TXDED': TXDED,
            'DMGHA': DMGHA,
            'NET': NET,
            'total': total,
            'total_tax': total_tax,
            'totals': totals,
            'date_month':date_month.month,
            'date_year' : date_month.year
        }

    def generate_income_taxes(self):
        data = self._get_report_pdf()
        return self.env.ref('libya_hr_payroll.report_payroll_libya_income_pdf').report_action([],data=data)







