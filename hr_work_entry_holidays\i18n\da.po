# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_holidays
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2021
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: lhmflexerp <<EMAIL>>, 2023\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_work_entry_holidays
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: Time Off"
msgstr "%s: Fri"

#. module: hr_work_entry_holidays
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"A leave cannot be set across multiple contracts with different working schedules.\n"
"\n"
"Please create one time off for each contract.\n"
"\n"
"Time off:\n"
"%s\n"
"\n"
"Contracts:\n"
"%s"
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_contract
msgid "Employee Contract"
msgstr "Ansættelseskontrakt"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid ""
"Every new time off type in this list will be reported as select work entry "
"in payslip."
msgstr ""

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR arbejdspostering"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR arbejdsposteringstype"

#. module: hr_work_entry_holidays
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays.work_entry_type_leave_form_inherit
msgid "Payroll"
msgstr "Løn"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid "Status"
msgstr "Status"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"Status er sat til 'At indsende', når en anmodning om fri oprettes.\n"
"Status er 'At godkende', når en anmodning om fri godkendes af bruger.\n"
"Status er 'Afvist', når en anmodning om fri afvises af leder.\n"
"Status er 'Godkendt', når en anmodning om fri godkendes af leder."

#. module: hr_work_entry_holidays
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"There is no employee set on the time off. Please make sure you're logged in "
"the correct company."
msgstr ""
"Der er ingen ansat angivet på fri perioden. Tjek venligst at du er logget "
"ind i den korrekte virksomhed."

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_id
msgid "Time Off"
msgstr "Fri"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid "Time Off Type"
msgstr "Fri type"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_leave_type__work_entry_type_id
msgid "Work Entry Type"
msgstr "Arbejdsposterings type"
