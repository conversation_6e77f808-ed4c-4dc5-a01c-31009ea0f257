# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mail_bot
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:05+0000\n"
"PO-Revision-Date: 2018-10-02 10:05+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:57
#, python-format
msgid ""
"Aaaaaw that's really cute but, you know, bots don't work that way. You're "
"too human for me! Let's keep it professional ❤️"
msgstr ""

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_res_partner
msgid "Contact"
msgstr "ទំនាក់ទំនង"

#. module: mail_bot
#: selection:res.users,odoobot_state:0
msgid "Disabled"
msgstr ""

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_channel
msgid "Discussion channel"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_mail_bot__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_thread
msgid "Email Thread"
msgstr ""

#. module: mail_bot
#. openerp-web
#: code:addons/mail_bot/static/src/js/mailbot_service.js:42
#, python-format
msgid "Enable desktop notifications to chat"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:42
#, python-format
msgid ""
"Great! 👍<br/>Now, try to <b>send an attachment</b>, like a picture of your "
"cute dog..."
msgstr ""

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_channel.py:38
#, python-format
msgid ""
"Hello,<br/>Odoo's chat helps employees collaborate efficiently. I'm here to "
"help you discover its features.<br/><b>Try to send me an emoji :)</b>"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:74
#, python-format
msgid "Hmmm..."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:75
#, python-format
msgid "I'm afraid I don't understand. Sorry!"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:59
#, python-format
msgid ""
"I'm just a bot... :( You can check <a "
"href=\"https://www.odoo.com/page/docs\">our documentation</a>) for more "
"information!"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:73
#, python-format
msgid ""
"I'm not smart enough to answer your question.<br/>To follow my guide, ask"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_mail_bot__id
msgid "ID"
msgstr "ID"

#. module: mail_bot
#: selection:res.users,odoobot_state:0
msgid "Idle"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_mail_bot____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:45
#, python-format
msgid ""
"Not a cute dog, but you get it 😊<br/>To access special features, <b>start "
"your sentence with '/'</b>. Try to get help."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:65
#, python-format
msgid ""
"Not exactly. To continue the tour, send an emoji, <b>type \":)\"</b> and "
"press enter."
msgstr ""

#. module: mail_bot
#: selection:res.users,odoobot_state:0
msgid "Not initialized"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:69
#, python-format
msgid ""
"Not sure wat you are doing. Please press / and wait for the propositions. "
"Select \"help\" and press enter"
msgstr ""

#. module: mail_bot
#. openerp-web
#: code:addons/mail_bot/static/src/js/systray_messaging_menu.js:64
#, python-format
msgid ""
"Odoo has now the permission to send you native notifications on this device."
msgstr ""

#. module: mail_bot
#. openerp-web
#: code:addons/mail_bot/static/src/js/systray_messaging_menu.js:61
#, python-format
msgid ""
"Odoo will not have the permission to send native notifications on this "
"device."
msgstr ""

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr ""

#. module: mail_bot
#. openerp-web
#: code:addons/mail_bot/static/src/js/mailbot_service.js:39
#, python-format
msgid "OdooBot has a request"
msgstr ""

#. module: mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding attachement"
msgstr ""

#. module: mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding canned"
msgstr ""

#. module: mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding command"
msgstr ""

#. module: mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding emoji"
msgstr ""

#. module: mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding ping"
msgstr ""

#. module: mail_bot
#. openerp-web
#: code:addons/mail_bot/static/src/js/systray_messaging_menu.js:60
#, python-format
msgid "Permission denied"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:79
#, python-format
msgid "Pong."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:76
#, python-format
msgid ""
"Sorry I'm sleepy. Or not! Maybe I'm just trying to hide my unawareness of "
"human language...<br/>I can show you features if you write"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:71
#, python-format
msgid ""
"Sorry, I am not listening. To get someone's attention, <b>ping him</b>. "
"Write \"@odoobot\" and select me."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:61
#, python-format
msgid "That's not nice! I'm a bot but I have feelings... 💔"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:67
#, python-format
msgid ""
"To <b>send an attachment</b>, click the 📎 icon on the right, and select a "
"file."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:54
#, python-format
msgid "To start, try to send me an emoji :)"
msgstr ""

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_res_users
msgid "Users"
msgstr "អ្នកប្រើ"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:48
#, python-format
msgid ""
"Wow you are a natural!<br/>Ping someone to grab its attention with "
"@nameoftheuser. <b>Try to ping me using @OdooBot</b> in a sentence."
msgstr ""

#. module: mail_bot
#. openerp-web
#: code:addons/mail_bot/static/src/js/systray_messaging_menu.js:63
#, python-format
msgid "Yay, push notifications are enabled!"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:51
#, python-format
msgid ""
"Yep, I am here! 🎉 <br/>You finished the tour, you can <b>close this chat "
"window</b>. Enjoy discovering Odoo."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:79
#, python-format
msgid "Yep, OdooBot is in the place!"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:60
#, python-format
msgid "fuck"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:58
#, python-format
msgid "help"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:56
#, python-format
msgid "i love you"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:56
#, python-format
msgid "love"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:52
#: code:addons/mail_bot/models/mail_bot.py:73
#: code:addons/mail_bot/models/mail_bot.py:76
#, python-format
msgid "start the tour"
msgstr ""
