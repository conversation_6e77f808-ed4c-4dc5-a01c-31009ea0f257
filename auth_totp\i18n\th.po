# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON>ano<PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_totp
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "%(browser)s on %(platform)s"
msgstr "%(browser)s บน %(platform)s"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_wizard
msgid "2-Factor Setup Wizard"
msgstr "วิซาร์ดการตั้งค่า 2-Factor"

#. module: auth_totp
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "2-Factor authentication is now enabled."
msgstr "เปิดใช้งานการตรวจสอบสิทธิ์แบบ 2 ปัจจัยแล้ว"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<span attrs=\"{'invisible': [('totp_enabled', '=', False)]}\" class=\"text-"
"muted\">Your account is protected!</span>"
msgstr ""
"<span attrs=\"{'invisible': [('totp_enabled', '=', False)]}\" class=\"text-"
"muted\">บัญชีของคุณได้รับการคุ้มครอง!</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-md-none d-block\">Or install an authenticator app</span>\n"
"                                        <span class=\"d-none d-md-block\">Install an authenticator app on your mobile device</span>"
msgstr ""
"<span class=\"d-md-none d-block\">หรือติดตั้งแอปรับรองความถูกต้อง</span>\n"
"                                        <span class=\"d-none d-md-block\">ติดตั้งแอปตรวจสอบความถูกต้องบนอุปกรณ์มือถือของคุณ</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-none d-md-block\">When requested to do so, scan the barcode below</span>\n"
"                                    <span class=\"d-block d-md-none\">When requested to do so, copy the key below</span>"
msgstr ""
"<span class=\"d-none d-md-block\">เมื่อได้รับการร้องขอให้สแกนบาร์โค้ดด้านล่าง</span>\n"
"                                    <span class=\"d-block d-md-none\">เมื่อได้รับการร้องขอให้คัดลอกคีย์ด้านล่าง</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"text-muted\">Popular ones include Authy, Google Authenticator "
"or the Microsoft Authenticator.</span>"
msgstr ""
"<span class=\"text-muted\">รายการยอดนิยม ได้แก่ Authy, Google Authenticator "
"หรือ Microsoft Authenticator</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Account Security"
msgstr "ความปลอดภัยของบัญชี"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Activate"
msgstr "เปิดใช้งาน"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Added On"
msgstr "เพิ่มลงบน"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Are you sure? Two-factor authentication will be required again on all your "
"devices"
msgstr ""
"คุณแน่ใจไหม? "
"คุณจะต้องใช้การตรวจสอบสิทธิ์แบบสองปัจจัยอีกครั้งบนอุปกรณ์ทั้งหมดของคุณ"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Authentication Code"
msgstr "โค้ดรับรองความถูกต้อง"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_device
msgid "Authentication Device"
msgstr "อุปกรณ์ตรวจสอบสิทธิ์"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Authenticator App Setup"
msgstr "การตั้งค่าแอป Authenticator"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cancel"
msgstr "ยกเลิก"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cannot scan it?"
msgstr "ไม่สามารถสแกนได้?"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Click on this link to open your authenticator app"
msgstr "คลิกที่ลิงก์นี้เพื่อเปิดแอปรับรองความถูกต้องของคุณ"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__create_date
msgid "Creation Date"
msgstr "วันที่สร้าง"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__name
msgid "Description"
msgstr "คำอธิบาย"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Device Name"
msgstr "ชื่ออุปกรณ์"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Disable 2FA"
msgstr "ปิดการใช้งาน 2FA"

#. module: auth_totp
#: model:ir.actions.server,name:auth_totp.action_disable_totp
msgid "Disable two-factor authentication"
msgstr "ปิดใช้งานการรับรองความถูกต้องด้วยสองปัจจัย"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__display_name
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Don't ask again on this device"
msgstr "ไม่ต้องถามอีกบนอุปกรณ์นี้"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Enable 2FA"
msgstr "เปิดใช้งาน2FA"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Enter your six-digit code below"
msgstr "ใส่รหัสหกหลักของคุณด้านล่าง"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__id
msgid "ID"
msgstr "ไอดี"

#. module: auth_totp
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "Invalid authentication code format."
msgstr "รูปแบบโค้ดรับรองสิทธิ์ไม่ถูกต้อง"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device____last_update
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Learn More"
msgstr "เรียนรู้เพิ่มเติม"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Login"
msgstr "เข้าสู่ระบบ"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Look for an \"Add an account\" button"
msgstr "มองหาปุ่ม \"เพิ่มบัญชี\""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Apple Store"
msgstr "บน Apple Store"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Google Play"
msgstr "บน Google Play"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__qrcode
msgid "Qrcode"
msgstr "Qrcode"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke"
msgstr "เพิกถอน"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke All"
msgstr "เพิกถอนทั้งหมด"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__scope
msgid "Scope"
msgstr "ขอบเขต"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__secret
msgid "Secret"
msgstr "ความลับ"

#. module: auth_totp
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "The verification code should only contain numbers"
msgstr "รหัสยืนยันควรมีเฉพาะตัวเลข"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid ""
"To login, enter below the six-digit authentication code provided by your Authenticator app.\n"
"                        <br/>"
msgstr ""
"ในการเข้าสู่ระบบ ให้ป้อนรหัสการตรวจสอบสิทธิ์หกหลักด้านล่างที่แอป Authenticator ให้มา\n"
"                        <br/>"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_secret
msgid "Totp Secret"
msgstr "Totp Secret"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Trusted Device"
msgstr "อุปกรณ์ที่เชื่อถือได้"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_trusted_device_ids
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Trusted Devices"
msgstr "อุปกรณ์ที่เชื่อถือได้"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-Factor Authentication Activation"
msgstr "การเปิดใช้งานการรับรองความถูกต้องด้วยสองปัจจัย"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Two-factor Authentication"
msgstr "การรับรองความถูกต้องแบบสองปัจจัย"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                                The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                                Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""
"การรับรองความถูกต้องแบบสองปัจจัย (\"2FA\") เป็นระบบการตรวจสอบสิทธิ์แบบคู่\n"
"                                อันแรกใช้รหัสผ่านของคุณและอันที่สองใช้โค้ดที่คุณได้รับจากแอปมือถือเฉพาะ\n"
"                                รายการยอดนิยม ได้แก่ Authy, Google Authenticator หรือ Microsoft Authenticator"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                            The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                            Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""
"การรับรองความถูกต้องแบบสองปัจจัย (\"2FA\") เป็นระบบการตรวจสอบสิทธิ์แบบคู่\n"
"                            อันแรกใช้รหัสผ่านของคุณและอันที่สองใช้โค้ดที่คุณได้รับจากแอปมือถือเฉพาะ\n"
"                            รายการยอดนิยม ได้แก่ Authy, Google Authenticator หรือ Microsoft Authenticator"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_enabled
msgid "Two-factor authentication"
msgstr "การรับรองความถูกต้องแบบสองปัจจัย"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Disabled"
msgstr "ปิดใช้งานการรับรองความถูกต้องแบบสองปัจจัย"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Enabled"
msgstr "เปิดใช้งานการรับรองความถูกต้องแบบสองปัจจัย"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication already enabled"
msgstr "เปิดใช้งานการรับรองความถูกต้องแบบสองปัจจัยแล้ว"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication can only be enabled for yourself"
msgstr ""
"การตรวจสอบสิทธิ์แบบสองปัจจัยสามารถเปิดใช้งานได้สำหรับตัวคุณเองเท่านั้น"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication disabled for the following user(s): %s"
msgstr "การรับรองความถูกต้องแบบสองปัจจัยถูกปิดใช้งานสำหรับผู้ใช้ต่อไปนี้:%s"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__url
msgid "Url"
msgstr "Url"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__user_id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__user_id
msgid "User"
msgstr "ผู้ใช้"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_res_users
msgid "Users"
msgstr "ผู้ใช้"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__code
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Verification Code"
msgstr "โค้ดยืนยัน"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "Verification failed, please double-check the 6-digit code"
msgstr "การยืนยันล้มเหลว โปรดตรวจสอบรหัส 6 หลักอีกครั้ง"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "e.g. 123456"
msgstr "เช่น 123456"
