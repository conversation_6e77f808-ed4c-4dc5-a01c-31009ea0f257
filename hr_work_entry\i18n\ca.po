# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# Jonatan Gk, 2022
# <PERSON><PERSON><PERSON>, 2022
# jabe<PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: jabelchi, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_work_entry
#: code:addons/hr_work_entry/models/hr_employee.py:0
#, python-format
msgid "%s work entries"
msgstr "%s entrades de treball"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "<span class=\"ml8\">Hours</span>"
msgstr "<span class=\"ml8\">Hores</span>"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__active
msgid "Active"
msgstr "Actiu"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Archived"
msgstr "Arxivat"

#. module: hr_work_entry
#: model:hr.work.entry.type,name:hr_work_entry.work_entry_type_attendance
msgid "Attendance"
msgstr "Assistència"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "Beginning"
msgstr "Inici"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__cancelled
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__code
msgid ""
"Carefull, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"Compte, el Codi s'utilitza en moltes referències, canviar-lo podria donar "
"lloc a canvis no desitjats."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__code
msgid "Code"
msgstr "Codi"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__color
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__color
msgid "Color"
msgstr "Color"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__company_id
msgid "Company"
msgstr "Empresa"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__conflict
msgid "Conflict"
msgstr "Conflicte"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Conflicting"
msgstr "Conflicte"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__conflict
msgid "Conflicts"
msgstr "Conflictes"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_date
msgid "Created on"
msgstr "Creat el"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Current Month"
msgstr "Mes actual"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Date"
msgstr "Data"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__department_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Department"
msgstr "Departament"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__draft
msgid "Draft"
msgstr "Esborrany"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_kanban
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__employee_id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__employee_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Employee"
msgstr "Empleat"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "End"
msgstr "Fi"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_start
msgid "From"
msgstr "Des de"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Entrada de treball HR"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Tipus d'entrada de treball de RH"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__id
msgid "ID"
msgstr "ID"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the work "
"entry type without removing it."
msgstr ""
"Si el camp actiu està establert a fals, us permetrà amagar el tipus "
"d'entrada de treball sense eliminar-lo."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee____last_update
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry____last_update
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__user_id
msgid "Me"
msgstr "Jo"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__name
msgid "Name"
msgstr "Nom"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Note: Validated work entries cannot be modified."
msgstr "Nota: Les ordres de treball validades no es poden modificar."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__duration
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Period"
msgstr "Període"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Detall del temps lliure dels recursos"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Search Work Entry"
msgstr "Buscar entrada de la feina"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
msgid "Search Work Entry Type"
msgstr "Buscar tipus d'entrada de treball"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: hr_work_entry
#. openerp-web
#: code:addons/hr_work_entry/static/src/xml/work_entry_templates.xml:0
#, python-format
msgid "Solve conflicts first"
msgstr "Soluciona primer els conflictes"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Start Date"
msgstr "Data inicial"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_start_before_end
msgid "Starting time should be before end time."
msgstr "El temps d'inici hauria de ser abans de l'hora de finalització."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__state
msgid "State"
msgstr "Estat/Província"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry_type_unique_work_entry_code
msgid "The same code cannot be associated to multiple work entry types."
msgstr ""
"El mateix codi no pot associar-se a diversos tipus d'entrades de treball."

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Time Off Options"
msgstr "Opcions de temps desactivat"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_stop
msgid "To"
msgstr "Fins a"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Type"
msgstr "Tipus"

#. module: hr_work_entry
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
#, python-format
msgid "Undefined"
msgstr "Indefinit"

#. module: hr_work_entry
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
#, python-format
msgid "Undefined Type"
msgstr "Tipus no definit"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__validated
msgid "Validated"
msgstr "Validat"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entries_no_validated_conflict
msgid "Validated work entries cannot overlap"
msgstr "Les entrades de treball validades no es poden superposar"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Detall del treball"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_employee_view_form
msgid "Work Entries"
msgstr "Entrades de Treball"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_user_work_entry_employee
msgid "Work Entries Employees"
msgstr "Entrades de treball Empleats"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action_conflict
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry"
msgstr "Entrada de la feina"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry Name"
msgstr "Nom de l'entrada de la feina"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_attendance__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_leaves__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.resource_calendar_leaves_view_search_inherit
msgid "Work Entry Type"
msgstr "Tipus d'entrada de la feina"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Work Entry Type Name"
msgstr "Nom del tipus d'entrada de la feina"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_type_action
msgid "Work Entry Types"
msgstr "Tipus d'entrada de la feina"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_has_end
msgid "Work entry must end. Please define an end date or a duration."
msgstr ""
"L'entrada de la feina ha d'acabar. Si us plau definiu una data de "
"finalització o una durada."

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_user_work_entry_employee_user_id_employee_id_unique
msgid "You cannot have the same employee twice."
msgstr "No es pot tenir el mateix empleat dues vegades."
