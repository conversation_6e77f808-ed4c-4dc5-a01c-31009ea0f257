<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_mrporder">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-call="web.internal_layout">
                <div class="page">
                    <div class="oe_structure"/>
                    <div class="row">
                        <div class="col-7">
                            <h2><span t-field="o.name"/></h2>
                        </div>
                        <div class="col-5">
                            <span class="text-right">
                                <div t-field="o.name" t-options="{'widget': 'barcode', 'width': 600, 'height': 100, 'img_style': 'width:350px;height:60px'}"/>
                            </span>
                        </div>
                    </div>
                    <div class="row mt32 mb32">
                        <div class="col-3" t-if="o.origin">
                            <strong>Source Document:</strong><br/>
                            <span t-field="o.origin"/>
                        </div>
                        <div class="col-3">
                            <strong>Responsible:</strong><br/>
                            <span t-field="o.user_id"/>
                        </div>
                    </div>

                    <div class="row mt32 mb32">
                        <div class="col-3">
                            <strong>Finished Product:</strong><br/>
                            <span t-field="o.product_id"/>
                        </div>
                        <div class="col-3" t-if="o.product_description_variants">
                            <strong>Description:</strong><br/>
                            <span t-field="o.product_description_variants"/>
                        </div>
                        <div class="col-3">
                            <strong>Quantity to Produce:</strong><br/>
                            <span t-field="o.product_qty"/>
                            <span t-field="o.product_uom_id.name" groups="uom.group_uom"/>
                        </div>
                    </div>

                    <div t-if="o.workorder_ids" groups="mrp.group_mrp_routings">
                        <h3>
                            <span t-if="o.state == 'done'">Operations Done</span>
                            <span t-else="">Operations Planned</span>
                        </h3>
                        <table class="table table-sm">
                            <tr>
                                <th><strong>Operation</strong></th>
                                <th><strong>WorkCenter</strong></th>
                                <th><strong>No. Of Minutes</strong></th>
                            </tr>
                            <tr t-foreach="o.workorder_ids" t-as="line2">
                                <td><span t-field="line2.name"/></td>
                                <td><span t-field="line2.workcenter_id.name"/></td>
                                <td>
                                    <span t-if="o.state != 'done'" t-field="line2.duration_expected"/>
                                    <span t-if="o.state == 'done'" t-field="line2.duration"/>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <h3 t-if="o.move_raw_ids">
                        <span t-if="o.state == 'done'">
                            Consumed Products
                        </span>
                        <span t-else="">
                            Products to Consume
                        </span>
                    </h3>

                    <table class="table table-sm" t-if="o.move_raw_ids">
                        <t t-set="has_product_barcode" t-value="any(m.product_id.barcode for m in o.move_raw_ids)"/>
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th t-attf-class="{{ 'text-right' if not has_product_barcode else '' }}">Quantity</th>
                                <th t-if="has_product_barcode" width="15%" class="text-center">Barcode</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-if="o.move_raw_ids">
                                <tr t-foreach="o.move_raw_ids.filtered(lambda m: m.state != 'cancel')" t-as="raw_line">
                                    <td>
                                        <span t-field="raw_line.product_id"/>
                                    </td>
                                    <td t-attf-class="{{ 'text-right' if not has_product_barcode else '' }}">
                                        <t t-if="o.state == 'done'">
                                            <span t-field="raw_line.quantity_done"/>
                                        </t>
                                        <t t-else="">
                                            <span t-field="raw_line.product_uom_qty"/>
                                        </t>
                                        <span t-field="raw_line.product_uom" groups="uom.group_uom"/>
                                    </td>
                                    <td t-if="has_product_barcode" width="15%" class="text-center">
                                        <t t-if="raw_line.product_id.barcode">
                                            <div t-field="raw_line.product_id.barcode" t-options="{'widget': 'barcode', 'width': 600, 'height': 100, 'img_style': 'width:100%;height:35px'}"/>
                                        </t>
                                    </td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                    <div class="oe_structure"/>
                </div>
            </t>
        </t>
    </t>
</template>

<template id="label_production_view_pdf">
    <t t-call="web.basic_layout">
        <div class="page">
            <t t-set="uom_categ_unit" t-value="env.ref('uom.product_uom_categ_unit')"/>
            <t t-foreach="docs" t-as="production">
                <t t-foreach="production.move_finished_ids" t-as="move">
                    <t t-if="production.state == 'done'">
                        <t t-set="move_lines" t-value="move.move_line_ids.filtered(lambda x: x.state == 'done' and x.qty_done)"/>
                    </t>
                    <t t-else="">
                        <t t-set="move_lines" t-value="move.move_line_ids.filtered(lambda x: x.state != 'done' and x.product_qty)"/>
                    </t>
                    <t t-foreach="move_lines" t-as="move_line">
                        <t t-if="move_line.product_uom_id.category_id == uom_categ_unit">
                            <t t-set="qty" t-value="int(move_line.qty_done)"/>
                        </t>
                        <t t-else="">
                            <t t-set="qty" t-value="1"/>
                        </t>
                        <t t-foreach="range(qty)" t-as="item">
                            <t t-translation="off">
                                <div style="display: inline-table; height: 10rem; width: 32%;">
                                    <table class="table table-bordered" style="border: 2px solid black;" t-if="production.move_finished_ids">
                                        <tr>
                                            <th class="table-active text-left" style="height:4rem;">
                                                <span t-esc="move.product_id.display_name"/>
                                                <br/>
                                                <span>Quantity:</span>
                                                <t t-if="move_line.product_uom_id.category_id == uom_categ_unit">
                                                    <span>1.0</span>
                                                    <span t-field="move_line.product_uom_id" groups="uom.group_uom"/>
                                                </t>
                                                <t t-else="">
                                                    <span t-esc="move_line.product_uom_qty" t-if="move_line.state !='done'"/>
                                                    <span t-esc="move_line.qty_done"  t-if="move_line.state =='done'"/>
                                                    <span t-field="move_line.product_uom_id" groups="uom.group_uom"/>
                                                </t>
                                            </th>
                                        </tr>
                                        <t t-if="move_line.product_id.tracking != 'none'">
                                            <tr>
                                                <td class="text-center align-middle">
                                                    <t t-if="move_line.lot_name or move_line.lot_id">
                                                        <div t-field="move_line.lot_name or move_line.lot_id.name" t-options="{'widget': 'barcode', 'width': 600, 'height': 150, 'img_style': 'width:100%;height:4rem'}"/>
                                                        <span t-esc="move_line.lot_name or move_line.lot_id.name"/>
                                                    </t>
                                                    <t t-else="">
                                                        <span class="text-muted">No barcode available</span>
                                                    </t>
                                                </td>
                                            </tr>
                                        </t>
                                        <t t-if="move_line.product_id.tracking == 'none'">
                                            <tr>
                                                <td class="text-center align-middle" style="height: 6rem;">
                                                    <t t-if="move_line.product_id.barcode">
                                                        <div t-field="move_line.product_id.barcode" t-options="{'widget': 'barcode', 'width': 600, 'height': 150, 'img_style': 'width:100%;height:4rem'}"/>
                                                        <span t-esc="move_line.product_id.barcode"/>
                                                    </t>
                                                    <t t-else="">
                                                        <span class="text-muted">No barcode available</span>
                                                    </t>
                                                </td>
                                            </tr>
                                        </t>
                                    </table>
                                </div>
                            </t>
                        </t>
                    </t>
                </t>
            </t>
        </div>
    </t>
</template>
</odoo>
