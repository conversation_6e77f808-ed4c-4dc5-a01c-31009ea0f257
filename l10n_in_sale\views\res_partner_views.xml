<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_in_view_partner_form" model="ir.ui.view">
        <field name="name">l10n.in.res.partner.vat.inherit</field>
        <field name="model">res.partner</field>
        <field name="priority" eval="100"/>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='vat']" position="after">
                <field name="l10n_in_shipping_gstin" groups="sale.group_delivery_invoice_address" attrs="{'invisible': ['|',('type', '!=', 'delivery'),('parent_id', '=', False)]}"/>
            </xpath>
            <xpath expr="//field[@name='child_ids']/form//field[@name='comment']" position="before">
                <field name="l10n_in_shipping_gstin" groups="sale.group_delivery_invoice_address" attrs="{'invisible': [('type', '!=', 'delivery')]}"/>
            </xpath>
        </field>
    </record>
</odoo>
