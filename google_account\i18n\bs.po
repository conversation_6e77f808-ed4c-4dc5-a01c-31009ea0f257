# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_account
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: google_account
#: model:ir.model,name:google_account.model_google_service
msgid "Google Service"
msgstr ""

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__id
msgid "ID"
msgstr "ID"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: google_account
#: code:addons/google_account/models/google_service.py:172
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:120
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:56
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:150
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired [%s]"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:193
#, python-format
msgid "Something went wrong with your request to google"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:131
#, python-format
msgid "The account for the Google service '%s' is not configured."
msgstr ""
