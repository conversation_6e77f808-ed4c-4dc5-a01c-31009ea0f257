<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_uy" model="res.partner">
        <field name="name">UY Company</field>
        <field name="vat">UY130551600010</field>
        <field name="street"></field>
        <field name="city">Montevideo</field>
        <field name="country_id" ref="base.uy"/>
        
        <field name="zip">12800</field>
        <field name="phone">+598 94 231 234</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.uyexample.com</field>
    </record>

    <record id="demo_company_uy" model="res.company">
        <field name="name">UY Company</field>
        <field name="partner_id" ref="partner_demo_company_uy"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_uy')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_uy.demo_company_uy'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_uy.uy_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_uy.demo_company_uy')"/>
    </function>
</odoo>
