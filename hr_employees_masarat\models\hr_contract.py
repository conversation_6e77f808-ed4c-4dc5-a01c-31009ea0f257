# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime
from odoo.exceptions import ValidationError
from datetime import date


class HrEmployeeContract(models.Model):

    _inherit = "hr.contract"

    #employee_age = fields.Integer(compute='_get_employee_age')

    #type_of_contract_x1 = fields.Selection([
    #     ('trial_contract', 'عقد فترة تجريبية'),
    #     ('annual_contract', 'عقد سنوي'),
    #     ('training', 'متدرب'),
    # ], 'نوع العقد', required=True)

    intren_work_contract_form = fields.Binary(string='نموذج عقد عمل تجريبي')
    annualy_contract_form = fields.Binary(string='عقد عمل سنوي')
    confidential_statement_form = fields.Binary(string='افادة بالسرية')
    disclaimer_form = fields.Binary(string='نموذج اخلاء طرف')
    outside_working_hours = fields.Binary(string='نموذج اذن عمل خارج اوقات الدوام')
    cooperating_contract_form = fields.Binary(string='نموذج عقد متعاون')
    # add by nabil in 24/08/2023
    training_contract_form = fields.Binary(string="عقد عمل فترة تدريبية")

    employee_fired_form = fields.Binary(string="نموذج فصل موظف")


    @api.depends('employee_id')
    def _get_employee_age(self):
         for elem in self:
             if elem.employee_id.birthday:
                 born = elem.employee_id.birthday
                 today = date.today()
                 elem.employee_age = (today.year - born.year - ((today.month, today.day) < (born.month, born.day)))
             else:
                 elem.employee_age = 0


    def filtered_assessment_view(self):
        #tree_id = self.env.ref("project_const.datasheet_tree")
        form_id = self.env.ref("hr_employees_masarat.hr_assessmint_form")
        try:
            count = self.env['hr.contract.assessment'].search([('contract_id','=',self.id)])[0].id
        except:
            count = None

        if count:
            return {
                'type': 'ir.actions.act_window',
                'name': ' تقييم أداء فترة تجريبية',
                'view_type': 'form',
                'view_mode': 'form',
                'res_id': count,
                'res_model': 'hr.contract.assessment',
                'domain': [('contract_id', '=', self.id)],
                'views': [(form_id.id, 'form')],
                'target': 'current',
            }
        else:
            return {
                'type': 'ir.actions.act_window',
                'name': ' تقييم أداء فترة تجريبية',
                'view_type': 'form',
                'view_mode': 'form',
                'res_model': 'hr.contract.assessment',
                'domain': [('contract_id', '=', self.id)],
                'views': [(form_id.id, 'form')],
                'target': 'current',
            }

    def filtered_assessment_annual_view(self):
        # tree_id = self.env.ref("project_const.datasheet_tree")
        form_id = self.env.ref("hr_employees_masarat.hr_assessment_annual_form")
        try:
            count = self.env['hr.contract.assessment.annual'].search([('contract_id', '=', self.id)])[0].id
        except:
            count = None

        if count:
            return {
                'type': 'ir.actions.act_window',
                'name': ' تقييم أداء سنوي',
                'view_type': 'form',
                'view_mode': 'form',
                'res_id': count,
                'res_model': 'hr.contract.assessment.annual',
                'domain': [('contract_id', '=', self.id)],
                'views': [(form_id.id, 'form')],
                'target': 'current',
            }
        else:
            return {
                'type': 'ir.actions.act_window',
                'name': ' تقييم أداء سنوي',
                'view_type': 'form',
                'view_mode': 'form',
                'res_model': 'hr.contract.assessment.annual',
                'domain': [('contract_id', '=', self.id)],
                'views': [(form_id.id, 'form')],
                'target': 'current',
            }


