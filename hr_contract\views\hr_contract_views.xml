<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="hr_hr_employee_view_form2" model="ir.ui.view">
            <field name="name">hr.hr.employee.view.form2</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <data>
                    <div name="button_box" position="inside">
                        <field name="contract_warning" invisible="1"/>
                        <button name="action_open_contract_history"
                            class="oe_stat_button"
                            icon="fa-book"
                            type="object"
                            groups="hr_contract.group_hr_contract_manager"
                            attrs="{'invisible' : [('employee_type', 'not in', ['employee', 'student', 'trainee'])]}">
                            <div attrs="{'invisible' : [('first_contract_date', '=', False)]}" class="o_stat_info">
                                <span class="o_stat_text text-success" attrs="{'invisible' : [('contract_warning', '=', True)]}" title="In Contract Since"> In Contract Since</span>
                                <span class="o_stat_value text-success" attrs="{'invisible' : [('contract_warning', '=', True)]}">
                                    <field name="first_contract_date" readonly="1"/>
                                </span>
                                <span class="o_stat_text text-danger" attrs="{'invisible' : [('contract_warning', '=', False)]}" title="In Contract Since">
                                    In Contract Since
                                </span>
                                <span class="o_stat_value text-danger" attrs="{'invisible' : [('contract_warning', '=', False)]}">
                                    <field name="first_contract_date" readonly="1"/>
                                </span>
                            </div>
                            <div attrs="{'invisible' : [('first_contract_date', '!=', False)]}" class="o_stat_info">
                                <span class="o_stat_value text-danger">
                                   <field name="contracts_count"/>
                                </span>
                                <span class="o_stat_text text-danger">
                                    Contracts
                                </span>
                            </div>
                        </button>
                    </div>
                    <xpath expr="//field[@name='user_id']" position="before">
                        <field name="first_contract_date"
                               attrs="{'invisible' : ['|', ('employee_type', 'not in', ['employee', 'student']), ('first_contract_date', '=', False)]}"
                               readonly="1"/>
                    </xpath>
                    <xpath expr="//field[@name='bank_account_id']" position="replace">
                        <field name="bank_account_id" context="{'display_partner':True}" attrs="{'invisible' : [('address_home_id', '=', False)]}"/>
                    </xpath>
                    <xpath expr="//field[@name='resource_calendar_id']" position="replace">
                        <field name="calendar_mismatch" invisible="1"/>
                        <label for="resource_calendar_id"/>
                        <div>
                            <field name="resource_calendar_id" required="1" nolabel="1"/>
                            <span attrs="{'invisible': [('calendar_mismatch', '=', False)]}"
                                class="fa fa-exclamation-triangle text-danger o_calendar_warning pl-3">
                            </span>
                            <span class="o_calendar_warning_tooltip text-danger">
                                Calendar Mismatch : The employee's calendar does not match its current contract calendar. This could lead to unexpected behaviors.
                            </span>
                        </div>
                    </xpath>
                    <field name="job_id" position="before">
                        <field name="contract_id" groups="hr_contract.group_hr_contract_manager"/>
                    </field>
                </data>
            </field>
        </record>

        <record id="hr_employee_view_search" model="ir.ui.view">
            <field name="name">hr.employee.view.search</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_filter"/>
            <field name="arch" type="xml">
                <data>
                    <xpath expr="//filter[@name='message_needaction']" position="after">
                        <separator/>
                        <filter string="Contract Warning" name="with_contract_warning" domain="[('contract_warning', '=', True)]"/>
                    </xpath>
                </data>
            </field>
        </record>

        <record id="hr_user_view_form" model="ir.ui.view">
        <field name="name">hr.user.preferences.view.form.contract.inherit</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="hr.res_users_view_form_profile"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='employee_bank_account_id']" position="replace">
                <field name="employee_bank_account_id" context="{'display_partner':True}" attrs="{'readonly': [('can_edit', '=', False)]}"/>
            </xpath>
        </field>
    </record>

        <record id="hr_contract_view_search" model="ir.ui.view">
            <field name="name">hr.contract.search</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <search string="Search Contract">
                    <field name="name" string="Contract"/>
                    <field name="date_start"/>
                    <field name="date_end"/>
                    <field name="employee_id"/>
                    <field name="job_id"/>
                    <field name="department_id" operator="child_of"/>
                    <field name="resource_calendar_id"/>
                    <filter string="Running" name="running" domain="[('state', '=', 'open')]"/>
                    <filter string="Not Running" name="not_running" domain="[('state', '!=', 'open')]"/>
                    <separator />
                    <filter string="Employed" name="current_employee" domain="[('employee_id.active', '=', True)]"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which have a next action date before today"/>
                    <filter string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_by_state" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Employee" name="group_by_employee" domain="[]" context="{'group_by': 'employee_id'}"/>
                        <filter string="Start Date" name="group_by_date_start" domain="[]" context="{'group_by': 'date_start'}"/>
                        <filter string="Job Position" name="group_by_job" domain="[]" context="{'group_by': 'job_id'}"/>
                        <filter string="Working Schedule" name="group_by_resource_calendar_id" domain="[]" context="{'group_by': 'resource_calendar_id'}"/>
                        <filter string="Salary Structure Type" name="group_by_structure_type_id" domain="[]" context="{'group_by': 'structure_type_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="hr_contract_view_form" model="ir.ui.view">
            <field name="name">hr.contract.form</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <form string="Current Contract">
                    <header>
                        <field name="state" widget="statusbar" options="{'clickable': '1'}"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box"/>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <div class="oe_title pr-0" name="title">
                            <h1 class="d-flex flex-row justify-content-between">
                                <field name="name" class="text-truncate" placeholder="Contract Reference"/>
                                <field name="kanban_state" widget="state_selection"/>
                            </h1>
                            <h2>
                                <field name="company_id" groups="base.group_multi_company" invisible="1"/>
                            </h2>
                        </div>
                        <group name="top_info">
                            <group name="top_info_left">
                                <field name="active" invisible="1"/>
                                <field name="employee_id"/>
                                <field name="date_start" string="Contract Start Date"/>
                                <field name="date_end" string="Contract End Date"/>
                                <field name="company_country_id" invisible="1"/>
                                <field name="country_code" invisible="1"/>
                                <field name="structure_type_id" domain="['|', ('country_id', '=', False), ('country_id', '=', company_country_id)]"/>
                                <field name="calendar_mismatch" invisible="1"/>
                                <label for="resource_calendar_id"/>
                                <div>
                                    <field name="resource_calendar_id" required="1" nolabel="1"/>
                                    <span attrs="{'invisible': ['|', ('calendar_mismatch', '=', False), ('state', '!=', 'open')]}"
                                        class="fa fa-exclamation-triangle text-danger o_calendar_warning pl-3">
                                    </span>
                                    <span class="o_calendar_warning_tooltip text-danger">
                                        Calendar Mismatch : The employee's calendar does not match this contract's calendar. This could lead to unexpected behaviors.
                                    </span>
                                </div>
                            </group>
                            <group name="top_info_right">
                                <field name="department_id"/>
                                <field name="job_id"/>
                                <field name="contract_type_id"/>
                                <field name="hr_responsible_id" required="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Contract Details" name="other">
                                <group><group name="contract_details"/></group>
                                <group name="notes_group" string="Notes">
                                    <field name="notes" nolabel="1"/>
                                </group>
                            </page>
                            <page string="Salary Information" name="information">
                                <group name="salary_info">
                                    <group name="salary">
                                        <label for="wage"/>
                                        <div class="o_row" name="wage">
                                            <field name="wage" nolabel="1"/>
                                            <span>/ month</span>
                                        </div>
                                    </group>
                                    <group name="yearly_advantages"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="hr_contract_view_tree" model="ir.ui.view">
            <field name="name">hr.contract.tree</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <tree string="Contracts" multi_edit="1" sample="1" default_order='date_start ASC'>
                    <field name="name" readonly="1"/>
                    <field name="employee_id" readonly="1" widget="many2one_avatar_employee"/>
                    <field name="job_id"/>
                    <field name="date_start" readonly="1"/>
                    <field name="date_end" readonly="1"/>
                    <field name="resource_calendar_id" optional="show"/>
                    <field name="structure_type_id" optional="show"/>
                    <field name="state" widget="badge" decoration-info="state == 'draft'" decoration-warning="state == 'close'" decoration-success="state == 'open'"/>
                    <field name="wage" invisible="1"/>
                    <field name="message_needaction" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company" readonly="1" optional="show"/>
                </tree>
            </field>
        </record>

        <record id="hr_contract_view_kanban" model="ir.ui.view">
            <field name="name">hr.contract.kanban</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_small_column" default_order="date_end" sample="1">
                    <field name="employee_id"/>
                    <field name="activity_state"/>
                    <field name="state"/>
                    <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                    <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_dropdown_kanban dropdown" t-if="!selection_mode" groups="base.group_user">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-toggle="dropdown" data-display="static" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <t t-if="widget.editable"><a role="menuitem" type="edit" class="dropdown-item">Edit Contract</a></t>
                                    <t t-if="widget.deletable"><a role="menuitem" type="delete" class="dropdown-item">Delete</a></t>
                                </div>
                            </div>
                            <div class="oe_kanban_content">
                                <div class="o_hr_contract_state">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                </div>
                                <div class="text-muted o_kanban_record_subtitle o_hr_contract_job_id" name="div_job_id">
                                    <field name="job_id"/>
                                </div>
                                <div class="oe_kanban_bottom_right">
                                    <span class="float-right">
                                        <field name="employee_id" widget="many2one_avatar_employee"/>
                                    </span>
                                    <span class="float-right mr4">
                                        <field name="kanban_state" widget="state_selection"/>
                                    </span>
                                </div>
                            </div>
                            <div class="oe_clear"></div>
                        </div>
                    </t>
                    </templates>
                </kanban>
            </field>
         </record>

        <record id="hr_contract_view_activity" model="ir.ui.view">
            <field name="name">hr.contract.activity</field>
            <field name="model">hr.contract</field>
            <field name="arch" type="xml">
                <activity string="Contracts">
                    <field name="employee_id"/>
                    <templates>
                        <div t-name="activity-box">
                            <img t-att-src="activity_image('hr.employee', 'avatar_128', record.employee_id.raw_value)" t-att-title="record.employee_id.value" t-att-alt="record.employee_id.value"/>
                            <div>
                                <field name="name" display="full"/>
                                <field name="job_id" muted="1" display="full"/>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <record id="action_hr_contract" model="ir.actions.act_window">
            <field name="name">Contracts</field>
            <field name="res_model">hr.contract</field>
            <field name="view_mode">kanban,tree,form,activity</field>
            <field name="domain">[('employee_id', '!=', False)]</field>
            <field name="context">{'search_default_group_by_state': 1}</field>
            <field name="search_view_id" ref="hr_contract_view_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new contract
              </p>
            </field>
        </record>

        <menuitem
            id="menu_human_resources_configuration_contract"
            name="Contracts"
            parent="hr.menu_human_resources_configuration"
            sequence="3"/>

</odoo>
