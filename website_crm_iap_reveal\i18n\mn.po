# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_lead_website
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <baskhu<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Batmu<PERSON><PERSON> Ganbat <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Batmunkh Ganbat <<EMAIL>>, 2021\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid ""
"1 credit is consumed per visitor matching the website traffic conditions and"
" whose company can be identified.<br/>"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Leads </span>"
msgstr "<span class=\"o_stat_text\"> Сэжим </span>"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Opportunities </span>"
msgstr "<span class=\"o_stat_text\"> Боломжууд </span>"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__active
msgid "Active"
msgstr "Идэвхитэй"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Archived"
msgstr "Архивласан"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_reveal_rule
msgid "CRM Lead Generation Rules"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_reveal_view
msgid "CRM Reveal View"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__lead_for
msgid "Choose whether to track companies only or companies and their contacts"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_for__companies
msgid "Companies"
msgstr "Компаниуд"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_for__people
msgid "Companies and their Contacts"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__company_size_min
msgid "Company Size"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__company_size_max
msgid "Company Size Max"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Contact Filter"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__country_ids
msgid "Countries"
msgstr "Улсууд"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__create_date
msgid "Create Date"
msgstr "Үүсгэсэн огноо"

#. module: crm_iap_lead_website
#: model_terms:ir.actions.act_window,help:crm_iap_lead_website.crm_reveal_rule_action
msgid "Create a conversion rule"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.actions.act_window,help:crm_iap_lead_website.crm_reveal_rule_action
msgid ""
"Create rules to generate B2B leads/opportunities from your website visitors."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_for
msgid "Data Tracking"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__display_name
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Enter Valid Regex."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__contact_filter_type
msgid "Filter On"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__filter_on_size
msgid "Filter companies based on their size."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__filter_on_size
msgid "Filter on Size"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "From"
msgstr "Хаанаас"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_ids
msgid "Generated Lead / Opportunity"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__2
msgid "High"
msgstr "Өндөр"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_iap_credits
msgid "IAP Credits"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__id
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__id
msgid "ID"
msgstr "ID"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_ip
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_ip
msgid "IP Address"
msgstr "IP Хаяг"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__industry_tag_ids
msgid "Industries"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule____last_update
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__write_date
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_type__lead
msgid "Lead"
msgstr "Сэжим"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Lead Data"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_lead_opportunity_form
msgid "Lead Generation Information"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_rule_id
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_rule_id
msgid "Lead Generation Rule"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.actions.act_window,name:crm_iap_lead_website.crm_reveal_view_action
#: model:ir.ui.menu,name:crm_iap_lead_website.crm_reveal_view_menu_action
msgid "Lead Generation Views"
msgstr ""

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid ""
"Lead Generation requires a GeoIP resolver which could not be found on your "
"system. Please consult https://pypi.org/project/GeoIP/."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.actions.server,name:crm_iap_lead_website.ir_cron_crm_reveal_lead_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_lead_website.ir_cron_crm_reveal_lead
#: model:ir.cron,name:crm_iap_lead_website.ir_cron_crm_reveal_lead
msgid "Lead Generation: Leads/Opportunities Generation"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Сэжим/Боломж"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__industry_tag_ids
msgid "Leave empty to always match. Odoo will not create lead if no match"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__0
msgid "Low"
msgstr "Нам"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid ""
"Make sure you know if you have to be GDPR compliant for storing personal "
"data."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.constraint,message:crm_iap_lead_website.constraint_crm_reveal_rule_limit_extra_contacts
msgid "Maximum 5 contacts are allowed!"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__1
msgid "Medium"
msgstr "Дундаж"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Missing Library"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_view__reveal_state__not_found
msgid "Not Found"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__extra_contacts
msgid "Number of Contacts"
msgstr "Холбогчийн тоо"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_count
msgid "Number of Generated Leads"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__opportunity_count
msgid "Number of Generated Opportunity"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__country_ids
msgid ""
"Only visitors of following countries will be converted into "
"leads/opportunities (using GeoIP)."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__state_ids
msgid ""
"Only visitors of following states will be converted into "
"leads/opportunities."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_type__opportunity
msgid "Opportunity"
msgstr "Боломж"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Opportunity Data"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Opportunity Generation Conditions"
msgstr ""

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Opportunity created by Odoo Lead Generation"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__other_role_ids
msgid "Other Roles"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__preferred_role_id
msgid "Preferred Role"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__priority
msgid "Priority"
msgstr "Урьтамж"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__regex_url
msgid ""
"Regex to track website pages. Leave empty to track the entire website, or / "
"to target the homepage. Example: /page* to track all the pages which begin "
"with /page"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__website_id
msgid "Restrict Lead generation to this website."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__contact_filter_type__role
msgid "Role"
msgstr "Дүр"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Rule"
msgstr "Дүрэм"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__name
msgid "Rule Name"
msgstr "Дүрмийн нэр"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__team_id
msgid "Sales Team"
msgstr "Борлуулалтын баг"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__user_id
msgid "Salesperson"
msgstr "Борлуулагч"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Search CRM Reveal Rule"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__seniority_id
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__contact_filter_type__seniority
msgid "Seniority"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__sequence
msgid "Sequence"
msgstr "Дугаарлалт"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_state
msgid "State"
msgstr "Төлөв"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__state_ids
msgid "States"
msgstr "Төлөв"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__suffix
msgid "Suffix"
msgstr "Дагавар"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__tag_ids
msgid "Tags"
msgstr "Пайз"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__extra_contacts
msgid ""
"This is the number of contacts to track if their role/seniority match your "
"criteria. Their details will show up in the history thread of generated "
"leads/opportunities. One credit is consumed per tracked contact."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__suffix
msgid ""
"This will be appended in name of generated lead so you can identify "
"lead/opportunity is generated with this rule"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_view__reveal_state__to_process
msgid "To Process"
msgstr "Боловсруулах"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_type
msgid "Type"
msgstr "Төрөл"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__regex_url
msgid "URL Expression"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Up to"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__sequence
msgid ""
"Used to order the rules with same URL and countries. Rules with a lower "
"sequence number will be processed first."
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__3
msgid "Very High"
msgstr "Маш Өндөр"

#. module: crm_iap_lead_website
#: model:ir.actions.act_window,name:crm_iap_lead_website.crm_reveal_rule_action
#: model:ir.ui.menu,name:crm_iap_lead_website.crm_reveal_rule_menu_action
msgid "Visits to Leads Rules"
msgstr ""

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__website_id
msgid "Website"
msgstr "Вэбсайт"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Website Traffic Conditions"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "additional credit(s) are consumed if the company matches this rule."
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "e.g. /page"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "e.g. US Visitors"
msgstr ""

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "employees"
msgstr "ажилчид"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "to"
msgstr "дуусах"
