<?xml version="1.0"?>
<odoo><data>
    <record id="mail_notification_view_tree" model="ir.ui.view">
        <field name="name">mail.notification.view.tree</field>
        <field name="model">mail.notification</field>
        <field name="arch" type="xml">
            <tree string="Notifications">
                <field name="mail_message_id"/>
                <field name="notification_type"/>
                <field name="res_partner_id"/>
                <field name="is_read"/>
                <field name="failure_type"/>
            </tree>
        </field>
    </record>

    <record id="mail_notification_view_form" model="ir.ui.view">
        <field name="name">mail.notification.view.form</field>
        <field name="model">mail.notification</field>
        <field name="arch" type="xml">
            <form string="Notification" duplicate="0">
                <header>
                    <field name="notification_status" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group string="Source">
                            <field name="mail_message_id"/>
                            <field name="notification_type"/>
                            <field name="mail_mail_id"/>
                            <field name="res_partner_id"/>
                        </group>
                        <group string="Status">
                            <field name="is_read"/>
                            <field name="read_date"/>
                            <field name="failure_type"/>
                            <field name="failure_reason"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="mail_notification_action" model="ir.actions.act_window">
        <field name="name">Notifications</field>
        <field name="res_model">mail.notification</field>
        <field name="view_mode">tree,form</field>
    </record>

</data></odoo>
