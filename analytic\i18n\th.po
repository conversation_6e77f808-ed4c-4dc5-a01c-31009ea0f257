# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* analytic
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr "<span class=\"o_stat_text\">อัตรากำไรขั้นต้น</span>"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__active
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_account_analytic_account_form
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Add a new analytic account"
msgstr "เพิ่มบัญชีวิเคราะห์รายการใหม่"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_tag_action
msgid "Add a new tag"
msgstr "เพิ่มแท็กใหม่"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr "จำนวน"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__account_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Analytic Account"
msgstr "บัญชีวิเคราะห์"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_distribution
msgid "Analytic Account Distribution"
msgstr "การกระจายบัญชีวิเคราะห์"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_group_action
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_tree_view
msgid "Analytic Account Groups"
msgstr "กลุ่มบัญชีวิเคราะห์"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_res_config_settings__group_analytic_accounting
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr "บัญชีวิเคราะห์"

#. module: analytic
#: model:res.groups,name:analytic.group_analytic_tags
msgid "Analytic Accounting Tags"
msgstr "แท็กบัญชีวิเคราะห์"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__analytic_distribution_ids
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr "บัญชีวิเคราะห์"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_group
msgid "Analytic Categories"
msgstr "หมวดหมู่วิเคราะห์"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active_analytic_distribution
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
msgid "Analytic Distribution"
msgstr "การกระจายการวิเคราะห์"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Entries"
msgstr "รายการวิเคราะห์"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Entry"
msgstr "รายการวิเคราะห์"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
msgid "Analytic Items"
msgstr "รายการวิเคราะห์"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr "ไลน์การวิเคราะห์"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__line_ids
msgid "Analytic Lines"
msgstr "ไลน์การวิเคราะห์"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__name
msgid "Analytic Tag"
msgstr "แท็กการวิเคราะห์"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_tag_action
#: model:ir.model,name:analytic.model_account_analytic_tag
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_tree_view
msgid "Analytic Tags"
msgstr "แท็กการวิเคราะห์"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr "พาร์ทเนอร์ที่เกี่ยวข้อง"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__balance
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Balance"
msgstr "ยอดคงเหลือ"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr "ยอดคงเหลือ:"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__category
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Category"
msgstr "หมวดหมู่"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr "ผังบัญชีวิเคราะห์"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__children_ids
msgid "Childrens"
msgstr "เด็ก"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "Click to add a new analytic account group."
msgstr "คลิกเพื่อเพิ่มกลุ่มบัญชีวิเคราะห์ใหม่"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__color
msgid "Color Index"
msgstr "ดัชนีสี"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__company_id
msgid "Company"
msgstr "บริษัท"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__complete_name
msgid "Complete Name"
msgstr "ชื่อเต็ม"

#. module: analytic
#: model:ir.model,name:analytic.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"การแปลงระหว่างหน่วยวัดจะเกิดขึ้นได้ก็ต่อเมื่ออยู่ในหมวดหมู่เดียวกัน "
"การแปลงจะทำตามอัตราส่วน"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""
"ต้นทุนจะถูกสร้างขึ้นโดยอัตโนมัติเมื่อคุณลงทะเบียนซัพพลายเออร์\n"
"                 ใบแจ้งหนี้ค่าใช้จ่ายหรือใบบันทึกเวลา"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__credit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Credit"
msgstr "เครดิต"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__currency_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__partner_id
msgid "Customer"
msgstr "ลูกค้า"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__date
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Date"
msgstr "วันที่"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__debit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Debit"
msgstr "เดบิต"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__description
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__name
msgid "Description"
msgstr "รายละเอียด"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Gross Margin"
msgstr "อัตรากำไรขั้นต้น"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__group_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__group_id
msgid "Group"
msgstr "กลุ่ม"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr "จัดกลุ่มตาม..."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__id
msgid "ID"
msgstr "ไอดี"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__active
msgid ""
"If the active field is set to False, it will allow you to hide the account "
"without removing it."
msgstr ""
"หากฟิลด์ที่ใช้งานอยู่ถูกตั้งค่าเป็น \"เท็จ\" "
"จะอนุญาตให้คุณซ่อนบัญชีได้โดยไม่ต้องลบออก"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""
"ใน Odoo มีการใช้คำสั่งขายและโปรเจกต์โดยใช้\n"
"                บัญชีวิเคราะห์ คุณสามารถติดตามต้นทุนและรายได้เพื่อวิเคราะห์\n"
"                อัตรากำไรขั้นต้นของคุณได้อย่างง่ายดาย"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__name
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Name"
msgstr "ชื่อ"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet"
msgstr "ยังไม่มีกิจกรรม"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account"
msgstr "ยังไม่มีกิจกรรมในบัญชีนี้"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_line__category__other
msgid "Other"
msgstr "อื่นๆ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_id
msgid "Parent"
msgstr "หลัก"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_path
msgid "Parent Path"
msgstr "เส้นทางหลัก"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__tag_id
msgid "Parent tag"
msgstr "แท็กหลัก"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__percentage
msgid "Percentage"
msgstr "เปอร์เซ็นต์"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "ปริมาณ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__code
msgid "Reference"
msgstr "อ้างอิง"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""
"รายได้จะถูกสร้างขึ้นโดยอัตโนมัติเมื่อคุณสร้างลูกค้า\n"
"              ใบแจ้งหนี้ สามารถสร้างใบแจ้งหนี้ของลูกค้าอิงตามคำสั่งขายได้\n"
"                (ใบแจ้งหนี้ราคาคงที่) บนใบบันทึกเวลา (อิงตามงานที่ทำ) หรือ\n"
"                เกี่ยวกับค่าใช้จ่าย (เช่น การออกใบแจ้งหนี้ค่าเดินทางใหม่)"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr "ค้นหาไลน์การวิเคราะห์"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
msgid "Search Analytic Tags"
msgstr "ค้นหาแท็กการวิเคราะห์"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_tag__active
msgid "Set active to false to hide the Analytic Tag without removing it."
msgstr "ตั้งค่าเปิดใช้งานเป็นเท็จเพื่อซ่อนแท็กการวิเคราะห์โดยไม่ต้องลบออก"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__tag_ids
msgid "Tags"
msgstr "แท็ก"

#. module: analytic
#: model:ir.model.constraint,message:analytic.constraint_account_analytic_distribution_check_percentage
msgid ""
"The percentage of an analytic distribution should be between 0 and 100."
msgstr "เปอร์เซ็นต์ของการกระจายการวิเคราะห์ควรอยู่ระหว่าง 0 ถึง 100"

#. module: analytic
#: code:addons/analytic/models/analytic_account.py:0
#, python-format
msgid ""
"The selected account belongs to another company than the one you're trying "
"to create an analytic item for"
msgstr ""
"บัญชีที่เลือกเป็นของ บริษัท "
"อื่นที่ไม่ใช่บัญชีที่คุณกำลังพยายามสร้างรายการวิเคราะห์สำหรับ"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "This allows you to classify your analytic accounts."
msgstr "วิธีนี้ช่วยให้คุณจัดประเภทบัญชีวิเคราะห์ของคุณได้"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "รวม"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_id
msgid "Unit of Measure"
msgstr "หน่วยวัด"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_category_id
msgid "UoM Category"
msgstr "หมวดหมู่ UoM"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__user_id
msgid "User"
msgstr "ผู้ใช้"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr "เช่น โปรเจกต์ XYZ"
