# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# Sarah <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-27 13:05+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "# 등록"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr "'. 다음에 대한 결과 표시 중 '"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "(Ref:"
msgstr "(참조:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "(only"
msgstr "(한정"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid ", .oe_country_events, .s_speaker_bio"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "<b>Drag and Drop</b> this snippet below the event title."
msgstr "행사 제목 아래에 이 스니펫을 <b>드래그 앤 드롭</b> 합니다."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr "<b>종료</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr "<b>시작</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<b>View all</b>"
msgstr "<b>모두 보기</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid ""
"<em>Write here a quote from one of your attendees. It gives confidence in "
"your events.</em>"
msgstr "<em>참석자 중 한 사람의 인용문을 여기에 쓰십시오. 그것은 당신의 행사에 대한 자신감을 줍니다.</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid ""
"<font style=\"font-size: 62px;\" "
"class=\"o_default_snippet_text\">Introduction</font>"
msgstr "<font style=\"font-size: 62px;\" class=\"o_default_snippet_text\">소개</font>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<i class=\"fa fa-ban mr-2\"/>Sold Out"
msgstr "<i class=\"fa fa-ban mr-2\"/>매진"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban mr-2\"/>게시 안함"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check mr-2\"/>Registered"
msgstr "<i class=\"fa fa-check mr-2\"/>등록된"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"
msgstr ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<i class=\"fa fa-flag mr-2\"/>Events:"
msgstr "<i class=\"fa fa-flag mr-2\"/>행사 :"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> 구글 캘린더에 추가하기"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> iCal/Outlook에 추가하기"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
#: model_terms:ir.ui.view,arch_db:website_event.s_country_events
msgid "<i class=\"fa fa-globe mr-2\"/>Upcoming Events"
msgstr "<i class=\"fa fa-globe mr-2\"/>다가오는 행사"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid ""
"<i class=\"fa fa-long-arrow-left text-primary mr-2\"/>\n"
"                            <span>All Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"<span class=\"badge badge-secondary text-uppercase "
"o_wevent_badge\">Speaker</span>"
msgstr ""
"<span class=\"badge badge-secondary text-uppercase "
"o_wevent_badge\">연사</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "<span class=\"navbar-brand h4 my-0 mr-auto\">Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_days pr-1\">0</span><span "
"class=\"o_countdown_metric pr-1\">days</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"py-2 o_wevent_registration_title text-left\">Tickets</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"text-dark font-weight-bold align-middle px-2\">Qty</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                    <i class=\"fa fa-ban mr-2\"/>Sold Out\n"
"                                </span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>온라인 행사</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span>Tickets</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"
msgstr ""
"<span>티켓</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr "<strong> 이용 가능한 좌석보다 더 많은 티켓을 주문했습니다</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr "지난 행사"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "회사 소개"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Add to Calendar"
msgstr "일정표에 추가하기"

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "모든 국가"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr "모든 국가"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr "웹사이트에 행사별 메뉴를 표시하고 관리할 수 있습니다."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Apply"
msgstr "적용"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Ask questions to attendees when registering online"
msgstr "온라인 등록시 참석자에게 질문"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"13 살 때 John DOE는 이미 고객을 위한 첫 번째 비즈니스 응용 프로그램을 개발하기 시작했습니다. 토목 공학을 습득 한 후 "
"TinyERP를 설립했습니다. 이것은 OpenERP의 첫 번째 단계였으며 이후에는 전 세계에서 가장 많이 설치된 오픈 소스 비즈니스 "
"소프트웨어인 Odoo가 되었습니다."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "참석자"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "작성자"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "게시 가능"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#, python-format
msgid "Cancel"
msgstr "취소"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "닫기"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
#, python-format
msgid "Community"
msgstr "커뮤니티"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr "커뮤니티 메뉴"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "회사"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Continue"
msgstr "계속"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "표지 속성"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Create"
msgstr "작성"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Create \"%s\""
msgstr "\"%s\" 생성"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "작성자"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "작성일자"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Custom Range"
msgstr "사용자 지정 범위"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr "날짜 &amp; 시간"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "날짜 (최근 날짜 순)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "날짜 (오래된 날짜 순)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "설명"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Discard"
msgstr "작성 취소"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "표시명"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr "웹 사이트에 전용 메뉴 표시"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr "웹사이트에 커뮤니티 탭 표시"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Don't forget to click <b>save</b> when you're done."
msgstr "완료 후에 <b>저장</b> 버튼을 꼭 눌러주세요."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Email"
msgstr "이메일"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr "종료 -"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "행사"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr "행사 커뮤니티 메뉴"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr "일정 날짜"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "행사 위치"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr "행사 메뉴"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Event Name"
msgstr "행사명"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "행사 등록"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr "행사 등록"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/customize_options.xml:0
#, python-format
msgid "Event Specific"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/customize_options.xml:0
#, python-format
msgid "Event Sub-menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Subtitle"
msgstr "행사 부제"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr "행사 태그 카테고리"

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr "행사 서식"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Title"
msgstr "행사 제목"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "행사를 찾을 수 없습니다!"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "행사 게시"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "행사 게시안함"

#. module: website_event
#: code:addons/website_event/models/website.py:0
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#, python-format
msgid "Events"
msgstr "행사"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Expired"
msgstr "만료됨"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_register_cta
#: model:ir.model.fields,field_description:website_event.field_event_type__menu_register_cta
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Extra Register Button"
msgstr "행사 등록 버튼"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event, and join the "
"conversation."
msgstr "이 행사에 대해 사람들이 보고 말하는 내용을 찾아 대화에 참여하십시오."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "Follow Us"
msgstr "팔로우하세요"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr "모든 행사에는 다음 콘텐츠가 표시됩니다."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Get the direction"
msgstr "방향을 잡으세요"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr "구글"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "ID"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
#, python-format
msgid "Introduction"
msgstr "소개"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr "소개 메뉴"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr "소개 메뉴"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr "완료됨"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "진행 중"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr "참여 여부"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "게시 여부"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr "John DOE"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: website_event
#. openerp-web
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#, python-format
msgid "Location"
msgstr "위치"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr "위치 메뉴"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr "위치 메뉴"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Looking great! Let's now <b>publish</b> this page so that it becomes "
"<b>visible</b> on your website!"
msgstr "멋있네요! 이제 이 페이지를<b>게시</b> 하여 웹사이트에 <b>보일 수 있도록</b> 해보세요!"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_search
msgid "Main Contact"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_visitor__parent_id
msgid "Main identity"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "메뉴"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "메뉴 유형"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "메뉴"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "N/A"
msgstr "해당 없음"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Name"
msgstr "이름"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "New Event"
msgstr "새 행사"

#. module: website_event
#: code:addons/website_event/models/website.py:0
#, python-format
msgid "Next Events"
msgstr "다음 행사"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr "아직 웹사이트 메뉴 항목이 없습니다!"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events found."
msgstr "행사가 없습니다."

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr "해당 방문자와 연결되어 있는 등록 내역이 없습니다."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr "' 에 대한 결과를 찾을 수 없습니다."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "On Site"
msgstr "현장"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Online"
msgstr "온라인"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "주최자"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr "교육훈련"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__parent_id
msgid "Parent"
msgstr "상위"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Past Events"
msgstr "지난 행사"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Phone <small>(Optional)</small>"
msgstr "전화번호 <small>(선택사항)</small>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Photos"
msgstr "사진"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "이 필드를 작성해 주십시오"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.js:0
#, python-format
msgid "Please select at least one ticket."
msgstr "적어도 한 개 이상의 티켓을 선택하십시오."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Questions"
msgstr "질문"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Ref:"
msgstr "참조;"

#. module: website_event
#. openerp-web
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
#: model_terms:ir.ui.view,arch_db:website_event.layout
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Register"
msgstr "등록하기"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Register Button"
msgstr "등록 버튼"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr "등록 메뉴"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr "등록 메뉴"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr "행사 등록됨"

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Registration"
msgstr "등록하기"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr "등록 확인됨!"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr "등록"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations Closed"
msgstr "등록 마감"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations are <b>closed</b>"
msgstr "등록이 <b>마감</b>되었습니다"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations not yet open"
msgstr "아직 등록이 시작되지 않았습니다"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr "시작 전 남은 시간"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr "행사 시작까지 남은 시간 (분)"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
msgid "Restrict publishing to this website."
msgstr "이 웹 사이트로의 게시를 제한합니다."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "행사 목록으로 돌아가기"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO 최적화"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "SHARE"
msgstr "공유"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales end on"
msgstr "판매 종료일"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales start on"
msgstr "판매 시작일"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr "행사 검색 중..."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all events from"
msgstr "모든 일정 보기"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Select Venue"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "Seo 이름"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr "웹사이트에 표시"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "매진"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "죄송합니다, 이용할 수 없는 행사입니다."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr "시작 -"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr "오늘 시작"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Start → End"
msgstr "시작 → 종료"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr "시작 <span/>"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr "웹사이트를 통해 문서에 접근 하는 전체 URL입니다."

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "This month"
msgstr "이번 달"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This shortcut will bring you right back to the event form."
msgstr "바로가기를 사용하면 행사 서식으로 바로 돌아갈 수 있습니다."

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr "기술 메뉴에는 모든 행사의 하위 메뉴 항목이 표시됩니다."

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "This ticket is not available for sale for this event"
msgstr "이 티켓은 해당 행사에서 판매할 수 없습니다."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr "티켓 #"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Sales starting on"
msgstr "티켓 판매 시작일"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets for this Event are <b>Sold Out</b>"
msgstr "해당 행사의 티켓은 <b>매진</b>되었습니다."

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Today"
msgstr "오늘"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Toggle navigation"
msgstr "탐색 전환"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "Unpublished"
msgstr "게시 안 함"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#, python-format
msgid "Upcoming Events"
msgstr "예정된 행사"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Use the top button '<b>+ New</b>' to create an event."
msgstr "행사를 생성하는데 '<b>+신규</b>' 상위 버튼 사용"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr "<b>바로가기</b> 를 사용하여 행사 웹페이지에 쉽게 엑세스할 수 있습니다."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragrah to write a short text about your events or company."
msgstr "이 단락을 사용하여 행사 또는 회사에 대한 짧은 문구를 작성합니다."

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr "URL 기반 메뉴가 아닌 경우에 사용"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Venue"
msgstr "행사장"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "화면"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "현재 웹 사이트에 공개"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "방문자"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Want to change your event configuration? Let's go back to the event form."
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
msgid "Website"
msgstr "웹사이트"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr "웹사이트 행사 메뉴"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr "웹사이트 행사 메뉴"

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "웹사이트 홈"

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "웹사이트 메뉴"

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr "웹사이트 메뉴"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr "웹사이트 하위 메뉴"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "웹사이트 방문자"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "웹사이트 메타 설명"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "웹사이트 메타 핵심어"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "웹사이트 메타 제목"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "웹사이트 오픈그래프 이미지"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "행사 시작 여부"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_done
msgid "Whether event is finished"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr "아직 진행 전인 경우 행사가 오늘 시작될지 여부"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"With the Edit button, you can <b>customize</b> the web page visitors will "
"see when registering."
msgstr "편집 버튼을 활용하면 방문자가 행사에 등록할 때 표시되는 페이지를 <b>사용자 지정</b>하여 만들 수 있습니다."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "available)"
msgstr "사용 가능)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr "iCal/Outlook"
