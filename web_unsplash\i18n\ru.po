# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_unsplash
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2021
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Сергей <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Серге<PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: web_unsplash
#: model_terms:ir.ui.view,arch_db:web_unsplash.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Generate an Access Key"
msgstr "<i class=\"fa fa-arrow-right\"/>Создать ключ доступа</i>"

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__unsplash_access_key
msgid "Access Key"
msgstr "Ключ доступа"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Application ID"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Apply"
msgstr "Применить"

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash URL!"
msgstr "ОШИБКА: Неизвестное Unsplash URL!"

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash notify URL!"
msgstr "ОШИБКА: Неизвестное сообщение Unsplash URL!"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Get an Access key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your access key here"
msgstr "Вставьте ваш ключ доступа здесь"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your application ID here"
msgstr "Вставьте ваш ID приложения здесь"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Photos (via Unsplash)"
msgstr "Фото (из Unsplash)"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your Unsplash access key and application ID."
msgstr "Проверьте ключ доступа и ID программы Unsplash."

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your internet connection or contact administrator."
msgstr "Проверьте подключение к интернету или обратитесь к администратору."

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Поле с изображением"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Search is temporarily unavailable"
msgstr "Поиск временно недоступен"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Setup Unsplash to access royalty free photos."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Something went wrong"
msgstr "Что-то пошло не так"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid ""
"The max number of searches is exceeded. Please retry in an hour or extend to"
" a better account."
msgstr ""
"Превышен максимальное количество поисков. Повторите попытку через час или "
"расширьте свой аккаунт."

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Unauthorized Key"
msgstr "Незарегистрированный ключ"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/js/unsplash_image_widget.js:0
#, python-format
msgid "Uploading %s '%s' images."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/js/unsplash_image_widget.js:0
#, python-format
msgid "Uploading '%s' image."
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_users
msgid "Users"
msgstr "Пользователи"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "and paste"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "and paste it here:"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "here:"
msgstr ""
