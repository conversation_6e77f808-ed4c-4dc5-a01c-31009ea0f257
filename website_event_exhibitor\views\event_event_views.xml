<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="event_event_view_form" model="ir.ui.view">
        <field name="name">event.event.view.form.inherit.exhibitor</field>
        <field name="model">event.event</field>
        <field name="priority" eval="5"/>
        <field name="inherit_id" ref="website_event.event_event_view_form"/>
        <field name="arch" type="xml">
            <field name="is_published" position="before">
                <button name="%(event_sponsor_action_from_event)d"
                        type="action"
                        class="oe_stat_button"
                        icon="fa-black-tie">
                    <field name="sponsor_count" string="Sponsors" widget="statinfo"/>
                </button>
            </field>
            <xpath expr="//label[@for='community_menu']" position="before">
                <label for="exhibitor_menu"/>
                <field name="exhibitor_menu"/>
            </xpath>
        </field>
    </record>

    <record id="event_event_view_list" model="ir.ui.view">
        <field name="name">event.event.view.list.inherit.exhibitor</field>
        <field name="model">event.event</field>
        <field name="inherit_id" ref="event.view_event_tree"/>
        <field name="arch" type="xml">
            <field name="stage_id" position="after">
                <field name="sponsor_count" readonly="1" optional="hide"/>
            </field>
        </field>
    </record>

</odoo>
