"id","name","model_id:id","group_id:id","perm_read","perm_write","perm_create","perm_unlink"
access_hr_employee_alw_user,hr.employee.alw.user,libya_hr_payroll.model_hr_other_alw,hr.group_hr_user,1,1,0,0

access_hr_employee_provide,hr.employee.provide,model_hr_employee_provide,hr.group_hr_user,1,1,1,1

access_hr_employee_alw_manager,hr.employee.alw.manager,libya_hr_payroll.model_hr_other_alw,hr.group_hr_manager,1,1,1,1

access_hr_employee_ded_user,hr.employee.ded.user,libya_hr_payroll.model_hr_ded,hr.group_hr_user,1,1,0,0
access_hr_employee_ded_manager,hr.employee.ded.manager,libya_hr_payroll.model_hr_ded,hr.group_hr_manager,1,1,1,1

access_hr_employee_alw_line_base,hr.employee.alw.line.base,libya_hr_payroll.model_hr_other_alw_line,base.group_user,1,1,1,1
access_hr_employee_alw_line_user,hr.employee.alw.line.user,libya_hr_payroll.model_hr_other_alw_line,hr.group_hr_user,1,1,1,1
access_hr_employee_alw_line_manager,hr.employee.alw.line.manager,libya_hr_payroll.model_hr_other_alw_line,hr.group_hr_manager,1,1,1,1

access_hr_employee_ded_line_base,hr.employee.ded.line.base,libya_hr_payroll.model_hr_ded_line,base.group_user,1,1,1,1
access_hr_employee_ded_line_user,hr.employee.ded.line.user,libya_hr_payroll.model_hr_ded_line,hr.group_hr_user,1,1,1,1
access_hr_employee_ded_line_manager,hr.employee.ded.line.manager,libya_hr_payroll.model_hr_ded_line,hr.group_hr_manager,1,1,1,1

access_hr_payroll_insurance,hr.employee.payroll.insurance,libya_hr_payroll.model_payroll_insurance,base.group_user,1,1,1,1
access_hr_payroll_deduction,hr.employee.payroll.deduction,libya_hr_payroll.model_payroll_deduction,base.group_user,1,1,1,1
access_hr_payroll_position,hr.employee.payroll.position,libya_hr_payroll.model_payroll_position,base.group_user,1,1,1,1
access_hr_payroll_allowances,hr.employee.payroll.allowances,libya_hr_payroll.model_payroll_allowances,base.group_user,1,1,1,1
access_hr_payroll_expenses,hr.employee.payroll.expenses,libya_hr_payroll.model_payroll_expenses,base.group_user,1,1,1,1
access_hr_payroll_income_tax,hr.employee.payroll.income.tax,libya_hr_payroll.model_income_tax,base.group_user,1,1,1,1
access_hr_payroll_cheque,hr.employee.payroll.cheque,libya_hr_payroll.model_payroll_cheque,base.group_user,1,1,1,1




