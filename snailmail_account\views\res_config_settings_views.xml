<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.snailmail.account</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="100"/>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='send_default']" position="inside">
                <div class="row" attrs="{'invisible': [('module_snailmail_account', '=', False)]}">
                    <field name="invoice_is_snailmail" class="col-lg-1 ml16"/>
                    <label for="invoice_is_snailmail"/>
                    <span class="fa fa-lg fa-building-o" title="Values set here are company-specific."/>
                </div>
            </xpath>

            <div id="snailmail_settings" position="inside">
                <div class="mt16" attrs="{'invisible': [('module_snailmail_account', '=', False)]}">
                    <div class="content-group">
                        <div class="row">
                            <field name="snailmail_color" class="col-lg-1 ml16"/>
                            <label for="snailmail_color"/>
                            <span class="fa fa-lg fa-building-o" title="Values set here are company-specific."/>
                        </div>
                        <div class="row">
                            <field name="snailmail_duplex" class="col-lg-1 ml16"/>
                            <label for="snailmail_duplex"/>
                            <span class="fa fa-lg fa-building-o" title="Values set here are company-specific."/>
                        </div>
                        <div class="row">
                            <field name="snailmail_cover" class="col-lg-1 ml16"/>
                            <label for="snailmail_cover"/>
                            <span class="fa fa-lg fa-building-o" title="Values set here are company-specific."/>
                        </div>
                    </div>
                </div>
                <widget name="iap_buy_more_credits" service_name="snailmail"/>
            </div>
        </field>
    </record>
</odoo>
