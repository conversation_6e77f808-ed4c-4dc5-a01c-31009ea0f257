<?xml version="1.0"?>
<odoo>
    <data>

        <record model="ir.ui.view" id="view_message_subtype_tree">
            <field name="name">mail.message.subtype.tree</field>
            <field name="model">mail.message.subtype</field>
            <field name="priority">10</field>
            <field name="arch" type="xml">
                <tree string="Subtype">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="res_model"/>
                    <field name="default"/>
                </tree>
            </field>
        </record>

        <record model="ir.ui.view" id="view_mail_message_subtype_form">
            <field name="name">mail.message.subtype.form</field>
            <field name="model">mail.message.subtype</field>
            <field name="arch" type="xml">
                <form string="Email message">
                    <sheet>
                        <group>
                            <group string='Description'>
                                <field name="name"/>
                                <field name="sequence"/>
                                <field name="res_model"/>
                                <field name="description"/>
                                <field name="default"/>
                                <field name="internal"/>
                                <field name="hidden"/>
                            </group>
                            <group string='Auto subscription'>
                                <field name="parent_id"/>
                                <field name="relation_field"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_view_message_subtype">
            <field name="name">Subtypes</field>
            <field name="res_model">mail.message.subtype</field>
            <field name="view_mode">tree,form</field>
        </record>

    </data>
</odoo>
