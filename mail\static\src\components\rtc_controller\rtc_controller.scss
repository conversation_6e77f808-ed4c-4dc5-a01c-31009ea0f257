// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_RtcController {
    display: flex;
    justify-content: space-between;
}

.o_RtcController_button {
    margin: map-get($spacers, 1);
    padding: map-get($spacers, 4);
    display: flex;

    &.o-isSmall {
        padding: map-get($spacers, 3);
    }
}

.o_RtcController_buttonIconWrapper {
    display: flex;
    width: map-get($spacers, 4);
    height: map-get($spacers, 4);
    justify-content: center;
    flex-direction: column;
    align-items: center;

    &.o-isSmall {
        width: map-get($spacers, 3);
        height: map-get($spacers, 3);
    }
}

.o_RtcController_buttons {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_RtcController_button {
    color: white;
    background: none;
    border: none;
    outline: none;
    border-radius: 50%;
    background-color: gray('800');

    &:hover {
        background-color: gray('700');
    }

    &.o-isActive {
        opacity: 1;
    }

    @include hover-focus () {
        outline: none;
    }
}

.o_RtcController_callToggle {
    background-color: theme-color('success');

    &:hover {
        background-color: darken(theme-color('success'), 20%);
    }

    &.o-isActive {
        background-color: theme-color('danger');

        &:hover {
            background-color: darken(theme-color('danger'), 20%);
        }
    }
}

.o_RtcController_videoButton.o-isActive {
    color: theme-color('success');
}
