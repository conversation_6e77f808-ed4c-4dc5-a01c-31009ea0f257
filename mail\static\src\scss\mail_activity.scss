/* Common */
.o_mail_activity {
    &.o_field_widget {
        display: block;
    }

    .o_thread_date_separator.o_border_dashed {
        border-bottom-style: dashed;

        &[data-toggle="collapse"] {
            cursor: pointer;

            .o_chatter_planned_activities_summary {
                display: none;
            }

            &.collapsed {
                margin-bottom: 0;
                transition: margin 0.8s ease 0s;

                .o_chatter_planned_activities_summary {
                    display: inline-block;

                    span {
                        padding: 0 5px;
                        border-radius: 100%;
                        font-size: 11px;
                    }
                }

                i.fa-caret-down:before {
                    content: '\f0da';
                }
            }
        }
    }

    #o_chatter_planned_activities {
        .o_thread_message {
            .o_thread_message_sidebar {
                .o_avatar_stack {
                    position: relative;
                    text-align: left;
                    margin-bottom: 8px;

                    img {
                        width: 31px;
                        height: 31px;
                        object-fit: cover;
                    }

                    .o_avatar_icon {
                        @include o-position-absolute($right: -5px, $bottom: -5px);
                        width: 25px;
                        height: 25px;
                        object-fit: cover;
                        padding: 6px 5px;
                        text-align: center;
                        line-height: 1.2;
                        color: white;
                        border-radius: 100%;
                        border: 2px solid white;
                    }
                }
            }

            .o_mail_info {
                .o_activity_summary {
                    @include o-text-overflow;
                    max-width: 290px;
                    vertical-align: middle;
                }
                .o_activity_info {
                    vertical-align: baseline;
                    padding: 4px 6px;
                    background: theme-color('light');
                    border-radius: 2px 2px 0 0;
                    @include o-hover-opacity(1, 1);

                    &.collapsed {
                        @include o-hover-opacity(0.5, 1);
                        background: transparent;
                    }
                }
            }

            .o_thread_message_collapse .dl-horizontal.card {
                display: inline-block;
                margin-bottom: 0;

                dt {
                    max-width: 80px;
                }
                dd {
                    margin-left: 95px;
                }
            }

            .o_thread_message_note {
                margin: 2px 0 5px;
                padding: 0px;
            }
            .o_thread_message_warning {
                margin: 2px 0 5px;
            }

            .o_activity_template_preview,.o_activity_template_send {
                font-weight: bold;
                color: $o-brand-primary;
                cursor: pointer;
                &:hover:not(.active) {
                    color: darken($o-brand-primary, 15%);
                    border-color: darken($o-brand-primary, 15%);
                }
            }

            .o_thread_message_tools {
                .o_activity_link {
                    padding: 0 $input-btn-padding-x;
                }
                .o_activity_done {
                    padding-left: 0;
                }
            }
        }
    }

    .o_activity_color_default {
        color: #dddddd;
    }

    .o_activity_color_planned {
        @extend .text-success;
    }
    .o_activity_color_overdue {
        @extend .text-danger;
    }
    .o_activity_color_today {
        @extend .text-warning;
    }
}

/* Feedback popover (form view) */
.o_mail_activity_feedback {
    max-width: 410px;
    outline: none;

    textarea {
        min-width: 250px;
    }
}

/* list_activity widget */
.o_list_view {
    .o_list_table tbody > tr {
        > td.o_data_cell.o_list_activity_cell {
            overflow: visible !important; // allow the activity dropdown to overflow
            .o_mail_activity {
                display: flex;
                max-width: 275px;
                .o_activity_btn {
                    margin-right: 3px;
                }
                .o_activity_summary {
                    @include o-text-overflow;
                }
            }
        }
    }
}

/* Kanban View */
.o_kanban_record{
    .o_kanban_inline_block {
        display: inline-block;
    }
}

.o_kanban_record, .o_view_controller{
    .o_mail_activity {
        .o_activity_btn {
            span.fa {
                overflow: visible;
                line-height: 1;
                vertical-align: middle;
            }
        }

        div.o_activity {
            min-width: 290px;
            padding: 0px 0px;

            .o_activity_log_container {
                max-height: 300px;
                overflow-y: auto;
            }

            ul.o_activity_log {
                color: $body-color;

                li {
                    .o_activity_title_entry {
                        display: flex;
                        align-items: baseline;
                        max-width: 275px;
                        .o_activity_summary {
                            @include o-text-overflow;
                        }
                    }

                    .o_edit_button {
                        opacity: 0.5;
                    }

                    &:hover .o_edit_button{
                        opacity: 1;
                    }

                    .o_activity_link_kanban {
                        font-size: 1.5em;
                        @include o-hover-text-color($text-muted, theme-color('success'));
                        @include o-hover-opacity(0.5, 1);
                    }

                    &.o_activity_selected {
                        border-bottom: 0;
                    }

                    &.o_activity_form > div {
                        padding-top: 0.5em;
                        padding-bottom: 0.7em;
                    }
                }
            }

            .o_no_activity {
                padding: 10px;
                cursor: initial;
            }
        }
    }
}
