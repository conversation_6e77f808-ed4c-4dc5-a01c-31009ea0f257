# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_survey
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr_recruitment_survey
#: model:survey.survey,description:hr_recruitment_survey.survey_recruitment_form
msgid ""
"<p>\n"
"    Please answer those questions to help recruitment officers to preprocess your application.\n"
"</p>"
msgstr ""
"<p>\n"
"Пожалуйста, ответьте на эти вопросы, чтобы помочь специалистам по подбору персонала подготовить вашу заявку.\n"
"</p>"

#. module: hr_recruitment_survey
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1
msgid ""
"<p>Please fill information about you: who you are, what are your education, experience, and activities.\n"
"    It will help us managing your application.</p>"
msgstr ""
"<p>Пожалуйста, заполните информацию о себе: кто вы, какое у вас образование, опыт и деятельность.\n"
" Эта информация поможет при рассмотрении вашей заявки.</p>"

#. module: hr_recruitment_survey
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid ""
"<p>Please summarize your education history: schools, location, diplomas, "
"...</p>"
msgstr ""
"<p>Пожалуйста, кратко опишите ваше образование: среднее/высшее, название "
"учреждения, специальность, квалификация."

#. module: hr_recruitment_survey
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid ""
"<p>Please tell us a bit more about yourself: what are your main activities, "
"...</p>"
msgstr ""
"<p>Пожалуйста, расскажите еще немного о себе: каковы ваши основные занятия, "
"хобби...</p>"

#. module: hr_recruitment_survey
#: model:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid ""
"<p>What are your main knowledge regarding the job you are applying to ?</p>"
msgstr ""
"<p>Каковы ваши основные знания, касающиеся работы, на которую вы "
"претендуете?</p>"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid ""
"<span class=\"o_stat_text\">Consult</span>\n"
"                        <span class=\"o_stat_text\">Interview</span>"
msgstr ""
"<span class=\"o_stat_text\">Консультация</span>\n"
"<span class=\"o_stat_text\">Интервью</span>"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1
msgid "About you"
msgstr "О себе"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid "Activities"
msgstr "Действия"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_invite__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_user_input__applicant_id
msgid "Applicant"
msgstr "Соискатель"

#. module: hr_recruitment_survey
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_applicant__survey_id
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_job__survey_id
msgid ""
"Choose an interview form for this job position and you will be able to "
"print/answer this interview from all applicants who apply for this job"
msgstr ""
"Выберите форму собеседования на эту должность, и вы сможете "
"распечатать/провести это собеседование со всеми соискателями, претендующими "
"на эту работу"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Create Interview Form"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_job_survey_inherit
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Display Interview Form"
msgstr "Открыть схему собеседования"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q4
msgid "Education"
msgstr "Образование"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q2
msgid "From which university did or will you graduate ?"
msgstr "Какой университет вы окончили или собираетесь окончить?"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row2
msgid "Getting on with colleagues"
msgstr "Ладить с коллегами"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row8
msgid "Getting perks such as free parking, gym passes"
msgstr ""
"Получение таких бонусов, как бесплатная парковка, абонементы в спортзал"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row1
msgid "Having a good pay"
msgstr "Иметь хорошую оплату труда"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row3
msgid "Having a nice office environment"
msgstr "Иметь хорошую офисную обстановку"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row7
msgid "Having freebies such as tea, coffee and stationery"
msgstr ""
"Наличие бесплатных бенефитов, таких как чай, кофе и канцелярские "
"принадлежности"

#. module: hr_recruitment_survey
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "If other, please specify:"
msgstr "Если другое, укажите, пожалуйста:"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col2
msgid "Important"
msgstr "Важное"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_job__survey_id
msgid "Interview Form"
msgstr "Форма интервью"

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
#, python-format
msgid "Interview Form : %s"
msgstr "Форма интервью: %s"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.res_config_settings_view_form
msgid "Interview Survey"
msgstr "Опрос"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_job
msgid "Job Position"
msgstr "Должность"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "Knowledge"
msgstr "Знания"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row6
msgid "Management quality"
msgstr "Качество управления"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col1
msgid "Not important"
msgstr "Неважно"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row5
msgid "Office location"
msgstr "Расположение офиса"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid "Past work experiences"
msgstr "Прошлый опыт работы"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Preview Interview"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.survey,title:hr_recruitment_survey.survey_recruitment_form
msgid "Recruitment Form"
msgstr "Рекрутинговая форма"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__response_id
msgid "Response"
msgstr "Ответ"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "SEND INTERVIEW"
msgstr "ОТПРАВИТЬ ИНТЕРВЬЮ"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "See interview report"
msgstr "Посмотреть отчет о собеседовании"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__response_state
msgid "Status"
msgstr "Статус"

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__survey_id
#, python-format
msgid "Survey"
msgstr "Опрос"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Приглашение к участию в опросе"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Опрос пользователей ввод"

#. module: hr_recruitment_survey
#: model_terms:survey.survey,description_done:hr_recruitment_survey.survey_recruitment_form
msgid "Thank you for answering this survey. We will come back to you soon."
msgstr ""
"Спасибо, что приняли участие в этом опросе. Мы вернемся с обратной связью в "
"ближайшее время."

#. module: hr_recruitment_survey
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "The answer you entered is not valid."
msgstr "Ответ, который вы ввели, неверный."

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/survey_invite.py:0
#, python-format
msgid "The applicant \"%s\" has finished the survey."
msgstr "Соискатель \"%s\" завершил прохождение опроса."

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/survey_invite.py:0
#, python-format
msgid "The survey %(survey_link)s has been sent to %(partner_link)s"
msgstr "Опрос %(survey_link)sбыл отправлен %(partner_link)s"

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/survey_invite.py:0
#, python-format
msgid "The survey has been sent to \"%s\"."
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "This question requires an answer."
msgstr "Этот вопрос требует ответа."

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col3
msgid "Very important"
msgstr "Очень важно"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q3
msgid "Were you referred by an employee?"
msgstr "Вас направил сотрудник?"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "What is important for you ?"
msgstr "Что для вас важно?"

#. module: hr_recruitment_survey
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q1
msgid "Which country are you from ?"
msgstr "Из какой вы страны?"

#. module: hr_recruitment_survey
#: model:survey.question.answer,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row4
msgid "Working with state of the art technology"
msgstr "Работа с современными технологиями"

#. module: hr_recruitment_survey
#: code:addons/hr_recruitment_survey/models/hr_applicant.py:0
#, python-format
msgid "You must define a Contact Name for this applicant."
msgstr "Вы должны определить контактное имя для этого заявителя."
