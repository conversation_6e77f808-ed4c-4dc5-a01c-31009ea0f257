<?xml version="1.0" ?>
<odoo>
   <data noupdate="1">
        <record id="expire_coupon_cron" model="ir.cron">
            <field name="name">Coupon: expire coupon based on date</field>
            <field name="model_id" ref="coupon.model_coupon_coupon"/>
            <field name="state">code</field>
            <field name="code">model.cron_expire_coupon()</field>
            <field name="active" eval="True"/>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
        </record>
   </data>
</odoo>
