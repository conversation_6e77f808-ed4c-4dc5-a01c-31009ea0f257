# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_vn
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-30 03:34+0000\n"
"PO-Revision-Date: 2021-09-30 03:34+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart335
#: model:account.account.template,name:l10n_vn.chart335
msgid "Accrued expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart141
#: model:account.account.template,name:l10n_vn.chart141
msgid "Advances"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2291
#: model:account.account.template,name:l10n_vn.chart2291
msgid "Allowances for decline in value of trading securities"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2293
#: model:account.account.template,name:l10n_vn.chart2293
msgid "Allowances for doubtful debts"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2292
#: model:account.account.template,name:l10n_vn.chart2292
msgid "Allowances for impairment of investments in other entities"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2294
#: model:account.account.template,name:l10n_vn.chart2294
msgid "Allowances for inventories"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2143
#: model:account.account.template,name:l10n_vn.chart2143
msgid "Amortization of intangible assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart34312
#: model:account.account.template,name:l10n_vn.chart34312
msgid "Bond discounts"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart34313
#: model:account.account.template,name:l10n_vn.chart34313
msgid "Bond premiums"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1212
#: model:account.account,name:l10n_vn.1_chart1282
#: model:account.account.template,name:l10n_vn.chart1212
#: model:account.account.template,name:l10n_vn.chart1282
msgid "Bonds"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3531
#: model:account.account.template,name:l10n_vn.chart3531
msgid "Bonus fund"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3411
#: model:account.account.template,name:l10n_vn.chart3411
msgid "Borrowing loans liabilities"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2111
#: model:account.account.template,name:l10n_vn.chart2111
msgid "Buildings and structures"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart441
#: model:account.account.template,name:l10n_vn.chart441
msgid "Capital expenditure funds"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart4112
#: model:account.account.template,name:l10n_vn.chart4112
msgid "Capital surplus"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2135
#: model:account.account.template,name:l10n_vn.chart2135
msgid "Computer software"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1541
#: model:account.account.template,name:l10n_vn.chart1541
msgid "Construction contracts"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3522
#: model:account.account.template,name:l10n_vn.chart3522
msgid "Construction warranty provisions"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2412
#: model:account.account.template,name:l10n_vn.chart2412
msgid "Construction works"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart4113
#: model:account.account.template,name:l10n_vn.chart4113
msgid "Conversion options on convertible bonds"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3432
#: model:account.account.template,name:l10n_vn.chart3432
msgid "Convertible bonds"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2132
#: model:account.account.template,name:l10n_vn.chart2132
msgid "Copyrights"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3334
#: model:account.account.template,name:l10n_vn.chart3334
msgid "Corporate income tax"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart632
#: model:account.account.template,name:l10n_vn.chart632
msgid "Costs of goods sold"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart8211
#: model:account.account.template,name:l10n_vn.chart8211
msgid "Current tax expense"
msgstr ""

#. module: l10n_vn
#: model:account.tax,description:l10n_vn.1_tax_purchase_vat0
#: model:account.tax,name:l10n_vn.1_tax_purchase_vat0
#: model:account.tax.template,description:l10n_vn.tax_purchase_vat0
#: model:account.tax.template,name:l10n_vn.tax_purchase_vat0
msgid "Deductible VAT 0%"
msgstr ""

#. module: l10n_vn
#: model:account.tax,description:l10n_vn.1_tax_purchase_vat10
#: model:account.tax,name:l10n_vn.1_tax_purchase_vat10
#: model:account.tax.template,description:l10n_vn.tax_purchase_vat10
#: model:account.tax.template,name:l10n_vn.tax_purchase_vat10
msgid "Deductible VAT 10%"
msgstr ""

#. module: l10n_vn
#: model:account.tax,description:l10n_vn.1_tax_purchase_vat5
#: model:account.tax,name:l10n_vn.1_tax_purchase_vat5
#: model:account.tax.template,description:l10n_vn.tax_purchase_vat5
#: model:account.tax.template,name:l10n_vn.tax_purchase_vat5
msgid "Deductible VAT 5%"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart243
#: model:account.account.template,name:l10n_vn.chart243
msgid "Deferred tax assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart8212
#: model:account.account.template,name:l10n_vn.chart8212
msgid "Deferred tax expense"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart347
#: model:account.account.template,name:l10n_vn.chart347
msgid "Deferred tax liabilities"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart344
#: model:account.account.template,name:l10n_vn.chart344
msgid "Deposits received"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6234
#: model:account.account.template,name:l10n_vn.chart6234
msgid "Depreciation expense"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2142
#: model:account.account.template,name:l10n_vn.chart2142
msgid "Depreciation of finance lease assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2147
#: model:account.account.template,name:l10n_vn.chart2147
msgid "Depreciation of investment properties"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2141
#: model:account.account.template,name:l10n_vn.chart2141
msgid "Depreciation of tangible fixed assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart412
#: model:account.account.template,name:l10n_vn.chart412
msgid "Differences upon asset revaluation"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart622
#: model:account.account.template,name:l10n_vn.chart622
msgid "Direct labour costs"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart621
#: model:account.account.template,name:l10n_vn.chart621
msgid "Direct raw material costs"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart417
#: model:account.account.template,name:l10n_vn.chart417
msgid "Enterprise reorganization assistance fund"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3523
#: model:account.account.template,name:l10n_vn.chart3523
msgid "Enterprise restructuring provisions"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart33381
#: model:account.account.template,name:l10n_vn.chart33381
msgid "Environment protection tax"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1534
#: model:account.account.template,name:l10n_vn.chart1534
msgid "Equipment and spare parts for replacement"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2281
#: model:account.account.template,name:l10n_vn.chart2281
msgid "Equity investments in other entities Other investment"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart4132
#: model:account.account.template,name:l10n_vn.chart4132
msgid "Exchange rate differences in pre-operating period"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart4131
#: model:account.account.template,name:l10n_vn.chart4131
msgid ""
"Exchange rate differences on revaluation of monetary items denominated in "
"foreign currency "
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1611
#: model:account.account.template,name:l10n_vn.chart1611
msgid "Expenditure brought forward"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1612
#: model:account.account.template,name:l10n_vn.chart1612
msgid "Expenditure of current year"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6271
#: model:account.account.template,name:l10n_vn.chart6271
msgid "Factory staff costs"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3339
#: model:account.account.template,name:l10n_vn.chart3339
msgid "Fees, charges and other payables"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2122
#: model:account.account.template,name:l10n_vn.chart2122
msgid "Finance lease intangible fixed assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3412
#: model:account.account.template,name:l10n_vn.chart3412
msgid "Finance lease liabilities"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2121
#: model:account.account.template,name:l10n_vn.chart2121
msgid "Finance lease tangible fixed assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart635
#: model:account.account.template,name:l10n_vn.chart635
msgid "Financial expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart515
#: model:account.account.template,name:l10n_vn.chart515
msgid "Financial income"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1551
#: model:account.account.template,name:l10n_vn.chart1551
msgid "Finished products - inventory"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1557
#: model:account.account.template,name:l10n_vn.chart1557
msgid "Finished products - real estates"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6414
#: model:account.account.template,name:l10n_vn.chart6414
msgid "Fixed asset deprecation"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6274
#: model:account.account,name:l10n_vn.1_chart6424
#: model:account.account.template,name:l10n_vn.chart6274
#: model:account.account.template,name:l10n_vn.chart6424
msgid "Fixed asset depreciation"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2411
#: model:account.account.template,name:l10n_vn.chart2411
msgid "Fixed assets prior to commissioning"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1122
#: model:account.account.template,name:l10n_vn.chart1122
msgid "Foreign currencies"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart158
#: model:account.account.template,name:l10n_vn.chart158
msgid "Goods in bonded warehouse"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart151
#: model:account.account.template,name:l10n_vn.chart151
msgid "Goods in transit"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart171
#: model:account.account.template,name:l10n_vn.chart171
msgid "Government bonds purchased for resale"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3384
#: model:account.account.template,name:l10n_vn.chart3384
msgid "Health insurance"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3333
#: model:account.account.template,name:l10n_vn.chart3333
msgid "Import and export tax"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1562
#: model:account.account.template,name:l10n_vn.chart1562
msgid "Incidental purchase costs"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart911
#: model:account.account.template,name:l10n_vn.chart911
msgid "Income Summary"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1533
#: model:account.account.template,name:l10n_vn.chart1533
msgid "Instruments for renting"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3363
#: model:account.account.template,name:l10n_vn.chart3363
msgid "Intra-company payables for borrowing costs eligible to be capitalized"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3362
#: model:account.account.template,name:l10n_vn.chart3362
msgid "Intra-company payables for foreign exchange differences"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3361
#: model:account.account.template,name:l10n_vn.chart3361
msgid "Intra-company payables for operating capital received"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1363
#: model:account.account.template,name:l10n_vn.chart1363
msgid ""
"Intra-company receivables on borrowing costs eligible to be capitalized"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1362
#: model:account.account.template,name:l10n_vn.chart1362
msgid "Intra-company receivables on foreign exchange"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart414
#: model:account.account.template,name:l10n_vn.chart414
msgid "Investment and development fund"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart222
#: model:account.account.template,name:l10n_vn.chart222
msgid "Investment in joint ventures and associates"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart221
#: model:account.account.template,name:l10n_vn.chart221
msgid "Investment in subsidiaries"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart217
#: model:account.account.template,name:l10n_vn.chart217
msgid "Investment properties"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6231
#: model:account.account.template,name:l10n_vn.chart6231
msgid "Labour costs"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3337
#: model:account.account.template,name:l10n_vn.chart3337
msgid "Land and housing tax, and rental charges"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2131
#: model:account.account.template,name:l10n_vn.chart2131
msgid "Land use rights"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1283
#: model:account.account.template,name:l10n_vn.chart1283
msgid "Lending loans"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2136
#: model:account.account.template,name:l10n_vn.chart2136
msgid "Licenses and franchises"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_vn_template_liquidity_transfer
#: model:account.account.template,name:l10n_vn.vn_template_liquidity_transfer
msgid "Liquidity Transfer"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2112
#: model:account.account.template,name:l10n_vn.chart2112
msgid "Machinery and equipment"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2413
#: model:account.account.template,name:l10n_vn.chart2413
msgid "Major repairs of fixed assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3534
#: model:account.account.template,name:l10n_vn.chart3534
msgid "Management bonus fund"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6232
#: model:account.account,name:l10n_vn.1_chart6272
#: model:account.account.template,name:l10n_vn.chart6232
#: model:account.account.template,name:l10n_vn.chart6272
msgid "Material costs"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6412
#: model:account.account.template,name:l10n_vn.chart6412
msgid "Materials and packing materials"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2113
#: model:account.account.template,name:l10n_vn.chart2113
msgid "Means of transportation and transmission"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1123
#: model:account.account.template,name:l10n_vn.chart1123
msgid "Monetary Gold"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart244
#: model:account.account.template,name:l10n_vn.chart244
msgid "Mortgage, collaterals and deposits"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart4611
#: model:account.account.template,name:l10n_vn.chart4611
msgid "Non-business funds bought forward"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart4612
#: model:account.account.template,name:l10n_vn.chart4612
msgid "Non-business funds for current year"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart466
#: model:account.account.template,name:l10n_vn.chart466
msgid "Non-business funds used for fixed asset acquisitions"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2114
#: model:account.account.template,name:l10n_vn.chart2114
msgid "Office equipment and furniture"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6423
#: model:account.account.template,name:l10n_vn.chart6423
msgid "Office equipment expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6422
#: model:account.account.template,name:l10n_vn.chart6422
msgid "Office supply expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3431
#: model:account.account.template,name:l10n_vn.chart3431
msgid "Ordinary bonds"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart41111
#: model:account.account.template,name:l10n_vn.chart41111
msgid "Ordinary shares with voting rights"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart811
#: model:account.account.template,name:l10n_vn.chart811
msgid "Other Expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart711
#: model:account.account.template,name:l10n_vn.chart711
msgid "Other Income"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart4118
#: model:account.account.template,name:l10n_vn.chart4118
msgid "Other capital"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart418
#: model:account.account.template,name:l10n_vn.chart418
msgid "Other equity funds"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6238
#: model:account.account,name:l10n_vn.1_chart6278
#: model:account.account,name:l10n_vn.1_chart6418
#: model:account.account,name:l10n_vn.1_chart6428
#: model:account.account.template,name:l10n_vn.chart6238
#: model:account.account.template,name:l10n_vn.chart6278
#: model:account.account.template,name:l10n_vn.chart6418
#: model:account.account.template,name:l10n_vn.chart6428
msgid "Other expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2118
#: model:account.account.template,name:l10n_vn.chart2118
msgid "Other fixed assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1288
#: model:account.account.template,name:l10n_vn.chart1288
msgid "Other held to maturity investments"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2138
#: model:account.account.template,name:l10n_vn.chart2138
msgid "Other intangible fixed assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3368
#: model:account.account.template,name:l10n_vn.chart3368
msgid "Other inter-company payables"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1368
#: model:account.account.template,name:l10n_vn.chart1368
msgid "Other intra-company receivables"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1542
#: model:account.account.template,name:l10n_vn.chart1542
msgid "Other products"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3524
#: model:account.account.template,name:l10n_vn.chart3524
msgid "Other provisions"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart5118
#: model:account.account.template,name:l10n_vn.chart5118
msgid "Other revenue"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1218
#: model:account.account.template,name:l10n_vn.chart1218
msgid "Other securities and financial instruments"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart33382
#: model:account.account.template,name:l10n_vn.chart33382
msgid "Other taxes"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3388
#: model:account.account.template,name:l10n_vn.chart3388
msgid "Others"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1388
#: model:account.account.template,name:l10n_vn.chart1388
msgid "Others receivables"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart33311
#: model:account.account.template,name:l10n_vn.chart33311
msgid "Output VAT"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6277
#: model:account.account,name:l10n_vn.1_chart6417
#: model:account.account,name:l10n_vn.1_chart6427
#: model:account.account.template,name:l10n_vn.chart6277
#: model:account.account.template,name:l10n_vn.chart6417
#: model:account.account.template,name:l10n_vn.chart6427
msgid "Outside services"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6237
#: model:account.account.template,name:l10n_vn.chart6237
msgid "Outside services "
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart157
#: model:account.account.template,name:l10n_vn.chart157
msgid "Outward goods on consignment"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart34311
#: model:account.account.template,name:l10n_vn.chart34311
msgid "Par value of bonds"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2133
#: model:account.account.template,name:l10n_vn.chart2133
msgid "Patents and inventions"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3385
#: model:account.account.template,name:l10n_vn.chart3385
msgid "Payables on equitization"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3348
#: model:account.account.template,name:l10n_vn.chart3348
msgid "Payables to others"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3341
#: model:account.account.template,name:l10n_vn.chart3341
msgid "Payables to staff"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2115
#: model:account.account.template,name:l10n_vn.chart2115
msgid "Perennial plants, working animals and farm livestocks"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3335
#: model:account.account.template,name:l10n_vn.chart3335
msgid "Personal income tax"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart41112
#: model:account.account.template,name:l10n_vn.chart41112
msgid "Preference shares"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart242
#: model:account.account.template,name:l10n_vn.chart242
msgid "Prepaid expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart357
#: model:account.account.template,name:l10n_vn.chart357
msgid "Price stabilization fund"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart2134
#: model:account.account.template,name:l10n_vn.chart2134
msgid "Product labels and trademarks"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3521
#: model:account.account.template,name:l10n_vn.chart3521
msgid "Product warranty provisions"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart631
#: model:account.account.template,name:l10n_vn.chart631
msgid "Production costs"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart337
#: model:account.account.template,name:l10n_vn.chart337
msgid "Progress billings for construction contracts"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1567
#: model:account.account.template,name:l10n_vn.chart1567
msgid "Properties held for sale"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6426
#: model:account.account.template,name:l10n_vn.chart6426
msgid "Provision expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1561
#: model:account.account.template,name:l10n_vn.chart1561
msgid "Purchase costs"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_01_vn
msgid "Purchase of Goods and Services"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6111
#: model:account.account.template,name:l10n_vn.chart6111
msgid "Purchases of raw materials"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart152
#: model:account.account.template,name:l10n_vn.chart152
msgid "Raw materials"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1385
#: model:account.account.template,name:l10n_vn.chart1385
msgid "Receivables from privatization"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1532
#: model:account.account.template,name:l10n_vn.chart1532
msgid "Reusable packaging materials"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart5114
#: model:account.account.template,name:l10n_vn.chart5114
msgid "Revenue from government grants"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart5117
#: model:account.account.template,name:l10n_vn.chart5117
msgid "Revenue from investment properties"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart5112
#: model:account.account.template,name:l10n_vn.chart5112
msgid "Revenue from sales of finished goods"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart5111
#: model:account.account.template,name:l10n_vn.chart5111
msgid "Revenue from sales of merchandises"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart5113
#: model:account.account.template,name:l10n_vn.chart5113
msgid "Revenue from services rendered"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_02_vn
msgid "Sales of Goods and Services"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart5213
#: model:account.account.template,name:l10n_vn.chart5213
msgid "Sales rebates"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart5212
#: model:account.account.template,name:l10n_vn.chart5212
msgid "Sales returns"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3561
#: model:account.account.template,name:l10n_vn.chart3561
msgid "Science and technology development fund"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3562
#: model:account.account.template,name:l10n_vn.chart3562
msgid ""
"Science and technology development fund used for fixed asset acquisition"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1543
#: model:account.account.template,name:l10n_vn.chart1543
msgid "Services"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1211
#: model:account.account.template,name:l10n_vn.chart1211
msgid "Shares"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1381
#: model:account.account.template,name:l10n_vn.chart1381
msgid "Shortage of assets awaiting resolution"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3383
#: model:account.account.template,name:l10n_vn.chart3383
msgid "Social insurance"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3332
#: model:account.account.template,name:l10n_vn.chart3332
msgid "Special consumption tax"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6411
#: model:account.account,name:l10n_vn.1_chart6421
#: model:account.account.template,name:l10n_vn.chart6411
#: model:account.account.template,name:l10n_vn.chart6421
msgid "Staff expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3381
#: model:account.account.template,name:l10n_vn.chart3381
msgid "Surplus of assets awaiting resolution"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3336
#: model:account.account.template,name:l10n_vn.chart3336
msgid "Tax on use of natural resources "
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6425
#: model:account.account.template,name:l10n_vn.chart6425
msgid "Taxes, fees and charges"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1281
#: model:account.account.template,name:l10n_vn.chart1281
msgid "Term deposits"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6233
#: model:account.account,name:l10n_vn.1_chart6273
#: model:account.account,name:l10n_vn.1_chart6413
#: model:account.account.template,name:l10n_vn.chart6233
#: model:account.account.template,name:l10n_vn.chart6273
#: model:account.account.template,name:l10n_vn.chart6413
msgid "Tools and instruments"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1531
#: model:account.account.template,name:l10n_vn.chart1531
msgid "Tools and supplies"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart5211
#: model:account.account.template,name:l10n_vn.chart5211
msgid "Trade discounts"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart331
#: model:account.account.template,name:l10n_vn.chart331
msgid "Trade payables"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart131
#: model:account.account.template,name:l10n_vn.chart131
msgid "Trade receivables"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart132
#: model:account.account.template,name:l10n_vn.chart132
msgid "Trade receivables(pos)"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3382
#: model:account.account.template,name:l10n_vn.chart3382
msgid "Trade union fees"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart419
#: model:account.account.template,name:l10n_vn.chart419
msgid "Treasury shares"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart4211
#: model:account.account.template,name:l10n_vn.chart4211
msgid "Undistributed profit after tax brought forward"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart4212
#: model:account.account.template,name:l10n_vn.chart4212
msgid "Undistributed profit(loss) after tax for the current year"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3387
#: model:account.account.template,name:l10n_vn.chart3387
msgid "Unearned revenue"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3386
#: model:account.account.template,name:l10n_vn.chart3386
msgid "Unemployment insurance"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_02_01_vn
msgid "Untaxed Purchase of Goods and Services"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_01_02_01_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_01_02_01_vn
msgid "Untaxed Purchase of Goods and Services taxed 0%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_03_02_01_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_03_02_01_vn
msgid "Untaxed Purchase of Goods and Services taxed 10%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_02_02_01_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_02_02_01_vn
msgid "Untaxed Purchase of Goods and Services taxed 5%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_02_02_vn
msgid "Untaxed Sales of Goods and Services"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_01_02_02_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_01_02_02_vn
msgid "Untaxed sales of goods and services taxed 0%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_03_02_02_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_03_02_02_vn
msgid "Untaxed sales of goods and services taxed 10%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_02_02_02_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_02_02_02_vn
msgid "Untaxed sales of goods and services taxed 5%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.group,name:l10n_vn.tax_group_0
msgid "VAT 0%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.group,name:l10n_vn.tax_group_10
msgid "VAT 10%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.group,name:l10n_vn.tax_group_5
msgid "VAT 5%"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart33312
#: model:account.account.template,name:l10n_vn.chart33312
msgid "VAT on imported goods"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1332
#: model:account.account.template,name:l10n_vn.chart1332
msgid "VAT on purchase of fixed assets"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1331
#: model:account.account.template,name:l10n_vn.chart1331
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_01_01_vn
msgid "VAT on purchase of goods and services"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_01_01_01_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_01_01_01_vn
msgid "VAT on purchase of goods and services 0%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_03_01_01_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_03_01_01_vn
msgid "VAT on purchase of goods and services 10%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_02_01_01_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_02_01_01_vn
msgid "VAT on purchase of goods and services 5%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_01_02_vn
msgid "VAT on sales of goods and services"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_01_01_02_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_01_01_02_vn
msgid "VAT on sales of goods and services 0%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_03_01_02_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_03_01_02_vn
msgid "VAT on sales of goods and services 10%"
msgstr ""

#. module: l10n_vn
#: model:account.tax.report.line,name:l10n_vn.account_tax_report_line_02_01_02_vn
#: model:account.tax.report.line,tag_name:l10n_vn.account_tax_report_line_02_01_02_vn
msgid "VAT on sales of goods and services 5%"
msgstr ""

#. module: l10n_vn
#: model:account.chart.template,name:l10n_vn.vn_template
msgid "VN - Chart of Accounts"
msgstr ""

#. module: l10n_vn
#: model:account.tax,description:l10n_vn.1_tax_sale_vat0
#: model:account.tax,name:l10n_vn.1_tax_sale_vat0
#: model:account.tax.template,description:l10n_vn.tax_sale_vat0
#: model:account.tax.template,name:l10n_vn.tax_sale_vat0
msgid "Value Added Tax (VAT) 0%"
msgstr ""

#. module: l10n_vn
#: model:account.tax,description:l10n_vn.1_tax_sale_vat10
#: model:account.tax,name:l10n_vn.1_tax_sale_vat10
#: model:account.tax.template,description:l10n_vn.tax_sale_vat10
#: model:account.tax.template,name:l10n_vn.tax_sale_vat10
msgid "Value Added Tax (VAT) 10%"
msgstr ""

#. module: l10n_vn
#: model:account.tax,description:l10n_vn.1_tax_sale_vat5
#: model:account.tax,name:l10n_vn.1_tax_sale_vat5
#: model:account.tax.template,description:l10n_vn.tax_sale_vat5
#: model:account.tax.template,name:l10n_vn.tax_sale_vat5
msgid "Value Added Tax (VAT) 5%"
msgstr ""

#. module: l10n_vn
#: model:ir.ui.menu,name:l10n_vn.account_reports_vn_statements_menu
msgid "Vietnam"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1121
#: model:account.account.template,name:l10n_vn.chart1121
msgid "Vietnamese Dong"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1544
#: model:account.account.template,name:l10n_vn.chart1544
msgid "Warranty costs"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart6415
#: model:account.account.template,name:l10n_vn.chart6415
msgid "Warranty expenses"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3532
#: model:account.account.template,name:l10n_vn.chart3532
msgid "Welfare fund"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart3533
#: model:account.account.template,name:l10n_vn.chart3533
msgid "Welfare fund used for fixed asset acquisitions"
msgstr ""

#. module: l10n_vn
#: model:account.account,name:l10n_vn.1_chart1361
#: model:account.account.template,name:l10n_vn.chart1361
msgid "Working capital provided to sub-units"
msgstr ""
