# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_livechat
# 
# Translators:
# <PERSON><PERSON><PERSON> <stre<PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>ra<PERSON><PERSON> Grzelak <<EMAIL>>, 2021
# <PERSON><PERSON> <judyta.kaz<PERSON><PERSON><PERSON>@openglobe.pl>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>z, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Piotr <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <tadeus<PERSON><PERSON><PERSON><EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Tadeusz Karpiński <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__session_count
msgid "# Sessions"
msgstr "# Sesje"

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "%s has left the conversation."
msgstr "%s opuścił konwersację."

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid ""
"%s has started a conversation with %s. \n"
"                        The chat request has been canceled."
msgstr ""
"%s rozpoczął rozmowę z %s. \n"
"                        Żądanie czatu zostało anulowane."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<small>%</small>"
msgstr "<small>%</small>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Live Chat</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<span>Livechat Channel</span>"
msgstr "<span>Kanał czatu</span>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "Available"
msgstr "Dostępny"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Avatar"
msgstr "Awatar"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Bad"
msgstr "Zły"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__can_publish
msgid "Can Publish"
msgstr "Można publikować"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Channel"
msgstr "Kanał"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_tree
msgid "Chat"
msgstr "Czat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Chats"
msgstr "Czaty"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_description
msgid "Description of the channel displayed on the website page"
msgstr "Opis dla kanału widoczny na stronie internetowej"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr "Kanał dyskusyjny"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Great"
msgstr "Świetnie"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_ir_http
msgid "HTTP Routing"
msgstr "Wytyczanie HTTP"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Happy face"
msgstr "Zadowolona twarz"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "History"
msgstr "Historia"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "In Conversation"
msgstr "W trakcie rozmowy"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__is_published
msgid "Is Published"
msgstr "Opublikowane"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Lang"
msgstr "Jęz."

#. module: website_livechat
#: code:addons/website_livechat/models/website.py:0
#, python-format
msgid "Live Support"
msgstr "Wsparcie przez czat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Live chat channel of your website"
msgstr "Kanał czatu na żywo na Twojej stronie internetowej"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_im_livechat_channel
msgid "Livechat Channel"
msgstr "Kanał czatu"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "Livechat Support Channels"
msgstr "Kanały wsparcia dla czatu"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:0
#, python-format
msgid "Name"
msgstr "Nazwa"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Neutral face"
msgstr "Neutralna twarz"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:0
#, python-format
msgid "New Channel"
msgstr "Nowy kanał"

#. module: website_livechat
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid "No Livechat Channel allows you to send a chat request for website %s."
msgstr ""
"Żaden kanał czatu nie pozwala na wysłanie zapytania o czat dla strony "
"internetowej %s."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Not rated yet"
msgstr "Jeszcze nie ocenione"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Okay"
msgstr "dobrze"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Online"
msgstr "Dostępny"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Operator Avatar"
msgstr "Awatar operatora"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_name
msgid "Operator Name"
msgstr "Nazwa operatora"

#. module: website_livechat
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid ""
"Recipients are not available. Please refresh the page to get latest visitors"
" status."
msgstr ""
"Odbiorcy są niedostępni. Proszę odświeżyć stronę, aby zaktualizować statusy "
"osób odwiedzających."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Sad face"
msgstr "Smutna twarz"

#. module: website_livechat
#: model:ir.actions.server,name:website_livechat.website_livechat_send_chat_request_action_server
msgid "Send Chat Requests"
msgstr "Wyślij prośbę o czat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
msgid "Send chat request"
msgstr "Wyślij prośbę o czat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Speaking With"
msgstr "Rozmawia z"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_id
msgid "Speaking with"
msgstr "Rozmawia z"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Statistics"
msgstr "Statystyki"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The"
msgstr "-"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The Team"
msgstr "Zespół"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_url
msgid "The full URL to access the document through the website."
msgstr "Pełny adres URL dostępu do dokumentu przez stronę."

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "The visitor"
msgstr "Osoba odwiedzająca"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "There are no public livechat channels to show."
msgstr "Nie ma publicznych kanałów czatu do pokazania."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "There are no ratings for this channel for now."
msgstr "Ten kanał nie ma jeszcze żadnej oceny."

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_published
msgid "Visible on current website"
msgstr "Widoczne na obecnej stronie"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_mail_channel__livechat_visitor_id
msgid "Visitor"
msgstr "Gość"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Visitor is online"
msgstr "Odwiedzający jest online"

#. module: website_livechat
#: model:ir.actions.act_window,name:website_livechat.website_visitor_livechat_session_action
msgid "Visitor's Sessions"
msgstr "Sesje odwiedzającego"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__mail_channel_ids
msgid "Visitor's livechat channels"
msgstr "Kanały czatu odwiedzającego"

#. module: website_livechat
#: model:ir.ui.menu,name:website_livechat.website_livechat_visitor_menu
msgid "Visitors"
msgstr "Odwiedzający"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#: model:ir.model,name:website_livechat.model_website
#, python-format
msgid "Website"
msgstr "Strona internetowa"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_res_config_settings__channel_id
msgid "Website Live Channel"
msgstr "Kanał na żywo na stronie internetowej"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website__channel_id
msgid "Website Live Chat Channel"
msgstr "Kanał czatu na stronie"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_url
msgid "Website URL"
msgstr "Adres strony internetowej"

#. module: website_livechat
#: code:addons/website_livechat/tests/test_livechat_basic_flow.py:0
#: model:ir.model,name:website_livechat.model_website_visitor
#, python-format
msgid "Website Visitor"
msgstr "Odwiedzający stronę"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_description
msgid "Website description"
msgstr "Opis strony"

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "an operator"
msgstr "operator"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "last feedbacks"
msgstr "ostatnia opinia"
