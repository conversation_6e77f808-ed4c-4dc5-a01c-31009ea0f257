# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_gmail
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-25 14:36+0000\n"
"PO-Revision-Date: 2022-03-03 13:58+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Gmail Credentials</span>"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token
msgid "Access Token"
msgstr "Prístupový token"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token_expiration
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token_expiration
msgid "Access Token Expiration Timestamp"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_authorization_code
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_authorization_code
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "Authorization Code"
msgstr "Autorizačný kód"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Client ID"
msgstr "ID klienta"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Client Secret"
msgstr "Tajný kľúč klienta"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "Gmail"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__use_google_gmail_service
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__use_google_gmail_service
msgid "Gmail Authentication"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_identifier
msgid "Gmail Client Id"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_secret
msgid "Gmail Client Secret"
msgstr ""

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_google_gmail_mixin
msgid "Google Gmail Mixin"
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__id
msgid "ID"
msgstr "ID"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_ir_mail_server
msgid "Mail Server"
msgstr "Mailový server"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_refresh_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_refresh_token
msgid "Refresh Token"
msgstr "Obnoviť token"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Send and receive email with your Gmail account."
msgstr ""

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"Setup your Gmail API credentials in the general settings to link a Gmail "
"account."
msgstr ""

#. module: google_gmail
#: model:ir.model.fields,help:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,help:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "The URL to generate the authorization code from Google"
msgstr "URL pre generovanie autorizačného kódu z Google"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "URI"
msgstr "URI"
