<?xml version="1.0" encoding="UTF-8" ?>
<odoo><data>

    <record id="event_type_booth_demo_conference_0" model="event.type.booth">
        <field name="name">Showbooth 1</field>
        <field name="event_type_id" ref="event.event_type_data_conference"/>
        <field name="booth_category_id" ref="event_booth_category_standard"/>
    </record>
    <record id="event_type_booth_demo_conference_1" model="event.type.booth">
        <field name="name">Showbooth 2</field>
        <field name="event_type_id" ref="event.event_type_data_conference"/>
        <field name="booth_category_id" ref="event_booth_category_standard"/>
    </record>
    <record id="event_type_booth_demo_conference_2" model="event.type.booth">
        <field name="name">Premium Showbooth 1</field>
        <field name="event_type_id" ref="event.event_type_data_conference"/>
        <field name="booth_category_id" ref="event_booth_category_premium"/>
    </record>
    <record id="event_type_booth_demo_conference_3" model="event.type.booth">
        <field name="name">Premium Showbooth 2</field>
        <field name="event_type_id" ref="event.event_type_data_conference"/>
        <field name="booth_category_id" ref="event_booth_category_premium"/>
    </record>

</data></odoo>
