<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_in" model="res.partner">
        <field name="name">IN Company</field>
        <field name="vat">36AABCT1332L011</field>
        <field name="street">Block no. 401</field>
        <field name="street2">Street 2</field>
        <field name="city">Hyderabad</field>
        <field name="country_id" ref="base.in"/>
        <field name="state_id" ref="base.state_in_ts"/>
        <field name="zip">500001</field>
        <field name="phone">+91 81234 56789</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.inexample.com</field>
    </record>

    <record id="demo_company_in" model="res.company">
        <field name="name">IN Company</field>
        <field name="partner_id" ref="partner_demo_company_in"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_in')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_in.demo_company_in'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_in.indian_chart_template_standard')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_in.demo_company_in')"/>
    </function>
</odoo>
