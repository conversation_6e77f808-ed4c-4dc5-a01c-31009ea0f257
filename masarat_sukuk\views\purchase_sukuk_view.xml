<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="purchase_sukuk_details_form" model="ir.ui.view">
        <field name="name">purchase.sukuk.wizard.report2.form</field>
        <field name="model">purchase.sukuk.wizard.report2</field>
        <field name="arch" type="xml">
            <form string="صكوك مشتريات/مصروفات">
                <group>
                    <group>
                        <field name="type" required="1"/>
                        <field name="move_id" attrs="{'required':[('type','=','move')], 'invisible':[('type','=','expense')]}" options="{'no_create': True, 'no_create_edit':True}"/>
                        <field name="expense_id" attrs="{'required':[('type','=','expense')], 'invisible':[('type','=','move')]}" options="{'no_create': True, 'no_create_edit':True}"/>
                        <field name="move_partner_id" readonly="1" options="{'no_open':True}"  attrs="{'invisible':[('type','!=','move')]}"/>
                        <field name="expense_partner_id" options="{'no_open':True}" attrs="{'required':[('type','=','expense')],'invisible':[('type','!=','expense')]}"/>
                        <field name="amount"/>
                    </group>
                    <group>
                        <field name="account_no_id" options="{'no_create': True, 'no_create_edit':True}"/>
                        <field name="bank_id" options="{'no_create': True, 'no_create_edit':True}"/>
                        <field name="branch_id" options="{'no_create': True, 'no_create_edit':True}"/>
                        <field name="dialog_box" readonly="1" nolabel="1"/>
                    </group>
                    <group>
                        <field name="user_id" readonly="1"/>
                        <field name="sukuk_computed" invisible="1" readonly="1"/>
                    </group>
                </group>
                <h3>
                    في حالة تعديل البيانات يدوياً يرجى التأكد من صحة الرقم التسلسلي ورقم الدفتر
                </h3>
                <notebook>
                    <page string="الحوافظ">
                        <field name="sukuk_page_ids" readonly="0">
                            <tree editable="top" create="false">
                                <field name="sukuk_book_id" string="دفتر الصكوك" options="{'no_create': True,'no_open': True, 'no_create_edit':True}" invisible="0"/>
                                <field name="bank_id" invisible="0" readonly="1"/>
                                <field name="branch_id" invisible="0" readonly="1"/>
                                <field name="partner_paid_to" readonly="1"/>
                                <field name="person_signature" readonly="1"/>
                                <field name="suke_book_number" readonly="0"/>
                                <field name="serial_no" readonly="0"/>
                                <field name="amount" readonly="1"/>
                                <field name="note" readonly="1"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
                <footer>
                    <button name="generate_payslips_sukuk"
                            type="object"
                            string="انشاء صك"
                            attrs="{'invisible':[('sukuk_computed','=',True)]}"
                            class="btn-primary"/>
                    <button name="get_confirm"
                            type="object"
                            string="تأكيد"
                            class="btn-primary"
                            attrs="{'invisible':[('sukuk_computed','=',False)]}"
                            confirm="في حالة التأكيد فسيتم اصدار الصك وتخزينه في المنظومة !"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>


    <record id="action_report_purchase_sukuk_details" model="ir.actions.act_window">
        <field name="name">اصدار صكوك مشتريات/مصروفات</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">purchase.sukuk.wizard.report2</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="purchase_sukuk_details_form"/>
        <field name="target">new</field>
    </record>

    <menuitem id="purchase_sukuk_menu"
              name="اصدار صكوك مشتريات/مصروفات"
              parent="account.menu_finance_entries"
              sequence="82"
              action="action_report_purchase_sukuk_details"/>

</odoo>