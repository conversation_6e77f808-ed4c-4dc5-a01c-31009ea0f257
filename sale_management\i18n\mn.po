# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_management
# 
# Translators:
# <PERSON>, 2021
# Наса<PERSON>-<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# hish, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Ba<PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <bask<PERSON><PERSON><PERSON><EMAIL>>, 2022\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_management
#: model:sale.order.template.line,name:sale_management.sale_order_template_line_1
msgid "4 Person Desk"
msgstr "4 хүний ширээ"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
msgid "<span>Options</span>"
msgstr "<span>Сонголтууд</span>"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__active
msgid "Active"
msgstr "Идэвхитэй"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a note"
msgstr "Тэмдэглэл нэмэх"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a product"
msgstr "Бараа нэмэх"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a section"
msgstr "Бүлэг нэмэх"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add one"
msgstr "Нэгийг нэмэх"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add to cart"
msgstr "Сагсанд нэмэх"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Add to order lines"
msgstr "Захиалгын мөр дээр нэмэх"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total
msgid "All Sales"
msgstr "Бүх борлуулалт"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Archived"
msgstr "Архивласан"

#. module: sale_management
#: model:ir.ui.menu,name:sale_management.menu_product_attribute_action
msgid "Attributes"
msgstr "Шинж чанарууд"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_uom_category_id
msgid "Category"
msgstr "Ангилал"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__company_id
msgid "Company"
msgstr "Компани"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Confirmation"
msgstr "Баталгаа"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__mail_template_id
msgid "Confirmation Mail"
msgstr "Баталгаажуулах шуудан"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_option__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Хэмжих нэгжийг хооронд нь хөрвүүлэх явдал нь зөвхөн нэг ангилалд хамаарч "
"байвал л хийгдэнэ. Хөрвүүлэлт нь харьцаан дээр суурилж явагдана."

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Create standardized offers with default products"
msgstr "Үндсэн бараа бүтээгдэхүүнүүдээ ашиглан стандарт үнийн санал бэлтгэх"

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid "Create your quotation template"
msgstr "Үнийн саналын загвараа үүсгэнэ үү"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_company__sale_order_template_id
msgid "Default Sale Template"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__company_so_template_id
msgid "Default Template"
msgstr "Үндсэн загвар"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__name
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Description"
msgstr "Тайлбар"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid ""
"Design your quotation templates using building blocks<br/>\n"
"                            <em attrs=\"{'invisible': [('module_sale_quotation_builder','=',False)]}\">Warning: this option will install the Website app.</em>"
msgstr ""
"Блок байгуулах боломжыг ашиглан үнийн саналын загвараа дизайнжуулаарай<br/>\n"
"                            <em attrs=\"{'invisible': [('module_sale_quotation_builder','=',False)]}\">Анхаар: Энэ сонголт нь Website app-г суулгах болно.</em>"

#. module: sale_management
#: model:ir.model,name:sale_management.model_digest_digest
msgid "Digest"
msgstr "Товч"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Disc.%"
msgstr "ХӨН.%"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__discount
msgid "Discount (%)"
msgstr "Хөнгөлөлт (%)"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_type
msgid "Display Type"
msgstr "Харагдах хэлбэр"

#. module: sale_management
#: code:addons/sale_management/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Хандах эрх алга, энэ өгөгдлийг мэдээ илгээх имэйл жагсаалт руу хийхгүй"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Documentation"
msgstr "Баримтжуулалт"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_non_accountable_fields_null
msgid ""
"Forbidden product, unit price, quantity, and UoM on non-accountable sale "
"quote line"
msgstr ""
"Хүчин төгөлдөр бус борлуулалтын үнийн саналын мөр дээр бараа, нэгж үнэ, тоо "
"хэмжээ болон хэмжих нэгж сонгох хориотой."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__sequence
msgid "Gives the sequence order when displaying a list of optional products."
msgstr "Нэмэлт барааг жагсаалтанд харуулахдаа эрэмбэ дараалал тодорхойлно уу."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__sequence
msgid "Gives the sequence order when displaying a list of sale quote lines."
msgstr ""
"Борлуулалтын үнийн саналын мөрүүдийг харуулах үед дэс дарааллын эрэмбийг "
"өгнө."

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__id
msgid "ID"
msgstr "ID"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__active
msgid ""
"If unchecked, it will allow you to hide the quotation template without "
"removing it."
msgstr ""
"Хэрэв сонгоогүй бол, энэ нь танд үнийн саналыг устгалгүйгээр ашиглалтаас "
"хасах боломжыг олгоно."

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total_value
msgid "Kpi All Sale Total Value"
msgstr "Бүх борлуулалтын нийт дүнгийн KPI"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__line_id
msgid "Line"
msgstr "Мөр"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_line_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Lines"
msgstr "Мөрүүд"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_accountable_product_id_required
msgid "Missing required product and UoM on accountable sale quote line."
msgstr ""
"Хүчин төгөлдөр борлуулалтын үнийн саналын мөр дээр бараа болон хэмжих нэгж "
"заавал сонгогдох ёстой."

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Note"
msgstr "Тэмдэглэл"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__number_of_days
msgid "Number of days for the validity date computation of the quotation"
msgstr "Үнийн саналын хүчинтэй хугацааг тооцох өдрийн тоо"

#. module: sale_management
#: model:sale.order.template.option,name:sale_management.sale_order_template_option_1
msgid "Office Chair"
msgstr "Оффисын сандал"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_payment
msgid "Online Payment"
msgstr "Онлайн төлбөр"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_signature
msgid "Online Signature"
msgstr "Онлайн гарын үсэг"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_option_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Optional Products"
msgstr "Нэмж санал болгох бараа"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_option_ids
#: model:ir.model.fields,field_description:sale_management.field_sale_order_line__sale_order_option_ids
msgid "Optional Products Lines"
msgstr "Нэмэлт барааны мөрүүд"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Options"
msgstr "Сонголтууд"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__is_present
msgid "Present on Quotation"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_id
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Product"
msgstr "Бараа"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__quantity
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_qty
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__quantity
msgid "Quantity"
msgstr "Тоо хэмжээ"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Quantity:"
msgstr "Тоо хэмжээ:"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__module_sale_quotation_builder
msgid "Quotation Builder"
msgstr "Үнийн санал зохиомжлогч"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__number_of_days
msgid "Quotation Duration"
msgstr "Үнийн саналын хүчинтэй хугацаа"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__name
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_tree
msgid "Quotation Template"
msgstr "Үнийн саналын загвар"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Үнийн саналын загварын мөр"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation Template Lines"
msgstr "Үнийн саналын загварын мөр"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "Үнийн саналын загварын сонголт"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__sale_order_template_id
msgid "Quotation Template Reference"
msgstr "Үнийн саналын загварын холбоос"

#. module: sale_management
#: model:ir.actions.act_window,name:sale_management.sale_order_template_action
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__group_sale_order_template
#: model:ir.ui.menu,name:sale_management.sale_order_template_menu
#: model:res.groups,name:sale_management.group_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Quotation Templates"
msgstr "Үнийн саналын загвар"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation expires after"
msgstr "Үнийн саналын хугацаа дуусах"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove"
msgstr "Хасах"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove one"
msgstr "Нэгийг хасах"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr ""
"Захиалагч руу захиалгыг онлайн гарын үсгээр баталгаажуулах хүсэлт илгээх."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr ""
"Захиалагч руу захиалгыг онлайн төлбөрөөр баталгаажуулах хүсэлт илгээх."

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_option
msgid "Sale Options"
msgstr "Борлуулалтын сонголтууд"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.digest_digest_view_form
msgid "Sales"
msgstr "Борлуулалт"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order
msgid "Sales Order"
msgstr "Борлуулалтын захиалга"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_line
msgid "Sales Order Line"
msgstr "Борлуулалтын захиалгын мөр"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__order_id
msgid "Sales Order Reference"
msgstr "Борлуулалтын захиалгын холбоос"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Sales Quotation Template Lines"
msgstr "Борлуулалтын үнийн саналын загварын мөр"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Search Quotation Template"
msgstr "Үнийн саналын загвар хайлт"

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_section
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Section"
msgstr "Бүлэг"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid ""
"Selling the same product in different sizes or colors? Try the product grid "
"and populate your orders with multiple quantities of each variant. This "
"feature also exists in the Purchase application."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__sequence
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sequence
msgid "Sequence"
msgstr "Дугаарлалт"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid ""
"Struggling with a complex product catalog? Try out the Product Configurator "
"to help sales configure a product with different options: colors, size, "
"capacity, etc. Make sale orders encoding easier and error-proof."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__display_type
msgid "Technical field for UX purpose."
msgstr "UX зорилготой техникийн талбар"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__note
msgid "Terms and conditions"
msgstr "Худалдааны нөхцөл, болзол"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid ""
"The Administrator can set default Terms & Conditions in Sales Settings. "
"Terms set here will show up instead if you select this quotation template."
msgstr ""
"Админ хэрэглэгч борлуулалтын ерөнхий тохиргоонд худалдааны үндсэн нөхцөл, "
"болзлыг тохируулах боломжтой. Энэхүү үнийн саналыг сонгож ашиглах үед энд "
"тохируулсан худалдааны нөхцөл ашиглагдах болно."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__mail_template_id
msgid ""
"This e-mail template will be sent on confirmation. Leave empty to send "
"nothing."
msgstr ""
"Борлуулалтыг батлах үед энэхүү загвар бүхий имэйл илгээгдэнэ. Юу ч илгээхгүй"
" байх бол хоосон үлдээнэ үү."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__is_present
msgid ""
"This field will be checked if the option line's product is already present "
"in the quotation."
msgstr ""

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale1_management_0
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid "Tip: Odoo supports configurable products"
msgstr ""

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale_management_1
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid "Tip: Sell or buy products in bulk with matrixes"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__price_unit
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Unit Price"
msgstr "Нэгж үнэ"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Unit Price:"
msgstr "Нэгж үнэ"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_id
msgid "Unit of Measure"
msgstr "Хэмжих нэгж"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__uom_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__uom_id
msgid "Unit of Measure "
msgstr "Хэмжих нэгж "

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "UoM"
msgstr "Хэмжих нэгж"

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid ""
"Use templates to create polished, professional quotes in minutes.\n"
"                Send these quotes by email and let your customers sign online.\n"
"                Use cross-selling and discounts to push and boost your sales."
msgstr ""
"Загвар ашиглан мэргэжлийн төвшинд чанартай үнийн саналыг хормын дотор бүтээнэ үү.\n"
"                 Энэ үнийн саналыг имэйлээр илгээж захиалагчаараа онлайн гарын үсэг зуруулах боломжтой.\n"
"                 Нэмэлт бараа болон хөнгөлөлт санал болгож борлуулалтаа өсгөн нэмэгдүүлнэ үү."

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid "You cannot add options to a confirmed order."
msgstr "Батлагдсан захиалга дээр сонголт нэмэх боломжгүй."

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"You cannot change the type of a sale quote line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"Та борлуулалтын үнийн саналын мөрийн төрлийг сольж өөрчлөх боломжгүй. Харин "
"үүний оронд тухайн мөрийг устгаад дахин шинээр зохих төрөл бүхий мөрүүдийг "
"нэмнэ үү."

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid "Your template cannot contain products from multiple companies."
msgstr ""

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"Your template contains products from company %(product_company)s whereas your template belongs to company %(template_company)s. \n"
" Please change the company of your template or remove the products from other companies."
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "days"
msgstr "өдөр"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "e.g. Standard Consultancy Package"
msgstr ""
