# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_delivery
# 
# Translators:
# <PERSON>, 2021
# Collex100, 2021
# <PERSON> <ye<PERSON><PERSON><PERSON><EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Константин <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Сергей Шеб<PERSON>ин <<EMAIL>>, 2021
# Evgeniia Kotova, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> Kotova, 2022\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "<b>Shipping Method: </b>"
msgstr "<b>Метод доставки:</b>"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Описание продукта, которое Вы хотели бы предоставлять вашим потребителям.Это"
" описание будет отображаться в каждом заказе покупателя, заказе на доставку "
"и акте покупателю/ кредитном обязательстве"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__can_publish
msgid "Can Publish"
msgstr "Может публиковать"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "Choose a delivery method"
msgstr "Выберите способ доставки"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_res_country
msgid "Country"
msgstr "Страна"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "DHL Shipping Methods"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "Сумма доставки"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery will be updated after choosing a new delivery method"
msgstr "Доставку будет обновлен после выбора нового способа доставки"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery:"
msgstr "доставка:"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description"
msgstr "Описание"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr ""
"Описание отображается на eCommerce и онлайн коммерческих предложениях."

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "Описание для Онлайн-предложения"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "FedEx Shipping Methods"
msgstr ""

#. module: website_sale_delivery
#. openerp-web
#: code:addons/website_sale_delivery/static/src/js/website_sale_delivery.js:0
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_methods
#, python-format
msgid "Free"
msgstr "Бесплатно"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__is_published
msgid "Is Published"
msgstr "Опубликовано"

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"It seems that there is already a transaction for your order, you can not "
"change the delivery method anymore"
msgstr ""

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"Ни один метод доставки не доступен для текущего заказа и адреса доставки. "
"Пожалуйста, свяжитесь с нами для получения дополнительной информации."

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_search_inherit_website_sale_delivery
msgid "Published"
msgstr "Опубликовано"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_id
msgid "Restrict publishing to this website."
msgstr "Ограничить публикацию на этом сайте."

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_methods
msgid "Select to compute delivery rate"
msgstr "Выберите, чтобы вычислить скорость доставки"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_delivery_carrier
#: model:ir.ui.menu,name:website_sale_delivery.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Методы доставки"

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr "К сожалению, мы не можем отправить ваш заказ"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_sale_order__amount_delivery
msgid "The amount without tax."
msgstr "Сумма без налога."

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_url
msgid "The full URL to access the document through the website."
msgstr "Полный URL, чтобы получить доступ к документу через веб-сайт."

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "USPS Shipping Methods"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_published
msgid "Visible on current website"
msgstr "Видимый на текущем сайте"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_id
msgid "Website"
msgstr "Вебсайт"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_url
msgid "Website URL"
msgstr "URL Веб-сайта"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr ""
