<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="website.ThemePreview.Buttons">
        <div>
            <div class="o_form_buttons_view" role="toolbar" aria-label="Main actions">
                <button type="object" name="button_choose_theme" class="btn btn-primary o_use_theme">
                    Start Now
                </button>
                <button class="btn btn-link o_switch_theme">
                    Choose another theme
                </button>
            </div>
        </div>
    </t>

    <t t-name="website.ThemePreview.SwitchModeButton">
        <div class="btn-group btn-group-toggle ml-1" data-toggle="buttons">
            <label class="btn btn-secondary active">
                <input type="radio" name="viewer" data-mode='desktop' autocomplete="off" checked='checked'/> Desktop
            </label>
            <label class="btn btn-secondary">
                <input type="radio" name="viewer" data-mode='mobile' autocomplete="off"/> Mobile
            </label>
        </div>
    </t>
    <t t-name="website.ThemePreview.Loader">
        <div class="o_theme_install_loader_container position-fixed fixed-top fixed-left
            h-100 w-100 d-flex flex-column align-items-center text-white font-weight-bold text-center">
            <t t-if="showTips">Building your website...</t>
            <div class="o_theme_install_loader"></div>
            <p t-if="showTips" class="o_theme_install_loader_tip w-25">
                TIP: Once loaded, follow the
                <span class="o_tooltip o_tooltip_visible bottom o_animated position-relative"></span>
                <br/>pointer to build the perfect page in 7 steps.
            </p>
        </div>
    </t>
</templates>
