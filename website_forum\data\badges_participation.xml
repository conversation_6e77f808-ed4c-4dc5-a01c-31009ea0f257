<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Biography: complet your profile -->
        <record id="badge_p_1" model="gamification.badge">
            <field name="name">Autobiographer</field>
            <field name="description">Completed own biography</field>
            <field name="level">bronze</field>
            <field name="rule_auth">nobody</field>
        </record>
        <record model="gamification.goal.definition" id="definition_configure_profile">
            <field name="name">Completed own biography</field>
            <field name="description">Write some information about yourself</field>
            <field name="computation_mode">count</field>
            <field name="display_mode">boolean</field>
            <field name="model_id" ref="base.model_res_users"/>
            <field name="condition">higher</field>
            <field name="domain">[
                ('partner_id.country_id', '!=', False),
                ('partner_id.city', '!=', False),
                ('partner_id.email', '!=', False)
            ]</field>
            <field name="batch_mode">True</field>
            <field name="batch_distinctive_field" ref="base.field_res_users__id"/>
            <field name="batch_user_expression">user.id</field>
        </record>
        <record model="gamification.challenge" id="challenge_configure_profile">
            <field name="name">Complete own biography</field>
            <field name="period">once</field>
            <field name="visibility_mode">personal</field>
            <field name="report_message_frequency">never</field>
            <field name="reward_id" ref="badge_p_1"/>
            <field name="reward_realtime">True</field>
            <field name="user_domain">[('karma', '>', 0)]</field>
            <field name="state">inprogress</field>
            <field name="challenge_category">forum</field>
        </record>
        <record model="gamification.challenge.line" id="line_configure_profile">
            <field name="definition_id" ref="definition_configure_profile"/>
            <field name="challenge_id" ref="challenge_configure_profile"/>
            <field name="target_goal">1</field>
        </record>

        <!-- Commentator: at least 10 comments posted on posts -->
        <record id="badge_p_2" model="gamification.badge">
            <field name="name">Commentator</field>
            <field name="description">Posted 10 comments</field>
            <field name="level">bronze</field>
            <field name="rule_auth">nobody</field>
        </record>
        <record model="gamification.goal.definition" id="definition_commentator">
            <field name="name">Commentator</field>
            <field name="description">Comment an answer or a question</field>
            <field name="computation_mode">count</field>
            <field name="display_mode">boolean</field>
            <field name="model_id" ref="mail.model_mail_message"/>
            <field name="condition">higher</field>
            <field name="domain" eval="[('message_type', '=', 'comment'), ('subtype_id', '=', ref('mail.mt_comment')), ('model', '=', 'forum.post')]"/>
            <field name="batch_mode">True</field>
            <field name="batch_distinctive_field" ref="mail.field_mail_message__author_id"/>
            <field name="batch_user_expression">user.partner_id.id</field>
        </record>
        <record model="gamification.challenge" id="challenge_commentator">
            <field name="name">Commentator</field>
            <field name="period">once</field>
            <field name="visibility_mode">personal</field>
            <field name="report_message_frequency">never</field>
            <field name="reward_id" ref="badge_p_2"/>
            <field name="reward_realtime">True</field>
            <field name="user_domain">[('karma', '>', 0)]</field>
            <field name="state">inprogress</field>
            <field name="challenge_category">forum</field>
        </record>
        <record model="gamification.challenge.line" id="line_commentator">
            <field name="definition_id" ref="definition_commentator"/>
            <field name="challenge_id" ref="challenge_commentator"/>
            <field name="target_goal">10</field>
        </record>

        <!-- Pundit: 10 answers with at least score of 10 -->
        <record id="badge_25" model="gamification.badge">
            <field name="name">Pundit</field>
            <field name="description">Left 10 answers with score of 10 or more</field>
            <field name="level">silver</field>
            <field name="rule_auth">nobody</field>
        </record>
        <record model="gamification.goal.definition" id="definition_pundit">
            <field name="name">Pundit</field>
            <field name="description">Post 10 answers with score of 10 or more</field>
            <field name="display_mode">boolean</field>
            <field name="condition">higher</field>
            <field name="model_id" ref="website_forum.model_forum_post"/>
            <field name="domain" eval="[('parent', '!=', False), ('vote_count' '>=', 10)]"/>
            <field name="batch_mode">True</field>
            <field name="batch_distinctive_field" ref="website_forum.field_forum_post__create_uid"/>
            <field name="batch_user_expression">user.id</field>
        </record>
        <record model="gamification.challenge" id="challenge_pundit">
            <field name="name">Pundit</field>
            <field name="period">once</field>
            <field name="visibility_mode">personal</field>
            <field name="report_message_frequency">never</field>
            <field name="reward_id" ref="badge_25"/>
            <field name="reward_realtime">True</field>
            <field name="user_domain">[('karma', '>', 0)]</field>
            <field name="state">inprogress</field>
            <field name="challenge_category">forum</field>
        </record>
        <record model="gamification.challenge.line" id="line_pundit">
            <field name="definition_id" ref="definition_pundit"/>
            <field name="target_goal">10</field>
            <field name="challenge_id" ref="challenge_pundit"/>
        </record>

        <!-- Chief Commentator: 100 comments -->
        <record id="badge_p_4" model="gamification.badge">
            <field name="name">Chief Commentator</field>
            <field name="description">Posted 100 comments</field>
            <field name="level">silver</field>
            <field name="rule_auth">nobody</field>
        </record>
        <record model="gamification.challenge" id="challenge_chief_commentator">
            <field name="name">Chief Commentator</field>
            <field name="period">once</field>
            <field name="visibility_mode">personal</field>
            <field name="report_message_frequency">never</field>
            <field name="reward_id" ref="badge_p_4"/>
            <field name="reward_realtime">True</field>
            <field name="user_domain">[('karma', '>', 0)]</field>
            <field name="state">inprogress</field>
            <field name="challenge_category">forum</field>
        </record>
        <record model="gamification.challenge.line" id="line_chief_commentator">
            <field name="definition_id" ref="definition_commentator"/>
            <field name="challenge_id" ref="challenge_chief_commentator"/>
            <field name="target_goal">100</field>
        </record>

        <record id="badge_32" model="gamification.badge">
            <field name="name">Taxonomist</field>
            <field name="description">Created a tag used by 15 questions</field>
            <field name="level">silver</field>
            <field name="rule_auth">nobody</field>
        </record>
        <record model="gamification.goal.definition" id="definition_taxonomist">
            <field name="name">Taxonomist</field>
            <field name="description">Create a tag which can used in minimum 15 questions</field>
            <field name="computation_mode">count</field>
            <field name="display_mode">boolean</field>
            <field name="model_id" ref="website_forum.model_forum_tag"/>
            <field name="condition">higher</field>
            <field name="domain">[('posts_count', '>=', 15)]</field>
            <field name="batch_mode">True</field>
            <field name="batch_distinctive_field" ref="website_forum.field_forum_tag__create_uid"/>
            <field name="batch_user_expression">user.id</field>
        </record>
        <record model="gamification.challenge" id="challenge_taxonomist">
            <field name="name">Taxonomist</field>
            <field name="period">once</field>
            <field name="visibility_mode">personal</field>
            <field name="report_message_frequency">never</field>
            <field name="reward_id" ref="badge_32"/>
            <field name="reward_realtime">True</field>
            <field name="user_domain">[('karma', '>', 0)]</field>
            <field name="state">inprogress</field>
            <field name="challenge_category">forum</field>
        </record>
        <record model="gamification.challenge.line" id="line_taxonomist">
            <field name="definition_id" ref="definition_taxonomist"/>
            <field name="challenge_id" ref="challenge_taxonomist"/>
            <field name="target_goal">1</field>
        </record>

    </data>
</odoo>
