<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!--
    #
    # SYSCOHADA : Définition des taux de TVA SYSCOHADA
    # Auteur : BAAMTU Sénégal
    # Version du fichier : 1.0
    # Date : 02/2010
    #
    #
    -->
    <record model="account.tax.template" id="tva_sale_18">
        <field name="name">TVA 18% (vente)</field>
        <field name="chart_template_id" ref="syscohada_chart_template"/>
        <field name="amount">18</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_18"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4441'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4449'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_purchase_18">
        <field name="name">TVA 18% (achat)</field>
        <field name="chart_template_id" ref="syscohada_chart_template"/>
        <field name="amount">18</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_18"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4441'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4449'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_exonere">
        <field name="name">Exonéré de TVA (vente)</field>
        <field name="chart_template_id" ref="syscohada_chart_template"/>
        <field name="amount">0</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_achat_exonere">
        <field name="name">Exonéré de TVA (achat)</field>
        <field name="chart_template_id" ref="syscohada_chart_template"/>
        <field name="amount">0</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

</odoo>
