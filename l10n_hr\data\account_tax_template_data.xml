<?xml version="1.0" encoding="utf-8"?>
<odoo>


    <record id="rrif_pdv_25" model="account.tax.template">
        <field name="description">PDV 25%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% PDV</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="10"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24003'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni_25')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24003'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni_25')],
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_25usl" model="account.tax.template">
        <field name="description">PDV 25% Usluge</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% PDV usluge</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24003'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni_25')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24003'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni_25')],
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_10" model="account.tax.template">
        <field name="description">PDV 10%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">10% PDV</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24000'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24000'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni')],
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_0" model="account.tax.template">
        <field name="description">PDV  0%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% PDV</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_isporuke_po')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_isporuke_po')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_avans_25" model="account.tax.template">
        <field name="description">PDV za predujam 25%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% PDV (za predujam)</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24013'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni_25')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24013'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni_25')],
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_avans_10" model="account.tax.template">
        <field name="description">PDV za predujam 10%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">10% PDV (za predujam)</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24010'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24010'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni')],
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_avans_0" model="account.tax.template">
        <field name="description">PDV za predujam 0%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% PDV (za predujam)</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_isporuke_po')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_isporuke_po')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_nezar_isp_25" model="account.tax.template">
        <field name="description">PDV po nezaračunanim isporukama 25%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% PDV za nezaračunane isp.</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24033'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni_25')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24033'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni_25')],
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_nezar_isp_10" model="account.tax.template">
        <field name="description">PDV po nezaračunanim isporukama 10%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">10% PDV za nezaračunane isp.</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24030'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif24030'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izdani_racuni')],
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_nezar_isp_0" model="account.tax.template">
        <field name="description">PDV po nezaračunanim isporukama 0%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% PDV za nezaračunane isp.</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_isporuke_po')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_isporuke_po')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_nepod_0" model="account.tax.template">
        <field name="description">1. KOJE NE PODLIJEŽU OPOREZIVANJU (čl. 2. u svezi s čl. 5 i čl. 8 st. 7 Zakona)</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% Ne podliježe op.</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_koje')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_koje')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_osl_izvoz_0" model="account.tax.template">
        <field name="description">2.1. IZVOZNE - s pravom na odbitak pretporeza (čl. 13. st. 1. toč. 1. i čl. 14. Zakona)</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% osl. izvozne</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izvozne')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_izvozne')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_osl_medpri_0" model="account.tax.template">
        <field name="description">2.2. U VEZI S MEĐUNARODNIM PRIJEVOZOM  (čl. 13.b Zakona)</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% osl. međ. prijevoz</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_isporuke')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_isporuke')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_osl_tuz_0" model="account.tax.template">
        <field name="description">2.3. TUZEMNE - bez prava na odbitak pretporeza (čl. 11. i čl. 11a Zakona)</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% osl tuzemne</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_tuzemne')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_tuzemne')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pdv_osl_ost_0" model="account.tax.template">
        <field name="description">2.4. OSTALO (čl. 13. st. 1. toč. 2. I čl. 13a Zakona) </field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% osl. ostalo</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_ostale')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_ostale')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pp_25" model="account.tax.template">
        <field name="description">Pretporez 25% PDV</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% PDV pretporez</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="10"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14003'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14003'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
        ]"/>
    </record>

    <record id="rrif_pp_25usl" model="account.tax.template">
        <field name="description">Pretporez 25% PDV Usluge</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% PDV pretporez usluge</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14003'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14003'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
        ]"/>
    </record>

    <record id="rrif_pp_10" model="account.tax.template">
        <field name="description">Pretporez 10% PDV</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">10% PDV pretporez</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="10"/>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14000'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_10_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14000'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_10_tax')],
            }),
        ]"/>
    </record>

    <record id="rrif_pp_0" model="account.tax.template">
        <field name="description">Pretporez 0% PDV</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% PDV pretporez</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_0')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_0')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pp_avans_25" model="account.tax.template">
        <field name="description">Pretporez za predujam 25% PDV</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% PDV pretporez za predujam</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14013'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14013'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
        ]"/>
    </record>

    <record id="rrif_pp_avans_0" model="account.tax.template">
        <field name="description">Pretporez za predujam 0% PDV</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% PDV pretporez za predujam</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_0')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_0')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rrif_pp_uvoz_25" model="account.tax.template">
        <field name="description">Plaćeni PDV 25% pri uvozu dobara</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% uvoz dobara</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14023'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14023'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu_tax')],
            }),
        ]"/>
    </record>

    <record id="rrif_pp_uvoz_10" model="account.tax.template">
        <field name="description">Plaćeni PDV 10% pri uvozu dobara</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">10% uvoz dobara</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="10"/>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14020'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14020'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu_tax')],
            }),
        ]"/>
    </record>

    <record id="rrif_pp_uvoz_0" model="account.tax.template">
        <field name="description">Plaćeni PDV 0% pri uvozu dobara</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">0% uvoz dobara</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>


     <record id="rrif_pp_ino_25" model="account.tax.template">
        <field name="description">Plaćeni PDV 25% na usluge inozemnih poduzetnika</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% ino. usluge</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_usluge_25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14033'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_usluge_25_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14033'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_usluge_25')],
            }),
        ]"/>
    </record>

    <record id="rrif_pp_ino_10" model="account.tax.template">
        <field name="description">Plaćeni PDV 10% na usluge inozemnih poduzetnika</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">10% ino. usluge</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_usluge_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14030'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_usluge_10_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_uvozu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14030'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_placeni_usluge_10')],
            }),
        ]"/>
    </record>

    <record id="rrif_ppr2_25" model="account.tax.template">
        <field name="description">25% R-2 dobavljač</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">25% R-2 dobavljač</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif1409'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif1409'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni')],
            }),
        ]"/>
    </record>

    <record id="rrif_pp_uvoz_samopdv_25" model="account.tax.template">
        <field name="description">Samo PDV kod uvoza 25%</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">Samo PDV kod uvoza 25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_other')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni_25')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_other')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni_25')],
            }),
        ]"/>
    </record>

    <record id="rrif_pp_uvoz_samopdv_25usl" model="account.tax.template">
        <field name="description">Samo PDV kod uvoza 25% usluge</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">Samo PDV kod uvoza 25% usluge</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_other')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni_usluge_25')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_other')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni_usluge_25')],
            }),
        ]"/>
    </record>

    <record id="rrif_ppdnp1_3070_1" model="account.tax.template">
        <field name="description">pp 30% Nepriznat 70% Priznat</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">pp 30% Nepriznat 70% Priznat</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_70'),ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_30')],
            }),
            (0,0, {
                'factor_percent': 30,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_tax')],
            }),
            (0,0, {
                'factor_percent': 70,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14002'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_70'),ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_30')],
            }),
            (0,0, {
                'factor_percent': 30,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_tax')],
            }),
            (0,0, {
                'factor_percent': 70,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14002'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
        ]"/>
    </record>

    <record id="rrif_ppdnp1_7030_1" model="account.tax.template">
        <field name="description">pp 70% Nepriznat 30% Priznat</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">pp 70% Nepriznat 30% Priznat</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_70'),ref('l10n_hr.account_tax_report_line_pretporez_25_30')],
            }),
            (0,0, {
                'factor_percent': 30,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14002'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
            (0,0, {
                'factor_percent': 70,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_70'),ref('l10n_hr.account_tax_report_line_pretporez_25_30')],
            }),
            (0,0, {
                'factor_percent': 30,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif14002'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_tax')],
            }),
            (0,0, {
                'factor_percent': 70,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_tax')],
            }),
        ]"/>
    </record>

    <record id="rrif_ppdnp1_3070_1_r2" model="account.tax.template">
        <field name="description">R2 pp 30% Nepriznat 70% Priznat</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">R2 pp 30% Nepriznat 70% Priznat</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_70'),ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_30')],
            }),
            (0,0, {
                'factor_percent': 30,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni')],
            }),
            (0,0, {
                'factor_percent': 70,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif1408'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_25_70'),ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_30')],
            }),
            (0,0, {
                'factor_percent': 30,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni')],
            }),
            (0,0, {
                'factor_percent': 70,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif1408'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_tax')],
            }),
        ]"/>
    </record>

    <record id="rrif_ppdnp1_7030_1_r2" model="account.tax.template">
        <field name="description">R2 pp 70% Nepriznat 30% Priznat</field>
        <field name="chart_template_id" ref="l10n_hr_chart_template_rrif"/>
        <field name="name">R2 pp 70% Nepriznat 30% Priznat</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="100"/>
        <field name="tax_group_id" ref="tax_group_25"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_70'),ref('l10n_hr.account_tax_report_line_pretporez_25_30')],
            }),
            (0,0, {
                'factor_percent': 30,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif1408'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni')],
            }),
            (0,0, {
                'factor_percent': 70,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'plus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_70'),ref('l10n_hr.account_tax_report_line_pretporez_25_30')],
            }),
            (0,0, {
                'factor_percent': 30,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif1408'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_pretporez_koji_neplaceni')],
            }),
            (0,0, {
                'factor_percent': 70,
                'repartition_type': 'tax',
                'account_id': ref('kp_rrif4199'),
                'minus_report_line_ids': [ref('l10n_hr.account_tax_report_line_nepriznati_pretporez_25_tax')],
            }),
        ]"/>
    </record>

</odoo>