<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="redirect_form">
        <form t-att-action="api_url" method="post">
            <input type="hidden" name="_input_charset" t-att-value="_input_charset"/>
            <input t-if="service == 'create_forex_trade'" type="hidden" name="currency" t-att-value="currency"/>
            <input type="hidden" name="notify_url" t-att-value="notify_url"/>
            <input type="hidden" name="out_trade_no" t-att-value="out_trade_no"/>
            <input type="hidden" name="partner" t-att-value="partner"/>
            <input t-if="product_code" type="hidden" name="product_code" t-att-value="product_code"/>
            <input type="hidden" name="return_url" t-att-value="return_url"/>
            <input type="hidden" name="service" t-att-value="service"/>
            <input type="hidden" name="sign" t-att-value="sign"/>
            <input type="hidden" name="subject" t-att-value="subject"/>
            <input type="hidden" name="total_fee" t-att-value="total_fee"/>
            <input type="hidden" name="sign_type" t-att-value="sign_type"/>
            <input t-if="payment_type" type="hidden" name="payment_type" t-att-value="payment_type"/>
            <input t-if="seller_email" type="hidden" name="seller_email" t-att-value="seller_email"/>
        </form>
    </template>

</odoo>
