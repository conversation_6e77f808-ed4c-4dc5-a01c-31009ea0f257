.o_mail_activity {

    .o_thread_date_separator {
        margin-top: 15px;
        margin-bottom: 30px;
        @include media-breakpoint-down(sm) {
            margin-top: 0px;
            margin-bottom: 15px;
        }
        border-bottom: 1px solid gray('400');
        text-align: center;

        .o_thread_date {
            position: relative;
            top: 10px;
            margin: 0 auto;
            padding: 0 10px;
            font-weight: bold;
            background: white;
        }
    }

    .o_thread_message {
        display: flex;
        padding: 4px $o-horizontal-padding;
        margin-bottom: 0px;

        .o_thread_message_sidebar {
            flex: 0 0 $o-mail-thread-avatar-size;
            margin-right: 10px;
            margin-top: 2px;
            text-align: center;
            font-size: smaller;
            .o_thread_message_sidebar_image {
                position: relative;
                height: $o-mail-thread-avatar-size;

                .o_updatable_im_status {
                    width: $o-mail-thread-avatar-size;
                }
                .o_mail_user_status {
                    position: absolute;
                    bottom: 0;
                    right: 0;

                    &.fa-circle-o {
                        display: none;
                    }
                }
            }

            @include media-breakpoint-down(sm) {
                margin-top: 4px;
                font-size: x-small;
            }

            .o_thread_message_avatar {
                width: $o-mail-thread-avatar-size;
                height: $o-mail-thread-avatar-size;
                object-fit: cover;
            }
        }
        .o_thread_icon {
            cursor: pointer;
            opacity: 0;
        }

        &:hover {
            .o_thread_icon {
                display: inline-block;
                opacity: $o-mail-thread-icon-opacity;
                &:hover {
                    opacity: 1;
                }
            }
        }

        .o_mail_redirect {
            cursor: pointer;
        }

        .o_thread_message_core {
            flex: 1 1 auto;
            min-width: 0;
            max-width: 100%;
            word-wrap: break-word;
            .o_thread_message_content > pre {
                white-space: pre-wrap;
                word-break: break-word;
            }

            .o_mail_note_title {
                margin-top: 9px;
            }

            .o_mail_subject {
                font-style: italic;
            }

            .o_mail_notification {
                font-style: italic;
                color: gray;
            }

            [summary~=o_mail_notification] { // name conflicts with channel notifications, but is odoo notification buttons to hide in chatter if present
                display: none;
            }

            p {
                margin: 0 0 9px; // Required by the old design to override a general rule on p's
                &:last-child {
                    margin-bottom: 0;
                }
            }
            a {
                display: inline-block;
                word-break: break-all;
            }
            :not(.o_image_box) > img {
                max-width: 100%;
                height: auto;
            }

            .o_mail_body_long {
                display: none;
            }

            .o_mail_info {
                margin-bottom: 2px;

                strong {
                    color: $headings-color;
                }
            }

            .o_thread_message_needaction, .o_thread_message_reply {
                padding: 4px;
            }
        }
    }
    .o_thread_title {
        margin-top: 20px;
        margin-bottom: 20px;
        font-weight: bold;
        font-size: 125%;

        &.o_neutral_face_icon:before {
            @extend %o-nocontent-init-image;
            @include size(120px, 140px);
            background: transparent url(/web/static/img/neutral_face.svg) no-repeat center;
        }
    }

    .o_mail_no_content {
        @include o-position-absolute(30%, 0, 0, 0);
        text-align: center;
        font-size: 115%;
    }

    .o_thread_message .o_thread_message_core .o_mail_read_more {
        display: block;
    }
}

.o_web_client .popover .o_thread_tooltip_icon {
    min-width: 1rem;
}

.o_web_client.o_touch_device {
    .o_mail_thread .o_thread_icon {
        opacity: $o-mail-thread-icon-opacity;
    }
}
