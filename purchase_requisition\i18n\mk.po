# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase_requisition
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-07-08 16:00+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/odoo/odoo-9/language/"
"mk/)\n"
"Language: mk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  mk.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"
"#-#-#-#-#  mk.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Call for Tender Reference:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Date</strong>"
msgstr "<strong>Датум</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Description</strong>"
msgstr "<strong>Опис</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Product UoM</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Qty</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Reference </strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Date</strong>"
msgstr "<strong>Закажан датум</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Ordering Date:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Selection Type:</strong><br/>"
msgstr "<strong>Тип на селекција:</strong><br/>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Source:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Vendor </strong>"
msgstr "<strong>Добавувач </strong>"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"A Call for Tenders is a procedure for generating competing offers from\n"
"            different bidders. In the call for tenders, you can record the\n"
"            products you need to buy and generate the creation of RfQs to\n"
"            vendors. Once the tenders have been registered, you can review "
"and\n"
"            compare them and you can validate some and cancel others."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_account_analytic_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_account_analytic_id
msgid "Analytic Account"
msgstr "Аналитичка сметка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Approved by Vendor"
msgstr "Одобрено од добавувач"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_bid_line_qty
msgid "Bid Line Qty"
msgstr "Количина на ставка од понуда"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Bid Selection"
msgstr "Избор на понуда"

#. module: purchase_requisition
#: model:ir.actions.report.xml,name:purchase_requisition.action_report_purchase_requisitions
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_requisition_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_name
msgid "Call for Tenders Reference"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Call for Tenders in negotiation"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Call for Tenders where tenders are closed"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_bid_line_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_partner
msgid "Cancel"
msgstr "Откажи"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel Call"
msgstr "Откажи повик"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Cancel Choice"
msgstr "Откажи избор"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel Purchase Order"
msgstr "Откажи налог за набавка"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Cancelled"
msgstr "Откажано"

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:302
#, python-format
msgid ""
"Cancelled by the call for tenders associated to this request for quotation."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:67
#, python-format
msgid "Cancelled by the tender associated to this quotation."
msgstr "Откажано од тендерот поврзан со оваа понуда"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_bid_line_qty
msgid "Change Bid line quantity"
msgstr "Промени количина на ставка од понуда"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_bid_line_qty
msgid "Change Quantity"
msgstr "Измени количина"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_partner
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_partner
msgid "Choose Vendor"
msgstr "Избери добавувач"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Choose product lines"
msgstr "Избери ставки на производи"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Click to start a new Call for Tenders process."
msgstr "Кликни да креираш нов процес на повик за тендери."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close Call for Tenders"
msgstr "Затвори повици на тендери"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Closed Tenders"
msgstr "Затворени тендери"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_company_id
msgid "Company"
msgstr "Компанија"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm Call"
msgstr "Потврди повик"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Confirm Order"
msgstr "Потврди Нарачка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm Purchase Order"
msgstr "Потврди налог за набавка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: selection:purchase.requisition,state:0
msgid "Confirmed"
msgstr "Потврдено"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_partner
msgid "Create Request for Quotation"
msgstr "Креирај барање за прибирање понуди"

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Create a draft purchase order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_create_uid
msgid "Created by"
msgstr "Креирано од"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_create_date
msgid "Created on"
msgstr "Креирано на"

#. module: purchase_requisition
#: code:addons/purchase_requisition/wizard/purchase_requisition_partner.py:22
#, python-format
msgid "Define product(s) you want to include in the call for tenders."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_description
msgid "Description"
msgstr "Опис"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_display_name
msgid "Display Name"
msgstr "Прикажи име"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Done"
msgstr "Завршено"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Draft"
msgstr "Нацрт"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "End Month"
msgstr "Завршен месец"

#. module: purchase_requisition
#. openerp-web
#: code:addons/purchase_requisition/static/src/xml/purchase_requisition.xml:5
#, python-format
msgid "Generate PO"
msgstr "Генерирај налог за набавка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Групирај по"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_id
msgid "ID"
msgstr "ID"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner___last_update
msgid "Last Modified on"
msgstr "Последна промена на"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_write_uid
msgid "Last Updated by"
msgstr "Последно ажурирање од"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_write_date
msgid "Last Updated on"
msgstr "Последно ажурирање на"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_procurement_order_requisition_id
msgid "Latest Requisition"
msgstr "Последно барање"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_multiple_rfq_per_supplier
msgid "Multiple RFQ per vendor"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Multiple Requisitions"
msgstr "Повеќекратни требувања"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New"
msgstr "Ново"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Order Date"
msgstr "Датум на нарачка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "PO Created"
msgstr "Креиран налог за нарачка"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_picking_type_id
msgid "Picking Type"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_procurement_order
#: model:ir.model.fields,field_description:purchase_requisition.field_product_template_purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_procurement_id
msgid "Procurement"
msgstr "Набавка"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_id
msgid "Product"
msgstr "Производ"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Product Lines"
msgstr "Ставки на производ"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_template
msgid "Product Template"
msgstr "Урнек на производ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_uom_id
msgid "Product Unit of Measure"
msgstr "ЕМ на производ"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Производи"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_po_line_ids
msgid "Products by vendor"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_ids
msgid "Products to Purchase"
msgstr "Производи кои треба да се набават"

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Propose a call for tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Purchase Order"
msgstr "Налог за набавка"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Ставка на налогот за набавка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Purchase Order Lines"
msgstr "Ставки на налогот за набавка"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_purchase_ids
msgid "Purchase Orders"
msgstr "Налози за набавка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr "Налози за набавка со барање"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "Барање за набавка"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
msgid "Purchase Requisition Line"
msgstr "Ставка на барање за набавка"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_partner
msgid "Purchase Requisition Partner"
msgstr "Партнер за барање за набавка"

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:435
#, python-format
msgid "Purchase Requisition created"
msgstr "Креирано барање за нарачка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Purchase Requisitions (exclusive)"
msgstr "Барање за нарачка (ексклузивно)"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
msgid "Purchase Tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.act_res_partner_2_purchase_order
msgid "Purchase orders"
msgstr "Налози за набавка"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_qty
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_bid_line_qty
msgid "Quantity"
msgstr "Количина"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line_quantity_tendered
msgid "Quantity Tendered"
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:201
#, python-format
msgid "RFQ created"
msgstr "Креирани барања за прибирање понуди"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Bids"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reference"
msgstr "Референца"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Request a Quotation"
msgstr "Барај понуда"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Requests for Quotation"
msgstr "Барање за прибирање понуди"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Requests for Quotation Details"
msgstr "Детали на Барање за понуда"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr "Барање"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Ресетирај до нацрт"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Responsible"
msgstr "Одговорен"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_schedule_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_schedule_date
msgid "Scheduled Date"
msgstr "Закажан датум"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_ordering_date
msgid "Scheduled Ordering Date"
msgstr "Закажан датум за нарачка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,exclusive:0
msgid "Select multiple RFQ"
msgstr "Избери повеќе барања за прибирање понуди"

#. module: purchase_requisition
#: selection:purchase.requisition,exclusive:0
msgid "Select only one RFQ (exclusive)"
msgstr "Избери само едно барање за прибирање понуди (ексклузивно)"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_exclusive
msgid ""
"Select only one RFQ (exclusive):  On the confirmation of a purchase order, "
"it cancels the remaining purchase order.\n"
"Select multiple RFQ:  It allows to have multiple purchase orders.On "
"confirmation of a purchase order it does not cancel the remaining orders"
msgstr ""
"Избери само едно барање за прибирање понуди (ексклузивно):  При потврда на "
"налогот за нарачка, го откажува остатокот од налогот за нарачка.\n"
"Избери повеќе барања за прибирање понуди:  Дозволува да имате повеќе налози "
"за нарачка. При потврда на налог за нарачка не ги откажува останатите налози "
"за нарачка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Send RFQ by Email"
msgstr "Испрати барање за прибирање понуди по е-маил"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Source"
msgstr "Извор"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_origin
msgid "Source Document"
msgstr "Изворен документ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Статус"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order_line_quantity_tendered
msgid ""
"Technical field for not loosing the initial information about the quantity "
"proposed in the tender"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_date_end
msgid "Tender Closing Deadline"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.purchase_line_tree
msgid "Tender Lines"
msgstr "Ставки на тендер"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_exclusive
msgid "Tender Selection Type"
msgstr "Вид на селекција на тендер"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr "Одредби и услови"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_schedule_date
msgid ""
"The expected and scheduled delivery date where all the products are received"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned"
msgstr "Недоделено"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned  Requisition"
msgstr "Недоделено барање"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Vendor"
msgstr "Добавувачи"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_partner_ids
msgid "Vendors"
msgstr "Добавувачи"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_warehouse_id
msgid "Warehouse"
msgstr "Магацин"

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:72
#, python-format
msgid "You can not confirm call because there is no product line."
msgstr ""
"Не сте во можност да го потврдите повикот бидејќи немате никаква ставка на "
"производ."

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:249
#, python-format
msgid "You have already generate the purchase order(s)."
msgstr "Веќе генериравте налог(и) за нарачка(и)."

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:198
#, python-format
msgid ""
"You have already one %s purchase order for this partner, you must cancel "
"this purchase order to create a new quotation."
msgstr ""
"Имате веќе еден %s налог за набавка за овој партнер, мора да го откажете "
"овој налог за набавка за да креирате понуда."

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:258
#, python-format
msgid "You have no line selected for buying."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr "на пр. PO0025"

#~ msgid "Action Needed"
#~ msgstr "Потребна е акција"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Датум на испраќање на последната порака"

#~ msgid "Followers"
#~ msgstr "Пратители"

#~ msgid "Followers (Channels)"
#~ msgstr "Пратители (Канали)"

#~ msgid "Followers (Partners)"
#~ msgstr "Пратители (Партнери)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Доколку е штиклирано, новите пораки го бараат вашето внимание."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Доколку е штиклирано, новите пораки го бараат вашето вниманите."

#~ msgid "Is Follower"
#~ msgstr "е следач"

#~ msgid "Last Message Date"
#~ msgstr "Датум на последна порака"

#~ msgid "Messages"
#~ msgstr "Пораки"

#~ msgid "Messages and communication history"
#~ msgstr "Пораки и историја на комуникација"

#~ msgid "Number of Actions"
#~ msgstr "Број на акции"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Број на пораки за кои ѝм е потребна акција"

#~ msgid "Number of unread messages"
#~ msgstr "Број на непрочитани пораки"

#~ msgid "Unread Messages"
#~ msgstr "Непрочитани Пораки"

#~ msgid "Unread Messages Counter"
#~ msgstr "Тезга на непрочитаните пораки"

#~ msgid "Website Messages"
#~ msgstr "Пораки на веб сајт"

#~ msgid "Website communication history"
#~ msgstr "Историја на веб комуникација"
