# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * point_of_sale
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-04-04 10:03+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Galician (http://www.transifex.com/odoo/odoo-9/language/gl/)\n"
"Language: gl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:642
#, python-format
msgid " REFUND"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_supplier_invoice_count
msgid "# Vendor Bills"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_nbr_lines
msgid "# of Lines"
msgstr "# de líneas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_purchase_order_count
msgid "# of Purchase Order"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1283
#, python-format
msgid "% discount"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1410
#: code:addons/point_of_sale/static/src/xml/pos.xml:1450
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "(update)"
msgstr "(actualizar)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "+ Transactions"
msgstr "+ Transaccións"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:279
#, python-format
msgid "123.14 €"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid ""
"<span class=\"o_stat_text\">Put</span>\n"
"                                <span class=\"o_stat_text\">Money In</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Set Closing Balance</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Set Opening Balance</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid ""
"<span class=\"o_stat_text\">Take</span>\n"
"                                <span class=\"o_stat_text\">Money Out</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Cash Balance</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Date</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reports</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>Company</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Ending Balance</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Journal</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Opening Date</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>Print Date</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Starting Balance</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>Starting Date</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Statement Name</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>Total</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>User</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "= Theoretical Closing Balance"
msgstr "= Saldo teórico de peche"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1885
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1171
#, python-format
msgid "A Customer Name Is Required"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_users_pos_security_pin
msgid ""
"A Security PIN used to protect sensible functionality in the Point of Sale"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "A custom receipt footer message"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "A custom receipt header message"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_sequence_number
msgid "A sequence number that is incremented with each order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which\n"
"                you sell through the point of sale. The user has to check "
"the\n"
"                currencies in your cash registers at the beginning and the "
"end\n"
"                of each session."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_sequence_number
msgid "A session-unique sequence number for the order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_receipt_header
msgid "A short text that will be inserted as a header in the printed receipt"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1449
#, python-format
msgid "ABC"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting Information"
msgstr "Información Contable"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order_sale_journal
msgid "Accounting journal used to post sales entries."
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_5
#: model:product.template,name:point_of_sale.partner_product_5_product_template
msgid "Acsone.eu"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
#: selection:pos.config,state:0
msgid "Active"
msgstr "Activo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal_journal_user
msgid "Active in Point of Sale"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1729
#, python-format
msgid "Add Tip"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_discount
msgid "Add a Global Discount"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:374
#: code:addons/point_of_sale/static/src/xml/pos.xml:452
#, python-format
msgid "Address"
msgstr "Enderezo"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr ""

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_reprint:0
msgid "Allow cashier to reprint receipts"
msgstr ""

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_discount:0
msgid "Allow discounts on order lines only"
msgstr ""

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_discount:0
msgid "Allow global discounts"
msgstr ""

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_mercury:0
msgid "Allows customers to pay with credit cards."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_settings_module_pos_discount
msgid ""
"Allows the cashier to quickly give a percentage sale discount for all the "
"sales order to a customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_settings_module_pos_loyalty
msgid ""
"Allows you to define a loyalty program in the point of sale, where the "
"customers earn loyalty points and get rewards"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_amount
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "Amount"
msgstr "Cantidade"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal_amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_name
msgid "An internal identification of the point of sale"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
msgid "Apply"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_discount
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_discount
msgid "Apply Discount"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1877
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_print_auto
msgid "Automatic Receipt Printing"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_cashdrawer
msgid "Automatically open the cashdrawer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_journal_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_journal_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Available Payment Methods"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_template_available_in_pos
msgid "Available in the Point of Sale"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_average_price
msgid "Average Price"
msgstr "Prezo medio"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.bacardi
#: model:product.template,name:point_of_sale.bacardi_product_template
msgid "Bacardi"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:267
#: code:addons/point_of_sale/static/src/xml/pos.xml:587
#, python-format
msgid "Back"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.baileys
#: model:product.template,name:point_of_sale.baileys_product_template
msgid "Baileys"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_statement_ids
msgid "Bank Statement"
msgstr "Extracto bancario"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Liña de extracto bancario"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:351
#: code:addons/point_of_sale/static/src/xml/pos.xml:393
#, python-format
msgid "Barcode"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1157
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
#, python-format
msgid "Barcode Scanner"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_barcode_nomenclature_id
msgid "Barcodes"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.beer
msgid "Beers"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
#: model:pos.category,name:point_of_sale.beverage
msgid "Beverages"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.raisins_noir
#: model:product.template,name:point_of_sale.raisins_noir_product_template
msgid "Black Grapes"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.bloody_mary
#: model:product.template,name:point_of_sale.bloody_mary_product_template
msgid "Bloody Mary"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.boni_orange
#: model:product.template,name:point_of_sale.boni_orange_product_template
msgid "Boni Oranges"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.budweiser
#: model:product.template,name:point_of_sale.budweiser_product_template
msgid "Budweiser"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:868
#, python-format
msgid "Button"
msgstr "Botón"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:781
#, python-format
msgid "CHANGE"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.caipirinha
#: model:product.template,name:point_of_sale.caipirinha_product_template
msgid "Caipirinha"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:420
#: code:addons/point_of_sale/static/src/xml/pos.xml:909
#: code:addons/point_of_sale/static/src/xml/pos.xml:926
#: code:addons/point_of_sale/static/src/xml/pos.xml:943
#: code:addons/point_of_sale/static/src/xml/pos.xml:963
#: code:addons/point_of_sale/static/src/xml/pos.xml:1015
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_discount
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: point_of_sale
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Cancelled"
msgstr "Cancelado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1866
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.captain_morgan
#: model:product.template,name:point_of_sale.captain_morgan_product_template
msgid "Captain Morgan"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.capuccino
#: model:product.template,name:point_of_sale.capuccino_product_template
msgid "Capuccino"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.carlsberg
#: model:product.template,name:point_of_sale.carlsberg_product_template
msgid "Carlsberg"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.carotte
#: model:product.template,name:point_of_sale.carotte_product_template
msgid "Carrots"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_cash_control
msgid "Cash Control"
msgstr "Control de Caixa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_journal_id
msgid "Cash Journal"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_id
msgid "Cash Register"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:106
#, python-format
msgid "Cash control can only be applied to cash journals."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_cashdrawer
msgid "Cashdrawer"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:19
#, python-format
msgid "Cashier"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:493
#, python-format
msgid "Change"
msgstr "Cambiar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:102
#, python-format
msgid "Change Cashier"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1116
#, python-format
msgid "Change Customer"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1729
#, python-format
msgid "Change Tip"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:649
#: code:addons/point_of_sale/static/src/xml/pos.xml:1339
#, python-format
msgid "Change:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_template_to_weight
msgid ""
"Check if the product should be weighted using the hardware scale integration"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_template_available_in_pos
msgid "Check if you want this product to appear in the Point of Sale"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_journal_journal_user
msgid ""
"Check this box if this journal define a payment method that can be used in a "
"point of sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_group_by
msgid ""
"Check this if you want to group the Journal Items by Product while closing a "
"Session"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1917
#, python-format
msgid "Check your internet connection and try again."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_child_id
msgid "Children Categories"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:322
#: code:addons/point_of_sale/static/src/xml/pos.xml:323
#, python-format
msgid "City"
msgstr "Cidade"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_template_action
msgid "Click to add a new product."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_account_journal_form
msgid "Click to add a payment method."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Click to create a new PoS config."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "Click to create a new order."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Click to define a new category."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid "Click to start a new session."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:18
#, python-format
msgid "Client"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:679
#: code:addons/point_of_sale/static/src/js/chrome.js:687
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Close"
msgstr "Pechar"

#. module: point_of_sale
#: selection:pos.session,state:0
msgid "Closed & Posted"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:335
#, python-format
msgid "Closing ..."
msgstr ""

#. module: point_of_sale
#: selection:pos.session,state:0
msgid "Closing Control"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_stop_at
msgid "Closing Date"
msgstr "Data límite"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.coke
#: model:product.template,name:point_of_sale.coke_product_template
msgid "Coca-Cola"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.cocktail
msgid "Cocktails"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.coffee
#: model:product.template,name:point_of_sale.coffee_product_template
msgid "Coffee"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_company_id
msgid "Company"
msgstr "Compañía"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poire_conference
#: model:product.template,name:point_of_sale.poire_conference_product_template
msgid "Conference pears"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Configuración"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
msgid "Configure Point of Sale"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid ""
"Configure at least one Point of Sale to be able to sell through the PoS "
"interface."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:684
#: code:addons/point_of_sale/static/src/xml/pos.xml:906
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:115
#, python-format
msgid "Connecting to the PosBox"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Continue Selling"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.corona
#: model:product.template,name:point_of_sale.corona_product_template
msgid "Corona"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.cosmopolitan
#: model:product.template,name:point_of_sale.cosmopolitan_product_template
msgid "Cosmopolitan"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1259
#, python-format
msgid "Could Not Read Image"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:330
#, python-format
msgid "Country"
msgstr "País"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_create_date
msgid "Created on"
msgstr "Creado o"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_create_date
msgid "Creation Date"
msgstr "Data de creación"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_mercury
msgid "Credit Cards"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.cuba_libre
#: model:product.template,name:point_of_sale.cuba_libre_product_template
msgid "Cuba Libre"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_currency_id
msgid "Currency"
msgstr "Moeda"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_current_session_id
msgid "Current Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_current_session_state
#, fuzzy
msgid "Current session state"
msgstr "Data de creación"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1740
#: code:addons/point_of_sale/static/src/xml/pos.xml:116
#: code:addons/point_of_sale/static/src/xml/pos.xml:617
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:459
#, python-format
msgid "Customer Invoice"
msgstr "Factura de Cliente"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.daiquiri
#: model:product.template,name:point_of_sale.daiquiri_product_template
msgid "Daiquiri"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_7
#: model:product.template,name:point_of_sale.partner_product_7_product_template
msgid "Datalp.com"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_date
msgid "Date Order"
msgstr "Data pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1147
#, python-format
msgid "Debug Window"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_account_id
msgid "Default Debit Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_barcode_nomenclature_id
msgid ""
"Defines what kind of barcodes are available and how they are assigned to "
"products, customers and cashiers"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_delay_validation
msgid "Delay Validation"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1167
#, python-format
msgid "Delete Paid Orders"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:261
#, python-format
msgid "Delete Paid Orders ?"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1168
#, python-format
msgid "Delete Unpaid Orders"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:271
#, python-format
msgid "Delete Unpaid Orders ?"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_property_delivery_carrier_id
msgid "Delivery Method"
msgstr "Método de entrega"

#. module: point_of_sale
#: selection:pos.config,state:0
msgid "Deprecated"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1119
#, python-format
msgid "Deselect Customer"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:53
#, python-format
msgid "Destroy Current Order ?"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_difference
msgid "Difference"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.disaronno
#: model:product.template,name:point_of_sale.disaronno_product_template
msgid "Disaronno"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:139
#, python-format
msgid "Disc"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_discount
msgid "Discount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_discount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_discount
msgid "Discount (%)"
msgstr "Desconto (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_notice
msgid "Discount Notice"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:724
#: code:addons/point_of_sale/static/src/xml/pos.xml:1312
#, python-format
msgid "Discount:"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:17
#, python-format
msgid "Discounted Product"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:790
#, python-format
msgid "Discounts"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_display_categ_images
msgid "Display Category Pictures"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_invoice_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_statement_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_usersproduct_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_display_name
msgid "Display Name"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_template_action
msgid ""
"Do not forget to set the price and the point of sale category\n"
"                in which it should appear. If a product has no point of "
"sale\n"
"                category, you can not sell it through the point of sale\n"
"                interface."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid "Do you want to open cash registers?"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Done"
msgstr "Feito"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:491
#, python-format
msgid "Due"
msgstr "Debido"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_9
#: model:product.template,name:point_of_sale.partner_product_9_product_template
msgid "EGGS-solutions.fr"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_3
#: model:product.template,name:point_of_sale.partner_product_3_product_template
msgid "Eezee-It"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.efes
#: model:product.template,name:point_of_sale.efes_product_template
msgid "Efes"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_4
#: model:product.template,name:point_of_sale.partner_product_4_product_template
msgid "Ekomurz.nl"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1150
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_electronic_scale
#, python-format
msgid "Electronic Scale"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:343
#: code:addons/point_of_sale/static/src/xml/pos.xml:378
#, python-format
msgid "Email"
msgstr "E-mail"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1837
#, python-format
msgid "Empty Order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_scan_via_proxy
msgid "Enable barcode scanning with a remotely connected barcode scanner"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_electronic_scale
msgid "Enables Electronic Scale integration"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_payment_terminal
msgid "Enables Payment Terminal integration"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_vkeyboard
msgid "Enables an integrated Virtual Keyboard"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_invoicing
msgid "Enables invoice generation from the Point of Sale"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "End of Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_balance_end_real
msgid "Ending Balance"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:13
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1188
#, python-format
msgid "Error: Could not Save Changes"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:239
#, python-format
msgid ""
"Error: The Point of Sale User must belong to the same company as the Point "
"of Sale. You are probably trying to load the point of sale as an "
"administrator in a multi-company setup, with the administrator account set "
"to the wrong company."
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.espresso
#: model:product.template,name:point_of_sale.espresso_product_template
msgid "Espresso"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1169
#, python-format
msgid "Export Paid Orders"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1170
#, python-format
msgid "Export Unpaid Orders"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.chicon_flandria_extra
#: model:product.template,name:point_of_sale.chicon_flandria_extra_product_template
msgid "Extra Flandria chicory"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Información adicional"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.fanta
#: model:product.template,name:point_of_sale.fanta_product_template
msgid "Fanta"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Features"
msgstr "Características"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1029
#, python-format
msgid "Finished Importing Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_fiscal_position_id
msgid "Fiscal Position"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_fiscal_position_ids
msgid "Fiscal Positions"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Footer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_big_scrollbars
msgid "For imprecise industrial touchscreens"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.fosters
#: model:product.template,name:point_of_sale.fosters_product_template
msgid "Foster's"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.fruits_vegetables
msgid "Fruits and Vegetables"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "General Information"
msgstr "Información xeral"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category_sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pomme_golden_perlim
#: model:product.template,name:point_of_sale.pomme_golden_perlim_product_template
msgid "Golden Apples Perlim"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.gordon
#: model:product.template,name:point_of_sale.gordon_product_template
msgid "Gordon"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pomme_granny_smith
#: model:product.template,name:point_of_sale.pomme_granny_smith_product_template
msgid "Granny Smith apples"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poivron_verts
#: model:product.template,name:point_of_sale.poivron_verts_product_template
msgid "Green Peppers"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.green_tea
#: model:product.template,name:point_of_sale.green_tea_product_template
msgid "Green Tea"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Agrupar por"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_group_by
msgid "Group Journal Items"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.guinness
#: model:product.template,name:point_of_sale.guinness_product_template
msgid "Guinness"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1181
#, python-format
msgid "Hardware Events"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Hardware Proxy / PosBox"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1177
#, python-format
msgid "Hardware Status"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_control
msgid "Has Cash Control"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Header"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.hot
msgid "Hot Drinks"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_invoice_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_statement_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_usersproduct_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/popups.js:113
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_proxy_ip
msgid "IP Address"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.ice_tea
#: model:product.template,name:point_of_sale.ice_tea_product_template
msgid "Ice Tea"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"If you put a photo on the category, the layout of the\n"
"                touchscreen interface will automatically. We suggest not to "
"put\n"
"                a photo on categories for small (1024x768) screens."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_image
msgid "Image"
msgstr "Imaxe"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.alcohol
msgid "Import Drinks"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1171
#, python-format
msgid "Import Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_data_drinks
msgid "Import common drinks data"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
msgid "Importable Point of Sales Data"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.tomate_en_grappe
#: model:product.template,name:point_of_sale.tomate_en_grappe_product_template
msgid "In Cluster Tomatoes"
msgstr ""

#. module: point_of_sale
#: selection:pos.session,state:0
msgid "In Progress"
msgstr "En curso"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:406
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
#: selection:pos.config,state:0
msgid "Inactive"
msgstr "Inactivo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_tax_included
msgid "Include Taxes in Prices"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:266
#, python-format
msgid "Incorrect Password"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_note
msgid "Internal Notes"
msgstr "Notas internas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:625
#: model:ir.actions.report.xml,name:point_of_sale.pos_invoice_report
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_invoice_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "Factura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Invoiced"
msgstr "Facturado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_invoicing
msgid "Invoicing"
msgstr "Facturando"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.irish_coffee
#: model:product.template,name:point_of_sale.irish_coffee_product_template
msgid "Irish Coffee"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_bank_statement_account_id
msgid "It acts as a default account for debit amount"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.jack_daniels
#: model:product.template,name:point_of_sale.jack_daniels_product_template
msgid "Jack Daniel's"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.jim_beam
#: model:product.template,name:point_of_sale.jim_beam_product_template
msgid "Jim Beam"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pomme_jonagold
#: model:product.template,name:point_of_sale.pomme_jonagold_product_template
msgid "Jonagold apples"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_journal_id
msgid "Journal"
msgstr "Diario"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_account_move
msgid "Journal Entry"
msgstr "Entrada de Diario"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Journals"
msgstr "Diarios"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_big_scrollbars
msgid "Large Scrollbars"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_invoice___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_statement___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_usersproduct___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order___last_update
msgid "Last Modified on"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_last_session_closing_cash
msgid "Last session closing cash"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_last_session_closing_date
msgid "Last session closing date"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poireaux_poireaux
#: model:product.template,name:point_of_sale.poireaux_poireaux_product_template
msgid "Leeks"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.citron
#: model:product.template,name:point_of_sale.citron_product_template
msgid "Lemon"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_name
msgid "Line No"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Lines of Point of Sale"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:33
#, python-format
msgid "List of Cash Registers"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:484
#: code:addons/point_of_sale/static/src/xml/pos.xml:38
#, python-format
msgid "Loading"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_location_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_location_id
msgid "Location"
msgstr "Lugar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_login_number
msgid "Login Sequence Number"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:293
#, python-format
msgid "Login as a Manager"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_loyalty
msgid "Loyalty Program"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Realizar pagamento"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.malibu
#: model:product.template,name:point_of_sale.malibu_product_template
msgid "Malibu"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
msgid "Manage loyalty program with points and rewards for customers"
msgstr ""

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Manager"
msgstr "Xestor"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.manhattan
#: model:product.template,name:point_of_sale.manhattan_product_template
msgid "Manhattan"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.margarita
#: model:product.template,name:point_of_sale.margarita_product_template
msgid "Margarita"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.martini
#: model:product.product,name:point_of_sale.martini_cocktail
#: model:product.template,name:point_of_sale.martini_cocktail_product_template
#: model:product.template,name:point_of_sale.martini_product_template
msgid "Martini"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_image_medium
msgid "Medium-sized image"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category_image_medium
msgid ""
"Medium-sized image of the category. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:494
#, python-format
msgid "Method"
msgstr "Método"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_banana
#: model:product.template,name:point_of_sale.milkshake_banana_product_template
msgid "Milkshake Banana"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_cherry
#: model:product.template,name:point_of_sale.milkshake_cherry_product_template
msgid "Milkshake Cherry"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_chocolate
#: model:product.template,name:point_of_sale.milkshake_chocolate_product_template
msgid "Milkshake Chocolate"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_strawberry
#: model:product.template,name:point_of_sale.milkshake_strawberry_product_template
msgid "Milkshake Strawberry"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_vanilla
#: model:product.template,name:point_of_sale.milkshake_vanilla_product_template
msgid "Milkshake Vanilla"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.milkshake
msgid "Milkshakes"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.miller
#: model:product.template,name:point_of_sale.miller_product_template
msgid "Miller"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.minute_maid
#: model:product.template,name:point_of_sale.minute_maid_product_template
msgid "Minute Maid"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_consumable
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
msgid "Miscellaneous"
msgstr "Diversos"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.mojito
#: model:product.template,name:point_of_sale.mojito_product_template
msgid "Mojito"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Month of order date"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "More <i class=\"fa fa-caret-down\"/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "My Sales"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:387
#: code:addons/point_of_sale/static/src/xml/pos.xml:398
#: code:addons/point_of_sale/static/src/xml/pos.xml:407
#, python-format
msgid "N/A"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:310
#: code:addons/point_of_sale/static/src/xml/pos.xml:451
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#, python-format
msgid "Name"
msgstr "Nome"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1847
#, python-format
msgid "Negative Bank Payment"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.nescafe
#: model:product.template,name:point_of_sale.nescafe_product_template
msgid "Nescafe"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "New"
msgstr "Novo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:650
#, python-format
msgid "Next Order"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:134
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr ""

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_mercury:0
msgid "No credit card"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/report/pos_invoice.py:25
#, python-format
msgid "No link to an invoice for %s."
msgstr ""

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_reprint:0
msgid "No reprint"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:24
#, python-format
msgid "No sequence defined on the journal"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1962
#: code:addons/point_of_sale/static/src/xml/pos.xml:332
#, python-format
msgid "None"
msgstr "Ningún"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"Note that you may use the menu <i>Your Session</i>\n"
"                to quickly open a new session."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Notes"
msgstr "Notas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_nb_print
msgid "Number of Print"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.index
msgid "Odoo POS"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:388
#, python-format
msgid "Offline"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:317
#, python-format
msgid "Offline Orders"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:838
#: code:addons/point_of_sale/static/src/xml/pos.xml:852
#: code:addons/point_of_sale/static/src/xml/pos.xml:866
#: code:addons/point_of_sale/static/src/xml/pos.xml:892
#: code:addons/point_of_sale/static/src/xml/pos.xml:923
#: code:addons/point_of_sale/static/src/xml/pos.xml:940
#: code:addons/point_of_sale/static/src/xml/pos.xml:1018
#: code:addons/point_of_sale/static/src/xml/pos.xml:1054
#, python-format
msgid "Ok"
msgstr "Aceptar"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.Onions
#: model:product.template,name:point_of_sale.Onions_product_template
msgid "Onions"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1245
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Open"
msgstr "Abrir"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_open_statement
msgid "Open Cash Register"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:636
#: code:addons/point_of_sale/static/src/xml/pos.xml:1183
#, python-format
msgid "Open Cashbox"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Open POS Menu"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid "Open Registers"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Open Session"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.act_pos_open_statement
#: model:ir.model,name:point_of_sale.model_pos_open_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid "Open Statements"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Opening Balance"
msgstr ""

#. module: point_of_sale
#: selection:pos.session,state:0
msgid "Opening Control"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_start_at
msgid "Opening Date"
msgstr "Data de apertura"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.papillon_orange
#: model:product.template,name:point_of_sale.papillon_orange_product_template
msgid "Orange Butterfly"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:282
#, python-format
msgid "Order"
msgstr "Pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:1515
#: code:addons/point_of_sale/static/src/js/models.js:1546
#, python-format
msgid "Order "
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_date_order
msgid "Order Date"
msgstr "Data do pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_sequence_id
msgid "Order IDs Sequence"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_lines
msgid "Order Lines"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Month"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_name
msgid "Order Ref"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_sequence_number
msgid "Order Sequence Number"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1164
#: model:ir.actions.act_window,name:point_of_sale.act_pos_session_orders
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:180
#, python-format
msgid "POS Order %s"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:191
#, python-format
msgid "POS order line %s"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "POS ordered created during current year"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line_pos_statement_id
msgid "POS statement"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_amount_paid
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Paid"
msgstr "Pagado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_parent_id
msgid "Parent Category"
msgstr "Categoría Pai"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_partner
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "Partner"
msgstr "Empresa"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.partner_services
msgid "Partner Services"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:263
#, python-format
msgid "Password ?"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:121
#: code:addons/point_of_sale/static/src/xml/pos.xml:590
#: code:addons/point_of_sale/wizard/pos_payment.py:51
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "Pagamento"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_payment_date
msgid "Payment Date"
msgstr "Data de pagamento"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_account_journal_form
#: model:ir.ui.menu,name:point_of_sale.menu_action_account_journal_form_open
msgid "Payment Methods"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_journal_id
msgid "Payment Mode"
msgstr "Modo de pagamento"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_payment_name
msgid "Payment Reference"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_payment_terminal
msgid "Payment Terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_account_journal_form
msgid ""
"Payment methods are defined by accounting journals having the\n"
"                field <i>PoS Payment Method</i> checked. In order to be "
"useable\n"
"                from the touchscreen interface, you must set the payment "
"method\n"
"                on the <i>Point of Sale</i> configuration."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_statement_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Payments"
msgstr "Pagos"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.peche
#: model:product.template,name:point_of_sale.peche_product_template
msgid "Peaches"
msgstr ""

#. module: point_of_sale
#: model:ir.filters,name:point_of_sale.filter_orders_per_session
msgid "Per session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:347
#: code:addons/point_of_sale/static/src/xml/pos.xml:382
#: code:addons/point_of_sale/static/src/xml/pos.xml:453
#, python-format
msgid "Phone"
msgstr "Teléfono"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1261
#, python-format
msgid "Phone:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_picking_id
msgid "Picking"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_picking_type_id
msgid "Picking Type"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pina_colada
#: model:product.template,name:point_of_sale.pina_colada_product_template
msgid "Pina Colada"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1876
#, python-format
msgid "Please Confirm Large Amount"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:272
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:437
#, python-format
msgid "Please provide a partner for the sale."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:474
#, python-format
msgid "Please select a payment method."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1908
#, python-format
msgid "Please select the Customer"
msgstr ""

#. module: point_of_sale
#: model:stock.picking.type,name:point_of_sale.picking_type_posout
msgid "PoS Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_pos
#: model:ir.model,name:point_of_sale.model_pos_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model:ir.ui.menu,name:point_of_sale.menu_pos_config_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_users_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
msgid "Point of Sale"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_template_pos_categ_id
msgid "Point of Sale Category"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_name
msgid "Point of Sale Name"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Statistics"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Payment"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_group_pos_user_id
msgid "Point of Sale User Group"
msgstr ""

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_restaurant:0
msgid "Point of sale for shops"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_product_pos_category
msgid "Pos Product Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_pos_session_username
msgid "Pos session username"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:326
#, python-format
msgid "Postcode"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Posted"
msgstr "Contabilizado"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pomme_de_terre
#: model:product.template,name:point_of_sale.pomme_de_terre_product_template
msgid "Potatoes"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_precompute_cash
msgid "Prefill Cash Payment"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:144
#, python-format
msgid "Price"
msgstr "Prezo"

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:16
#, python-format
msgid "Priced Product"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_pricelist_id
msgid "Pricelist"
msgstr "Lista de prezos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:657
#: code:addons/point_of_sale/static/src/xml/pos.xml:1184
#, python-format
msgid "Print Receipt"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_print_via_proxy
msgid "Print via Proxy"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:377
#, python-format
msgid "Printer"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/devices.js:425
#, python-format
msgid "Printing Error: "
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "Produto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Categoría de Producto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_product_qty
msgid "Product Quantity"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_product_tmpl_id
msgid "Product Template"
msgstr "Modelo de Producto"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Produtos"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_pos_categ_id
msgid "Public Category"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_in
msgid "Put Money In"
msgstr "Poñer diñeiro"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:134
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
#, python-format
msgid "Qty"
msgstr "Cantidade"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_qty
msgid "Quantity"
msgstr "Cantidade"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Re-Print"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1185
#, python-format
msgid "Read Weighting Scale"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Real Closing Balance"
msgstr "Saldo de peche real"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Receipt"
msgstr "Recibo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_receipt_footer
msgid "Receipt Footer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_receipt_header
msgid "Receipt Header"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Receipt Printer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_pos_reference
msgid "Receipt Ref"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.red_label
#: model:product.template,name:point_of_sale.red_label_product_template
msgid "Red Label"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poivron_rouges
#: model:product.template,name:point_of_sale.poivron_rouges_product_template
msgid "Red Pepper"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.red_wine
#: model:product.template,name:point_of_sale.red_wine_product_template
msgid "Red Wine"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pamplemousse_rouge_pamplemousse
#: model:product.template,name:point_of_sale.pamplemousse_rouge_pamplemousse_product_template
msgid "Red grapefruit"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.redbull
#: model:product.template,name:point_of_sale.redbull_product_template
msgid "RedBull"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reports"
msgstr "Informes"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_reprint
msgid "Reprints"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1154
#, python-format
msgid "Reset"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_user_id
msgid "Responsible"
msgstr "Responsable"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_restaurant
msgid "Restaurant"
msgstr ""

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_restaurant:0
msgid "Restaurant: activate table management"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Resume"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:652
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_amount_return
msgid "Returned"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.rose_wine
#: model:product.template,name:point_of_sale.rose_wine_product_template
msgid "Rose Wine"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.safari
#: model:product.template,name:point_of_sale.safari_product_template
msgid "Safari"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_sale_journal
msgid "Sale Journal"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sales Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Salesman"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Salesperson"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:385
#, python-format
msgid "Scale"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1160
#, python-format
msgid "Scan"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1161
#, python-format
msgid "Scan EAN-13"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:368
#, python-format
msgid "Scanner"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.schweppes
#: model:product.template,name:point_of_sale.schweppes_product_template
msgid "Schweppes"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:424
#, python-format
msgid "Search Customers"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:191
#, python-format
msgid "Search Products"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_pos_security_pin
msgid "Security PIN"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_users.py:15
#, python-format
msgid "Security PIN can only contain digits"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:432
#, python-format
msgid "Select Customer"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:237
#, python-format
msgid "Select User"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1973
#, python-format
msgid "Select tax"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:202
#, python-format
msgid "Selected orders do not have the same session!"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_sequence_number
msgid "Sequence Number"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:704
#, python-format
msgid "Served by"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1921
#, python-format
msgid "Server Error"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:249
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_name
msgid "Session ID"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1043
#, python-format
msgid "Session ids:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Session:"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.act_pos_config_sessions
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1114
#, python-format
msgid "Set Customer"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1153
#, python-format
msgid "Set Weight"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Set to Active"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Set to Deprecated"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Set to Inactive"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Settings"
msgstr "Configuración"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1263
#, python-format
msgid "Shop:"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:42
#, python-format
msgid "Skip"
msgstr "Pasar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_print_skip_screen
msgid "Skip Receipt Screen"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_image_small
msgid "Small-sized image"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category_image_small
msgid ""
"Small-sized image of the category. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.smirnoff
#: model:product.template,name:point_of_sale.smirnoff_product_template
msgid "Smirnoff"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.soft
msgid "Soft Drinks"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:322
#, python-format
msgid "Some orders could not be submitted to"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.sprite
#: model:product.template,name:point_of_sale.sprite_product_template
msgid "Sprite"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_start_categ_id
msgid "Start Category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_balance_start
msgid "Starting Balance"
msgstr "Saldo inicial"

#. module: point_of_sale
#: model:ir.actions.report.xml,name:point_of_sale.action_report_account_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "Statement"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Statement lines"
msgstr "Liñas de extracto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Statements"
msgstr "Estados"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Status"
msgstr "Estado"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.stella_artois
#: model:product.template,name:point_of_sale.stella_artois_product_template
msgid "Stella Artois"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_stock_location_id
msgid "Stock Location"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:318
#: code:addons/point_of_sale/static/src/xml/pos.xml:319
#, python-format
msgid "Street"
msgstr "Rúa"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.limon
#: model:product.template,name:point_of_sale.limon_product_template
msgid "Stringers"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:752
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_price_subtotal_incl
#, python-format
msgid "Subtotal"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_price_subtotal
msgid "Subtotal w/o Tax"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_price_sub_total
msgid "Subtotal w/o discount"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1298
#, python-format
msgid "Subtotal:"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1035
#, python-format
msgid "Successfully  imported"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1034
#, python-format
msgid "Successfully imported"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_balance_end
msgid "Sum of opening balance and transactions."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Summary by Payment Methods"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_property_purchase_currency_id
msgid "Supplier Currency"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:765
#, python-format
msgid "TOTAL"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_out
msgid "Take Money Out"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_order.py:300
#: code:addons/point_of_sale/static/src/js/screens.js:1983
#, python-format
msgid "Tax"
msgstr "Imposto"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:355
#: code:addons/point_of_sale/static/src/xml/pos.xml:402
#, python-format
msgid "Tax ID"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_tax_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_tax_ids_after_fiscal_position
msgid "Taxes"
msgstr "Impostos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1133
#, python-format
msgid "Taxes:"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:684
#, python-format
msgid "Tel:"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:492
#, python-format
msgid "Tendered"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.tequila
#: model:product.template,name:point_of_sale.tequila_product_template
msgid "Tequila"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:260
#, python-format
msgid "The POS order must have lines when calling this method"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:887
#, python-format
msgid ""
"The Point of Sale could not find any product, client, employee\n"
"                    or action associated with the scanned barcode."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:150
#, python-format
msgid ""
"The company of a payment method is different than the one of point of sale"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:145
#, python-format
msgid ""
"The company of the sale journal is different than the one of point of sale"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:140
#, python-format
msgid ""
"The company of the stock location is different than the one of point of sale"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:155
#, python-format
msgid ""
"The default fiscal position must be included in the available fiscal "
"positions of the point of sale"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_tax_included
msgid ""
"The displayed prices will always include all taxes, even if the taxes have "
"been setup differently"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:93
#: sql_constraint:pos.session:0
#, python-format
msgid "The name of this POS Session must be unique !"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1916
#, python-format
msgid "The order could not be sent"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1927
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_precompute_cash
msgid ""
"The payment input will behave similarily to bank payment input, and will be "
"prefilled with the exact due amount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_config_id
#: model:ir.model.fields,help:point_of_sale.field_pos_session_config_id
msgid "The physical point of sale you will use."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_display_categ_images
msgid "The product categories will be displayed with pictures."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_tip_product_id
msgid ""
"The product used to encode the customer tip. Leave empty if you do not "
"accept tips."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1260
#, python-format
msgid "The provided file could not be read due to an unknown error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_print_auto
msgid "The receipt will automatically be p-rinted at the end of each order"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1922
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid ""
"The system will open all cash registers, so that you can start recording "
"payments. We suggest you to control the opening balance of each register, "
"using their CashBox tab."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_settings_module_pos_mercury
msgid "The transactions are processed by MercuryPay"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:215
#, python-format
msgid "The type of the journal for your payment method should be bank or cash "
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:94
#, python-format
msgid ""
"There are pending operations that could not be saved into the database, are "
"you sure you want to exit?"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1867
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle "
"the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of "
"sale configuration"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_box.py:21
#, python-format
msgid "There is no cash register for this PoS Session"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:607
#, python-format
msgid ""
"There is no receivable account defined to make payment for the partner: \"%s"
"\" (id:%d)."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:605
#, python-format
msgid "There is no receivable account defined to make payment."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1838
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_users_property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_users_property_delivery_carrier_id
msgid "This delivery method will be used when invoicing from picking."
msgstr ""
"Este método de entrega utilizarase cando se facture a partir do albará."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_journal_amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance "
"and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category_image
msgid ""
"This field holds the image used as image for the cateogry, limited to "
"1024x1024px."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_settings_module_pos_restaurant
msgid ""
"This module adds several restaurant features to the Point of Sale: \n"
"\n"
"- Bill Printing: Allows you to print a receipt before the order is paid \n"
"\n"
"- Bill Splitting: Allows you to split an order into different orders \n"
"\n"
"- Kitchen Order Printing: allows you to print orders updates to kitchen or "
"bar printers"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:262
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:272
#, python-format
msgid ""
"This operation will permanently destroy all unpaid orders from all sessions "
"that have been put in the local storage. You will lose all the data and exit "
"the point of sale. This operation cannot be undone."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_template_pos_categ_id
msgid "Those categories are used to group similar products for point of sale."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:631
#, python-format
msgid "Tip"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_tip_product_id
msgid "Tip Product"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_template_to_weight
msgid "To Weigh With Scale"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:638
#, python-format
msgid ""
"To return product(s), you need to open a session that will be used to "
"register the refund."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Today"
msgstr "Hoxe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_amount_total
msgid "Total"
msgstr "Total"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_total_discount
msgid "Total Discount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_price_total
msgid "Total Price"
msgstr "Prezo total"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:802
#, python-format
msgid "Total Taxes"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_total_entry_encoding
msgid "Total of all paid sale orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_balance_end_real
msgid "Total of closing cash control lines."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_balance_start
msgid "Total of opening cash control lines."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1132
#: code:addons/point_of_sale/static/src/xml/pos.xml:1318
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:312
#, python-format
msgid "Trade Receivables"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.tuborg
#: model:product.template,name:point_of_sale.tuborg_product_template
msgid "Tuborg"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:134
#, python-format
msgid ""
"Unable to open the session. You have to assign a sale journal to your point "
"of sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_price_unit
msgid "Unit Price"
msgstr "Prezo unidade"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:883
#, python-format
msgid "Unknown Barcode"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1926
#, python-format
msgid "Unknown Error"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1244
#, python-format
msgid "Unsupported File Format"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid ""
"Use this menu to browse previous orders. To record new\n"
"                orders, you may use the menu <i>Your Session</i> for\n"
"                the touchscreen interface."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model:res.groups,name:point_of_sale.group_pos_user
msgid "User"
msgstr "Usuario"

#. module: point_of_sale
#: model:ir.actions.report.xml,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.report.xml,name:point_of_sale.action_report_pos_users_product
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "User's Product"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1262
#, python-format
msgid "User:"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_uuid
msgid "Uuid"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:687
#, python-format
msgid "VAT:"
msgstr "IVE:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:591
#, python-format
msgid "Validate"
msgstr "Validar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Validate Closing & Post Entries"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_6
#: model:product.template,name:point_of_sale.partner_product_6_product_template
msgid "Vauxoo.com"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_vkeyboard
msgid "Virtual KeyBoard"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_stock_location_id
msgid "Warehouse"
msgstr "Almacén"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.water
#: model:product.template,name:point_of_sale.water_product_template
msgid "Water"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:15
#, python-format
msgid "Weighted Product"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1179
#, python-format
msgid "Weighting"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.white_russian
#: model:product.template,name:point_of_sale.white_russian_product_template
msgid "White Russian"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.white_wine
#: model:product.template,name:point_of_sale.white_wine_product_template
msgid "White Wine"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.wine
msgid "Wine"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1105
#: code:addons/point_of_sale/static/src/xml/pos.xml:1282
#, python-format
msgid "With a"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Year"
msgstr "Ano"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poivron_jaunes
#: model:product.template,name:point_of_sale.poivron_jaunes_product_template
msgid "Yellow Peppers"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.tea
#: model:product.template,name:point_of_sale.tea_product_template
msgid "Yellow Tea"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid ""
"You can define another list of available currencies on the\n"
"                                <i>Cash Registers</i> tab of the"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:396
#, python-format
msgid ""
"You cannot change the partner of a POS order for which an invoice has "
"already been issued."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:25
#, python-format
msgid ""
"You cannot confirm all orders of this session, because they have not the "
"'paid' status"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:117
#, python-format
msgid ""
"You cannot create two active sessions related to the same point of sale!"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:112
#, python-format
msgid "You cannot create two active sessions with the same responsible!"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/product.py:21
#, python-format
msgid ""
"You cannot delete a product saleable in point of sale while a session is "
"still opened."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1848
#, python-format
msgid ""
"You cannot have a negative amount in a Bank payment. Use a cash payment "
"method to return money to the customer."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:231
#, python-format
msgid ""
"You cannot use the session of another users. This session is owned by %s. "
"Please first close this one to use this point of sale."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:18
#, python-format
msgid ""
"You have to define which payment method must be available in the point of "
"sale by reusing existing bank and cash through \"Accounting / "
"Configuration / Journals / Journals\". Select a journal and check the field "
"\"PoS Payment Method\" from the \"Point of Sale\" tab. You can also create "
"new payment methods directly from menu \"PoS Backend / Configuration / "
"Payment Methods\"."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:621
#, python-format
msgid "You have to open at least one cashbox."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:724
#, python-format
msgid "You have to select a pricelist in the sale form !"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:712
#, python-format
msgid ""
"You have to select a pricelist in the sale form !\n"
"Please set one before choosing a product."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_template_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1909
#, python-format
msgid "You need to select the customer before you can invoice an order."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:123
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:54
#, python-format
msgid "You will lose any data associated with the current order"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1189
#, python-format
msgid "Your Internet connection is probably down."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:213
#, python-format
msgid ""
"Your ending balance is too different from the theoretical cash closing "
"(%.2f), the maximum allowed is: %.2f. You can contact your manager to force "
"it."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1124
#, python-format
msgid "Your shopping cart is empty"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:327
#, python-format
msgid "ZIP"
msgstr "C.P."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.courgette
#: model:product.template,name:point_of_sale.courgette_product_template
msgid "Zucchini"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1097
#, python-format
msgid "at"
msgstr "ás"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "barcode.rule"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1040
#, python-format
msgid "belong to another session:"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_8
#: model:product.template,name:point_of_sale.partner_product_8_product_template
msgid "camptocamp.com"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1385
#, python-format
msgid "caps lock"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_in
msgid "cash.box.in"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_out
msgid "cash.box.out"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1412
#: code:addons/point_of_sale/static/src/xml/pos.xml:1454
#, python-format
msgid "close"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1370
#: code:addons/point_of_sale/static/src/xml/pos.xml:1447
#, python-format
msgid "delete"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1107
#, python-format
msgid "discount"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1881
#, python-format
msgid "for an order of"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:171
#, python-format
msgid "not used"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1034
#, python-format
msgid "paid orders"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, fuzzy
msgid "payment method."
msgstr "Modo de pagamento"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
msgid "pos.config"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config_settings
msgid "pos.config.settings"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
msgid "pos.session"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "report.point_of_sale.report_invoice"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_statement
msgid "report.point_of_sale.report_statement"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_usersproduct
msgid "report.point_of_sale.report_usersproduct"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_order.py:139
#: code:addons/point_of_sale/static/src/xml/pos.xml:1397
#: code:addons/point_of_sale/static/src/xml/pos.xml:1452
#, python-format
msgid "return"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1398
#: code:addons/point_of_sale/static/src/xml/pos.xml:1409
#, python-format
msgid "shift"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1371
#, python-format
msgid "tab"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1035
#, python-format
msgid "unpaid orders"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1037
#, python-format
msgid "unpaid orders could not be imported"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1039
#, python-format
msgid "were duplicates of existing orders"
msgstr ""

#~ msgid "Account"
#~ msgstr "Conta"

#~ msgid "Alias Domain"
#~ msgstr "Dominio de alias"

#~ msgid "Date"
#~ msgstr "Data"

#~ msgid "Dates"
#~ msgstr "Datas"

#~ msgid "Description"
#~ msgstr "Descrición"

#~ msgid "Error!"
#~ msgstr "Erro!"

#~ msgid "Generate Entries"
#~ msgstr "Xerar asentos"

#~ msgid ""
#~ "Gives the probability to assign a lead to this partner. (0 means no "
#~ "assignation.)"
#~ msgstr ""
#~ "Indica a probabilidade de asignar unha iniciativa a esta empresa. (0 "
#~ "significa ningunha asignación)"

#~ msgid "Invoices"
#~ msgstr "Facturas"

#~ msgid "Level"
#~ msgstr "Nivel"

#~ msgid "Reference"
#~ msgstr "Referencia"

#~ msgid "Sales Lines"
#~ msgstr "Liñas vendas"

#~ msgid "unknown"
#~ msgstr "descoñecido"
