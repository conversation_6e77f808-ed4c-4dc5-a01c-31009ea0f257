<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="14.423" height="11.442" x="0" y="0"/>
    <linearGradient id="linearGradient-3" x1="72.875%" x2="40.332%" y1="46.435%" y2="33.916%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-4" x1="88.517%" x2="50%" y1="39.246%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-5" width="15.385" height="11.442" x="0" y="0"/>
    <linearGradient id="linearGradient-7" x1="72.875%" x2="40.332%" y1="46.866%" y2="35.864%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-8" x1="88.517%" x2="50%" y1="40.548%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-9" width="14.423" height="11.442" x="0" y="0"/>
    <path id="path-11" d="M15 16v2H0v-2h15zm19 0v2H19v-2h15zm19 0v2H38v-2h15z"/>
    <filter id="filter-12" width="101.9%" height="200%" x="-.9%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-13" d="M33 28v1H19v-1h14zm-3-3v1H19v-1h11zm3-3v1H19v-1h14z"/>
    <filter id="filter-14" width="107.1%" height="128.6%" x="-3.6%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <path id="path-15" d="M52 28v1H38v-1h14zm-3-3v1H38v-1h11zm3-3v1H38v-1h14z"/>
    <filter id="filter-16" width="107.1%" height="128.6%" x="-3.6%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <path id="path-17" d="M14 28v1H0v-1h14zm-3-3v1H0v-1h11zm3-3v1H0v-1h14z"/>
    <filter id="filter-18" width="107.1%" height="128.6%" x="-3.6%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_three_columns">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 16)">
        <g class="image_1_border">
          <rect width="15" height="12" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.288 .28)">
            <mask id="mask-2" fill="#fff">
              <use xlink:href="#path-1"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
            <ellipse cx="11.106" cy="2.93" fill="#F3EC60" class="oval" mask="url(#mask-2)" rx="2.163" ry="2.093"/>
            <ellipse cx="14.567" cy="12.558" fill="url(#linearGradient-3)" class="oval" mask="url(#mask-2)" rx="6.779" ry="4.186"/>
            <ellipse cx=".144" cy="12.698" fill="url(#linearGradient-4)" class="oval" mask="url(#mask-2)" rx="10.817" ry="6.558"/>
          </g>
          <path fill="#FFF" d="M15 0v12H0V0h15zm-1 1H1v10h13V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(18)">
          <rect width="16" height="12" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.308 .28)">
            <mask id="mask-6" fill="#fff">
              <use xlink:href="#path-5"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
            <ellipse cx="11.846" cy="2.93" fill="#F3EC60" class="oval" mask="url(#mask-6)" rx="2.308" ry="2.093"/>
            <ellipse cx="15.538" cy="12.558" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-6)" rx="7.231" ry="4.186"/>
            <ellipse cx=".154" cy="12.698" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-6)" rx="11.538" ry="6.558"/>
          </g>
          <path fill="#FFF" d="M16 0v12H0V0h16zm-1 1H1v10h14V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(38)">
          <rect width="15" height="12" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.288 .28)">
            <mask id="mask-10" fill="#fff">
              <use xlink:href="#path-9"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-9"/>
            <ellipse cx="11.106" cy="2.93" fill="#F3EC60" class="oval" mask="url(#mask-10)" rx="2.163" ry="2.093"/>
            <ellipse cx="14.567" cy="12.558" fill="url(#linearGradient-3)" class="oval" mask="url(#mask-10)" rx="6.779" ry="4.186"/>
            <ellipse cx=".144" cy="12.698" fill="url(#linearGradient-4)" class="oval" mask="url(#mask-10)" rx="10.817" ry="6.558"/>
          </g>
          <path fill="#FFF" d="M15 0v12H0V0h15zm-1 1H1v10h13V1z" class="rectangle_2"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-12)" xlink:href="#path-11"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-11"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-14)" xlink:href="#path-13"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-13"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-16)" xlink:href="#path-15"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-15"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-18)" xlink:href="#path-17"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-17"/>
        </g>
      </g>
    </g>
  </g>
</svg>
