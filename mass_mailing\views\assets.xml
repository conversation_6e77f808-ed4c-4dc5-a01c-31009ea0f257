<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="iframe_css_assets_edit" groups="base.group_user">
        <t t-call-assets="mass_mailing.assets_mail_themes" t-js="false"/>
        <t t-call="mass_mailing.mass_mailing_mail_style"/>
        <t t-call-assets="mass_mailing.assets_mail_themes_edition" t-js="false"/>
        <!-- To view the body_arch field in readonly and have it display exactly
        like in edit, load all the same css. TODO: move all this and the above
        to a separate asset -->
        <t t-call-assets="web.assets_common" t-js="false"/>
        <t t-call-assets="web.assets_frontend" t-js="false"/>
        <t t-call-assets="web_editor.assets_wysiwyg" t-js="false"/>
    </template>

    <template id="iframe_css_assets_readonly" groups="base.group_user">
        <link rel="stylesheet" type="text/scss" href="/mass_mailing/static/src/css/basic_theme_readonly.css"/>
        <t t-call="mass_mailing.mass_mailing_mail_style"/>
    </template>

    <template id="mass_mailing_mail_style">
        <style>
            .o_layout * {
                box-sizing: border-box !important;
            }
            .o_layout :not(.fa) {
                font-family: Arial, sans-serif !important;
            }
            /* Remove space around the email design. */
                html,
                body {
                    margin: 0 auto !important;
                    padding: 0 !important;
                    height: 100% !important;
                    width: 100% !important;
                }

                /* Stop Outlook resizing small text. */
                * {
                    -ms-text-size-adjust: 100%;
                }

                /* Stop Outlook from adding extra spacing to tables. */
                table,
                td {
                    mso-table-lspace: 0pt !important;
                    mso-table-rspace: 0pt !important;
                }

                /* Use a better rendering method when resizing images in Outlook IE. */
                img {
                    -ms-interpolation-mode:bicubic;
                }

                /* Prevent Windows 10 Mail from underlining links. Styles for underlined links should be inline. */
                a {
                    text-decoration: none;
                }
            @media screen and (max-width: 768px) {
                .o_mail_table_styles {
                    width: 100% !important;
                }
            }
        </style>
    </template>

    </odoo>
