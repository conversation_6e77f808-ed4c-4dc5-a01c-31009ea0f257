# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_quotation_builder
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is put on the quote."
msgstr ""
":\n"
"Deze inhoud is alleen zichtbaar op de offerte als dit\n"
"product op de offerte wordt opgenomen."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is used in the quote."
msgstr ""
":\n"
"Deze inhoud is alleen zichtbaar op de offerte als dit\n"
"product wordt gebruikt op de offerte."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid ""
":\n"
"                        the content below will disappear if this\n"
"                        product is removed from the quote."
msgstr ""
":\n"
"de onderstaande inhoud zal verdwijnen als dit\n"
"product wordt verwijderd uit de offerte."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"<strong>Template Header:</strong> this content\n"
"                                    will appear on all quotations using this\n"
"                                    template."
msgstr ""
"<strong>Sjabloonkop:</strong> deze inhoud\n"
"verschijnt op alle offertes die het sjabloon\n"
"gebruiken."

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "About us"
msgstr "Over ons"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "An awesome"
msgstr "Een geweldige"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"As a leading professional services firm,\n"
"                                we know that success is all about the\n"
"                                commitment we put on strong services."
msgstr ""
"Als een toonaangevend professionele diensten bedrijf,\n"
"weten we dat het succes sterk afhangt van de\n"
"toeweiding die we hebben voor onze sterke diensten."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid "Close"
msgstr "Sluiten"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Design Template"
msgstr "Ontwerp sjabloon"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Great quotation templates will significantly\n"
"                                <strong>boost your success rate</strong>. The\n"
"                                first section is usually about your company,\n"
"                                your references, your methodology or\n"
"                                guarantees, your team, SLA, terms and conditions, etc."
msgstr ""
"Geweldige offerte sjablonen zullen significant helpen\n"
"bij het<strong>vergroten van je succes</strong>. Het \n"
"eerste deel is meestal over je bedrijf,\n"
"je referenties, je methode/garanties, \n"
"je team, SLA, voorwaarden, etc."

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"If you edit a quotation from the 'Preview' of a quotation, you will\n"
"                        update that quotation only. If you edit the quotation\n"
"                        template (from the Configuration menu), all future quotations will\n"
"                        use this modified template."
msgstr ""
"Wanneer een offerte wordt bewerkt in de 'voorbeeld weergave' van een offerte, kun je\n"
"alleen deze offerte bijwerken. Wanneer je een offertesjabloon\n"
"bewerkt (vanuit het instellingen menu), zullen alle toekomstige offerte \n"
"het aangepaste sjabloon gebruiken."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "Open Source CRM"
msgstr "Open Source CRM"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Optional Product:"
msgstr "Optioneel product:"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Offer"
msgstr "Ons aanbod"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Quality"
msgstr "Onze kwaliteit"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Service"
msgstr "Onze diensten"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Price"
msgstr "Prijs"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_product_template
msgid "Product Template"
msgstr "Productsjabloon"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Product quality is the foundation we\n"
"                                stand on; we build it with a relentless\n"
"                                focus on fabric, performance and craftsmanship."
msgstr ""
"Productkwaliteit is het fundament waar wij\n"
"op staan; we bouwen het met een genadeloze\n"
"focus op stof, prestatie en vakmanschap."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Product:"
msgstr "Product:"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_description
msgid "Quotation Description"
msgstr "Offerteomschrijving"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_only_description
msgid "Quotation Only Description"
msgstr "Omschrijving voor alleen de offerte"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template
msgid "Quotation Template"
msgstr "Offertesjabloon"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Offertesjabloonregel"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "Offerte sjabloon optie"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_option
msgid "Sale Options"
msgstr "Verkoopopties"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order
msgid "Sales Order"
msgstr "Verkooporder"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Terms &amp; Conditions"
msgstr "Algemene voorwaarden"

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_sale_order_template_line__website_description
msgid "The quotation description (not used on eCommerce)"
msgstr "De offerte omschrijving (niet gebruikt voor E-commerce)"

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_description
msgid ""
"This field uses the Quotation Only Description if it is defined, otherwise "
"it will try to read the eCommerce Description."
msgstr ""
"Dit veld gebruikt de alleen voor de offerte gebruikt als deze is "
"gedefinieerd, anders probeert het de E-commerce-beschrijving te lezen."

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"This is a <strong>sample quotation template</strong>. You should\n"
"                                customize it to fit your own needs from the <i>Sales</i>\n"
"                                application, using the menu: Configuration /\n"
"                                Quotation Templates."
msgstr ""
"Dit is een <strong>voorbeeld offertesjabloon</strong>. Je zou het moeten\n"
"personaliseren naar je eigen behoefte vanuit de <i>Verkoopbeheer</i>\n"
"applicatie, door het menu Configuratie /\n"
"Offertesjablonen te gebruiken."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "This is a preview of the sale order template."
msgstr "Dit is een voorbeeld van het verkoopordersjabloon."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"Titles with style <i>Heading 2</i> and\n"
"                                    <i>Heading 3</i> will be used to generate the\n"
"                                    table of content automatically."
msgstr ""
"Titels met stijl <i>Kop 2</i> en\n"
"<i>Kop 3</i> zal worden gebruikt om de\n"
"inhoudsopgave aan te maken."

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"We always ensure that our products are\n"
"                                set at a fair price so that you will be\n"
"                                happy to buy them."
msgstr ""
"We verzekeren ons er altijd van dat onze producten\n"
"een eerlijke prijs hebben zodat jij\n"
"ze met plezier koopt."

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_option__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_option__website_description
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Website Description"
msgstr "Website omschrijving"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"You can <strong>set a description per product</strong>. Odoo will\n"
"                        automatically create a quotation using the descriptions\n"
"                        of all products in the proposal. The table of content\n"
"                        on the left is generated automatically using the styles you\n"
"                        used in your description (heading 1, heading 2, ...)"
msgstr ""
"Je kunt <strong>een omschrijving instellen per product</strong>. Odoo zal\n"
"automatisch een offerte maken met behulp van de omschrijvingen\n"
"van alle producten in het voorstel. De inhoudsopgave\n"
"aan de linkerkant wordt automatisch gegenereerd met behulp van de stijlen die je\n"
"gebruikt in je beschrijving (rubriek 1, rubriek 2, ...)"
