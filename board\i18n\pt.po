# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "\"%s\" added to dashboard"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"\"Add to\n"
"                Dashboard\""
msgstr ""
"\"Adicionar ao\n"
"Painel\""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "'%s' added to dashboard"
msgstr "'%s' adicionado ao painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add"
msgstr "Adicionar"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my Dashboard"
msgstr "Adicionar ao meu Painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my dashboard"
msgstr "Adicionar ao meu painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Are you sure you want to remove this item?"
msgstr "Tem a certeza de que pretende remover este item?"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr "Quadro"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout"
msgstr "Mudar aspeto"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout.."
msgstr "Mudar aspeto..."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Choose dashboard layout"
msgstr "Escolher o layout do painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Could not add filter to dashboard"
msgstr "Não foi possível adicionar o filtro ao painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Edit Layout"
msgstr "Editar aspeto"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Layout"
msgstr "Aspeto"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
#, python-format
msgid "My Dashboard"
msgstr "O Meu Painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                menu, switch to list or graph view, and click"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                dashboard using the search options."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Your personal dashboard is empty"
msgstr "O seu Painel Pessoal está vazio"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "in the extended search options."
msgstr ""
