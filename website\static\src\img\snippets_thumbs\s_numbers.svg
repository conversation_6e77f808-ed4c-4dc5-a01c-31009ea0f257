<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <path id="path-1" d="M14 26v1H5v-1h9zm-1-3v1H7v-1h6zm1-3v1H5v-1h9z"/>
    <filter id="filter-2" width="111.1%" height="128.6%" x="-5.6%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-3" width="17" height="2" x="1" y="14"/>
    <filter id="filter-4" width="105.9%" height="200%" x="-2.9%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-5" d="M40 26v1h-9v-1h9zm-1-3v1h-6v-1h6zm1-3v1h-9v-1h9z"/>
    <filter id="filter-6" width="111.1%" height="128.6%" x="-5.6%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-7" width="17" height="2" x="27" y="14"/>
    <filter id="filter-8" width="105.9%" height="200%" x="-2.9%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_numbers">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(19 16)">
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-1"/>
        </g>
        <g class="rectangle_copy">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-3"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-5"/>
        </g>
        <g class="rectangle_copy">
          <use fill="#000" filter="url(#filter-8)" xlink:href="#path-7"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-7"/>
        </g>
        <path fill="#E4E4E4" fill-rule="nonzero" d="M7.41 10.522v-2.59h1.47V6.489H7.41V.46H5.504L.595 6.674v1.258h4.526v2.59h2.29zM5.12 6.49H1.642l3.48-4.395V6.49zm7.89 4.033c1.207 0 2.2-.317 2.976-.953.777-.636 1.166-1.455 1.166-2.458 0-.57-.116-1.047-.349-1.432a2.858 2.858 0 0 0-.861-.92 3.482 3.482 0 0 0-1.152-.502 5.439 5.439 0 0 0-1.2-.133c-.524 0-.98.07-1.367.212-.387.141-.704.29-.95.444l.28-2.256h5.284V.638h-5.776l-.663 5.03.533.179c.228-.278.486-.503.773-.674.287-.17.642-.256 1.066-.256.547 0 .992.206 1.336.619.345.412.517.94.517 1.582 0 .442-.053.838-.158 1.186-.104.349-.26.65-.465.906a1.803 1.803 0 0 1-1.408.684c-.127 0-.264-.01-.41-.031a1.814 1.814 0 0 1-.383-.092c.05-.142.114-.347.192-.616.077-.269.116-.517.116-.745a.964.964 0 0 0-.345-.776c-.23-.193-.526-.29-.886-.29-.346 0-.623.111-.83.335a1.16 1.16 0 0 0-.311.82c0 .547.312 1.02.936 1.422.625.4 1.404.601 2.338.601z" class="45"/>
        <path fill="#E4E4E4" fill-rule="nonzero" d="M29.128 10.563c1.941-.255 3.473-.91 4.594-1.965 1.12-1.055 1.681-2.412 1.681-4.07 0-1.254-.353-2.252-1.06-2.995C33.638.79 32.686.42 31.487.42c-1.107 0-2.03.316-2.768.947-.739.63-1.108 1.427-1.108 2.389 0 .451.072.87.216 1.254.143.385.343.715.598.988.25.269.547.48.889.636.341.155.708.232 1.1.232.47 0 .892-.056 1.268-.167.376-.112.765-.343 1.166-.694-.064.406-.16.8-.287 1.183A4.04 4.04 0 0 1 32 8.274c-.237.333-.54.625-.91.875-.368.251-.833.456-1.394.616l-.724.123.157.676zM31.54 5.99c-.42 0-.758-.216-1.015-.65-.258-.432-.386-1.013-.386-1.742 0-.834.126-1.472.379-1.914.253-.442.582-.663.988-.663.42 0 .76.29 1.022.871s.393 1.419.393 2.512c0 .087-.002.176-.007.267a4.767 4.767 0 0 0-.007.232 2.9 2.9 0 0 1-.007.222 1.756 1.756 0 0 0-.006.12c-.192.287-.4.483-.623.588a1.7 1.7 0 0 1-.731.157zM42.574 8v-.52a7.37 7.37 0 0 1-.615-.082c-.278-.045-.476-.095-.595-.15a.69.69 0 0 1-.304-.277.831.831 0 0 1-.106-.427V2.538c0-.314.007-.67.02-1.066.014-.397.028-.743.042-1.04h-1.203a3.585 3.585 0 0 1-.298.363 2.502 2.502 0 0 1-.482.396 2.842 2.842 0 0 1-.721.315 3.61 3.61 0 0 1-1.029.13h-.376v.65h1.73v4.333c0 .192-.039.342-.116.451a.623.623 0 0 1-.322.233 3.68 3.68 0 0 1-.612.11c-.298.036-.516.058-.652.067V8h5.64z" class="91"/>
      </g>
    </g>
  </g>
</svg>
