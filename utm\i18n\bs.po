# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * utm
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__active
msgid "Active"
msgstr "Aktivan"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__campaign_id
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_tree
msgid "Campaign"
msgstr "Kampanja"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__name
msgid "Campaign Name"
msgstr "Naziv kampanje"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_act
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
msgid "Campaigns"
msgstr "Kampanje"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
msgid "Channel"
msgstr "Kanal"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__name
msgid "Channel Name"
msgstr "Ime kanala"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr "Božićni specijal"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_date
#: model:ir.model.fields,field_description:utm.field_utm_source__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Define a new medium"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "Define a new source"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium__display_name
#: model:ir.model.fields,field_description:utm.field_utm_mixin__display_name
#: model:ir.model.fields,field_description:utm.field_utm_source__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr "E-mail kampanja - usluge"

#. module: utm
#: model:utm.source,name:utm.utm_source_facebook
msgid "Facebook"
msgstr "Facebook"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__id
#: model:ir.model.fields,field_description:utm.field_utm_medium__id
#: model:ir.model.fields,field_description:utm.field_utm_mixin__id
#: model:ir.model.fields,field_description:utm.field_utm_source__id
msgid "ID"
msgstr "ID"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign____last_update
#: model:ir.model.fields,field_description:utm.field_utm_medium____last_update
#: model:ir.model.fields,field_description:utm.field_utm_mixin____last_update
#: model:ir.model.fields,field_description:utm.field_utm_source____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_date
#: model:ir.model.fields,field_description:utm.field_utm_source__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: utm
#: model:utm.source,name:utm.utm_source_mailing
msgid "Lead Newsletter"
msgstr ""

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr "Pratioc veza"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.model.fields,field_description:utm.field_utm_mixin__medium_id
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Medium"
msgstr "Medijum"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_products
#: model:utm.source,name:utm.utm_source_newsletter
msgid "Newsletter"
msgstr "Newsletter"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr "Prodaja"

#. module: utm
#: model:utm.source,name:utm.utm_source_search_engine
msgid "Search engine"
msgstr "Pretraživač"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__source_id
msgid "Source"
msgstr "Izvor"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source__name
msgid "Source Name"
msgstr "Naziv izvora"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr "Izvori"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_twitter
msgid "Twitter"
msgstr "Twitter"

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
msgid "UTM Campaign"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "UTM Medium"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "UTM Mixin"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_source
msgid "UTM Source"
msgstr ""

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr "UTMs"
