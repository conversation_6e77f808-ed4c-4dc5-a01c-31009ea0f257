<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_id_chart" model="account.chart.template">
        <field name="property_account_receivable_id" ref="l10n_id_11210010"/>
        <field name="property_account_payable_id" ref="l10n_id_21100010"/>
        <field name="property_account_expense_categ_id" ref="l10n_id_51000010"/>
        <field name="property_account_income_categ_id" ref="l10n_id_41000010"/>
        <field name="property_stock_account_input_categ_id" ref="l10n_id_29000000"/>
        <field name="property_stock_account_output_categ_id" ref="l10n_id_29000000"/>
        <field name="property_stock_valuation_account_id" ref="l10n_id_11300180"/>
        <field name="income_currency_exchange_account_id" ref="l10n_id_81100010"/>
        <field name="expense_currency_exchange_account_id" ref="l10n_id_91100010"/>
        <field name="default_pos_receivable_account_id" ref="l10n_id_11210011"/>
        <field name="use_anglo_saxon" eval="1"/>
    </record>
</odoo>
