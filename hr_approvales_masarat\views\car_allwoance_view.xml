<?xml version="1.0"?>
<odoo>
    <record id="masarat_car_view_form" model="ir.ui.view">
        <field name="name">hr.masarat.car.form</field>
        <field name="model">hr.masarat.car</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                    <button string="Manager Approval"
                            attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}"
                            name="make_manager_approval" type="object" class="oe_highlight"/>
                    <button string="Manager Refuse"
                            attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}"
                            name="make_manager_refused" type="object"/>
                    <button string="HR Approval" name="make_hr_approval" type="object" class="oe_highlight"
                            groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <button string="HR Refuse" name="make_hr_refused" type="object"
                            groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <!--                    <button string="Cancel" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','=','draft')]}" name="make_cancel_approval" type="object"/>-->
                    <button string="Cancel" attrs="{'invisible': [('state','=','draft')]}" name="make_cancel_approval"
                            type="object"/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="employee_id" placeholder="Employee"
                                   attrs="{'readonly': [('is_hr_group', '!=', 'yes')]}"/>
                            <field name="is_hr_group" invisible="1"/>
                        </h2>
                    </div>
                    <group>
                        <group string="Request Info">
                            <field name="is_manager" invisible="1"/>
                            <field name="manager_id" options='{"no_open": True}'/>
                            <field name="request_date"/>
                        </group>
                        <group string="Total Overtime Hours">
                            <field name="car_allowance_total_hours" widget="float_time"/>
                        </group>
                        <group>
                            <field name="Note"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="حركات السيارة">
                            <field name="car_line_ids" attrs="{'readonly': [('state','!=','draft')]}">
                                <tree editable="bottom">
                                    <field name="located_by" options="{'no_open': True, 'no_create': True, 'no_create_edit':True}"/>
                                    <field name="request_date"/>
                                    <field name="allowance_hours" widget="float_time"/>
                                    <field name="location"/>
                                    <field name="mission_type"/>
                                </tree>
                                <form>
                                    <group>
                                        <group>
                                            <field name="located_by" options="{'no_open': True, 'no_create': True, 'no_create_edit':True}"/>
                                            <field name="location"/>
                                            <field name="mission_type"/>
                                        </group>
                                        <group>
                                            <field name="request_date"/>
                                            <field name="allowance_hours" widget="float_time"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="masarat_car_view_tree" model="ir.ui.view">
        <field name="name">hr.masarat.car.tree</field>
        <field name="model">hr.masarat.car</field>
        <field name="arch" type="xml">
            <tree>
                <field name="employee_id"/>
                <field name="car_allowance_total_hours" widget="float_time"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="action_masarat_car_view" model="ir.actions.act_window">
        <field name="name">حركة السيارة</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.car</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem
            id="menu_car_allawoance"
            name="حركة السيارة"
            parent="hr_masarat_approvals"
            action="action_masarat_car_view"
            sequence="7"/>
</odoo>

