<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_purchaseorder_document">
    <t t-call="web.external_layout">
        <t t-set="o" t-value="o.with_context(lang=o.partner_id.lang)"/>
        <t t-set="address">
            <div t-field="o.partner_id"
            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
            <p t-if="o.partner_id.vat"><t t-esc="o.company_id.account_fiscal_country_id.vat_label or 'Tax ID'"/>: <span t-field="o.partner_id.vat"/></p>
        </t>
        <t t-if="o.dest_address_id">
            <t t-set="information_block">
                <strong>Shipping address:</strong>
                <div t-if="o.dest_address_id">
                    <div t-field="o.dest_address_id"
                        t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}' name="purchase_shipping_address"/>
                </div>

            </t>
        </t>
        <div class="page">
            <div class="oe_structure"/>

            <h2 t-if="o.state in ['draft', 'sent', 'to approve']">Request for Quotation #<span t-field="o.name"/></h2>
            <h2 t-if="o.state in ['purchase', 'done']">Purchase Order #<span t-field="o.name"/></h2>
            <h2 t-if="o.state == 'cancel'">Cancelled Purchase Order #<span t-field="o.name"/></h2>

            <div id="informations" class="row mt32 mb32">
                <div t-if="o.user_id" class="col-3 bm-2">
                    <strong>Purchase Representative:</strong>
                    <p t-field="o.user_id" class="m-0"/>
                </div>
                <div t-if="o.partner_ref" class="col-3 bm-2">
                    <strong>Your Order Reference:</strong>
                    <p t-field="o.partner_ref" class="m-0"/>
                </div>
                <div t-if="o.state in ['purchase','done'] and o.date_approve" class="col-3 bm-2">
                    <strong>Order Date:</strong>
                    <p t-field="o.date_approve" class="m-0"/>
                </div>
                <div t-elif="o.date_order" class="col-3 bm-2">
                    <strong >Order Deadline:</strong>
                    <p t-field="o.date_order" class="m-0"/>
                </div>
            </div>

            <table class="table table-sm o_main_table">
                <thead>
                    <tr>
                        <th name="th_description"><strong>Description</strong></th>
                        <th name="th_taxes"><strong>Taxes</strong></th>
                        <th name="th_date_req" class="text-center"><strong>Date Req.</strong></th>
                        <th name="th_quantity" class="text-right"><strong>Qty</strong></th>
                        <th name="th_price_unit" class="text-right"><strong>Unit Price</strong></th>
                        <th name="th_amount" class="text-right"><strong>Amount</strong></th>
                    </tr>
                </thead>
                <tbody>
                    <t t-set="current_subtotal" t-value="0"/>
                    <t t-foreach="o.order_line" t-as="line">
                        <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal" groups="account.group_show_line_subtotals_tax_excluded"/>
                        <t t-set="current_subtotal" t-value="current_subtotal + line.price_total" groups="account.group_show_line_subtotals_tax_included"/>

                        <tr t-att-class="'bg-200 font-weight-bold o_line_section' if line.display_type == 'line_section' else 'font-italic o_line_note' if line.display_type == 'line_note' else ''">
                            <t t-if="not line.display_type">
                                <td id="product">
                                    <span t-field="line.name"/>
                                </td>
                                <td name="td_taxes">
                                    <span t-esc="', '.join(map(lambda x: x.name, line.taxes_id))"/>
                                </td>
                                <td class="text-center">
                                    <span t-field="line.date_planned"/>
                                </td>
                                <td class="text-right">
                                    <span t-field="line.product_qty"/>
                                    <span t-field="line.product_uom.name" groups="uom.group_uom"/>
                                </td>
                                <td class="text-right">
                                    <span t-field="line.price_unit"/>
                                </td>
                                <td class="text-right">
                                    <span t-field="line.price_subtotal"
                                        t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                </td>
                            </t>
                            <t t-if="line.display_type == 'line_section'">
                                <td colspan="99" id="section">
                                    <span t-field="line.name"/>
                                </td>
                                <t t-set="current_section" t-value="line"/>
                                <t t-set="current_subtotal" t-value="0"/>
                            </t>
                            <t t-if="line.display_type == 'line_note'">
                                <td colspan="99" id="note">
                                    <span t-field="line.name"/>
                                </td>
                            </t>
                        </tr>
                        <t t-if="current_section and (line_last or o.order_line[line_index+1].display_type == 'line_section')">
                            <tr class="is-subtotal text-right">
                                <td colspan="99" id="subtotal">
                                    <strong class="mr16">Subtotal</strong>
                                    <span
                                        t-esc="current_subtotal"
                                        t-options='{"widget": "monetary", "display_currency": o.currency_id}'
                                    />
                                </td>
                            </tr>
                        </t>
                    </t>
                </tbody>
            </table>

            <div id="total" class="row justify-content-end">
                <div class="col-4">
                    <table class="table table-sm">
                        <t t-set="tax_totals" t-value="json.loads(o.tax_totals_json)"/>
                        <t t-call="account.document_tax_totals"/>
                    </table>
                </div>
            </div>

            <p t-field="o.notes"/>
            <div class="oe_structure"/>
        </div>
    </t>
</template>

<template id="report_purchaseorder">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-call="purchase.report_purchaseorder_document" t-lang="o.partner_id.lang"/>
        </t>
    </t>
</template>
</odoo>
