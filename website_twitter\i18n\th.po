# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_twitter
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON>awa<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2021\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                Show me how to obtain the Twitter API key and Twitter API secret"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                แสดงวิธีรับคีย์ Twitter API และTwitter API secret"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Twitter Roller</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">ลูกกลิ้ง Twitter</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะเว็บไซต์\" groups=\"website.group_multi_website\"/>"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Callback URL: </strong>Leave blank"
msgstr "<strong>เรียกกลับ URL: </strong>ปล่อยว่าง"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Description: </strong> Odoo Twitter Integration"
msgstr "<strong>รายละเอียด: </strong> การผสาน Odoo Twitter "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Name: </strong> Odoo Twitter Integration"
msgstr "<strong>ชื่อ: </strong> การผสาน Odoo Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Website: </strong>"
msgstr "<strong>เว็บไซต์: </strong>"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_key
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API Key"
msgstr "API Key"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_secret
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API secret"
msgstr "API secret"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Accept terms of use and click on the Create your Twitter application button "
"at the bottom"
msgstr ""
"ยอมรับเงื่อนไขการใช้งานและคลิกที่ปุ่มสร้างแอปพลิเคชัน Twitter "
"ของคุณที่ด้านล่าง"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Authentication credentials were missing or incorrect. Maybe screen name "
"tweets are protected."
msgstr ""
"ข้อมูลรับรองการตรวจสอบสิทธิ์ขาดหายไปหรือไม่ถูกต้อง "
"บางทีทวีตชื่อหน้าจออาจได้รับการคุ้มครอง"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Copy/Paste Consumer Key (API Key) and Consumer Secret (API Secret) keys "
"below."
msgstr ""
"คัดลอก/วาง Consumer Key( คีย์ API) และ Consumer Secret (รหัสลับของ API) "
"ด้านล่าง"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Create a new Twitter application on"
msgstr "สร้างแอปพลิเคชัน Twitter ใหม่บน"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_screen_name
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Favorites From"
msgstr "รายการโปรดจาก"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_screen_name
msgid "Get favorites from this screen name"
msgstr "รับรายการโปรดจากชื่อหน้าจอนี้"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "HTTP Error: Something is misconfigured"
msgstr "ข้อผิดพลาด HTTP: มีการกำหนดค่าบางอย่างผิดพลาด"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "How to configure the Twitter API access"
msgstr "วิธีกำหนดค่าการเข้าถึง Twitter API"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__id
msgid "ID"
msgstr "ไอดี"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Internet connection refused: We failed to reach a twitter server."
msgstr ""
"การเชื่อมต่ออินเตอร์เน็ตถูกปฏิเสธ: เราไม่สามารถเข้าถึงเซิร์ฟเวอร์ Twitter"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Please set a Twitter screen name to load favorites from, in the Website "
"Settings (it does not have to be yours)"
msgstr ""
"โปรดตั้งชื่อหน้าจอ Twitter เพื่อโหลดรายการโปรดจากการตั้งค่าเว็บไซต์ "
"(ไม่จำเป็นต้องเป็นของคุณ)"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid "Please set the Twitter API Key and Secret in the Website Settings."
msgstr "โปรดตั้งค่าคีย์ Twitter API และข้อมูลลับในการตั้งค่าเว็บไซต์"

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/js/website.twitter.editor.js:0
#, python-format
msgid "Reload"
msgstr "โหลดใหม่"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Request cannot be served due to the applications rate limit having been "
"exhausted for the resource."
msgstr ""
"ไม่สามารถให้บริการตามคำขอได้เนื่องจากขีดจำกัดอัตราแอปพลิเคชันหมดลงสำหรับทรัพยากรแล้ว"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__screen_name
msgid "Screen Name"
msgstr "ชื่อหน้าจอ"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_screen_name
msgid ""
"Screen Name of the Twitter Account from which you want to load favorites.It "
"does not have to match the API Key/Secret."
msgstr ""
"ชื่อหน้าจอของบัญชี Twitter ที่คุณต้องการโหลดรายการโปรด "
"ไม่จำเป็นต้องตรงกับคีย์ Key/Secret"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Switch to the API Keys tab: <br/>"
msgstr "สลับไปที่แท็บคีย์ API: <br/>"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but overloaded with requests. Try again later."
msgstr ""
"เซิร์ฟเวอร์ Twitter เปิดให้บริการแล้ว แต่มีคำขอม่ากเกินไป "
"ลองอีกครั้งในภายหลัง"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but the request could not be serviced due to "
"some failure within our stack. Try again later."
msgstr ""
"เซิร์ฟเวอร์ Twitter ใช้งานได้แล้ว "
"แต่ไม่สามารถให้บริการตามคำขอได้เนื่องจากเกิดความล้มเหลวบางอย่างภายในสแตกของเรา"
" ลองอีกครั้งในภายหลัง"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request is understood, but it has been refused or access is not allowed."
" Please check your Twitter API Key and Secret."
msgstr ""
"เข้าใจคำขอแล้ว แต่ถูกปฏิเสธหรือไม่อนุญาตให้เข้าถึง โปรดตรวจสอบคีย์ Twitter "
"API และ Secret ของคุณ"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request was invalid or cannot be otherwise served. Requests without "
"authentication are considered invalid and will yield this response."
msgstr ""
"คำขอไม่ถูกต้องหรือไม่สามารถให้บริการได้ "
"คำขอที่ไม่มีการรับรองความถูกต้องจะถือว่าไม่ถูกต้องและจะให้ผลการตอบกลับนี้"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "There was no new data to return."
msgstr "ไม่มีข้อมูลใหม่ให้ส่งคืน"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet_id
msgid "Tweet ID"
msgstr "ไอดีทวีต"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet
msgid "Tweets"
msgstr "ทวีต"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter API Credentials"
msgstr "ข้อมูลรับรอง Twitter API"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_key
msgid "Twitter API Key"
msgstr "Twitter API Key"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_secret
msgid "Twitter API Secret"
msgstr "Twitter API Secret"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_key
msgid "Twitter API key"
msgstr "Twitter API key"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_key
msgid "Twitter API key you can get it from https://apps.twitter.com/"
msgstr "คีย์ Twitter API ที่คุณสามารถหาได้จาก https://apps.twitter.com/"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_secret
msgid "Twitter API secret"
msgstr "Twitter API secret"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_secret
msgid "Twitter API secret you can get it from https://apps.twitter.com/"
msgstr "Twitter API secret ที่คุณสามารถได้จาก https://apps.twitter.com/"

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter Configuration"
msgstr "การกำหนดค่า Twitter"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter authorization error! Please double-check your Twitter API Key and "
"Secret!"
msgstr ""
"ข้อผิดพลาดในการอนุญาต Twitter! โปรดตรวจสอบคีย์ Twitter API และ Secret "
"ของคุณอีกครั้ง!"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Twitter is down or being upgraded."
msgstr "Twitter หยุดทำงานหรือกำลังอัปเกรด"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter seems broken. Please retry later. You may consider posting an issue "
"on Twitter forums to get help."
msgstr ""
"ทวิตเตอร์เหมือนจะล่ม โปรดลองอีกครั้งในภายหลัง "
"คุณอาจพิจารณาโพสต์ปัญหาบนฟอรั่ม Twitter เพื่อขอความช่วยเหลือ"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_server_uri
msgid "Twitter server uri"
msgstr "เซิร์ฟเวอร์ Twitter uri"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter tutorial"
msgstr "การสอน Twitter"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Twitter user @%(username)s has less than 12 favorite tweets. Please add more"
" or choose a different screen name."
msgstr ""
"ผู้ใช้ Twitter @%(username)s มีทวีตโปรดน้อยกว่า 12 รายการ "
"โปรดเพิ่มหรือเลือกชื่อหน้าจอต่าง ๆ "

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter's user"
msgstr "ผู้ใช้ทวิตเตอร์"

#. module: website_twitter
#: model:ir.actions.server,name:website_twitter.ir_cron_twitter_actions_ir_actions_server
#: model:ir.cron,cron_name:website_twitter.ir_cron_twitter_actions
#: model:ir.cron,name:website_twitter.ir_cron_twitter_actions
msgid "Twitter: Fetch new favorites"
msgstr "Twitter: เรียกรายการโปรดใหม่"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__website_id
msgid "Website"
msgstr "เว็บไซต์"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_twitter_tweet
msgid "Website Twitter"
msgstr "เว็บไซต์ Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://apps.twitter.com/app/new"
msgstr "https://apps.twitter.com/app/new"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "with the following values:"
msgstr "ด้วยค่าต่อไปนี้:"
