<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_bg" model="res.partner">
        <field name="name">BG Company</field>
        <field name="street">3233 Green Acres Road</field>
        <field name="street2">Unit 07</field>
        <field name="city">Sofia</field>
        <field name="zip">1000</field>
        <field name="country_id" ref="base.bg"/>
        <field name="phone">+852 5123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.bgexample.com</field>
        <field name="vat">*********</field>
    </record>

    <record id="demo_company_bg" model="res.company">
        <field name="name">BG Company</field>
        <field name="partner_id" ref="partner_demo_company_bg"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_bg')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_bg.demo_company_bg'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_bg.l10n_bg_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_bg.demo_company_bg')"/>
    </function>
</odoo>
