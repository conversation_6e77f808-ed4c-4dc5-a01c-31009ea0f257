# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import_module
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "ยกเลิก"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr "ปิด"

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Could not select database '%s'"
msgstr "ไม่สามารถเลือกฐานข้อมูล '%s'"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "File '%s' exceed maximum allowed file size"
msgstr "ไฟล์ '%s' มีขนาดใหญ่เกินกว่าที่อนุญาต"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr "บังคับเริ่มต้น"

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid ""
"Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr ""
"บังคับโหมดเริ่มต้นแม้ว่าจะติดตั้งแล้วก็ตาม (จะอัปเดตบันทึก `noupdate='1'')"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr "รหัส"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import App"
msgstr "แอพที่จะนำเข้า"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr "นำเข้าข้อความ"

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import Module"
msgstr "นำเข้าโมดูล"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import module"
msgstr "นำเข้าโมดูล"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr "โมดูลที่นำเข้า"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr "โมดูล"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr "ไฟล์ .ZIP ของโมดูล"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "No file sent."
msgstr "ไม่มีไฟล์ที่ส่ง"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr ""
"หมายเหตุ: คุณสามารถนำเข้าได้เฉพาะโมดูลข้อมูล (ไฟล์ .xml และสินทรัพย์คงที่)"

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Only administrators can upload a module"
msgstr "มีเพียงผู้ดูแลระบบเท่านั้นที่สามารถอัปโหลดโมดูลได้"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Only zip files are supported."
msgstr "รองรับเฉพาะไฟล์ zip เท่านั้น"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Open Modules"
msgstr "เปิด โมดุล"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Select module package to import (.zip file):"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "สถานะ"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Studio customizations require Studio"
msgstr "การปรับแต่งสตูดิโอต้องใช้แอปสตูดิโอ"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Studio customizations require the Odoo Studio app."
msgstr "การปรับแต่งสตูดิโอต้องใช้แอปสตูดิโอของ Odoo"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"Unmet module dependencies: \n"
"\n"
" - %s"
msgstr ""

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr "มุมมอง"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr "เสร็จสิ้น"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr "init"
