<?xml version="1.0"?>
<odoo>
<!---->
    <record id="view_hr_job_form_inherited_x1" model="ir.ui.view">
        <field name="name">hr.job.form</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_hr_job_form"/>
        <field name="arch" type="xml">
<!--            <xpath expr="/form/sheet/notebook/page[1]/div[1]" position="replace">-->
<!--                <div attrs="{'invisible': [('state', '!=', 'recruit')]}" name="job_description">-->
<!--                    <label for="description"/>-->
<!--                    <field name="description" string="description"/>-->
<!--                </div>-->
<!--            </xpath>-->
            <xpath expr="/form/sheet/notebook/page[2]/group[1]/group[2]" position="after">
                <group>
                    <field name="requisted_speciality_ids" widget="one2many_list">
                        <tree editable="bottom">
                            <field name="name" required="1"/>
                        </tree>
                    </field>
                    <field name="scientific_qualification"/>
                    <field name="years_of_experiance"/>
                </group>
                <group>
                    <field name="requisted_skills_ids" widget="many2many_tags"/>
                </group>
                <group>
                    <field name="reason_for_reqruitment"/>
                </group>
            </xpath>
        </field>
    </record>
</odoo>


