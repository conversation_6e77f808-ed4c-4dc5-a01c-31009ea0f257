<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="ProductScreen" owl="1">
        <div class="product-screen screen" t-att-class="{ oe_hidden: !props.isShown }">
            <div class="screen-full-width">
                <div class="leftpane pane-border" t-if="!env.isMobile || state.mobile_pane === 'left'">
                    <OrderWidget/>
                    <div class="pads">
                        <div class="control-buttons">
                            <t t-if="env.isMobile and controlButtons.length > 3">
                                <div class="control-button" t-on-click="_displayAllControlPopup">More...</div>
                            </t>
                            <t t-else="">
                                <t t-foreach="controlButtons" t-as="cb" t-key="cb.name">
                                    <t t-component="cb.component" t-key="cb.name"/>
                                </t>
                            </t>
                        </div>
                        <div class="subpads">
                            <t t-set="_actionName">Payment</t>
                            <ActionpadWidget client="client" actionName="_actionName" t-on-switchpane="switchPane" />
                            <NumpadWidget activeMode="state.numpadMode" />
                        </div>
                    </div>
                </div>
                <div class="rightpane" t-if="!env.isMobile || state.mobile_pane === 'right'">
                    <ProductsWidget mobileSearchBarIsShown="props.mobileSearchBarIsShown"/>
                    <MobileOrderWidget t-if="env.isMobile" pane="state.mobile_pane" t-on-switchpane="switchPane"/>
                </div>
            </div>
        </div>
    </t>

</templates>
