# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase_requisition
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Call for Tender Reference:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Date</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Description</strong>"
msgstr "<strong>Opis</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Product UoM</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Qty</strong>"
msgstr "<strong>Količina</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Reference </strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Date</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Ordering Date:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Selection Type:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Source:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Vendor </strong>"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_date_end
msgid "Agreement Deadline"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_name
msgid "Agreement Reference"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_exclusive
msgid "Agreement Selection Type"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_id_10513
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_name
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
msgid "Agreement Type"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.res_config_settings_view_form
msgid "Agreement Types"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_account_analytic_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_account_analytic_id
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Bid Selection"
msgstr ""

#. module: purchase_requisition
#: model:purchase.requisition.type,name:purchase_requisition.type_single
msgid "Blanket Order"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.report,name:purchase_requisition.action_report_purchase_requisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel Call"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Cancelled"
msgstr "Poništeno"

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:82
#, python-format
msgid "Cancelled by the agreement associated to this quotation."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Click to start a new purchase agreement."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_company_id
msgid "Company"
msgstr "Preduzeće"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm"
msgstr "Potvrdi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: selection:purchase.requisition,state:0
msgid "Confirmed"
msgstr "Potvrđeno"

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Create a draft purchase order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
msgid "Data for new quotations"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_schedule_date
msgid "Delivery Date"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_description
msgid "Description"
msgstr "Opis"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: purchase_requisition
#: selection:purchase.requisition.type,line_copy:0
msgid "Do not create RfQ lines automatically"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: selection:purchase.requisition,state:0
msgid "Done"
msgstr "Završeno"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_move_dest_id
msgid "Downstream Move"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: selection:purchase.requisition,state:0
msgid "Draft"
msgstr "Nacrt"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "End Month"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"Example of purchase agreements include call for tenders and blanket orders."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"For a blanket order, you can record an agreement for a specifc period\n"
"            (e.g. a year) and you order products within this agreement, benefiting\n"
"            from the negociated prices."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_id
msgid "ID"
msgstr "ID"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"In a call for tenders, you can record the products you need to buy\n"
"            and generate the creation of RfQs to vendors. Once the tenders have\n"
"            been registered, you can review and compare them and you can\n"
"            validate some and cancel others."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "In negotiation"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_line_copy
msgid "Lines"
msgstr "Stavke"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Multiple Requisitions"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Agreements"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "New Quotation"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_order_count
msgid "Number of Orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_picking_type_id
msgid "Operation Type"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_qty_ordered
msgid "Ordered Quantities"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_ordering_date
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_product_purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_template_purchase_requisition
msgid "Procurement"
msgstr "Nabava"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_procurement_rule
msgid "Procurement Rule"
msgstr "Pravilo nabave"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_id
msgid "Product"
msgstr "Proizvod"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_uom_id
msgid "Product Unit of Measure"
msgstr "JM proizvoda"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Proizvodi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_ids
msgid "Products to Purchase"
msgstr ""

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Propose a call for tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_requisition_id
msgid "Purchase Agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_type
msgid "Purchase Agreement Type"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.tender_type_action
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_type
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_tree
msgid "Purchase Agreement Types"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Purchase Agreements"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
msgid "Purchase Order"
msgstr "Narudžbenica"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Stavka narudžbenice"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_purchase_ids
msgid "Purchase Orders"
msgstr "Narudžbenice"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
msgid "Purchase Requisition Line"
msgstr ""

#. module: purchase_requisition
#: model:purchase.requisition.type,name:purchase_requisition.type_multi
msgid "Purchase Tender"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.act_res_partner_2_purchase_order
msgid "Purchase orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_quantity_copy
msgid "Quantities"
msgstr "Količine"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_qty
msgid "Quantity"
msgstr "Količina"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Orders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.product_template_form_view_inherit
msgid "Reordering"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_to_so
msgid "Request for Quotation"
msgstr "Zahtjev za ponudu"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_list
msgid "Request for Quotations"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Requests for Quotation Details"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Vrati u izradu"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Responsible"
msgstr "Odgovoran"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_schedule_date
msgid "Scheduled Date"
msgstr "Planirani datum"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Purchase Agreements"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition.type,exclusive:0
msgid "Select multiple RFQ"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition.type,exclusive:0
msgid "Select only one RFQ (exclusive)"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_type_exclusive
msgid ""
"Select only one RFQ (exclusive):  when a purchase order is confirmed, cancel the remaining purchase order.\n"
"\n"
"                    Select multiple RFQ: allows multiple purchase orders. On confirmation of a purchase order it does not cancel the remaining orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: purchase_requisition
#: selection:purchase.requisition.type,quantity_copy:0
msgid "Set quantities manually"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Source"
msgstr "Izvor"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_origin
msgid "Source Document"
msgstr "Izvorni dokument"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Status"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr "Uslovi i dogovori"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_schedule_date
msgid ""
"The expected and scheduled delivery date where all the products are received"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned  Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_price_unit
msgid "Unit Price"
msgstr "Jed. cena"

#. module: purchase_requisition
#: selection:purchase.requisition.type,line_copy:0
msgid "Use lines of agreement"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition.type,quantity_copy:0
msgid "Use quantities of agreement"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Validate"
msgstr "Potvrdi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_vendor_id
msgid "Vendor"
msgstr "Dobavljač"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_warehouse_id
msgid "Warehouse"
msgstr "Skladište"

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:88
#, python-format
msgid "You cannot confirm call because there is no product line."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:105
#, python-format
msgid ""
"You have to cancel or validate every RfQ before closing the purchase "
"requisition."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr ""
