<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Manufacturing Order -->
        <record id="mrp_production_tree_view" model="ir.ui.view">
            <field name="name">mrp.production.tree</field>
            <field name="model">mrp.production</field>
            <field name="arch" type="xml">
                <tree string="Manufacturing Orders" js_class="lazy_column_list" default_order="priority desc, date_planned_start desc" multi_edit="1" sample="1" decoration-info="state == 'draft'">
                    <header>
                        <button name="button_plan" type="object" string="Plan"/>
                        <button name="do_unreserve" type="object" string="Unreserve"/>
                    </header>
                    <field name="priority" optional="show" widget="priority" nolabel="1"/>
                    <field name="message_needaction" invisible="1"/>
                    <field name="name" decoration-bf="1"/>
                    <field name="date_planned_start" readonly="1" optional="show" widget="remaining_days"/>
                    <field name="date_deadline" widget="remaining_days" attrs="{'invisible': [('state', 'in', ['done', 'cancel'])]}" optional="hide"/>
                    <field name="product_id" readonly="1" optional="show"/>
                    <field name="lot_producing_id" optional="hide"/>
                    <field name="bom_id" readonly="1" optional="hide"/>
                    <field name="activity_ids" string="Next Activity" widget="list_activity" optional="show"/>
                    <field name="origin" optional="show"/>
                    <field name="user_id" optional="hide" widget="many2one_avatar_user"/>
                    <field name="components_availability_state" invisible="1" options='{"lazy": true}'/>
                    <field name="components_availability" options='{"lazy": true}'
                        attrs="{'invisible': [('state', 'not in', ['confirmed', 'progress'])]}"
                        optional="show"
                        decoration-success="reservation_state == 'assigned' or components_availability_state == 'available'"
                        decoration-warning="reservation_state != 'assigned' and components_availability_state in ('expected', 'available')"
                        decoration-danger="reservation_state != 'assigned' and components_availability_state == 'late'"/>
                    <field name="reservation_state" optional="hide" decoration-danger="reservation_state == 'confirmed'" decoration-success="reservation_state == 'assigned'"/>
                    <field name="product_qty" sum="Total Qty" string="Quantity" readonly="1" optional="show"/>
                    <field name="product_uom_id" string="UoM" options="{'no_open':True,'no_create':True}" groups="uom.group_uom" optional="show"/>
                    <field name="production_duration_expected" attrs="{'invisible': [('production_duration_expected', '=', 0)]}" groups="mrp.group_mrp_routings" widget="float_time" sum="Total expected duration" optional="show"/>
                    <field name="production_real_duration" attrs="{'invisible': [('production_real_duration', '=', 0)]}" groups="mrp.group_mrp_routings" widget="float_time" sum="Total real duration" optional="show"/>
                    <field name="company_id" readonly="1" groups="base.group_multi_company" optional="show"/>
                    <field name="state" 
                           decoration-success="state in ('done', 'to_close')"
                           decoration-warning="state == 'progress'"
                           decoration-info="state in ('confirmed', 'draft')"
                           optional="show" widget="badge"/>
                    <field name="activity_exception_decoration" widget="activity_exception"/>
                    <field name="delay_alert_date" invisible="1"/>
                    <field nolabel="1" name="json_popover" widget="stock_rescheduling_popover" attrs="{'invisible': [('json_popover', '=', False)]}"/>
                </tree>
            </field>
        </record>

        <record id="production_order_server_action" model="ir.actions.server">
            <field name="name">Mrp: Plan Production Orders</field>
            <field name="model_id" ref="mrp.model_mrp_production"/>
            <field name="binding_model_id" ref="mrp.model_mrp_production"/>
            <field name="binding_view_types">list</field>
            <field name="groups_id" eval="[(4, ref('mrp.group_mrp_routings'))]"/>
            <field name="state">code</field>
            <field name="code">records.button_plan()</field>
        </record>

        <record id="action_production_order_mark_done" model="ir.actions.server">
            <field name="name">Mark as Done</field>
            <field name="model_id" ref="mrp.model_mrp_production"/>
            <field name="binding_model_id" ref="mrp.model_mrp_production"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                res = records.filtered(lambda mo: mo.state in {'confirmed', 'to_close', 'progress'}).button_mark_done()
                if res is not True:
                    action = res
            </field>
        </record>

        <record id="mrp_production_form_view" model="ir.ui.view">
            <field name="name">mrp.production.form</field>
            <field name="model">mrp.production</field>
            <field name="arch" type="xml">
                <form string="Manufacturing Orders">
                <header>
                    <field name="confirm_cancel" invisible="1"/>
                    <field name="show_lock" invisible="1"/>
                    <button name="button_mark_done" attrs="{'invisible': ['|', '|', ('state', 'in', ('draft', 'cancel', 'done', 'to_close')), ('qty_producing', '=', 0), ('move_raw_ids', '!=', [])]}" string="Validate" type="object" class="oe_highlight"
                            confirm="There are no components to consume. Are you still sure you want to continue?" data-hotkey="g"/>
                    <button name="button_mark_done" attrs="{'invisible': ['|', '|', ('state', 'in', ('draft', 'cancel', 'done', 'to_close')), ('qty_producing', '=', 0), ('move_raw_ids', '=', [])]}" string="Validate" type="object" class="oe_highlight" data-hotkey="g"/>
                    <button name="button_mark_done" attrs="{'invisible': [
                        '|',
                        ('move_raw_ids', '=', []),
                        '&amp;',
                        '|',
                        ('state', 'not in', ('confirmed', 'progress')),
                        ('qty_producing', '!=', 0),
                        ('state', '!=', 'to_close')]}" string="Mark as Done" type="object" class="oe_highlight" data-hotkey="g"/>
                    <button name="button_mark_done" attrs="{'invisible': [
                        '|',
                        ('move_raw_ids', '!=', []),
                        '&amp;',
                        '|',
                        ('state', 'not in', ('confirmed', 'progress')),
                        ('qty_producing', '!=', 0),
                        ('state', '!=', 'to_close')]}" string="Mark as Done" type="object" class="oe_highlight" data-hotkey="g"
                            confirm="There are no components to consume. Are you still sure you want to continue?"/>
                    <button name="action_confirm" attrs="{'invisible': [('state', '!=', 'draft')]}" string="Confirm" type="object" class="oe_highlight" data-hotkey="v"/>
                    <button name="button_plan" attrs="{'invisible': ['|', '|', ('state', 'not in', ('confirmed', 'progress', 'to_close')), ('workorder_ids', '=', []), ('is_planned', '=', True)]}" type="object" string="Plan" class="oe_highlight" data-hotkey="x"/>
                    <button name="button_unplan" type="object" string="Unplan" attrs="{'invisible': ['|', ('is_planned', '=', False), ('state', '=', 'cancel')]}" data-hotkey="x"/>
                    <button name="action_assign" attrs="{'invisible': ['|', ('state', 'in', ('draft', 'done', 'cancel')), ('reserve_visible', '=', False)]}" string="Check availability" type="object" data-hotkey="q"/>
                    <button name="do_unreserve" type="object" string="Unreserve" attrs="{'invisible': [('unreserve_visible', '=', False)]}" data-hotkey="w"/>
                    <button name="button_scrap" type="object" string="Scrap" attrs="{'invisible': [('state', 'in', ('cancel', 'draft'))]}" data-hotkey="y"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,progress,done"/>
                    <button name="action_toggle_is_locked" attrs="{'invisible': ['|', ('show_lock', '=', False), ('is_locked', '=', False)]}" string="Unlock" groups="mrp.group_mrp_manager" type="object" help="Unlock the manufacturing order to adjust what has been consumed or produced." data-hotkey="l"/>
                    <button name="action_toggle_is_locked" attrs="{'invisible': ['|', ('show_lock', '=', False), ('is_locked', '=', True)]}" string="Lock" groups="mrp.group_mrp_manager" type="object" help="Lock the manufacturing order to prevent changes to what has been consumed or produced." data-hotkey="l"/>
                    <field name="show_serial_mass_produce" invisible="1"/>
                    <button name="action_serial_mass_produce_wizard" attrs="{'invisible': [('show_serial_mass_produce', '=', False)]}" string="Mass Produce" type="object"/>
                    <button name="action_cancel" type="object" string="Cancel" data-hotkey="z"
                            attrs="{'invisible': ['|', '|', ('id', '=', False), ('state', 'in', ('done', 'cancel')), ('confirm_cancel', '=', True)]}"/>
                    <button name="action_cancel" type="object" string="Cancel" data-hotkey="z"
                            attrs="{'invisible': ['|', '|', ('id', '=', False), ('state', 'in', ('done', 'cancel')), ('confirm_cancel', '=', False)]}"
                            confirm="Some product moves have already been confirmed, this manufacturing order can't be completely cancelled. Are you still sure you want to process ?"/>
                    <button name="button_unbuild" type="object" string="Unbuild" attrs="{'invisible': [('state', '!=', 'done')]}" data-hotkey="shift+v"/>
                </header>
                <sheet>
                    <field name="reservation_state" invisible="1"/>
                    <field name="date_planned_finished" invisible="1"/>
                    <field name="is_locked" invisible="1"/>
                    <field name="qty_produced" invisible="1"/>
                    <field name="unreserve_visible" invisible="1"/>
                    <field name="reserve_visible" invisible="1"/>
                    <field name="consumption" invisible="1"/>
                    <field name="is_planned" invisible="1"/>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" name="action_view_mrp_production_childs" type="object" icon="fa-wrench" attrs="{'invisible': [('mrp_production_child_count', '=', 0)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="mrp_production_child_count"/></span>
                                <span class="o_stat_text">Child MO</span>
                            </div>
                        </button>
                        <button class="oe_stat_button" name="action_view_mrp_production_sources" type="object" icon="fa-wrench" attrs="{'invisible': [('mrp_production_source_count', '=', 0)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="mrp_production_source_count"/></span>
                                <span class="o_stat_text">Source MO</span>
                            </div>
                        </button>
                        <button class="oe_stat_button" name="action_view_mrp_production_backorders" type="object" icon="fa-wrench" attrs="{'invisible': [('mrp_production_backorder_count', '&lt;', 2)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="mrp_production_backorder_count"/></span>
                                <span class="o_stat_text">Backorders</span>
                            </div>
                        </button>
                        <button class="oe_stat_button" name="action_see_move_scrap" type="object" icon="fa-arrows-v" attrs="{'invisible': [('scrap_count', '=', 0)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="scrap_count"/></span>
                                <span class="o_stat_text">Scraps</span>
                            </div>
                        </button>
                        <button type="object" name="action_view_mo_delivery" class="oe_stat_button" icon="fa-truck"  groups="base.group_user" attrs="{'invisible': [('delivery_count', '=', 0)]}">
                            <field name="delivery_count" widget="statinfo" string="Transfers"/>
                        </button>
                        <button name="%(stock.action_stock_report)d" icon="fa-arrow-up" class="oe_stat_button" string="Traceability" type="action" states="done" groups="stock.group_production_lot"/>
                        <button name="%(action_mrp_production_moves)d" type="action" string="Product Moves" class="oe_stat_button" icon="fa-exchange" attrs="{'invisible': [('state', 'not in', ('progress', 'done'))]}"/>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="priority" widget="priority" class="mr-3"/>
                            <field name="name" placeholder="Manufacturing Reference" nolabel="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="id" invisible="1"/>
                            <field name="use_create_components_lots" invisible="1"/>
                            <field name="show_lot_ids" invisible="1"/>
                            <field name="product_tracking" invisible="1"/>
                            <field name="product_id" context="{'default_detailed_type': 'product'}" attrs="{'readonly': [('state', '!=', 'draft')]}" default_focus="1"/>
                            <field name="product_tmpl_id" invisible="1"/>
                            <field name="forecasted_issue" invisible="1"/>
                            <field name="product_description_variants" attrs="{'invisible': [('product_description_variants', 'in', (False, ''))], 'readonly': [('state', '!=', 'draft')]}"/>
                            <label for="product_qty" string="Quantity"/>
                            <div class="o_row no-gutters d-flex">
                                <div attrs="{'invisible': [('state', '=', 'draft')]}" class="o_row">
                                    <field name="qty_producing" class="text-left" attrs="{'readonly': ['|', ('state', '=', 'cancel'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}"/>
                                    /
                                </div>
                                <field name="product_qty" class="oe_inline text-left" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('state', 'not in', ('draft', 'done'))]}"/>
                                <button type="action" name="%(mrp.action_change_production_qty)d"
                                    context="{'default_mo_id': id}" class="oe_link oe_inline" style="margin: 0px; padding: 0px;" attrs="{'invisible': ['|', ('state', 'in', ('draft', 'done','cancel')), ('id', '=', False)]}">
                                    <field name="product_qty" class="oe_inline" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                </button>
                                <label for="product_uom_id" string="" class="oe_inline"/>
                                <field name="product_uom_category_id" invisible="1"/>
                                <field name="product_uom_id" options="{'no_open': True, 'no_create': True}" force_save="1" groups="uom.group_uom" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <span class='font-weight-bold'>To Produce</span>
                                <button type="object" name="action_product_forecast_report" icon="fa-area-chart" attrs="{'invisible': [('forecasted_issue', '=', True)]}"/>
                                <button type="object" name="action_product_forecast_report" icon="fa-area-chart" attrs="{'invisible': [('forecasted_issue', '=', False)]}" class="text-danger"/>
                            </div>
                            <label for="lot_producing_id" attrs="{'invisible': ['|', ('state', '=', 'draft'), ('product_tracking', 'in', ('none', False))]}"/>
                            <div class="o_row" attrs="{'invisible': ['|', ('state', '=', 'draft'), ('product_tracking', 'in', ('none', False))]}">
                                <field name="lot_producing_id"
                                    context="{'default_product_id': product_id, 'default_company_id': company_id}" attrs="{'invisible': [('product_tracking', 'in', ('none', False))]}"/>
                                <button name="action_generate_serial" type="object" class="btn btn-primary fa fa-plus-square-o" aria-label="Creates a new serial/lot number" title="Creates a new serial/lot number" role="img" attrs="{'invisible': ['|', ('product_tracking', 'in', ('none', False)), ('lot_producing_id', '!=', False)]}"/>
                            </div>
                            <field name="bom_id"
                                context="{'default_product_tmpl_id': product_tmpl_id}" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                        <group name="group_extra_info">
                            <label for="date_planned_start"/>
                            <div class="o_row">
                                <field name="date_planned_start"
                                    attrs="{'readonly': [('state', 'in', ['done', 'cancel'])]}"
                                    decoration-warning="state not in ('done', 'cancel') and date_planned_start &lt; now"
                                    decoration-danger="state not in ('done', 'cancel') and date_planned_start &lt; current_date"
                                    decoration-bf="state not in ('done', 'cancel') and (date_planned_start &lt; current_date or date_planned_start &lt; now)"/>
                                <field name="delay_alert_date" invisible="1"/>
                                <field nolabel="1" name="json_popover" widget="stock_rescheduling_popover" attrs="{'invisible': [('json_popover', '=', False)]}"/>
                            </div>
                            <field name="components_availability_state" invisible="1"/>
                            <field name="components_availability" attrs="{'invisible': [('state', 'not in', ['confirmed', 'progress'])]}"
                                decoration-success="reservation_state == 'assigned' or components_availability_state == 'available'"
                                decoration-warning="reservation_state != 'assigned' and components_availability_state in ('expected', 'available')"
                                decoration-danger="reservation_state != 'assigned' and components_availability_state == 'late'"/>
                            <field name="user_id"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" attrs="{'readonly': [('state', '!=', 'draft')]}" force_save="1"/>
                            <field name="show_final_lots" invisible="1"/>
                            <field name="production_location_id" invisible="1" readonly="1"/>
                            <field name="move_finished_ids" invisible="1" attrs="{'readonly': ['|', ('state', '=', 'cancel'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}">
                                <tree editable="bottom">
                                    <field name="product_id"/>
                                    <field name="product_uom_qty"/>
                                    <field name="product_uom"/>
                                    <field name="operation_id"/>
                                    <field name="byproduct_id"/>
                                    <field name="name"/>
                                    <field name="date_deadline"/>
                                    <field name="picking_type_id"/>
                                    <field name="location_id"/>
                                    <field name="location_dest_id"/>
                                    <field name="company_id"/>
                                    <field name="warehouse_id"/>
                                    <field name="origin"/>
                                    <field name="group_id"/>
                                    <field name="propagate_cancel"/>
                                    <field name="move_dest_ids"/>
                                    <field name="state"/>
                                    <!-- Useless as the editable in tree declaration -> For Form Test-->
                                    <field name="product_uom_category_id"/>
                                    <field name="allowed_operation_ids"/>
                                </tree>
                            </field>
                        </group>
                    </group>
                    <notebook>
                        <page string="Components" name="components">
                            <field name="move_raw_ids"
                                context="{'default_date': date_planned_start, 'default_date_deadline': date_deadline, 'default_location_id': location_src_id, 'default_location_dest_id': production_location_id, 'default_state': 'draft', 'default_raw_material_production_id': id, 'default_picking_type_id': picking_type_id, 'default_company_id': company_id}"
                                attrs="{'readonly': ['|', ('state', '=', 'cancel'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}" options="{'delete': [('state', '=', 'draft')]}">
                                <tree default_order="is_done,sequence" editable="bottom">
                                    <field name="product_id" force_save="1" required="1" context="{'default_detailed_type': 'product'}" attrs="{'readonly': ['|', '|', ('move_lines_count', '&gt;', 0), ('state', '=', 'cancel'), '&amp;', ('state', '!=', 'draft'), ('additional', '=', False) ]}"/>
                                    <field name="location_id" string="From" readonly="1" groups="stock.group_stock_multi_locations" optional="show"/>
                                    <field name="move_line_ids" invisible="1">
                                        <tree>
                                            <field name="lot_id" invisible="1"/>
                                            <field name="owner_id" invisible="1"/>
                                            <field name="package_id" invisible="1"/>
                                            <field name="result_package_id" invisible="1"/>
                                            <field name="location_id" invisible="1"/>
                                            <field name="location_dest_id" invisible="1"/>
                                            <field name="qty_done" invisible="1"/>
                                            <field name="product_id" invisible="1"/>
                                            <field name="product_uom_id" invisible="1"/>
                                            <field name="product_uom_qty" invisible="1"/>
                                            <field name="state" invisible="1"/>
                                            <field name="move_id" invisible="1"/>
                                            <field name="id" invisible="1"/>
                                        </tree>
                                    </field>
                                    
                                    <field name="propagate_cancel" invisible="1"/>
                                    <field name="price_unit" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="name" invisible="1"/>
                                    <field name="allowed_operation_ids" invisible="1"/>
                                    <field name="unit_factor" invisible="1"/>
                                    <field name="date_deadline" invisible="1" force_save="1"/>
                                    <field name="date" invisible="1"/>
                                    <field name="additional" invisible="1"/>
                                    <field name="picking_type_id" invisible="1"/>
                                    <field name="has_tracking" invisible="1"/>
                                    <field name="operation_id" invisible="1"/>
                                    <field name="is_done" invisible="1"/>
                                    <field name="bom_line_id" invisible="1"/>
                                    <field name="sequence" invisible="1"/>
                                    <field name="location_id" invisible="1"/>
                                    <field name="warehouse_id" invisible="1"/>
                                    <field name="is_locked" invisible="1"/>
                                    <field name="move_lines_count" invisible="1"/>
                                    <field name="location_dest_id" domain="[('id', 'child_of', parent.location_dest_id)]" invisible="1"/>
                                    <field name="state" invisible="1" force_save="1"/>
                                    <field name="should_consume_qty" invisible="1"/>
                                    <field name="product_uom_qty" widget="mrp_should_consume" force_save="1" string="To Consume" attrs="{'readonly': ['&amp;', ('parent.state', '!=', 'draft'), '|', '&amp;', ('parent.state', 'not in', ('confirmed', 'progress', 'to_close')), ('parent.is_planned', '!=', True), '&amp;', ('state', '!=', 'draft'), ('parent.is_locked', '=', True)]}" width="1"/>
                                    <field name="product_uom" attrs="{'readonly': [('state', '!=', 'draft'), ('id', '!=', False)]}" options="{'no_open': True, 'no_create': True}" groups="uom.group_uom"/>
                                    <field name="product_type" invisible="1"/>
                                    <field name="product_qty" invisible="1" readonly="1"/>
                                    <field name="reserved_availability" invisible="1"/>
                                    <field name="forecast_expected_date" invisible="1"/>
                                    <!-- Button are used in state draft to doesn't have the name of the column "Reserved"-->
                                    <button type="object" name="action_product_forecast_report" icon="fa-area-chart" attrs="{'column_invisible': [('parent.state', '!=', 'draft')], 'invisible': [('forecast_availability', '&lt;', 0)]}"/>
                                    <button type="object" name="action_product_forecast_report" icon="fa-area-chart text-danger" attrs="{'column_invisible': [('parent.state', '!=', 'draft')], 'invisible': [('forecast_availability', '&gt;=', 0)]}"/>
                                    <field name="forecast_availability" string="Reserved" attrs="{'column_invisible': [('parent.state', 'in', ('draft', 'done'))]}" widget="forecast_widget"/>
                                    <field name="is_quantity_done_editable" invisible="1"/>
                                    <field name="quantity_done" string="Consumed"
                                        decoration-success="not is_done and (quantity_done - should_consume_qty == 0)"
                                        decoration-warning="not is_done and (quantity_done - should_consume_qty &gt; 0.0001)"
                                        attrs="{'column_invisible': [('parent.state', '=', 'draft')], 'readonly': [('has_tracking', '!=','none')]}"/>
                                    <field name="show_details_visible" invisible="1"/>
                                    <field name="lot_ids" widget="many2many_tags"
                                        groups="stock.group_production_lot" optional="hide"
                                        attrs="{'invisible': ['|', '|', ('show_details_visible', '=', False), ('has_tracking', '!=', 'serial'), ('parent.state', '=', 'draft')],
                                                'readonly': ['|', '|', ('show_details_visible', '=', False), ('has_tracking', '!=', 'serial'), ('parent.state', '=', 'draft')],
                                                'column_invisible': [('parent.show_lot_ids', '=',  False)]}"
                                        options="{'create': [('parent.use_create_components_lots', '!=', False)]}"
                                        context="{'default_company_id': company_id, 'default_product_id': product_id}"
                                        domain="[('product_id','=',product_id)]"
                                    />
                                    <field name="group_id" invisible="1"/>
                                    <button name="action_show_details" type="object" icon="fa-list" context="{'default_product_uom_qty': 0}" attrs="{'invisible': ['|', ('show_details_visible', '=', False), ('has_tracking', '=','none')]}" options="{&quot;warn&quot;: true}"/>
                                    <button class="o_optional_button" name="action_show_details" type="object" icon="fa-list" context="{'default_product_uom_qty': 0}" attrs="{'invisible': ['|', ('has_tracking', '!=','none'), ('show_details_visible', '=', False)]}" options="{&quot;warn&quot;: true}"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Work Orders" name="operations" groups="mrp.group_mrp_routings">
                            <field name="workorder_ids" attrs="{'readonly': [('state', 'in', ['cancel', 'done'])]}" context="{'tree_view_ref': 'mrp.mrp_production_workorder_tree_editable_view', 'default_product_uom_id': product_uom_id, 'from_manufacturing_order': True}"/>
                        </page>
                        <page string="By-Products" name="finished_products" groups="mrp.group_mrp_byproducts">
                            <field name="move_byproduct_ids" context="{'default_date': date_planned_finished, 'default_date_deadline': date_deadline, 'default_location_id': production_location_id, 'default_location_dest_id': location_dest_id, 'default_state': 'draft', 'default_production_id': id, 'default_picking_type_id': picking_type_id, 'default_company_id': company_id}" attrs="{'readonly': ['|', ('state', '=', 'cancel'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}" options="{'delete': [('state', '=', 'draft')]}">
                                <tree default_order="is_done,sequence" decoration-muted="is_done" editable="bottom">
                                    <field name="byproduct_id" invisible="1"/>
                                    <field name="product_id" context="{'default_detailed_type': 'product'}" domain="[('id', '!=', parent.product_id)]" required="1"/>
                                    <field name="location_dest_id" string="To" readonly="1" groups="stock.group_stock_multi_locations"/>

                                    <field name="move_line_ids" invisible="1">
                                        <tree>
                                            <field name="lot_id" invisible="1"/>
                                            <field name="owner_id" invisible="1"/>
                                            <field name="package_id" invisible="1"/>
                                            <field name="result_package_id" invisible="1"/>
                                            <field name="location_id" invisible="1"/>
                                            <field name="location_dest_id" invisible="1"/>
                                            <field name="qty_done" invisible="1"/>
                                            <field name="product_id" invisible="1"/>
                                            <field name="product_uom_id" invisible="1"/>
                                            <field name="product_uom_qty" invisible="1"/>
                                            <field name="state" invisible="1"/>
                                            <field name="move_id" invisible="1"/>
                                            <field name="id" invisible="1"/>
                                        </tree>
                                    </field>

                                    <field name="company_id" invisible="1"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="name" invisible="1"/>
                                    <field name="allowed_operation_ids" invisible="1"/>
                                    <field name="unit_factor" invisible="1"/>
                                    <field name="date" invisible="1"/>
                                    <field name="additional" invisible="1"/>
                                    <field name="picking_type_id" invisible="1"/>
                                    <field name="has_tracking" invisible="1"/>
                                    <field name="operation_id" invisible="1"/>
                                    <field name="is_done" invisible="1"/>
                                    <field name="bom_line_id" invisible="1"/>
                                    <field name="sequence" invisible="1"/>
                                    <field name="location_id" invisible="1"/>
                                    <field name="warehouse_id" invisible="1"/>
                                    <field name="is_locked" invisible="1"/>
                                    <field name="move_lines_count" invisible="1"/>
                                    <field name="location_dest_id" domain="[('id', 'child_of', parent.location_dest_id)]" invisible="1"/>
                                    <field name="state" invisible="1" force_save="1"/>
                                    <field name="product_uom_qty" string="To Produce" force_save="1" attrs="{'readonly': ['&amp;', ('parent.state', '!=', 'draft'), '|', '&amp;', ('parent.state', 'not in', ('confirmed', 'progress', 'to_close')), ('parent.is_planned', '!=', True), ('parent.is_locked', '=', True)]}"/>
                                    <field name="is_quantity_done_editable" invisible="1"/>
                                    <field name="quantity_done" string="Produced" attrs="{'column_invisible': [('parent.state', '=', 'draft')], 'readonly': [('is_quantity_done_editable', '=', False)]}"/>
                                    <field name="product_uom" groups="uom.group_uom"/>
                                    <field name="cost_share" optional="hide"/>
                                    <field name="show_details_visible" invisible="1"/>
                                    <field name="lot_ids" widget="many2many_tags"
                                        groups="stock.group_production_lot"
                                        attrs="{'invisible': ['|', '|', ('show_details_visible', '=', False), ('has_tracking', '!=', 'serial'), ('parent.state', '=', 'draft')]}"
                                        options="{'create': [('parent.use_create_components_lots', '!=', False)]}"
                                        context="{'default_company_id': company_id, 'default_product_id': product_id}"
                                        domain="[('product_id','=',product_id)]"
                                    />
                                    <button name="action_show_details" type="object" icon="fa-list" attrs="{'invisible': ['|', ('has_tracking', '=','none'), ('show_details_visible', '=', False)]}" options="{&quot;warn&quot;: true}"/>
                                    <button class="o_optional_button" name="action_show_details" type="object" icon="fa-list" attrs="{'invisible': ['|', ('has_tracking', '!=','none'), ('show_details_visible', '=', False)]}" options="{&quot;warn&quot;: true}"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Miscellaneous" name="miscellaneous">
                            <group>
                                <group>
                                    <field name="picking_type_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="location_src_id" groups="stock.group_stock_multi_locations" options="{'no_create': True}" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="location_dest_id" groups="stock.group_stock_multi_locations" options="{'no_create': True}" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                                <group>
                                    <field name="origin"/>
                                    <field name="date_deadline"
                                        attrs="{'invisible': ['|', ('state', 'in', ('done', 'cancel')), ('date_deadline', '=', False)]}"
                                        decoration-danger="date_deadline and date_deadline &lt; current_date"
                                        decoration-bf="date_deadline and date_deadline &lt; current_date"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
                </form>
            </field>
        </record>

        <record id="mrp_production_kanban_view" model="ir.ui.view">
            <field name="name">mrp.production.kanban</field>
            <field name="model">mrp.production</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1">
                    <field name="name"/>
                    <field name="product_id"/>
                    <field name="product_qty"/>
                    <field name="product_uom_id" options="{'no_open':True,'no_create':True}"/>
                    <field name="date_planned_start"/>
                    <field name="state"/>
                    <field name="activity_state"/>
                    <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <field name="priority" widget="priority"/>
                                    <div class="o_kanban_record_headings mt4 ml-1">
                                        <strong class="o_kanban_record_title"><span><t t-esc="record.product_id.value"/></span></strong>
                                    </div>
                                    <span class="float-right text-right"><t t-esc="record.product_qty.value"/> <small><t t-esc="record.product_uom_id.value"/></small></span>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left text-muted">
                                        <span><t t-esc="record.name.value"/> <t t-esc="record.date_planned_start.value and record.date_planned_start.value.split(' ')[0] or False"/></span>
                                        <field name="activity_ids" widget="kanban_activity"/>
                                        <field name="delay_alert_date" invisible="1"/>
                                        <field nolabel="1" name="json_popover" widget="stock_rescheduling_popover" attrs="{'invisible': [('json_popover', '=', False)]}"/>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span t-attf-class="badge #{['draft', 'cancel'].indexOf(record.state.raw_value) > -1 ? 'badge-secondary' : ['none'].indexOf(record.state.raw_value) > -1 ? 'badge-danger' : ['confirmed'].indexOf(record.state.raw_value) > -1 ? 'badge-warning' : ['done'].indexOf(record.state.raw_value) > -1 ? 'badge-success' : 'badge-primary'}"><t t-esc="record.state.value"/></span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_production_calendar" model="ir.ui.view">
            <field name="name">mrp.production.calendar</field>
            <field name="model">mrp.production</field>
            <field eval="2" name="priority"/>
            <field name="arch" type="xml">
                <calendar date_start="date_planned_start" date_stop="date_planned_finished"
                          string="Manufacturing Orders" event_limit="5" quick_add="False">
                    <field name="user_id" avatar_field="avatar_128"/>
                    <field name="product_id"/>
                    <field name="product_qty"/>
                </calendar>
            </field>
        </record>

        <record id="view_production_pivot" model="ir.ui.view">
            <field name="name">mrp.production.pivot</field>
            <field name="model">mrp.production</field>
            <field name="arch" type="xml">
                <pivot string="Manufacturing Orders" sample="1">
                    <field name="date_planned_start" type="row"/>
                </pivot>
            </field>
        </record>

        <record id="view_production_graph" model="ir.ui.view">
            <field name="name">mrp.production.graph</field>
            <field name="model">mrp.production</field>
            <field name="arch" type="xml">
                <graph string="Manufacturing Orders" stacked="0" sample="1">
                    <field name="date_planned_finished"/>
                    <field name="product_uom_qty" type="measure"/>
                    <field name="backorder_sequence" invisible="1"/>
                    <field name="qty_producing" string="Quantity Produced"/>
                    <field name="product_uom_qty" string= "Product Quantity"/>
                </graph>
            </field>
        </record>

        <record id="view_mrp_production_filter" model="ir.ui.view">
            <field name="name">mrp.production.select</field>
            <field name="model">mrp.production</field>
            <field name="arch" type="xml">
                <search string="Search Production">
                    <field name="name" string="Manufacturing Order" filter_domain="['|', ('name', 'ilike', self), ('origin', 'ilike', self)]"/>
                    <field name="product_id"/>
                    <field name="move_raw_ids" string="Component" filter_domain="[('move_raw_ids.product_id', 'ilike', self)]"/>
                    <field name="name" string="Work Center" filter_domain="[('bom_id.operation_ids.workcenter_id', 'ilike', self)]"/>
                    <field name="origin"/>
                    <filter string="To Do" name="todo" domain="[('state', 'in', ('draft', 'confirmed', 'progress', 'to_close'))]"
                        help="Manufacturing Orders which are in confirmed state."/>
                    <filter string="Starred" name="starred" domain="[('priority', '=', '1')]"/>
                    <separator/>
                    <filter string="Draft" name="filter_draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Confirmed" name="filter_confirmed" domain="[('state', '=', 'confirmed')]"/>
                    <filter string="Planned" name="filter_planned" domain="[('is_planned', '=', True)]" groups="mrp.group_mrp_routings"/>
                    <filter string="In Progress" name="filter_in_progress" domain="[('state', '=', 'progress')]"/>
                    <filter string="To Close" name="filter_to_close" domain="[('state', '=', 'to_close')]"/>
                    <filter string="Done" name="filter_done" domain="[('state', '=', 'done')]"/>
                    <filter string="Cancelled" name="filter_cancel" domain="[('state', '=', 'cancel')]"/>
                    <separator/>
                    <filter string="Waiting" name="waiting" domain="[('reservation_state', 'in', ('waiting', 'confirmed'))]"/>
                    <filter string="Ready" name="filter_ready" domain="[('reservation_state', '=', 'assigned')]"/>
                    <separator/>
                    <filter string="Planning Issues" name="planning_issues" help="Late MO or Late delivery of components"
                        domain="['|', ('delay_alert_date', '!=', False), '&amp;', ('date_deadline', '&lt;', current_date), ('state', '=', 'confirmed')]"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter name="filter_date_planned_start" string="Scheduled Date" date="date_planned_start"/>
                    <filter name="filter_plan_date" invisible="1" string="Scheduled Date: Last 365 Days" domain="[('date_planned_start', '>', (datetime.datetime.now() + relativedelta(days=-365)).to_utc().strftime('%Y-%m-%d %H:%M:%S'))]"/>
                    <separator/>
                    <filter string="Warnings" name="activities_exception"
                        domain="[('activity_exception_decoration', '!=', False)]"/>
                    <group expand="0" string="Group By...">
                        <filter string="Product" name="product" domain="[]" context="{'group_by': 'product_id'}"/>
                        <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Material Availability" name="groupby_reservation_state" domain="[]" context="{'group_by': 'reservation_state'}"/>
                        <filter string="Scheduled Date" name="scheduled_date" domain="[]" context="{'group_by': 'date_planned_start'}" help="Scheduled Date by Month"/>
                    </group>
               </search>
            </field>
        </record>

        <record id="mrp_production_action" model="ir.actions.act_window">
            <field name="name">Manufacturing Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.production</field>
            <field name="view_mode">tree,kanban,form,calendar,pivot,graph</field>
            <field name="view_id" eval="False"/>
            <field name="search_view_id" ref="view_mrp_production_filter"/>
            <field name="context">{'search_default_todo': True, 'default_company_id': allowed_company_ids[0]}</field>
            <field name="domain">[('picking_type_id.active', '=', True)]</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No manufacturing order found. Let's create one.
              </p><p>
                Consume <a name="%(product.product_template_action)d" type='action' tabindex="-1">components</a> and build finished products using <a name="%(mrp_bom_form_action)d" type='action' tabindex="-1">bills of materials</a>
              </p>
            </field>
        </record>

        <record id="mrp_production_action_picking_deshboard" model="ir.actions.act_window">
            <field name="name">Manufacturing Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.production</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" eval="False"/>
            <field name="search_view_id" ref="view_mrp_production_filter"/>
            <field name="domain">[('picking_type_id', '=', active_id)]</field>
            <field name="context">{'default_picking_type_id': active_id}</field>
        </record>

        <record id="mrp_production_action_unreserve_tree" model="ir.actions.server">
            <field name="name">Unreserve</field>
            <field name="model_id" ref="mrp.model_mrp_production"/>
            <field name="binding_model_id" ref="mrp.model_mrp_production"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                records.do_unreserve()
            </field>
        </record>

        <menuitem action="mrp_production_action"
            id="menu_mrp_production_action"
            parent="menu_mrp_manufacturing"
            sequence="1"/>

        <record id="mrp_production_report" model="ir.actions.act_window">
            <field name="name">Manufacturing Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.production</field>
            <field name="view_mode">graph,pivot,form</field>
            <field name="target">current</field>
            <field name="context">{
                'search_default_product': 1,
                'search_default_scheduled_date': 2,
                'search_default_filter_confirmed': True,
                'search_default_filter_planned': True,
                'default_company_id': allowed_company_ids[0],
                'allowed_company_ids': allowed_company_ids
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data yet!
                </p><p>
                    Create a new manufacturing order
                </p>
            </field>
        </record>

        <menuitem id="menu_mrp_workorder_todo"
                name="Work Orders"
                action="mrp_workorder_todo"
                parent="menu_mrp_manufacturing"
                groups="group_mrp_routings"/>

        <menuitem id="menu_mrp_production_report"
            name="Manufacturing Orders"
            parent="menu_mrp_reporting"
            action="mrp_production_report"
            sequence="11"/>

        <menuitem id="menu_mrp_work_order_report"
            name="Work Orders"
            parent="menu_mrp_reporting"
            action="mrp_workorder_report"
            groups="group_mrp_routings"
            sequence="10"/>

        <record id="action_mrp_production_form" model="ir.actions.act_window">
            <field name="name">Manufacturing Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.production</field>
            <field name="view_mode">form</field>
        </record>
    </data>
</odoo>
