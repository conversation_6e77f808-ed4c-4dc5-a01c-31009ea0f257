# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_transfer
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: payment_transfer
#: model_terms:ir.ui.view,arch_db:payment_transfer.transfer_transaction_status
msgid "<strong>Communication: </strong>"
msgstr "<strong>İletişim: </strong>"

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__qr_code
msgid "Enable QR Codes"
msgstr "QR Kodlarını Etkinleştir"

#. module: payment_transfer
#: model:ir.model.fields,help:payment_transfer.field_payment_acquirer__qr_code
msgid "Enable the use of QR-codes when paying by wire transfer."
msgstr ""
"Banka havalesiyle ödeme yaparken QR kodlarının kullanılmasını etkinleştirin."

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Referans %s eşleşen bir işlem bulunamadı."

#. module: payment_transfer
#: model_terms:ir.ui.view,arch_db:payment_transfer.transfer_transaction_status
msgid "Or scan me with your banking app."
msgstr "Ya da bankacılık uygulamanızla beni tarayın."

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Ödeme Alıcısı"

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_transaction
msgid "Payment Transaction"
msgstr "Ödeme İşlemi"

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__provider
msgid "Provider"
msgstr "Sağlayıcı"

#. module: payment_transfer
#: model:ir.model.fields,help:payment_transfer.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "Bu alıcı ile birlikte kullanılacak Ödeme Hizmeti Sağlayıcısı"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_transaction.py:0
#, python-format
msgid "The customer has selected %(acq_name)s to make the payment."
msgstr "Müşteri ödemeyi yapmak için %(acq_name)s seçmiştir."

#. module: payment_transfer
#: model:ir.model.fields.selection,name:payment_transfer.selection__payment_acquirer__provider__transfer
msgid "Wire Transfer"
msgstr "Manuel Transfer"
