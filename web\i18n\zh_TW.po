# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr "記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr "# 代碼編輯器"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "%d 天前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "%d 小時前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "%d 分鐘前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "%d 月前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "%d 年前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_mixin.js:0
#, python-format
msgid "%s Files"
msgstr "%s 檔案"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "%s days ago"
msgstr "%s幾天前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' 不是正確的日期"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/dates.js:0
#, python-format
msgid "'%s' is not a correct date or datetime"
msgstr "“%s”不是正確的日期或日期時間"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s'不是正確的日期、日期時間或時間"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' 不是正確的日期時間"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' 不是正確的浮點數"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' 不是正確的整數"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr "『%s』 不是正確的貨幣欄位"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' 無法轉換為日期、日期時間或時間"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "'%s' is unsynchronized with '%s'."
msgstr "“%s”與“%s”不同步。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#, python-format
msgid "(%s/%sMb)"
msgstr "(%s/%sMb)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(change)"
msgstr "(更改)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr "(個數)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "(no result)"
msgstr "(無結果)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(no string)"
msgstr "(無字元串)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(無備註)"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "07/08/2020"
msgstr "07/08/2020"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "08/07/2020"
msgstr "08/07/2020"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "1 record"
msgstr "1條記錄"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ 2,887.50</span>"
msgstr "<span class=\"text-nowrap\">$ 2,887.50</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">1,500.00</span>"
msgstr "<span class=\"text-nowrap\">1,500.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">2,350.00</span>"
msgstr "<span class=\"text-nowrap\">2,350.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">Tax 15%</span>"
msgstr "<span class=\"text-nowrap\">Tax 15%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                                   Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"
msgstr ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 聖巴巴拉路\n"
"                                                   <br/>普萊森特山 CA 94523<br/>美國</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span id=\"line_tax_ids\">15.00%</span>"
msgstr "<span id=\"line_tax_ids\">15.00%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span itemprop=\"name\">Deco Addict</span>"
msgstr "<span itemprop=\"name\">Deco Addict</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"
msgstr "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>5.000</span>"
msgstr "<span>5.000</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Amount</span>"
msgstr "<span>數量</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Description</span>"
msgstr "<span>說明</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>Invoice</span>\n"
"                           <span>INV/2020/07/0003</span>"
msgstr ""
"<span>應收憑單</span>\n"
"                        <span>INV/2020/07/0003</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Payment terms: 30 Days</span>"
msgstr "<span>付款條件：30 天</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Quantity</span>"
msgstr "<span>數量</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Taxes</span>"
msgstr "<span>稅金</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Unit Price</span>"
msgstr "<span>單價</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8220] Four Person Desk<br/>\n"
"                                       Four person modern office workstation</span>"
msgstr ""
"<span>[FURN_8220] 四人桌<br/>\n"
"                                       四人現代辦公工作站</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8999] Three-Seat Sofa<br/>\n"
"                                       Three Seater Sofa with Lounger in Steel Grey Colour</span>"
msgstr ""
"<span>[FURN_8999] 三人沙發<br/>\n"
"                                       三人座沙發，帶鋼灰色躺椅</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Due Date:</strong>"
msgstr "<strong>到期日期:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>憑單日期:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Subtotal</strong>"
msgstr "<strong>小計</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Total</strong>"
msgstr "<strong>總計</strong>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A filter with same name already exists."
msgstr "已存在同名過濾器"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A name for your favorite filter is required."
msgstr "您的最愛內之過濾器名稱是必需的。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr "彈出窗口已被封鎖。您可能需要更改瀏覽器設定以允許此頁面的彈出窗口。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr "所有"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr "任何"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Denied"
msgstr "存取被拒絕"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Error"
msgstr "存取錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "存取全部企業版應用"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Action"
msgstr "動作"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Action ID:"
msgstr "動作ID:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "動作窗視圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Assets Debugging"
msgstr "啟用資產偵錯"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr "啟動測試資產偵錯"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Activate debug mode"
msgstr "啟動Debug模式"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add"
msgstr "增加"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Add %s"
msgstr "添加 %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "添加自訂篩選"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Add Custom Group"
msgstr "加入自訂分組"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add a Column"
msgstr "添加一列"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add a condition"
msgstr "添加條件"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Add a line"
msgstr "添加明細行"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr "添加分支"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add column"
msgstr "添加列"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr "添加篩選"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr "添加新的值"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr "添加節點"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Add qweb directive context"
msgstr "添加 qweb 指令上下文"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr "添加標籤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr "添加到最愛"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Additional actions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "添加： "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr "發出您的簽章"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt and Sign"
msgstr "核可與簽章"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr "警示"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/search_panel_model_extension.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_arch_parser.js:0
#, python-format
msgid "All"
msgstr "所有"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "All day"
msgstr "全天"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "所有使用者"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Alt"
msgstr "Alt"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Among the"
msgstr "在以下項目中:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "An error occurred"
msgstr "發生了錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "And more"
msgstr "更多"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Any"
msgstr "任何"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_header
msgid ""
"Appears by default on the top right corner of your printed documents (report"
" header)."
msgstr "將出現在列印的報表的右上角(報表頁首)。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Apply"
msgstr "套用"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr "歸檔"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Archive All"
msgstr "歸檔所有"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr "您確定要歸檔此列中的所有記錄嗎？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr "您確定要歸檔所有選定的記錄嗎？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr "是否確實要存檔此記錄？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "您確定要移除此列嗎？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "您確定要移除此篩選嗎？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records ?"
msgstr "您確定要刪除這些記錄嗎？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records?"
msgstr "您確定要刪除這些記錄嗎？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "您確定要刪除此記錄嗎？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "您確定要刪除此記錄嗎？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr "是否確實要對這些更新執行以下更新"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Ascending"
msgstr "升序"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Attach"
msgstr "附加"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Attachment"
msgstr "附件"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr "自動"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Available fields"
msgstr "可用欄位"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Avatar"
msgstr "頭像"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background_image
msgid "Background Image"
msgstr "背景圖片"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#, python-format
msgid "Badge"
msgstr "徽章"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Badges"
msgstr "徽章"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Bar Chart"
msgstr "柱狀圖"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "基礎"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Become Superuser"
msgstr "成為超級使用者"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr "二進制欄位不能匯出到Excel，除非它們的內容是Base64編碼。這似乎不適用於 %s。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Binary file"
msgstr "二進位文件"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr "補丁修復保證"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Button"
msgstr "按鈕"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Button Type:"
msgstr "按鈕類型："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt and Sign, I agree that the chosen signature/initials will "
"be a valid electronic representation of my hand-written signature/initials "
"for all purposes when it is used on documents, including legally binding "
"contracts."
msgstr "通過按一下\"核閱與簽章\"，我同意所簽名為有效的電子方式表示本人的意思表示，當它用於文件（包括具有法律約束力的合約）的簽核與驗證。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar"
msgstr "日曆"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Calendar toolbar"
msgstr "日曆工具欄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr "日曆視圖未定義 'date_start'的屬性。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/file_upload_progress_bar.xml:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_bar.xml:0
#, python-format
msgid "Cancel Upload"
msgstr "取消上傳"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr "卡片顏色： %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/change_password.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Change Password"
msgstr "更改密碼"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Change default:"
msgstr "更改預設："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Change graph"
msgstr "變化圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/_deprecated/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#, python-format
msgid "Checkbox"
msgstr "複選框"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Checkboxes"
msgstr "複選框"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Choose"
msgstr "選擇"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/file_input/file_input.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Choose File"
msgstr "選擇檔案"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Choose a debug command..."
msgstr "選擇Debug命令..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Clear"
msgstr "清除"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr "清除簽名"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/notifications/notification.xml:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/dialog.xml:0
#, python-format
msgid "Close"
msgstr "關閉"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Colors"
msgstr "配色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr "欄 %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Column title"
msgstr "專欄標題"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Command"
msgstr "命令"

#. module: web
#: model:ir.model,name:web.model_res_company
msgid "Companies"
msgstr "公司"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_id
msgid "Company"
msgstr "公司"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_details
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Company Details"
msgstr "公司詳情"

#. module: web
#: model:ir.model,name:web.model_base_document_layout
msgid "Company Document Layout"
msgstr "公司文件佈局"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo
msgid "Company Logo"
msgstr "公司 Logo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__name
msgid "Company Name"
msgstr "公司名稱"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_header
msgid "Company Tagline"
msgstr "公司標語"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr "公司名稱"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/comparison_menu/comparison_menu.xml:0
#, python-format
msgid "Comparison"
msgstr "比較"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "條件:"

#. module: web
#: model:ir.actions.act_window,name:web.action_base_document_layout_configurator
msgid "Configure your document layout"
msgstr "配置您的文件佈局"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "確認"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection lost. Trying to reconnect..."
msgstr "連接中斷。正在嘗試重新連接..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection restored. You are back online."
msgstr "連接已恢復。您已重新上線。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Context:"
msgstr "上下文:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Control"
msgstr "Ctrl"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Control panel buttons"
msgstr "控制面板按鈕"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copied !"
msgstr "已複製 !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Copy"
msgstr "複製"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr "複製完整錯誤到剪貼板"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr "複製到剪貼簿"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Copyright &amp;copy;"
msgstr "版權所有 &amp;copy;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr "未能連接至伺服器"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Could not display the selected image"
msgstr "無法顯示所選圖像"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Could not display the specified image url."
msgstr "無法顯示指定的圖片網址。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr "不能序列化 XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr "無法設置封面圖像：視圖中提供了不正確的欄位 （\"%s\"）。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/helpers/utils.js:0
#, python-format
msgid "Count"
msgstr "個數"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__country_id
msgid "Country"
msgstr "國家"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Create"
msgstr "建立"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr "建立"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "建立  \"<strong>%s</strong>\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Create a new record"
msgstr "建立新紀錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr "建立並編輯..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Create record"
msgstr "建立記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create: %s"
msgstr "建立：%s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_uid
msgid "Created by"
msgstr "創立者"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_date
msgid "Created on"
msgstr "建立於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation Date:"
msgstr "建立日期："

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation User:"
msgstr "建立使用者："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr "目前狀態"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__custom_colors
msgid "Custom Colors"
msgstr "自訂顏色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark blue"
msgstr "深藍色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark purple"
msgstr "深紫色"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "資料庫"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Date"
msgstr "日期"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Date & Time"
msgstr "日期＆時間"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Day"
msgstr "日"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Deactivate debug mode"
msgstr "停用debug模式"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.debug_icon
msgid ""
"Debug mode is activated#{debug_mode_help}. Click here to exit debug mode."
msgstr "開發者模式已啟動[debug_mode_help]。點選此處退出開發者模式。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Debug tools..."
msgstr "Debug工具..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Decimal"
msgstr "十進制"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Default"
msgstr "預設"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Default:"
msgstr "預設："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Delete"
msgstr "刪除"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Delete item"
msgstr "刪除項目"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr "刪除節點"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr "刪除行"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Descending"
msgstr "降序"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Description"
msgstr "說明"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard"
msgstr "取消"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Discard a record modification"
msgstr "取消記錄修改"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard record"
msgstr "捨棄記錄"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_bar.js:0
#, python-format
msgid "Do you really want to cancel the upload of %s?"
msgstr "您確定要取消上傳 %s 嗎？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr "您真的要刪除此匯出模板？"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__external_report_layout_id
msgid "Document Template"
msgstr "文件模板"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Documentation"
msgstr "系統使用說明"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain"
msgstr "Domain"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr "Domain節點"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not properly formed"
msgstr "Domain未正確形成"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not supported"
msgstr "不支持篩選條件"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain:"
msgstr "Domain名稱："

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Don't leave yet,"
msgstr "還不要離開"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "請不要離開，<br />它還在加載中..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Download"
msgstr "下載"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Download PDF Preview"
msgstr "下載 PDF 預覽"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Download xlsx"
msgstr "下載 xlsx"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr "畫線"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "下拉選單"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "複製"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Edit"
msgstr "編輯"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Edit Action"
msgstr "編輯動作"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr "編輯列"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit ControlPanelView"
msgstr "編輯控制台視圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit Domain"
msgstr "編輯domain"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Edit Stage"
msgstr "編輯階段"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit View: "
msgstr "編輯視圖："

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Edit a record"
msgstr "編輯記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit record"
msgstr "編輯記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__email
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "電郵"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Enable profiling"
msgstr "啟用分析"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Error, password not changed !"
msgstr "錯誤，密碼未更改！"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Esc to discard"
msgstr "按Esc取消"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr "每一個人的日曆"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr "一切"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Expand all"
msgstr "展開全部"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Export"
msgstr "匯出"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Export All"
msgstr "匯出全部"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Export Data"
msgstr "匯出資料"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Export Format:"
msgstr "匯出格式："

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr "不支援將分組資料匯出到 csv。"

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "External ID"
msgstr "外部 ID"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "External link"
msgstr "外部連結"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/control_panel_model_extension.js:0
#, python-format
msgid "Failed to evaluate search context"
msgstr "無法評估搜索上下文"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr "否"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Favorites"
msgstr "最愛"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Field:"
msgstr "欄位："

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Fields View Get"
msgstr "欄位視圖獲取"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fields to export"
msgstr "要匯出的欄位"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File"
msgstr "文件"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr "文件上傳"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr "以相同名字的篩選已經存在。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/filter_menu.xml:0
#, python-format
msgid "Filters"
msgstr "篩選"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Flip axis"
msgstr "翻轉軸"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Fold"
msgstr "收攏"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr "關注者"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__font
msgid "Font"
msgstr "字體"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Footer"
msgstr "頁尾"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "顯示在所有報告下方的頁尾文字。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_controller.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 16384 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""
"對於 Excel 兼容性，如果列數超過 16384，則無法匯出資料。\n"
"\n"
"提示：嘗試翻轉軸，進一步過濾或減少所選資料"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "表單"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr "自由視圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "完整名稱"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fushia"
msgstr "紫紅色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr "通過Odoo企業版獲得該功能及其他更多！"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "圖形"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Green"
msgstr "綠色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_controller.js:0
#: code:addons/web/static/src/search/group_by_menu/group_by_menu.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Group By"
msgstr "分組依據"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Handle"
msgstr "處理"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr "顯示在所有報告頂部的標題文字"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr "在看板中隱藏"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr "點選下鍵導航到下面的列表"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr "點選 ENTER 來"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr "點選 ENTER 來建立"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr "點選 ENTER 來儲存"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr "點選 ESCAPE 取消"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr "我確信這一點。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr "我想更新資料（匯入相容匯出）"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__id
msgid "ID"
msgstr "ID"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "ID:"
msgstr "ID："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid ""
"If you change %s or %s, the synchronization will be reapplied and the data "
"will be modified."
msgstr "如果更改 %s 或 %s，將重新應用同步並修改資料"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Image"
msgstr "圖像"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "In %s days"
msgstr "於%s 天後"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Integer"
msgstr "整數"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Interval"
msgstr "間隔"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Invalid data"
msgstr "無效的資料"

#. module: web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr "資料庫名稱無效。只允許字母數字字元、下劃線、連字元和點。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid domain"
msgstr "不正確的domain"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr "不正確的欄位鏈"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid ""
"Invalid field chain. You may have used a non-existing field name or followed"
" a non-relational field."
msgstr "無效的欄位鏈。您可能使用了不存在的欄位名稱或使用了非關係欄位。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Invalid fields:"
msgstr "無效欄位："

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Invalid inherit mode. Module %s and template name %s"
msgstr "繼承模式無效。模組 %s 和範本名稱 %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid ""
"It is possible that the \"t-call\" time does not correspond to the overall time of the\n"
"            template. Because the global time (in the drop down) does not take into account the\n"
"            duration which is not in the rendering (look for the template, read, inheritance,\n"
"            compilation...). During rendering, the global time also takes part of the time to make\n"
"            the profile as well as some part not logged in the function generated by the qweb."
msgstr ""
"t-call 時間可能與範本的總時間不對應。因為全域時間\n"
"            （在下拉選單中）沒有考慮繪製過程中未包含的持續時間\n"
"            （尋找範本、讀取、繼承、編譯等）。在繪製過程中，\n"
"            全域時間也計及製作設定檔的部份時間，\n"
"            以及qweb產生的函數中未記錄的部份時間。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "看板"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Kanban Examples"
msgstr "看板示例"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Kanban: no action for type: "
msgstr "看板：對類型無動作"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Languages"
msgstr "語系"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "最後修改日期："

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr "最後修改者："

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Layout"
msgstr "格式"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background
msgid "Layout Background"
msgstr "格式背景"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Leave the Developer Tools"
msgstr "離開開發者工具"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Light blue"
msgstr "淺藍"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Line Chart"
msgstr "線狀圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "列表"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "加載"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Load more... ("
msgstr "加載更多...("

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/webclient/loading_indicator/loading_indicator.xml:0
#, python-format
msgid "Loading"
msgstr "正在上載"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr "加載中，請稍候..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading..."
msgstr "載入中..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "登入"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr "以超級使用者身份登入"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Log out"
msgstr "登出"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Logo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_primary_color
msgid "Logo Primary Color"
msgstr "logo原色"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_secondary_color
msgid "Logo Secondary Color"
msgstr "logo二級原色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Mac"
msgstr "Mac"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "MailDeliveryException"
msgstr "郵件投遞異常"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Main actions"
msgstr "主要行動"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Manage Attachments"
msgstr "附件管理"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "管理資料庫"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Manage Filters"
msgstr "管理篩選"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Many2many"
msgstr "Many2many"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Many2one"
msgstr "Many2one"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match"
msgstr "匹配"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr "匹配記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr "根據以下規則匹配記錄："

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr "也許您應該考慮按下 F5 鍵重新載入網頁...ORZ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view.xml:0
#, python-format
msgid "Measures"
msgstr "度量"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Medium blue"
msgstr "中藍色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "Meeting Subject"
msgstr "會議主題"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Meeting Subject:"
msgstr "會議主題:"

#. module: web
#: model:ir.model,name:web.model_ir_ui_menu
msgid "Menu"
msgstr "選單"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Method:"
msgstr "方法："

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Missing Record"
msgstr "缺失記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Mobile support"
msgstr "支援手機設備"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Model Record Rules"
msgstr "模型記錄規則"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Modifiers:"
msgstr "修飾器："

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Module %s not loaded or inexistent, or templates of addon being loaded (%s) "
"are misordered"
msgstr "模組  %s未載入或不存在，或載入的載入元件  (%s) 的範本順序錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Monetary"
msgstr "貨幣的"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Month"
msgstr "月"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "More"
msgstr "更多"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Multiline Text"
msgstr "多行文本"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "My Odoo.com account"
msgstr "我的 Odoo.com 帳戶"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr "無"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "New"
msgstr "新增"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "New %s"
msgstr "新%s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "New Event"
msgstr "新活動"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New Password"
msgstr "新密碼"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New Password (Confirmation)"
msgstr "新密碼（確認）"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New design"
msgstr "新設計"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New template"
msgstr "新增模板"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Next"
msgstr "下一個"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Next page"
msgstr "下一頁"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "No"
msgstr "否"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "No Update:"
msgstr "無更新:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/client_actions.js:0
#, python-format
msgid "No action with id '%s' could be found"
msgstr "找不到 ID 為“%s”的動作"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "No color"
msgstr "無顏色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/default_providers.js:0
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "No commands found"
msgstr "未找到命令"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr "沒有資料"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/helpers/no_content_helpers.xml:0
#, python-format
msgid "No data to display"
msgstr "無資料可供顯示"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "No match found."
msgstr "未找到匹配項。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "No menu found"
msgstr "沒有找到選單"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "No records"
msgstr "沒有記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr "未找到記錄！"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_palette.js:0
#, python-format
msgid "No results found"
msgstr "無任何結果"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "No template found to inherit from. Module %s and template name %s"
msgstr "未找到要繼承的範本。模組 %s 和範本名稱 %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "No valid record to save"
msgstr "沒有要儲存的有效記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "No view of type '%s' could be found in the current action."
msgstr "在當前操作中找不到類型為“%s”的視圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "None"
msgstr "無"

#. module: web
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr "未設定"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr "歸檔狀態"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr "處於歸檔狀態，點選以更改它"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Object:"
msgstr "模型:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Odoo Apps 將即刻可用"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Client Error"
msgstr "Odoo客戶端錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo 企業版"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Error"
msgstr "Odoo錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Network Error"
msgstr "Odoo 網路錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Server Error"
msgstr "Odoo 伺服器錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr "操作時段過期"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Odoo警告"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid ""
"Of the %d records selected, only the first %d have been archived/unarchived."
msgstr "在所選的 %d 條記錄中，只有前 %d 條歸檔/取消歸檔。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "確定"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Old Password"
msgstr "舊密碼"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "變更時："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr "One2many"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Only employees can access this database. Please contact the administrator."
msgstr "只有員工可以存取此資料庫。請聯繫管理員。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Only the first %d records have been deleted (out of %d selected)"
msgstr "僅刪除了前 %d 條記錄（已選擇 %d 條記錄）"

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr "僅支援類別%(supported_types)s 的類型 (found type %(field_type)s)（支援類型）"

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr "僅支援篩選器的類型  %(supported_types)s 找到類型  (found type %(field_type)s) "

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "僅用於您個人"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open Command Palette"
msgstr "打開命令面板"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Open View"
msgstr "打開視圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu.xml:0
#, python-format
msgid "Open developer tools"
msgstr "開啟開發人員工具"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open the next record"
msgstr "打開下一記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open the previous record"
msgstr "打開前一記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open to kanban view"
msgstr "打開看板視圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open to list view"
msgstr "打開列表視圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "打開： "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Open: %s"
msgstr "打開：%s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr "可選列"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Orange"
msgstr "橘色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_progressbar.js:0
#, python-format
msgid "Other"
msgstr "其他"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "PDF Viewer"
msgstr "PDF 檢視器"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""
"頁:\n"
"                    <span class=\"page\"/>\n"
"                    /\n"
"                    <span class=\"topage\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr "頁: <span class=\"page\"/> / <span class=\"topage\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__paperformat_id
msgid "Paper format"
msgstr "紙張格式"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__partner_id
msgid "Partner"
msgstr "業務夥伴"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "密碼"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage"
msgstr "百分比"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr "百分比圓形圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__phone
#, python-format
msgid "Phone"
msgstr "電話"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Pick a color"
msgstr "選一種顏色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Pie Chart"
msgstr "圓餅圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Pie chart cannot mix positive and negative numbers. "
msgstr "圓餅圖不能混合正負數。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr "圓餅圖不能混用正數和負數。嘗試改變您的domain只顯示正數結果"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr "樞紐分析表"

#. module: web
#: code:addons/web/controllers/pivot.py:0
#, python-format
msgid "Pivot %(title)s (%(model_name)s)"
msgstr "樞紐 %(title)s (%(model_name)s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Pivot settings"
msgstr "樞軸設置"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Please be patient."
msgstr "請耐心等候。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr "請先點選「儲存」按鈕"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Please enter a numerical value"
msgstr "請輸入數值"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please enter save field list name"
msgstr "請輸入欄位列表的名稱"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Please save before attaching a file"
msgstr "添加文件前請先儲存"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "請選擇要匯出的欄位..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "請選擇要儲存成匯出列表的欄位..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr "請更新以下翻譯："

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr "請使用複製按鈕將錯誤報告給您的支援服務。"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"Please use the following communication for your payment : <b><span>\n"
"                           INV/2020/07/0003</span></b>"
msgstr "付款時請備註以下資訊： <b><span>INV/2020/07/0003</span></b>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr "由 %s%s 驅動"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "由 <span>Odoo</span> 驅動"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Preferences"
msgstr "個人設定"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview
msgid "Preview"
msgstr "預覽"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr "預覽外部報表"

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr "預覽內部報表"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview_logo
msgid "Preview logo"
msgstr "預覽logo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Previous"
msgstr "前一個"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Period"
msgstr "前一期間"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Year"
msgstr "前一年"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous menu"
msgstr "前一個選單"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous page"
msgstr "上一頁"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__primary_color
msgid "Primary Color"
msgstr "主要色彩"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/report.xml:0
#: code:addons/web/static/src/legacy/xml/report.xml:0
#, python-format
msgid "Print"
msgstr "列印"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/report.xml:0
#, python-format
msgid "Printing options"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Priority"
msgstr "優先級"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_card.xml:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_card.xml:0
#, python-format
msgid "Processing..."
msgstr "處理中..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Progress Bar"
msgstr "進度條"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Purple"
msgstr "紫色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q1"
msgstr "Q1"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q2"
msgstr "Q2"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q3"
msgstr "Q3"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q4"
msgstr "Q4"

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "QWeb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Quarter"
msgstr "季"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Quick add"
msgstr "快速添加"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Quick search: %s"
msgstr "快速搜尋： %s"

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
#: model:ir.model,name:web.model_ir_qweb_field_image_url
msgid "Qweb Field Image"
msgstr "Qweb圖像欄位"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGB"
msgstr "RGB"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGBA"
msgstr "RGBA"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Radio"
msgstr "單選"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record qweb"
msgstr "記錄qweb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record sql"
msgstr "記錄sql"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record traces"
msgstr "記錄軌跡"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Red"
msgstr "紅色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Refresh"
msgstr "重新整理"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr "重新生成資產包"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid "Relation not allowed"
msgstr "關聯不允許"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr "關係如下"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation:"
msgstr "關係："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remaining Days"
msgstr "剩餘天數"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Remove"
msgstr "移除"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Remove Cover Image"
msgstr "移除封面圖像"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Remove field"
msgstr "移除欄位"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr "從最愛移除"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr "刪除標籤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr "從清單中移除這個最愛"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Report"
msgstr "報表"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_footer
msgid "Report Footer"
msgstr "報表頁尾"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_layout_id
msgid "Report Layout"
msgstr "報表格式"

#. module: web
#: model:ir.actions.report,name:web.action_report_layout_preview
msgid "Report Layout Preview"
msgstr "報表格式預覽"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Request timeout"
msgstr "請求超時"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Reset to logo colors"
msgstr "重置為logo顏色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/clickbot/clickbot_loader.js:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr "執行 Click Everywhere Test 這條命令"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr "執行JS 手機端測試"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Tests"
msgstr "執行JS 測試"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "SIGNATURE"
msgstr "簽章"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Salmon pink"
msgstr "朱紅色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Save"
msgstr "儲存"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Save & Close"
msgstr "儲存並關閉"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Save & New"
msgstr "儲存並新增"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Save a record"
msgstr "儲存記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr "另存為..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Save current search"
msgstr "儲存目前搜尋"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Save default"
msgstr "儲存預設"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save record"
msgstr "儲存記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Search"
msgstr "搜尋"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Search More..."
msgstr "搜尋更多..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_service.js:0
#, python-format
msgid "Search for a command..."
msgstr "搜索命令..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search for records"
msgstr "搜索記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_palette.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Search..."
msgstr "搜尋..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Search: %s"
msgstr "搜尋 - %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__secondary_color
msgid "Secondary Color"
msgstr "次要色彩"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "See details"
msgstr "查看詳情"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "See examples"
msgstr "查看示例"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select"
msgstr "選擇"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""
"選擇 <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr "選擇簽名樣式"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr "選擇模型添加到篩選。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Select a view"
msgstr "選擇視圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select all"
msgstr "全選"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select field"
msgstr "選擇欄位"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Selected records"
msgstr "選擇的記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Selection"
msgstr "選擇"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Selection:"
msgstr "選擇:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Set Default"
msgstr "設預設"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Set Defaults"
msgstr "設預設"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr "設定一張封面圖像"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a kanban state..."
msgstr "設置看板狀態..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a priority..."
msgstr "設置優先級..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr "在使用者上設置時區"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set kanban state..."
msgstr "設置看板狀態..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set priority..."
msgstr "設置優先級..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Settings"
msgstr "設定"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Share with all users"
msgstr "與所有使用者共享"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Shortcuts"
msgstr "快捷鍵"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Show sub-fields"
msgstr "顯示子欄位"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr "顯示本地可用模組"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "簽名"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "尺寸："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid ""
"Something happened while trying to contact the server, check that the server"
" is online and that you still have a working network connection."
msgstr "嘗試聯繫伺服器時發生了一些問題，請檢查伺服器是否運作並且您的網路連接是否仍然有效。"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Something horrible happened"
msgstr "發生了一些問題，但先別緊張..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Sort graph"
msgstr "排序圖"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Special:"
msgstr "特殊："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Stacked"
msgstr "堆疊"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Start typing..."
msgstr "開始輸入…"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "目前還在載入，請稍等..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "系統仍然在運作中...<br />請耐心等候...."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "風格"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr "樣式"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Support"
msgstr "支援"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr "語法錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Tags"
msgstr "標籤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Take a minute to get a coffee,"
msgstr "花一分鐘喝杯咖啡，"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr ""
"稍安勿躁，<br />系統還在運作中，您可以站起來活動一下筋骨...\n"
"來!跟著老師一起深蹲100次!"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__vat
msgid "Tax ID"
msgstr "稅務編號"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Technical Translation"
msgstr "技術性翻譯"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
msgid "Tel:"
msgstr "電話:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Template %s already exists in module %s"
msgstr "範本 %s 已在模組 %s 中存在"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Template:"
msgstr "模板："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Text"
msgstr "文本"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr "稅號。 如果聯絡人需繳納稅款，請填寫。 用於某些法律聲明。"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr "此儲存格的內容對於 XLSX 檔來說太長（超過 %s  字元）。請為此匯出使用 CSV 格式。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save."
msgstr "該欄位是空的，沒有什麼可儲存的。"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "新密碼與確認密碼必須相同。"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "您提供的原密碼是錯誤的，密碼沒有改變。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr "操作被中斷了。這通常意味著目前的操作花費了太多時間。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "所選文件超出了文件的最大值設定：%s。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr "欄位類型  '%s' 必須是一個關聯到 'ir.attachment'模型的 many2many欄位。"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr "要匯出為 Excel 2007-2013 （.xlsx） 格式的行太多 (%s rows, limit: %s)。請考慮拆分匯出。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr "沒有可用的圖像設定為封面。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr "上傳您的文件時發生了問題"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/date_picker.js:0
#, python-format
msgid "This date is in the future. Make sure this is what you expect."
msgstr "這個日期是在未來。確保這是您所期望的。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/datepicker/datepicker.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr "這個日期是在將來。確信這是您所期望的。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr "此domain不支援。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr "此檔無效。請選擇圖像。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr "這是一個所有人都可以使用的整體篩選，如果您繼續，將會向所有人移除。"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr "這是外部報表的一個例子。"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr "這是內部報表的一個例子。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This update will only consider the records of the current page."
msgstr "本次更新只會考慮當前頁面的記錄。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Time"
msgstr "時間"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""
"時區不匹配：此時區與您的瀏覽器不同。\n"
"請設置與瀏覽器相同的時區，以避免系統中的時間差異。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Today"
msgstr "今天"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Toggle"
msgstr "切換"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Tomorrow"
msgstr "明天"

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid "Too many items to display."
msgstr "太多項目無法顯示"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "總計"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#, python-format
msgid "Translate: %s"
msgstr "翻譯：%s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr "真"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/helpers/no_content_helpers.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                    active filter in the search bar."
msgstr ""
"嘗試添加一些記錄，或者確保沒有\n"
"                    搜索欄中的有效篩選。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Try to change your domain to only display positive results"
msgstr "嘗試更改您的domain以僅顯示正面結果"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Type:"
msgstr "類型："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "URL"
msgstr "網址"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr "在這個系統上找不到Wkhtmltopdf。報告將以HTML顯示。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "取消歸檔"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Unarchive All"
msgstr "取消歸檔所有"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught CORS Error"
msgstr "未獲取的 CORS 錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Javascript Error"
msgstr "未獲取的 Javascript 錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Promise"
msgstr "未獲取的承諾"

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_model.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Undefined"
msgstr "未定義的"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Unfold"
msgstr "展開"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid ""
"Unknown CORS error\n"
"\n"
"An unknown CORS error occured.\n"
"The error probably originates from a JavaScript file served from a different origin.\n"
"(Opening your browser console might give you a hint on the error.)"
msgstr ""
"未知的 CORS 錯誤\n"
"\n"
"發生未知的 CORS 錯誤。\n"
"該錯誤可能源於來自不同來源的 JavaScript 文件。\n"
"（打開瀏覽器控制台可能會給您提示錯誤。）"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr "未知的非字面類型 "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr "刪除行"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/_deprecated/data.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "Unnamed"
msgstr "未命名的"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr "未命名的"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Update to:"
msgstr "更新至："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#, python-format
msgid "Upgrade now"
msgstr "即刻升級"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr "升級到企業"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr "升級到未來版本"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Upload and Set"
msgstr "上傳並設定"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_bar.js:0
#, python-format
msgid "Upload cancelled"
msgstr "上傳已取消"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Upload your file"
msgstr "上傳您的文件"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploaded"
msgstr "已上傳"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploading"
msgstr "上傳中"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr "上傳錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploading..."
msgstr "正在上傳..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#, python-format
msgid "Uploading... (%s%%)"
msgstr "正在上傳... (%s%%)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Use This For My Kanban"
msgstr "將此用於我的看板"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Use by default"
msgstr "預設使用"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu.xml:0
#, python-format
msgid "User"
msgstr "使用者"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "User Error"
msgstr "使用者錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Validation Error"
msgstr "驗證錯誤"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_renderer.js:0
#, python-format
msgid "Values set here are company-specific."
msgstr "此處設定的值是特定於每間公司的。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr "變異"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View %s"
msgstr "視圖 %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Access Rights"
msgstr "查看存取權限"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Fields"
msgstr "視圖欄位"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "View Metadata"
msgstr "查看元數據"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Record Rules"
msgstr "查看記錄規則"

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr "檢視類型"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr "視圖切換器"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr "網上基準"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr "網路手機端測試"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr "網上測試"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__website
msgid "Website Link"
msgstr "網站連結"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Week"
msgstr "周"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Widget:"
msgstr "小部件:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Windows/Linux"
msgstr "Windows/Linux"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid "Wrap raw html within an iframe"
msgstr "將原始 html 包裝在 iframe 中"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Wrong login/password"
msgstr "錯誤的登入名/密碼"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "XML ID:"
msgstr "XML ID："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Year"
msgstr "年"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Yellow"
msgstr "黃色"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "Yes"
msgstr "是"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Yesterday"
msgstr "昨天"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "您無權限進行附件的更新"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr "您無法追蹤此欄位連結構的關係。"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "您不能讓任何密碼為空。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "You may not believe it,"
msgstr "你可能不相信，"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "也許您不相信，<br />但是系統真的還在運作...."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr "您需要在編輯翻譯之前儲存此新記錄。是否要繼續？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr "您需要至少使用兩個工人來啟動Odoo以列印報表的pdf版本。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr "您應該將Wkhtmltopdf的版本至少升級到0.12.0，以便正確顯示頁首和頁尾，以及支援頁之間的表分隔。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Your Odoo session expired. The current page is about to be refreshed."
msgstr "你的 Odoo 操作時段已過期。本頁面即將自動重新載入。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr "您安裝的Wkhtmltopdf 似乎壞了。報告將以HTML格式顯示。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/name_and_signature.js:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Your name"
msgstr "您的姓名"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr "[沒有小部件 %s]"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "一天前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "大約一分鐘前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "大約一月前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "大約一年前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "大約一小時前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "all records"
msgstr "所有記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "are valid for this update."
msgstr "此更新有效。"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "as a new"
msgstr "設為新"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "at:"
msgstr "在："

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "because it's loading..."
msgstr "因為它正在載入..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "but the application is actually loading..."
msgstr "但系統實際上正在載入..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr "子類"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "contains"
msgstr "包含"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "date"
msgstr "日期"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr "不含"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "doesn't contain"
msgstr "不包含"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/network/download.js:0
#, python-format
msgid "downloading..."
msgstr "下載中...."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "e.g. Global Business Solutions"
msgstr "例如：整體商業解決方案"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "for:"
msgstr "為："

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than"
msgstr "大於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than or equal to"
msgstr "大於或等於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "hex"
msgstr "hex"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr "在"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is"
msgstr "是"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after"
msgstr "在之後"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after or equal to"
msgstr "在此之後"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before"
msgstr "在之前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before or equal to"
msgstr "在此之前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is between"
msgstr "介於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is equal to"
msgstr "等於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is false"
msgstr "為假"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not"
msgstr "不是"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr "不等於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not equal to"
msgstr "不等於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not set"
msgstr "未設定"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is set"
msgstr "已設定"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is true"
msgstr "為真"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "it's still loading..."
msgstr "它仍在載入..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/fields/formatters.js:0
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr "kMGTPE"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than"
msgstr "少於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr "少於一分鐘前"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than or equal to"
msgstr "少於或等於"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_mixin.js:0
#, python-format
msgid "message: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "ms"
msgstr "毫秒"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "no description provided"
msgstr "未提供說明"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "not"
msgstr "不是"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr "不在"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr "未設定 (否)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr "以下規則:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of:"
msgstr "的:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/action_model.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/search_model.js:0
#, python-format
msgid "or"
msgstr "或"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr "上級"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#, python-format
msgid "props.fields"
msgstr "props.fields"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "query"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "record(s)"
msgstr "記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "records ?"
msgstr "記錄？"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "remaining)"
msgstr "剩餘)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "search"
msgstr "搜尋"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "selected"
msgstr "已選取"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "selected records,"
msgstr "選擇的記錄"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "set"
msgstr "設定"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr "設定(真)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/effects/effect_service.js:0
#, python-format
msgid "well Done!"
msgstr "做得好！"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "{\"o_act_window\": actionType === \"ir.actions.act_window\"}"
msgstr "{\"o_act_window\": actionType === \"ir.actions.act_window\"}"
