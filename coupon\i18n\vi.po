# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* coupon
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Vo Than<PERSON>huy, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: Vo Thanh Thuy, 2022\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%(amount)s %(currency)s discount on total amount"
msgstr "%(amount)s %(currency)s chiết khấu trên tổng giá tiền"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%(percentage)s%% discount on %(product_name)s"
msgstr "%(percentage)s%% chiết khấu cho %(product_name)s"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%s%% discount on cheapest product"
msgstr "%s%% chiết khấu cho sản phẩm rẻ nhất"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%s%% discount on products"
msgstr "%s%% chiết khấu cho sản phẩm"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%s%% discount on total amount"
msgstr "%s%% chiết khấu cho tổng giá tiền"

#. module: coupon
#: code:addons/coupon/wizard/coupon_generate.py:0
#, python-format
msgid "%s, a coupon has been generated for you"
msgstr "%s, một phiếu giảm giá đã được tạo cho bạn"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "*Valid for following products:"
msgstr "*Áp dụng cho các sản phẩm sau: "

#. module: coupon
#: model:coupon.program,name:coupon.10_percent_coupon
msgid "10% Discount"
msgstr "10% chiết khấu"

#. module: coupon
#: model:product.product,name:coupon.product_product_10_percent_discount
#: model:product.template,name:coupon.product_product_10_percent_discount_product_template
msgid "10.0% discount on total amount"
msgstr "10.0% chiết khấu cho tổng giá tiền"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid ""
"<span attrs=\"{'invisible': [('discount_type', '!=', "
"'percentage')],'required': [('discount_type', '=', 'percentage')]}\" "
"class=\"oe_inline\">%</span>"
msgstr ""
"<span attrs=\"{'invisible': [('discount_type', '!=', "
"'percentage')],'required': [('discount_type', '=', 'percentage')]}\" "
"class=\"oe_inline\">%</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
msgid ""
"<span class=\"o_form_label oe_inline\"> Days</span> <span "
"class=\"oe_grey\">if 0, infinite use</span>"
msgstr ""
"<span class=\"o_form_label oe_inline\"> Ngày</span> <span "
"class=\"oe_grey\">nếu 0, không giới hạn</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "<span class=\"oe_grey\"> if 0, no limit</span>"
msgstr "<span class=\"oe_grey\"> nếu 0, không giới hạn</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid ""
"<span> Days</span>\n"
"                    <span class=\"oe_grey\"> if 0, coupon doesn't expire</span>"
msgstr ""
"<span> Ngày</span>\n"
"                    <span class=\"oe_grey\"> nếu 0, phiếu giảm giá không hết hạn</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid ""
"<span> Orders</span>\n"
"                    <span class=\"oe_grey\"> if 0, infinite use</span>"
msgstr ""
"<span> Đơn hàng</span>\n"
"                    <span class=\"oe_grey\"> nếu 0, không giới hạn</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "<span>Minimum purchase of</span>"
msgstr "<span>Đơn mua hàng tối thiểu là</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "<span>Valid for purchase above</span>"
msgstr "<span>Áp dụng cho đơn mua hàng trên</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "<span>products</span>"
msgstr "<span>sản phẩm</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid ""
"<strong style=\"font-size: 55px; color: #875A7B\">get free shipping</strong>"
msgstr ""
"<strong style=\"font-size: 55px; color: #875A7B\">giao hàng miễn "
"phí</strong>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.view_coupon_program_kanban
msgid "<strong>Active</strong>"
msgstr "<strong>Hoạt động</strong>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.view_coupon_program_kanban
msgid "<strong>Coupons</strong>"
msgstr "<strong>Phiếu giảm giá</strong>"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__active
msgid "A program is available for the customers when active"
msgstr "Chương trình có sẵn cho khách hàng khi ở trạng thái hoạt động"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__promo_code
msgid ""
"A promotion code is a code that is associated with a marketing discount. For"
" example, a retailer might tell frequent customers to enter the promotion "
"code 'THX001' to receive a 10%% discount on their whole order."
msgstr ""
"Mã khuyến mại là mã được kết hợp với chiết khấu tiếp thị. Ví dụ: một nhà bán"
" lẻ có thể yêu cầu khách hàng thường xuyên nhập mã khuyến mại 'THX001' để "
"được giảm giá 10%% cho toàn bộ đơn đặt hàng của họ."

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__program_type
msgid ""
"A promotional program can be either a limited promotional offer without code (applied automatically)\n"
"                or with a code (displayed on a magazine for example) that may generate a discount on the current\n"
"                order or create a coupon for a next order.\n"
"\n"
"                A coupon program generates coupons with a code that can be used to generate a discount on the current\n"
"                order or create a coupon for a next order."
msgstr ""
"Chương trình khuyến mại có thể là một khuyến mại có giới hạn không có mã (áp dụng tự động)\n"
"                hoặc có mã (ví dụ được in trên tạp chí) có thể tạo ra một khoản giảm giá cho đơn hàng\n"
"                hiện tại hoặc tạo giảm giá cho đơn hàng tiếp theo.\n"
"\n"
"                Chương trình phiếu giảm giá tạo phiếu giảm giá với mã có thể được sử dụng để giảm giá \n"
"                cho đơn hàng hiện tại hoặc tạo giảm giá cho đơn hàng tiếp theo."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__promo_applicability
msgid "Applicability"
msgstr "Có thể được áp dụng"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Apply Discount"
msgstr "Áp dụng chiết khấu"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_applicability__on_current_order
msgid "Apply On Current Order"
msgstr "Áp dụng cho đơn hiện tại"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Apply on First"
msgstr "Áp dụng đầu tiên"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_search
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_code_usage__no_code_needed
msgid "Automatically Applied"
msgstr "Áp dụng tự động"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__promo_code_usage
msgid ""
"Automatically Applied - No code is required, if the program rules are met, the reward is applied (Except the global discount or the free shipping rewards which are not cumulative)\n"
"Use a code - If the program rules are met, a valid code is mandatory for the reward to be applied\n"
msgstr ""
"Áp dụng tự động - Không yêu cầu mã, nếu đáp ứng quy tắc chương trình, quà tặng sẽ được áp dụng (Ngoại trừ chiết khấu toàn đơn hàng hoặc giao hàng miễn phí không được tích lũy)\n"
"Sử dụng mã - Nếu đáp ứng quy tắc chương trình, bắt buộc phải có mã hợp lệ để áp dụng quà tặng của chương trình\n"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_partners_domain
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_partners_domain
msgid "Based on Customers"
msgstr "Dựa theo khách hàng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_products_domain
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_products_domain
msgid "Based on Products"
msgstr "Dựa theo sản phẩm"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_promo_program
msgid ""
"Build up promotion programs to attract more customers with discounts, free products, free delivery, etc.\n"
"                You can share promotion codes or grant the promotions automatically if some conditions are met."
msgstr ""
"Xây dựng các chương trình khuyến mãi để thu hút nhiều khách hàng hơn bằng chiết khấu, sản phẩm miễn phí, giao hàng miễn phí, v.v.\n"
"                Bạn có thể chia sẻ mã khuyến mại hoặc tự động cấp các chương trình khuyến mại nếu khách hàng đáp ứng một số điều kiện. "

#. module: coupon
#: model:coupon.program,name:coupon.3_cabinets_plus_1_free
msgid "Buy 3 large cabinets, get one for free"
msgstr "Tủ lớn, mua 3 tặng 1"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_tree
msgid "Cancel"
msgstr "Hủy"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__cancel
msgid "Cancelled"
msgstr "Đã hủy"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__code
msgid "Code"
msgstr "Mã"

#. module: coupon
#: model:coupon.program,name:coupon.10_percent_auto_applied
msgid "Code for 10% on orders"
msgstr "Mã giảm giá 10% cho đơn hàng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__company_id
msgid "Company"
msgstr "Công ty"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "Compose Email"
msgstr "Soạn email"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Conditions"
msgstr "Điều kiện"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Congratulations"
msgstr "Xin chúc mừng"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_coupon
msgid "Coupon"
msgstr "Phiếu giảm giá"

#. module: coupon
#: model:ir.actions.report,name:coupon.report_coupon_code
msgid "Coupon Code"
msgstr "Mã giảm giá"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__coupon_count
msgid "Coupon Count"
msgstr "Số phiếu giảm giá"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_program
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__program_type__coupon_program
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Coupon Program"
msgstr "Chương trình phiếu giảm giá"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_program_action_coupon_program
msgid "Coupon Programs"
msgstr "Chương trình phiếu giảm giá"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_reward
msgid "Coupon Reward"
msgstr "Phần thưởng giảm giá"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_rule
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_id
msgid "Coupon Rule"
msgstr "Quy tắc giảm giá"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Coupon Validity"
msgstr "Hiệu lực phiếu giảm giá"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_date_to
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_date_to
msgid "Coupon program end date"
msgstr "Ngày kết thúc CT giảm giá"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_date_from
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_date_from
msgid "Coupon program start date"
msgstr "Ngày bắt đầu CT giảm giá"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__sequence
msgid ""
"Coupon program will be applied based on given sequence if multiple programs "
"are defined on same condition(For minimum amount)"
msgstr ""
"Chương trình phiếu giảm giá sẽ được áp dụng dựa trên trình tự đã cho nếu "
"nhiều chương trình được xác định dựa vào một điều kiện(Cho số tiền tối "
"thiểu)"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_partners_domain
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_partners_domain
msgid "Coupon program will work for selected customers only"
msgstr ""
"Chương trình phiếu giảm giá sẽ chỉ áp dụng cho một số khách hàng được chọn"

#. module: coupon
#: model:ir.actions.server,name:coupon.expire_coupon_cron_ir_actions_server
#: model:ir.cron,cron_name:coupon.expire_coupon_cron
#: model:ir.cron,name:coupon.expire_coupon_cron
msgid "Coupon: expire coupon based on date"
msgstr "Phiếu giảm giá: phiếu giảm giá hết hạn dựa trên ngày"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_action
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_tree
msgid "Coupons"
msgstr "Phiếu giảm giá"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_coupon_program
msgid "Create a new coupon program"
msgstr "Tạo chương trình phiếu giảm giá mới"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_promo_program
msgid "Create a new promotion program"
msgstr "Tạo chương trình khuyến mại mới"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_program__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_program__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__partners_domain
msgid "Customer"
msgstr "Khách hàng"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_product_uom_id
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Đơn vị đo mặc định dùng cho tất cả hoạt động kho. "

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_percentage
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_percentage
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__reward_type__discount
msgid "Discount"
msgstr "Chiết khấu"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_type
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_type
msgid ""
"Discount - Reward will be provided as discount.\n"
"Free Product - Free product will be provide as reward \n"
"Free Shipping - Free shipping will be provided as reward (Need delivery module)"
msgstr ""
"Chiết khấu - Phần thưởng được cung cấp sẽ là chiết khấu.\n"
"Sản phẩm miễn phí - Sản phẩm miễn phí sẽ được cung cấp là phần thưởng.\n"
"Vận chuyển miễn phí - Vận chuyển miễn phí sẽ được cung cấp là phần thưởng (Cần mô-đun giao hàng)"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_apply_on
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_apply_on
msgid "Discount Apply On"
msgstr "Chiết khấu áp dụng cho"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_max_amount
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_max_amount
msgid "Discount Max Amount"
msgstr "Khoản chiết khấu tối đa"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_type
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_type
msgid "Discount Type"
msgstr "Loại chiết khấu"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "Discount percentage should be between 1-100"
msgstr "Phần trăm chiết khấu phải từ 1-100"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_program__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: coupon
#: model:ir.model,name:coupon.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Trợ lý hướng dẫn soạn email"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_date_to
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_date_to
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__expiration_date
msgid "Expiration Date"
msgstr "Ngày hết hạn"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__expired
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_search
msgid "Expired"
msgstr "Đã hết hạn"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_search
msgid "Expired Programs"
msgstr "Chương trình đã hết hạn"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_fixed_amount
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_fixed_amount
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_type__fixed_amount
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Fixed Amount"
msgstr "Khoản cố định"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__partner_id
msgid "For Customer"
msgstr "Cho khách hàng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_product_id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_product_id
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__reward_type__product
msgid "Free Product"
msgstr "Sản phẩm miễn phí"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "Free Product - %s"
msgstr "Sản phẩm miễn phí - %s"

#. module: coupon
#: model:product.product,name:coupon.product_product_free_large_cabinet
#: model:product.template,name:coupon.product_product_free_large_cabinet_product_template
msgid "Free Product - Large Cabinet"
msgstr "Sản phẩm miễn phí - Tủ lớn"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid "Generate"
msgstr "Tạo"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_generate_wizard
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
msgid "Generate Coupon"
msgstr "Tạo phiếu giảm giá"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid "Generate Coupons"
msgstr "Tạo phiếu giảm giá"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_coupon_program
msgid ""
"Generate and share coupon codes with your customers to get discounts or free"
" products."
msgstr ""
"Tạo và chia sẻ mã giảm giá với khách hàng để nhận chiết khấu hoặc sản phẩm "
"miễn phí. "

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__coupon_ids
msgid "Generated Coupons"
msgstr "Phiếu giảm giá được tạo"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__generation_type
msgid "Generation Type"
msgstr "Kiểu tạo "

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__has_partner_email
msgid "Has Partner Email"
msgstr "Có email đối tác"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Here is your reward from"
msgstr "Đây là phần thưởng của bạn từ"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__id
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__id
#: model:ir.model.fields,field_description:coupon.field_coupon_program__id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__id
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__id
msgid "ID"
msgstr "ID"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "Invalid partner."
msgstr "Đối tác không hợp lệ."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_program____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_reward____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_rule____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_program__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_program__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Logo"
msgstr "Logo"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Max Discount Amount"
msgstr "Khoản chiết khấu tối đa"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__maximum_use_number
msgid "Maximum Use Number"
msgstr "Số lần sử dụng tối đa"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_max_amount
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_max_amount
msgid "Maximum amount of discount that can be provided"
msgstr "Khoản chiết khấu tối đa có thể cung cấp"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__maximum_use_number
msgid "Maximum number of sales orders in which reward can be provided"
msgstr "Số đơn bán hàng tối đa áp dụng chương trình tặng thưởng "

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Minimum Purchase Of"
msgstr "Đơn mua hàng tối thiểu là"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_min_quantity
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_min_quantity
msgid "Minimum Quantity"
msgstr "Số lượng tối thiểu"

#. module: coupon
#: code:addons/coupon/models/coupon_rules.py:0
#, python-format
msgid "Minimum purchased amount should be greater than 0"
msgstr "Khoản tiền mua hàng tối thiểu phải lớn hơn 0"

#. module: coupon
#: code:addons/coupon/models/coupon_rules.py:0
#, python-format
msgid "Minimum quantity should be greater than 0"
msgstr "Số lượng tối thiểu phải lớn hơn 0"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_minimum_amount
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_minimum_amount
msgid "Minimum required amount to get the reward"
msgstr "Khoản tiền tối thiểu bắt buộc để nhận thưởng"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_min_quantity
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_min_quantity
msgid "Minimum required product quantity to get the reward"
msgstr "Số lượng sản phẩm tối thiểu bắt buộc để nhận thưởng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__name
msgid "Name"
msgstr "Tên"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__nbr_coupons
#: model:ir.model.fields.selection,name:coupon.selection__coupon_generate_wizard__generation_type__nbr_coupon
msgid "Number of Coupons"
msgstr "Số phiếu giảm giá"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_generate_action
msgid "Number of Coupons To Generate"
msgstr "Số phiếu giảm giá cần tạo"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_generate_wizard__generation_type__nbr_customer
msgid "Number of Selected Customers"
msgstr "Số khách hàng được chọn"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_generate_wizard__nbr_coupons
msgid "Number of coupons"
msgstr "Số phiếu giảm giá"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_apply_on__cheapest_product
msgid "On Cheapest Product"
msgstr "Sản phẩm rẻ nhất"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_apply_on__on_order
msgid "On Order"
msgstr "Đơn hàng"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_apply_on
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_apply_on
msgid ""
"On Order - Discount on whole order\n"
"Cheapest product - Discount on cheapest product of the order\n"
"Specific products - Discount on selected specific products"
msgstr ""
"Đơn hàng - Chiết khấu cho cả đơn hàng\n"
"Sản phẩm rẻ nhất - Chiết khấu cho sản phẩm rẻ nhất trong đơn hàng\n"
"Sản phẩm cụ thể - Chiết khấu cho sản phẩm cụ thể được chọn"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_products_domain
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_products_domain
msgid "On Purchase of selected product, reward will be given"
msgstr "Nhận phần thưởng khi mua sản phẩm được chọn "

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_apply_on__specific_products
msgid "On Specific Products"
msgstr "Sản phẩm cụ thể"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__reserved
msgid "Pending"
msgstr "Đang chờ"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_type__percentage
msgid "Percentage"
msgstr "Phần trăm"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_type
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_type
msgid ""
"Percentage - Entered percentage discount will be provided\n"
"Amount - Entered fixed amount discount will be provided"
msgstr ""
"Phần trăm - Phần trăm chiết khấu đã nhập sẽ được cung cấp\n"
"Khoản tiền - Khoản chiết khấu cố định đã nhập sẽ được cung cấp"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_coupon__discount_line_product_id
msgid "Product used in the sales order to apply the discount."
msgstr "Sản phẩm dùng trong đơn bán hàng sẽ áp dụng chiết khấu."

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_line_product_id
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_line_product_id
msgid ""
"Product used in the sales order to apply the discount. Each coupon program "
"has its own reward product for reporting purpose"
msgstr ""
"Sản phẩm dùng trong đơn bán hàng sẽ áp dụng chiết khấu. Mỗi chương trình "
"phiếu giảm giá có sản phẩm phần thưởng riêng cho mục đích báo cáo"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_specific_product_ids
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_specific_product_ids
msgid "Products"
msgstr "Sản phẩm"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_specific_product_ids
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_specific_product_ids
msgid ""
"Products that will be discounted if the discount is applied on specific "
"products"
msgstr ""
"Sản phẩm sẽ được giảm giá nếu chiết khấu được áp dụng cho sản phẩm cụ thể"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__program_id
msgid "Program"
msgstr "Chương trình"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Program Name"
msgstr "Tên chương trình"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__program_type
msgid "Program Type"
msgstr "Loại chương trình"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__promo_code_usage
msgid "Promo Code Usage"
msgstr "Cách dùng mã KM"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__promo_code
msgid "Promotion Code"
msgstr "Mã khuyến mãi"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_program_action_promo_program
msgid "Promotion Programs"
msgstr "Chương trình khuyến mãi"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__program_type__promotion_program
msgid "Promotional Program"
msgstr "CT khuyến mãi"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_product_quantity
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_product_quantity
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Quantity"
msgstr "Số lượng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_id
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Reward"
msgstr "Thưởng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_description
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_description
msgid "Reward Description"
msgstr "Mô tả phần thưởng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__discount_line_product_id
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_line_product_id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_line_product_id
msgid "Reward Line Product"
msgstr "Sản phẩm dòng phần thưởng"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_product_id
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_product_id
msgid "Reward Product"
msgstr "Sản phẩm thưởng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_type
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_type
msgid "Reward Type"
msgstr "Loại phần thưởng"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_product_quantity
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_product_quantity
msgid "Reward product quantity"
msgstr "Số lượng sản phẩm thưởng"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Rewards"
msgstr "Phần thưởng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_minimum_amount
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_minimum_amount
msgid "Rule Minimum Amount"
msgstr "Quy tắc khoản tối thiểu"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_minimum_amount_tax_inclusion
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_minimum_amount_tax_inclusion
msgid "Rule Minimum Amount Tax Inclusion"
msgstr "Quy tắc bao gồm số tiền thuế tối thiểu"

#. module: coupon
#: model:ir.model,name:coupon.model_report_coupon_report_coupon
msgid "Sales Coupon Report"
msgstr "Báo cáo phiếu giảm giá "

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select company"
msgstr "Chọn công ty"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Select customer"
msgstr "Chọn khách hàng "

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select product"
msgstr "Chọn sản phẩm"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select products"
msgstr "Chọn sản phẩm"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select reward product"
msgstr "Chọn sản phẩm thưởng"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_tree
msgid "Send"
msgstr "Gửi"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_applicability__on_next_order
msgid "Send a Coupon"
msgstr "Gửi phiếu giảm giá"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_form
msgid "Send by Email"
msgstr "Gửi qua email"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__sent
msgid "Sent"
msgstr "Đã gửi"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid ""
"Some selected customers do not have an email address and will not receive "
"the coupon."
msgstr ""
"Một số khách hàng được chọn không có địa chỉ email và sẽ không nhận được "
"phiếu giảm giá."

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid "Specify a mail template to send the generated coupons as email."
msgstr "Xác định mẫu email để gửi phiếu giảm giá được tạo qua email."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_date_from
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_date_from
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__state
msgid "State"
msgstr "Trạng thái"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_rule__rule_minimum_amount_tax_inclusion__tax_excluded
msgid "Tax Excluded"
msgstr "Chưa gồm thuế"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_rule__rule_minimum_amount_tax_inclusion__tax_included
msgid "Tax Included"
msgstr "Đã gồm thuế"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Thank you,"
msgstr "Cảm ơn,"

#. module: coupon
#: model:ir.model.constraint,message:coupon.constraint_coupon_coupon_unique_coupon_code
msgid "The coupon code must be unique!"
msgstr "Mã giảm giá phải là duy nhất!"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "The coupon program for %s is in draft or closed state"
msgstr "Chương trình giảm giá cho %s ở trạng thái nháp hoặc đóng"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_fixed_amount
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_fixed_amount
msgid "The discount in fixed amount"
msgstr "Chiết khấu khoản cố định"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_percentage
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_percentage
msgid "The discount in percentage, between 1 and 100"
msgstr "Phần trăm chiết khấu, giữa 1 và 100"

#. module: coupon
#: code:addons/coupon/models/coupon_program.py:0
#, python-format
msgid "The program code must be unique!"
msgstr "Mã chương trình phải là duy nhất!"

#. module: coupon
#: code:addons/coupon/models/coupon_rules.py:0
#, python-format
msgid "The start date must be before the end date"
msgstr "Ngày bắt đầu phải trước ngày kết thúc"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon %s exists but the origin sales order is not validated yet."
msgstr "Mã giảm giá %s tồn tại nhưng đơn hàng gốc chưa được xác nhận."

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon has already been used (%s)."
msgstr "Phiếu giảm giá này đã được sử dụng (%s)."

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon has been cancelled (%s)."
msgstr "Phiếu giảm giá này đã bị hủy (%s)."

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon is expired (%s)."
msgstr "Phiếu giảm giá này đã hết hạn (%s)."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__total_order_count
msgid "Total Order Count"
msgstr "Tổng số đơn hàng"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_product_uom_id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_product_uom_id
msgid "Unit of Measure"
msgstr "Đơn vị đo"

#. module: coupon
#: model:product.product,uom_name:coupon.product_product_10_percent_discount
#: model:product.product,uom_name:coupon.product_product_free_large_cabinet
#: model:product.template,uom_name:coupon.product_product_10_percent_discount_product_template
#: model:product.template,uom_name:coupon.product_product_free_large_cabinet_product_template
msgid "Units"
msgstr "Đơn vị"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_code_usage__code_needed
msgid "Use a code"
msgstr "Sử dụng mã"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__template_id
msgid "Use template"
msgstr "Sử dụng mẫu"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Use this promo code before"
msgstr "Sử dụng mã KM trước"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__used
msgid "Used"
msgstr "Đã dùng"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__new
msgid "Valid"
msgstr "Hợp lệ"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Validity"
msgstr "Xác nhận"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__validity_duration
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
msgid "Validity Duration"
msgstr "Thời hạn hiệu lực"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__validity_duration
msgid "Validity duration for a coupon after its generation"
msgstr "Thời lượng hiệu lực của phiếu giảm giá sau khi tạo "

#. module: coupon
#: code:addons/coupon/models/coupon_program.py:0
#, python-format
msgid "You can not delete a program in active state"
msgstr "Bạn không thể xóa chương trình đang ở trạng thái hoạt động"

#. module: coupon
#: code:addons/coupon/models/product_product.py:0
#, python-format
msgid ""
"You cannot delete a product that is linked with Coupon or Promotion program."
" Archive it instead."
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "e.g. 10% Discount"
msgstr "VD: 10% chiết khấu"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off %s"
msgstr "chiết khấu %s"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off on %s"
msgstr "chiết khấu cho %s"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off on some products*"
msgstr "chiết khấu cho một số sản phẩm*"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off on the cheapest product"
msgstr "chiết khấu cho sản phẩm rẻ nhất"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "on your next order"
msgstr "cho đơn hàng tiếp theo"
