<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>


    <!-- SALARY STRATURE -->
    <record id="view_hr_payroll_structure_list_view" model="ir.ui.view">
        <field name="name">hr.payroll.structure.tree</field>
        <field name="model">hr.payroll.structure</field>
        <field name="arch" type="xml">
            <tree string="Employee Function">
                <field name="name"/>
                <field name="code"/>
                <field name="rule_ids"/>
                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
            </tree>
        </field>
    </record>
    <record id="hr_payroll_structure_view_kanban" model="ir.ui.view">
        <field name="name">hr.payroll.structure.kanban</field>
        <field name="model">hr.payroll.structure</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                            <div class="row">
                                <div class="col-12">
                                    <strong><field name="name"/></strong>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span>Code: <field name="code"/></span>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="view_hr_payroll_structure_tree" model="ir.ui.view">
        <field name="name">hr.payroll.structure.tree</field>
        <field name="model">hr.payroll.structure</field>
        <field name="field_parent">children_ids</field>
        <field name="arch" type="xml">
            <tree string="Salary Structure">
                <field name="name"/>
                <field name="code"/>
                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
            </tree>
        </field>
    </record>
    <record id="view_hr_payroll_structure_filter" model="ir.ui.view">
        <field name="name">hr.payroll.structure.select</field>
        <field name="model">hr.payroll.structure</field>
        <field name="arch" type="xml">
            <search string="Payroll Structures">
                <field name="name" string="Payroll Structures" filter_domain="['|',('name','ilike',self),('code','ilike',self)]"/>
            </search>
        </field>
    </record>
    <record id="view_hr_employee_grade_form" model="ir.ui.view">
        <field name="name">hr.payroll.structure.form</field>
        <field name="model">hr.payroll.structure</field>
        <field name="arch" type="xml">
            <form string="Employee Function">
                <group col="4">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="parent_id"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                </group>
                <notebook colspan="4">
                     <page string="Salary Rules">
                       <field name="rule_ids" domain="[('parent_rule_id','=',False)]">
                         <tree>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="category_id"/>
                            <field name="sequence" invisible="1"/>
                            <field name="register_id"/>
                         </tree>
                      </field>
                     </page>
                </notebook>
            </form>
        </field>
    </record>
    <record id="action_view_hr_payroll_structure_list_form" model="ir.actions.act_window">
        <field name="name">Salary Structures</field>
        <field name="res_model">hr.payroll.structure</field>
        <field name="view_mode">tree,kanban,form</field>
    </record>
    <menuitem
        id="menu_hr_payroll_structure_view"
        action="action_view_hr_payroll_structure_list_form"
        parent="menu_hr_payroll_community_configuration"
        sequence="2"
    />


    <!-- SALARY CATEGORY-->
    <record id="hr_salary_rule_category_form" model="ir.ui.view">
        <field name="name">hr.salary.rule.category.form</field>
        <field name="model">hr.salary.rule.category</field>
        <field name="arch" type="xml">
            <form string="Salary Categories">
                <group col="4">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="parent_id"/>
                </group>
                <group string="Notes">
                    <field name="note" nolabel="1"/>
                </group>
            </form>
        </field>
    </record>
    <record id="hr_salary_rule_category_tree" model="ir.ui.view">
        <field name="name">hr.salary.rule.category.tree</field>
        <field name="model">hr.salary.rule.category</field>
        <field name="arch" type="xml">
            <tree string="Salary Rule Categories">
                <field name="name"/>
                <field name="code"/>
                <field name="parent_id" invisible="1"/>
            </tree>
        </field>
    </record>
    <record id="view_hr_salary_rule_category_filter" model="ir.ui.view">
        <field name="name">hr.salary.rule.category.select</field>
        <field name="model">hr.salary.rule.category</field>
        <field name="arch" type="xml">
            <search string="Salary Rule Categories">
                <field name="name" string="Salary Rule Categories" filter_domain="['|',('name','ilike',self),('code','ilike',self)]"/>
           </search>
        </field>
    </record>
    <record id="action_hr_salary_rule_category" model="ir.actions.act_window">
        <field name="name">Salary Rule Categories</field>
        <field name="res_model">hr.salary.rule.category</field>
        <field name="view_id" ref="hr_salary_rule_category_tree"/>
       <field name="search_view_id" ref="view_hr_salary_rule_category_filter"/>
    </record>
    <menuitem
        id="menu_hr_salary_rule_category"
        action="action_hr_salary_rule_category"
        parent="menu_hr_payroll_community_configuration"
        sequence="11"
        groups="base.group_no_one"
    />

    <!--
    Contribution Register
    -->
    <record id="hr_contribution_register_tree" model="ir.ui.view">
        <field name="name">hr.contribution.register.tree</field>
        <field name="model">hr.contribution.register</field>
        <field name="arch" type="xml">
            <tree string="Contribution Registers">
                <field name="name" />
                <field name="company_id"  groups="base.group_multi_company" options="{'no_create': True}"/>
            </tree>
        </field>
    </record>
    <record id="hr_contribution_register_view_kanban" model="ir.ui.view">
        <field name="name">hr.contribution.register.kanban</field>
        <field name="model">hr.contribution.register</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                            <div class="row">
                                <div class="col-12">
                                    <strong><field name="name"/></strong>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="hr_contribution_register_filter" model="ir.ui.view">
        <field name="name">hr.contribution.register.search</field>
        <field name="model">hr.contribution.register</field>
        <field name="arch" type="xml">
            <search string="Contribution Registers">
                <field name="name" string="Contribution Registers"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </search>
        </field>
    </record>
    <record id="hr_contribution_register_form" model="ir.ui.view">
        <field name="name">hr.contribution.register.form</field>
        <field name="model">hr.contribution.register</field>
        <field name="arch" type="xml">
            <form string="Contribution">
                <group>
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" class="oe_inline"/>
                </group>
                <newline/>
                <group>
                <separator string="Description"/><newline/>
                <field name="note" nolabel="1"/></group>
            </form>
        </field>
    </record>
    <record id="action_contribution_register_form" model="ir.actions.act_window">
        <field name="name">Contribution Registers</field>
        <field name="res_model">hr.contribution.register</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Add a new contribution register
          </p><p>
            A contribution register is a third party involved in the salary
            payment of the employees. It can be the social security, the
            state or anyone that collect or inject money on payslips.
          </p>
        </field>
    </record>
    <menuitem
        id="menu_action_hr_contribution_register_form"
        action="action_contribution_register_form"
        parent="menu_hr_payroll_community_configuration"
        sequence="14"
    />

        <!-- Salary Rules -->

        <record id="hr_salary_rule_list" model="ir.ui.view">
            <field name="name">hr.salary.rule.list</field>
            <field name="model">hr.salary.rule</field>
            <field name="arch" type="xml">
                <tree string="Salary Rules">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="category_id"/>
                    <field name="sequence" invisible="1"/>
                    <field name="register_id"/>
                </tree>
            </field>
        </record>
        <record id="hr_salary_rule_view_kanban" model="ir.ui.view">
            <field name="name">hr.salary.rule.kanban</field>
            <field name="model">hr.salary.rule</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                                <div class="row">
                                    <div class="col-8">
                                        <strong><field name="name"/></strong>
                                    </div>
                                    <div class="col-4">
                                        <span class="float-right"><field name="category_id"/></span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <span>Code: <field name="code"/></span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        <record id="hr_salary_rule_tree" model="ir.ui.view">
            <field name="name">hr.salary.rule.tree</field>
            <field name="model">hr.salary.rule</field>
            <field name="field_parent">child_ids</field>
            <field eval="20" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Salary Rules">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="category_id" invisible="True"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                </tree>
            </field>
        </record>
        <record id="hr_salary_rule_form" model="ir.ui.view">
            <field name="name">hr.salary.rule.form</field>
            <field name="model">hr.salary.rule</field>
            <field name="arch" type="xml">
                <form string="Salary Rules">
                    <label for="name" class="oe_edit_only"/>
                    <h1><field name="name"/></h1>
                    <label for="category_id" class="oe_edit_only"/>
                    <h2><field name="category_id"/></h2>

                    <group col="4">
                       <field name="code"/>
                       <field name="sequence" />
                       <field name="active"/>
                       <field name="appears_on_payslip"/>
                       <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                    </group>
                    <notebook colspan="6">
                        <page string="General">
                            <group col="4">
                                <separator colspan="4" string="Conditions"/>
                                <field name="condition_select"/><newline/>
                                <field name="condition_python" attrs="{'invisible':[('condition_select','!=','python')], 'required': [('condition_select','=','python')]}" colspan="4"/> <newline/>
                                <field name="condition_range" attrs="{'invisible':[('condition_select','!=','range')], 'required':[('condition_select','=','range')]}"/><newline/>
                                <field name="condition_range_min" colspan="2" attrs="{'invisible':[('condition_select','!=','range')], 'required':[('condition_select','=','range')]}"/><newline/>
                                <field name="condition_range_max" colspan="2" attrs="{'invisible':[('condition_select','!=','range')], 'required':[('condition_select','=','range')]}"/><newline/>
                                <separator colspan="4" string="Computation"/>
                                <field name="amount_select"/><newline/>
                                <field name="amount_percentage_base" attrs="{'invisible':[('amount_select','!=','percentage')], 'required': [('amount_select','=','percentage')]}"/><newline/>
                                <field name="quantity" attrs="{'invisible':[('amount_select','=','code')], 'required':[('amount_select','!=','code')]}"/><newline/>
                                <field name="amount_fix"  attrs="{'invisible':[('amount_select','!=','fix')], 'required':[('amount_select','=','fix')]}"/><newline/>
                                <field colspan="4" name="amount_python_compute" attrs="{'invisible':[('amount_select','!=','code')], 'required':[('amount_select','=','code')]}"/>
                                <field name="amount_percentage" attrs="{'invisible':[('amount_select','!=','percentage')], 'required':[('amount_select','=','percentage')]}"/>
                                <separator colspan="4" string="Company Contribution"/>
                                <field name="register_id"/>
                            </group>
                        </page>
                        <page name="rules" string="Child Rules">
                           <field name="parent_rule_id"/>
                           <separator string="Children Definition"/>
                           <field name="child_ids"/>
                        </page>
                           <page string="Inputs">
                                <field name="input_ids" mode="tree">
                                    <tree string="Input Data" editable="bottom">
                                        <field name="name"/>
                                        <field name="code"/>
                                    </tree>
                                </field>
                           </page>
                        <page string="Description">
                            <field name="note"/>
                        </page>
                    </notebook>
                </form>
            </field>
        </record>
        <record id="view_hr_rule_filter" model="ir.ui.view">
            <field name="name">hr.salary.rule.select</field>
            <field name="model">hr.salary.rule</field>
            <field name="arch" type="xml">
                <search string="Search Salary Rule">
                    <field name="name" string="Salary Rules" filter_domain="['|',('name','ilike',self),('code','ilike',self)]"/>
                    <field name="category_id"/>
                    <field name="condition_range_min"/>
                    <group col="8" colspan="4" expand="0" string="Group By">
                        <filter string="Category" name="head" context="{'group_by':'category_id'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="action_salary_rule_form" model="ir.actions.act_window">
            <field name="name">Salary Rules</field>
            <field name="res_model">hr.salary.rule</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">[('parent_rule_id','=',False)]</field>
            <field name="search_view_id" ref="view_hr_rule_filter"/>
        </record>
        <menuitem id="menu_action_hr_salary_rule_form"
                  action="action_salary_rule_form"
                  parent="menu_hr_payroll_community_configuration"
                  sequence="12"/>


        <record id="act_children_salary_rules" model="ir.actions.act_window">
            <field name="name">All Children Rules</field>
            <field name="res_model">hr.salary.rule</field>
            <field name="domain">[('parent_rule_id', '=', active_id)]</field>
        </record>
    </data>
</odoo>
