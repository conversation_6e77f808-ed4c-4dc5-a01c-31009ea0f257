# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_org_chart
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Dominican Republic) (https://www.transifex.com/odoo/teams/41243/es_DO/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_DO\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/js/hr_org_chart.js:136
#, python-format
msgid "Direct Subordinates of %s"
msgstr ""

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:126
#, python-format
msgid "Direct subordinates"
msgstr ""

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee
msgid "Employee"
msgstr ""

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:89
#, python-format
msgid "In order to get an organigram, set a manager and save the record."
msgstr ""

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/js/hr_org_chart.js:146
#, python-format
msgid "Indirect Subordinates of %s"
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_child_all_count
msgid "Indirect Surbordinates Count"
msgstr ""

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:136
#, python-format
msgid "Indirect subordinates"
msgstr ""

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:87
#, python-format
msgid "No hierarchy position."
msgstr ""

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
msgid "Organization Chart"
msgstr ""

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/js/hr_org_chart.js:139
#, python-format
msgid "Subordinates of %s"
msgstr ""

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:88
#, python-format
msgid "This employee has no manager or subordinate."
msgstr ""

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:144
#, python-format
msgid "Total"
msgstr "Total"
