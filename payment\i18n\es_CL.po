# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Chile) (https://www.transifex.com/odoo/teams/41243/es_CL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_html_3ds
msgid "3D Secure HTML"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-lock\"> Pay</i>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-plus-circle\"> Add new card</i>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-trash\"/> Delete"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:76
#: code:addons/payment/static/src/js/payment_form.js:219
#, python-format
msgid "<p>Please fill all the inputs required.</p>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:180
#, python-format
msgid "<p>Please select a payment method.</p>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:274
#, python-format
msgid "<p>Please select the option to add a new payment method.</p>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:315
#, python-format
msgid "<p>This card is currently linked to the following records:<p/>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:108
#: code:addons/payment/static/src/js/payment_form.js:120
#: code:addons/payment/static/src/js/payment_form.js:254
#: code:addons/payment/static/src/js/payment_form.js:267
#, python-format
msgid "<p>We are not able to add your payment method at the moment.</p>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:298
#: code:addons/payment/static/src/js/payment_form.js:333
#, python-format
msgid "<p>We are not able to delete your payment method at the moment.</p>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:155
#: code:addons/payment/static/src/js/payment_form.js:161
#, python-format
msgid "<p>We are not able to redirect you to the payment form.</p>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:169
#, python-format
msgid "<p>We're unable to process your payment.</p>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"<span class=\"o_warning_text\">Test</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_transfer
msgid "<span><i>Cancel,</i> Your payment has been cancelled.</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_transfer
msgid ""
"<span><i>Done,</i> Your online payment has been successfully processed. "
"Thank you for your order.</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_transfer
msgid ""
"<span><i>Error,</i> Please be aware that an error occurred during the "
"transaction. The order has been confirmed but will not be paid. Do not "
"hesitate to contact us if you have any questions on the status of your "
"order.</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_stripe
msgid ""
"<span><i>Pending,</i> Your online payment has been successfully processed. "
"But your order is not validated yet.</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid ""
"<span><i>Pending</i>... The order will be validated after the "
"payment.</span>"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_acquirer_id
msgid "Acquirer"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token_acquirer_id
msgid "Acquirer Account"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token_acquirer_ref
msgid "Acquirer Ref."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_acquirer_reference
msgid "Acquirer Reference"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon_acquirer_ids
msgid "Acquirers"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
msgid "Acquirers list"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Activate"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token_active
msgid "Active"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_active
msgid "Add Extra Fees"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_address
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Address"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_adyen
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment
#: selection:payment.acquirer,save_token:0
msgid "Always"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_amount
#: model:ir.model.fields,help:payment.field_payment_transaction_amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Amount"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_authorize_implemented
msgid "Authorize Mechanism Supported"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_authorize
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Authorized"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_transfer
msgid "Bank Account"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_callback_res_id
msgid "Callback Document ID"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_callback_model_id
msgid "Callback Document Model"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_callback_hash
msgid "Callback Hash"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_callback_method
msgid "Callback Method"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:323
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_cancel_msg
msgid "Cancel Message"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Canceled"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:168
#, python-format
msgid "Cannot set-up the payment"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_capture_manually
msgid "Capture Amount Manually"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Capture Transaction"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_capture_manually
msgid "Capture the amount from Odoo, when the delivery is completed."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_city
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "City"
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_acquirer
msgid "Click to create a payment acquirer."
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_icon
msgid "Click to create a payment icon."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_transfer
msgid "Communication"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_company_id
msgid "Company"
msgstr "Compañía"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Configuration"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Configure"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:322
#, python-format
msgid "Confirm Deletion"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_module_id
msgid "Corresponding Module"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner_payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users_payment_token_count
msgid "Count Payment Token"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_country_ids
msgid "Countries"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Country"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_create_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon_create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token_create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_create_date
#: model:ir.model.fields,field_description:payment.field_payment_icon_create_date
#: model:ir.model.fields,field_description:payment.field_payment_token_create_date
msgid "Created on"
msgstr "Creado en"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_create_date
msgid "Creation Date"
msgstr "Fecha creación"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Credentials"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "Credit card(s)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_currency_id
msgid "Currency"
msgstr "Moneda"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_custom
msgid "Custom"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_id
msgid "Customer"
msgstr "Cliente"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_description
msgid "Description"
msgstr "Descripción"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_sequence
msgid "Determine the display order"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_display_name
#: model:ir.model.fields,field_description:payment.field_payment_icon_display_name
#: model:ir.model.fields,field_description:payment.field_payment_token_display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: payment
#: selection:payment.transaction,state:0
msgid "Done"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_done_msg
msgid "Done Message"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Draft"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "E-mail"
msgstr ""

#. module: payment
#: model:account.payment.method,name:payment.account_payment_method_electronic_in
msgid "Electronic"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_email
msgid "Email"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_environment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Environment"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Error"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_error_msg
msgid "Error Message"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:372
#, python-format
msgid "Error: "
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_fees
msgid "Fees"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_implemented
msgid "Fees Computation Supported"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction_fees
msgid "Fees amount; set by the system because depends on the acquirer"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction_state_message
msgid "Field used to store error and/or validation messages for information"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_dom_fixed
msgid "Fixed domestic fees"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_int_fixed
msgid "Fixed international fees"
msgstr ""

#. module: payment
#: selection:payment.transaction,type:0
msgid "Form"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_view_template_id
msgid "Form Button Template"
msgstr ""

#. module: payment
#: selection:payment.transaction,type:0
msgid "Form with tokenization"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "From"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Group By"
msgstr "Agrupar por"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_pre_msg
msgid "Help Message"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_id
#: model:ir.model.fields,field_description:payment.field_payment_icon_id
#: model:ir.model.fields,field_description:payment.field_payment_token_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction_id
msgid "ID"
msgstr "ID (identificación)"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_specific_countries
msgid ""
"If you leave it empty, the payment acquirer will be available for all the "
"countries."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_image
#: model:ir.model.fields,field_description:payment.field_payment_icon_image
msgid "Image"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon_image_payment_form
msgid "Image displayed on the payment form"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_ogone
msgid "Ingenico"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Install"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_module_state
msgid "Installation State"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction_reference
msgid "Internal reference of the TX"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_lang
msgid "Language"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer___last_update
#: model:ir.model.fields,field_description:payment.field_payment_icon___last_update
#: model:ir.model.fields,field_description:payment.field_payment_token___last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_write_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon_write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token_write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_write_date
#: model:ir.model.fields,field_description:payment.field_payment_icon_write_date
#: model:ir.model.fields,field_description:payment.field_payment_token_write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: payment
#: selection:payment.acquirer,save_token:0
msgid "Let the customer decide"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon_acquirer_ids
msgid "List of Acquirers supporting this payment icon."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_website_published
msgid "Make this payment acquirer available (Customer invoices, etc.)"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "Manage your Payment Methods"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_meth_link
msgid "Manage your payment methods"
msgstr ""

#. module: payment
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_image_medium
msgid "Medium-sized image"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_image_medium
msgid ""
"Medium-sized image of this provider. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_state_message
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Message"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_post_msg
msgid "Message displayed after having done the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_pre_msg
msgid "Message displayed to explain and help the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_error_msg
msgid "Message displayed, if error is occur during the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_cancel_msg
msgid "Message displayed, if order is cancel during the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_done_msg
msgid ""
"Message displayed, if order is done successfully after having done the "
"payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_pending_msg
msgid ""
"Message displayed, if order is in pending state after having done the "
"payment process."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Messages"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:395
#, python-format
msgid "Missing partner reference when trying to create a new payment token"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:75
#: code:addons/payment/static/src/js/payment_form.js:218
#, python-format
msgid "Missing values"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_name
#: model:ir.model.fields,field_description:payment.field_payment_icon_name
#: model:ir.model.fields,field_description:payment.field_payment_token_name
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Name"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token_name
msgid "Name of the payment token"
msgstr ""

#. module: payment
#: selection:payment.acquirer,save_token:0
msgid "Never"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:179
#: code:addons/payment/static/src/js/payment_form.js:273
#, python-format
msgid "No payment method selected"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_payment_flow
msgid ""
"Note: Subscriptions does not take this field in account, it uses server to "
"server by default."
msgstr ""

#. module: payment
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:376
#, python-format
msgid "Ok"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:759
#, python-format
msgid "Only transactions in the Authorized status can be captured."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:766
#, python-format
msgid "Only transactions in the Authorized status can be voided."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token_partner_id
msgid "Partner"
msgstr "Empresa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_name
msgid "Partner Name"
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:39
#, python-format
msgid "Pay Now"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payu
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Payment"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Payment Acquirer"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.acquirer_list
msgid "Payment Acquirers"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_icon
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
msgid "Payment Icon"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_icon
#: model:ir.ui.menu,name:payment.payment_icon_menu
msgid "Payment Icons"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_journal_id
msgid "Payment Journal"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "Payment Methods"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_payment_token_id
msgid "Payment Token"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner_payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users_payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form_view
#: model_terms:ir.ui.view,arch_db:payment.payment_token_tree_view
#: model_terms:ir.ui.view,arch_db:payment.payment_token_view_search
msgid "Payment Tokens"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
#: model:ir.model.fields,field_description:payment.field_account_payment_payment_transaction_id
msgid "Payment Transaction"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.actions.act_window,name:payment.action_payment_tx_ids
#: model:ir.model.fields,field_description:payment.field_payment_token_payment_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
#: model_terms:ir.ui.view,arch_db:payment.transaction_list
msgid "Payment Transactions"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_payment_flow
msgid "Payment flow"
msgstr ""

#. module: payment
#: code:addons/payment/models/account_payment.py:59
#, python-format
msgid "Payment transaction failed (%s)"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_account_payment
#: model:ir.ui.menu,name:payment.root_payment_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form_view
msgid "Payments"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_journal_id
msgid ""
"Payments will be registered into this journal. If you get paid straight on your bank account,\n"
"                select your bank account. If you get paid in batch for several transactions, create a specific\n"
"                payment journal for this payment acquirer to easily manage the bank reconciliation. You hold\n"
"                the amount in a temporary transfer account of your books (created automatically when you create\n"
"                the payment journal). Then when you get paid on your bank account by the payment acquirer, you\n"
"                reconcile the bank statement line with this temporary transfer account. Use reconciliation\n"
"                templates to do it in one-click."
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_paypal
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Pending"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_pending_msg
msgid "Pending Message"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_phone
msgid "Phone"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_transfer
msgid "Please use the following transfer details"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_transfer
msgid "Please use the order name as communication reference."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Processed by"
msgstr ""

#. module: payment
#: selection:payment.acquirer,environment:0
msgid "Production"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_provider
#: model:ir.model.fields,field_description:payment.field_payment_transaction_provider
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Provider"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Reference"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction_acquirer_reference
msgid "Reference of the TX as stored in the acquirer database"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Refunded"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Refunding"
msgstr ""

#. module: payment
#: constraint:payment.acquirer:0
msgid "Required fields not filled"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_registration_view_template_id
msgid "S2S Form Template"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_save_token
msgid "Save Cards"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Save my payment data"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.payment_token_action
#: model:ir.ui.menu,name:payment.payment_token_menu
msgid "Saved Payment Data"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_payment_token_id
msgid "Saved payment token"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_token_implemented
msgid "Saving Card Data supported"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:107
#: code:addons/payment/static/src/js/payment_form.js:119
#: code:addons/payment/static/src/js/payment_form.js:154
#: code:addons/payment/static/src/js/payment_form.js:160
#: code:addons/payment/static/src/js/payment_form.js:253
#: code:addons/payment/static/src/js/payment_form.js:297
#: code:addons/payment/static/src/js/payment_form.js:332
#, python-format
msgid "Server Error"
msgstr ""

#. module: payment
#: selection:payment.transaction,type:0
msgid "Server To Server"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:266
#, python-format
msgid "Server error"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token_short_name
msgid "Short name"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sips
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_image_small
msgid "Small-sized image"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_image_small
msgid ""
"Small-sized image of this provider. It is automatically resized as a 64x64px"
" image, with aspect ratio preserved. Use this field anywhere a small image "
"is required."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_specific_countries
msgid "Specific Countries"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_state
msgid "Status"
msgstr "Estado"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_stripe
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_payment_icon_ids
msgid "Supported Payment Icons"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_registration_view_template_id
msgid "Template for method registration"
msgstr ""

#. module: payment
#: selection:payment.acquirer,environment:0
msgid "Test"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_post_msg
msgid "Thanks Message"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:570
#, python-format
msgid "The %s payment acquirers are not allowed to manual capture mode!"
msgstr ""

#. module: payment
#: selection:payment.acquirer,payment_flow:0
msgid "The customer encode his payment details on the website."
msgstr ""

#. module: payment
#: selection:payment.acquirer,payment_flow:0
msgid "The customer is redirected to the website of the acquirer."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:563
#, python-format
msgid "The payment transaction reference must be unique!"
msgstr ""

#. module: payment
#: code:addons/payment/models/account_payment.py:42
#, python-format
msgid ""
"This feature is not available for payment acquirers set to the \"Authorize\" mode.\n"
"Please use a token from another provider than %s."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon_image
msgid ""
"This field holds the image used for this payment icon, limited to "
"1024x1024px"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_image
msgid ""
"This field holds the image used for this provider, limited to 1024x1024px"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_save_token
msgid ""
"This option allows customers to save their credit card as a payment token "
"and to reuse it for a later purchase.If you manage subscriptions (recurring "
"invoicing), you need it to automatically charge the customer when you issue "
"an invoice."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_country_ids
msgid ""
"This payment gateway is available for selected countries. If none is "
"selected it is available for all countries."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"This template renders the acquirer button with all necessary values.\n"
"                                            It is rendered with qWeb with the following evaluation context:"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_transfer
msgid ""
"Transfer information will be provided after choosing the payment aquirer."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_type
msgid "Type"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_date_validate
msgid "Validation Date"
msgstr ""

#. module: payment
#: selection:payment.transaction,type:0
msgid "Validation of the bank card"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_int_var
msgid "Variable international fees (in percents)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token_verified
msgid "Verified"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_website_published
msgid "Visible in Portal / Website"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Void Transaction"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:318
#, python-format
msgid "Warning!"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_transfer
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_stripe
msgid "You will be prompt with Stripe Payment page for payment information."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_adyen
msgid ""
"You will be redirected to the Adyen website after clicking on the payment "
"button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_authorize
msgid ""
"You will be redirected to the Authorize website after clicking on the "
"payment button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_buckaroo
msgid ""
"You will be redirected to the Buckaroo website after clicking on the payment"
" button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_ogone
msgid ""
"You will be redirected to the Ingenico website after clicking on the payment"
" button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_payu
msgid ""
"You will be redirected to the PayUmoney website after clicking on the "
"payment button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_paypal
msgid ""
"You will be redirected to the Paypal website after clicking on the payment "
"button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_sips
msgid ""
"You will be redirected to the Sips website after clicking on payment button."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "ZIP"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_zip
msgid "Zip"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "acquirer: payment.acquirer browse record"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "amount: the transaction amount, a float"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "and more"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "context: the current context dictionary"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "currency: the transaction currency browse record"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "partner: the buyer partner browse record, not necessarily set"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"partner_values: specific values about the buyer, for example coming from a "
"shipping form"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_token
msgid "payment.token"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "reference: the transaction reference number"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "tx_url: transaction URL to post the form"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "tx_values: transaction values"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "user: current user browse record"
msgstr ""
