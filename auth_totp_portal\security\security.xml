<odoo>
    <!-- copies ACL for portal users -->
    <record model="ir.model.access" id="access_auth_totp_portal_wizard">
        <field name="name">auth_totp_portal wizard access rules</field>
        <field name="model_id" ref="auth_totp.model_auth_totp_wizard"/>
        <field name="group_id" ref="base.group_portal"/>
        <field name="perm_read">1</field>
        <field name="perm_write">1</field>
        <field name="perm_create">1</field>
        <field name="perm_unlink">1</field>
    </record>
    <!-- adds portal users to rule -->
    <record model="ir.rule" id="auth_totp.rule_auth_totp_wizard">
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>
</odoo>
