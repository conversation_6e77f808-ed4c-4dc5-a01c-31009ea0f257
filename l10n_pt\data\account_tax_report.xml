<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tax_report" model="account.tax.report">
        <field name="name">Relatório de IVA</field>
        <field name="country_id" ref="base.pt"/>
    </record>

    <record id="tax_report_pt_base" model="account.tax.report.line">
        <field name="name">Base</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_pt_base_vendas" model="account.tax.report.line">
        <field name="name">Vendas</field>
        <field name="parent_id" ref="tax_report_pt_base"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_pt_base_vendas_faturas" model="account.tax.report.line">
        <field name="name">Faturas/Notas de débito</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_base_vendas_faturas_normal" model="account.tax.report.line">
        <field name="name">Normal</field>
        <field name="tag_name">tax_report_pt_base_vendas_faturas_normal</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_base_vendas_faturas_intermedia" model="account.tax.report.line">
        <field name="name">Intermédia</field>
        <field name="tag_name">tax_report_pt_base_vendas_faturas_intermedia</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_pt_base_vendas_faturas_reduzida" model="account.tax.report.line">
        <field name="name">Reduzida</field>
        <field name="tag_name">tax_report_pt_base_vendas_faturas_reduzida</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_base_vendas_faturas_isento" model="account.tax.report.line">
        <field name="name">Isento</field>
        <field name="tag_name">tax_report_pt_base_vendas_faturas_isento</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_pt_base_vendas_notas_credito" model="account.tax.report.line">
        <field name="name">Notas de crédito</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_base_vendas_notas_credito_normal" model="account.tax.report.line">
        <field name="name">Normal</field>
        <field name="tag_name">tax_report_pt_base_vendas_notas_credito_normal</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_base_vendas_notas_credito_intermedia" model="account.tax.report.line">
        <field name="name">Intermédia</field>
        <field name="tag_name">tax_report_pt_base_vendas_notas_credito_intermedia</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_pt_base_vendas_notas_credito_reduzida" model="account.tax.report.line">
        <field name="name">Reduzida</field>
        <field name="tag_name">tax_report_pt_base_vendas_notas_credito_reduzida</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_base_vendas_notas_credito_isento" model="account.tax.report.line">
        <field name="name">Isento</field>
        <field name="tag_name">tax_report_pt_base_vendas_notas_credito_isento</field>
        <field name="parent_id" ref="tax_report_pt_base_vendas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_pt_base_despesas" model="account.tax.report.line">
        <field name="name">Despesas</field>
        <field name="parent_id" ref="tax_report_pt_base"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_pt_base_despesas_faturas" model="account.tax.report.line">
        <field name="name">Faturas</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_base_despesas_faturas_normal" model="account.tax.report.line">
        <field name="name">Normal</field>
        <field name="tag_name">tax_report_pt_base_despesas_faturas_normal</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_base_despesas_faturas_intermedia" model="account.tax.report.line">
        <field name="name">Intermédia</field>
        <field name="tag_name">tax_report_pt_base_despesas_faturas_intermedia</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_pt_base_despesas_faturas_reduzida" model="account.tax.report.line">
        <field name="name">Reduzida</field>
        <field name="tag_name">tax_report_pt_base_despesas_faturas_reduzida</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_base_despesas_faturas_isento" model="account.tax.report.line">
        <field name="name">Isento</field>
        <field name="tag_name">tax_report_pt_base_despesas_faturas_isento</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_pt_base_despesas_notas_credito" model="account.tax.report.line">
        <field name="name">Notas de crédito</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_base_despesas_notas_credito_normal" model="account.tax.report.line">
        <field name="name">Normal</field>
        <field name="tag_name">tax_report_pt_base_despesas_notas_credito_normal</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_base_despesas_notas_credito_intermedia" model="account.tax.report.line">
        <field name="name">Intermédia</field>
        <field name="tag_name">tax_report_pt_base_despesas_notas_credito_intermedia</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_pt_base_despesas_notas_credito_reduzida" model="account.tax.report.line">
        <field name="name">Reduzida</field>
        <field name="tag_name">tax_report_pt_base_despesas_notas_credito_reduzida</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_base_despesas_notas_credito_isento" model="account.tax.report.line">
        <field name="name">Isento</field>
        <field name="tag_name">tax_report_pt_base_despesas_notas_credito_isento</field>
        <field name="parent_id" ref="tax_report_pt_base_despesas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">4</field>
    </record>


    <record id="tax_report_pt_iva" model="account.tax.report.line">
        <field name="name">IVA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_pt_tax_vendas" model="account.tax.report.line">
        <field name="name">Vendas</field>
        <field name="parent_id" ref="tax_report_pt_iva"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_pt_tax_vendas_faturas" model="account.tax.report.line">
        <field name="name">Faturas/Notas de débito (Total IVA liquidado)</field>
        <field name="code">tax_report_pt_tax_vendas_faturas</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_tax_vendas_faturas_normal" model="account.tax.report.line">
        <field name="name">Normal</field>
        <field name="tag_name">tax_report_pt_tax_vendas_faturas_normal</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_tax_vendas_faturas_intermedia" model="account.tax.report.line">
        <field name="name">Intermédia</field>
        <field name="tag_name">tax_report_pt_tax_vendas_faturas_intermedia</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_pt_tax_vendas_faturas_reduzida" model="account.tax.report.line">
        <field name="name">Reduzida</field>
        <field name="tag_name">tax_report_pt_tax_vendas_faturas_reduzida</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_tax_vendas_faturas_isento" model="account.tax.report.line">
        <field name="name">Isento</field>
        <field name="tag_name">tax_report_pt_tax_vendas_faturas_isento</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_pt_tax_vendas_notas_credito" model="account.tax.report.line">
        <field name="name">Notas de crédito (Total IVA regularizado a favor da empresa)</field>
        <field name="code">tax_report_pt_tax_vendas_notas_credito</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_tax_vendas_notas_credito_normal" model="account.tax.report.line">
        <field name="name">Normal</field>
        <field name="tag_name">tax_report_pt_tax_vendas_notas_credito_normal</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_tax_vendas_notas_credito_intermedia" model="account.tax.report.line">
        <field name="name">Intermédia</field>
        <field name="tag_name">tax_report_pt_tax_vendas_notas_credito_intermedia</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_pt_tax_vendas_notas_credito_reduzida" model="account.tax.report.line">
        <field name="name">Reduzida</field>
        <field name="tag_name">tax_report_pt_tax_vendas_notas_credito_reduzida</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_tax_vendas_notas_credito_isento" model="account.tax.report.line">
        <field name="name">Isento</field>
        <field name="tag_name">tax_report_pt_tax_vendas_notas_credito_isento</field>
        <field name="parent_id" ref="tax_report_pt_tax_vendas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_pt_tax_despesas" model="account.tax.report.line">
        <field name="name">Despesas</field>
        <field name="parent_id" ref="tax_report_pt_iva"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_pt_tax_despesas_faturas" model="account.tax.report.line">
        <field name="name">Faturas (Total IVA dedutível)</field>
        <field name="code">tax_report_pt_tax_despesas_faturas</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_tax_despesas_faturas_normal" model="account.tax.report.line">
        <field name="name">Normal</field>
        <field name="tag_name">tax_report_pt_tax_despesas_faturas_normal</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_tax_despesas_faturas_intermedia" model="account.tax.report.line">
        <field name="name">Intermédia</field>
        <field name="tag_name">tax_report_pt_tax_despesas_faturas_intermedia</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_pt_tax_despesas_faturas_reduzida" model="account.tax.report.line">
        <field name="name">Reduzida</field>
        <field name="tag_name">tax_report_pt_tax_despesas_faturas_reduzida</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_tax_despesas_faturas_isento" model="account.tax.report.line">
        <field name="name">Isento</field>
        <field name="tag_name">tax_report_pt_tax_despesas_faturas_isento</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas_faturas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_pt_tax_despesas_notas_credito" model="account.tax.report.line">
        <field name="name">Notas de crédito (Total IVA regularizado a favor do Estado)</field>
        <field name="code">tax_report_pt_tax_despesas_notas_credito</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_tax_despesas_notas_credito_normal" model="account.tax.report.line">
        <field name="name">Normal</field>
        <field name="tag_name">tax_report_pt_tax_despesas_notas_credito_normal</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_pt_tax_despesas_notas_credito_intermedia" model="account.tax.report.line">
        <field name="name">Intermédia</field>
        <field name="tag_name">tax_report_pt_tax_despesas_notas_credito_intermedia</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_pt_tax_despesas_notas_credito_reduzida" model="account.tax.report.line">
        <field name="name">Reduzida</field>
        <field name="tag_name">tax_report_pt_tax_despesas_notas_credito_reduzida</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_pt_tax_despesas_notas_credito_isento" model="account.tax.report.line">
        <field name="name">Isento</field>
        <field name="tag_name">tax_report_pt_tax_despesas_notas_credito_isento</field>
        <field name="parent_id" ref="tax_report_pt_tax_despesas_notas_credito"/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_pt_tax_total_pagar" model="account.tax.report.line">
        <field name="name">TOTAL IVA A PAGAR</field>
        <field name="code">tax_report_pt_tax_total_pagar</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">3</field>
        <field name="formula">max(0, tax_report_pt_tax_vendas_faturas - tax_report_pt_tax_vendas_notas_credito - tax_report_pt_tax_despesas_faturas + tax_report_pt_tax_despesas_notas_credito)</field>
    </record>

    <record id="tax_report_pt_tax_total_receber" model="account.tax.report.line">
        <field name="name">TOTAL IVA A RECEBER</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">4</field>
        <field name="formula">max(0, -tax_report_pt_tax_vendas_faturas + tax_report_pt_tax_vendas_notas_credito + tax_report_pt_tax_despesas_faturas - tax_report_pt_tax_despesas_notas_credito)</field>
    </record>
</odoo>
