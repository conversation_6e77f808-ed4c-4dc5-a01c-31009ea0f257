<?xml version="1.0" encoding="utf-8"?>
<odoo>



    <!--Loan Tree view-->
    <record id="hr_loan_sittigs_tree_view" model="ir.ui.view">
        <field name="name">hr.loan.sittings.tree</field>
        <field name="model">hr.loan.sittings</field>
        <field name="arch" type="xml">
            <tree  editable="bottom" string="Loan Requests Sittings">
                <field name="name"/>
                <field name="number_of_installments"/>
                <field name="min_amount"/>
                <field name="max_amount"/>
            </tree>
        </field>
    </record>


    <record id="act_hr_loan_sittings_view" model="ir.actions.act_window">
        <field name="name">Loans Sittings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.loan.sittings</field>
        <field name="view_mode">tree</field>
    </record>

        <!--loan menu-->
    <menuitem name="Sittings"
              id="menu_base_hr_loan_sittings"
              parent="menu_hr_loans_and_advances"
              action="act_hr_loan_sittings_view"
              sequence="2"/>

</odoo>
<!--              groups="hr_approvales_masarat.group_hr_approvales_masarat"-->
