# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_stock
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:170
#, python-format
msgid ""
"\n"
"There are %s %s available accross all warehouses."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference:</strong>"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_invoice_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterms:</strong>"
msgstr ""

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_aftersale
msgid "After-Sale"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Apply special routes from orders (e.g. dropshipping, MTO)"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid ""
"By default Odoo computes the expected delivery date this way: order date + "
"highest customer lead time of products. With this option, you can set a "
"specific delivery date on orders rather than setting lead times on products."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Preduzeća"

#. module: sale_stock
#: selection:sale.order,picking_policy:0
msgid "Deliver all products at once"
msgstr ""

#. module: sale_stock
#: selection:sale.order,picking_policy:0
msgid "Deliver each product when available"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Isporuka"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings_module_sale_order_dates
msgid "Delivery Date"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_delivery_count
msgid "Delivery Orders"
msgstr "Nalozi za isporuku"

#. module: sale_stock
#: model:res.groups,name:sale_stock.group_display_incoterm
msgid "Display incoterms on Sales Order and related invoices"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Display incoterms on orders &amp; invoices"
msgstr ""

#. module: sale_stock
#: model:res.groups,name:sale_stock.group_route_so_lines
msgid "Enable Route on Sales Order Line"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_account_invoice_incoterms_id
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings_group_display_incoterm
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_incoterm
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Incoterms"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_account_invoice_incoterms_id
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-"
"the-art transportation practices."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_location_route
msgid "Inventory Routes"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_invoice_line
msgid "Invoice Line"
msgstr "Stavka računa"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_invoiced
msgid "Invoicing"
msgstr "Fakturisanje"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings_use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company_security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings_security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised"
" date, to cope with unexpected delays in the supply chain."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:174
#, python-format
msgid "Not enough inventory!"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings_group_route_so_lines
msgid "Order-Specific Routes"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:188
#, python-format
msgid "Ordered quantity decreased!"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line_product_packaging
msgid "Package"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_picking_ids
msgid "Pickings"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_procurement_group_id
msgid "Procurement Group"
msgstr "Grupa nabavke"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
msgid "Procurement Requisition"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_rule
msgid "Procurement Rule"
msgstr "Pravilo nabave"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
msgid "Quotation"
msgstr "Ponuda"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Reserving products manually in delivery orders or by running the scheduler "
"is advised to better manage priorities in case of long customer lead times "
"or/and frequent stock-outs. By default, the scheduler runs automatically "
"every 24 hours."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line_route_id
msgid "Route"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Routes for Sales Order Lines"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move_sale_line_id
msgid "Sale Line"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group_sale_id
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "Sales"
msgstr "Prodaja"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking_sale_id
msgid "Sales Order"
msgstr "Prodajni Nalog"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Stavka naloga za prodaju"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Stavke prodajnog naloga"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Orders Statistics"
msgstr "Statistika Prodajnih Naloga"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company_security_lead
msgid "Sales Safety Days"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings_security_lead
msgid "Security Lead Time"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings_use_security_lead
msgid "Security Lead Time for Sales"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_location_route_sale_selectable
msgid "Selectable on Sales Order Line"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Set a delivery date on orders"
msgstr ""

#. module: sale_stock
#: selection:res.config.settings,default_picking_policy:0
msgid "Ship all products at once"
msgstr ""

#. module: sale_stock
#: selection:res.config.settings,default_picking_policy:0
msgid "Ship products as soon as available, with back orders"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Shipping Information"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings_default_picking_policy
msgid "Shipping Management"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_picking_policy
msgid "Shipping Policy"
msgstr "Opcije transporta"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Skladišni prenosi"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line_move_ids
msgid "Stock Moves"
msgstr "Prenosnice"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid ""
"This allows to apply a special route on sales order lines (e.g. "
"dropshipping, make-to-order) in case of unusual order scenario. To be "
"available in sales orders, routes must have 'Sales Order Line' option "
"checked in their setup form."
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:273
#, python-format
msgid "This product is packaged by %.2f %s. You should sell %.2f %s."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report_warehouse_id
msgid "Warehouse"
msgstr "Skladište"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:272
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to reserve sold products"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:189
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the delivery order if needed."
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:166
#, python-format
msgid ""
"You plan to sell %s %s but you only have %s %s available in %s warehouse."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "dana"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "res.config.settings"
msgstr ""
