// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------
.o_DiscussSidebarCategory_command {
    margin-left: $o-mail-discuss-sidebar-category-item-margin;
    margin-right:$o-mail-discuss-sidebar-category-item-margin;

    &:first-child {
        margin-left: 0;
    }

    &:last-child {
        margin-right: 0;
    }
}

.o_DiscussSidebarCategory_commands {
    display: flex;
}

.o_DiscussSidebarCategory_header {
    display: flex;
    align-items: center;
    margin: 5px 0;
}

.o_DiscussSidebarCategory_headerItem {
    margin-left: $o-mail-discuss-sidebar-category-item-margin;
    margin-right: $o-mail-discuss-sidebar-category-item-margin;

    &:last-child {
        margin-right: $o-mail-discuss-sidebar-scrollbar-width;
    }
}

.o_DiscussSidebarCategory_itemNewInput {
    flex: 1 1 auto;
    margin-left: $o-mail-discuss-sidebar-category-item-avatar-left-margin;
    margin-right: $o-mail-discuss-sidebar-scrollbar-width;
}

.o_DiscussSidebarCategory_newChannelAutocompleteSuggestions {
    word-wrap: break-word;
}

.o_DiscussSidebarCategory_newItem {
    display: flex;
    justify-content: center;
    padding-bottom: map-get($spacers, 2);
}

.o_DiscussSidebarCategory_title {
    display: flex;
    align-items: baseline;
}

.o_DiscussSidebarCategory_titleIcon {
    width: $o-mail-discuss-sidebar-category-title-icon-size;
    height: $o-mail-discuss-sidebar-category-title-icon-size;
}


// ------------------------------------------------------------------
// style
// ------------------------------------------------------------------

.o_DiscussSidebarCategory_command {
    cursor: pointer;

    &:not(:hover) {
        color: gray('600');
    }
}

.o_DiscussSidebarCategory_counter {
    background-color: $o-brand-primary;
    color: gray('300');
}

.o_DiscussSidebarCategory_headerItem {
    user-select: none;
}

.o_DiscussSidebarCategory_itemNewInput {
    outline: none;
}

.o_DiscussSidebarCategory_title {
    cursor: pointer;

    &:not(:hover)
    {
        color: gray('600');
    }
}

.o_DiscussSidebarCategory_titleIcon {
    font-size: 0.75em;
}

.o_DiscussSidebarCategory_titleText {
    font-size: $font-size-sm;
    text-transform: uppercase;
    font-weight: bolder;
}
