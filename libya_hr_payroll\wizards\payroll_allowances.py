# -*- coding: utf-8 -*-

from odoo import fields, models, api
from dateutil.relativedelta import relativedelta
from datetime import date, datetime


class CreateWizard(models.TransientModel):
    _name = 'payroll.allowances'
    _description = 'Payroll allowances'

    date_from = fields.Date("Date From",default=lambda self: fields.Date.to_string(date.today().replace(day=1)),required=True)
    date_to = fields.Date("Date To",default=lambda self: fields.Date.to_string((datetime.now() + relativedelta(months=+1, day=1, days=-1)).date()),required=True)
    #work_location = fields.Many2one('work.location', 'Work Location')
    employee_id = fields.Many2one('hr.employee', 'Employee')



    def generate_payroll_allowances(self):
        # data = self._get_report_pdf()
        # print(data)
        data = {'model': self._name, 'ids': self.ids, 'date_from': self.date_from, 'date_to': self.date_to,
                'employee_id': self.employee_id.id}
        return self.env.ref('libya_hr_payroll.report_payroll_libya_allowances_pdf').report_action(self,data=data)



class PayrollAllowancesReport_x1(models.AbstractModel):
    _name = 'report.libya_hr_payroll.report_libya_allowances'
    _description = 'Payroll allowances'


    def _get_report_values(self, docids, data=None):
        if data['employee_id']:
            exist_payslip = self.env['hr.payslip'].search([('date_from','>=',data['date_from']),('date_to','<=',data['date_to']),
                                                       ('employee_id','=',data['employee_id'])])
        else:
            exist_payslip = self.env['hr.payslip'].search(
                [('date_from','>=',data['date_from']),('date_to','<=',data['date_to'])])
        date_month = datetime.strptime(data['date_from'],'%Y-%m-%d')
        payslip_ids = []
        for payslip in exist_payslip:
            payslip_ids.append({
                'employee_name': payslip.employee_id.name,
                'basic': payslip.line_ids.filtered(lambda line: line.code == 'BASIC').total,
                'Other': payslip.line_ids.filtered(lambda line: line.code == 'Other').total,
                'HRA': payslip.line_ids.filtered(lambda line: line.code == 'HRA').total,
                'Meal': payslip.line_ids.filtered(lambda line: line.code == 'Meal').total,
                'TOBASIC':payslip.line_ids.filtered(lambda line: line.code == 'TOBASIC').total,
                'MDED':payslip.line_ids.filtered(lambda line: line.code == 'MDED').total,
                'TDDED':payslip.line_ids.filtered(lambda line: line.code == 'TDDED').total,
                'Travel':payslip.line_ids.filtered(lambda line: line.code == 'Travel').total,
                'DA':payslip.line_ids.filtered(lambda line: line.code == 'DA').total,
                'Medical':payslip.line_ids.filtered(lambda line: line.code == 'Medical').total,
                'NETSALARY':payslip.line_ids.filtered(lambda line: line.code == 'NETSALARY').total,
                'EOS': payslip.line_ids.filtered(lambda line: line.code == 'EOS').total,


            })

        report_data = {
            'payslip_ids': payslip_ids,
            'date_month':date_month.month,
            'date_year' : date_month.year,
            'location' : '/'
        }

        return report_data
