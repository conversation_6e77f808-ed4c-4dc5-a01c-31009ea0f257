<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
    <record id="account_group_1" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1</field>
        <field name="name">Financiación Básica</field>
    </record>
    <record id="account_group_10" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">10</field>
        <field name="name">Capital</field>
    </record>
    <record id="account_group_100" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">100</field>
        <field name="name">Capital social</field>
    </record>
    <record id="account_group_101" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">101</field>
        <field name="name">Fondo social</field>
    </record>
    <record id="account_group_102" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">102</field>
        <field name="name">Capital</field>
    </record>
    <record id="account_group_103" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">103</field>
        <field name="name">Socios por desembolsos no exigidos</field>
    </record>
    <record id="account_group_1030" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1030</field>
        <field name="name">Socios por desembolsos no exigidos, capital social</field>
    </record>
    <record id="account_group_1034" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1034</field>
        <field name="name">Socios por desembolsos no exigidos, capital pendiente de inscripción</field>
    </record>
    <record id="account_group_104" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">104</field>
        <field name="name">Socios por aportaciones no dinerarias pendientes</field>
    </record>
    <record id="account_group_1040" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1040</field>
        <field name="name">Socios por aportaciones no dinerarias pendientes, capital social</field>
    </record>
    <record id="account_group_1044" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1044</field>
        <field name="name">Socios por aportaciones no dinerarias pendientes, capital pendiente de inscripción</field>
    </record>
    <record id="account_group_108" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">108</field>
        <field name="name">Acciones o participaciones propias en situaciones especiales</field>
    </record>
    <record id="account_group_109" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">109</field>
        <field name="name">Acciones o participaciones propias para reducción de capital</field>
    </record>
    <record id="account_group_11" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">11</field>
        <field name="name">Reservas y otros instrumentos de patrimonio</field>
    </record>
    <record id="account_group_110" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">110</field>
        <field name="name">Prima de emisión o asunción</field>
    </record>
    <record id="account_group_111" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">111</field>
        <field name="name">Otros instrumentos de patrimonio neto</field>
    </record>
    <record id="account_group_1110" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1110</field>
        <field name="name">Patrimonio neto por emisión de instrumentos financieros compuestos</field>
    </record>
    <record id="account_group_1111" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1111</field>
        <field name="name">Resto de instrumentos de patrimonio neto</field>
    </record>
    <record id="account_group_112" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">112</field>
        <field name="name">Reserva legal</field>
    </record>
    <record id="account_group_113" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">113</field>
        <field name="name">Reservas voluntarias</field>
    </record>
    <record id="account_group_114" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">114</field>
        <field name="name">Reservas especiales</field>
    </record>
    <record id="account_group_1140" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1140</field>
        <field name="name">Reservas para acciones o participaciones de la sociedad dominante</field>
    </record>
    <record id="account_group_1141" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1141</field>
        <field name="name">Reservas estatutarias</field>
    </record>
    <record id="account_group_1142" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1142</field>
        <field name="name">Reserva por capital amortizado</field>
    </record>
    <record id="account_group_1143" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1143</field>
        <field name="name">Reserva por fondo de comercio</field>
    </record>
    <record id="account_group_1144" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1144</field>
        <field name="name">Reservas por acciones propias aceptadas en garantía</field>
    </record>
    <record id="account_group_115" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">115</field>
        <field name="name">Reservas por pérdidas y ganancias actuariales y otros ajustes</field>
    </record>
    <record id="account_group_118" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">118</field>
        <field name="name">Aportaciones de socios o propietarios</field>
    </record>
    <record id="account_group_119" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">119</field>
        <field name="name">Diferencias por ajuste del capital a euros</field>
    </record>
    <record id="account_group_12" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">12</field>
        <field name="name">Resultados pendientes de aplicación</field>
    </record>
    <record id="account_group_120" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">120</field>
        <field name="name">Remanente</field>
    </record>
    <record id="account_group_121" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">121</field>
        <field name="name">Resultados negativos de ejercicios anteriores</field>
    </record>
    <record id="account_group_129" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">129</field>
        <field name="name">Resultado del ejercicio</field>
    </record>
    <record id="account_group_13" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">13</field>
        <field name="name">Subvenciones, donaciones y ajustes por cambios de valor</field>
    </record>
    <record id="account_group_130" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">130</field>
        <field name="name">Subvenciones oficiales de capital</field>
    </record>
    <record id="account_group_131" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">131</field>
        <field name="name">Donaciones y legados de capital</field>
    </record>
    <record id="account_group_132" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">132</field>
        <field name="name">Otras subvenciones, donaciones y legados</field>
    </record>
    <record id="account_group_133" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">133</field>
        <field name="name">Ajustes por valoración en activos financieros disponibles para la venta</field>
    </record>
    <record id="account_group_134" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">134</field>
        <field name="name">Operaciones de cobertura</field>
    </record>
    <record id="account_group_1340" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1340</field>
        <field name="name">Cobertura de flujos de efectivo</field>
    </record>
    <record id="account_group_1341" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1341</field>
        <field name="name">Cobertura de una inversión neta en un negocio en el extranjero</field>
    </record>
    <record id="account_group_135" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">135</field>
        <field name="name">Diferencias de conversión</field>
    </record>
    <record id="account_group_136" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">136</field>
        <field name="name">Ajustes por valoración en activos no corrientes y grupos enajenables de elementos, mantenidos para la venta</field>
    </record>
    <record id="account_group_137" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">137</field>
        <field name="name">Ingresos fiscales a distribuir en varios ejercicios</field>
    </record>
    <record id="account_group_1370" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1370</field>
        <field name="name">Ingresos fiscales por diferencias permanentes a distribuir en varios ejercicios</field>
    </record>
    <record id="account_group_1371" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1371</field>
        <field name="name">Ingresos fiscales por deducciones y bonificaciones a distribuir en varios ejercicios</field>
    </record>
    <record id="account_group_14" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">14</field>
        <field name="name">Provisiones</field>
    </record>
    <record id="account_group_140" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">140</field>
        <field name="name">Provisión por retribuciones a largo plazo al personal</field>
    </record>
    <record id="account_group_141" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">141</field>
        <field name="name">Provisión para impuestos</field>
    </record>
    <record id="account_group_142" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">142</field>
        <field name="name">Provisión para otras responsabilidades</field>
    </record>
    <record id="account_group_143" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">143</field>
        <field name="name">Provisión por desmantelamiento, retiro o rehabilitación del inmovilizado</field>
    </record>
    <record id="account_group_145" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">145</field>
        <field name="name">Provisión para actuaciones medioambientales</field>
    </record>
    <record id="account_group_146" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">146</field>
        <field name="name">Provisión para reestructuraciones</field>
    </record>
    <record id="account_group_147" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">147</field>
        <field name="name">Provisión por transacciones con pagos basados en instrumentos de patrimonio</field>
    </record>
    <record id="account_group_15" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">15</field>
        <field name="name">Deudas a largo plazo con características especiales</field>
    </record>
    <record id="account_group_150" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">150</field>
        <field name="name">Acciones o participaciones a largo plazo consideradas como pasivos financieros</field>
    </record>
    <record id="account_group_153" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">153</field>
        <field name="name">Desembolsos no exigidos por acciones o participaciones consideradas como pasivos financieros</field>
    </record>
    <record id="account_group_1533" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1533</field>
        <field name="name">Desembolsos no exigidos, empresas del grupo</field>
    </record>
    <record id="account_group_1534" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1534</field>
        <field name="name">Desembolsos no exigidos, empresas asociadas</field>
    </record>
    <record id="account_group_1535" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1535</field>
        <field name="name">Desembolsos no exigidos, otras partes vinculadas</field>
    </record>
    <record id="account_group_1536" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1536</field>
        <field name="name">Otros desembolsos no exigidos</field>
    </record>
    <record id="account_group_154" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">154</field>
        <field name="name">Aportaciones no dinerarias pendientes por acciones o participaciones consideradas como pasivos financieros</field>
    </record>
    <record id="account_group_1543" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1543</field>
        <field name="name">Aportaciones no dinerarias pendientes, empresas del grupo</field>
    </record>
    <record id="account_group_1544" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1544</field>
        <field name="name">Aportaciones no dinerarias pendientes, empresas asociadas</field>
    </record>
    <record id="account_group_1545" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1545</field>
        <field name="name">Aportaciones no dinerarias pendientes, otras partes vinculadas</field>
    </record>
    <record id="account_group_1546" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1546</field>
        <field name="name">Otras aportaciones no dinerarias pendientes</field>
    </record>
    <record id="account_group_16" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">16</field>
        <field name="name">Deudas a largo plazo con partes vinculadas</field>
    </record>
    <record id="account_group_160" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">160</field>
        <field name="name">Deudas a largo plazo con entidades de crédito vinculadas</field>
    </record>
    <record id="account_group_1603" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1603</field>
        <field name="name">Deudas a largo plazo con entidades de crédito, empresas del grupo</field>
    </record>
    <record id="account_group_1604" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1604</field>
        <field name="name">Deudas a largo plazo con entidades de crédito, empresas asociadas</field>
    </record>
    <record id="account_group_1605" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1605</field>
        <field name="name">Deudas a largo plazo con otras entidades de crédito vinculadas</field>
    </record>
    <record id="account_group_161" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">161</field>
        <field name="name">Proveedores de inmovilizado a largo plazo, partes vinculadas</field>
    </record>
    <record id="account_group_1613" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1613</field>
        <field name="name">Proveedores de inmovilizado a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_1614" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1614</field>
        <field name="name">Proveedores de inmovilizado a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_1615" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1615</field>
        <field name="name">Proveedores de inmovilizado a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_162" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">162</field>
        <field name="name">Acreedores por arrendamiento financiero a largo plazo, partes vinculadas</field>
    </record>
    <record id="account_group_1623" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1623</field>
        <field name="name">Acreedores por arrendamiento financiero a largo plazo, empresas de grupo</field>
    </record>
    <record id="account_group_1624" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1624</field>
        <field name="name">Acreedores por arrendamiento financiero a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_1625" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1625</field>
        <field name="name">Acreedores por arrendamiento financiero a largo plazo, otras partes vinculadas.</field>
    </record>
    <record id="account_group_163" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">163</field>
        <field name="name">Otras deudas a largo plazo con partes vinculadas</field>
    </record>
    <record id="account_group_1633" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1633</field>
        <field name="name">Otras deudas a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_1634" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1634</field>
        <field name="name">Otras deudas a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_1635" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1635</field>
        <field name="name">Otras deudas a largo plazo, con otras partes vinculadas</field>
    </record>
    <record id="account_group_17" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">17</field>
        <field name="name">Deudas a largo plazo por préstamos recibidos, empréstitos y otros conceptos</field>
    </record>
    <record id="account_group_170" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">170</field>
        <field name="name">Deudas a largo plazo con entidades de crédito</field>
    </record>
    <record id="account_group_171" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">171</field>
        <field name="name">Deudas a largo plazo</field>
    </record>
    <record id="account_group_172" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">172</field>
        <field name="name">Deudas a largo plazo transformables en subvenciones, donaciones y legados</field>
    </record>
    <record id="account_group_173" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">173</field>
        <field name="name">Proveedores de inmovilizado a largo plazo</field>
    </record>
    <record id="account_group_174" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">174</field>
        <field name="name">Acreedores por arrendamiento financiero a largo plazo</field>
    </record>
    <record id="account_group_175" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">175</field>
        <field name="name">Efectos a pagar a largo plazo</field>
    </record>
    <record id="account_group_176" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">176</field>
        <field name="name">Pasivos por derivados financieros a largo plazo</field>
    </record>
    <record id="account_group_1760" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1760</field>
        <field name="name">Pasivos por derivados financieros</field>
    </record>
    <record id="account_group_1765" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1765</field>
        <field name="name">Pasivos por derivados financieros a largo plazo, cartera de negociación</field>
    </record>
    <record id="account_group_1768" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">1768</field>
        <field name="name">Pasivos por derivados financieros a largo plazo, instrumentos de cobertura</field>
    </record>
    <record id="account_group_177" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">177</field>
        <field name="name">Obligaciones y bonos</field>
    </record>
    <record id="account_group_178" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">178</field>
        <field name="name">Obligaciones y bonos convertibles</field>
    </record>
    <record id="account_group_179" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">179</field>
        <field name="name">Deudas representadas en otros valores negociables</field>
    </record>
    <record id="account_group_18" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">18</field>
        <field name="name">Pasivos por fianzas, garantías y otros conceptos a largo plazo</field>
    </record>
    <record id="account_group_180" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">180</field>
        <field name="name">Fianzas recibidas a largo plazo</field>
    </record>
    <record id="account_group_181" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">181</field>
        <field name="name">Anticipos recibidos por ventas o prestaciones de servicios a largo plazo</field>
    </record>
    <record id="account_group_185" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">185</field>
        <field name="name">Depósitos recibidos a largo plazo</field>
    </record>
    <record id="account_group_189" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">189</field>
        <field name="name">Garantías financieras a largo plazo</field>
    </record>
    <record id="account_group_19" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">19</field>
        <field name="name">Situaciones transitorias de financiación</field>
    </record>
    <record id="account_group_190" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">190</field>
        <field name="name">Acciones o participaciones emitidas</field>
    </record>
    <record id="account_group_192" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">192</field>
        <field name="name">Suscriptores de acciones</field>
    </record>
    <record id="account_group_194" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">194</field>
        <field name="name">Capital emitido pendiente de inscripción</field>
    </record>
    <record id="account_group_195" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">195</field>
        <field name="name">Acciones o participaciones emitidas consideradas como pasivos financieros</field>
    </record>
    <record id="account_group_197" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">197</field>
        <field name="name">Suscriptores de acciones consideradas como pasivos financieros</field>
    </record>
    <record id="account_group_199" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">199</field>
        <field name="name">Acciones o participaciones emitidas consideradas como pasivos financieros pendientes de inscripción.</field>
    </record>
    <record id="account_group_2" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2</field>
        <field name="name">Activo no corriente</field>
    </record>
    <record id="account_group_20" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">20</field>
        <field name="name">Inmovilizaciones intangibles</field>
    </record>
    <record id="account_group_200" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">200</field>
        <field name="name">Investigación</field>
    </record>
    <record id="account_group_201" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">201</field>
        <field name="name">Desarrollo</field>
    </record>
    <record id="account_group_202" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">202</field>
        <field name="name">Concesiones administrativas</field>
    </record>
    <record id="account_group_203" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">203</field>
        <field name="name">Propiedad industrial</field>
    </record>
    <record id="account_group_204" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">204</field>
        <field name="name">Fondo de comercio</field>
    </record>
    <record id="account_group_205" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">205</field>
        <field name="name">Derechos de traspaso</field>
    </record>
    <record id="account_group_206" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">206</field>
        <field name="name">Aplicaciones informáticas</field>
    </record>
    <record id="account_group_207" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">207</field>
        <field name="name">Derechos sobre activos cedidos en uso</field>
    </record>
    <record id="account_group_209" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">209</field>
        <field name="name">Anticipos para inmovilizaciones intangibles</field>
    </record>
    <record id="account_group_21" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">21</field>
        <field name="name">Inmovilizaciones materiales</field>
    </record>
    <record id="account_group_210" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">210</field>
        <field name="name">Terrenos y bienes naturales</field>
    </record>
    <record id="account_group_211" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">211</field>
        <field name="name">Construcciones</field>
    </record>
    <record id="account_group_212" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">212</field>
        <field name="name">Instalaciones técnicas</field>
    </record>
    <record id="account_group_213" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">213</field>
        <field name="name">Maquinaria</field>
    </record>
    <record id="account_group_214" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">214</field>
        <field name="name">Utillaje</field>
    </record>
    <record id="account_group_215" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">215</field>
        <field name="name">Otras instalaciones</field>
    </record>
    <record id="account_group_216" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">216</field>
        <field name="name">Mobiliario</field>
    </record>
    <record id="account_group_217" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">217</field>
        <field name="name">Equipos para procesos de información</field>
    </record>
    <record id="account_group_218" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">218</field>
        <field name="name">Elementos de transporte</field>
    </record>
    <record id="account_group_219" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">219</field>
        <field name="name">Otro inmovilizado material</field>
    </record>
    <record id="account_group_22" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">22</field>
        <field name="name">Inversiones inmobiliarias</field>
    </record>
    <record id="account_group_220" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">220</field>
        <field name="name">Inversiones en terrenos y bienes naturales</field>
    </record>
    <record id="account_group_221" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">221</field>
        <field name="name">Inversiones en construcciones</field>
    </record>
    <record id="account_group_23" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">23</field>
        <field name="name">Inmovilizaciones materiales en curso</field>
    </record>
    <record id="account_group_230" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">230</field>
        <field name="name">Adaptación de terrenos y bienes naturales</field>
    </record>
    <record id="account_group_231" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">231</field>
        <field name="name">Construcciones en curso</field>
    </record>
    <record id="account_group_232" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">232</field>
        <field name="name">Instalaciones técnicas en montaje</field>
    </record>
    <record id="account_group_233" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">233</field>
        <field name="name">Maquinaria en montaje</field>
    </record>
    <record id="account_group_237" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">237</field>
        <field name="name">Equipos para procesos de información en montaje</field>
    </record>
    <record id="account_group_239" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">239</field>
        <field name="name">Anticipos para inmovilizaciones materiales</field>
    </record>
    <record id="account_group_24" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">24</field>
        <field name="name">Inversiones financieras a largo plazo en partes vinculadas</field>
    </record>
    <record id="account_group_240" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">240</field>
        <field name="name">Participaciones a largo plazo en partes vinculadas</field>
    </record>
    <record id="account_group_2403" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2403</field>
        <field name="name">Participaciones a largo plazo en empresas del grupo</field>
    </record>
    <record id="account_group_2404" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2404</field>
        <field name="name">Participaciones a largo plazo en empresas asociadas</field>
    </record>
    <record id="account_group_2405" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2405</field>
        <field name="name">Participaciones a largo plazo en otras partes vinculadas</field>
    </record>
    <record id="account_group_241" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">241</field>
        <field name="name">Valores representativos de deuda a largo plazo de partes vinculadas</field>
    </record>
    <record id="account_group_2413" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2413</field>
        <field name="name">Valores representativos de deuda a largo plazo de empresas del grupo</field>
    </record>
    <record id="account_group_2414" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2414</field>
        <field name="name">Valores representativos de deuda a largo plazo de empresas asociadas</field>
    </record>
    <record id="account_group_2415" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2415</field>
        <field name="name">Valores representativos de deuda a largo plazo de otras partes vinculadas</field>
    </record>
    <record id="account_group_242" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">242</field>
        <field name="name">Créditos a largo plazo a partes vinculadas</field>
    </record>
    <record id="account_group_2423" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2423</field>
        <field name="name">Créditos a largo plazo a empresas del grupo</field>
    </record>
    <record id="account_group_2424" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2424</field>
        <field name="name">Créditos a largo plazo a empresas asociadas</field>
    </record>
    <record id="account_group_2425" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2425</field>
        <field name="name">Créditos a largo plazo a otras partes vinculadas</field>
    </record>
    <record id="account_group_249" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">249</field>
        <field name="name">Desembolsos pendientes sobre participaciones a largo plazo en partes vinculadas</field>
    </record>
    <record id="account_group_2493" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2493</field>
        <field name="name">Desembolsos pendientes sobre participaciones a largo plazo en empresas del grupo.</field>
    </record>
    <record id="account_group_2494" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2494</field>
        <field name="name">Desembolsos pendientes sobre participaciones a largo plazo en empresas asociadas.</field>
    </record>
    <record id="account_group_2495" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2495</field>
        <field name="name">Desembolsos pendientes sobre participaciones a largo plazo en otras partes vinculadas</field>
    </record>
    <record id="account_group_25" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">25</field>
        <field name="name">Otras inversiones financieras a largo plazo</field>
    </record>
    <record id="account_group_250" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">250</field>
        <field name="name">Inversiones financieras a largo plazo en instrumentos de patrimonio</field>
    </record>
    <record id="account_group_251" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">251</field>
        <field name="name">Valores representativos de deuda a largo plazo</field>
    </record>
    <record id="account_group_252" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">252</field>
        <field name="name">Créditos a largo plazo</field>
    </record>
    <record id="account_group_253" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">253</field>
        <field name="name">Créditos a largo plazo por enajenación de inmovilizado</field>
    </record>
    <record id="account_group_254" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">254</field>
        <field name="name">Créditos a largo plazo al personal</field>
    </record>
    <record id="account_group_255" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">255</field>
        <field name="name">Activos por derivados financieros a largo plazo</field>
    </record>
    <record id="account_group_2550" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2550</field>
        <field name="name">Activos por derivados financieros a largo plazo, cartera de negociación</field>
    </record>
    <record id="account_group_2553" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2553</field>
        <field name="name">Activos por derivados financieros a largo plazo, instrumentos de cobertura</field>
    </record>
    <record id="account_group_257" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">257</field>
        <field name="name">Derechos de reembolso derivados de contratos de seguro relativos a retribuciones a largo plazo al personal</field>
    </record>
    <record id="account_group_258" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">258</field>
        <field name="name">Imposiciones a largo plazo</field>
    </record>
    <record id="account_group_259" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">259</field>
        <field name="name">Desembolsos pendientes sobre participaciones en el patrimonio neto a largo plazo</field>
    </record>
    <record id="account_group_26" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">26</field>
        <field name="name">Fianzas y depósitos constituidos a largo plazo</field>
    </record>
    <record id="account_group_260" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">260</field>
        <field name="name">Fianzas constituidas a largo plazo</field>
    </record>
    <record id="account_group_265" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">265</field>
        <field name="name">Depósitos constituidos a largo plazo</field>
    </record>
    <record id="account_group_28" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">28</field>
        <field name="name">Amortización acumulada del inmovilizado</field>
    </record>
    <record id="account_group_280" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">280</field>
        <field name="name">Amortización acumulada del inmovilizado intangible</field>
    </record>
    <record id="account_group_2800" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2800</field>
        <field name="name">Amortización acumulada de investigación</field>
    </record>
    <record id="account_group_2801" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2801</field>
        <field name="name">Amortización acumulada de desarrollo</field>
    </record>
    <record id="account_group_2802" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2802</field>
        <field name="name">Amortización acumulada de concesiones administrativas</field>
    </record>
    <record id="account_group_2803" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2803</field>
        <field name="name">Amortización acumulada de propiedad industrial</field>
    </record>
    <record id="account_group_2805" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2805</field>
        <field name="name">Amortización acumulada de derechos de traspaso</field>
    </record>
    <record id="account_group_2806" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2806</field>
        <field name="name">Amortización acumulada de aplicaciones informáticas</field>
    </record>
    <record id="account_group_281" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">281</field>
        <field name="name">Amortización acumulada del inmovilizado material</field>
    </record>
    <record id="account_group_2811" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2811</field>
        <field name="name">Amortización acumulada de construcciones</field>
    </record>
    <record id="account_group_2812" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2812</field>
        <field name="name">Amortización acumulada de instalaciones técnicas</field>
    </record>
    <record id="account_group_2813" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2813</field>
        <field name="name">Amortización acumulada de maquinaria</field>
    </record>
    <record id="account_group_2814" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2814</field>
        <field name="name">Amortización acumulada de utillaje</field>
    </record>
    <record id="account_group_2815" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2815</field>
        <field name="name">Amortización acumulada de otras instalaciones</field>
    </record>
    <record id="account_group_2816" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2816</field>
        <field name="name">Amortización acumulada de mobiliario</field>
    </record>
    <record id="account_group_2817" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2817</field>
        <field name="name">Amortización acumulada de equipos para procesos de información</field>
    </record>
    <record id="account_group_2818" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2818</field>
        <field name="name">Amortización acumulada de elementos de transporte</field>
    </record>
    <record id="account_group_2819" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2819</field>
        <field name="name">Amortización acumulada de otro inmovilizado material</field>
    </record>
    <record id="account_group_282" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">282</field>
        <field name="name">Amortización acumulada de las inversiones inmobiliarias</field>
    </record>
    <record id="account_group_283" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">283</field>
        <field name="name">Cesiones de uso sin contraprestación</field>
    </record>
    <record id="account_group_29" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">29</field>
        <field name="name">Deterioro de valor de activos no corrientes</field>
    </record>
    <record id="account_group_290" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">290</field>
        <field name="name">Deterioro de valor del inmovilizado intangible</field>
    </record>
    <record id="account_group_2900" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2900</field>
        <field name="name">Deterioro de valor de investigación</field>
    </record>
    <record id="account_group_2901" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2901</field>
        <field name="name">Deterioro del valor de desarrollo</field>
    </record>
    <record id="account_group_2902" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2902</field>
        <field name="name">Deterioro de valor de concesiones administrativas</field>
    </record>
    <record id="account_group_2903" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2903</field>
        <field name="name">Deterioro de valor de propiedad industrial</field>
    </record>
    <record id="account_group_2905" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2905</field>
        <field name="name">Deterioro de valor de derechos de traspaso</field>
    </record>
    <record id="account_group_2906" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2906</field>
        <field name="name">Deterioro de valor de aplicaciones informáticas</field>
    </record>
    <record id="account_group_291" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">291</field>
        <field name="name">Deterioro de valor del inmovilizado material</field>
    </record>
    <record id="account_group_2910" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2910</field>
        <field name="name">Deterioro de valor de terrenos y bienes naturales</field>
    </record>
    <record id="account_group_2911" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2911</field>
        <field name="name">Deterioro de valor de construcciones</field>
    </record>
    <record id="account_group_2912" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2912</field>
        <field name="name">Deterioro de valor de instalaciones técnicas</field>
    </record>
    <record id="account_group_2913" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2913</field>
        <field name="name">Deterioro de valor de maquinaria</field>
    </record>
    <record id="account_group_2914" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2914</field>
        <field name="name">Deterioro de valor de utillaje</field>
    </record>
    <record id="account_group_2915" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2915</field>
        <field name="name">Deterioro de valor de otras instalaciones</field>
    </record>
    <record id="account_group_2916" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2916</field>
        <field name="name">Deterioro de valor de mobiliario</field>
    </record>
    <record id="account_group_2917" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2917</field>
        <field name="name">Deterioro de valor de equipos para procesos de información</field>
    </record>
    <record id="account_group_2918" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2918</field>
        <field name="name">Deterioro de valor de elementos de transporte</field>
    </record>
    <record id="account_group_2919" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2919</field>
        <field name="name">Deterioro de valor de otro inmovilizado material</field>
    </record>
    <record id="account_group_292" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">292</field>
        <field name="name">Deterioro de valor de las inversiones inmobiliarias</field>
    </record>
    <record id="account_group_2920" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2920</field>
        <field name="name">Deterioro de valor de los terrenos y bienes naturales</field>
    </record>
    <record id="account_group_2921" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2921</field>
        <field name="name">Deterioro de valor de construcciones</field>
    </record>
    <record id="account_group_293" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">293</field>
        <field name="name">Deterioro de valor de participaciones a largo plazo en partes vinculadas</field>
    </record>
    <record id="account_group_2933" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2933</field>
        <field name="name">Deterioro de valor de participaciones a largo plazo en empresas del grupo</field>
    </record>
    <record id="account_group_2934" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2934</field>
        <field name="name">Deterioro de valor de participaciones a largo plazo en empresas asociadas</field>
    </record>
    <record id="account_group_294" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">294</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a largo plazo de partes vinculadas</field>
    </record>
    <record id="account_group_2943" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2943</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a largo plazo de empresas del grupo</field>
    </record>
    <record id="account_group_2944" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2944</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a largo plazo de empresas asociadas</field>
    </record>
    <record id="account_group_2945" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2945</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a largo plazo de otras partes vinculadas</field>
    </record>
    <record id="account_group_295" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">295</field>
        <field name="name">Deterioro de valor de créditos a largo plazo a partes vinculadas</field>
    </record>
    <record id="account_group_2953" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2953</field>
        <field name="name">Deterioro de valor de créditos a largo plazo a empresas del grupo</field>
    </record>
    <record id="account_group_2954" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2954</field>
        <field name="name">Deterioro de valor de créditos a largo plazo a empresas asociadas</field>
    </record>
    <record id="account_group_2955" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">2955</field>
        <field name="name">Deterioro de valor de créditos a largo plazo a otras partes vinculadas</field>
    </record>
    <record id="account_group_296" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">296</field>
        <field name="name">Deterioro de valor de participaciones en el patrimonio neto a largo plazo</field>
    </record>
    <record id="account_group_297" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">297</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a largo plazo</field>
    </record>
    <record id="account_group_298" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">298</field>
        <field name="name">Deterioro de valor de créditos a largo plazo</field>
    </record>
    <record id="account_group_299" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">299</field>
        <field name="name">Deterioro de valor de bienes inmuebles</field>
    </record>
    <record id="account_group_3" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">3</field>
        <field name="name">Existencias</field>
    </record>
    <record id="account_group_30" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">30</field>
        <field name="name">Comerciales</field>
    </record>
    <record id="account_group_300" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">300</field>
        <field name="name">Mercaderías A</field>
    </record>
    <record id="account_group_301" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">301</field>
        <field name="name">Mercaderías B</field>
    </record>
    <record id="account_group_31" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">31</field>
        <field name="name">Materias primas</field>
    </record>
    <record id="account_group_310" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">310</field>
        <field name="name">Materias primas A</field>
    </record>
    <record id="account_group_311" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">311</field>
        <field name="name">Materias primas B</field>
    </record>
    <record id="account_group_32" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">32</field>
        <field name="name">Otros aprovisionamientos</field>
    </record>
    <record id="account_group_320" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">320</field>
        <field name="name">Elementos y conjuntos incorporables</field>
    </record>
    <record id="account_group_321" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">321</field>
        <field name="name">Combustibles</field>
    </record>
    <record id="account_group_322" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">322</field>
        <field name="name">Repuestos</field>
    </record>
    <record id="account_group_325" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">325</field>
        <field name="name">Materiales diversos</field>
    </record>
    <record id="account_group_326" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">326</field>
        <field name="name">Embalajes</field>
    </record>
    <record id="account_group_327" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">327</field>
        <field name="name">Envases</field>
    </record>
    <record id="account_group_328" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">328</field>
        <field name="name">Material de oficina</field>
    </record>
    <record id="account_group_33" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">33</field>
        <field name="name">Productos en curso</field>
    </record>
    <record id="account_group_330" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">330</field>
        <field name="name">Productos en curso A</field>
    </record>
    <record id="account_group_331" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">331</field>
        <field name="name">Productos en curso B</field>
    </record>
    <record id="account_group_34" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">34</field>
        <field name="name">Productos semiterminados</field>
    </record>
    <record id="account_group_340" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">340</field>
        <field name="name">Productos semiterminados A</field>
    </record>
    <record id="account_group_341" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">341</field>
        <field name="name">Productos semiterminados B</field>
    </record>
    <record id="account_group_35" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">35</field>
        <field name="name">Productos terminados</field>
    </record>
    <record id="account_group_350" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">350</field>
        <field name="name">Productos terminados A</field>
    </record>
    <record id="account_group_351" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">351</field>
        <field name="name">Productos terminados B</field>
    </record>
    <record id="account_group_36" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">36</field>
        <field name="name">Subproductos, residuos y materiales recuperados</field>
    </record>
    <record id="account_group_360" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">360</field>
        <field name="name">Subproductos A</field>
    </record>
    <record id="account_group_361" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">361</field>
        <field name="name">Subproductos B</field>
    </record>
    <record id="account_group_365" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">365</field>
        <field name="name">Residuos A</field>
    </record>
    <record id="account_group_366" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">366</field>
        <field name="name">Residuos B</field>
    </record>
    <record id="account_group_368" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">368</field>
        <field name="name">Materiales recuperados A</field>
    </record>
    <record id="account_group_369" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">369</field>
        <field name="name">Materiales recuperados B</field>
    </record>
    <record id="account_group_39" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">39</field>
        <field name="name">Deterioro de valor de las existencias</field>
    </record>
    <record id="account_group_390" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">390</field>
        <field name="name">Deterioro de valor de las mercaderías</field>
    </record>
    <record id="account_group_391" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">391</field>
        <field name="name">Deterioro de valor de las materias primas</field>
    </record>
    <record id="account_group_392" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">392</field>
        <field name="name">Deterioro de valor de otros aprovisionamientos</field>
    </record>
    <record id="account_group_393" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">393</field>
        <field name="name">Deterioro de valor de los productos en curso</field>
    </record>
    <record id="account_group_394" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">394</field>
        <field name="name">Deterioro de valor de los productos semiterminados</field>
    </record>
    <record id="account_group_395" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">395</field>
        <field name="name">Deterioro de valor de los productos terminados</field>
    </record>
    <record id="account_group_396" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">396</field>
        <field name="name">Deterioro de valor de los subproductos, residuos y materiales recuperados</field>
    </record>
    <record id="account_group_4" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4</field>
        <field name="name">Acreedores y deudores por operaciones comerciales</field>
    </record>
    <record id="account_group_40" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">40</field>
        <field name="name">Proveedores</field>
    </record>
    <record id="account_group_400" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">400</field>
        <field name="name">Proveedores</field>
    </record>
    <record id="account_group_4000" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4000</field>
        <field name="name">Proveedores (euros)</field>
    </record>
    <record id="account_group_4004" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4004</field>
        <field name="name">Proveedores (moneda extranjera)</field>
    </record>
    <record id="account_group_4009" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4009</field>
        <field name="name">Proveedores, facturas pendientes de recibir o de formalizar</field>
    </record>
    <record id="account_group_401" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">401</field>
        <field name="name">Proveedores, efectos comerciales a pagar</field>
    </record>
    <record id="account_group_403" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">403</field>
        <field name="name">Proveedores, empresas del grupo</field>
    </record>
    <record id="account_group_4030" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4030</field>
        <field name="name">Proveedores, empresas del grupo (euros)</field>
    </record>
    <record id="account_group_4031" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4031</field>
        <field name="name">Efectos comerciales a pagar, empresas del grupo</field>
    </record>
    <record id="account_group_4034" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4034</field>
        <field name="name">Proveedores, empresas del grupo (moneda extranjera)</field>
    </record>
    <record id="account_group_4036" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4036</field>
        <field name="name">Envases y embalajes a devolver a proveedores, empresas del grupo</field>
    </record>
    <record id="account_group_4039" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4039</field>
        <field name="name">Proveedores, empresas del grupo, facturas pendientes de recibir o de formalizar</field>
    </record>
    <record id="account_group_404" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">404</field>
        <field name="name">Proveedores, empresas asociadas</field>
    </record>
    <record id="account_group_405" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">405</field>
        <field name="name">Proveedores, otras partes vinculadas</field>
    </record>
    <record id="account_group_406" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">406</field>
        <field name="name">Envases y embalajes a devolver a proveedores</field>
    </record>
    <record id="account_group_407" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">407</field>
        <field name="name">Anticipos a proveedores</field>
    </record>
    <record id="account_group_41" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">41</field>
        <field name="name">Acreedores varios</field>
    </record>
    <record id="account_group_410" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">410</field>
        <field name="name">Acreedores por prestaciones de servicios</field>
    </record>
    <record id="account_group_4100" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4100</field>
        <field name="name">Acreedores por prestaciones de servicios (euros)</field>
    </record>
    <record id="account_group_4104" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4104</field>
        <field name="name">Acreedores por prestaciones de servicios (moneda extranjera)</field>
    </record>
    <record id="account_group_4109" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4109</field>
        <field name="name">Acreedores por prestaciones de servicios, facturas pendientes de recibir o de formalizar</field>
    </record>
    <record id="account_group_411" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">411</field>
        <field name="name">Acreedores, efectos comerciales a pagar</field>
    </record>
    <record id="account_group_412" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">412</field>
        <field name="name">Beneficiarios, acreedores</field>
    </record>
    <record id="account_group_419" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">419</field>
        <field name="name">Acreedores por operaciones en común</field>
    </record>
    <record id="account_group_43" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">43</field>
        <field name="name">Clientes</field>
    </record>
    <record id="account_group_430" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">430</field>
        <field name="name">Clientes</field>
    </record>
    <record id="account_group_4300" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4300</field>
        <field name="name">Clientes (euros)</field>
    </record>
    <record id="account_group_4304" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4304</field>
        <field name="name">Clientes (moneda extranjera)</field>
    </record>
    <record id="account_group_4309" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4309</field>
        <field name="name">Clientes, facturas pendientes de formalizar</field>
    </record>
    <record id="account_group_431" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">431</field>
        <field name="name">Clientes, efectos comerciales a cobrar</field>
    </record>
    <record id="account_group_4310" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4310</field>
        <field name="name">Efectos comerciales en cartera</field>
    </record>
    <record id="account_group_4311" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4311</field>
        <field name="name">Efectos comerciales descontados</field>
    </record>
    <record id="account_group_4312" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4312</field>
        <field name="name">Efectos comerciales en gestión de cobro</field>
    </record>
    <record id="account_group_4315" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4315</field>
        <field name="name">Efectos comerciales impagados</field>
    </record>
    <record id="account_group_432" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">432</field>
        <field name="name">Clientes, operaciones de «factoring»</field>
    </record>
    <record id="account_group_433" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">433</field>
        <field name="name">Clientes, empresas del grupo</field>
    </record>
    <record id="account_group_4330" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4330</field>
        <field name="name">Clientes empresas del grupo (euros)</field>
    </record>
    <record id="account_group_4331" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4331</field>
        <field name="name">Efectos comerciales a cobrar, empresas del grupo</field>
    </record>
    <record id="account_group_4332" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4332</field>
        <field name="name">Clientes empresas del grupo, operaciones de «factoring»</field>
    </record>
    <record id="account_group_4334" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4334</field>
        <field name="name">Clientes empresas del grupo (moneda extranjera)</field>
    </record>
    <record id="account_group_4336" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4336</field>
        <field name="name">Clientes empresas del grupo de dudoso cobro</field>
    </record>
    <record id="account_group_4337" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4337</field>
        <field name="name">Envases y embalajes a devolver a clientes, empresas del grupo</field>
    </record>
    <record id="account_group_4339" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4339</field>
        <field name="name">Clientes empresas del grupo, facturas pendientes de formalizar</field>
    </record>
    <record id="account_group_434" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">434</field>
        <field name="name">Clientes, empresas asociadas</field>
    </record>
    <record id="account_group_435" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">435</field>
        <field name="name">Clientes, otras partes vinculadas</field>
    </record>
    <record id="account_group_436" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">436</field>
        <field name="name">Clientes de dudoso cobro</field>
    </record>
    <record id="account_group_437" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">437</field>
        <field name="name">Envases y embalajes a devolver por clientes</field>
    </record>
    <record id="account_group_438" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">438</field>
        <field name="name">Anticipos de clientes</field>
    </record>
    <record id="account_group_44" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">44</field>
        <field name="name">Deudores varios</field>
    </record>
    <record id="account_group_440" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">440</field>
        <field name="name">Deudores</field>
    </record>
    <record id="account_group_4400" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4400</field>
        <field name="name">Deudores (euros)</field>
    </record>
    <record id="account_group_4404" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4404</field>
        <field name="name">Deudores (moneda extranjera)</field>
    </record>
    <record id="account_group_4409" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4409</field>
        <field name="name">Deudores, facturas pendientes de formalizar</field>
    </record>
    <record id="account_group_441" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">441</field>
        <field name="name">Deudores, efectos comerciales a cobrar</field>
    </record>
    <record id="account_group_4410" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4410</field>
        <field name="name">Deudores, efectos comerciales en cartera</field>
    </record>
    <record id="account_group_4411" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4411</field>
        <field name="name">Deudores, efectos comerciales descontados</field>
    </record>
    <record id="account_group_4412" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4412</field>
        <field name="name">Deudores, efectos comerciales en gestión de cobro</field>
    </record>
    <record id="account_group_4415" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4415</field>
        <field name="name">Deudores, efectos comerciales impagados</field>
    </record>
    <record id="account_group_446" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">446</field>
        <field name="name">Deudores de dudoso cobro</field>
    </record>
    <record id="account_group_447" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">447</field>
        <field name="name">Usuarios, deudores</field>
    </record>
    <record id="account_group_448" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">448</field>
        <field name="name">Patrocinadores, afiliados y otros deudores</field>
    </record>
    <record id="account_group_449" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">449</field>
        <field name="name">Deudores por operaciones en común</field>
    </record>
    <record id="account_group_46" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">46</field>
        <field name="name">Personal</field>
    </record>
    <record id="account_group_460" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">460</field>
        <field name="name">Anticipos de remuneraciones</field>
    </record>
    <record id="account_group_464" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">464</field>
        <field name="name">Entregas para gastos a justificar</field>
    </record>
    <record id="account_group_465" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">465</field>
        <field name="name">Remuneraciones pendientes de pago</field>
    </record>
    <record id="account_group_466" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">466</field>
        <field name="name">Remuneraciones mediante sistemas de aportación definida pendientes de pago</field>
    </record>
    <record id="account_group_47" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">47</field>
        <field name="name">Administraciones públicas</field>
    </record>
    <record id="account_group_470" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">470</field>
        <field name="name">Hacienda Pública, deudora por diversos conceptos</field>
    </record>
    <record id="account_group_4700" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4700</field>
        <field name="name">Hacienda Pública, deudora por IVA</field>
    </record>
    <record id="account_group_4708" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4708</field>
        <field name="name">Hacienda Pública, deudora por subvenciones concedidas</field>
    </record>
    <record id="account_group_4709" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4709</field>
        <field name="name">Hacienda Pública, deudora por devolución de impuestos</field>
    </record>
    <record id="account_group_471" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">471</field>
        <field name="name">Organismos de la Seguridad Social, deudores</field>
    </record>
    <record id="account_group_472" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">472</field>
        <field name="name">Hacienda Pública, IVA soportado</field>
    </record>
    <record id="account_group_473" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">473</field>
        <field name="name">Hacienda Pública, retenciones y pagos a cuenta</field>
    </record>
    <record id="account_group_474" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">474</field>
        <field name="name">Activos por impuesto diferido</field>
    </record>
    <record id="account_group_4740" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4740</field>
        <field name="name">Activos por diferencias temporarias deducibles</field>
    </record>
    <record id="account_group_4742" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4742</field>
        <field name="name">Derechos por deducciones y bonificaciones pendientes de aplicar</field>
    </record>
    <record id="account_group_4745" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4745</field>
        <field name="name">Crédito por pérdidas a compensar del ejercicio</field>
    </record>
    <record id="account_group_475" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">475</field>
        <field name="name">Hacienda Pública, acreedora por conceptos fiscales</field>
    </record>
    <record id="account_group_4750" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4750</field>
        <field name="name">Hacienda Pública, acreedora por IVA</field>
    </record>
    <record id="account_group_4751" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4751</field>
        <field name="name">Hacienda Pública, acreedora por retenciones practicadas</field>
    </record>
    <record id="account_group_4752" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4752</field>
        <field name="name">Hacienda Pública, acreedora por impuesto sobre sociedades</field>
    </record>
    <record id="account_group_4758" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4758</field>
        <field name="name">Hacienda Pública, acreedora por subvenciones a reintegrar</field>
    </record>
    <record id="account_group_4759" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4759</field>
        <field name="name">Hacienda Pública, acreedora por otros conceptos</field>
    </record>
    <record id="account_group_476" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">476</field>
        <field name="name">Organismos de la Seguridad Social, acreedores</field>
    </record>
    <record id="account_group_477" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">477</field>
        <field name="name">Hacienda Pública, IVA repercutido</field>
    </record>
    <record id="account_group_479" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">479</field>
        <field name="name">Pasivos por diferencias temporarias imponibles</field>
    </record>
    <record id="account_group_48" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">48</field>
        <field name="name">Ajustes por periodificación</field>
    </record>
    <record id="account_group_480" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">480</field>
        <field name="name">Gastos anticipados</field>
    </record>
    <record id="account_group_485" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">485</field>
        <field name="name">Ingresos anticipados</field>
    </record>
    <record id="account_group_49" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">49</field>
        <field name="name">Deterioro de valor de créditos comerciales y provisiones a corto plazo</field>
    </record>
    <record id="account_group_490" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">490</field>
        <field name="name">Deterioro de valor de créditos por operaciones comerciales</field>
    </record>
    <record id="account_group_493" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">493</field>
        <field name="name">Deterioro de valor de créditos por operaciones comerciales con partes vinculadas</field>
    </record>
    <record id="account_group_4933" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4933</field>
        <field name="name">Deterioro de valor de créditos por operaciones comerciales con empresas del grupo</field>
    </record>
    <record id="account_group_4934" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4934</field>
        <field name="name">Deterioro de valor de créditos por operaciones comerciales con empresas asociadas</field>
    </record>
    <record id="account_group_4935" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4935</field>
        <field name="name">Deterioro de valor de créditos por operaciones comerciales con otras partes vinculadas</field>
    </record>
    <record id="account_group_499" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">499</field>
        <field name="name">Provisiones por operaciones comerciales</field>
    </record>
    <record id="account_group_4994" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4994</field>
        <field name="name">Provisión por contratos onerosos</field>
    </record>
    <record id="account_group_4999" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">4999</field>
        <field name="name">Provisión para otras operaciones comerciales</field>
    </record>
    <record id="account_group_5" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5</field>
        <field name="name">Cuentas financieras</field>
    </record>
    <record id="account_group_50" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">50</field>
        <field name="name">Empréstitos, deudas con carácterísticas especiales y otras emisiones análogas a corto plazo</field>
    </record>
    <record id="account_group_500" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">500</field>
        <field name="name">Obligaciones y bonos a corto plazo Obligaciones y bonos convertibles a corto plazo</field>
    </record>
    <record id="account_group_501" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">501</field>
        <field name="name">Obligaciones y bonos convertibles a corto plazo</field>
    </record>
    <record id="account_group_502" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">502</field>
        <field name="name">Acciones o participaciones a corto plazo consideradas como pasivos financieros</field>
    </record>
    <record id="account_group_505" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">505</field>
        <field name="name">Deudas representadas en otros valores negociables a corto plazo</field>
    </record>
    <record id="account_group_506" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">506</field>
        <field name="name">Intereses a corto plazo de empréstitos y otras emisiones análogas</field>
    </record>
    <record id="account_group_507" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">507</field>
        <field name="name">Dividendos de acciones o participaciones consideradas como pasivos financieros</field>
    </record>
    <record id="account_group_509" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">509</field>
        <field name="name">Valores negociables amortizados</field>
    </record>
    <record id="account_group_5090" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5090</field>
        <field name="name">Obligaciones y bonos amortizados</field>
    </record>
    <record id="account_group_5091" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5091</field>
        <field name="name">Obligaciones y bonos convertibles amortizados</field>
    </record>
    <record id="account_group_5095" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5095</field>
        <field name="name">Otros valores negociables amortizados</field>
    </record>
    <record id="account_group_51" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">51</field>
        <field name="name">Deudas a corto plazo con partes vinculadas</field>
    </record>
    <record id="account_group_510" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">510</field>
        <field name="name">Deudas a corto plazo con entidades de crédito vinculadas</field>
    </record>
    <record id="account_group_5103" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5103</field>
        <field name="name">Deudas a corto plazo con entidades de crédito, empresas del grupo</field>
    </record>
    <record id="account_group_5104" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5104</field>
        <field name="name">Deudas a corto plazo con entidades de crédito, empresas asociadas</field>
    </record>
    <record id="account_group_5105" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5105</field>
        <field name="name">Deudas a corto plazo con otras entidades de crédito vinculadas</field>
    </record>
    <record id="account_group_511" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">511</field>
        <field name="name">Proveedores de inmovilizado a corto plazo, partes vinculadas</field>
    </record>
    <record id="account_group_5113" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5113</field>
        <field name="name">Proveedores de inmovilizado a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_5114" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5114</field>
        <field name="name">Proveedores de inmovilizado a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_5115" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5115</field>
        <field name="name">Proveedores de inmovilizado a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_512" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">512</field>
        <field name="name">Acreedores por arrendamiento financiero a corto plazo, partes vinculadas.</field>
    </record>
    <record id="account_group_5123" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5123</field>
        <field name="name">Acreedores por arrendamiento financiero a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_5124" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5124</field>
        <field name="name">Acreedores por arrendamiento financiero a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_5125" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5125</field>
        <field name="name">Acreedores por arrendamiento financiero a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_513" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">513</field>
        <field name="name">Otras deudas a corto plazo con partes vinculadas</field>
    </record>
    <record id="account_group_5133" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5133</field>
        <field name="name">Otras deudas a corto plazo con empresas del grupo</field>
    </record>
    <record id="account_group_5134" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5134</field>
        <field name="name">Otras deudas a corto plazo con empresas asociadas</field>
    </record>
    <record id="account_group_5135" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5135</field>
        <field name="name">Otras deudas a corto plazo con otras partes vinculadas</field>
    </record>
    <record id="account_group_514" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">514</field>
        <field name="name">Intereses a corto plazo de deudas con partes vinculadas</field>
    </record>
    <record id="account_group_5143" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5143</field>
        <field name="name">Intereses a corto plazo de deudas, empresas del grupo</field>
    </record>
    <record id="account_group_5144" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5144</field>
        <field name="name">Intereses a corto plazo de deudas, empresas asociadas</field>
    </record>
    <record id="account_group_5145" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5145</field>
        <field name="name">Intereses a corto plazo de deudas, otras partes vinculadas</field>
    </record>
    <record id="account_group_52" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">52</field>
        <field name="name">Deudas a corto plazo por préstamos recibidos y otros conceptos</field>
    </record>
    <record id="account_group_520" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">520</field>
        <field name="name">Deudas a corto plazo con entidades de crédito</field>
    </record>
    <record id="account_group_5200" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5200</field>
        <field name="name">Préstamos a corto plazo de entidades de crédito</field>
    </record>
    <record id="account_group_5201" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5201</field>
        <field name="name">Deudas a corto plazo por crédito dispuesto</field>
    </record>
    <record id="account_group_5208" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5208</field>
        <field name="name">Deudas por efectos descontados</field>
    </record>
    <record id="account_group_5209" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5209</field>
        <field name="name">Deudas por operaciones de «factoring»</field>
    </record>
    <record id="account_group_521" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">521</field>
        <field name="name">Deudas a corto plazo</field>
    </record>
    <record id="account_group_522" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">522</field>
        <field name="name">Deudas a corto plazo transformables en subvenciones, donaciones y legados</field>
    </record>
    <record id="account_group_523" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">523</field>
        <field name="name">Proveedores de inmovilizado a corto plazo</field>
    </record>
    <record id="account_group_524" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">524</field>
        <field name="name">Acreedores por arrendamiento financiero a corto plazo</field>
    </record>
    <record id="account_group_525" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">525</field>
        <field name="name">Efectos a pagar a corto plazo</field>
    </record>
    <record id="account_group_526" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">526</field>
        <field name="name">Dividendo activo a pagar</field>
    </record>
    <record id="account_group_527" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">527</field>
        <field name="name">Intereses a corto plazo de deudas con entidades de crédito</field>
    </record>
    <record id="account_group_528" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">528</field>
        <field name="name">Intereses a corto plazo de deudas</field>
    </record>
    <record id="account_group_529" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">529</field>
        <field name="name">Provisiones a corto plazo</field>
    </record>
    <record id="account_group_5290" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5290</field>
        <field name="name">Provisión a corto plazo por retribuciones al personal</field>
    </record>
    <record id="account_group_5291" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5291</field>
        <field name="name">Provisión a corto plazo para impuestos</field>
    </record>
    <record id="account_group_5292" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5292</field>
        <field name="name">Provisión a corto plazo para otras responsabilidades</field>
    </record>
    <record id="account_group_5293" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5293</field>
        <field name="name">Provisión a corto plazo por desmantelamiento, retiro o rehabilitación del inmovilizado</field>
    </record>
    <record id="account_group_5295" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5295</field>
        <field name="name">Provisión a corto plazo para actuaciones medioambientales</field>
    </record>
    <record id="account_group_5296" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5296</field>
        <field name="name">Provisión a corto plazo para reestructuraciones</field>
    </record>
    <record id="account_group_5297" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5297</field>
        <field name="name">Provisión a corto plazo por transacciones con pagos basados en instrumentos de patrimonio</field>
    </record>
    <record id="account_group_53" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">53</field>
        <field name="name">Inversiones financieras a corto plazo en partes vinculadas</field>
    </record>
    <record id="account_group_530" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">530</field>
        <field name="name">Participaciones a corto plazo en partes vinculadas</field>
    </record>
    <record id="account_group_5303" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5303</field>
        <field name="name">Participaciones a corto plazo, en empresas del grupo</field>
    </record>
    <record id="account_group_5304" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5304</field>
        <field name="name">Participaciones a corto plazo, en empresas asociadas</field>
    </record>
    <record id="account_group_5305" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5305</field>
        <field name="name">Participaciones a corto plazo, en otras partes vinculadas</field>
    </record>
    <record id="account_group_531" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">531</field>
        <field name="name">Valores representativos de deuda a corto plazo de partes vinculadas</field>
    </record>
    <record id="account_group_5313" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5313</field>
        <field name="name">Valores representativos de deuda a corto plazo de empresas del grupo</field>
    </record>
    <record id="account_group_5314" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5314</field>
        <field name="name">Valores representativos de deuda a corto plazo de empresas asociadas</field>
    </record>
    <record id="account_group_5315" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5315</field>
        <field name="name">Valores representativos de deuda a corto plazo de otras partes vinculadas</field>
    </record>
    <record id="account_group_532" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">532</field>
        <field name="name">Créditos a corto plazo a partes vinculadas</field>
    </record>
    <record id="account_group_5323" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5323</field>
        <field name="name">Créditos a corto plazo a empresas del grupo</field>
    </record>
    <record id="account_group_5324" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5324</field>
        <field name="name">Créditos a corto plazo a empresas asociadas</field>
    </record>
    <record id="account_group_5325" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5325</field>
        <field name="name">Créditos a corto plazo a otras partes vinculadas</field>
    </record>
    <record id="account_group_533" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">533</field>
        <field name="name">Intereses a corto plazo de valores representativos de deuda de partes vinculadas</field>
    </record>
    <record id="account_group_5333" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5333</field>
        <field name="name">Intereses a corto plazo de valores representativos de deuda de empresas del grupo</field>
    </record>
    <record id="account_group_5334" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5334</field>
        <field name="name">Intereses a corto plazo de valores representativos de deuda de empresas asociadas</field>
    </record>
    <record id="account_group_5335" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5335</field>
        <field name="name">Intereses a corto plazo de valores representativos de deuda de otras partes vinculadas</field>
    </record>
    <record id="account_group_534" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">534</field>
        <field name="name">Intereses a corto plazo de créditos a partes vinculadas</field>
    </record>
    <record id="account_group_5343" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5343</field>
        <field name="name">Intereses a corto plazo de créditos a empresas del grupo</field>
    </record>
    <record id="account_group_5344" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5344</field>
        <field name="name">Intereses a corto plazo de créditos a empresas asociadas</field>
    </record>
    <record id="account_group_5345" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5345</field>
        <field name="name">Intereses a corto plazo de créditos a otras partes vinculadas</field>
    </record>
    <record id="account_group_535" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">535</field>
        <field name="name">Dividendo a cobrar de inversiones financieras en partes vinculadas</field>
    </record>
    <record id="account_group_5353" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5353</field>
        <field name="name">Dividendo a cobrar de empresas del grupo</field>
    </record>
    <record id="account_group_5354" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5354</field>
        <field name="name">Dividendo a cobrar de empresas asociadas</field>
    </record>
    <record id="account_group_5355" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5355</field>
        <field name="name">Dividendo a cobrar de otras partes vinculadas</field>
    </record>
    <record id="account_group_539" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">539</field>
        <field name="name">Desembolsos pendientes sobre participaciones a corto plazo en partes vinculadas</field>
    </record>
    <record id="account_group_5393" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5393</field>
        <field name="name">Desembolsos pendientes sobre participaciones a corto plazo en empresas del grupo.</field>
    </record>
    <record id="account_group_5394" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5394</field>
        <field name="name">Desembolsos pendientes sobre participaciones a corto plazo en empresas asociadas.</field>
    </record>
    <record id="account_group_5395" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5395</field>
        <field name="name">Desembolsos pendientes sobre participaciones a corto plazo en otras partes vinculadas</field>
    </record>
    <record id="account_group_54" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">54</field>
        <field name="name">Otras inversiones financieras a corto plazo</field>
    </record>
    <record id="account_group_540" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">540</field>
        <field name="name">Inversiones financieras a corto plazo en instrumentos de patrimonio</field>
    </record>
    <record id="account_group_541" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">541</field>
        <field name="name">Valores representativos de deuda a corto plazo</field>
    </record>
    <record id="account_group_542" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">542</field>
        <field name="name">Créditos a corto plazo</field>
    </record>
    <record id="account_group_543" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">543</field>
        <field name="name">Créditos a corto plazo por enajenación de inmovilizado</field>
    </record>
    <record id="account_group_544" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">544</field>
        <field name="name">Créditos a corto plazo al personal</field>
    </record>
    <record id="account_group_545" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">545</field>
        <field name="name">Dividendo a cobrar</field>
    </record>
    <record id="account_group_546" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">546</field>
        <field name="name">Intereses a corto plazo de valores representativos de deudas</field>
    </record>
    <record id="account_group_547" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">547</field>
        <field name="name">Intereses a corto plazo de créditos</field>
    </record>
    <record id="account_group_548" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">548</field>
        <field name="name">Imposiciones a corto plazo</field>
    </record>
    <record id="account_group_549" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">549</field>
        <field name="name">Desembolsos pendientes sobre participaciones en el patrimonio neto a corto plazo</field>
    </record>
    <record id="account_group_55" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">55</field>
        <field name="name">Otras cuentas no bancarias</field>
    </record>
    <record id="account_group_550" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">550</field>
        <field name="name">Titular de la explotación</field>
    </record>
    <record id="account_group_551" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">551</field>
        <field name="name">Cuenta corriente con socios y administradores</field>
    </record>
    <record id="account_group_552" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">552</field>
        <field name="name">Cuenta corriente con otras personas y entidades vinculadas</field>
    </record>
    <record id="account_group_5523" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5523</field>
        <field name="name">Cuenta corriente con empresas del grupo</field>
    </record>
    <record id="account_group_5524" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5524</field>
        <field name="name">Cuenta corriente con empresas asociadas</field>
    </record>
    <record id="account_group_5525" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5525</field>
        <field name="name">Cuenta corriente con otras partes vinculadas</field>
    </record>
    <record id="account_group_553" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">553</field>
        <field name="name">Cuentas corrientes en fusiones y escisiones</field>
    </record>
    <record id="account_group_5530" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5530</field>
        <field name="name">Socios de sociedad disuelta</field>
    </record>
    <record id="account_group_5531" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5531</field>
        <field name="name">Socios, cuenta de fusión</field>
    </record>
    <record id="account_group_5532" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5532</field>
        <field name="name">Socios de sociedad escindida</field>
    </record>
    <record id="account_group_5533" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5533</field>
        <field name="name">Socios, cuenta de escisión</field>
    </record>
    <record id="account_group_554" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">554</field>
        <field name="name">Cuenta corriente con uniones temporales de empresas y comunidades de bienes</field>
    </record>
    <record id="account_group_555" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">555</field>
        <field name="name">Partidas pendientes de aplicación</field>
    </record>
    <record id="account_group_556" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">556</field>
        <field name="name">Desembolsos exigidos sobre participaciones en el patrimonio neto</field>
    </record>
    <record id="account_group_5563" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5563</field>
        <field name="name">Desembolsos exigidos sobre participaciones, empresas del grupo</field>
    </record>
    <record id="account_group_5564" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5564</field>
        <field name="name">Desembolsos exigidos sobre participaciones, empresas asociadas</field>
    </record>
    <record id="account_group_5565" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5565</field>
        <field name="name">Desembolsos exigidos sobre participaciones, otras partes vinculadas</field>
    </record>
    <record id="account_group_5566" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5566</field>
        <field name="name">Desembolsos exigidos sobre participaciones de otras empresas</field>
    </record>
    <record id="account_group_557" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">557</field>
        <field name="name">Dividendo activo a cuenta</field>
    </record>
    <record id="account_group_558" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">558</field>
        <field name="name">Socios por desembolsos exigidos</field>
    </record>
    <record id="account_group_5580" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5580</field>
        <field name="name">Socios por desembolsos exigidos sobre acciones o participaciones ordinarias</field>
    </record>
    <record id="account_group_5585" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5585</field>
        <field name="name">Socios por desembolsos exigidos sobre acciones o participaciones consideradas como pasivos financieros</field>
    </record>
    <record id="account_group_559" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">559</field>
        <field name="name">Derivados financieros a corto plazo</field>
    </record>
    <record id="account_group_5590" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5590</field>
        <field name="name">Activos por derivados financieros a corto plazo, cartera de negociación</field>
    </record>
    <record id="account_group_5593" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5593</field>
        <field name="name">Activos por derivados financieros a corto plazo, instrumentos de cobertura</field>
    </record>
    <record id="account_group_5595" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5595</field>
        <field name="name">Pasivos por derivados financieros a corto plazo, cartera de negociación</field>
    </record>
    <record id="account_group_5598" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5598</field>
        <field name="name">Pasivos por derivados financieros a corto plazo, instrumentos de cobertura</field>
    </record>
    <record id="account_group_56" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">56</field>
        <field name="name">Fianzas y depósitos recibidos y constituidos a corto plazo y ajustes por periodificación</field>
    </record>
    <record id="account_group_560" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">560</field>
        <field name="name">Fianzas recibidas a corto plazo</field>
    </record>
    <record id="account_group_561" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">561</field>
        <field name="name">Depósitos recibidos a corto plazo</field>
    </record>
    <record id="account_group_565" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">565</field>
        <field name="name">Fianzas constituidas a corto plazo</field>
    </record>
    <record id="account_group_566" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">566</field>
        <field name="name">Depósitos constituidos a corto plazo</field>
    </record>
    <record id="account_group_567" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">567</field>
        <field name="name">Intereses pagados por anticipado</field>
    </record>
    <record id="account_group_568" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">568</field>
        <field name="name">Intereses cobrados por anticipado</field>
    </record>
    <record id="account_group_569" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">569</field>
        <field name="name">Garantías financieras a corto plazo</field>
    </record>
    <record id="account_group_57" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">57</field>
        <field name="name">Tesorería</field>
    </record>
    <record id="account_group_570" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">570</field>
        <field name="name">Caja, euros</field>
    </record>
    <record id="account_group_571" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">571</field>
        <field name="name">Caja, moneda extranjera</field>
    </record>
    <record id="account_group_572" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">572</field>
        <field name="name">Bancos e instituciones de crédito c/c vista, euros</field>
    </record>
    <record id="account_group_573" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">573</field>
        <field name="name">Bancos e instituciones de crédito c/c vista, moneda extranjera</field>
    </record>
    <record id="account_group_574" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">574</field>
        <field name="name">Bancos e instituciones de crédito, cuentas de ahorro, euros</field>
    </record>
    <record id="account_group_575" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">575</field>
        <field name="name">Bancos e instituciones de crédito, cuentas de ahorro, moneda extranjera</field>
    </record>
    <record id="account_group_576" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">576</field>
        <field name="name">Inversiones a corto plazo de gran liquidez</field>
    </record>
    <record id="account_group_58" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">58</field>
        <field name="name">Activos no corrientes mantenidos para la venta y activos y pasivos asociados</field>
    </record>
    <record id="account_group_580" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">580</field>
        <field name="name">Inmovilizado</field>
    </record>
    <record id="account_group_581" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">581</field>
        <field name="name">Inversiones con personas y entidades vinculadas</field>
    </record>
    <record id="account_group_582" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">582</field>
        <field name="name">Inversiones financieras</field>
    </record>
    <record id="account_group_583" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">583</field>
        <field name="name">Existencias, deudores comerciales y otras cuentas a cobrar</field>
    </record>
    <record id="account_group_584" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">584</field>
        <field name="name">Otros activos</field>
    </record>
    <record id="account_group_585" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">585</field>
        <field name="name">Provisiones</field>
    </record>
    <record id="account_group_586" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">586</field>
        <field name="name">Deudas con características especiales</field>
    </record>
    <record id="account_group_587" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">587</field>
        <field name="name">Deudas con personas y entidades vinculadas</field>
    </record>
    <record id="account_group_588" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">588</field>
        <field name="name">Acreedores comerciales y otras cuentas a pagar</field>
    </record>
    <record id="account_group_589" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">589</field>
        <field name="name">Otros pasivos</field>
    </record>
    <record id="account_group_59" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">59</field>
        <field name="name">Deterioro del valor de inversiones financieras a corto plazo y de activos no corrientes mantenidos para la venta</field>
    </record>
    <record id="account_group_593" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">593</field>
        <field name="name">Deterioro de valor de participaciones a corto plazo en partes vinculadas</field>
    </record>
    <record id="account_group_596" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">596</field>
        <field name="name">Deterioro de valor de participaciones a corto plazo</field>
    </record>
    <record id="account_group_5933" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5933</field>
        <field name="name">Deterioro de valor de participaciones a corto plazo en empresas del grupo</field>
    </record>
    <record id="account_group_5934" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5934</field>
        <field name="name">Deterioro de valor de participaciones a corto plazo en empresas asociadas</field>
    </record>
    <record id="account_group_594" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">594</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a corto plazo de partes vinculadas</field>
    </record>
    <record id="account_group_5943" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5943</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a corto plazo de empresas del grupo</field>
    </record>
    <record id="account_group_5944" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5944</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a corto plazo de empresas asociadas</field>
    </record>
    <record id="account_group_5945" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5945</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a corto plazo de otras partes vinculadas</field>
    </record>
    <record id="account_group_595" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">595</field>
        <field name="name">Deterioro de valor de créditos a corto plazo a partes vinculadas</field>
    </record>
    <record id="account_group_5953" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5953</field>
        <field name="name">Deterioro de valor de créditos a corto plazo a empresas del grupo</field>
    </record>
    <record id="account_group_5954" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5954</field>
        <field name="name">Deterioro de valor de créditos a corto plazo a empresas asociadas</field>
    </record>
    <record id="account_group_5955" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5955</field>
        <field name="name">Deterioro de valor de créditos a corto plazo a otras partes vinculadas</field>
    </record>
    <record id="account_group_597" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">597</field>
        <field name="name">Deterioro de valor de valores representativos de deuda a corto plazo</field>
    </record>
    <record id="account_group_598" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">598</field>
        <field name="name">Deterioro de valor de créditos a corto plazo</field>
    </record>
    <record id="account_group_599" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">599</field>
        <field name="name">Deterioro de valor de activos no corrientes mantenidos para la venta</field>
    </record>
    <record id="account_group_5990" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5990</field>
        <field name="name">Deterioro de valor de inmovilizado no corriente mantenido para la venta</field>
    </record>
    <record id="account_group_5991" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5991</field>
        <field name="name">Deterioro de valor de inversiones con personas y entidades vinculadas no corrientes mantenidas para la venta</field>
    </record>
    <record id="account_group_5992" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5992</field>
        <field name="name">Deterioro de valor de inversiones financieras no corrientes mantenidas para la venta</field>
    </record>
    <record id="account_group_5993" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5993</field>
        <field name="name">Deterioro de valor de existencias, deudores comerciales y otras cuentas a cobrar integrados en un grupo enajenable mantenido para la venta</field>
    </record>
    <record id="account_group_5994" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">5994</field>
        <field name="name">Deterioro de valor de otros activos mantenidos para la venta</field>
    </record>
        <record id="account_group_6" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6</field>
        <field name="name">Compras y gastos</field>
    </record>
    <record id="account_group_60" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">60</field>
        <field name="name">Compras</field>
    </record>
    <record id="account_group_600" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">600</field>
        <field name="name">Compras de mercaderías</field>
    </record>
    <record id="account_group_601" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">601</field>
        <field name="name">Compras de materias primas</field>
    </record>
    <record id="account_group_602" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">602</field>
        <field name="name">Compras de otros aprovisionamientos</field>
    </record>
    <record id="account_group_606" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">606</field>
        <field name="name">Descuentos sobre compras por pronto pago</field>
    </record>
    <record id="account_group_6060" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6060</field>
        <field name="name">Descuentos sobre compras por pronto pago de mercaderías</field>
    </record>
    <record id="account_group_6061" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6061</field>
        <field name="name">Descuentos sobre compras por pronto pago de materias primas</field>
    </record>
    <record id="account_group_6062" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6062</field>
        <field name="name">Descuentos sobre compras por pronto pago de otros aprovisionamientos</field>
    </record>
    <record id="account_group_607" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">607</field>
        <field name="name">Trabajos realizados por otras empresas</field>
    </record>
    <record id="account_group_608" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">608</field>
        <field name="name">Devoluciones de compras y operaciones similares</field>
    </record>
    <record id="account_group_6080" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6080</field>
        <field name="name">Devoluciones de compras de mercaderías</field>
    </record>
    <record id="account_group_6081" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6081</field>
        <field name="name">Devoluciones de compras de materias primas</field>
    </record>
    <record id="account_group_6082" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6082</field>
        <field name="name">Devoluciones de compras de otros aprovisionamientos</field>
    </record>
    <record id="account_group_609" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">609</field>
        <field name="name">«Rappels» por compras</field>
    </record>
    <record id="account_group_6090" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6090</field>
        <field name="name">«Rappels» por compras de mercaderías</field>
    </record>
    <record id="account_group_6091" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6091</field>
        <field name="name">«Rappels» por compras de materias primas</field>
    </record>
    <record id="account_group_6092" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6092</field>
        <field name="name">«Rappels» por compras de otros aprovisionamientos</field>
    </record>
    <record id="account_group_61" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">61</field>
        <field name="name">Variación de existencias</field>
    </record>
    <record id="account_group_610" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">610</field>
        <field name="name">Variación de existencias de mercaderías</field>
    </record>
    <record id="account_group_611" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">611</field>
        <field name="name">Variación de existencias de materias primas</field>
    </record>
    <record id="account_group_612" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">612</field>
        <field name="name">Variación de existencias de otros aprovisionamientos</field>
    </record>
    <record id="account_group_62" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">62</field>
        <field name="name">Servicios exteriores</field>
    </record>
    <record id="account_group_620" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">620</field>
        <field name="name">Gastos en investigación y desarrollo del ejercicio</field>
    </record>
    <record id="account_group_621" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">621</field>
        <field name="name">Arrendamientos y cánones</field>
    </record>
    <record id="account_group_622" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">622</field>
        <field name="name">Reparaciones y conservación</field>
    </record>
    <record id="account_group_623" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">623</field>
        <field name="name">Servicios de profesionales independientes</field>
    </record>
    <record id="account_group_624" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">624</field>
        <field name="name">Transportes</field>
    </record>
    <record id="account_group_625" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">625</field>
        <field name="name">Primas de seguros</field>
    </record>
    <record id="account_group_626" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">626</field>
        <field name="name">Servicios bancarios y similares</field>
    </record>
    <record id="account_group_627" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">627</field>
        <field name="name">Publicidad, propaganda y relaciones públicas</field>
    </record>
    <record id="account_group_628" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">628</field>
        <field name="name">Suministros</field>
    </record>
    <record id="account_group_629" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">629</field>
        <field name="name">Otros servicios</field>
    </record>
    <record id="account_group_63" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">63</field>
        <field name="name">Tributos</field>
    </record>
    <record id="account_group_630" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">630</field>
        <field name="name">Impuesto sobre beneficios</field>
    </record>
    <record id="account_group_6300" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6300</field>
        <field name="name">Impuesto corriente</field>
    </record>
    <record id="account_group_6301" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6301</field>
        <field name="name">Impuesto diferido</field>
    </record>
    <record id="account_group_631" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">631</field>
        <field name="name">Otros tributos</field>
    </record>
    <record id="account_group_633" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">633</field>
        <field name="name">Ajustes negativos en la imposición sobre beneficios</field>
    </record>
    <record id="account_group_634" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">634</field>
        <field name="name">Ajustes negativos en la imposición indirecta</field>
    </record>
    <record id="account_group_6341" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6341</field>
        <field name="name">Ajustes negativos en IVA de activo corriente</field>
    </record>
    <record id="account_group_6342" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6342</field>
        <field name="name">Ajustes negativos en IVA de inversiones</field>
    </record>
    <record id="account_group_636" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">636</field>
        <field name="name">Devolución de impuestos</field>
    </record>
    <record id="account_group_638" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">638</field>
        <field name="name">Ajustes positivos en la imposición sobre beneficios</field>
    </record>
    <record id="account_group_639" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">639</field>
        <field name="name">Ajustes positivos en la imposición indirecta</field>
    </record>
    <record id="account_group_6391" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6391</field>
        <field name="name">Ajustes positivos en IVA de activo corriente</field>
    </record>
    <record id="account_group_6392" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6392</field>
        <field name="name">Ajustes positivos en IVA de inversiones</field>
    </record>
    <record id="account_group_64" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">64</field>
        <field name="name">Gastos de personal</field>
    </record>
    <record id="account_group_640" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">640</field>
        <field name="name">Sueldos y salarios</field>
    </record>
    <record id="account_group_641" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">641</field>
        <field name="name">Indemnizaciones</field>
    </record>
    <record id="account_group_642" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">642</field>
        <field name="name">Seguridad Social a cargo de la empresa</field>
    </record>
    <record id="account_group_643" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">643</field>
        <field name="name">Retribuciones a largo plazo mediante sistemas de aportación definida</field>
    </record>
    <record id="account_group_644" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">644</field>
        <field name="name">Retribuciones a largo plazo mediante sistemas de prestación definida</field>
    </record>
    <record id="account_group_6440" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6440</field>
        <field name="name">Contribuciones anuales</field>
    </record>
    <record id="account_group_6442" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6442</field>
        <field name="name">Otros costes</field>
    </record>
    <record id="account_group_645" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">645</field>
        <field name="name">Retribuciones al personal mediante instrumentos de patrimonio</field>
    </record>
    <record id="account_group_6450" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6450</field>
        <field name="name">Retribuciones al personal liquidados con instrumentos de patrimonio</field>
    </record>
    <record id="account_group_6457" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6457</field>
        <field name="name">Retribuciones al personal liquidados en efectivo basado en instrumentos de patrimonio</field>
    </record>
    <record id="account_group_649" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">649</field>
        <field name="name">Otros gastos sociales</field>
    </record>
    <record id="account_group_65" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">65</field>
        <field name="name">Otros gastos de gestión</field>
    </record>
    <record id="account_group_650" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">650</field>
        <field name="name">Pérdidas de créditos comerciales incobrables</field>
    </record>
    <record id="account_group_651" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">651</field>
        <field name="name">Resultados de operaciones en común</field>
    </record>
    <record id="account_group_6510" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6510</field>
        <field name="name">Beneficio transferido (gestor)</field>
    </record>
    <record id="account_group_6511" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6511</field>
        <field name="name">Pérdida soportada (partícipe o asociado no gestor)</field>
    </record>
    <record id="account_group_653" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">653</field>
        <field name="name">Compensación de gastos por prestaciones de colaboración</field>
    </record>
    <record id="account_group_654" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">654</field>
        <field name="name">Reembolsos de gastos al órgano de gobierno</field>
    </record>
    <record id="account_group_655" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">655</field>
        <field name="name">Pérdidas de créditos incobrables derivados de la actividad</field>
    </record>
    <record id="account_group_656" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">656</field>
        <field name="name">Resultados de operaciones en común</field>
    </record>
    <record id="account_group_658" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">658</field>
        <field name="name">Reintegro de subvenciones, donaciones y legados recibidos, afectos a la actividad propia de la entidad</field>
    </record>
    <record id="account_group_659" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">659</field>
        <field name="name">Otras pérdidas en gestión corriente</field>
    </record>
    <record id="account_group_66" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">66</field>
        <field name="name">Gastos financieros</field>
    </record>
    <record id="account_group_660" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">660</field>
        <field name="name">Gastos financieros por actualización de provisiones</field>
    </record>
    <record id="account_group_661" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">661</field>
        <field name="name">Intereses de obligaciones y bonos</field>
    </record>
    <record id="account_group_6610" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6610</field>
        <field name="name">Intereses de obligaciones y bonos a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6611" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6611</field>
        <field name="name">Intereses de obligaciones y bonos a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6612" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6612</field>
        <field name="name">Intereses de obligaciones y bonos a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6613" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6613</field>
        <field name="name">Intereses de obligaciones y bonos a largo plazo, otras empresas</field>
    </record>
    <record id="account_group_6615" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6615</field>
        <field name="name">Intereses de obligaciones y bonos a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6616" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6616</field>
        <field name="name">Intereses de obligaciones y bonos a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6617" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6617</field>
        <field name="name">Intereses de obligaciones y bonos a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6618" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6618</field>
        <field name="name">Intereses de obligaciones y bonos a corto plazo, otras empresas</field>
    </record>
    <record id="account_group_662" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">662</field>
        <field name="name">Intereses de deudas</field>
    </record>
    <record id="account_group_6620" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6620</field>
        <field name="name">Intereses de deudas, empresas del grupo</field>
    </record>
    <record id="account_group_6621" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6621</field>
        <field name="name">Intereses de deudas, empresas asociadas</field>
    </record>
    <record id="account_group_6622" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6622</field>
        <field name="name">Intereses de deudas, otras partes vinculadas</field>
    </record>
    <record id="account_group_6623" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6623</field>
        <field name="name">Intereses de deudas con entidades de crédito</field>
    </record>
    <record id="account_group_6624" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6624</field>
        <field name="name">Intereses de deudas, otras empresas</field>
    </record>
    <record id="account_group_663" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">663</field>
        <field name="name">Pérdidas por valoración de instrumentos financieros por su valor razonable</field>
    </record>
    <record id="account_group_6630" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6630</field>
        <field name="name">Pérdidas de cartera de negociación</field>
    </record>
    <record id="account_group_6631" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6631</field>
        <field name="name">Pérdidas de designados por la empresa</field>
    </record>
    <record id="account_group_6632" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6632</field>
        <field name="name">Pérdidas de disponibles para la venta</field>
    </record>
    <record id="account_group_6633" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6633</field>
        <field name="name">Pérdidas de instrumentos de cobertura</field>
    </record>
    <record id="account_group_664" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">664</field>
        <field name="name">Gastos por dividendos de acciones o participaciones consideradas como pasivos financieros</field>
    </record>
    <record id="account_group_6640" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6640</field>
        <field name="name">Dividendos de pasivos, empresas del grupo</field>
    </record>
    <record id="account_group_6641" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6641</field>
        <field name="name">Dividendos de pasivos, empresas asociadas</field>
    </record>
    <record id="account_group_6642" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6642</field>
        <field name="name">Dividendos de pasivos, otras partes vinculadas</field>
    </record>
    <record id="account_group_6643" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6643</field>
        <field name="name">Dividendos de pasivos, otras empresas</field>
    </record>
    <record id="account_group_665" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">665</field>
        <field name="name">Intereses por descuento de efectos y operaciones de «factoring»</field>
    </record>
    <record id="account_group_6650" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6650</field>
        <field name="name">Intereses por descuento de efectos en entidades de crédito del grupo</field>
    </record>
    <record id="account_group_6651" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6651</field>
        <field name="name">Intereses por descuento de efectos en entidades de crédito asociadas</field>
    </record>
    <record id="account_group_6652" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6652</field>
        <field name="name">Intereses por descuento de efectos en otras entidades de crédito vinculadas</field>
    </record>
    <record id="account_group_6653" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6653</field>
        <field name="name">Intereses por descuento de efectos en otras entidades de crédito</field>
    </record>
    <record id="account_group_6654" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6654</field>
        <field name="name">Intereses por operaciones de «factoring» con entidades de crédito del grupo</field>
    </record>
    <record id="account_group_6655" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6655</field>
        <field name="name">Intereses por operaciones de "factoring" con entidades de crédito asociadas</field>
    </record>
    <record id="account_group_6656" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6656</field>
        <field name="name">Intereses por operaciones de "factoring" con entidades de crédito vinculadas</field>
    </record>
    <record id="account_group_6657" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6657</field>
        <field name="name">Intereses por operaciones de «factoring» con otras entidades de crédito</field>
    </record>
    <record id="account_group_666" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">666</field>
        <field name="name">Pérdidas en participaciones y valores representativos de deuda</field>
    </record>
    <record id="account_group_6660" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6660</field>
        <field name="name">Pérdidas en valores representativos de deuda a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6661" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6661</field>
        <field name="name">Pérdidas en valores representativos de deuda a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6662" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6662</field>
        <field name="name">Pérdidas en valores representativos de deuda a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6663" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6663</field>
        <field name="name">Pérdidas en participaciones y valores representativos de deuda a largo plazo, otras empresas</field>
    </record>
    <record id="account_group_6665" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6665</field>
        <field name="name">Pérdidas en participaciones y valores representativos de deuda a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6666" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6666</field>
        <field name="name">Pérdidas en participaciones y valores representativos de deuda a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6667" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6667</field>
        <field name="name">Pérdidas en valores representativos de deuda a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6668" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6668</field>
        <field name="name">Pérdidas en valores representativos de deuda a corto plazo, otras empresas</field>
    </record>
    <record id="account_group_667" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">667</field>
        <field name="name">Pérdidas de créditos no comerciales</field>
    </record>
    <record id="account_group_6670" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6670</field>
        <field name="name">Pérdidas de créditos a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6671" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6671</field>
        <field name="name">Pérdidas de créditos a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6672" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6672</field>
        <field name="name">Pérdidas de créditos a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6673" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6673</field>
        <field name="name">Pérdidas de créditos a largo plazo, otras empresas</field>
    </record>
    <record id="account_group_6675" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6675</field>
        <field name="name">Pérdidas de créditos a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6676" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6676</field>
        <field name="name">Pérdidas de créditos a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6677" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6677</field>
        <field name="name">Pérdidas de créditos a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6678" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6678</field>
        <field name="name">Pérdidas de créditos a corto plazo, otras empresas</field>
    </record>
    <record id="account_group_668" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">668</field>
        <field name="name">Diferencias negativas de cambio</field>
    </record>
    <record id="account_group_669" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">669</field>
        <field name="name">Otros gastos financieros</field>
    </record>
    <record id="account_group_67" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">67</field>
        <field name="name">Pérdidas procedentes de activos no corrientes y gastos excepcionales</field>
    </record>
    <record id="account_group_670" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">670</field>
        <field name="name">Pérdidas procedentes del inmovilizado intangible</field>
    </record>
    <record id="account_group_671" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">671</field>
        <field name="name">Pérdidas procedentes del inmovilizado material</field>
    </record>
    <record id="account_group_672" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">672</field>
        <field name="name">Pérdidas procedentes de las inversiones inmobiliarias</field>
    </record>
    <record id="account_group_673" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">673</field>
        <field name="name">Pérdidas procedentes de participaciones a largo plazo en partes vinculadas</field>
    </record>
    <record id="account_group_6733" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6733</field>
        <field name="name">Pérdidas procedentes de participaciones a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6734" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6734</field>
        <field name="name">Pérdidas procedentes de participaciones a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6735" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6735</field>
        <field name="name">Pérdidas procedentes de participaciones a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_675" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">675</field>
        <field name="name">Pérdidas por operaciones con obligaciones propias</field>
    </record>
    <record id="account_group_678" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">678</field>
        <field name="name">Gastos excepcionales</field>
    </record>
    <record id="account_group_68" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">68</field>
        <field name="name">Dotaciones para amortizaciones</field>
    </record>
    <record id="account_group_680" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">680</field>
        <field name="name">Amortización del inmovilizado intangible</field>
    </record>
    <record id="account_group_681" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">681</field>
        <field name="name">Amortización del inmovilizado material</field>
    </record>
    <record id="account_group_682" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">682</field>
        <field name="name">Amortización de las inversiones inmobiliarias</field>
    </record>
    <record id="account_group_69" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">69</field>
        <field name="name">Pérdidas por deterioroy otras dotaciones</field>
    </record>
    <record id="account_group_690" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">690</field>
        <field name="name">Pérdidas por deterioro del inmovilizado intangible</field>
    </record>
    <record id="account_group_691" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">691</field>
        <field name="name">Pérdidas por deterioro del inmovilizado material</field>
    </record>
    <record id="account_group_692" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">692</field>
        <field name="name">Pérdidas por deterioro de las inversiones inmobiliarias</field>
    </record>
    <record id="account_group_693" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">693</field>
        <field name="name">Pérdidas por deterioro de existencias</field>
    </record>
    <record id="account_group_6930" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6930</field>
        <field name="name">Pérdidas por deterioro de productos terminados y en curso de fabricación</field>
    </record>
    <record id="account_group_6931" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6931</field>
        <field name="name">Pérdidas por deterioro de mercaderías</field>
    </record>
    <record id="account_group_6932" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6932</field>
        <field name="name">Pérdidas por deterioro de materias primas</field>
    </record>
    <record id="account_group_6933" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6933</field>
        <field name="name">Pérdidas por deterioro de otros aprovisionamientos</field>
    </record>
    <record id="account_group_694" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">694</field>
        <field name="name">Pérdidas por deterioro de créditos por operaciones comerciales</field>
    </record>
    <record id="account_group_695" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">695</field>
        <field name="name">Dotación a la provisión por operaciones comerciales</field>
    </record>
    <record id="account_group_6954" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6954</field>
        <field name="name">Dotación a la provisión por contratos onerosos</field>
    </record>
    <record id="account_group_6959" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6959</field>
        <field name="name">Dotación a la provisión para otras operaciones comerciales</field>
    </record>
    <record id="account_group_696" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">696</field>
        <field name="name">Pérdidas por deterioro de participaciones y valores representativos de deuda a largo plazo</field>
    </record>
    <record id="account_group_6960" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6960</field>
        <field name="name">Pérdidas por deterioro de participaciones en instrumentos de patrimonio neto a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6961" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6961</field>
        <field name="name">Pérdidas por deterioro de participaciones en instrumentos de patrimonio neto a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6962" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6962</field>
        <field name="name">Pérdidas por deterioro de participaciones en instrumentos de patrimonio neto a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6963" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6963</field>
        <field name="name">Pérdidas por deterioro de participaciones en instrumentos de patrimonio neto a largo plazo, otras empresas</field>
    </record>
    <record id="account_group_6965" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6965</field>
        <field name="name">Pérdidas por deterioro en valores representativos de deuda a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6966" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6966</field>
        <field name="name">Pérdidas por deterioro en valores representativos de deuda a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6967" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6967</field>
        <field name="name">Pérdidas por deterioro en valores representativos de deuda a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6968" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6968</field>
        <field name="name">Pérdidas por deterioro en valores representativos de deuda a largo plazo, de otras empresas</field>
    </record>
    <record id="account_group_697" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">697</field>
        <field name="name">Pérdidas por deterioro de créditos a largo plazo</field>
    </record>
    <record id="account_group_6970" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6970</field>
        <field name="name">Pérdidas por deterioro de créditos a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6971" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6971</field>
        <field name="name">Pérdidas por deterioro de créditos a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6972" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6972</field>
        <field name="name">Pérdidas por deterioro de créditos a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6973" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6973</field>
        <field name="name">Pérdidas por deterioro de créditos a largo plazo, otras empresas</field>
    </record>
    <record id="account_group_698" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">698</field>
        <field name="name">Pérdidas por deterioro de participaciones y valores representativos de deuda a corto plazo</field>
    </record>
    <record id="account_group_6980" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6980</field>
        <field name="name">Pérdidas por deterioro de participaciones en instrumentos de patrimonio neto a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6981" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6981</field>
        <field name="name">Pérdidas por deterioro de participaciones en instrumentos de patrimonio neto a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6985" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6985</field>
        <field name="name">Pérdidas por deterioro en valores representativos de deuda a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6986" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6986</field>
        <field name="name">Pérdidas por deterioro en valores representativos de deuda a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6987" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6987</field>
        <field name="name">Pérdidas por deterioro en valores representativos de deuda a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6988" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6988</field>
        <field name="name">Pérdidas por deterioro en valores representativos de deuda a corto plazo, de otras empresas</field>
    </record>
    <record id="account_group_699" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">699</field>
        <field name="name">Pérdidas por deterioro de créditos a corto plazo</field>
    </record>
    <record id="account_group_6990" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6990</field>
        <field name="name">Pérdidas por deterioro de créditos a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_6991" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6991</field>
        <field name="name">Pérdidas por deterioro de créditos a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_6992" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6992</field>
        <field name="name">Pérdidas por deterioro de créditos a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_6993" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">6993</field>
        <field name="name">Pérdidas por deterioro de créditos a corto plazo, otras empresas</field>
    </record>
        <record id="account_group_7" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7</field>
        <field name="name">Ventas e ingresos</field>
    </record>
    <record id="account_group_70" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">70</field>
        <field name="name">Ventas de mercaderías, de producción propia, de servicios, etc</field>
    </record>
    <record id="account_group_700" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">700</field>
        <field name="name">Ventas de mercaderías</field>
    </record>
    <record id="account_group_701" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">701</field>
        <field name="name">Ventas de productos terminados</field>
    </record>
    <record id="account_group_702" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">702</field>
        <field name="name">Ventas de productos semiterminados</field>
    </record>
    <record id="account_group_703" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">703</field>
        <field name="name">Ventas de subproductos y residuos</field>
    </record>
    <record id="account_group_704" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">704</field>
        <field name="name">Ventas de envases y embalajes</field>
    </record>
    <record id="account_group_705" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">705</field>
        <field name="name">Prestaciones de servicios</field>
    </record>
    <record id="account_group_706" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">706</field>
        <field name="name">Descuentos sobre ventas por pronto pago</field>
    </record>
    <record id="account_group_7060" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7060</field>
        <field name="name">Descuentos sobre ventas por pronto pago de mercaderías</field>
    </record>
    <record id="account_group_7061" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7061</field>
        <field name="name">Descuentos sobre ventas por pronto pago de productos terminados</field>
    </record>
    <record id="account_group_7062" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7062</field>
        <field name="name">Descuentos sobre ventas por pronto pago de productos semiterminados</field>
    </record>
    <record id="account_group_7063" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7063</field>
        <field name="name">Descuentos sobre ventas por pronto pago de subproductos y residuos</field>
    </record>
    <record id="account_group_708" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">708</field>
        <field name="name">Devoluciones de ventas y operaciones similares</field>
    </record>
    <record id="account_group_7080" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7080</field>
        <field name="name">Devoluciones de ventas de mercaderías</field>
    </record>
    <record id="account_group_7081" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7081</field>
        <field name="name">Devoluciones de ventas de productos terminados</field>
    </record>
    <record id="account_group_7082" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7082</field>
        <field name="name">Devoluciones de ventas de productos semiterminados</field>
    </record>
    <record id="account_group_7083" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7083</field>
        <field name="name">Devoluciones de ventas de subproductos y residuos</field>
    </record>
    <record id="account_group_7084" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7084</field>
        <field name="name">Devoluciones de ventas de envases y embalajes</field>
    </record>
    <record id="account_group_709" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">709</field>
        <field name="name">«Rappels» sobre ventas</field>
    </record>
    <record id="account_group_7090" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7090</field>
        <field name="name">«Rappels» sobre ventas de mercaderías</field>
    </record>
    <record id="account_group_7091" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7091</field>
        <field name="name">«Rappels» sobre ventas de productos terminados</field>
    </record>
    <record id="account_group_7092" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7092</field>
        <field name="name">«Rappels» sobre ventas de productos semiterminados</field>
    </record>
    <record id="account_group_7093" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7093</field>
        <field name="name">«Rappels» sobre ventas de subproductos y residuos</field>
    </record>
    <record id="account_group_7094" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7094</field>
        <field name="name">«Rappels» sobre ventas de envases y embalajes</field>
    </record>
    <record id="account_group_71" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">71</field>
        <field name="name">Variación de existencias</field>
    </record>
    <record id="account_group_710" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">710</field>
        <field name="name">Variación de existencias de productos en curso</field>
    </record>
    <record id="account_group_711" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">711</field>
        <field name="name">Variación de existencias de productos semiterminados</field>
    </record>
    <record id="account_group_712" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">712</field>
        <field name="name">Variación de existencias de productos terminados</field>
    </record>
    <record id="account_group_713" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">713</field>
        <field name="name">Variación de existencias de subproductos, residuos y materiales recuperados</field>
    </record>
    <record id="account_group_72" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">72</field>
        <field name="name">Ingresos propios de la entidad</field>
    </record>
    <record id="account_group_720" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">720</field>
        <field name="name">Cuotas de asociados y afiliados</field>
    </record>
    <record id="account_group_721" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">721</field>
        <field name="name">Cuotas de usuarios</field>
    </record>
    <record id="account_group_722" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">722</field>
        <field name="name">Promociones para captación de recursos</field>
    </record>
    <record id="account_group_723" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">723</field>
        <field name="name">Ingresos de patrocinadores y colaboraciones</field>
    </record>
    <record id="account_group_728" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">728</field>
        <field name="name">Ingresos por reintegro de ayudas y asignaciones</field>
    </record>
    <record id="account_group_73" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">73</field>
        <field name="name">Trabajos realizados para la empresa</field>
    </record>
    <record id="account_group_730" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">730</field>
        <field name="name">Trabajos realizados para el inmovilizado intangible</field>
    </record>
    <record id="account_group_731" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">731</field>
        <field name="name">Trabajos realizados para el inmovilizado material</field>
    </record>
    <record id="account_group_732" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">732</field>
        <field name="name">Trabajos realizados en inversiones inmobiliarias</field>
    </record>
    <record id="account_group_733" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">733</field>
        <field name="name">Trabajos realizados para el inmovilizado material en curso</field>
    </record>
    <record id="account_group_74" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">74</field>
        <field name="name">Subvenciones, donaciones y legados</field>
    </record>
    <record id="account_group_740" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">740</field>
        <field name="name">Subvenciones, donaciones y legados a la explotación</field>
    </record>
    <record id="account_group_746" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">746</field>
        <field name="name">Subvenciones, donaciones y legados de capital transferidos al resultado del ejercicio</field>
    </record>
    <record id="account_group_747" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">747</field>
        <field name="name">Otras subvenciones, donaciones y legados transferidos al resultado del ejercicio</field>
    </record>
    <record id="account_group_75" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">75</field>
        <field name="name">Otros ingresos de gestión</field>
    </record>
    <record id="account_group_751" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">751</field>
        <field name="name">Resultados de operaciones en común</field>
    </record>
    <record id="account_group_7510" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7510</field>
        <field name="name">Pérdida transferida (gestor)</field>
    </record>
    <record id="account_group_7511" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7511</field>
        <field name="name">Beneficio atribuido (partícipe o asociado no gestor)</field>
    </record>
    <record id="account_group_752" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">752</field>
        <field name="name">Ingresos por arrendamientos</field>
    </record>
    <record id="account_group_753" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">753</field>
        <field name="name">Ingresos de propiedad industrial cedida en explotación</field>
    </record>
    <record id="account_group_754" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">754</field>
        <field name="name">Ingresos por comisiones</field>
    </record>
    <record id="account_group_755" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">755</field>
        <field name="name">Ingresos por servicios al personal</field>
    </record>
    <record id="account_group_759" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">759</field>
        <field name="name">Ingresos por servicios diversos</field>
    </record>
    <record id="account_group_76" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">76</field>
        <field name="name">Ingresos financieros</field>
    </record>
    <record id="account_group_760" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">760</field>
        <field name="name">Ingresos de participaciones en instrumentos de patrimonio</field>
    </record>
    <record id="account_group_7600" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7600</field>
        <field name="name">Ingresos de participaciones en instrumentos de patrimonio, empresas del grupo</field>
    </record>
    <record id="account_group_7601" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7601</field>
        <field name="name">Ingresos de participaciones en instrumentos de patrimonio, empresas asociadas</field>
    </record>
    <record id="account_group_7602" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7602</field>
        <field name="name">Ingresos de participaciones en instrumentos de patrimonio, otras partes vinculadas</field>
    </record>
    <record id="account_group_7603" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7603</field>
        <field name="name">Ingresos de participaciones en instrumentos de patrimonio, otras empresas</field>
    </record>
    <record id="account_group_761" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">761</field>
        <field name="name">Ingresos de valores representativos de deuda</field>
    </record>
    <record id="account_group_7610" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7610</field>
        <field name="name">Ingresos de valores representativos de deuda, empresas del grupo</field>
    </record>
    <record id="account_group_7611" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7611</field>
        <field name="name">Ingresos de valores representativos de deuda, empresas asociadas</field>
    </record>
    <record id="account_group_7612" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7612</field>
        <field name="name">Ingresos de valores representativos de deuda, otras partes vinculadas</field>
    </record>
    <record id="account_group_7613" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7613</field>
        <field name="name">Ingresos de valores representativos de deuda, otras empresas</field>
    </record>
    <record id="account_group_762" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">762</field>
        <field name="name">Ingresos de créditos</field>
    </record>
    <record id="account_group_7620" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7620</field>
        <field name="name">Ingresos de créditos a largo plazo</field>
    </record>
    <record id="account_group_76200" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">76200</field>
        <field name="name">Ingresos de créditos a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_76201" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">76201</field>
        <field name="name">Ingresos de créditos a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_76202" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">76202</field>
        <field name="name">Ingresos de créditos a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_76203" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">76203</field>
        <field name="name">Ingresos de créditos a largo plazo, otras empresas</field>
    </record>
    <record id="account_group_7621" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7621</field>
        <field name="name">Ingresos de créditos a corto plazo</field>
    </record>
    <record id="account_group_76210" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">76210</field>
        <field name="name">Ingresos de créditos a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_76211" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">76211</field>
        <field name="name">Ingresos de créditos a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_76212" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">76212</field>
        <field name="name">Ingresos de créditos a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_76213" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">76213</field>
        <field name="name">Ingresos de créditos a corto plazo, otras empresas</field>
    </record>
    <record id="account_group_763" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">763</field>
        <field name="name">Beneficios por valoración de instrumentos financieros por su valor razonable</field>
    </record>
    <record id="account_group_7630" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7630</field>
        <field name="name">Beneficios de cartera de negociación</field>
    </record>
    <record id="account_group_7631" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7631</field>
        <field name="name">Beneficios de designados por la empresa</field>
    </record>
    <record id="account_group_7632" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7632</field>
        <field name="name">Beneficios de disponibles para la venta</field>
    </record>
    <record id="account_group_7633" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7633</field>
        <field name="name">Beneficios de instrumentos de cobertura</field>
    </record>
    <record id="account_group_766" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">766</field>
        <field name="name">Beneficios en participaciones y valores representativos de deuda</field>
    </record>
    <record id="account_group_7660" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7660</field>
        <field name="name">Beneficios en valores representativos de deuda a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_7661" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7661</field>
        <field name="name">Beneficios en valores representativos de deuda a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_7662" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7662</field>
        <field name="name">Beneficios en valores representativos de deuda a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_7663" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7663</field>
        <field name="name">Beneficios en participaciones y valores representativos de deuda a largo plazo, otras empresas</field>
    </record>
    <record id="account_group_7665" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7665</field>
        <field name="name">Beneficios en participaciones y valores representativos de deuda a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_7666" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7666</field>
        <field name="name">Beneficios en participaciones y valores representativos de deuda a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_7667" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7667</field>
        <field name="name">Beneficios en valores representativos de deuda a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_7668" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7668</field>
        <field name="name">Beneficios en valores representativos de deuda a corto plazo, otras empresas</field>
    </record>
    <record id="account_group_767" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">767</field>
        <field name="name">Ingresos de activos afectos y de derechos de reembolso relativos a retribuciones a largo plazo</field>
    </record>
    <record id="account_group_768" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">768</field>
        <field name="name">Diferencias positivas de cambio</field>
    </record>
    <record id="account_group_769" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">769</field>
        <field name="name">Otros ingresos financieros</field>
    </record>
    <record id="account_group_77" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">77</field>
        <field name="name">Beneficios procedentes de activos no corrientes e ingresos excepcionales</field>
    </record>
    <record id="account_group_770" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">770</field>
        <field name="name">Beneficios procedentes del inmovilizado intangible</field>
    </record>
    <record id="account_group_771" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">771</field>
        <field name="name">Beneficios procedentes del inmovilizado material</field>
    </record>
    <record id="account_group_772" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">772</field>
        <field name="name">Beneficios procedentes de las inversiones inmobiliarias</field>
    </record>
    <record id="account_group_773" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">773</field>
        <field name="name">Beneficios procedentes de participaciones a largo plazo en partes vinculadas</field>
    </record>
    <record id="account_group_7733" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7733</field>
        <field name="name">Beneficios procedentes de participaciones a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_7734" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7734</field>
        <field name="name">Beneficios procedentes de participaciones a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_7735" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7735</field>
        <field name="name">Beneficios procedentes de participaciones a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_774" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">774</field>
        <field name="name">Diferencia negativa en combinaciones de negocios</field>
    </record>
    <record id="account_group_775" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">775</field>
        <field name="name">Beneficios por operaciones con obligaciones propias</field>
    </record>
    <record id="account_group_778" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">778</field>
        <field name="name">Ingresos excepcionales</field>
    </record>
    <record id="account_group_79" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">79</field>
        <field name="name">Excesos y aplicaciones de provisiones y de pérdidas por deterioro</field>
    </record>
    <record id="account_group_790" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">790</field>
        <field name="name">Reversión del deterioro del inmovilizado intangible</field>
    </record>
    <record id="account_group_791" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">791</field>
        <field name="name">Reversión del deterioro del inmovilizado material</field>
    </record>
    <record id="account_group_792" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">792</field>
        <field name="name">Reversión del deterioro de las inversiones inmobiliarias</field>
    </record>
    <record id="account_group_793" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">793</field>
        <field name="name">Reversión del deterioro de existencias</field>
    </record>
    <record id="account_group_7930" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7930</field>
        <field name="name">Reversión del deterioro de productos terminados y en curso de fabricación</field>
    </record>
    <record id="account_group_7931" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7931</field>
        <field name="name">Reversión del deterioro de mercaderías</field>
    </record>
    <record id="account_group_7932" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7932</field>
        <field name="name">Reversión del deterioro de materias primas</field>
    </record>
    <record id="account_group_7933" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7933</field>
        <field name="name">Reversión del deterioro de otros aprovisionamientos</field>
    </record>
    <record id="account_group_794" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">794</field>
        <field name="name">Reversión del deterioro de créditos por operaciones comerciales</field>
    </record>
    <record id="account_group_795" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">795</field>
        <field name="name">Exceso de provisiones</field>
    </record>
    <record id="account_group_7950" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7950</field>
        <field name="name">Exceso de provisión por retribuciones al personal</field>
    </record>
    <record id="account_group_7951" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7951</field>
        <field name="name">Exceso de provisión para impuestos</field>
    </record>
    <record id="account_group_7952" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7952</field>
        <field name="name">Exceso de provisión para otras responsabilidades</field>
    </record>
    <record id="account_group_7954" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7954</field>
        <field name="name">Exceso de provisión por operaciones comerciales</field>
    </record>
    <record id="account_group_79544" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">79544</field>
        <field name="name">Exceso de provisión por contratos onerosos</field>
    </record>
    <record id="account_group_79549" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">79549</field>
        <field name="name">Exceso de provisión para otras operaciones comerciales</field>
    </record>
    <record id="account_group_7955" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7955</field>
        <field name="name">Exceso de provisión para actuaciones medioambientales</field>
    </record>
    <record id="account_group_7956" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7956</field>
        <field name="name">Exceso de provisión para reestructuraciones</field>
    </record>
    <record id="account_group_7957" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7957</field>
        <field name="name">Exceso de provisión por transacciones con pagos basados en instrumentos de patrimonio</field>
    </record>
    <record id="account_group_796" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">796</field>
        <field name="name">Reversión del deterioro de participaciones y valores representativos de deuda a largo plazo</field>
    </record>
    <record id="account_group_7960" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7960</field>
        <field name="name">Reversión del deterioro de participaciones en instrumentos de patrimonio neto a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_7961" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7961</field>
        <field name="name">Reversión del deterioro de participaciones en instrumentos de patrimonio neto a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_7965" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7965</field>
        <field name="name">Reversión del deterioro de valores representativos de deuda a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_7966" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7966</field>
        <field name="name">Reversión del deterioro de valores representativos de deuda a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_7967" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7967</field>
        <field name="name">Reversión del deterioro de valores representativos de deuda a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_7968" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7968</field>
        <field name="name">Reversión del deterioro de valores representativos de deuda a largo plazo, otras empresas</field>
    </record>
    <record id="account_group_797" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">797</field>
        <field name="name">Reversión del deterioro de créditos a largo plazo</field>
    </record>
    <record id="account_group_7970" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7970</field>
        <field name="name">Reversión del deterioro de créditos a largo plazo, empresas del grupo</field>
    </record>
    <record id="account_group_7971" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7971</field>
        <field name="name">Reversión del deterioro de créditos a largo plazo, empresas asociadas</field>
    </record>
    <record id="account_group_7972" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7972</field>
        <field name="name">Reversión del deterioro de créditos a largo plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_7973" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7973</field>
        <field name="name">Reversión del deterioro de créditos a largo plazo, otras empresas</field>
    </record>
    <record id="account_group_798" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">798</field>
        <field name="name">Reversión del deterioro de participaciones y valores representativos de deuda a corto plazo</field>
    </record>
    <record id="account_group_7980" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7980</field>
        <field name="name">Reversión del deterioro de participaciones en instrumentos de patrimonio neto a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_7981" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7981</field>
        <field name="name">Reversión del deterioro de participaciones en instrumentos de patrimonio neto a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_7985" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7985</field>
        <field name="name">Reversión del deterioro en valores representativos de deuda a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_7986" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7986</field>
        <field name="name">Reversión del deterioro en valores representativos de deuda a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_7987" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7987</field>
        <field name="name">Reversión del deterioro en valores representativos de deuda a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_7988" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7988</field>
        <field name="name">Reversión del deterioro en valores representativos de deuda a corto plazo, otras empresas</field>
    </record>
    <record id="account_group_799" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">799</field>
        <field name="name">Reversión del deterioro de créditos a corto plazo</field>
    </record>
    <record id="account_group_7990" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7990</field>
        <field name="name">Reversión del deterioro de créditos a corto plazo, empresas del grupo</field>
    </record>
    <record id="account_group_7991" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7991</field>
        <field name="name">Reversión del deterioro de créditos a corto plazo, empresas asociadas</field>
    </record>
    <record id="account_group_7992" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7992</field>
        <field name="name">Reversión del deterioro de créditos a corto plazo, otras partes vinculadas</field>
    </record>
    <record id="account_group_7993" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">7993</field>
        <field name="name">Reversión del deterioro de créditos a corto plazo, otras empresas</field>
    </record>
    <record id="account_group_8" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">8</field>
        <field name="name">Gastos imputados al patrimonio neto</field>
    </record>
    <record id="account_group_80" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">80</field>
        <field name="name">Gastos financieros por valoración de activos y pasivos</field>
    </record>
    <record id="account_group_800" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">800</field>
        <field name="name">Pérdidas en activos financieros disponibles para la venta</field>
    </record>
    <record id="account_group_802" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">802</field>
        <field name="name">Transferencia de beneficios en activos financieros disponibles para la venta</field>
    </record>
    <record id="account_group_81" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">81</field>
        <field name="name">Gastos en operaciones de cobertura</field>
    </record>
    <record id="account_group_810" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">810</field>
        <field name="name">Pérdidas por coberturas de flujos de efectivo</field>
    </record>
    <record id="account_group_811" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">811</field>
        <field name="name">Pérdidas por coberturas de inversiones netas en un negocio en el extranjero</field>
    </record>
    <record id="account_group_812" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">812</field>
        <field name="name">Transferencia de beneficios por coberturas de flujos de efectivo</field>
    </record>
    <record id="account_group_813" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">813</field>
        <field name="name">Transferencia de beneficios por coberturas de inversiones netas en un negocio en el extranjero</field>
    </record>
    <record id="account_group_82" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">82</field>
        <field name="name">Gastos por diferencias de conversión</field>
    </record>
    <record id="account_group_820" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">820</field>
        <field name="name">Diferencias de conversión negativas</field>
    </record>
    <record id="account_group_821" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">821</field>
        <field name="name">Transferencia de diferencias de conversión positivas</field>
    </record>
    <record id="account_group_83" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">83</field>
        <field name="name">Impuesto sobre beneficios</field>
    </record>
    <record id="account_group_830" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">830</field>
        <field name="name">Impuesto sobre beneficios</field>
    </record>
    <record id="account_group_8300" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">8300</field>
        <field name="name">Impuesto corriente</field>
    </record>
    <record id="account_group_8301" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">8301</field>
        <field name="name">Impuesto diferido</field>
    </record>
    <record id="account_group_833" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">833</field>
        <field name="name">Ajustes negativos en la imposición sobre beneficios</field>
    </record>
    <record id="account_group_834" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">834</field>
        <field name="name">Ingresos fiscales por diferencias permanentes</field>
    </record>
    <record id="account_group_835" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">835</field>
        <field name="name">Ingresos fiscales por deducciones y bonificaciones</field>
    </record>
    <record id="account_group_836" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">836</field>
        <field name="name">Transferencia de diferencias permanentes</field>
    </record>
    <record id="account_group_837" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">837</field>
        <field name="name">Transferencia de deducciones y bonificaciones</field>
    </record>
    <record id="account_group_838" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">838</field>
        <field name="name">Ajustes positivos en la imposición sobre beneficios</field>
    </record>
    <record id="account_group_84" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">84</field>
        <field name="name">Transferencias de subvenciones, donaciones y legados</field>
    </record>
    <record id="account_group_840" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">840</field>
        <field name="name">Transferencia de subvenciones oficiales de capital</field>
    </record>
    <record id="account_group_841" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">841</field>
        <field name="name">Transferencia de donaciones y legados de capital</field>
    </record>
    <record id="account_group_842" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">842</field>
        <field name="name">Transferencia de otras subvenciones, donaciones y legados</field>
    </record>
    <record id="account_group_85" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">85</field>
        <field name="name">Gastos por pérdidas actuariales y ajustes en los activos por retribuciones a largo plazo de prestación definida</field>
    </record>
    <record id="account_group_850" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">850</field>
        <field name="name">Pérdidas actuariales</field>
    </record>
    <record id="account_group_851" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">851</field>
        <field name="name">Ajustes negativos en activos por retribuciones a largo plazo de prestación definida</field>
    </record>
    <record id="account_group_86" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">86</field>
        <field name="name">Gastos por activos no corrientes en venta</field>
    </record>
    <record id="account_group_860" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">860</field>
        <field name="name">Pérdidas en activos no corrientes y grupos enajenables de elementos mantenidos para la venta</field>
    </record>
    <record id="account_group_862" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">862</field>
        <field name="name">Transferencia de beneficios en activos no corrientes y grupos enajenables de elementos mantenidos para la venta</field>
    </record>
    <record id="account_group_89" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">89</field>
        <field name="name">Gastos de participaciones en empresas del grupo o asociadas con ajustes valorativos positivos previos</field>
    </record>
    <record id="account_group_891" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">891</field>
        <field name="name">Deterioro de participaciones en el patrimonio, empresas del grupo</field>
    </record>
    <record id="account_group_892" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">892</field>
        <field name="name">Deterioro de participaciones en el patrimonio, empresas asociadas</field>
    </record>
    <record id="account_group_9" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">9</field>
        <field name="name">Ingresos imputados al patrimonio neto</field>
    </record>
    <record id="account_group_90" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">90</field>
        <field name="name">Ingresos financieros por valoración de activosy pasivos</field>
    </record>
    <record id="account_group_900" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">900</field>
        <field name="name">Beneficios en activos financieros disponibles para la venta</field>
    </record>
    <record id="account_group_902" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">902</field>
        <field name="name">Transferencia de pérdidas de activos financieros disponibles para la venta</field>
    </record>
    <record id="account_group_91" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">91</field>
        <field name="name">Ingresos en operaciones de cobertura</field>
    </record>
    <record id="account_group_910" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">910</field>
        <field name="name">Beneficios por coberturas de flujos de efectivo</field>
    </record>
    <record id="account_group_911" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">911</field>
        <field name="name">Beneficios por coberturas de una inversión neta en un negocio en el extranjero</field>
    </record>
    <record id="account_group_912" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">912</field>
        <field name="name">Transferencia de pérdidas por coberturas de flujos de efectivo</field>
    </record>
    <record id="account_group_913" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">913</field>
        <field name="name">Transferencia de pérdidas por coberturas de una inversión neta en un negocio en el extranjero</field>
    </record>
    <record id="account_group_92" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">92</field>
        <field name="name">Ingresos por diferencias de conversión</field>
    </record>
    <record id="account_group_920" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">920</field>
        <field name="name">Diferencias de conversión positivas</field>
    </record>
    <record id="account_group_921" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">921</field>
        <field name="name">Transferencia de diferencias de conversión negativas</field>
    </record>
    <record id="account_group_94" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">94</field>
        <field name="name">Ingresos por subvenciones, donaciones y legados</field>
    </record>
    <record id="account_group_940" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">940</field>
        <field name="name">Ingresos de subvenciones oficiales de capital</field>
    </record>
    <record id="account_group_941" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">941</field>
        <field name="name">Ingresos de donaciones y legados de capital</field>
    </record>
    <record id="account_group_942" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">942</field>
        <field name="name">Ingresos de otras subvenciones, donaciones y legados</field>
    </record>
    <record id="account_group_95" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">95</field>
        <field name="name">Ingresos por ganancias actuariales y ajustes en los activos por retribuciones a largo plazo de prestación definida</field>
    </record>
    <record id="account_group_950" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">950</field>
        <field name="name">Ganancias actuariales</field>
    </record>
    <record id="account_group_951" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">951</field>
        <field name="name">Ajustes positivos en activos por retribuciones a largo plazo de prestación definida</field>
    </record>
    <record id="account_group_96" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">96</field>
        <field name="name">Ingresos por activos no corrientes en venta</field>
    </record>
    <record id="account_group_960" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">960</field>
        <field name="name">Beneficios en activos no corrientes y grupos enajenables de elementos mantenidos para la venta</field>
    </record>
    <record id="account_group_962" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">962</field>
        <field name="name">Transferencia de pérdidas en activos no corrientes y grupos enajenables de elementos mantenidos para la venta</field>
    </record>
    <record id="account_group_99" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">99</field>
        <field name="name">Ingresos de participaciones en empresas del grupo o asociadas con ajustes valorativos negativos previos</field>
    </record>
    <record id="account_group_991" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">991</field>
        <field name="name">Recuperación de ajustes valorativos negativos previos, empresas del grupo</field>
    </record>
    <record id="account_group_992" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">992</field>
        <field name="name">Recuperación de ajustes valorativos negativos previos, empresas asociadas</field>
    </record>
    <record id="account_group_993" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">993</field>
        <field name="name">Transferencia por deterioro de ajustes valorativos negativos previos, empresas del grupo</field>
    </record>
    <record id="account_group_994" model="account.group.template">
        <field name="chart_template_id" ref="account_chart_template_common"/>
        <field name="code_prefix_start">994</field>
        <field name="name">Transferencia por deterioro de ajustes valorativos negativos previos, empresas asociadas</field>
    </record>
    </data>
</odoo>
