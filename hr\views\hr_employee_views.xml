<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_employee_filter" model="ir.ui.view">
            <field name="name">hr.employee.search</field>
            <field name="model">hr.employee</field>
            <field name="arch" type="xml">
                <search string="Employees">
                    <field name="name" string="Employee" filter_domain="['|', ('work_email', 'ilike', self), ('name', 'ilike', self)]"/>
                    <field name="category_ids" groups="hr.group_hr_user"/>
                    <field name="job_id"/>
                    <separator/>
                    <filter string="Unread Messages" name="message_needaction" domain="[('message_needaction', '=', True)]"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter name="group_manager" string="Manager" domain="[]" context="{'group_by': 'parent_id'}"/>
                        <filter name="group_department" string="Department" domain="[]" context="{'group_by': 'department_id'}"/>
                        <filter name="group_job" string="Job" domain="[]" context="{'group_by': 'job_id'}"/>
                    </group>
                    <searchpanel>
                        <field name="company_id" groups="base.group_multi_company" icon="fa-building" enable_counters="1"/>
                        <field name="department_id" icon="fa-users" enable_counters="1"/>
                    </searchpanel>
                </search>
             </field>
        </record>

        <record id="view_employee_form" model="ir.ui.view">
            <field name="name">hr.employee.form</field>
            <field name="model">hr.employee</field>
            <field name="arch" type="xml">
                <form string="Employee" js_class="hr_employee_form" class='o_employee_form'>
                    <field name="active" invisible="1"/>
                    <field name="user_partner_id" invisible="1"/>
                    <field name="hr_presence_state" invisible="1"/>
                    <field name="image_128" invisible="1" />
                    <header>
                        <button name="%(plan_wizard_action)d" string="Launch Plan" type="action" groups="hr.group_hr_manager" attrs="{'invisible': [('active', '=', False)]}"/>
                    </header>
                    <sheet>
                        <div name="button_box" class="oe_button_box">
                            <button
                                id="hr_presence_button"
                                class="oe_stat_button"
                                disabled="1"
                                attrs="{'invisible': ['|', ('last_activity', '=', False), ('user_id', '=', False)]}">
                                <div role="img" class="fa fa-fw fa-circle text-success o_button_icon" attrs="{'invisible': [('hr_presence_state', '!=', 'present')]}" aria-label="Available" title="Available"/>
                                <div role="img" class="fa fa-fw fa-circle text-warning o_button_icon" attrs="{'invisible': [('hr_presence_state', '!=', 'to_define')]}" aria-label="Away" title="Away"/>
                                <div role="img" class="fa fa-fw fa-circle text-danger o_button_icon" attrs="{'invisible': [('hr_presence_state', '!=', 'absent')]}" aria-label="Not available" title="Not available"/>

                                <div class="o_stat_info" attrs="{'invisible': [('hr_presence_state', '=', 'present')]}">
                                    <span class="o_stat_text">
                                        Not Connected
                                    </span>
                                </div>
                                <div class="o_stat_info" attrs="{'invisible': [('hr_presence_state', '!=', 'present')]}">
                                    <span class="o_stat_value" attrs="{'invisible': [('last_activity_time', '=', False)]}">
                                        <field name="last_activity_time"/>
                                    </span>
                                    <span class="o_stat_value" attrs="{'invisible': [('last_activity_time', '!=', False)]}">
                                        <field name="last_activity"/>
                                    </span>
                                    <span class="o_stat_text">Present Since</span>
                                </div>
                            </button>
                        </div>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="avatar_128" invisible="1"/>
                        <field name="image_1920" widget='image' class="oe_avatar" options='{"zoom": true, "preview_image":"avatar_128"}'/>
                        <div class="oe_title">
                            <h1 class="d-flex">
                                <field name="name" placeholder="Employee's Name" required="True"/>
                                <a title="Chat" icon="fa-comments" href="#" class="ml8 o_employee_chat_btn" invisible="not context.get('chat_icon')" attrs="{'invisible': [('user_id','=', False)]}" role="button"><i class="fa fa-comments"/></a>
                            </h1>
                            <h2>
                                <field name="job_title" placeholder="Job Position" />
                            </h2>
                            <field name="category_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}" placeholder="Tags"  groups="hr.group_hr_manager"/>
                        </div>
                        <group>
                            <group>
                                <field name="mobile_phone" widget="phone"/>
                                <field name="work_phone" widget="phone"/>
                                <field name="work_email" widget="email"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                                <field name="company_country_id" invisible="1"/>
                                <field name="company_country_code" invisible="1"/>
                            </group>
                            <group>
                                <field name="department_id"/>
                                <field name="parent_id"/>
                                <field name="coach_id"/>
                            </group>
                        </group>
                        <notebook>
                            <page name="public" string="Work Information">
                                <div id="o_work_employee_container"> <!-- These two div are used to position org_chart -->
                                    <div id="o_work_employee_main">
                                        <group string="Location">
                                            <field name="address_id"
                                                context="{'show_address': 1}"
                                                options='{"always_reload": True, "highlight_first_line": True}'/>
                                            <field name="work_location_id" context="{'default_address_id': address_id}" />
                                        </group>
                                        <group name="managers" string="Approvers" class="hide-group-if-empty">
                                            <!-- is overridden in other hr related modules -->
                                        </group>
                                        <group name="departure" string="Departure" attrs="{'invisible': [('active', '=', True)]}">
                                            <field name="departure_reason_id" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                                            <field name="departure_description"/>
                                            <field name="departure_date"/>
                                        </group>
                                        <group string="Schedule">
                                            <field name="resource_calendar_id" required="1"/>
                                            <field name="id" invisible="1"/>
                                            <field name="tz" attrs="{'required': [('id', '!=', False)]}"/>
                                        </group>
                                    </div>
                                </div>
                            </page>
                            <page name="personal_information" string="Private Information" groups="hr.group_hr_user">
                                <group>
                                    <group string="Private Contact">
                                        <field name="address_home_id"
                                            context="{
                                                'show_address': 1,
                                                'default_type': 'private',
                                                'form_view_ref': 'base.res_partner_view_form_private'}"
                                            options='{"always_reload": True, "highlight_first_line": True}'/>
                                        <field name="private_email" string="Email"/>
                                        <field name="phone" class="o_force_ltr" groups="hr.group_hr_user" string="Phone" readonly="True"/>
                                        <field name="lang" string="Language"/>
                                        <field name="bank_account_id" context="{'default_partner_id': address_home_id}"/>
                                        <label for="km_home_work"/>
                                        <div class="o_row" name="div_km_home_work">
                                            <field name="km_home_work" groups="hr.group_hr_user"/>
                                            <span>Km</span>
                                        </div>

                                    </group>
                                    <group string="Citizenship">
                                        <field name="country_id" options='{"no_open": True, "no_create": True}'/>
                                        <field name="identification_id" groups="hr.group_hr_user"/>
                                        <field name="passport_id" groups="hr.group_hr_user"/>
                                        <field name="gender"/>
                                        <field name="birthday"/>
                                        <field name="place_of_birth" groups="hr.group_hr_user"/>
                                        <field name="country_of_birth" groups="hr.group_hr_user"/>
                                    </group>
                                    <group string="Marital Status">
                                        <field name="marital"/>
                                        <field name="spouse_complete_name" attrs="{'invisible': [('marital', 'not in', ['married', 'cohabitant'])]}" groups="hr.group_hr_user"/>
                                        <field name="spouse_birthdate" attrs="{'invisible': [('marital', 'not in', ['married', 'cohabitant'])]}" groups="hr.group_hr_user"/>
                                    </group>
                                    <group string="Dependant">
                                        <field name="children"/>
                                    </group>
                                    <group string="Emergency">
                                        <field name="emergency_contact"/>
                                        <field name="emergency_phone" class="o_force_ltr"/>
                                    </group>
                                    <group string="Work Permit" name="has_work_permit">
                                        <field name="visa_no"/>
                                        <field name="permit_no"/>
                                        <field name="visa_expire"/>
                                        <field name="work_permit_expiration_date"/>
                                        <field name="has_work_permit"/>
                                    </group>
                                    <group string="Education">
                                        <field name="certificate"/>
                                        <field name="study_field"/>
                                        <field name="study_school"/>
                                    </group>
                                </group>
                            </page>
                            <page name="hr_settings" string="HR Settings" groups="hr.group_hr_user">
                                <group>
                                    <group string='Status' name="active_group">
                                        <field name="employee_type"/>
                                        <field name="user_id" string="Related User" domain="[('share', '=', False)]"/>
                                    </group>
                                    <group string="Attendance/Point of Sale" name="identification_group">
                                        <field name="pin" string="PIN Code"/>
                                        <label for="barcode"/>
                                        <div class="o_row">
                                            <field name="barcode"/>
                                            <button string="Generate" class="btn btn-link" type="object" name="generate_random_barcode" attrs="{'invisible': [('barcode', '!=', False)]}"/>
                                            <button name="%(hr_employee_print_badge)d" string="Print Badge" class="btn btn-link" type="action" attrs="{'invisible': [('barcode', '=', False)]}"/>
                                        </div>
                                    </group>
                                    <group string='Payroll' name="payroll_group">
                                        <field name="job_id"/>
                                    </group>
                                    <group name="application_group"/>
                                </group>
                            </page>
                        </notebook>

                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="view_employee_tree" model="ir.ui.view">
            <field name="name">hr.employee.tree</field>
            <field name="model">hr.employee</field>
            <field name="arch" type="xml">
                <tree string="Employees" multi_edit="1" sample="1" js_class="hr_employee_list">
                    <field name="name" readonly="1"/>
                    <field name="work_phone" class="o_force_ltr" readonly="1"/>
                    <field name="work_email"/>
                    <field name="activity_ids" widget="list_activity"/>
                    <field name="activity_user_id" optional="hide" string="Activity by" widget="many2one_avatar_user"/>
                    <field name="activity_date_deadline" widget="remaining_days" options="{'allow_order': '1'}"/>
                    <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                    <field name="department_id"/>
                    <field name="job_id"/>
                    <field name="parent_id"/>
                    <field name="address_id" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="work_location_id" optional="hide"/>
                    <field name="coach_id" invisible="1"/>
                    <field name="active" invisible="1"/>
                    <field name="category_ids" widget="many2many_tags" options="{'color_field': 'color'}" optional="hide"/>
                </tree>
            </field>
        </record>

        <record id="hr_kanban_view_employees" model="ir.ui.view">
           <field name="name">hr.employee.kanban</field>
           <field name="model">hr.employee</field>
           <field name="priority">10</field>
           <field name="arch" type="xml">
               <kanban class="o_hr_employee_kanban" js_class="hr_employee_kanban" sample="1">
                   <field name="id"/>
                   <field name="hr_presence_state"/>
                   <field name="user_id"/>
                   <field name="user_partner_id"/>
                   <field name="hr_icon_display"/>
                   <field name="image_128" />
                   <templates>
                       <t t-name="kanban-box">
                       <div class="oe_kanban_global_click o_kanban_record_has_image_fill o_hr_kanban_record">
                           <field name="avatar_128" widget="image" class="o_kanban_image_fill_left" options="{'zoom': true, 'zoom_delay': 1000, 'background': true, 'preventClicks': false}"/>

                            <div class="oe_kanban_details">
                               <div class="o_kanban_record_top">
                                   <div class="o_kanban_record_headings">
                                       <strong class="o_kanban_record_title">
                                            <div class="float-right"
                                                 t-if="record.hr_icon_display.raw_value == 'presence_present'"
                                                 name="presence_present">
                                                <!-- Employee is present/connected and it is normal according to his work schedule  -->
                                                <span class="fa fa-circle text-success" role="img" aria-label="Present"
                                                      title="Present" name="presence_present">
                                                </span>
                                            </div>
                                            <div class="float-right"
                                                 t-if="record.hr_icon_display.raw_value == 'presence_absent'"
                                                 name="presence_absent">
                                                <!-- Employee is not present and it is normal according to his work schedule -->
                                                <span class="fa fa-circle-o text-muted" role="img" aria-label="Absent"
                                                      title="Absent" name="presence_absent">
                                                </span>
                                            </div>
                                            <div class="float-right"
                                                 t-if="record.hr_icon_display.raw_value == 'presence_absent_active'"
                                                    name="presence_absent_active">
                                                <!-- Employee is connected but according to his work schedule,
                                                     he should not work for now  -->
                                                <span class="fa fa-circle-o text-success" role="img"
                                                      aria-label="Present but not active"
                                                      title="Present but not active"
                                                      name="presence_absent_active">
                                                </span>
                                            </div>
                                            <div class="float-right"
                                                 t-if="record.hr_icon_display.raw_value == 'presence_to_define'"
                                                    name="presence_to_define">
                                                <!-- Employee is not here but according to his work schedule, he should be connected -->
                                                <span class="fa fa-circle text-warning" role="img"
                                                      aria-label="To define" title="To define"
                                                      name="presence_to_define">
                                                </span>
                                            </div>
                                            <field name="name" placeholder="Employee's Name"/>
                                       </strong>
                                       <span t-if="record.job_title.raw_value" class="o_kanban_record_subtitle"><field name="job_title"/></span>
                                   </div>
                               </div>
                               <field name="category_ids" widget="many2many_tags" options="{'color_field': 'color'}" groups="hr.group_hr_manager"/>
                               <ul>
                                   <li id="last_login"/>
                                   <li t-if="record.work_email.raw_value" class="o_text_overflow"><field name="work_email" /></li>
                                   <li t-if="record.work_phone.raw_value" class="o_force_ltr"><field name="work_phone" /></li>
                               </ul>
                           </div>
                           <div class="oe_kanban_content position-absolute fixed-bottom mr-2 o_hr_employee_kanban_bottom">
                               <div class="o_kanban_record_bottom">
                                   <div class="oe_kanban_bottom_left"/>
                                   <div class="oe_kanban_bottom_right float-right">
                                       <a title="Chat" icon="fa-comments" href="#" class="ml8 o_employee_chat_btn" attrs="{'invisible': [('user_id','=', False)]}" role="button"><i class="fa fa-comments"/></a>
                                       <div class="hr_activity_container">
                                           <field name="activity_ids" widget="kanban_activity"/>
                                       </div>
                                   </div>
                               </div>
                           </div>
                       </div>
                       </t>
                   </templates>
               </kanban>
            </field>
        </record>

        <record id="hr_employee_view_activity" model="ir.ui.view">
            <field name="name">hr.employee.activity</field>
            <field name="model">hr.employee</field>
            <field name="arch" type="xml">
                <activity string="Employees">
                    <field name="id"/>
                    <templates>
                        <div t-name="activity-box">
                            <img t-att-src="activity_image('hr.employee', 'avatar_128', record.id.raw_value)" role="img" t-att-title="record.id.value" t-att-alt="record.id.value"/>
                            <div>
                                <field name="name" display="full"/>
                                <field name="job_id" muted="1" display="full"/>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <record id="open_view_employee_list_my" model="ir.actions.act_window">
            <field name="name">Employees</field>
            <field name="res_model">hr.employee</field>
            <field name="view_mode">kanban,tree,form,activity</field>
            <field name="domain">[]</field>
            <field name="context">{'chat_icon': True}</field>
            <field name="view_id" eval="False"/>
            <field name="search_view_id" ref="view_employee_filter"/>
            <field name="help" type="html">
             <p class="o_view_nocontent_smiling_face">
               Add a new employee
             </p><p>
               With just a quick glance on the Odoo employee screen, you
               can easily find all the information you need for each person;
               contact data, job position, availability, etc.
             </p>
            </field>
        </record>

        <record id="open_view_employee_tree" model="ir.actions.act_window">
            <field name="name">Employees Structure</field>
            <field name="res_model">hr.employee</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_employee_tree"/>
            <field name="domain">[('parent_id','=',False)]</field>
            <field name="search_view_id" ref="view_employee_filter"/>
        </record>

        <record id="open_view_employee_list" model="ir.actions.act_window">
            <field name="name">Employees</field>
            <field name="res_model">hr.employee</field>
            <field name="view_mode">form,tree</field>
            <field name="view_id" eval="False"/>
            <field name="search_view_id" ref="view_employee_filter"/>
        </record>

        <!-- Employee tree by manager -->
        <record id="view_partner_tree2" model="ir.ui.view">
            <field name="name">hr.employee.tree</field>
            <field name="model">hr.employee</field>
            <field name="priority" eval="20"/>
            <field name="arch" type="xml">
                <tree string="Employees">
                    <field name="name"/>
                    <field name="work_phone" class="o_force_ltr"/>
                    <field name="work_email"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="department_id"/>
                    <field name="job_id"/>
                    <field name="coach_id" invisible="1"/>
                    <field name="parent_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="act_employee_from_department" model="ir.actions.act_window">
            <field name="name">Employees</field>
            <field name="res_model">hr.employee</field>
            <field name="view_mode">kanban,form,tree</field>
            <field name="search_view_id" ref="view_employee_filter"/>
            <field name="context">{
                "searchpanel_default_department_id": active_id,
                "default_department_id": active_id}
            </field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Add a new employee
              </p><p>
                With just a quick glance on the Odoo employee screen, you
                can easily find all the information you need for each person;
                contact data, job position, availability, etc.
              </p>
            </field>
        </record>

    </data>
</odoo>
