# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: mrp_account
#: code:addons/mrp_account/models/mrp_production.py:0
#: model:ir.model,name:mrp_account.model_account_analytic_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_id
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
#, python-format
msgid "Analytic Account"
msgstr "Аналітичний рахунок"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Рядок аналітики"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__analytic_account_id
msgid ""
"Analytic account in which cost and revenue entries will take        place "
"for financial management of the manufacturing order."
msgstr ""
"Аналітичний рахунок, у якому будуть здійснюватися записи витрат і доходів "
"для фінансового управління виробничим замовленням."

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_bom__analytic_account_id
msgid ""
"Analytic account in which cost and revenue entries will take place for "
"financial management of the manufacturing order."
msgstr ""
"Аналітичний рахунок, у якому будуть здійснюватися записи витрат і доходів "
"для фінансового управління виробничим замовленням."

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_bom
msgid "Bill of Material"
msgstr "Специфікація"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Bills of Materials"
msgstr "Специфікації"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr "Підрахунок специфікацій"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "Категорія"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "Розрахуйте ціну зі специфікації"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"Обчисліть ціну товару, використовуючи товари та операції відповідної "
"специфікації, тільки для виготовлених товарів."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid "Cost Recorded"
msgstr "Записи вартості"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Cost"
msgstr "Додаткова ціна"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__extra_cost
msgid "Extra cost per produced unit"
msgstr "Додаткова ціна на вироблену одиницю"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid ""
"Fill this only if you want automatic analytic accounting entries on "
"production orders."
msgstr ""
"Заповніть це, лише якщо ви хочете автоматичні аналітичні записи "
"бухгалтерського обліку для виробничих замовлень."

#. module: mrp_account
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "Замовлення на виробництво"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Manufacturing Orders"
msgstr "Замовлення на виробництво"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
msgid "Manufacturing Orders Count"
msgstr "Підрахунок замовлень на виробництво"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_id
msgid "Mo Analytic Account Line"
msgstr "Рядок аналітичного рахунку Mo"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product"
msgstr "Товар"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product Template"
msgstr "Шаблон товару"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
msgid "Production Order"
msgstr "Замовлення на виробництво"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "Показати оцінку"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "Складське переміщення "

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_rule
msgid "Stock Rule"
msgstr "Складське правило"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid ""
"Technical field automatically checked when a ongoing production posts "
"journal entries for its costs. This way, we can record one production's cost"
" multiple times and only consider new entries in the work centers time "
"lines."
msgstr ""
"Технічне поле автоматично перевіряється, коли поточне виробництво записує в "
"журнал для його витрат. Таким чином ми можемо зареєструвати вартість одного "
"виду продукції кілька разів і розглядати нові записи лише у тимчасових "
"робочих центрах."

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "Valuation"
msgstr "Оцінка"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_id
msgid "Wc Analytic Account Line"
msgstr "Рядок аналітичного рахунку Wc"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "Робочий центр"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr "Робоче замовлення"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr "Підрахунок робочих завдань"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#, python-format
msgid "Work Orders"
msgstr "Робочі замовлення"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Журнал продуктивності робочого центру"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenters"
msgstr "Робочі центри"

#. module: mrp_account
#: code:addons/mrp_account/models/stock_move.py:0
#, python-format
msgid "[Raw] %s"
msgstr "[Raw] %s"

#. module: mrp_account
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
#, python-format
msgid "[WC] %s"
msgstr "[WC] %s"
