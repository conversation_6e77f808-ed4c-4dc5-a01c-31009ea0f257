<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_analytic_account_view_form_purchase" model="ir.ui.view">
        <field name="name">account.analytic.account.form.purchase</field>
        <field name="model">account.analytic.account</field>
        <field name="inherit_id" ref="analytic.view_account_analytic_account_form"/>
        <field name="groups_id" eval="[(4, ref('purchase.group_purchase_user'))]"/>
        <field eval="10" name="priority"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button class="oe_stat_button" type="object" name="action_view_purchase_orders"
                    icon="fa-credit-card" attrs="{'invisible': [('purchase_order_count', '=', 0)]}">
                    <field string="Purchase Orders" name="purchase_order_count" widget="statinfo"/>
                </button>
            </div>
        </field>
    </record>

</odoo>
