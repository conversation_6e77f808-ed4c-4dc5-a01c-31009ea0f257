# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "アカウント情報"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "アカウントトークン"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Buy credits"
msgstr "クレジットを購入"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "会社"

#. module: iap
#: model:ir.model,name:iap.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
msgid "Created by"
msgstr "作成者"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
msgid "Created on"
msgstr "作成日"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
msgid "Display Name"
msgstr "表示名"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Documentation"
msgstr "ドキュメント"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "アプリ内課金"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "アプリ内課金アカウント"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "アプリ内課金アカウント"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "IAPリード強化API"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
msgid "ID"
msgstr "ID"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Insufficient Balance"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Insufficient credit to perform this service."
msgstr "このサービスを実行するにはクレジットが足りません。"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#: model:ir.actions.server,name:iap.open_iap_account
msgid "Open IAP Account"
msgstr "Open IAPアカウント"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
msgid "Service Name"
msgstr "サービス名"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Start a Trial at Odoo"
msgstr "Odooでトライアルを始める"

#. module: iap
#: code:addons/iap/tools/iap_tools.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app. The url it tried to contact was %s"
msgstr "このサービスが要求したURLがエラーを返しました。アプリの製作者に連絡して下さい。このサービスがコンタクトしようとしたURLは%s"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
#, python-format
msgid "View My Services"
msgstr "購入済みサービス一覧"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "IAPサービス一覧とクレジットを購入"
