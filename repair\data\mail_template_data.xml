<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="mail_template_repair_quotation" model="mail.template">
            <field name="name">Repair Quotation: Send by email</field>
            <field name="model_id" ref="repair.model_repair_order"/>
            <field name="subject">{{ object.partner_id.name }} Repair Orders (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_from">{{ (object.create_uid.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px;font-size: 13px;">
        Hello <t t-out="object.partner_id.name or ''"><PERSON></t>,<br/>
        Here is your repair order <strong t-out="object.name or ''">RO/00004</strong>
        <t t-if="object.invoice_method != 'none'">
            amounting in <strong><t t-out="format_amount(object.amount_total, object.pricelist_id.currency_id) or ''">$ 100.00</t>.</strong><br/>
        </t>
        <t t-else="">
            .<br/>
        </t>
        You can reply to this email if you have any questions.
        <br/><br/>
        Thank you,
        <t t-if="user.signature">
            <br />
            <t t-out="user.signature or ''">--<br/>Mitchell Admin</t>
        </t>
    </p>
</div></field>
            <field name="report_template" ref="action_report_repair_order"/>
            <field name="report_name">{{ (object.name or '').replace('/','_') }}</field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
