# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rating
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>l<PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> V. <<EMAIL>>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_count
msgid "# Ratings"
msgstr "# Vertinimų"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__access_token
msgid "Access token to set the rating of the value"
msgstr "Prieigos raktas įvertinimo reikšmei nustatyti"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__message_id
msgid ""
"Associated message when posting a review. Mainly used in website addons."
msgstr ""
"Susieta žinutė, rodoma skelbiant apžvalgą. Dažniausiai naudojama svetainės "
"įskiepiams."

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__partner_id
msgid "Author of the rating"
msgstr "Įvertinimo autorius"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__feedback
msgid "Comment"
msgstr "Komentaras"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Customer"
msgstr "Klientas"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Date"
msgstr "Data"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ko
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Dissatisfied"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Document"
msgstr "Dokumentas"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model
msgid "Document Model"
msgstr "Dokumento modelis"

#. module: rating
#: model:ir.model,name:rating.model_mail_thread
msgid "Email Thread"
msgstr "El. pašto diskusija"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__consumed
msgid "Enabled if the rating has been filled."
msgstr "Įjungta, jei įvertinimas yra užpildytas."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Feel free to write a feedback on your experience:"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__consumed
msgid "Filled Rating"
msgstr "Užpildytas įvertinimas"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Go back to the Homepage"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Group By"
msgstr "Grupuoti pagal"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__id
msgid "ID"
msgstr "ID"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_id
msgid "Identifier of the rated object"
msgstr "Įvertinto objekto identifikatorius"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image
msgid "Image"
msgstr "Paveikslėlis"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 30 days"
msgstr "Paskutinės 30 dienų"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 7 days"
msgstr "Paskutinės 7 dienos"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: rating
#: model:ir.model,name:rating.model_mail_message
#: model:ir.model.fields,field_description:rating.field_rating_rating__message_id
msgid "Message"
msgstr "Žinutė"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_model_id
msgid "Model of the followed resource"
msgstr "Prenumeruojamų išteklių modelis"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "My Ratings"
msgstr "Mano vertinimai"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_name
msgid "Name"
msgstr "Pavadinimas"

#. module: rating
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__none
msgid "No Rating yet"
msgstr "Įvertinimų dar nėra"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "No rating yet"
msgstr "Įvertinimų dar nėra"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ok
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Okay"
msgstr "Gerai"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rated_partner_id
msgid "Owner of the rated resource"
msgstr "Įvertinto objekto savininkas"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_id
msgid "Parent Document"
msgstr "Tėvinis dokumentas"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model
msgid "Parent Document Model"
msgstr "Tėvinio dokumento modelis"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_name
msgid "Parent Document Name"
msgstr "Tėvinio dokumento pavadinimas"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Parent Holder"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_ref
msgid "Parent Ref"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model_id
msgid "Parent Related Document Model"
msgstr "Susijusio tėvinio dokumento modelis"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rated Operator"
msgstr ""

#. module: rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rating"
msgstr "Įvertinimas"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg
msgid "Rating Average"
msgstr "Įvertinimo vidurkis"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Paskutinio įvertinimo atsiliepimas "

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_image
msgid "Rating Last Image"
msgstr "Paskutinio įvertinimo paveikslėlis"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_value
msgid "Rating Last Value"
msgstr "Paskutinio įvertinimo reikšmė"

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr "Įvertinimo Mixin"

#. module: rating
#: model:ir.model,name:rating.model_rating_parent_mixin
msgid "Rating Parent Mixin"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_value
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_value
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating
msgid "Rating Value"
msgstr "Įvertinimo reikšmė"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
msgid "Rating Value (/5)"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_count
msgid "Rating count"
msgstr "Įvertinimų kiekis"

#. module: rating
#: model:ir.model.constraint,message:rating.constraint_rating_rating_rating_range
msgid "Rating should be between 0 and 5"
msgstr ""

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rating
msgid "Rating value: 0=Unhappy, 5=Happy"
msgstr ""

#. module: rating
#: model:ir.actions.act_window,name:rating.rating_rating_view
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_ids
#: model:ir.ui.menu,name:rating.rating_rating_menu_technical
#: model_terms:ir.ui.view,arch_db:rating.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_tree
msgid "Ratings"
msgstr "Įvertinimai"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__rating_last_feedback
#: model:ir.model.fields,help:rating.field_rating_rating__feedback
msgid "Reason of the rating"
msgstr "Įvertinimo priežastis"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model_id
msgid "Related Document Model"
msgstr "Susijusio dokumento modelis"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_ids
msgid "Related ratings"
msgstr "Susiję vertinimai"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Resource"
msgstr "Išteklius"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__resource_ref
msgid "Resource Ref"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_name
msgid "Resource name"
msgstr "Ištekliaus pavadinimas"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__top
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Satisfied"
msgstr "Patenkintas"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__access_token
msgid "Security Token"
msgstr "Apsaugos prieigos raktas"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Send Feedback"
msgstr "Siųsti atsiliepimą"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_date
msgid "Submitted on"
msgstr "Pateikta"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Thank you for rating our services!"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thank you, we appreciate your feedback!"
msgstr ""

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_name
msgid "The name of the rated resource."
msgstr "Įvertinto objekto pavadinimas."

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "There is no rating for this object at the moment."
msgstr "Šis objektas šiuo metu neturi įvertinimo."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Today"
msgstr "Šiandien"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__is_internal
msgid "Visible Internally Only"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "by"
msgstr "pagal"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "for"
msgstr "skirtas"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "on"
msgstr "esantis"
