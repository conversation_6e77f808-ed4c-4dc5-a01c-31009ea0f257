# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_il
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-02 14:52+0000\n"
"PO-Revision-Date: 2022-11-02 14:52+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_il
#: model:account.tax.template,description:l10n_il.il_vat_pa_purchase_16
msgid "16%"
msgstr ""

#. module: l10n_il
#: model:account.tax.template,description:l10n_il.il_vat_inputs_17
#: model:account.tax.template,description:l10n_il.il_vat_inputs_1_4_17
#: model:account.tax.template,description:l10n_il.il_vat_inputs_2_3_17
#: model:account.tax.template,description:l10n_il.il_vat_inputs_fa_17
#: model:account.tax.template,description:l10n_il.il_vat_pa_sales_17
#: model:account.tax.template,description:l10n_il.il_vat_sales_17
#: model:account.tax.template,description:l10n_il.il_vat_self_inv_purchase
msgid "17%"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111100
msgid "Account Payable"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101200
msgid "Account Receivable"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101201
msgid "Account Receivable(POS)"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111650
msgid "Advances"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101250
msgid "Allowance for credit losses"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_220800
msgid "Bad debts expense"
msgstr ""

#. module: l10n_il
#: model:account.group.template,name:l10n_il.il_group_101401
msgid "Bank And Cash"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101410
msgid "Bank Deposit"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_212300
msgid "Bank Fees"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_300100
msgid "Capital"
msgstr ""

#. module: l10n_il
#: model:account.group.template,name:l10n_il.il_group_300000
msgid "Capital And Shares"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101420
msgid "Cheques"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_220200
msgid "Communication expenses"
msgstr ""

#. module: l10n_il
#: model:account.group.template,name:l10n_il.il_group_201000
msgid "Cost of Goods"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_201000
msgid "Cost of Goods Sold"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_211000
msgid "Cost of Services Sold"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111400
msgid "Credit cards"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_220500
msgid "Credit provision expenses"
msgstr ""

#. module: l10n_il
#: model:account.group.template,name:l10n_il.il_group_101110
msgid "Current Assets"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111000
#: model:account.group.template,name:l10n_il.il_group_111000
msgid "Current Liabilities"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_212000
msgid "Customer discounts"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_213000
msgid "Customer returns"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_112210
msgid "Deferred Revenue"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111730
msgid "Deferred Taxes"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_220400
msgid "Depretiation expenses"
msgstr ""

#. module: l10n_il
#: model:account.account.tag,name:l10n_il.account_tag_dividend_account
msgid "Dividend Account"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_202300
msgid "Donations"
msgstr ""

#. module: l10n_il
#: model:account.fiscal.position.template,name:l10n_il.account_fiscal_position_eilat
msgid "Eilat"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111550
msgid "Employees Benefits"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111500
msgid "Employees wages"
msgstr ""

#. module: l10n_il
#: model:account.group.template,name:l10n_il.il_group_202000
msgid "Expenses"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101430
msgid "Financial Assets"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_202400
msgid "Fines"
msgstr ""

#. module: l10n_il
#: model:account.group.template,name:l10n_il.il_group_100100
msgid "Fixed Assets"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_202100
msgid "Foreign Exchange Gain&Loss"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_220000
msgid "G&A Expenses"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_216000
msgid "Good's Shrinkage"
msgstr ""

#. module: l10n_il
#: model:account.fiscal.position.template,name:l10n_il.account_fiscal_position_import_export
msgid "Import / Export"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111700
msgid "Income Tax"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111230
msgid "Income tax withheld - customers"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111220
msgid "Income tax withheld - dividends"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111210
msgid "Income tax withheld - employees"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111200
msgid "Income tax withheld - vendors"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_100420
msgid "Intangible Assets"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_202200
msgid "Interest Expenses"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_200300
msgid "Interest Income"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111460
msgid "Interest Payable"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101160
msgid "Inventory - Finished goods"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101140
msgid "Inventory - Raw materials"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101150
msgid "Inventory - Work in progress"
msgstr ""

#. module: l10n_il
#: model:account.fiscal.position.template,name:l10n_il.account_fiscal_position_israel
msgid "Israel"
msgstr ""

#. module: l10n_il
#: model:account.chart.template,name:l10n_il.il_chart_template
msgid "Israel - Chart of Accounts"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_100100
msgid "Land and buildings"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_100110
msgid "Land and buildings depreciation"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_chart_template_liquidity_transfer
msgid "Liquidity Transfer"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_112100
msgid "Long Term loans"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_217000
msgid "Lost goods"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_100200
msgid "Machinery and equipment"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_100210
msgid "Machinery and equipment depreciation"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111710
msgid "National Insurance"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_112000
#: model:account.group.template,name:l10n_il.il_group_112000
msgid "Non-current Liabilities"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_300110
msgid "Ordinary Shares"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_202000
msgid "Other Expenses"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_200200
#: model:account.group.template,name:l10n_il.il_group_200200
msgid "Other Income"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111660
msgid "Other Payable"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_220700
msgid "Other non-operating expenses"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_100400
msgid "Other property"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_100410
msgid "Other property depreciation"
msgstr ""

#. module: l10n_il
#: model:account.fiscal.position.template,name:l10n_il.account_fiscal_position_palestinian_authority
msgid "Palestinian Authority (PA)"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101440
msgid "Petty Cash"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_300120
msgid "Preferred Shares"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101350
msgid "Prepayments"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_200000
msgid "Product Sales"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_112150
msgid "Provisions"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_212200
msgid "Purchase of Equipments"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_220100
msgid "Rent Expenses"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_300300
msgid "Reserve and Profit/Loss Account"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_212100
msgid "Salary Expenses"
msgstr ""

#. module: l10n_il
#: model:account.group.template,name:l10n_il.il_group_200000
msgid "Sales Income"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_220600
msgid "Sales and marketing expenses"
msgstr ""

#. module: l10n_il
#: model:account.fiscal.position.template,name:l10n_il.account_fiscal_position_self_invoice
#: model:account.tax.template,name:l10n_il.il_vat_self_inv_purchase
msgid "Self Invoice"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_200100
msgid "Services Sales"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_300130
msgid "Shares Premium"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111450
msgid "Short term loans"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101130
msgid "Stock Interim Account (Delivered)"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101120
msgid "Stock Interim Account (Received)"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101110
msgid "Stock Valuation account"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_220300
msgid "Transportation expenses"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101320
msgid "VAT - Fixed Assets"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101330
msgid "VAT - Import Transactions"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101310
msgid "VAT - Inputs"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101840
msgid "VAT - Inputs import line"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_101340
msgid "VAT - PA Import Transactions"
msgstr ""

#. module: l10n_il
#: model:account.tax.group,name:l10n_il.tax_group_vat_16
msgid "VAT 16%"
msgstr ""

#. module: l10n_il
#: model:account.tax.template,name:l10n_il.il_vat_pa_purchase_16
msgid "VAT 16% (PA)"
msgstr ""

#. module: l10n_il
#: model:account.tax.group,name:l10n_il.tax_group_vat_17
msgid "VAT 17%"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_vat_due
msgid "VAT DUE"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_out_base_exempt
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_out_base_exempt_title
#: model:account.tax.report.line,tag_name:l10n_il.account_tax_report_line_out_base_exempt
msgid "VAT Exempt Sales (BASE)"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_in_balance
msgid "VAT INPUTS (TAX)"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_vat_in_fa
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_vat_in_fa_title
#: model:account.tax.report.line,tag_name:l10n_il.account_tax_report_line_vat_in_fa
msgid "VAT INPUTS (fixed assets)"
msgstr ""

#. module: l10n_il
#: model:account.tax.template,description:l10n_il.il_vat_only_purchase
#: model:account.tax.template,name:l10n_il.il_vat_only_purchase
msgid "VAT Import Line"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_in_balance_1_4
#: model:account.tax.report.line,tag_name:l10n_il.account_tax_report_line_in_balance_1_4
#: model:account.tax.template,name:l10n_il.il_vat_inputs_1_4_17
msgid "VAT Inputs 1/4"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_in_balance_17
#: model:account.tax.report.line,tag_name:l10n_il.account_tax_report_line_in_balance_17
msgid "VAT Inputs 17%"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_in_balance_2_3
#: model:account.tax.report.line,tag_name:l10n_il.account_tax_report_line_in_balance_2_3
#: model:account.tax.template,name:l10n_il.il_vat_inputs_2_3_17
msgid "VAT Inputs 2/3"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_in_balance_pa_16
#: model:account.tax.report.line,tag_name:l10n_il.account_tax_report_line_in_balance_pa_16
msgid "VAT Inputs PA 16%"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111120
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_out_balance_pa
#: model:account.tax.report.line,tag_name:l10n_il.account_tax_report_line_out_balance_pa
#: model:account.tax.template,name:l10n_il.il_vat_pa_sales_17
msgid "VAT PA Sales"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_out_base
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_out_base_title
#: model:account.tax.report.line,tag_name:l10n_il.account_tax_report_line_out_base
msgid "VAT SALES (BASE)"
msgstr ""

#. module: l10n_il
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_vat_sales_tax
msgid "VAT SALES (TAX)"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111110
#: model:account.tax.report.line,name:l10n_il.account_tax_report_line_out_balance
#: model:account.tax.report.line,tag_name:l10n_il.account_tax_report_line_out_balance
#: model:account.tax.template,name:l10n_il.il_vat_sales_17
msgid "VAT Sales"
msgstr ""

#. module: l10n_il
#: model:account.tax.template,name:l10n_il.il_vat_purchase_zero
#: model:account.tax.template,name:l10n_il.il_vat_sales_zero
msgid "VAT Zero"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_111720
msgid "VAT due"
msgstr ""

#. module: l10n_il
#: model:account.tax.group,name:l10n_il.tax_group_vat_exempt_purchase
#: model:account.tax.template,name:l10n_il.il_vat_purchase_exempt
msgid "VAT exempt purchase"
msgstr ""

#. module: l10n_il
#: model:account.tax.group,name:l10n_il.tax_group_vat_exempt
msgid "VAT exempt sale"
msgstr ""

#. module: l10n_il
#: model:account.tax.template,name:l10n_il.il_vat_sales_exempt
msgid "VAT exempt sales"
msgstr ""

#. module: l10n_il
#: model:account.tax.template,name:l10n_il.il_vat_inputs_17
msgid "VAT inputs"
msgstr ""

#. module: l10n_il
#: model:account.tax.template,name:l10n_il.il_vat_inputs_fa_17
msgid "VAT inputs for fixed assets"
msgstr ""

#. module: l10n_il
#: model:account.fiscal.position.template,name:l10n_il.account_fiscal_position_vat_zero
msgid "Vat Zero"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_100300
msgid "Vehicles"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_100310
msgid "Vehicles depreciation"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_215000
msgid "Vendor discounts"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_214000
msgid "Vendor returns"
msgstr ""

#. module: l10n_il
#: model:account.account.tag,name:l10n_il.account_tag_retention_tax_customers_account
msgid "Withholding Customers Tax Account"
msgstr ""

#. module: l10n_il
#: model:account.account.tag,name:l10n_il.account_tag_retention_tax_dividend_account
msgid "Withholding Dividend Tax Account"
msgstr ""

#. module: l10n_il
#: model:account.account.tag,name:l10n_il.account_tag_retention_tax_employees_account
msgid "Withholding Employees Tax Account"
msgstr ""

#. module: l10n_il
#: model:account.account.tag,name:l10n_il.account_tag_retention_tax_vendor_account
msgid "Withholding Vendor Tax Account"
msgstr ""

#. module: l10n_il
#: model:account.account.template,name:l10n_il.il_account_300270
msgid "current year earnings"
msgstr ""
