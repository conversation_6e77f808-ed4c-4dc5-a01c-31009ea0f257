# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_coupon
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#, python-format
msgid "A coupon is needed for coupon programs."
msgstr ""

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.layout
msgid "Could not apply the promo code:"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model,name:website_sale_coupon.model_coupon_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__coupon_id
msgid "Coupon"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model,name:website_sale_coupon.model_coupon_program
msgid "Coupon Program"
msgstr ""

#. module: website_sale_coupon
#: model:ir.ui.menu,name:website_sale_coupon.menu_coupon_type_config
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.res_config_settings_view_form
msgid "Coupon Programs"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model,name:website_sale_coupon.model_coupon_share
msgid "Create links that apply a coupon and redirect to a specific page"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.cart_discount
msgid "Discount:"
msgstr "Atlaide:"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.cart_discount
msgid "Discounted amount"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__display_name
msgid "Display Name"
msgstr "Attēlotais nosaukums"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_share_view_form
msgid "Done"
msgstr "Gatavs"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_share_view_form
msgid "Generate Short Link"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__id
msgid "ID"
msgstr "ID"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_result
msgid "Invalid or expired promo code."
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz modificēts"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__write_uid
msgid "Last Updated by"
msgstr "Pēdējoreiz atjaunoja"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__write_date
msgid "Last Updated on"
msgstr "Pēdējoreiz atjaunots"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__program_id
msgid "Program"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__program_website_id
msgid "Program Website"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__promo_code
msgid "Promo Code"
msgstr ""

#. module: website_sale_coupon
#: model:ir.ui.menu,name:website_sale_coupon.menu_promotion_type_config
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.res_config_settings_view_form
msgid "Promotion Programs"
msgstr ""

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#, python-format
msgid "Provide either a coupon or a program."
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__redirect
msgid "Redirect"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,help:website_sale_coupon.field_coupon_program__website_id
#: model:ir.model.fields,help:website_sale_coupon.field_coupon_share__program_website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: website_sale_coupon
#: model:ir.model,name:website_sale_coupon.model_sale_order
msgid "Sales Order"
msgstr "Pasūtījums"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_view_tree
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_program_view_tree_website
msgid "Share"
msgstr "Dalīties"

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_share_view_form
#, python-format
msgid "Share Coupon"
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__share_link
msgid "Share Link"
msgstr ""

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/controllers/main.py:0
#, python-format
msgid ""
"The coupon will be automatically applied when you add something in your "
"cart."
msgstr ""

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.layout
msgid "The following promo code was applied on your order:"
msgstr ""

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/models/sale_coupon_program.py:0
#, python-format
msgid "The program code must be unique by website!"
msgstr ""

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#, python-format
msgid "The shared website should correspond to the website of the program."
msgstr ""

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_program__website_id
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__website_id
msgid "Website"
msgstr "Tīkla vietne"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_share_view_form
msgid ""
"You can share this promotion with your customers.\n"
"                            It will be applied at checkout when the customer uses this link."
msgstr ""

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_result
msgid "You have successfully applied following promo code:"
msgstr ""

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_result
msgid "Your reward"
msgstr ""

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_result
msgid "is available on a next order with this promo code:"
msgstr ""
