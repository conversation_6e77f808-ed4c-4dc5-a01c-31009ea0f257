# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_matrix
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2021\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_template__product_add_mode
msgid "Add product mode"
msgstr "הוסף מצב מוצר"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,help:sale_product_matrix.field_product_template__product_add_mode
msgid ""
"Configurator: choose attribute values to add the matching         product variant to the order.\n"
"Grid: add several variants at once from the grid of attribute values"
msgstr ""
"מגדיר: בחר ערכי תכונות כדי להוסיף את וריאנט המוצר התואם להזמנה.\n"
"רשת: הוסף מספר וריאנטים בבת אחת מרשת הערכים של התכונות"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "רשת תבנית מוצר"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_update
msgid "Grid Update"
msgstr "עדכן רשת"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__report_grids
msgid ""
"If set, the matrix of the products configurable by matrix will be shown on "
"the report of the order."
msgstr ""
"אם מוגדר, המטריצה ​​של המוצרים הניתנים להגדרה על ידי מטריצה תוצג בדוח "
"ההזמנה."

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid
msgid "Matrix local storage"
msgstr "אחסון מקומי של מטריצה"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__matrix
msgid "Order Grid Entry"
msgstr "רשומת רשת הזמנות"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__report_grids
msgid "Print Variant Grids"
msgstr "הדפס תצוגות רשת של וריאנטים"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__configurator
msgid "Product Configurator"
msgstr "מגדיר מוצר"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_product_template
msgid "Product Template"
msgstr "תבנית מוצר "

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_sale_order
msgid "Sales Order"
msgstr "הזמנת לקוח"

#. module: sale_product_matrix
#: model_terms:ir.ui.view,arch_db:sale_product_matrix.product_template_grid_view_form
msgid "Sales Variant Selection"
msgstr "בחירת וריאנט מכירות"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr "שדה טכני לפונקציונליות של product_matrix."

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid
msgid ""
"Technical local storage of grid. \n"
"If grid_update, will be loaded on the SO. \n"
"If not, represents the matrix to open."
msgstr ""
"אחסון טכני מקומי לרשת. \n"
"אם grid_update, אז ייטען בהזמנת לקוח. \n"
"אם לא, מייצג את המטריצה ​​לפתיחה."

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr "בין אם שדה הרשת מכיל מטריצה ​​חדשה ליישום או לא."

#. module: sale_product_matrix
#: code:addons/sale_product_matrix/models/sale_order.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple sale lines."
msgstr "אינך יכול לשנות את כמות המוצר הקיימת במספר שורות מכירה."
