<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="l10n_in_port_code_form_view" model="ir.ui.view">
        <field name="name">l10n_in.port.code.form</field>
        <field name="model">l10n_in.port.code</field>
        <field name="arch" type="xml">
            <form string="India Port Code">
                 <group>
                    <group>
                        <field name="name"/>
                        <field name="code"/>
                    </group>
                    <group>
                        <field name="state_id"/>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <record id="l10n_in_port_code_tree_view" model="ir.ui.view">
        <field name="name">l10n_in.port.code.tree</field>
        <field name="model">l10n_in.port.code</field>
        <field name="arch" type="xml">
            <tree string="India Port Code">
                <field name="name"/>
                <field name="code"/>
                <field name="state_id"/>
            </tree>
        </field>
    </record>

    <record id="l10n_in_port_code_search_view" model="ir.ui.view">
        <field name="name">l10n_in.port.code.search</field>
        <field name="model">l10n_in.port.code</field>
        <field name="arch" type="xml">
            <search string="India Port Code">
                <field name="name" string="Port" filter_domain="['|',('name', 'ilike', self),('code', 'ilike', self)]"/>
                <field name="state_id"/>
                <group expand="0" string="Group By">
                    <filter string="State" name="state" domain="[]" context="{'group_by': 'state_id'}"/>
                </group>
            </search>
        </field>
    </record>

</odoo>
