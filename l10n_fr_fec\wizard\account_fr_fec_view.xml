<?xml version="1.0" encoding="utf-8"?>
<odoo>
<record id="account_fr_fec_view" model="ir.ui.view">
    <field name="name">account.fr.fec.form.view</field>
    <field name="model">account.fr.fec</field>
    <field name="arch" type="xml">
        <form string="FEC File Generation">
            <div class="alert alert-info" role="alert" attrs="{'invisible': [('test_file', '=', True)]}">
                When you download a FEC file, the lock date is set to the end date.
                If you want to test the FEC file generation, please tick the test file checkbox.
            </div>
            <div class="alert alert-info" role="alert" attrs="{'invisible': [('test_file', '=', False)]}">
                You are in test mode. The FEC file generation will not set the lock date.
            </div>
            <notebook>
                <page string="Options" name="options">
                    <group>
                        <field name="date_from"/>
                        <field name="date_to"/>
                        <field name="test_file"/>
                        <field name="export_type" attrs="{'invisible': [('test_file', '=', False)]}"/>
                    </group>
                </page>
                <page string="Technical Info" name="technical_info">
                    <group>
                        <div>
                        The encoding of this text file is UTF-8. The structure of file is CSV separated by pipe '|'.
                        </div>
                    </group>
                    <group>
                        <table style="width:80%">
                            <tr>
                                <th>Technical Name</th>
                                <th>Column</th>
                                <th>Comment</th>
                            </tr>
                            <tr>
                                <td>JournalCode</td>
                                <td># 0</td>
                            </tr>
                            <tr>
                                <td>JournalLib</td>
                                <td>
                                    # 1</td>
                            </tr>
                            <tr>
                                <td>EcritureNum</td>
                                <td># 2</td>
                            </tr>
                            <tr>
                                <td>EcritureDate</td>
                                <td>
                                    # 3</td>
                            </tr>
                            <tr>
                                <td>CompteNum</td>
                                <td># 4</td>
                            </tr>
                            <tr>
                                <td>CompteLib</td>
                                <td># 5</td>
                            </tr>
                            <tr>
                                <td>CompAuxNum</td>
                                <td># 6</td>
                                <td>We use partner.id</td>
                            </tr>
                            <tr>
                                <td>CompAuxLib</td>
                                <td># 7</td>
                            </tr>
                            <tr>
                                <td>PieceRef</td>
                                <td># 8</td>
                            </tr>
                            <tr>
                                <td>PieceDate</td>
                                <td># 9</td>
                            </tr>
                            <tr>
                                <td>EcritureLib</td>
                                <td># 10</td>
                            </tr>
                            <tr>
                                <td>Debit</td>
                                <td># 11</td>
                            </tr>
                            <tr>
                                <td>Credit</td>
                                <td># 12</td>
                            </tr>
                            <tr>
                                <td>EcritureLet</td>
                                <td># 13</td>
                            </tr>
                            <tr>
                                <td>DateLet</td>
                                <td># 14</td>
                            </tr>
                            <tr>
                                <td>ValidDate</td>
                                <td># 15</td>
                            </tr>
                            <tr>
                                <td>Montantdevise</td>
                                <td># 16</td>
                            </tr>
                            <tr>
                                <td>Idevise</td>
                                <td># 17</td>
                            </tr>
                        </table>
                    </group>
                </page>
            </notebook>
            <footer>
                <button string="Generate" name="generate_fec" type="object"
                    class="oe_highlight"/>
                <button string="Cancel" class="btn btn-secondary" special="cancel" data-hotkey="z"/>
            </footer>
        </form>
    </field>
</record>

<record id="account_fr_fec_action" model="ir.actions.act_window">
    <field name="name">FEC</field>
    <field name="res_model">account.fr.fec</field>
    <field name="view_mode">form</field>
    <field name="target">new</field>
</record>

<menuitem id="account_fr_fec_menu"
        parent="l10n_fr.account_reports_fr_statements_menu"
        action="account_fr_fec_action"
        sequence="100" />
</odoo>
