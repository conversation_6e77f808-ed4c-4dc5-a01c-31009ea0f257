# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>eli Õigus <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# JanaAvalah, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: JanaAvalah, 2023\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_form
msgid "<span class=\"o_vat_label\">VAT</span>"
msgstr ""

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Ettevõtted"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: base_vat
#: model:ir.model,name:base_vat.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Finantspositsioon"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, you will not be able to save a contact if its "
"VAT number cannot be verified by the European VIES service."
msgstr ""
"Kui selles ruudus on linnukene, siis sa ei saa salvestada kontakti juhul kui"
" tema käibemaksukohustuslase numbrit ei saa kinnitada Euroopa "
"käibemaksualase teabe vahetamise süsteem VIES."

#. module: base_vat
#: model:ir.model.fields,help:base_vat.field_res_partner__vat
#: model:ir.model.fields,help:base_vat.field_res_users__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""
"Maksu ID-kood. Täitke see, kui kontaktile kohaldatakse riiklikke makse. "
"Kasutatakse mõnedes juriidilistes avaldustes."

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"%(vat_label)s number [%(wrong_vat)s] jaoks %(record_label)s ei tundu olevat kehtiv.\n"
"Märkus: eeldatav vorming on %(expected_format)s"

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s either failed "
"the VIES VAT validation check or did not respect the expected format "
"%(expected_format)s."
msgstr ""
"%(vat_label)s number [%(wrong_vat)s] jaoks %(record_label)s ei läbinud "
"VIES-i käibemaksu kinnitamise kontrolli või ei olnud õiges vormingus "
"%(expected_format)s."

#. module: base_vat
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid ""
"The country detected for this foreign VAT number does not match the one set "
"on this fiscal position."
msgstr ""
"Selle välisriigi käibemaksukohustuslase numbri jaoks tuvastatud riik ei ühti"
" sellele positsioonile määratud riigiga."

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.company_form_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_form
msgid "VAT"
msgstr "KMKR nr"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vat
#: model:ir.model.fields,field_description:base_vat.field_res_users__vat
msgid "VAT/Tax ID"
msgstr ""

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "Kinnitage KM number"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "Kinnitage KM number, kasutades Euroopa VIES teenust"

#. module: base_vat
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid "fiscal position [%s]"
msgstr "finantspositsioon [%s]"

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "partner [%s]"
msgstr "partner [%s]"
