# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_timesheet_attendance
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Basque (https://www.transifex.com/odoo/teams/41243/eu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: eu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report_date
msgid "Date"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report_display_name
msgid "Display Name"
msgstr "Izena erakutsi"

#. module: hr_timesheet_attendance
#: model:ir.actions.act_window,name:hr_timesheet_attendance.action_hr_timesheet_attendance_report
msgid "HR Timesheet/Attendance Report"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report_id
msgid "ID"
msgstr "ID"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report___last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_timesheet_attendance
#: code:addons/hr_timesheet_attendance/models/hr_timesheet_sheet.py:76
#, python-format
msgid ""
"Please verify that the total difference of the sheet is lower than %.2f."
msgstr ""

#. module: hr_timesheet_attendance
#: code:addons/hr_timesheet_attendance/models/hr_timesheet_sheet.py:85
#, python-format
msgid ""
"The timesheet cannot be validated as it contains an attendance record with "
"no Check Out."
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "This Month"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "This Week"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.ui.menu,name:hr_timesheet_attendance.menu_hr_timesheet_attendance_report
msgid "Timesheet / Attendance"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report_total_attendance
msgid "Total Attendance"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report_total_difference
msgid "Total Difference"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report_total_timesheet
msgid "Total Timesheet"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report_user_id
msgid "User"
msgstr ""

#. module: hr_timesheet_attendance
#: code:addons/hr_timesheet_attendance/models/hr_timesheet_sheet.py:53
#, python-format
msgid "You cannot delete a timesheet that has attendance entries."
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model,name:hr_timesheet_attendance.model_hr_timesheet_attendance_report
msgid "hr.timesheet.attendance.report"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "timesheet attendance"
msgstr ""
