# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sms
# 
# Translators:
# Андрей <PERSON>ев <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON> <yeli<PERSON><PERSON><EMAIL>>, 2021
# Ev<PERSON><PERSON><PERSON>, 2022
# valmasone, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_invalid_count
msgid "# Invalid recipients"
msgstr "Недействительные получатели"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_valid_count
msgid "# Valid recipients"
msgstr "Действительные получатели"

#. module: sms
#: code:addons/sms/models/sms_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#, python-format
msgid "%s characters, fits in %s SMS (%s) "
msgstr "%s символы, помещающиеся в %s SMS (%s)"

#. module: sms
#: code:addons/sms/wizard/sms_composer.py:0
#, python-format
msgid "%s invalid recipients"
msgstr ""

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid ""
"%s out of the %s selected SMS Text Messages have successfully been resent."
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this SMS again to the recipients you did not select."
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid ""
"<span class=\"text-warning\" attrs=\"{'invisible': [('no_record', '=', "
"False)]}\">No records</span>"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_base_automation__state
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__state
#: model:ir.model.fields,field_description:sms.field_ir_cron__state
msgid "Action To Do"
msgstr "Действие для выполнения"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__active_domain
msgid "Active domain"
msgstr "Активный домен"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__active_domain_count
msgid "Active records count"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"Add a contextual action on the related model to open a sms composer with "
"this template"
msgstr ""

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending an SMS."
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__model_id
msgid "Applies to"
msgstr "Применяется к"

#. module: sms
#: code:addons/sms/wizard/sms_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s SMS delivery failures? You won't be able"
" to re-send these SMS later!"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "В черном списке"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Номер в черном списке - мобильный"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Номер в черном списке "

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__body
#: model:ir.model.fields,field_description:sms.field_sms_template__body
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__body
msgid "Body"
msgstr "Содержимое"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Buy credits"
msgstr "получить кредит"

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "Buy credits."
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Cancel"
msgstr "Отмена"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
msgid "Cancel notification in failure"
msgstr "Отменить сообщение об ошибке"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__canceled
msgid "Canceled"
msgstr "Отменено"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Check"
msgstr "Проверка"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose a language:"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose an example"
msgstr "Выберите пример"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Close"
msgstr "Закрыть"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__composition_mode
msgid "Composition Mode"
msgstr ""

#. module: sms
#: model:ir.model,name:sms.model_res_partner
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Contact"
msgstr "Контакт"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Content"
msgstr "Содержимое"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_uid
msgid "Created by"
msgstr "Создан"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__create_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_date
msgid "Created on"
msgstr "Создан"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__partner_id
msgid "Customer"
msgstr "Заказчик"

#. module: sms
#: model:sms.template,name:sms.sms_template_demo_0
msgid "Customer: automated SMS"
msgstr ""

#. module: sms
#: model:sms.template,body:sms.sms_template_demo_0
msgid "Dear {{ object.display_name }} this is an automated SMS."
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__null_value
msgid "Default Value"
msgstr "Значение по умолчанию "

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Discard"
msgstr "Отменить"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_cancel_action
msgid "Discard SMS delivery failures"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
msgid "Discard delivery failures"
msgstr "Отменить неудачные доставки"

#. module: sms
#: model:ir.model,name:sms.model_sms_cancel
msgid "Dismiss notification for resend by model"
msgstr "Отклонить сообщение о повторную отправку по модели"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__display_name
#: model:ir.model.fields,field_description:sms.field_sms_composer__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__display_name
#: model:ir.model.fields,field_description:sms.field_sms_sms__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: sms
#: model:ir.model,name:sms.model_mail_followers
msgid "Document Followers"
msgstr "Подписчики документа"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_id
msgid "Document ID"
msgstr "ID документа"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids
msgid "Document IDs"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model
msgid "Document Model Name"
msgstr "Модель документа"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_duplicate
msgid "Duplicate"
msgstr "Дублировать"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Dynamic Placeholder Generator"
msgstr "Динамический генератор заполнителя"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "редактировать партнеров"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr "Цепочка эл.почты"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__error
msgid "Error"
msgstr "Ошибка"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__failure_type
msgid "Failure Type"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__failure_type
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__failure_type
msgid "Failure type"
msgstr "Тип неудачи"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_object_field
msgid "Field"
msgstr "Поле"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Окончательное выражение подстановки для копирования-вставки в желаемое поле "
"шаблона."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: sms
#: code:addons/sms/wizard/sms_composer.py:0
#, python-format
msgid "Following numbers are not correctly encoded: %s"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_cancel
msgid "Has Cancel"
msgstr "должен отменить"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_insufficient_credit
msgid "Has Insufficient Credit"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_message__has_sms_error
msgid "Has SMS error"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_unregistered_account
msgid "Has Unregistered Account"
msgstr ""

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,help:sms.field_mail_message__has_sms_error
msgid "Has error"
msgstr "имеет ошибку"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__help_message
msgid "Help message"
msgstr "вспомогательное сообщение"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__id
#: model:ir.model.fields,field_description:sms.field_sms_composer__id
#: model:ir.model.fields,field_description:sms.field_sms_resend__id
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__id
#: model:ir.model.fields,field_description:sms.field_sms_sms__id
#: model:ir.model.fields,field_description:sms.field_sms_template__id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__id
msgid "ID"
msgstr "Идентификатор"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction
#: model:ir.model.fields,help:sms.field_res_partner__message_unread
msgid "If checked, new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: sms
#: model:ir.model.fields,help:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_department__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_job__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_leave_allocation__message_has_sms_error
#: model:ir.model.fields,help:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_channel__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,help:sms.field_note_note__message_has_sms_error
#: model:ir.model.fields,help:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_users__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено - некоторые сообщения имеют ошибку доставки."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red icon next to "
"each message."
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Ignore all"
msgstr ""

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__outgoing
msgid "In Queue"
msgstr "В очереди"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__comment_single_recipient
msgid "Indicates if the SMS composer targets a single specific recipient"
msgstr ""

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_credit
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "Недостаточно кредитов"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Invalid phone number"
msgstr ""

#. module: sms
#: code:addons/sms/wizard/sms_composer.py:0
#, python-format
msgid "Invalid recipient number. Please update it."
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_valid
msgid "Is valid"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_keep_log
msgid "Keep a note on document"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__lang
msgid "Language"
msgstr "Язык"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel____last_update
#: model:ir.model.fields,field_description:sms.field_sms_composer____last_update
#: model:ir.model.fields,field_description:sms.field_sms_resend____last_update
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient____last_update
#: model:ir.model.fields,field_description:sms.field_sms_sms____last_update
#: model:ir.model.fields,field_description:sms.field_sms_template____last_update
#: model:ir.model.fields,field_description:sms.field_sms_template_preview____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__write_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_base_automation__sms_mass_keep_log
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_mass_keep_log
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_mass_keep_log
msgid "Log as Note"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__mail_message_id
msgid "Mail Message"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_model__is_mail_thread_sms
msgid "Mail Thread SMS"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: sms
#: model:ir.model,name:sms.model_mail_message
#: model:ir.model.fields,field_description:sms.field_sms_composer__body
#: model:ir.model.fields,field_description:sms.field_sms_resend__mail_message_id
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Message"
msgstr "Сообщение"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: sms
#: model:ir.model,name:sms.model_mail_notification
msgid "Message Notifications"
msgstr "Уведомления о сообщениях"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__message_type
#: model:ir.model.fields,help:sms.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Тип сообщения: эл. почта - для эл. почты, уведомление - для системных "
"сообщений, комментарий - для остальных сообщений, таких как сообщения "
"пользователя"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_missing
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "Недостающий номер"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__model
msgid "Model"
msgstr "Модель"

#. module: sms
#: model:ir.model,name:sms.model_ir_model
msgid "Models"
msgstr "Модели"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__name
msgid "Name"
msgstr "Название"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__no_record
msgid "No Record"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__notification_id
msgid "Notification"
msgstr "Уведомления"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__notification_type
msgid "Notification Type"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_number
#: model:ir.model.fields,field_description:sms.field_sms_sms__number
msgid "Number"
msgstr "Номер"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__number_field_name
msgid "Number Field"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error_counter
msgid "Number of errors"
msgstr "Количество ошибок"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Количество сообщений, требующих действия"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ошибкой отправки"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__res_ids_count
msgid ""
"Number of recipients that will receive the SMS if sent in mass mode, without"
" applying the Active Domain value"
msgstr ""

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__active_domain_count
msgid "Number of records found when searching with the value in Active Domain"
msgstr ""

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_unread_counter
msgid "Number of unread messages"
msgstr "Количество непрочитанных сообщений"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_optout
msgid "Opted Out"
msgstr "Отписались"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Необязательный язык перевода (код ISO) для выбора при отправке электронной "
"почты. Если он не задан, будет использоваться английская версия. Обычно это "
"плейсходлер, который обеспечивает соответствующий язык, например, {{ "
"object.partner_id.lang }}."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Необязательное значение, используемое если целевое поле пусто"

#. module: sms
#: model:ir.model,name:sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "Исходящие СМС"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_id
msgid "Partner"
msgstr "Партнёр"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread_phone
msgid "Phone Blacklist Mixin"
msgstr "Миксин Черный список телефона"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Номер в черном списке"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Домашний/Мобильный"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__copyvalue
msgid "Placeholder Expression"
msgstr "Выражение подстановки"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__comment
msgid "Post on a document"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Preview"
msgstr "Просмотр"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Preview of"
msgstr "Просмотр"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Put in queue"
msgstr "Поставить в очередь"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Reason"
msgstr "Причина"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_name
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Recipient"
msgstr "Получатель"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number_itf
msgid "Recipient Number"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__recipient_ids
msgid "Recipients"
msgstr "Получатели"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__numbers
msgid "Recipients (Numbers)"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_description
msgid "Recipients (Partners)"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__resource_ref
msgid "Record reference"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model
msgid "Related Document Model"
msgstr "Модель связанного документа"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Remove the contextual action of the related model"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__render_model
msgid "Rendering Model"
msgstr "Модель рендеринга"

#. module: sms
#: model:ir.actions.server,name:sms.ir_actions_server_sms_sms_resend
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__resend
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Resend"
msgstr "переслать"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend_recipient
msgid "Resend Notification"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Retry"
msgstr "Повторить"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/components/message/message.xml:0
#: code:addons/sms/static/src/components/message/message.xml:0
#: model:ir.actions.act_window,name:sms.sms_sms_action
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id
#: model:ir.model.fields.selection,name:sms.selection__mail_message__message_type__sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__notification_type__sms
#: model:ir.ui.menu,name:sms.sms_sms_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
#, python-format
msgid "SMS"
msgstr "СМС"

#. module: sms
#: model:ir.model,name:sms.model_sms_api
msgid "SMS API"
msgstr "SMS API"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_department__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_job__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_leave_allocation__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_channel__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_note_note__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_users__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "SMS Failures"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_number
msgid "SMS Number"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS Preview"
msgstr ""

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#, python-format
msgid "SMS Pricing"
msgstr "Цена SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend
msgid "SMS Resend"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__state
msgid "SMS Status"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_base_automation__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_template_id
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "SMS Template"
msgstr "Шаблон SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_preview
msgid "SMS Template Preview"
msgstr ""

#. module: sms
#: model:ir.model,name:sms.model_sms_template
#: model:ir.ui.menu,name:sms.sms_template_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_tree
msgid "SMS Templates"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS content"
msgstr ""

#. module: sms
#: model:ir.actions.server,name:sms.ir_cron_sms_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:sms.ir_cron_sms_scheduler_action
#: model:ir.cron,name:sms.ir_cron_sms_scheduler_action
msgid "SMS: SMS Queue Manager"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized
#: model:ir.model.fields,field_description:sms.field_sms_composer__sanitized_numbers
msgid "Sanitized Number"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_search
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_search
msgid "Search SMS Templates"
msgstr ""

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Выбор целевого поля из связанной модели документа.\n"
"Если это поле взаимоотношений, вы сможете выбрать целевое поле на месте назначения взаимоотношений."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Send Now"
msgstr "Отправить сейчас"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send SMS"
msgstr "Отправить SMS"

#. module: sms
#: code:addons/sms/models/sms_template.py:0
#, python-format
msgid "Send SMS (%s)"
msgstr ""

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_phone_widget.js:0
#: code:addons/sms/static/src/js/fields_phone_widget.js:0
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_single
#: model:ir.actions.act_window,name:sms.sms_composer_action_form
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__state__sms
#, python-format
msgid "Send SMS Text Message"
msgstr "Отправить SMS сообщение"

#. module: sms
#: model:ir.model,name:sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr ""

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__mass
msgid "Send SMS in batch"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send an SMS"
msgstr "Отправить SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_force_send
msgid "Send directly"
msgstr ""

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__numbers
msgid "Send to numbers"
msgstr ""

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_resend_action
msgid "Sending Failures"
msgstr ""

#. module: sms
#: code:addons/sms/models/ir_actions.py:0
#, python-format
msgid "Sending SMS can only be done on a mail.thread model"
msgstr ""

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__sent
msgid "Sent"
msgstr "Отправлено"

#. module: sms
#: model:ir.model,name:sms.model_ir_actions_server
msgid "Server Action"
msgstr "Действие сервера"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_server
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_server
msgid "Server Error"
msgstr "Ошибка сервера"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Set up an account"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sidebar_action_id
msgid "Sidebar action"
msgstr "Действие боковой панели"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sidebar_action_id
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Действие боковой панели для доступности этого шаблона по записям, связанным "
"с моделью документа"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__comment_single_recipient
msgid "Single Mode"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_resend_id
msgid "Sms Resend"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__sms_template_id
msgid "Sms Template"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number
msgid "Stored Recipient Number"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sub_model_object_field
msgid "Sub-field"
msgstr "Субполе"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sub_object
msgid "Sub-model"
msgstr "Субмодель"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "Success"
msgstr "Успех"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_preview_action
msgid "Template Preview"
msgstr "Просмотр шаблона"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__lang
msgid "Template Preview Language"
msgstr "Язык шаблона"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_action
msgid "Templates"
msgstr "Шаблоны"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "The SMS Text Messages could not be resent."
msgstr ""

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "The number you're trying to reach is not correctly formatted."
msgstr ""

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_id
#: model:ir.model.fields,help:sms.field_sms_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "Тип документа, с которым этот шаблон может быть использован"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "There are no SMS Text Messages to resend."
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.res_partner_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__message_type
#: model:ir.model.fields,field_description:sms.field_mail_message__message_type
msgid "Type"
msgstr "Тип"

#. module: sms
#: model:ir.model.fields,help:sms.field_base_automation__state
#: model:ir.model.fields,help:sms.field_ir_actions_server__state
#: model:ir.model.fields,help:sms.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""
"Тип действия сервера. Доступны следующие значения:\n"
"- `Выполнить код Python`: блок кода python, который будет выполнен\n"
"- `Создать`: создать запись с новыми значениями\n"
"- `Обновить запись`: обновить поля записи\n"
"- `Выполнить несколько действий ': действие, которое запускает несколько других действий\n"
"- `Отправить Email`: автоматически отправить сообщение (модуль Общение)\n"
"- `Добавить подписчиков`: добавить подписчиков к записи (модуль Общение)\n"
"- `Создать следующее действие ': создать действие (модуль Общение)"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__recipient_single_number_itf
msgid ""
"UX field allowing to edit the recipient number. If changed it will be stored"
" onto the recipient."
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_unread
msgid "Unread Messages"
msgstr "Непрочитанные сообщения"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Счетчик непрочитанных сообщений"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_acc
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_acc
msgid "Unregistered Account"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__template_id
msgid "Use Template"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__use_active_domain
msgid "Use active domain"
msgstr "Использовать активный домен"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_use_blacklist
msgid "Use blacklist"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids_count
msgid "Visible records count"
msgstr ""

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "Warning"
msgstr "Предупреждение"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайтом"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Когда поле связи выбрано как первое поле, это поле даёт вам выбор целевого "
"поля в пределах модели целевого документа (под-модели)."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Когда связывающее поле выбрано как первое поле, это поле показывает "
"связанную модель документа."

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_model__is_mail_thread_sms
msgid "Whether this model supports messages and notifications through SMS"
msgstr ""

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_format
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "Неверный формат номера"

#. module: sms
#: code:addons/sms/wizard/sms_resend.py:0
#, python-format
msgid "You do not have access to the message and/or related document."
msgstr ""

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "You don't have an eligible IAP account."
msgstr ""

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "You don't have enough credits on your IAP account."
msgstr ""

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#, python-format
msgid ""
"Your SMS Text Message must include at least one non-whitespace character"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "are invalid."
msgstr "недействительны."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "e.g. +1 415 555 0100"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Calendar Reminder"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Contact"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. en_US or {{ object.partner_id.lang }}"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"recipients are valid\n"
"                                and"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "record:"
msgstr "запись:"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "records instead. <br/>"
msgstr "вместо этих"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "records selected."
msgstr "Выбранные записи"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "to send to all"
msgstr "отправить всем"
