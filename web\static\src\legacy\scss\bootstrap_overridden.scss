///
/// This file is a copy of the bootstrap _variables.scss file where all the
/// left-untouched variables definition have been removed.
///

//
// Color system
//

// All of those are BS default
$white:    #fff !default;
$gray-100: $o-gray-100 !default;
$gray-200: $o-gray-200 !default;
$gray-300: $o-gray-300 !default;
$gray-400: $o-gray-400 !default;
$gray-500: $o-gray-500 !default;
$gray-600: $o-gray-600 !default;
$gray-700: $o-gray-700 !default;
$gray-800: $o-gray-800 !default;
$gray-900: $o-gray-900 !default;

$black:    #000 !default;

$primary: $o-brand-primary !default;
$secondary: $white !default;

$success: $o-success !default;
$info: $o-info !default;
$warning: $o-warning !default;
$danger: $o-danger !default;

// Options
//
// Quickly modify global styling by enabling or disabling optional features.

// Body
//
// Settings for the `<body>` element.

$body-bg: $o-brand-secondary !default;
$body-color: $o-main-text-color !default;

// Links
//
// Style anchor elements.

$link-color: $o-main-link-color !default;
$link-hover-decoration: none !default;

// Muted
//
// Style .text-muted elements

$text-muted: $o-main-color-muted !default;

// Grid columns
//
// Set the number of columns and specify the width of the gutters.

$grid-gutter-width: $o-horizontal-padding * 2 !default;

// Components
//
// Define common padding and border radius sizes and more.

$border-radius: 3px !default;
$border-color: $o-gray-300 !default;

$component-active-color: $o-brand-primary !default;
$component-active-bg: $gray-200 !default;

$caret-width: 4px !default;

// Fonts
//
// Font, line-height, and color for body text, headings, and more.

$font-family-sans-serif: o-add-unicode-support-font(("Lucida Grande", Helvetica, Verdana, Arial, sans-serif), 1) !default;

$font-size-base: $o-font-size-base !default;
$line-height-base: $o-line-height-base !default;

$h1-font-size: $font-size-base * 2.0 !default;
$h2-font-size: $font-size-base * 1.5 !default;
$h3-font-size: $font-size-base * 1.3 !default;
$h4-font-size: $font-size-base * 1.2 !default;
$h5-font-size: $font-size-base * 1.1 !default;

$headings-font-weight: bold !default;
$headings-color: $o-main-headings-color !default;

// Tables
//
// Customizes the `.table` component with basic values, each used across all table variations.

$table-accent-bg: rgba($black, .01) !default;
$table-hover-bg: rgba($black, .04) !default;
$table-border-color: $gray-200 !default;

// Dropdowns
//
// Dropdown menu container and contents.

$dropdown-border-color: $gray-300 !default;

$dropdown-link-color: $o-main-text-color !default;
$dropdown-link-hover-color: $gray-900 !default;
$dropdown-link-hover-bg: rgba($black, 0.08) !default;

$dropdown-link-active-color: $gray-900 !default;
$dropdown-link-active-bg: transparent !default;

$dropdown-link-disabled-color: $o-main-color-muted !default;

$dropdown-item-padding-y: $o-dropdown-vpadding !default;
$dropdown-item-padding-x: $o-dropdown-hpadding !default;


// Forms
$custom-control-indicator-border-color: $gray-500 !default;
$custom-control-label-disabled-color: $o-main-color-muted !default;

$custom-control-indicator-checked-color: $white !default;
$custom-control-indicator-checked-bg: $primary !default;
$custom-control-indicator-checked-border-color: $primary !default;

$custom-control-indicator-disabled-bg: $gray-200 !default;
$custom-control-indicator-checked-disabled-bg: $gray-200 !default;

// Z-index master list

// Change the z-index of the modal-backdrop elements to be equal to the
// modal elements' ones. Bootstrap does not support multi-modals, and without
// this rule all the modal-backdrops are below all the opened modals.
// Indeed, bootstrap forces them to a lower z-index as the modal-backdrop
// element (unique in their supported cases) might be put after the modal
// element (if the modal is already in the DOM, hidden, then opened). This
// cannot happen in odoo though as modals are not hidden but removed from
// the DOM and are always put at the end of the body when opened.
//
// TODO the following code was disabled because it is saas-incompatible
//
// $zindex-modal-backdrop: $zindex-modal;

// Navs

$nav-tabs-link-active-bg: $white !default;

$nav-pills-border-radius: 0 !default;
$nav-pills-link-active-color: $white !default;
$nav-pills-link-active-bg: $o-brand-primary !default;

// Toasts

$toast-max-width: 320px !default;
$toast-padding-x: 1.5rem !default;
$toast-padding-y: 0.5rem !default;
$toast-font-size: $font-size-base !default;
$toast-background-color: rgba($white, .7) !default;
$toast-header-background-color: $toast-background-color !default;

// Modals

// Padding applied to the modal body
$modal-inner-padding: $o-horizontal-padding !default;

$modal-lg: $o-modal-lg !default;
$modal-md: $o-modal-md !default;

// Breadcrumbs

$breadcrumb-padding-y: 0 !default;
$breadcrumb-padding-x: 0 !default;
$breadcrumb-margin-bottom: 0 !default;

$breadcrumb-bg: $o-control-panel-background-color !default;
$breadcrumb-item-padding: .2em !default;

// Code

$code-color: $o-main-code-color!default;
