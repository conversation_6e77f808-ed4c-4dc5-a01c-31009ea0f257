# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <zd<PERSON>chu<PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON> <a.wis<PERSON><PERSON>@hadron.eu.com>, 2021
# <PERSON><PERSON><PERSON> <piotr.w.<PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <stre<PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.warczak<PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <judyta.kazmie<PERSON><EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <m<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Dawid Prus, 2021
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: Tadeusz Karpiński <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed_slides_count
msgid "# Completed Slides"
msgstr "# Ukończone Slajdy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
msgid "# Views"
msgstr "# Odsłon"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__public_views
msgid "# of Public Views"
msgstr "# z Widoku Publicznego"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr "# z podglądu strony internetowej "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completion
msgid "% Completed Slides"
msgstr "% Ukończone Slajdy"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "'. Showing results for '"
msgstr "'. Wyświetl wyniki dla '"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_slides_list.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "(empty)"
msgstr "(puste)"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ". This way, they will be secured."
msgstr ". W ten sposób będą one zabezpieczone."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_5
msgid "3 Main Methodologies"
msgstr "3 Główne metodologie"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "<b>%s</b> is requesting access to this course."
msgstr "<b>%s</b> prosi o dostęp do tego kursu"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<b>(empty)</b>"
msgstr "<b>(puste)</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<b>Order by</b>"
msgstr "<b>Zamów prez</b>"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"<b>Save & Publish</b> your lesson to make it available to your attendees."
msgstr "<b>Zapisz i opublikuj</b> lekcję, aby udostępnić ją uczestnikom."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "<b>Save</b> your question."
msgstr "<b>Zapisz</b> pytanie."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<b>Uncategorized</b>"
msgstr "<b>Nieskategoryzowane</b>"

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been invited to join a new course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_completed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br/>\n"
"                        <p><b>Congratulations!</b></p>\n"
"                        <p>You've completed the course <b t-out=\"object.channel_id.name or ''\">Basics of Gardening</b></p>\n"
"                        <p>Check out the other available courses.</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Explore courses\n"
"                            </a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <t t-out=\"object.slide_type or ''\">document</t> <strong t-out=\"object.name or ''\">Trees</strong> with you!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong t-out=\"object.name or ''\">Trees</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        There is something new in the course <strong t-out=\"object.channel_id.name or ''\">Trees, Wood and Gardens</strong> you are following:<br/><br/>\n"
"                        <center><strong t-out=\"object.name or ''\">Trees</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View content</a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"fa fa-arrow-right mr-1\"/>All Courses"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>Wszystkie Kursy"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> Statystyki"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Lessons</span>"
msgstr ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Lekcje</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-chevron-left mr-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"
msgstr ""
"<i class=\"fa fa-chevron-left mr-2\"/><span class=\"d-none d-sm-inline-"
"block\">Podgląd</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-circle-o-notch fa-spin mr-2\"/><b>Loading...</b>"
msgstr "<i class=\"fa fa-circle-o-notch fa-spin mr-2\"/><b>Wczytywanie...</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-clock-o mr-2\" aria-label=\"Create date\"/>"
msgstr "<i class=\"fa fa-clock-o mr-2\" aria-label=\"Create date\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-clock-o mr-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"
msgstr ""
"<i class=\"fa fa-clock-o mr-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-cloud-upload mr-1\"/>Upload new content"
msgstr "<i class=\"fa fa-cloud-upload mr-1\"/>Prześlij nową zawartość"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-code\"/> Embed"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-comments\"/> Comments ("
msgstr "<i class=\"fa fa-comments\"/>Komponenty("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-desktop mr-2\"/>\n"
"                <span class=\"d-none d-sm-inline-block\">Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-desktop mr-2\"/>\n"
"<span class=\"d-none d-sm-inline-block\">Pełny Ekran</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-envelope\"/> Email"
msgstr "<i class=\"fa fa-envelope\"/>E-mail"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope\"/> Send Email"
msgstr "<i class=\"fa fa-envelope\"/>Wyślij E-mail"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-eraser mr-1\"/>Clear filters"
msgstr "Wyczyść filtry"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "<i class=\"fa fa-eraser\"/> Clear filters"
msgstr "Wyczyść filtry"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This document is private."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/>Ten dokument jest prywatny."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-eye mr-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye mr-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-code-o mr-2\" aria-label=\"Webpage\" role=\"img\" "
"title=\"Webpage\"/>"
msgstr ""
"<i class=\"fa fa-file-code-o mr-2\" aria-label=\"Webpage\" role=\"img\" "
"title=\"Webpage\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-image-o mr-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"
msgstr ""
"<i class=\"fa fa-file-image-o mr-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-pdf-o mr-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"
msgstr ""
"<i class=\"fa fa-file-pdf-o mr-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-video-o mr-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"
msgstr ""
"<i class=\"fa fa-file-video-o mr-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-flag mr-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"
msgstr "<i class=\"fa fa-flag mr-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<i class=\"fa fa-flag text-warning\"/> Quiz"
msgstr "<i class=\"fa fa-flag text-warning\"/> Quiz"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen_sidebar_category
msgid "<i class=\"fa fa-flag-checkered text-warning mr-2\"/>Quiz"
msgstr "<i class=\"fa fa-flag-checkered text-warning mr-2\"/>Quiz"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-folder mr-2\" aria-label=\"Open folder\"/>"
msgstr "<i class=\"fa fa-folder mr-2\" aria-label=\"Open folder\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-folder-o mr-1\"/><span>Add Section</span>"
msgstr "<i class=\"fa fa-folder-o mr-1\"/><span>Dodaj sekcję</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-folder-o mr-1\"/>Add a section"
msgstr "<i class=\"fa fa-folder-o mr-1\"/>Dodaj sekcję"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "<i class=\"fa fa-graduation-cap mr-1\"/>All courses"
msgstr "<i class=\"fa fa-graduation-cap mr-1\"/>Wszystkie kursy"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> O programie"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-home\"/> Course"
msgstr "<i class=\"fa fa-home\"/>Kurs"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block ml-1\">Back "
"to course</span>"
msgstr ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block ml-1\">Wróć "
"do kursu</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-info-circle\"/>\n"
"                    The social sharing module will be unlocked when a moderator will allow your publication."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<i class=\"fa fa-plus mr-1\"/> <span class=\"d-none d-md-inline-block\">Add "
"Content</span>"
msgstr ""
"<i class=\"fa fa-plus mr-1\"/><span class=\"d-none d-md-inline-block\">Dodaj"
" Zawartość</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-plus mr-1\"/><span>Add Content</span>"
msgstr "<i class=\"fa fa-plus mr-1\"/><span>Dodaj Zawartość</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>Add Question</span>"
msgstr ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"<span>Dodaj pytanie</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>Add Quiz</span>"
msgstr "<i class=\"fa fa-plus mr-2\"/>nieupubliczniono"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-question mr-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"
msgstr ""
"<i class=\"fa fa-question mr-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/> Udostępnij"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-share-alt\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Udostępij</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "<i class=\"fa fa-share-square fa-fw\"/> Share"
msgstr "<i class=\"fa fa-share-square fa-fw\"/> Udostępnij"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-share-square\"/> Share"
msgstr "<i class=\"fa fa-share-square\"/> Udostępnij"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Exit Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Opuść Pełny Ekran</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star text-black-25\" aria-label=\"A star\"/>"
msgstr "<i class=\"fa fa-star text-black-25\" aria-label=\"A star\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star\" aria-label=\"A star\" role=\"img\"/>"
msgstr "<i class=\"fa fa-star\" aria-label=\"A star\" role=\"img\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star-half-o\" aria-label=\"Half a star\" role=\"img\"/>"
msgstr "<i class=\"fa fa-star-half-o\" aria-label=\"Half a star\" role=\"img\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid ""
"<i class=\"fa fa-tag mr-2 text-muted\"/>\n"
"                      My Courses"
msgstr ""
"<i class=\"fa fa-tag mr-2 text-muted\"/>\n"
"Moje Kursy"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"Dislikes\" "
"title=\"Dislikes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"Dislikes\" "
"title=\"Dislikes\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"Likes\" title=\"Likes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"Likes\" "
"title=\"Likes\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid ""
"<small class=\"text-success\">\n"
"                                Request already sent\n"
"                            </small>"
msgstr ""
"<small class=\"text-success\">\n"
"Prośba została już wysłana\n"
"</small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid ""
"<small><span class=\"badge badge-pill badge-success pull-right my-1 py-1 "
"px-2 font-weight-normal\"><i class=\"fa fa-check\"/> "
"Completed</span></small>"
msgstr ""
"<small><span class=\"badge badge-pill badge-success pull-right my-1 py-1 "
"px-2 font-weight-normal\"><i class=\"fa fa-check\"/>Ukończono</span></small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-danger\">Unpublished</span>"
msgstr "<span class=\"badge badge-danger\">Nieopublikowano</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge badge-info badge-arrow-right font-weight-normal px-2 "
"py-1 m-1\">New</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-info\">Preview</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge badge-light badge-hide border font-weight-normal px-2 "
"py-1 m-1\">Add Quiz</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid ""
"<span class=\"badge badge-pill badge-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/>Ukończono</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid ""
"<span class=\"badge badge-pill badge-success py-1 px-2 mx-auto\" "
"style=\"font-size: 1em\"><i class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success py-1 px-2 mx-auto\" "
"style=\"font-size: 1em\"><i class=\"fa fa-check\"/>Ukończono</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<span class=\"badge badge-pill badge-success py-1 px-2\" style=\"font-size: "
"1em\"><i class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success py-1 px-2\" style=\"font-size: "
"1em\"><i class=\"fa fa-check\"/>Ukończono</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-check\"/> "
"Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-"
"check\"/>Ukończono</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\"><span>Preview</span></span>"
msgstr ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\"><span>Podgląd</span></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\">Preview</span>"
msgstr ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\">Podgląd</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge font-weight-bold px-2 py-1 m-1 badge-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"d-none d-sm-inline-block\">Next</span> <i class=\"fa fa-"
"chevron-right ml-2\"/>"
msgstr ""
"<span class=\"d-none d-sm-inline-block\">Następne</span><i class=\"fa fa-"
"chevron-right ml-2\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                    Create a Google Project and Get a Key"
msgstr ""
"<span class=\"fa fa-arrow-right\"/>\n"
"Utwórz projekt Google i pobierz Klucz"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "<span class=\"fa fa-clipboard\"> Copy Text</span>"
msgstr "<span class=\"fa fa-clipboard\">Kopiuj Tekst</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "<span class=\"font-weight-bold text-muted mr-2\">Current rank:</span>"
msgstr "<span class=\"font-weight-bold text-muted mr-2\">Obecna ranga:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "<span class=\"font-weight-normal\">Last update:</span>"
msgstr "<span class=\"font-weight-normal\">Ostatni Update:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid ""
"<span class=\"form-text text-muted d-block w-100\">Send presentation through"
" email</span>"
msgstr ""
"<span class=\"form-text text-muted d-block w-100\">Wyślij prezentację przez "
"e-mail</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Slajdy</span>\n"
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_partner_view_form
msgid "<span class=\"o_stat_text\">Courses</span>"
msgstr "<span class=\"o_stat_text\">Kursy</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card_information_arrow
msgid "<span class=\"o_wslides_arrow\">New Content</span>"
msgstr "<span class=\"o_wslides_arrow\">Nowa Zawartość</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training
msgid "<span class=\"p-2\">Course content</span>"
msgstr "<span class=\"p-2\">Zawartość Kursu</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "<span class=\"text-500 mx-2\">•</span>"
msgstr "<span class=\"text-500 mx-2\">•</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted font-weight-bold mr-3\">Rating</span>"
msgstr "<span class=\"text-muted font-weight-bold mr-3\">Ocena</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Attendees</span>"
msgstr "<span class=\"text-muted\">Uczestnicy</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Common tasks for a computer scientist</span>"
msgstr "<span class=\"text-muted\">Typowe zadania dla informatyka</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Contents</span>"
msgstr "<span class=\"text-muted\">Zawartośći</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Parts of computer science</span>"
msgstr "<span class=\"text-muted\">Części informatyki</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"first\" class=\"mr-1 mr-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"fullscreen\" class=\"ml-1 ml-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ml-2 mr-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span name=\"done_members_count_label\" class=\"text-muted\">Finished</span>"
msgstr "<span name=\"done_members_count_label\" class=\"text-muted\">Ukończone</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span name=\"members_done_count_label\" class=\"o_stat_text\">Finished</span>"
msgstr ""
"<span name=\"members_done_count_label\" "
"class=\"o_stat_text\">Ukończone</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span> hours</span>"
msgstr "<span> godzin</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<span>Add Tag</span>"
msgstr "<span>Dodaj tag</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Answering Questions</span>"
msgstr "<span>Odpowiadanie na pytania</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking Question</span>"
msgstr "<span>Zadawanie pytań</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking the right question</span>"
msgstr "<span>Zadawanie właściwych pytań</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Logic</span>"
msgstr "<span>Logika</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Mathematics</span>"
msgstr "<span>Matematyka</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "<span>Preview</span>"
msgstr "<span>Podgląd</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Science</span>"
msgstr "<span>Nauka</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>XP</span>"
msgstr "<span>XP</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "<span>×</span>"
msgstr "<span>x</span>"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_fullscreen_player.js:0
#: code:addons/website_slides/static/src/js/slides_share.js:0
#, python-format
msgid "<strong>Thank you!</strong> Mail has been sent."
msgstr "<strong>Dziękujemy!</strong>Mail został wysłany."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_1
msgid "A Mighty Forest from Ages"
msgstr "Potężny las od wieków"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_0
msgid "A fruit"
msgstr "Owoc"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"A good course has a structure. Pick a name for your first section and click "
"<b>Save</b> to create it."
msgstr ""
"Dobry kurs ma strukturę. Wybierz nazwę pierwszej sekcji i kliknij "
"<b>Zapisz</b>, aby ją utworzyć."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_4
msgid "A little chat with Harry Potted"
msgstr "Mała pogawędka z Harrym Pottedem"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_2_gard2
msgid ""
"A lot of nice documentation: trees, wood, gardens. A gold mine for "
"references."
msgstr ""
"Dużo ładnej dokumentacji: drzewa, drewno, ogrody. Kopalnia złota dla "
"referencji."

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_0
msgid "A shovel"
msgstr "Łopata"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_exclusion_html_content_and_url
msgid ""
"A slide is either filled with a document url or HTML content. Not both."
msgstr ""

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_1
msgid "A spoon"
msgstr "Łyżka"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_5
msgid "A summary of know-how: how and what."
msgstr "A summary of know-how: how and what."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_0
msgid ""
"A summary of know-how: how and what. All the basics for this course about "
"gardening."
msgstr ""
"Podsumowanie know-how: jak i co. Wszystkie podstawy tego kursu o "
"ogrodnictwie."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_0
msgid ""
"A summary of know-how: what are the main trees categories and how to "
"differentiate them."
msgstr ""
"Podsumowanie know-how: jakie są główne kategorie drzew i jak je rozróżnić?"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_2
msgid "A table"
msgstr "Tabela"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_tag_slide_tag_unique
msgid "A tag must be unique!"
msgstr "Tag musi być unikalny!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_1
msgid "A vegetable"
msgstr "Warzywo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr "Klucz API"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Granted"
msgstr "Dostęp przyznany"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_groups
msgid "Access Groups"
msgstr "Grupy dostępu"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Refused"
msgstr "Nie przyznano Dostępu"

#. module: website_slides
#: model:mail.activity.type,name:website_slides.mail_activity_data_access_request
msgid "Access Request"
msgstr "Prośba o Dostęp"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_requested_access
msgid "Access Requested"
msgstr "Poproszono o Dostęp"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Access Rights"
msgstr "Prawa dostępu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr "Wymagane działanie"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Actions"
msgstr "Akcje"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "Aktywne"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_ids
msgid "Activities"
msgstr "Czynności"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekoracja wyjątku aktywności"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_state
msgid "Activity State"
msgstr "Stan aktywności"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktywności"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_comment
msgid "Add Comment"
msgstr "Dodaj komentarz "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Content"
msgstr "Dodaj zawartość"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_review
msgid "Add Review"
msgstr "Dodaj recenzję"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Section"
msgstr "Dodaj sekcję"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Add Tag"
msgstr "Dodaj znacznik"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
msgid "Add a new lesson"
msgstr "Dodaj nową lekcję"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#, python-format
msgid "Add a section"
msgstr "Dodaj sekcję"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#, python-format
msgid "Add a tag"
msgstr "Dodaj znacznik"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add an answer below this one"
msgstr "Dodaj odpowiedź poniżej "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add comment on this answer"
msgstr "Dodaj komentarz do odpowiedzi "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Add existing contacts..."
msgstr "Dodaj istniejące kontakty..."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid ""
"Add quizzes at the end of your lessons to evaluate what your students "
"understood."
msgstr ""
"Dodaj quizy na koniec swoich lekcji by sprawdzić co zrozumieli twoi "
"studenci."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_ids
msgid "Additional Resource for this slide"
msgstr "Dodatkowe zasoby dla tego slajdu"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Additional Resources"
msgstr "Dodatkowe Zasoby"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_resource
msgid "Additional resource for a particular slide"
msgstr "Dodatkowe Zasoby do określonego slajdu"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_advanced
msgid "Advanced"
msgstr "Zaawansowane"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "All Courses"
msgstr "Wszystkie kursy"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "All completed classes and earned karma will be lost."
msgstr "Wszystkie ukończone lekcje i zebrana karma zostaną utracone."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__partner_ids
msgid "All members of the channel."
msgstr "Wszyscy członkowie kanału."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "All questions must be answered !"
msgstr "Należy odpowiedzieć na wszystkie pytania!"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_5_furn2
msgid "All you need to know about furniture creation."
msgstr "Wszystko, co musisz wiedzieć o tworzeniu mebli."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow Download"
msgstr "Pozwól na pobieranie"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "Pozwól na podgląd"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Allow Rating"
msgstr "Pozwól na Ocenię"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__allow_comment
msgid "Allow rating on Course"
msgstr "Pozwól na ocenę na Kursie"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Allow students to review your course"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow the user to download the content of the slide."
msgstr "Pozwól użytkownikowi na pobieranie zawartości tej strony"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_allow_comment
msgid "Allows comment"
msgstr "Pozwól na komentarze "

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already Requested"
msgstr "Już żądano"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Already installing \"%s\"."
msgstr "już instalowanie \"%s\"."

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already member"
msgstr "już jesteś członikem"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Amazing!"
msgstr "Niesamowite!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_2
msgid "And also bananas"
msgstr "A także banany"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__text_value
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answer_ids
msgid "Answer"
msgstr "Odpowiedź"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Pojawia się w"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_visibility
msgid ""
"Applied directly as ACLs. Allow to hide channels and their content for non "
"members."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive"
msgstr "Archiwizuj"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive Slide"
msgstr "Archiwizuj Slajd"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr "Zarchiwizowane"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to archive this slide ?"
msgstr "Jesteś pewien że chcesz z archiwizować ten slajd?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to delete this category ?"
msgstr "Jesteś pewien, że chcesz usunąć tę kategorię?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Are you sure you want to delete this question :"
msgstr "Jesteś pewien że chcesz usunąć to pytanie :"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Attachment"
msgstr "Załącznik"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr "Liczba załączników"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__attachment_ids
msgid "Attachments"
msgstr "Załączniki"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_avg
msgid "Attempts Avg"
msgstr "Próby Średnie"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_count
msgid "Attempts Count"
msgstr "Próby Ilość"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_attendees
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Attendees"
msgstr "Uczestnicy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_done_count
msgid "Attendees Done Count"
msgstr "Liczba uczestników"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_count
msgid "Attendees count"
msgstr "Liczba uczestników"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Attendees of %s"
msgstr "Uczestnicy %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_group_ids
msgid "Auto Enroll Groups"
msgstr "Automatyczne rejestrowanie grup"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Badges"
msgstr "Odznaki"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_basic
msgid "Basic"
msgstr "Podstawowe"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_5_furn2
msgid "Basics of Furniture Creation"
msgstr "Podstawy tworzenia mebli"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_0_gard_0
msgid "Basics of Gardening"
msgstr "Podstawy ogrodnictwa"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Be notified when a new content is added."
msgstr "Powiadomienie o dodaniu nowej zawartości."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_comment
msgid "Can Comment"
msgstr "Może komentować"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Można edytować treść"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_publish
msgid "Can Publish"
msgstr "Można publikować"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_review
msgid "Can Review"
msgstr "Może Recenzować"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr "Może ładować pliki"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_vote
msgid "Can Vote"
msgstr "Może Głosować"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#, python-format
msgid "Cancel"
msgstr "Anuluj"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_carpenter
msgid "Carpenter"
msgstr "Stolarz"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Catchy Headline"
msgstr "Chwytliwy nagłówek"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_category_ids
msgid "Categories"
msgstr "Kategorie"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Category"
msgstr "Kategoria"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_1
msgid "Certification"
msgstr "Certyfikacja"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Certifications"
msgstr "Certyfikacje"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_certification
#: model:gamification.challenge.line,name:website_slides.badge_data_certification_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_certification_goal
msgid "Certified Knowledge"
msgstr "Certyfikowano Wiedzę"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Change video privacy settings"
msgstr "Zmiana ustawień prywatności wideo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Channel"
msgstr "Kanał"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "Kanał / Partnerzy (Członkowie)"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_invite
msgid "Channel Invitation Wizard"
msgstr "Kreator zaproszeń do kanałów"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Channel Member"
msgstr "Członek Kanału"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_type
msgid "Channel type"
msgstr "Typ Kanału"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__completed
msgid "Channel validated, even if slides / lessons are added once done."
msgstr ""
"Kanał zatwierdzony, nawet jeśli slajdy / lekcje zostaną dodane po "
"zakończeniu."

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag_group
msgid "Channel/Course Groups"
msgstr "Kanał / Grupy kursów"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag
msgid "Channel/Course Tag"
msgstr "Kanał/ Znaczniki kursu"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_invite
msgid "Channel: Invite by email"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__channel_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "Kanały"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_cheatsheet
msgid "CheatSheet"
msgstr "CheatSheet (ściągawka)"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check Profile"
msgstr "Sprawdź profil"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check answers"
msgstr "Sprawdź odpowiedzi"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check your answers"
msgstr "Sprawdź swoje odpowiedzi"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Choose a <b>File</b> on your computer."
msgstr "Wybierz <b>plik</b> na komputerze."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Choose a PDF or an Image"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Choose a layout"
msgstr "Wybierz układ"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_3_furn0
msgid "Choose your wood !"
msgstr "Wybierz drewno!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Clear filters"
msgstr "Wyczyść pliki"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Click here to send a verification email allowing you to participate at the "
"eLearning."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to start the course"
msgstr "Kliknij tutaj, aby rozpocząć kurs"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Click on \"New\" in the top-right corner to write your first course."
msgstr "Kliknij \"Nowy\" w prawym górnym rogu by stworzyć swój pierwszy kurs."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on the <b>Create</b> button to create your first course."
msgstr "Kliknij przycisk <b>Utwórz</b>, aby utworzyć swój pierwszy kurs."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on your <b>Course</b> to go back to the table of content."
msgstr "Kliknij na swój <b>kurs</b>, aby przejść z powrotem do spisu treści."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
#, python-format
msgid "Close"
msgstr "Zamknij"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Color"
msgstr "Kolor"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__color
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__color
msgid "Color Index"
msgstr "Indeks kolorów"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_colorful
msgid "Colorful"
msgstr "Kolorowy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__comment
msgid "Comment"
msgstr "Komentarz"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Comments"
msgstr "Komentarz"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering                               questions. In this course, you'll "
"study those topics with activities about mathematics, science and logic."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions. In this course, you'll study those topics with "
"activities about mathematics, science and logic."
msgstr ""
"Wspólnym zadaniem informatyka jest zadawanie właściwych pytań i odpowiadanie"
" na nie. W tym kursie zapoznasz się z tymi tematami, wykonując ćwiczenia z "
"zakresu matematyki, nauk ścisłych i logiki."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Communication"
msgstr "Komunikacja"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_karma
#: model:gamification.challenge.line,name:website_slides.badge_data_karma_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_karma_goal
msgid "Community hero"
msgstr "Bohater Społeczności"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_company_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_company_count
msgid "Company Course Count"
msgstr "Ilość kursów firmowych"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_0
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_0
msgid "Comparing Hardness of Wood Species"
msgstr "Porównanie twardości gatunków drewna"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_course_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_course
msgid "Complete a course"
msgstr "Ukończ kurs"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_profile_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_profile
msgid "Complete your profile"
msgstr "ukończ swój profil"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Completed"
msgstr "Ukończona"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: model:mail.template,name:website_slides.mail_template_channel_completed
#, python-format
msgid "Completed Course"
msgstr "Kurs ukończony"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_completed_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_completed_ids
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Completed Courses"
msgstr "ukończ kursy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completion
msgid "Completion"
msgstr "Ukończenie"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed_template_id
msgid "Completion Email"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Completion Time"
msgstr "czas ukończenia"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Compose Email"
msgstr "Utwórz wiadomość e-mail"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Computer Science for kids"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_enroll
msgid "Condition to enroll: everyone, on invite, on payment (sale bridge)."
msgstr ""
"Warunki zapisów: wszyscy, na zaproszenie, za opłatą (sprzedaż łączona)."

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_configuration
msgid "Configuration"
msgstr "Konfiguracja"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_completed
msgid "Congratulation! You completed {{ object.channel_id.name }}"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations! Your first lesson is available. Let's see the options "
"available here. The tag \"<b>New</b>\" indicates that this lesson was "
"created less than 7 days ago."
msgstr ""
"Gratulacje! Twoja pierwsza lekcja jest już dostępna. Zobaczmy dostępne "
"opcje. Znacznik \"<b>Nowy</b>\" wskazuje, że ta lekcja została utworzona "
"mniej niż 7 dni temu."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Congratulations, you have reached the last rank!"
msgstr "Gratulacje, zdobyłeś ostatnią rangę!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, you've created your first course.<br/>Click on the title of"
" this content to see it in fullscreen mode."
msgstr ""
"Gratulacje, stworzyłeś swój pierwszy kurs. <br/> Kliknij tytuł tej "
"zawartości, aby zobaczyć ją w trybie pełnoekranowym."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, your course has been created, but there isn't any content "
"yet. First, let's add a <b>Section</b> to give your course a structure."
msgstr ""
"Gratulacje, kurs został utworzony, ale nie ma jeszcze żadnej zawartości. "
"Najpierw dodajmy <b>sekcję</b>, aby nadać kursowi strukturę."

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_6_furn3
#, python-format
msgid "Contact Responsible"
msgstr "Kontakt z osobą odpowiedzialną"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Contact all the members of a course via mass mailing"
msgstr "Powiadom wszystkich członków kursu przez wiadomość masową"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Contact the responsible to enroll."
msgstr "Skontaktuj się z osobą odpowiedzialną za rejestrację."

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Contact us"
msgstr "Skontaktuj się z nami"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__datas
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Content"
msgstr "Zawartość"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Content Preview"
msgstr "Podgląd zawartości"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_question
msgid "Content Quiz Question"
msgstr "Zawartość Pytania Quizu "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_content_tags
msgid "Content Tags"
msgstr "Tagi zawartości"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Title"
msgstr "Tytuł Zawartości"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_slide_action
#: model:ir.actions.act_window,name:website_slides.slide_slide_action_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_content
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_contents
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Contents"
msgstr "Zawartość"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Continue"
msgstr "Kontynuuj"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Copy Link"
msgstr "Skopiuj Link"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_1
msgid "Correct !"
msgstr "Prawidłowo!"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_0
msgid "Correct ! A shovel is the perfect tool to dig a hole."
msgstr "Prawidłowo! Szpadel jest idealnym narzędziem do kopania dziur."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_0
msgid "Correct ! A strawberry is a fruit because it's the product of a tree."
msgstr "Prawidłowo! Truskawka jest owocem, ponieważ pochodzi z drzewa."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_0
msgid "Correct ! Congratulations you have time to loose"
msgstr "Prawidłowo! Gratulacje, masz czas do stracenia"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Correct ! You did it !"
msgstr "Prawidłowo! Udało ci się!"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Could not fetch data from url. Document or access right not available:\n"
"%s"
msgstr ""
"Nie można pobrać danych z adresu URL. Dokument lub prawo dostępu jest niedostępne:\n"
"%s"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Course"
msgstr "Kurs"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_count
msgid "Course Count"
msgstr "licznik Kursów"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Group Name"
msgstr "Nazwa grupy kursów"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_group_action
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_course_groups
msgid "Course Groups"
msgstr "Grupy Kursowe"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Course Name"
msgstr "Nazwa kursu"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_form
msgid "Course Tag"
msgstr "Znacznik kursu"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Tag Group"
msgstr "grupa znaczników kursu"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Course Tag Groups"
msgstr "grupy znaczników kursu"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_tree
msgid "Course Tags"
msgstr "Znaczniki Kursu"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Course Title"
msgstr "Tytuł Kursu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_finish
msgid "Course finished"
msgstr "Kurs Ukończony"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Course not published yet"
msgstr "Kurs nie opublikowany"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_rank
msgid "Course ranked"
msgstr "Ranking Kursu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_type
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_type
msgid "Course type"
msgstr "Typ Kursu"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Course: %s"
msgstr "Kurs:%s"

#. module: website_slides
#: code:addons/website_slides/models/website.py:0
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_courses
#: model:website.menu,name:website_slides.website_menu_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#, python-format
msgid "Courses"
msgstr "Kursy"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Create"
msgstr "Utwórz"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Create a community and let the members help each others"
msgstr "Stwórz społeczność i pozwól członkom na pomaganie sobie na wzajem"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid "Create a course"
msgstr "Utwórz Kurs"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Create new %s '%s'"
msgstr "Utwórz nowy %s `%s`"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Create new Tag '%s'"
msgstr "Utwórz nowy znacznik '%s'"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "Create new content for your eLearning"
msgstr "Tworzenie nowych treści dla e-learningu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
msgid "Creation Date"
msgstr "Data utworzenia"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__html_content
msgid "Custom HTML content for slides of type 'Web Page'."
msgstr ""

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_6_furn3
msgid "DIY Furniture"
msgstr "Meble dla majsterkowiczów"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Data (nowa do starej)"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Data (stara do nowej)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__null_value
msgid "Default Value"
msgstr "Wartość domyślna"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Definiuj widoczność wyzwania w menu"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#, python-format
msgid "Delete"
msgstr "Usuń"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
#, python-format
msgid "Delete Category"
msgstr "Usuń kategorię"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Delete Question"
msgstr "Usuń Pytanie"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__promote_strategy
msgid ""
"Depending the promote strategy, a slide will appear on the top of the course's page :\n"
" * Latest Published : the slide created last.\n"
" * Most Voted : the slide which has to most votes.\n"
" * Most Viewed ; the slide which has been viewed the most.\n"
" * Specific : You choose the slide to appear.\n"
" * None : No slides will be shown.\n"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Description"
msgstr "Opis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_html
msgid "Detailed Description"
msgstr "Szczegółowy Opis"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_1
msgid "Did you read the whole article ?"
msgstr "Przeczytałeś cały artykuł?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Discard"
msgstr "Odrzuć"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Discover more"
msgstr "Odkryj więcej"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Dislikes"
msgstr "Nielubiane"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Display"
msgstr "Wyświetl"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_question__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__sequence
msgid "Display order"
msgstr "Kolejność wyświetlania"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_0
msgid "Do you make beams out of lemon trees ?"
msgstr "Czy robisz belki z drzew cytrynowych?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_1
msgid "Do you make lemons out of beams ?"
msgstr "Czy robisz cytryny z belek?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Do you really want to leave the course?"
msgstr "Czy na pewno chcesz opuścić kurs?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_0
msgid "Do you think Harry Potted has a good name ?"
msgstr "Czy uważasz, że Harry Potted to dobre imię?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Do you want to install the \"%s\" app?"
msgstr "Czy chcesz zainstalować aplikację \"%s\""

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_5_3_question_0
msgid "Do you want to reply correctly ?"
msgstr "Czy chcesz odpowiedzieć poprawnie?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Do you want to request access to this course ?"
msgstr "Czy chcesz poprosić o dostęp do tego kursu?"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__document
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Document"
msgstr "Dokument"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_id
msgid "Document ID"
msgstr "ID dokumentu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "Document URL"
msgstr "URL dokumentu"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__documentation
msgid "Documentation"
msgstr "Dokumentacja"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Documentation Layout"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_document
#: model:slide.slide,name:website_slides.slide_category_demo_4_0
msgid "Documents"
msgstr "Dokumenty"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_2
msgid "Dog Friendly"
msgstr "Przyjazny dla psów"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Don't have an account ?"
msgstr "Nie masz jeszcze konta?"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Done"
msgstr "Wykonano"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Done !"
msgstr "Zrobione!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__done_count
msgid "Done Count"
msgstr "Ukończono Kurs"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "Pobierz"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download Content"
msgstr "Pobierz Zawartość"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_10
msgid "Drawing 1"
msgstr "Rysunek 1"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_11
msgid "Drawing 2"
msgstr "Rysunek 2"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Dropdown menu"
msgstr "Menu rozwijane"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_time
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__completion_time
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#, python-format
msgid "Duration"
msgstr "Czas trwania"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Edit"
msgstr "Edytuj"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Edit in backend"
msgstr "Edytuj na backendzie"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_email
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Email"
msgstr "E-mail"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid "Email attendees once a new content is published"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__completed_template_id
msgid "Email attendees once they've finished the course"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_template_id
msgid "Email template used when sharing a slide"
msgstr "Szablon e-mail użyty podczas udostępniania slajdu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr "kod osaczony"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embedcount_ids
msgid "Embed Count"
msgstr "kod osaczony"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in your website"
msgstr "Umieść na swojej stronie"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "Osadzony licznik obejrzenia slajdów"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "End course"
msgstr "Skończ Kurs"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_2
msgid "Energy Efficiency Facts"
msgstr "Fakty dotyczące efektywności energetycznej"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Enjoy this exclusive content !"
msgstr "Ciesz się tą ekskluzywną zawartością!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_msg
msgid "Enroll Message"
msgstr "Zarejestruj wiadomość"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_enroll
msgid "Enroll Policy"
msgstr "Zasady rejestracji"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Enrolled On"
msgstr "Zarejestrowany na"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter at least two possible <b>Answers</b>."
msgstr "Wprowadź co najmniej dwie możliwe <b>odpowiedzi</b>."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter your <b>Question</b>. Be clear and concise."
msgstr "Wpisz swoje <b>pytanie</b>. Wyrażaj się jasno i zwięźle."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Enter your answer"
msgstr "Wpisz swoją odpowiedź"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Enter your question"
msgstr "Wpisz swoje pytanie"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Estimated slide completion time"
msgstr "Przewidywany czas konkurencji slajdów"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Evaluate and certify your students."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Evaluate your students and certify them"
msgstr ""

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_exercises
msgid "Exercises"
msgstr "Ćwiczenia"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "External Links"
msgstr "Linki Zewnętrzne"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_link
msgid "External URL for a particular slide"
msgstr "Zewnętrzne URL dla konkretnego slajdu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__link_ids
msgid "External URL for this slide"
msgstr "Zewnętrzny URL dla tej strony"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "External sources"
msgstr "Zewnętrzne Źródła"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Failed to install \"%s\"."
msgstr "Instalacja \"%s\" nie powiodła się"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__model_object_field
msgid "Field"
msgstr "Pole"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/controllers/main.py:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "File is too big. File size cannot exceed 25MB"
msgstr "Plik jest zbyt duży. Rozmiar pliku nie może przekraczać 25 MB"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Filter &amp; order"
msgstr "Filtr &amp; porządek"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr "Wyrażenie, które możesz skopiować do jednego z pól szablonu."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Finish Course"
msgstr "Skończ Kurs"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "First attempt"
msgstr "Pierwsza próba"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"First, create your lesson, then edit it with the website builder. You'll be "
"able to drop building blocks on your page and edit them."
msgstr ""
"Najpierw utwórz lekcję, a następnie edytuj ją w kreatorze stron. Będziesz "
"mógł upuszczać bloki konstrukcyjne na swojej stronie i edytować je."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "First, let's add a <b>Presentation</b>. It can be a .pdf or an image."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "First, upload your videos on YouTube and mark them as"
msgstr "Najpierw prześlij swoje filmy na YouTube i oznacz je jako"

#. module: website_slides
#: code:addons/website_slides/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
#, python-format
msgid "Followed Courses"
msgstr "Obserwowane Kursy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "Obserwatorzy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr "Obserwatorzy (partnerzy)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikona Font awesome np. fa-tasks"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_0
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Foreword"
msgstr "Przedmowa"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_0
msgid "Foreword for this documentation: how to use it, main attention points"
msgstr ""
"Przedmowa do niniejszej dokumentacji: jak z niej korzystać, główne punkty "
"uwagi"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_forum
msgid "Forum"
msgstr "Forum"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Fourth and more attempt"
msgstr "Cztery i więcej prób"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_2
msgid "From a piece of wood to a fully functional furniture, step by step."
msgstr "Od kawałka drewna do w pełni funkcjonalnego mebla, krok po kroku."

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_furniture
msgid "Furniture Designer"
msgstr "Projektant mebli"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_4_furn1
msgid "Furniture Technical Specifications"
msgstr "Specyfikacje techniczne mebli"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_12
msgid "GLork"
msgstr "GLork"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Wyzwanie"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_gardener
msgid "Gardener"
msgstr "Ogrodnik"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_0
msgid "Gardening: The Know-How"
msgstr "Ogrodnictwo: Know-How"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Generate revenues thanks to your courses"
msgstr ""

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_certification_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_certification
msgid "Get a certification"
msgstr "Zdobądź certyfikat"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_register
#: model:gamification.challenge.line,name:website_slides.badge_data_register_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_register_goal
msgid "Get started"
msgstr "Rozpocznij"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course a helpful <b>Description</b>."
msgstr "Nadaj swojemu kursowi pomocny <b>opis</b>."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course an engaging <b>Title</b>."
msgstr "Nadaj swojemu kursowi angażujący <b>tytuł</b>."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr "Klucz Dokumentów Google"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr "Klucz interfejsu Google Drive"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/components/activity/activity.xml:0
#, python-format
msgid "Grant Access"
msgstr "Daj Dostęp"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_graph
msgid "Graph of Contents"
msgstr "Wykres Zawartości"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_id
msgid "Group"
msgstr "Grupa"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "Grupuj wg"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__name
msgid "Group Name"
msgstr "Nazwa grupy"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid "Group of users allowed to publish contents on a documentation course."
msgstr ""
"Grupa użytkowników uprawnionych do publikowania treści w kursie "
"dokumentacji."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_sequence
msgid "Group sequence"
msgstr "Sekwencja grupy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__html_content
msgid "HTML Content"
msgstr "Zawartość HTML"

#. module: website_slides
#: model:ir.model,name:website_slides.model_ir_http
msgid "HTTP Routing"
msgstr "Wytyczanie HTTP"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_1
msgid "Hand on !"
msgstr "Ręka do góry!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_message
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__has_message
msgid "Has Message"
msgstr "Ma wiadomość"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_6
msgid "Here is How to get the Sweetest Strawberries you ever tasted!"
msgstr "Oto jak uzyskać najsłodsze truskawki, jakie kiedykolwiek jadłeś!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Home"
msgstr "Strona główna"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_1
msgid "Home Gardening"
msgstr "Ogrodnictwo domowe"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_2
msgid "How To Build a HIGH QUALITY Dining Table with LIMITED TOOLS"
msgstr ""
"Jak zbudować WYSOKIEJ JAKOŚCI stół do jadalni przy użyciu OGRANICZONEJ "
"ILOŚCI NARZĘDZI?"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_3
msgid "How to Grow and Harvest The Best Strawberries | Basics"
msgstr "Jak uprawiać i zbierać najlepsze truskawki | Podstawy"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_6
msgid ""
"How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks"
msgstr ""
"Jak uprawiać i zbierać najlepsze truskawki | Porady i wskazówki ogrodnicze"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to create a Lesson as a Web Page?"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_1
msgid "How to find quality wood"
msgstr "Jak znaleźć wysokiej jakości drewno"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_3
msgid "How to plant a potted tree"
msgstr "Jak sadzić drzewo w doniczce"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your PowerPoint Presentations or Word Documents?"
msgstr "Jak przesłać prezentacje PowerPoint lub dokumenty Word?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your videos ?"
msgstr "Jak przesyłać filmy?"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_2
msgid "How to wall decorating by tree planting in hanging plastic bottles."
msgstr ""
"Jak udekorować ścianę, sadząc drzewo w wiszących plastikowych butelkach."

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_howto
msgid "HowTo"
msgstr "Jak to zrobić"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_question__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "ID"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona wskazująca na wyjątek aktywności."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__allow_comment
#: model:ir.model.fields,help:website_slides.field_slide_slide__channel_allow_comment
msgid ""
"If checked it allows members to either:\n"
" * like content and post comments on documentation course;\n"
" * post comment and review on training course;"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeśli zaznaczone, nowe wiadomości wymagają twojej uwagi."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Jeśli zaznaczone, niektóre wiadomości napotkały błędy podczas doręczenia."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_4_furn1
msgid ""
"If you are looking for technical specifications, have a look at this "
"documentation."
msgstr ""
"Jeśli szukasz specyfikacji technicznych, zapoznaj się z tą dokumentacją."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"If you want to be sure that attendees have understood and memorized the "
"content, you can add a Quiz on the lesson. Click on <b>Add Quiz</b>."
msgstr ""
"Jeśli chcesz mieć pewność, że uczestnicy zrozumieli i zapamiętali treść, "
"możesz dodać quiz do lekcji. Kliknij przycisk <b>Dodaj quiz</b>."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1920
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1920
msgid "Image"
msgstr "Obraz"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1024
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1024
msgid "Image 1024"
msgstr "Obraz 1024"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_128
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_128
msgid "Image 128"
msgstr "Obraz 128"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_256
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_256
msgid "Image 256"
msgstr "Obraz 256"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_512
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_512
msgid "Image 512"
msgstr "Obraz 512"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_0
msgid "Incorrect !"
msgstr "Niepoprawnie!"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_1
msgid "Incorrect ! A strawberry is not a vegetable."
msgstr "Niepoprawnie! Truskawka nie jest warzywem."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_2
msgid "Incorrect ! A table is a piece of furniture."
msgstr "Niepoprawnie! Stół jest meblem."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_1
msgid "Incorrect ! Good luck digging a hole with a spoon..."
msgstr "Niepoprawnie! Powodzenia w kopaniu dziury łyżką..."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_2
msgid "Incorrect ! Seriously ?"
msgstr "Niepoprawnie! Poważnie?"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_1
msgid "Incorrect ! You better think twice..."
msgstr "Niepoprawnie! Lepiej pomyśl dwa razy..."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_1
msgid "Incorrect ! You really should read it."
msgstr "Niepoprawnie! Naprawdę powinieneś to przeczytać."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_2
msgid "Incorrect ! of course not ..."
msgstr "Incorrect ! Oczywiście, że nie..."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__infographic
msgid "Infographic"
msgstr "Infografika"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographic
msgid "Infographics"
msgstr "Infografiki"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Install"
msgstr "Instaluj"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Installing \"%s\"."
msgstr "Instalowanie \"%s\"."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_0
msgid "Interesting Facts"
msgstr "Interesujące fakty"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_1
msgid "Interesting Tree Facts"
msgstr "Interesujące fakty na temat drzew"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_1
msgid "Interesting information about home gardening. Keep it close !"
msgstr "Ciekawe informacje o przydomowym ogrodnictwie. Trzymaj się blisko!"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_intermediate
msgid "Intermediate"
msgstr "Pośredni"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"Wewnętrzny błąd serwera, spróbuj ponownie później lub skontaktuj się z administratorem.\n"
"Oto komunikat o błędzie: %s"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Invalid file type. Please select pdf or image file"
msgstr "Nieprawidłowy typ pliku. Wybierz plik PDF lub plik obrazu"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Invite"
msgstr "Zaproś"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed
msgid "Is Completed"
msgstr "Jest Ukończone"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "Jest edytorem"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr "Jest obserwatorem"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member
msgid "Is Member"
msgstr "Jest Członkiem"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_new_slide
msgid "Is New Slide"
msgstr "Jest Nowym Slajdem"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is Published"
msgstr "Opublikowane"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_category
msgid "Is a category"
msgstr "Jest kategorią"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__is_correct
msgid "Is correct answer"
msgstr "Jest poprawną odpowiedzią "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_3
msgid ""
"Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and "
"Landscaping. Narrated by Leif Knecht, owner."
msgstr ""
"Jim i Todd sadzą drzewko w doniczce dla klienta Knecht's Nurseries and "
"Landscaping. Narracja: Leif Knecht, właściciel."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Join & Submit"
msgstr "Dołącz i Wyślij"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
#, python-format
msgid "Join Course"
msgstr "Dołącz do Kursu"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Join the Course"
msgstr "Dolącz do Kursu"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Join the course to take the quiz and verify your answers!"
msgstr "Dołacz do Kursu by zrobić quiz i zweryfikować odpowiedzi!"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_2
msgid "Just some basics Energy Efficiency Facts."
msgstr "Kilka podstawowych faktów na temat efektywności energetycznej."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_1
msgid "Just some basics Interesting Tree Facts."
msgstr "Tylko kilka podstawowych interesujących faktów na temat drzew."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_0
msgid "Just some basics Tree Infographic."
msgstr "Tylko kilka podstawowych infografik drzew."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Karma"
msgstr "Karma"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_comment
msgid "Karma needed to add a comment on a slide of this course"
msgstr "Karma wymagana do dodania komentarza na slajdzie tego kursu"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_review
msgid "Karma needed to add a review on the course"
msgstr "Karma wymagana do recenzji tego kursu"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_vote
msgid "Karma needed to like/dislike a slide of this course."
msgstr "Wymagana Karma, aby polubić/nie polubić slajdu tego kursu."

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_profile
#: model:gamification.challenge.line,name:website_slides.badge_data_profile_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_profile_goal
msgid "Know yourself"
msgstr "Znaj Siebie"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_3_furn0
msgid ""
"Knowing which kind of wood to use depending on your application is important. In this course you\n"
"will learn the basics of wood characteristics."
msgstr ""
"Ważne jest, aby wiedzieć, jakiego rodzaju drewna użyć w zależności od zastosowania. W tym kursie\n"
"poznasz podstawy charakterystyki drewna."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_3
msgid ""
"Knowing wood characteristics is a requirement in order to know which kind of"
" wood to use in a given situation."
msgstr ""
"Znajomość właściwości drewna jest niezbędna, aby wiedzieć, jakiego rodzaju "
"drewna użyć w danej sytuacji."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__lang
msgid "Language"
msgstr "Język"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Action On"
msgstr "Ostatnia akcja na"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_embed____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_question____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_tag____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_last_update
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Last Update"
msgstr "Ostatnia aktualizacja"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__latest
msgid "Latest Published"
msgstr "Ostatnio opublikowany"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "Latest achievements"
msgstr "Ostatnie Osiągnięcie"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "Leaderboard"
msgstr "Tablica Wyników"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_1_gard1
msgid ""
"Learn how to take care of your favorite trees. Learn when to plant, how to "
"manage potted trees, ..."
msgstr ""
"Dowiedz się, jak dbać o swoje ulubione drzewa. Dowiedz się, kiedy sadzić, "
"jak pielęgnować drzewa doniczkowe, ..."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_0_gard_0
msgid "Learn the basics of gardening !"
msgstr "Naucz się podstaw ogrodnictwa!"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_1
msgid "Learn to identify quality wood in order to create solid furnitures."
msgstr ""
"Naucz się rozpoznawać drewno wysokiej jakości, aby tworzyć solidne meble."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Leave the course"
msgstr "Opuść Kurs"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Lesson"
msgstr "Lekcja"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Lesson Nav"
msgstr "Lekcja Nav"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_slide_vote
msgid "Lesson voted"
msgstr "Przegłosowana lekcja"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Lessons"
msgstr "Lekcje"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz_finish.js:0
#, python-format
msgid "Level up!"
msgstr "Podniosłeś swój Poziom!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Likes"
msgstr "Polubienia"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__link
msgid "Link"
msgstr "Odnośnik"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__template_id
msgid "Mail Template"
msgstr "Szablon wiadomości"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_mass_mailing_slides
msgid "Mailing"
msgstr "Wysyłanie wiadomości"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_main_attachment_id
msgid "Main Attachment"
msgstr "Główny załącznik"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_0
msgid "Main Trees Categories"
msgstr "Główne kategorie drzew"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_manager
msgid "Manager"
msgstr "Menedżer"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Mark the correct answer by checking the <b>correct</b> mark."
msgstr "Zaznacz prawidłową odpowiedź, zaznaczając <b>właściwy</b> znak."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Member"
msgstr "Członek"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_ids
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Members"
msgstr "Członkowie"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_ids
msgid "Members Information"
msgstr "Informacja o członku"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__members
msgid "Members Only"
msgstr "Tylko członkowie"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Members Views"
msgstr "Podgląd Członków"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_group_ids
msgid ""
"Members of those groups are automatically added as members of the channel."
msgstr "Członkowie tych grup są automatycznie dodawani jako członkowie kanału"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Menu Entry"
msgstr "Wpis w Menu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr "Błąd doręczenia wiadomości"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_msg
msgid "Message explaining the enroll process"
msgstr "Komunikat wyjaśniający proces rejestracji"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "Wiadomości"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_1
msgid "Methods"
msgstr "Metody"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_2
msgid "Mighty Carrots"
msgstr "Potężne marchewki"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_1
msgid ""
"Mighty forest just don't appear in a few weeks. Learn how time made our "
"forests mighty and mysterious."
msgstr ""
"Potężne lasy nie powstają w ciągu kilku tygodni. Dowiedz się, jak czas "
"uczynił nasze lasy potężnymi i tajemniczymi."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__mime_type
msgid "Mime-type"
msgstr "Typ-mime"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Minutes"
msgstr "Minuty"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Missing \"Tag Group\" for creating a new \"Tag\"."
msgstr "Brakująca \"Grupa tagów\" do tworzenia nowego \"Tagu\"."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Mobile sub-nav"
msgstr "Mobilna nawigacja podrzędna"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "More info"
msgstr "Więcej informacji"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_viewed
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Most Viewed"
msgstr "Najczęściej oglądany"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_voted
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Most Voted"
msgstr "Najczęściej głosowane"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Most popular courses"
msgstr "Najpopularniejsze Kursy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Ostateczny terminin moich aktywności"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "My Courses"
msgstr "Moje Kursy"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "My courses"
msgstr "Moje kursy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
msgid "Name"
msgstr "Nazwa"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Nav"
msgstr "Nav"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "New"
msgstr "Nowe"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "New Certification"
msgstr "Nowy Certyfikat"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "New Content Email"
msgstr "Email Nowej Zawartości"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "New Course"
msgstr "Nowy Kurs"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid "New {{ object.slide_type }} published on {{ object.channel_id.name }}"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Newest"
msgstr "Najnowsze"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Newest courses"
msgstr "Najnowsze Kursy"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Next"
msgstr "Następny"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Następna Czynność wydarzenia w kalendarzu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Termin kolejnej czynności"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_summary
msgid "Next Activity Summary"
msgstr "Podsumowanie kolejnej czynności"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_id
msgid "Next Activity Type"
msgstr "Typ następnej czynności"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Next rank:"
msgstr "Następna Ranga;"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_1
#, python-format
msgid "No"
msgstr "Nie"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "No Course created yet."
msgstr "Brak utworzonych Kursów"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No completed courses yet!"
msgstr "Brak ukończonych Kursów"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "No content was found using your search"
msgstr "Nie znaleziono żadnych treści przy użyciu wyszukiwania"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search"
msgstr "Nie znaleziono kursu pasującego do wyszukiwania"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search."
msgstr "Nie znaleziono kursu pasującego do wyszukiwania."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "No data yet!"
msgstr "Brak danych!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No followed courses yet!"
msgstr "Brak Obserwowanych Kursów!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "No leaderboard currently :("
msgstr "Brak tablicy wyników"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "No presentation available."
msgstr "Brak prezentacji."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No results found for '"
msgstr "Nie znaleziono wyników dla '"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__none
msgid "None"
msgstr "Brak"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Not enough karma to comment"
msgstr "Zbyt mała liczba punktów karma aby komentować"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Not enough karma to review"
msgstr "Niewystarczająca ilość karmy by wyświetlić"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Notifications"
msgstr "Notyfikacje"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr "Liczba akcji"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_document
msgid "Number of Documents"
msgstr "Liczba dokumentów"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_infographic
msgid "Number of Infographics"
msgstr "Ilość Infografik"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_presentation
msgid "Number of Presentations"
msgstr "Ilość Prezentacji"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_quiz
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_quiz
msgid "Number of Quizs"
msgstr "Ilość quizów"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_video
msgid "Number of Videos"
msgstr "Ilość Wideo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_webpage
msgid "Number of Webpages"
msgstr "Ilość stron"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__comments_count
msgid "Number of comments"
msgstr "Ilość Komentarzy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of errors"
msgstr "Liczba błędów"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Liczba wiadomości wymagających akcji"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Liczba wiadomości z błędami przy doręczeniu"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread_counter
msgid "Number of unread messages"
msgstr "Liczba nieprzeczytanych wiadomości"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__questions_count
msgid "Numbers of Questions"
msgstr "Ilość Pyrań"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Odoo"
msgstr "Odoo"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Odoo • Image and Text"
msgstr "Odoo • Obraz i tekst"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_officer
msgid "Officer"
msgstr "Urzędnik"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__invite
msgid "On Invitation"
msgstr "Na zaproszenie"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Once you're done, don't forget to <b>Publish</b> your course."
msgstr "Po zakończeniu nie zapomnij <b>opublikować</b> swojego kursu."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Only JPG, PNG, PDF, files types are supported"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Opcjonalny język tłumaczenia (kod ISO) do wyboru podczas wysyłania "
"wiadomości e-mail. Jeśli nie jest ustawiony, zostanie użyta wersja "
"angielska. Powinno to być zazwyczaj wyrażenie placeholder, które zapewnia "
"odpowiedni język, np. {{ object.partner_id.lang }}."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Opcjonalna wartość do zastosowania, jeśli pole docelowe jest puste"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Options"
msgstr "Opcje"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__partner_id
msgid "Partner"
msgstr "Kontrahent"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_has_new_content
msgid "Partner Has New Content"
msgstr "Partner ma nową zawartość"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"People already took this quiz. To keep course progression it should not be "
"deleted."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__copyvalue
msgid "Placeholder Expression"
msgstr "Wyrażenie zastępcze"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Please"
msgstr "Proszę"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to join this course"
msgstr ""
"Proszę <a href=\"/web/login?redirect=%s\">zaloguj się</a> by dołączyć do "
"tego kursu"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to vote this lesson"
msgstr ""
"Proszę <a href=\"/web/login?redirect=%s\">zaloguj się</a>by głosować na "
"lekcję"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to join "
"this course"
msgstr ""
"Proszę <a href=\"/web/signup?redirect=%s\">utwórz konto</a> by dołaczyć do "
"tego kursu"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to vote "
"this lesson"
msgstr ""
"Proszę <a href=\"/web/signup?redirect=%s\">utwórz konto</a> by głosować na "
"lekcję"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Please enter valid Youtube or Google Doc URL"
msgstr "Wprowadź poprawny adres URL serwisu YouTube lub Google Doc"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Please enter valid youtube or google doc url"
msgstr "Wprowadź prawidłowy adres URL YouTube lub dokumentów Google"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz_question_form.js:0
#, python-format
msgid "Please fill in the question"
msgstr "Prosimy o wypełnienie pytania"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "Proszę uzupełnić to pole"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Please select at least one recipient."
msgstr "Wybierz co najmniej jednego odbiorcę."

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_course
#: model:gamification.challenge.line,name:website_slides.badge_data_course_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_course_goal
msgid "Power User"
msgstr "Power User"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Powered by"
msgstr "Zasilane przez"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__presentation
#: model:slide.slide,name:website_slides.slide_slide_demo_4_12
#, python-format
msgid "Presentation"
msgstr "Prezentacja"

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "Prezentacja opublikowana"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_presentation
msgid "Presentations"
msgstr "Prezentacje"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Preview"
msgstr "Podgląd"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Private Course"
msgstr "Prywatny Kurs"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Progress"
msgstr "Postęp"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Promoted Content"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promoted_slide_id
msgid "Promoted Slide"
msgstr "Promowany slajd"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__public
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__public
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Public"
msgstr "Publiczne"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Public Views"
msgstr "Publiczne Wyświetlenia"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Data publikacji"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr "Data publikacji"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "Opublikowano"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Published Date"
msgstr "Data publikacji"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Publishing is restricted to the responsible of training courses or members "
"of the publisher group for documentation courses"
msgstr ""
"Publikowanie jest ograniczone do osób odpowiedzialnych za kursy szkoleniowe "
"lub członków grupy wydawców kursów dokumentacyjnych."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__question_id
msgid "Question"
msgstr "Pytanie"

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer"
msgstr "Pytanie \"%s\" musi mieć 1 poprawną odpowiedź "

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer and at least 1 incorrect answer"
msgstr ""
"Pytanie \"%s\" musi posiadać 1 poprawną odpowiedź i przynajmniej 1 "
"niepoprawną odpowiedź"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__question
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Question Name"
msgstr "Nazwa pytania"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__question_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Questions"
msgstr "Pytania"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__quiz
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Quiz"
msgstr "Quiz"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Quiz Demo Data"
msgstr "Dane demonstracyjne quizu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__quiz_attempts_count
msgid "Quiz attempts count"
msgstr "Liczba prób rozwiązania quizu"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_question_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_quizzes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree_report
msgid "Quizzes"
msgstr "Quizy"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_ids
msgid "Rating"
msgstr "Ocena"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_graph_slide_channel
msgid "Rating Average"
msgstr "Średnia Rankingu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_stars
msgid "Rating Average (Stars)"
msgstr "Średnia Rankingu (Gwiazdy)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Ocena ostatniego komentarza"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Ocena ostatniego obrazu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Ocena ostatniej wartości"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_count
msgid "Rating count"
msgstr "Ilość ocen"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Rating of %s"
msgstr "Oceny %s"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_karma_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_karma
msgid "Reach 2000 XP"
msgstr "Zdobądź 2000 XP"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Reach new heights"
msgstr "Osiągnij nowy poziom"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "Powód dla oceny"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__partner_ids
msgid "Recipients"
msgstr "Odbiorcy"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/components/activity/activity.xml:0
#, python-format
msgid "Refuse Access"
msgstr "Odmów Dostępu"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_register_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_register
msgid "Register to the platform"
msgstr "Zarejstruj się do platformy"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Related"
msgstr "Powiązane"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Remove"
msgstr "Usuń"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove the answer comment"
msgstr "Usuń komentarz odpowiedzi"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove this answer"
msgstr "Usuń odpowiedź"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__render_model
msgid "Rendering Model"
msgstr "Model renderowania"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report
msgid "Reporting"
msgstr "Raportowanie"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request Access."
msgstr "Poproś o dostęp"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request sent !"
msgstr "Prośba wysłana!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Reset"
msgstr "Zresetuj"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__data
msgid "Resource"
msgstr "Zasób"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Resources"
msgstr "Zasoby"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__user_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_user_id
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Responsible"
msgstr "Odpowiedzialny"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_user_id
msgid "Responsible User"
msgstr "Użytkownik odpowiedzialny"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Responsible already contacted."
msgstr "Odpowiedzialni już się skontaktowali."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict publishing to this website."
msgstr "Ogranicz publikowanie do tej strony."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Retry"
msgstr "Ponów"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.private_profile
msgid "Return to the course."
msgstr "Wróć do kursu"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Review"
msgstr "Recenzja"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Review Course"
msgstr "Kurs przeglądowy"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_reviews
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_reviews
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Reviews"
msgstr "Opinie"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/portal_chatter.js:0
#, python-format
msgid "Reviews (%d)"
msgstr "Recenzje (%d)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_fourth_attempt_reward
msgid "Reward: every attempt after the third try"
msgstr "Nagroda: każda próba po trzeciej próbie"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_first_attempt_reward
msgid "Reward: first attempt"
msgstr "Nagroda: pierwsza próba"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_second_attempt_reward
msgid "Reward: second attempt"
msgstr "Nagroda: druga próba"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_third_attempt_reward
msgid "Reward: third attempt"
msgstr "Nagroda: trzecia próba"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Rewards"
msgstr "Nagrody"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr "Zoptymalizowane SEO"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Błąd dostarczenia wiadomości SMS"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Save"
msgstr "Zapisz"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Save & Publish"
msgstr "Zapisz i opublikuj"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Save your presentations or documents as PDF files and upload them."
msgstr "Zapisz swoje prezentacje lub dokumenty jako pliki PDF i prześlij je."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search"
msgstr "Szukaj"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Contents"
msgstr "Szukaj zawartości"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search courses"
msgstr "Szukaj Kursów "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search in content"
msgstr "Szukaj w zawartości"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Second attempt"
msgstr "Drugie podejście"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
#, python-format
msgid "Section"
msgstr "Sekcja"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Section Subtitle"
msgstr "Podtytuł sekcji"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Section name"
msgstr "Nazwa Sekcji"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_token
msgid "Security Token"
msgstr "Token uprawnień"

#. module: website_slides
#: code:addons/website_slides/models/res_users.py:0
#, python-format
msgid "See our eLearning"
msgstr "Zobacz nasze e-learningi"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Select <b>Course</b> to create it and manage it."
msgstr "Wybierz <b>Kurs</b>, aby go utworzyć i nim zarządzać."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Select page to start with"
msgstr "Wybierz stronę, od której chcesz zacząć"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Wybierz pole docelowe z powiązanego modelu dokumentu.\n"
"Jeśli to pole jest  typu relacja to będzie można wybrać pole w docelowej relacji."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Select the correct answer below :"
msgstr "Wybierz i popraw odpowiedź poniżej: "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_sale_slides
msgid "Sell on eCommerce"
msgstr "Sprzedaj w eCommerce"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Send"
msgstr "Wyślij"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Send Email"
msgstr "Wyślij e-mail"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__seo_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__seo_name
msgid "Seo name"
msgstr "Nazwa SEO"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_question__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__sequence
msgid "Sequence"
msgstr "Sekwencja"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Set Done"
msgstr "Ustaw Wykonane"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.website_slides_action_settings
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_settings
msgid "Settings"
msgstr "Ustawienia"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Share"
msgstr "Udostępnij"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share Channel"
msgstr "Udostępnij Kanał"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
#, python-format
msgid "Share Link"
msgstr "Link do udostępniania"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_template_id
msgid "Share Template"
msgstr "Udostępnij Szablon"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Share by mail"
msgstr "Udostępnij przez e-mail"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Facebook"
msgstr "Udostępnij na Facebook"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on LinkedIn"
msgstr "Udostępnij na LinkedIn"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_body
#, python-format
msgid "Share on Social Networks"
msgstr "Udostępnij przez Social Media"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Twitter"
msgstr "Udostępnij na Twitter"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_short
msgid "Short Description"
msgstr "Krótki Opis"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_4
msgid "Show your newly mastered knowledge !"
msgstr "Pokaż swoją nowo zdobytą wiedzę!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign Up !"
msgstr "Zarejestruj się!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Sign in"
msgstr "Zaloguj się"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign in and join the course to verify your answers!"
msgstr "Zarejestruj się i dołącz do kursu by zweryfikować swoje odpowiedzi!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Size"
msgstr "Rozmiar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Skill up and have an impact! Your business career starts here.<br/>Time to "
"start a course."
msgstr ""
"Zdobądź umiejętności i miej wpływ! Twoja kariera biznesowa zaczyna się "
"tutaj.<br/> Czas rozpocząć kurs."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__slide_id
msgid "Slide"
msgstr "Slajd"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "Slajd / Partner urządził m2m"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_published
msgid "Slide Published"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_answer
msgid "Slide Question's Answer"
msgstr "Odpowiedź pytania slajdu"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_shared
msgid "Slide Shared"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "Tagi slajdów"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_partner_ids
msgid "Slide User Data"
msgstr "dane użytkownika ślajdu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_id
msgid "Slide channel"
msgstr "Kanał slajdu"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#, python-format
msgid "Slide image"
msgstr "Obrazek slajdu"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Slide with questions must be marked as done when submitting all good answers"
" "
msgstr ""
"Slajd z pytaniami musi zostać oznaczony jako ukończony po przesłaniu "
"wszystkich dobrych odpowiedzi."

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_content_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_ids
msgid "Slides"
msgstr "Slajdy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
msgid "Slides and categories"
msgstr "Slajdy i kategorie"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_6_furn3
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_6_furn3
msgid "So much amazing certification."
msgstr "Tyle niesamowitych certyfikatów."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Sort by"
msgstr "Sortuj wg"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__specific
msgid "Specific"
msgstr "Określone"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Start Course"
msgstr "Rozpocznij kurs"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Start Course Channel"
msgstr "Rozpocznij Kanał Kursu"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Zacznij od klienta - dowiedz się, czego chce i daj mu to."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Start your online course today!"
msgstr "Rozpocznij kurs online już dziś!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Statistics"
msgstr "Statystyki"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status na podstawie czynności\n"
"Zaległe: Termin już minął\n"
"Dzisiaj: Data czynności przypada na dzisiaj\n"
"Zaplanowane: Przyszłe czynności."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__sub_model_object_field
msgid "Sub-field"
msgstr "Podpole"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__sub_object
msgid "Sub-model"
msgstr "Podmodel"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__subject
msgid "Subject"
msgstr "Temat"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Subject..."
msgstr "Temat..."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Subscribe"
msgstr "Subskrybuj"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information"
msgstr "Informacje subskrybenta"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information for the current logged in user"
msgstr "Informacje subskrybenta dostępne dla zalogowanego użytkownika"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__partner_ids
msgid "Subscribers"
msgstr "Subskrybenci"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_partner_ids
msgid "Subscribers information"
msgstr "Informacje Subskrybentów"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Succeed and gain karma"
msgstr "Wygrywaj i zdobywaj karmę"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
#, python-format
msgid "Tag"
msgstr "Tag"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#, python-format
msgid "Tag Group"
msgstr "Oznacz Grupę"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Tag Group (required for new tags)"
msgstr "Oznacz Grup (wymagane dla nowych oznaczeń)"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Tag Name"
msgstr "Nazwa tagu"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag__color
msgid ""
"Tag color used in both backend and website. No color means no display in "
"kanban or front-end, to distinguish internal tags from public categorization"
" tags"
msgstr ""
"Kolor tagu używany zarówno w backendzie, jak i na stronie internetowej. Brak"
" koloru oznacza brak wyświetlania w kanban lub front-end, aby odróżnić "
"wewnętrzne tagi od publicznych tagów kategoryzacji."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_data_other
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
#, python-format
msgid "Tags"
msgstr "Tagi"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "Tagi..."

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_1_gard1
msgid "Taking care of Trees"
msgstr "Dbanie o drzewa"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_4_1
msgid "Technical Drawings"
msgstr "Rysunki techniczne"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_10
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_11
msgid "Technical drawing"
msgstr "Rysunek techniczny"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_2
msgid "Test Yourself"
msgstr "Sprawdź się"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_4
msgid "Test your knowledge"
msgstr "Sprawdź swoją wiedzę"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_3
msgid "Test your knowledge !"
msgstr "Sprawdź swoją wiedzę!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Test your students with small Quizzes"
msgstr "Sprawdź swoich uczniów za pomocą małych quizów"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Duration</b> of the lesson is based on the number of pages of your "
"document. You can change this number if your attendees will need more time "
"to assimilate the content."
msgstr ""
"<b>Czas trwania</b> lekcji zależy od liczby stron dokumentu. Możesz zmienić "
"tę liczbę, jeśli uczestnicy będą potrzebować więcej czasu na przyswojenie "
"treści."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Title</b> of your lesson is autocompleted but you can change it if "
"you want.</br>A <b>Preview</b> of your file is available on the right side "
"of the screen."
msgstr ""
"<b>Tytuł</b> lekcji jest uzupełniany automatycznie, ale możesz go zmienić, "
"jeśli chcesz.</br> <b>Podgląd pliku</b> jest dostępny po prawej stronie "
"ekranu."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr ""
"Kurs jest dostępny dla wszystkich : Użytkownik nie musi dołączać do kanału "
"by mieć dostęp do zawartości tego kursu."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description_short
msgid "The description that is displayed on the course card"
msgstr "Opis, który jest wyświetlany na karcie kursu"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description
msgid ""
"The description that is displayed on top of the course page, just below the "
"title"
msgstr "Opis wyświetlany w górnej części strony kursu, tuż pod tytułem."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"The document type will be set automatically based on the document URL and "
"properties (e.g. height and width for presentation and document)."
msgstr ""
"Typ dokumentu zostanie ustawiony automatycznie na podstawie adresu URL "
"dokumentu i właściwości (na przykład wysokości i szerokości dla prezentacji "
"i dokumentu)."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__completion_time
msgid "The estimated completion time for this slide"
msgstr "Przewidywany czas ukończenia dla tego slajdu"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external sign up in configuration."
msgstr ""
"Następujący odbiorcy nie mają konta użytkownika: %s. Należy utworzyć dla "
"nich konta użytkowników lub zezwolić na rejestrację zewnętrzną w "
"konfiguracji."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr "Pełny adres URL dostępu do dokumentu przez stronę."

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_theory
msgid "Theory"
msgstr "Teoria"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "There are no quizzes"
msgstr "Nie ma żadnych quizów"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel_report
msgid "There are no ratings for these courses at the moment"
msgstr "W tej chwili nie ma oceny dla tych kursów"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr "Wystąpił błąd podczas ratyfikowania tego quizu"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr "zewnętrzny adres URL"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Third attempt"
msgstr "Trzecia próba"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_answer__comment
msgid "This comment will be displayed to the user if he selects this answer"
msgstr ""
"Ten komentarz zostanie wyświetlony użytkownikowi jeśli wybierze tą odpowiedź"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "This course is private."
msgstr "Ten kurs jest prywatny."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer"
msgstr "To jest poprawna odpowiedź"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer, congratulations"
msgstr "To jest poprawna odpowiedź, gratulacje"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr "Ten quiz został już ukończony. Ponowne ukończenie jest niemożliwe"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This video already exists in this channel on the following slide: %s"
msgstr "Film już istnieje na tym kanale na slajdzie: %s"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__name
#, python-format
msgid "Title"
msgstr "Tytuł"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Toggle navigation"
msgstr "Przełącz nawigację"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_tools
msgid "Tools"
msgstr "Narzędzia"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_0
msgid "Tools and Methods"
msgstr "Narzędzia i metody"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_0
msgid "Tools you will need to complete this course."
msgstr "Narzędzia potrzebne do ukończenia tego kursu."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Total"
msgstr "Suma"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_slides
msgid "Total Slides"
msgstr "Całkowita liczba Slajdów"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Total Views"
msgstr "Całkowita liczba Wyświetleń"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__training
msgid "Training"
msgstr "Szkolenie"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Training Layout"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_0
msgid "Tree Infographic"
msgstr "Infografika drzewa"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_2
msgid "Tree planting in hanging bottles on wall"
msgstr "Sadzenie drzew w wiszących butelkach na ścianie"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_0
msgid "Trees"
msgstr "Drzewa"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_2_gard2
msgid "Trees, Wood and Gardens"
msgstr "Drzewa, drewno i ogrody"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "Typ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ wyjątku działania na rekordzie."

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "Nie można wysłać wiadomości, skonfiguruj adres e-mail nadawcy."

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#, python-format
msgid "Uncategorized"
msgstr "Bez kategorii"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_0
msgid "Unforgettable Tools"
msgstr "Niezapomniane narzędzia"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Unknown document"
msgstr "Nieznany dokument"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Unknown error"
msgstr "Nieokreślony błąd"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Unknown error, try again."
msgstr "Nieznany błąd, spróbuj jeszcze raz"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Unpublished"
msgstr "Nieopublikowane"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread
msgid "Unread Messages"
msgstr "Nieprzeczytane wiadomości"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Licznik nieprzeczytanych wiadomości"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Update"
msgstr "Aktualizacja"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr "Prześlij grupy"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "Upload Presentation"
msgstr "Załaduj prezentację"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Upload a document"
msgstr "Prześlij dokument"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_id
msgid "Uploaded by"
msgstr "Przesłane przez"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Uploading document ..."
msgstr "Przesyłanie dokumentu ..."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__tag_ids
msgid "Used to categorize and filter displayed channels/courses"
msgstr "Używane do kategoryzowania i filtrowania wyświetlanych kanałów/kursów"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__color
msgid "Used to decorate kanban view"
msgstr "Służy do dekorowania widoku kanban"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_vote
msgid "User vote"
msgstr "Głos Użytkownika"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_users
msgid "Users"
msgstr "Użytkownicy"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__video
#, python-format
msgid "Video"
msgstr "Wideo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_video
msgid "Videos"
msgstr "Wideo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "View"
msgstr "Widok"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "View all"
msgstr "Wyświetl wszystko"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "View course"
msgstr "Wyświetl kurs"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Views"
msgstr "Widoki"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_card
msgid "Views •"
msgstr "Wyświetlenia •"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_visibility
msgid "Visibility"
msgstr "Widoczność"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr "Widoczne na obecnej stronie"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Visits"
msgstr "Odwiedzin"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_vote
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__vote
msgid "Vote"
msgstr "Głosuj"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_votes
msgid "Votes"
msgstr "Głosy"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Votes and comments are disabled for this course"
msgstr "Głosy i komentarze są wyłączone dla tego kursu"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "Oczekiwanie na weryfikację"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_1
msgid "Watching the master(s) at work"
msgstr "Obserwowanie mistrzów przy pracy"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_4
msgid ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say !"
msgstr ""
"Pogawędziliśmy trochę z Harrym Pottedem, na pewno miał ciekawe rzeczy do "
"powiedzenia!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__webpage
#, python-format
msgid "Web Page"
msgstr "Strona internetowa"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_webpage
msgid "Webpages"
msgstr "Strony Internetowe"

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
msgid "Website"
msgstr "Strona internetowa"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__gamification_challenge__challenge_category__slides
msgid "Website / Slides"
msgstr "Strona internetowa / Slajdy"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr "Wiadomości"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr "Adres strony internetowej"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr "Historia komunikacji"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr "Opis strony - meta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr "Słowa kluczowe dla strony - meta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr "Tytuł strony - meta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr "Obrazek typu opengraph dla strony"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Welcome on your course's home page. It's still empty for now. Click on "
"\"<b>New</b>\" to write your first course."
msgstr ""
"Witamy na stronie głównej kursu. Na razie jest ona pusta. Kliknij "
"\"<b>Nowy</b>\", aby napisać swój pierwszy kurs."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "What does"
msgstr "Co oznacza"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_0
msgid "What is a strawberry ?"
msgstr "Co to jest truskawka?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_1
msgid "What is the best tool to dig a hole for your plants ?"
msgstr "Jakie jest najlepsze narzędzie do kopania dołków pod rośliny?"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_2
msgid "What was the question again ?"
msgstr "Jakie było pytanie?"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Jeśli pole relacji jest wybrane jako pierwsze, to to pole pozwala ci wybrać "
"pole docelowe w docelowym modelu (podmodelu)."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Kiedy pole relacji jest wybrane jako pierwsze, to pole pokazuje model, "
"którego relacja dotyczy."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_1
msgid ""
"Which wood type is best for my solid wood furniture? That's the question we "
"help you answer in this video !"
msgstr ""
"Który rodzaj drewna jest najlepszy dla moich mebli z litego drewna? To "
"pytanie, na które pomożemy Ci odpowiedzieć w tym filmie!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"With Quizzes you can keep your students focused and motivated by answering "
"some questions and gaining some karma points"
msgstr ""
"Dzięki quizom możesz utrzymać koncentrację i motywację uczniów, odpowiadając"
" na pytania i zdobywając punkty karmy"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_1
msgid "Wood"
msgstr "Drewno"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_1
msgid "Wood Bending With Steam Box"
msgstr "Gięcie drewna za pomocą skrzynki parowej"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_3
msgid "Wood Characteristics"
msgstr "Charakterystyka drewna"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_1
msgid "Wood Types"
msgstr "Rodzaje drewna"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_3_0
msgid "Working with Wood"
msgstr "Praca z drewnem"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product or services. <br>To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Napisz jeden lub dwa akapity opisujące Twój produkt lub usługi. <br> Aby "
"odnieść sukces, treść musi być przydatna dla czytelników."

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Napisz jeden lub dwa akapity opisujące Twój produkt, usługi lub konkretną "
"funkcję. <br> Aby odnieść sukces, treść musi być przydatna dla czytelników."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "XP"
msgstr "XP"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_0
#, python-format
msgid "Yes"
msgstr "Tak"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid ""
"You are not allowed to add members to this course. Please contact the course"
" responsible or an administrator."
msgstr ""
"Nie możesz dodawać członków do tego kursu. Skontaktuj się z osobą "
"odpowiedzialną za kurs lub z administratorem."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"You can add <b>comments</b> on answers. This will be visible with the "
"results if the user select this answer."
msgstr ""
"Możesz dodawać <b>komentarze</b> do odpowiedzi. Będą one widoczne wraz z "
"wynikami, jeśli użytkownik wybierze tę odpowiedź."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "You can not upload password protected file."
msgstr "Nie możesz przesłać pliku chronionego hasłem."

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot add tags to this course."
msgstr "Nie możesz dodawać znaczników do tego kursu"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as completed if you are not among its members."
msgstr ""
"Nie możesz zaznaczyć Slajdu jako ukończony jeżeli nie jesteś jego członkiem"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as viewed if you are not among its members."
msgstr ""
"Nie możesz zaznaczyć Slajdu jako wyświetlony jeżeli nie jesteś jego "
"członkiem."

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"You cannot mark a slide quiz as completed if you are not among its members."
msgstr ""
"Nie możesz zaznaczyć quizu slajdu jako ukończony jeżeli nie jesteś jego "
"członkiem."

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot upload on this channel."
msgstr "Nie możesz przesyłać na tym kanale"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have access to this lesson"
msgstr "Nie masz dostępu do tej lekcji"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have enough karma to vote"
msgstr "Nie masz wystarczająco karmy do głosowania"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "You gained"
msgstr "Zdobyłeś"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "You have already joined this channel"
msgstr "Już dołączyłeś do tego kanału"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You have already voted for this lesson"
msgstr "Już zagłosowałeś na tą lekcję"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_invite
msgid "You have been invited to join {{ object.channel_id.name }}"
msgstr "Zostałeś zaproszony do dołączenia do {{ object.channel_id.name }}."

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "You have to sign in before"
msgstr "Musisz się najpierw zarejstować"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "You may now participate in our eLearning."
msgstr "Możesz teraz wziąć udział w naszym e-learningu."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "You must be logged to submit the quiz."
msgstr "Musisz być zalogowany by przesłac quiz"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You must be member of this course to vote"
msgstr "Musisz być członkiem tego kursu by głosować"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_2
msgid "You won't believe those facts about carrots."
msgstr "Nie uwierzysz w te fakty dotyczące marchewki."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "You're enrolled"
msgstr "Jesteś zarejestrowany"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Your"
msgstr "Twój"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Your Google API key is invalid, please update it in your settings.\n"
"Settings > Website > Features > API Key"
msgstr ""
"Twój klucz API Google jest nieprawidłowy, zaktualizuj go w ustawieniach.\n"
"Ustawienia > Witryna > Funkcje > Klucz API"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_level
msgid "Your Level"
msgstr "Twój poziom"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_role
msgid "Your Role"
msgstr "Twoja rola"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Your first section is created, now it's time to add lessons to your course. "
"Click on <b>Add Content</b> to upload a document, create a web page or link "
"a video."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Link"
msgstr "Link Youtube"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Video URL"
msgstr "Youtube Video URL"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_id
msgid "Youtube or Google Document ID"
msgstr "Youtube lub Google Document ID"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "Youtube or Google Document URL"
msgstr "Youtube lub Google Document URL"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.achievement_card
msgid "achieved"
msgstr "zdobyto"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "breadcrumb"
msgstr "okruszek"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "by email."
msgstr "przez email."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "e.g. Computer Science for kids"
msgstr "np. Informatyka dla dzieci"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. How to grow your business with Odoo?"
msgstr "np. Jak rozwijać swój biznes z Odoo?"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid ""
"e.g. In this video, we'll give you the keys on how Odoo can help you to grow"
" your business. At the end, we'll propose you a quiz to test your knowledge."
msgstr ""
"np. W tym filmie pokażemy ci jak Odoo może pomóc ci rozwinąć swój biznes. Na"
" koniec zaproponujemy ci quiz by sprawdzić swoją wiedzę"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "e.g. Introduction"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "e.g. Your Level"
msgstr "np. Twój poziom"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. https://www.odoo.com"
msgstr "np. https://www.odoo.com"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_root
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "eLearning"
msgstr "eNauka"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_ids
msgid "eLearning Courses"
msgstr "Kursy eNauki"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_overview
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "eLearning Overview"
msgstr "Przegląd eNauki"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "login"
msgstr "login"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"means? The YouTube \"unlisted\" means it is a video which can be viewed only"
" by the users with the link to it. Your video will never come up in the "
"search results nor on your channel."
msgstr ""
"oznacza? YouTube \"unlisted\" oznacza, że jest to film, który może być "
"oglądany tylko przez użytkowników z linkiem do niego. Twoje wideo nigdy nie "
"pojawi się w wynikach wyszukiwania ani na Twoim kanale."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "or Leave the course"
msgstr "lub Opuść kurs"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "reviews"
msgstr "recenzje"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#, python-format
msgid "sign in"
msgstr "Zaloguj"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid "steps"
msgstr "kroki"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "to contact responsible."
msgstr "aby skontaktować się z osobą odpowiedzialną."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "to download resources"
msgstr "do pobrania zasobów"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "to enroll."
msgstr "aby się zapisać."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "to share this"
msgstr "By udostępnić"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "unlisted"
msgstr "unlisted"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "xp"
msgstr "xp"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "{{ user.name }} shared a {{ object.slide_type }} with you!"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "└<span class=\"ml-1\">Uncategorized</span>"
msgstr "└<span class=\"ml-1\">Nieskategoryzowane</span>"
