# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_transfer
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>ling-Nesselbosch, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_transfer
#: model_terms:ir.ui.view,arch_db:payment_transfer.transfer_transaction_status
msgid "<strong>Communication: </strong>"
msgstr "<strong>Kommunikation: </strong>"

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__qr_code
msgid "Enable QR Codes"
msgstr "QR-Codes freigeben"

#. module: payment_transfer
#: model:ir.model.fields,help:payment_transfer.field_payment_acquirer__qr_code
msgid "Enable the use of QR-codes when paying by wire transfer."
msgstr "Aktivieren Sie die Verwendung von QR-Codes für Überweisungen."

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Keine Transaktion gefunden, die der Referenz %s entspricht."

#. module: payment_transfer
#: model_terms:ir.ui.view,arch_db:payment_transfer.transfer_transaction_status
msgid "Or scan me with your banking app."
msgstr "Oder scannen Sie mich mit Ihrer Banking-App."

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Zahlungsanbieter"

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_transaction
msgid "Payment Transaction"
msgstr "Zahlungstransaktion"

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__provider
msgid "Provider"
msgstr "Anbieter"

#. module: payment_transfer
#: model:ir.model.fields,help:payment_transfer.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "Der mit diesem Zahlungsanbieter zu verwendende Dienstleister"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_transaction.py:0
#, python-format
msgid "The customer has selected %(acq_name)s to make the payment."
msgstr "Der Kunde hat %(acq_name)s für die Zahlung ausgewählt."

#. module: payment_transfer
#: model:ir.model.fields.selection,name:payment_transfer.selection__payment_acquirer__provider__transfer
msgid "Wire Transfer"
msgstr "Banküberweisung"
