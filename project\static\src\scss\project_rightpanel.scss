// ------- View with rightpanel -------
$o-rightpanel-p: $o-horizontal-padding;
$o-rightpanel-p-small: $o-horizontal-padding*0.5;
$o-rightpanel-p-tiny: $o-rightpanel-p-small*0.5;

$o-rightpanel-category-default-color: $o-brand-primary;
$o-rightpanel-filter-default-color: #D59244;

.o_controller_with_rightpanel {
    display: flex;
    align-items: flex-start;

    .o_renderer_with_rightpanel {
        flex: 1 1 100%;
        overflow: auto; // make the renderer and search panel scroll individually
        max-height: 100%;
        position: relative;
    }
}

html .o_web_client > .o_action_manager > .o_action > .o_content > .o_controller_with_rightpanel {
    overflow: hidden;
}

.o_rightpanel {
    flex: 0 0 25%;
    overflow: auto;
    height: 100%;
    padding: $o-rightpanel-p-small $o-rightpanel-p $o-rightpanel-p*2 $o-rightpanel-p;
    border-left: 1px solid $gray-300;
    background-color: white;


    .o_rightpanel_section {
        padding-bottom: $o-rightpanel-p;
        padding-top: $o-rightpanel-p;

        &:nth-child(n+3){
            border-top: 1px solid $gray-300;
        }

        &:first-child{
            padding-bottom: 0px;
        }

        &:nth-child(2){
            padding-top: 0px;
        }

        .o_rightpanel_header {
            padding: $o-rightpanel-p-tiny 0 $o-rightpanel-p-small 0;

            .o_rightpanel_title {
                cursor: default;
                padding: 8px 0;
                font-weight: bold;
                font-size: large;
                display:inline;
            }

            .o_rightpanel_button {
                cursor: pointer;
                font-weight: bold;
                font-size: small;
                color: $o-brand-primary;
                float: right;
                padding-top: $o-rightpanel-p-tiny;
            }
        }

        .o_rightpanel_data {
            font-size: small;
            .o_rightpanel_data_row {
                &:hover {
                    background-color: $gray-100;
                    border-radius: 5px;
                }
            }
            .o_rightpanel_left_col {
                font-size: small;
                padding-top: 0;
                padding-bottom: 0;
                &.o_rightpanel_left_button {
                    color: $o-brand-primary;
                    padding-left: 0;
                }
                &.o_rightpanel_left_text {
                    padding-right: $o-rightpanel-p-small;
                }
            }
            .o_rightpanel_right_col {
                float: right;
            }

            .o_rightpanel_center_col {
                flex-grow: 1;
                text-align: center;
            }

            .o_open_milestone {
                margin-bottom: $o-rightpanel-p-tiny;
                display: flex;
                &.o_milestone_danger {
                    color: $red;
                }
                .o_milestone_checkbox {
                    cursor: pointer;
                    padding-right: $o-rightpanel-p-tiny;
                }
                .o_milestone_detail {
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    flex-grow: 1;
                }
                .o_delete_icon {
                    cursor: pointer;
                    padding-left: $o-rightpanel-p-tiny;
                }
            }
        }

        .o_rightpanel_hidden {
            display: none;
        }

        .o_color_red {
            color: $red;
        }
        .o_color_green {
            color: $green;
        }

        .o_form_view {
            .oe_button_box {
                display: flex;
                flex-wrap: wrap;
                margin-left: -16px;
                margin-right: -16px;

                @media screen and (max-device-width: 768px) {
                    margin-top: -4px;
                    margin-bottom: 0px;
                }
                
                .oe_stat_button {
                    flex-basis: 33.333333%;
                    border-width: 1px;
                    margin-top: -1px;
                    margin-left: -1px;
                    height: 56px;

                    &:nth-child(3n){
                        border-right-width: 0px;
                    }

                    .o_stat_text {
                        white-space: normal;
                    }
                }

                @media screen and (max-width: 1600px) {
                    .oe_stat_button {
                        flex-basis: 50%;
                    
                        &:nth-child(2n){
                            border-right-width: 0px;
                        }
                    }
                }

            }
        }
    }

}
