# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payulatam
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Norwegian Bokmål (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "Invalid payment status."
msgstr ""

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "Invalid sign: received %(sign)s, computed %(check)s."
msgstr ""

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields.selection,name:payment_payulatam.selection__payment_acquirer__provider__payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__payulatam_api_key
msgid "PayU Latam API Key"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__payulatam_account_id
msgid "PayU Latam Account ID"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__payulatam_merchant_id
msgid "PayU Latam Merchant ID"
msgstr ""

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Betalingsinnløser"

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_account_payment_method
msgid "Payment Methods"
msgstr "Betalingsmetoder"

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransaksjon"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__provider
msgid "Provider"
msgstr "Transportør"

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference (%(ref)s) or sign (%(sign)s)."
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_acquirer__payulatam_merchant_id
msgid "The ID solely used to identify the account with PayULatam"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_acquirer__payulatam_account_id
msgid ""
"The ID solely used to identify the country-dependent shop with PayULatam"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_payulatam
#: model:account.payment.method,name:payment_payulatam.payment_method_payulatam
msgid "payulatam"
msgstr ""
