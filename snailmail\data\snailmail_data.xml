<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.cron" id="snailmail_print">
            <field name="name">Snailmail: process letters queue</field>
            <field name="model_id" ref="model_snailmail_letter"/>
            <field name="state">code</field>
            <field name="code">model._snailmail_cron()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
        </record>
    </data>
</odoo>
