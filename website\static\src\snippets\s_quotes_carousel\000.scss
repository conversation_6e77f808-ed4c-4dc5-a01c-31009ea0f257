.s_quotes_carousel:not([data-vcss]) {
    blockquote {
        padding: $grid-gutter-width;
        margin-bottom: 0;
        .s_quotes_carousel_icon {
            position: absolute;
            top: 0;
            left: -3rem;
        }
        img {
            max-width: 40px;
            margin-right: 5px;
            border-radius: 50%;
        }
        footer {
            background-color: transparent;
            &:before {
                content:"";
            }
        }
    }
}
