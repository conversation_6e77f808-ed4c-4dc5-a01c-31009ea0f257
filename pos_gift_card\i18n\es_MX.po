# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_gift_card
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "<strong>Gift Card Code</strong>"
msgstr "<strong>Código de tarjeta de regalo</strong>"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Amount of the gift card:"
msgstr "Importe de la tarjeta de regalo:"

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "Barcode"
msgstr "Código de barras"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_barcode_rule
msgid "Barcode Rule"
msgstr "Regla de código de barras"

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_order_line__generated_gift_card_ids
msgid "Bought Gift Card"
msgstr "Compró una tarjeta de regalo"

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_gift_card__buy_pos_order_line_id
msgid "Buy Pos Order Line"
msgstr "Comprar línea de orden de venta de PdV"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "Card expires"
msgstr "La tarjeta expira"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Check a gift card"
msgstr "Ver una tarjeta de regalo"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_pos_order_line__gift_card_id
msgid "Deducted from this Gift Card"
msgstr "Deducido de esta tarjeta de regalo"

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_pos_config__gift_card_settings
msgid "Defines the way you want to set your gift cards."
msgstr "Define cómo desea establecer sus tarjetas de regalo."

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: pos_gift_card
#: model:ir.model.fields.selection,name:pos_gift_card.selection__pos_config__gift_card_settings__create_set
msgid "Generate a new barcode and set a price"
msgstr "Genera un nuevo código de barras y establece un precio"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Generate barcode"
msgstr "Generar código de barras"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardButton.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: model:ir.actions.report,name:pos_gift_card.gift_card_report_pdf
#: model:ir.model,name:pos_gift_card.model_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_config__use_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_order_line__gift_card_id
#: model:ir.model.fields.selection,name:pos_gift_card.selection__barcode_rule__type__gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.pos_gift_card_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_gift_card.res_config_view_form_inherit_pos_coupon
#, python-format
msgid "Gift Card"
msgstr "Tarjeta de regalo"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Gift Card Barcode:"
msgstr "Código de barras de la tarjeta de regalo:"

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_order__gift_card_count
msgid "Gift Card Count"
msgstr "Número de tarjetas de regalo"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Gift Card Error"
msgstr "Error en la tarjeta de regalo"

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_config__gift_card_product_id
#: model_terms:ir.ui.view,arch_db:pos_gift_card.pos_gift_card_config_view_form
msgid "Gift Card Product"
msgstr "Producto de tarjeta de regalo"

#. module: pos_gift_card
#: model:ir.ui.menu,name:pos_gift_card.pos_gift_card_menu
msgid "Gift Cards"
msgstr "Tarjetas de regalo"

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_config__gift_card_settings
msgid "Gift Cards settings"
msgstr "Ajustes de tarjetas de regalo"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Gift card balance is too low."
msgstr "El balance de la tarjeta de regalo es muy bajo."

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Gift card is not valid."
msgstr "La tarjeta de regalo no es válida."

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.pos_gift_card_config_view_form
msgid "Gift card settings"
msgstr "Ajustes de tarjeta de regalo"

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "Here is your gift card!"
msgstr "¡Aquí está su tarjeta de regalo!"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuración del Punto de venta"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Líneas de orden del punto de venta"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_pos_order
msgid "Point of Sale Orders"
msgstr "Órdenes del punto de venta"

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_gift_card__buy_pos_order_line_id
msgid "Pos Order line where this gift card has been bought."
msgstr "Línea de orden de PdV donde se compró esta tarjeta de regalo."

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_gift_card__redeem_pos_order_line_ids
msgid "Pos Redeems"
msgstr "Redenciones de PdV"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Remaining amount of the gift card:"
msgstr "Importe restante en la tarjeta de regalo:"

#. module: pos_gift_card
#: model:ir.model.fields.selection,name:pos_gift_card.selection__pos_config__gift_card_settings__scan_set
msgid "Scan an existing barcode and set a price"
msgstr "Escanear un código de barras existente y establecer un precio"

#. module: pos_gift_card
#: model:ir.model.fields.selection,name:pos_gift_card.selection__pos_config__gift_card_settings__scan_use
msgid "Scan an existing barcode with an existing price"
msgstr "Escanear un código de barras existente con un precio existente"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Scan and set price on gift card"
msgstr "Escanear y establecer un precio en la tarjeta de regalo"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Scan gift card"
msgstr "Escanear tarjeta de regalo"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/GiftCardPopup.js:0
#, python-format
msgid "This gift card has already been sold"
msgstr "Ya se vendió esta tarjeta de regalo"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/models.js:0
#, python-format
msgid "This gift card is already applied"
msgstr "Ya se aplicó esta tarjeta de regalo"

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_pos_config__gift_card_product_id
msgid "This product is used as reference on customer receipts."
msgstr "Este producto se utiliza como referencia en los recibos del cliente."

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_barcode_rule__type
msgid "Type"
msgstr "Tipo"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Use a gift card"
msgstr "Utilizar una tarjeta de regalo"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/GiftCardPopup.js:0
#, python-format
msgid "You cannot sell a gift card that has already been sold"
msgstr "No puede vender una tarjeta de regalo que ya ha sido vendida"
