# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_sips
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-19 08:20+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/odoo/"
"odoo-9/language/en_GB/)\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_sips
#: code:addons/payment_sips/models/sips.py:163
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/sips.py:161
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_sips
#: model_terms:payment.acquirer,cancel_msg:payment_sips.payment_acquirer_sips
msgid "<span><i>Cancel,</i> Your payment has been cancelled.</span>"
msgstr ""

#. module: payment_sips
#: model_terms:payment.acquirer,done_msg:payment_sips.payment_acquirer_sips
msgid ""
"<span><i>Done,</i> Your online payment has been successfully processed. "
"Thank you for your order.</span>"
msgstr ""

#. module: payment_sips
#: model_terms:payment.acquirer,error_msg:payment_sips.payment_acquirer_sips
msgid ""
"<span><i>Error,</i> Please be aware that an error occurred during the "
"transaction. The order has been confirmed but won't be paid. Don't hesitate "
"to contact us if you have any questions on the status of your order.</span>"
msgstr ""

#. module: payment_sips
#: model_terms:payment.acquirer,pending_msg:payment_sips.payment_acquirer_sips
msgid ""
"<span><i>Pending,</i> Your online payment has been successfully processed. "
"But your order is not validated yet.</span>"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/sips.py:84
#, python-format
msgid "Currency not supported by Wordline"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/sips.py:65
#, python-format
msgid "Incorrect payment acquirer provider"
msgstr ""

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Payment Acquirer"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Payment Transaction"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer_sips_merchant_id
msgid "SIPS API User Password"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer_sips_secret
msgid "SIPS Secret"
msgstr ""

#. module: payment_sips
#: model:payment.acquirer,name:payment_sips.payment_acquirer_sips
msgid "Sips"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/sips.py:159
#, python-format
msgid "Sips: received data for reference %s"
msgstr ""

#. module: payment_sips
#: model_terms:payment.acquirer,pre_msg:payment_sips.payment_acquirer_sips
msgid ""
"You will be redirected to the Sips website after clicking on payment button."
msgstr ""
