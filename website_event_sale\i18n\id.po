# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <wa<PERSON>use<PERSON><PERSON>@gmail.com>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Abe Manyo, 2022\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity cannot be applied to this "
"event tickets product."
msgstr ""
"Item daftar harga dengan kuantitas minimal yang positif tidak dapat "
"diterapkan ke produk tiket acara ini."

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity will not be applied to the "
"event tickets products."
msgstr ""
"Item daftar harga dengan kuantitas minimal yang positif tidak akan "
"diterapkan ke produk tiket acara."

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Tiket acara"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "Free"
msgstr "Bebas"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "From"
msgstr "Dari"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Peraturan Daftar Harga"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_product
msgid "Product"
msgstr "Produk"

#. module: website_event_sale
#: code:addons/website_event_sale/controllers/main.py:0
#, python-format
msgid "Registration"
msgstr "Registrasi rawat inap"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order
msgid "Sales Order"
msgstr "Order Penjualan"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Detail Order Penjualan"

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "Sorry, The %(ticket)s tickets for the %(event)s event are sold out."
msgstr "Maaf, tiket %(ticket)s untuk acara %(event)s sudah sold out."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid ""
"Sorry, only %(remaining_seats)d seats are still available for the %(ticket)s"
" ticket for the %(event)s event."
msgstr ""
"Maaf, hanya %(remaining_seats)d tempat duduk yang masih tersisa untuk tiket "
"%(ticket)s untuk acara %(event)s."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "The ticket doesn't match with this product."
msgstr "Tiket tidak sesuai dengan produk ini."

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid "Warning"
msgstr "Peringatan"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_website
msgid "Website"
msgstr "Website"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "to"
msgstr "kepada"
