<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data noupdate="1">
        <record id="digest.digest_digest_default" model="digest.digest">
            <field name="kpi_project_task_opened">True</field>
        </record>
    </data>

    <data>
        <record id="digest_tip_project_0" model="digest.tip">
            <field name="name">Tip: Customize tasks and stages according to the project</field>
            <field name="sequence">1200</field>
            <field name="group_id" ref="project.group_project_manager"/>
            <field name="tip_description" type="html">
<div>
    <p class="tip_title">Tip: Customize tasks and stages according to the project</p>
    <p class="tip_content">Customize how tasks are named according to the project and create tailor made status messages for each step of the workflow. It helps to document your workflow: what should be done at which step.</p>
    <img src="/project/static/src/img/project-custom-tasks.gif" class="illustration_border" />
</div>
            </field>
        </record>
    </data>
</odoo>
