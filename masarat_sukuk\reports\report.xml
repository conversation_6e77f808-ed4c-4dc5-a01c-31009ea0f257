<?xml version="1.0"?>
<odoo>

    <template id="report_banks_payslip_sukuk_x1">
        <t t-call="web.external_layout">
            <t t-set="payslip_index" t-value="0"/>
            <div class="page" style="direction: rtl;">
                <h4 style="text-align: center;">
                    <strong>حوافظ الإرفاق للمصارف</strong>
                </h4>
                <div style="text-align: center;">
                    <h4>
                        <strong>الأخوة مصرف /</strong>
                        <strong t-esc="docs[line]['bank']"/>
                    </h4>
                    <h4>
                        <strong>فرع /</strong>
                        <strong t-esc="docs[line]['branch']"/>
                    </h4>
                </div>
                <br/>
                <div style="font-size:18px;font-weight:bold;">
                    <span>نرفق لكم الصك رقم <span t-esc="docs[line]['serial_no']"/> بملبغ</span>&#160;&#160;&#160;&#160;
                    <strong t-esc="'%.2f'% sum(m['net'] for m in docs[line]['payslips'])"/>
                    <span style="margin-right:80px;">دينار ليبي فقط بتاريخ</span>
                    <strong t-esc="context_timestamp(datetime.datetime.now()).strftime('%d-%m-%Y')"
                            style="margin-right:10px;"/>

                </div>

                <h6 style="background-color:#D0D0D0;font-size:18px;font-weight:bold;">
                    فقط&#160;
                    <span t-esc="docs[line]['amount_text']"/>
                </h6>
                <div style="font-size:18px;font-weight:bold;">
                    <strong>وهو يمثل صافي المرتبات للأخوة الموظفين</strong>
                </div>
                <div style="font-size:18px;font-weight:bold;">
                    <span>عن شهر</span>&#160;<strong t-esc="str(date_from)[:10].split('-')[1]"/>&#160;&#160;
                    <span>لسنة</span>&#160;&#160;&#160;<strong t-esc="str(date_from)[:10].split('-')[0]"/>
                    <br/>
                    <span>ف يرجى قيد القيمة لحساباتهم المفتوحة لديكم كالتالي:</span>

                </div>
                <br/>

<!--                <table class="table table-bordered" style="font-size:17px;font-weight:bold;">-->
                <table class="table table-bordered" style="font-size:17px;font-weight:bold;" width="100%">
                    <thead>
                        <tr style="text-align: center;">
                            <th>م</th>
                            <th>الاسم</th>
                            <th>الرقم الوطني</th>
                            <!--                            <th>الموقع</th>-->
                            <th>رقم الحساب</th>
                            <th>الصافي</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="docs[line]['payslips']" t-as="payslips" style="text-align: center;">
                            <td style="background-color:#D0D0D0;">
                                <t t-set="payslip_index" t-value="payslip_index + 1"/>
                                <span t-esc="payslip_index"/>
                            </td>
                            <td>
                                <span t-esc="payslips['name']"/>
                            </td>
                            <td>
                                <span t-esc="payslips['national_number']"/>
                            </td>
                            <!--                            <td>-->
                            <!--                                <span t-esc="payslips['work_location']"/>-->
                            <!--                            </td>-->
                            <td>
                                <span t-esc="payslips['account_number']"/>
                            </td>
                            <td style="background-color:white;">
                                <span
                                        t-esc="'%.3f'% payslips['net']"/>
                            </td>
                        </tr>
                    </tbody>
                    <thead style="background-color:#D0D0D0	;">
                        <tr class="text-center">
                            <th colspan="4">المجموع</th>
                            <th>
                                <span t-esc="'%.0f'% sum(m['net'] for m in docs[line]['payslips'])"/>
                            </th>
                        </tr>
                    </thead>
                </table>
                <br/>
                <br/>
                <br/>
                <table border="1"
                       style="border-collapse: collapse; width: 100%; height: 42px; margin-left: auto; margin-right: auto;">
                    <tbody>
                        <tr style="height: 21px;">
                            <td style="width: 50%; height: 21px; text-align: center;">إعداد:.............</td>
                            <td style="width: 50%; height: 21px; text-align: center;">مراجعة:.............</td>
                        </tr>
                        <tr style="height: 21px;">
                            <td style="width: 50%; height: 21px; text-align: center;">التوقيع:............</td>
                            <td style="width: 50%; height: 21px; text-align: center;">التوقيع:............</td>
                        </tr>
                    </tbody>
                </table>
                <br/>
                <br/>
                <div class="text-center">
                    <h5>
                        مدير الشؤون الادارية والمالية--------------------------------
                    </h5>
                </div>
            </div>
        </t>
    </template>


    <template id="report_banks_payslip_sukuk">
        <t t-call="web.html_container">
            <t t-foreach="docs.keys()" t-as="line">
                <t t-call="masarat_sukuk.report_banks_payslip_sukuk_x1"/>
            </t>
        </t>
    </template>

    <report
            id="report_sukuk_payroll"
            string="حوافظ المرتبات"
            model="hr.payslip"
            report_type="qweb-pdf"
            name="masarat_sukuk.report_banks_payslip_sukuk"
            file="masarat_sukuk.report_banks_payslip_sukuk"/>
</odoo>
