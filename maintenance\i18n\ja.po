# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* maintenance
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# NANI<PERSON> <naniwa.ma<PERSON><PERSON>@gmail.com>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON><PERSON> N. <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Category:</b>"
msgstr "<b>カテゴリ:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Model Number:</b>"
msgstr "<b>モデルナンバー:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Request to:</b>"
msgstr "<b>リクエスト先:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Serial Number:</b>"
msgstr "<b>シリアル番号:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"badge badge-warning float-right\">Canceled</span>"
msgstr "<span class=\"badge badge-warning float-right\">取消済</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"ml8\">hours</span>"
msgstr "<span class=\"ml8\">時間</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Reporting</span>"
msgstr "<span>レポーティング</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Requests</span>"
msgstr "<span>依頼</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Equipments:</strong>"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Maintenance:</strong>"
msgstr "<strong>整備:</strong>"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "このエイリアスの新しいレコードを作成する時にデフォルト値を与えるためのPython辞書です。"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer3
#: model:maintenance.equipment,name:maintenance.equipment_computer5
msgid "Acer Laptop"
msgstr "Acer ラップトップ"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__active
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__active
msgid "Active"
msgstr "有効"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_ids
msgid "Activities"
msgstr "活動"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "例外の活動を示す文字装飾"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_state
msgid "Activity State"
msgstr "活動状態"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動種別アイコン"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.mail_activity_type_action_config_maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_menu_config_activity_type
msgid "Activity Types"
msgstr "活動タイプ"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid "Add a new equipment"
msgstr "新しい備品を追加"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_category_action
msgid "Add a new equipment category"
msgstr "新しい備品カテゴリを追加"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid "Add a new maintenance request"
msgstr "新規整備リクエストを追加"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_stage_action
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_dashboard_action
msgid "Add a new stage in the maintenance request"
msgstr "整備リクエストに新規ステージを追加"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_team_action_settings
msgid "Add a team in the maintenance request"
msgstr "整備リクエストにチームを追加"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_id
msgid "Alias"
msgstr "エイリアス"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_contact
msgid "Alias Contact Security"
msgstr "エイリアス連絡先セキュリティ"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_name
msgid "Alias Name"
msgstr "エイリアス名"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_domain
msgid "Alias domain"
msgstr "エイリアスドメイン"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_model_id
msgid "Aliased Model"
msgstr "エイリアス対象モデル"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "All"
msgstr "全て"

#. module: maintenance
#: model:ir.model.constraint,message:maintenance.constraint_maintenance_equipment_serial_no
msgid "Another asset already exists with this serial number!"
msgstr "ほかの資産で既にこのシリアルナンバーが存在します！"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__archive
msgid "Archive"
msgstr "アーカイブ"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Archived"
msgstr "アーカイブ済"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Assign To User"
msgstr "ユーザに割り当て"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Assigned"
msgstr "割当済"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "割当日"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Assigned to"
msgstr "担当者"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__blocked
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Blocked"
msgstr "ブロック済"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Cancel"
msgstr "取消"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__category_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Category"
msgstr "カテゴリ"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
msgid "Category Name"
msgstr "カテゴリ名"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__close_date
msgid "Close Date"
msgstr "完了日"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__color
msgid "Color Index"
msgstr "カラーインデクス"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__note
msgid "Comments"
msgstr "コメント"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__company_id
msgid "Company"
msgstr "会社"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_computer
msgid "Computers"
msgstr "コンピュータ"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_configuration
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Configuration"
msgstr "設定"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__corrective
msgid "Corrective"
msgstr "修正措置"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__cost
msgid "Cost"
msgstr "原価"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Created By"
msgstr "作成者"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_uid
msgid "Created by"
msgstr "作成者"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "ユーザにより作成済"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_date
msgid "Created on"
msgstr "作成日"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_open_count
msgid "Current Maintenance"
msgstr "現在保守整備中のもの"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "カスタムバウンスメッセージ"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_dashboard
msgid "Dashboard"
msgstr "ダッシュボード"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__effective_date
msgid ""
"Date at which the equipment became effective. This date will be used to "
"compute the Mean Time Between Failure."
msgstr "機器が有効になった日付。この日付は平均故障間隔を計算するために使用されます。"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__next_action_date
msgid "Date of the next preventive maintenance"
msgstr "次の予防保守整備の日"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__request_date
msgid "Date requested for the maintenance to happen"
msgstr "整備が実施のリクエスト日付"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__schedule_date
msgid ""
"Date the maintenance team plans the maintenance.  It should not differ much "
"from the Request Date. "
msgstr "整備チームが整備を計画する日。 リクエスト日と大きく違わないはずです。"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__close_date
msgid "Date the maintenance was finished. "
msgstr "整備が終了した日。"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__period
msgid "Days between each preventive maintenance"
msgstr "各予防保守整備の間の日数"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_defaults
msgid "Default Values"
msgstr "デフォルト値"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Delete"
msgstr "削除"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__description
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Description"
msgstr "説明"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__display_name
msgid "Display Name"
msgstr "表示名"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Done"
msgstr "完了"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Dropdown menu"
msgstr "ドロップダウンメニュー"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__duration
msgid "Duration"
msgstr "経過時間"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__duration
msgid "Duration in hours."
msgstr "所要時間(時間)"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Edit..."
msgstr "編集"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__effective_date
msgid "Effective Date"
msgstr "有効日"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Email Alias"
msgstr "Eメールエイリアス"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_id
msgid ""
"Email alias for this equipment category. New emails will automatically "
"create a new equipment under this category."
msgstr "この機器カテゴリのEメールエイリアス。新しいEメールは、自動的にこのカテゴリの下に新しい機器を作成します。"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__email_cc
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Email cc"
msgstr "メールCC"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__equipment_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__equipment_ids
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Equipment"
msgstr "備品"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_cat_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_mat_assign
msgid "Equipment Assigned"
msgstr "備品割当済"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_category_action
#: model:ir.ui.menu,name:maintenance.menu_maintenance_cat
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Equipment Categories"
msgstr "備品カテゴリ"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__category_id
msgid "Equipment Category"
msgstr "設備分類"

#. module: maintenance
#: model:res.groups,name:maintenance.group_equipment_manager
msgid "Equipment Manager"
msgstr "機器マネジャー"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__name
msgid "Equipment Name"
msgstr "備品名"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action_from_category_form
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_ids
#: model:ir.ui.menu,name:maintenance.menu_equipment_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Equipments"
msgstr "備品"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__fold
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__fold
msgid "Folded in Maintenance Pipe"
msgstr "保守整備パイプラインで折り畳む"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid ""
"Follow the process of the request and communicate with the collaborator."
msgstr "依頼プロセスに従って協力者とコミュニケーションします。"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeのアイコン 例. fa-tasks"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Future Activities"
msgstr "将来の活動"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Group by..."
msgstr "グル―プ化…"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_printer1
msgid "HP Inkjet printer"
msgstr "HP インクジェットプリンター"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer11
#: model:maintenance.equipment,name:maintenance.equipment_computer9
msgid "HP Laptop"
msgstr "HP製ノートパソコン"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__3
msgid "High"
msgstr "高"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "High-priority"
msgstr "優先度-高"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__id
msgid "ID"
msgstr "ID"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "エイリアスを保持する親レコードのID(例：プロジェクトは、タスク作成エイリアスを保持)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon"
msgstr "アイコン"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "例外的なアクティビティを示唆するアイコン"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_unread
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_unread
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_unread
msgid "If checked, new messages require your attention."
msgstr "チェックされている場合は、新しいメッセージに注意が必要です。"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合、一部のメッセージで配信エラーが発生しています。"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "設定されている場合、このコンテンツはデフォルトのメッセージではなく、許可されていないユーザーに自動的に送信されます。"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__normal
#: model:maintenance.stage,name:maintenance.stage_1
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "In Progress"
msgstr "進行中"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_maintenance
msgid "Internal Maintenance"
msgstr "内部メンテナンス"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Internal Notes"
msgstr "内部注記"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__kanban_state
msgid "Kanban State"
msgstr "かんばん状態"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment____last_update
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category____last_update
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request____last_update
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage____last_update
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Late Activities"
msgstr "遅れた活動"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__email_cc
msgid "List of cc from incoming emails."
msgstr "受信メールからのccのリスト。"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__location
msgid "Location"
msgstr "ロケーション"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_losses
msgid "Losses Analysis"
msgstr "ロス分析"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__1
msgid "Low"
msgstr "低"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_main_attachment_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_main_attachment_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_main_attachment_id
msgid "Main Attachment"
msgstr "主な添付"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_ids
#: model:ir.ui.menu,name:maintenance.menu_m_request
#: model:ir.ui.menu,name:maintenance.menu_maintenance_title
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Maintenance"
msgstr "整備"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_request_calendar
msgid "Maintenance Calendar"
msgstr "保守整備カレンダー"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_count
msgid "Maintenance Count"
msgstr "整備数"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_duration
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Maintenance Duration"
msgstr "保守整備期間"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__maintenance_duration
msgid "Maintenance Duration in hours."
msgstr "整備所要時間(時間単位)"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "整備機器"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment_category
msgid "Maintenance Equipment Category"
msgstr "整備機器カテゴリ"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_request
#: model:mail.activity.type,name:maintenance.mail_act_maintenance_request
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Maintenance Request"
msgstr "整備依頼"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_cat_req_created
msgid "Maintenance Request Created"
msgstr "整備依頼を作成"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Maintenance Request Search"
msgstr "整備リクエスト検索"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_tree
msgid "Maintenance Request Stage"
msgstr "整備依頼の状況"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_search
msgid "Maintenance Request Stages"
msgstr "整備依頼の状況"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_created
msgid "Maintenance Request created"
msgstr "整備依頼を作成"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_cal
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_from_equipment
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_link
#: model:ir.actions.act_window,name:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model:ir.actions.act_window,name:maintenance.maintenance_request_action_reports
#: model:ir.ui.menu,name:maintenance.maintenance_request_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_request_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Maintenance Requests"
msgstr "整備依頼"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_stage
msgid "Maintenance Stage"
msgstr "整備状況"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_stage_configuration
msgid "Maintenance Stages"
msgstr "保守整備状況"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_tree
msgid "Maintenance Team"
msgstr "保守整備チーム"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_dashboard_action
#: model:ir.model,name:maintenance.model_maintenance_team
#: model:ir.ui.menu,name:maintenance.menu_maintenance_teams
msgid "Maintenance Teams"
msgstr "保守整備チーム"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_type
msgid "Maintenance Type"
msgstr "保守整備種別"

#. module: maintenance
#: model:ir.actions.server,name:maintenance.maintenance_requests_cron_ir_actions_server
#: model:ir.cron,cron_name:maintenance.maintenance_requests_cron
#: model:ir.cron,name:maintenance.maintenance_requests_cron
msgid "Maintenance: generate preventive maintenance requests"
msgstr "整備: 予防保守リクエストを作成"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_metrology
msgid "Metrology"
msgstr "計測機器"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__model
msgid "Model"
msgstr "モデル"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_monitor
msgid "Monitors"
msgstr "モニタ"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__my_activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "活動の締切"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "My Equipments"
msgstr "自分の備品"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "My Maintenances"
msgstr "自分のメンテナンス"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Name"
msgstr "名称"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_0
msgid "New Request"
msgstr "新規依頼"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_calendar_event_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "次の活動カレンダーイベント"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "次の活動期限"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_summary
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_summary
msgid "Next Activity Summary"
msgstr "次の活動サマリ"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_id
msgid "Next Activity Type"
msgstr "次の活動タイプ"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Next Preventive Maintenance"
msgstr "次の予防保守"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__2
msgid "Normal"
msgstr "通常"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__note
msgid "Note"
msgstr "ノート"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of Actions"
msgstr "アクションの数"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count
msgid "Number of Requests"
msgstr "リクエスト数"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_block
msgid "Number of Requests Blocked"
msgstr "ブロックされたリクエスト数"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_date
msgid "Number of Requests Scheduled"
msgstr "予定済リクエスト数"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_unscheduled
msgid "Number of Requests Unscheduled"
msgstr "未計画の依頼数"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_high_priority
msgid "Number of Requests in High Priority"
msgstr "優先度高のリクエスト数"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "アクションを必要とするメッセージの数"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーのメッセージ数"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_unread_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_unread_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_unread_counter
msgid "Number of unread messages"
msgstr "未読メッセージ件数"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "すべての受信メッセージに添付されるスレッド(レコード)のオプションIDです。設定した場合は新しいレコードの作成を無効にします。"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_oee
msgid "Overall Equipment Effectiveness (OEE)"
msgstr "設備総合効率(OEE)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__owner_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Owner"
msgstr "オーナー"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid "Parent Model"
msgstr "親モデル"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "親レコードスレッドID"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"エイリアスを保持する親モデル。エイリアスの参照を保持しているモデルは、必ずしもalias_model_idによって与えられたモデルではありません(例：プロジェクト(parent_model)とタスク(model))"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_phone
msgid "Phones"
msgstr "電話"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"メールゲートウェイを使用したメッセージドキュメント投稿のポリシー\n"
"- everyone: 誰にでも投稿可能\n"
"- partners: 許可されたパートナーのみ\n"
"- followers: 関連ドキュメントのフォロワーか、フォローしているチャンネルのメンバーのみ\n"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__preventive
msgid "Preventive"
msgstr "予防"

#. module: maintenance
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid "Preventive Maintenance - %s"
msgstr "予防保守整備 - %s"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Preventive Maintenance Frequency"
msgstr "予防保守頻度"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_printer
msgid "Printers"
msgstr "プリンター"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__priority
msgid "Priority"
msgstr "優先度"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Product Information"
msgstr "プロダクト情報"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Ready"
msgstr "準備完了"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__done
msgid "Ready for next stage"
msgstr "次ステージへの準備済"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Record Colour"
msgstr "レコード色"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid "Record Thread ID"
msgstr "レコードのスレッドID"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Reopen Request"
msgstr "依頼を再度かける"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_3
msgid "Repaired"
msgstr "修理済"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_reports
msgid "Reporting"
msgstr "レポーティング"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__request_ids
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Request"
msgstr "リクエスト"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_created
msgid "Request Created"
msgstr "依頼作成済"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__request_date
msgid "Request Date"
msgstr "問合せ日"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__done
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__done
msgid "Request Done"
msgstr "完了した依頼"

#. module: maintenance
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid "Request planned for <a href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s</a>"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Requested By"
msgstr "依頼者"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "Requested by :"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_ids
msgid "Requests"
msgstr "依頼"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__technician_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Responsible"
msgstr "担当者"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_user_id
msgid "Responsible User"
msgstr "担当者"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_monitor1
#: model:maintenance.equipment,name:maintenance.equipment_monitor4
#: model:maintenance.equipment,name:maintenance.equipment_monitor6
msgid "Samsung Monitor 15\""
msgstr "サムスン製 15インチモニター"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Scheduled"
msgstr "予定"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__schedule_date
msgid "Scheduled Date"
msgstr "計画日"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_4
msgid "Scrap"
msgstr "廃棄"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__scrap_date
msgid "Scrap Date"
msgstr "廃棄日"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Search"
msgstr "検索"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__sequence
msgid "Sequence"
msgstr "付番"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__serial_no
msgid "Serial Number"
msgstr "シリアル番号"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__archive
msgid ""
"Set archive to true to hide the maintenance request without deleting it."
msgstr "保守要求を削除せずに非表示にするには、アーカイブをtrueに設定します。"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Show all records which has next action date is before today"
msgstr "次のアクションの日付が今日より前のすべてのレコードを表示する"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_software
msgid "Software"
msgstr "ソフトウェア"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__stage_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Stage"
msgstr "ステージ"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_stage_action
msgid "Stages"
msgstr "ステージ"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_status
msgid "Status Changed"
msgstr "ステータスが変更されました"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"活動に基づく状態\n"
"延滞：期限は既に過ぎました\n"
"当日：活動日は本日です\n"
"予定：将来の活動。"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_status
msgid "Status changed"
msgstr "ステータスが変更されました。"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_subcontractor
msgid "Subcontractor"
msgstr "外注先"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__name
msgid "Subjects"
msgstr "件名"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Team"
msgstr "チーム"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__member_ids
msgid "Team Members"
msgstr "チームメンバ"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__name
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "Team Name"
msgstr "チーム名"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_team_action_settings
msgid "Teams"
msgstr "チーム"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__technician_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Technician"
msgstr "担当技術者"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"このエイリアスに対応した(Odooドキュメント種別)モデル。既存のレコードに返信しない任意の受信メールは、このモデルの新しいレコード(例えば、プロジェクトタスク)の作成を引き起こします"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "eメールのエイリアス.例えば<<EMAIL>>のメールを受け取りたい場合'jobs’を選択"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"このエイリアスに電子メールを受信した時に作成されるレコードのオーナー。このフィールドが設定されない場合、システムは送信者(From)アドレスに基づいた適切なオーナーを見つけるか、見つからなければ管理者アカウントを使用します。"

#. module: maintenance
#: model:res.groups,comment:maintenance.group_equipment_manager
msgid "The user will be able to manage equipments."
msgstr "ユーザは機器を管理できるようになります。"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "To Do"
msgstr "未処理"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Today Activities"
msgstr "本日の活動"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Top Priorities"
msgstr "最優先"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid ""
"Track equipments and link it to an employee or department.\n"
"                You will be able to manage allocations, issues and maintenance of your equipment."
msgstr ""
"備品を追跡し、従業員や部門にリンクさせることができます。\n"
"備品の割り当て、問題、メンテナンスを管理することができます。"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記録上の例外アクティビティのタイプ。"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unassigned"
msgstr "未割当"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Under Maintenance"
msgstr "整備中"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_unread
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_unread
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_unread
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unread Messages"
msgstr "未読メッセージ"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_unread_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_unread_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未読メッセージカウンター"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Unscheduled"
msgstr "未計画"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Used in location"
msgstr "使用場所"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Vendor"
msgstr "仕入先"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_ref
msgid "Vendor Reference"
msgstr "仕入先参照"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__0
msgid "Very Low"
msgstr "非常に低い"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__warranty_date
msgid "Warranty Expiration Date"
msgstr "保証書有効期限"

#. module: maintenance
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid ""
"You cannot delete an equipment category containing equipments or maintenance"
" requests."
msgstr "備品や整備リクエストが残っている備品カテゴリは消去することが出来ません。"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "days"
msgstr "日"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "e.g. Internal Maintenance"
msgstr "例：内部メンテナンス"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "e.g. LED Monitor"
msgstr "例：LEDモニター"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "e.g. Monitors"
msgstr "例: モニター"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "e.g. Screen not working"
msgstr "例：画面が表示されない"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "hours"
msgstr "時間"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_graph
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_pivot
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_tree
msgid "maintenance Request"
msgstr "保守整備依頼"
