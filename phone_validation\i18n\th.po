# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* phone_validation
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: phone_validation
#: code:addons/phone_validation/models/phone_blacklist.py:0
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid " Please correct the number and try again."
msgstr "โปรดแก้ไขหมายเลขแล้วลองอีกครั้ง"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Add a phone number in the blacklist"
msgstr "เพิ่มหมายเลขโทรศัพท์ในบัญชีดำ"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: phone_validation
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Phone Number?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการยกเลิกการแบล็คลิสต์หมายเลขโทรศัพท์นี้"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_attachment_count
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Blacklist"
msgstr "Blacklist"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Blacklist Date"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "โทรศัพท์ในบัญชีดำคือมือถือ"

#. module: phone_validation
#: model:ir.actions.act_window,name:phone_validation.phone_blacklist_action
msgid "Blacklisted Phone Numbers"
msgstr "หมายเลขโทรศัพท์ที่อยู่ในบัญชีแบล็คลิสต์"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "โทรศัพท์ในบัญชีดำคือมือถือ"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Blacklisted phone numbers won't receive SMS Mailings anymore."
msgstr "หมายเลขโทรศัพท์ที่ขึ้นบัญชีแบล็คลิสต์จะไม่ได้รับจดหมาย SMS อีกต่อไป"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Confirm"
msgstr "ยืนยัน"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Discard"
msgstr "ยกเลิก"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__display_name
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"ช่องที่ใช้เก็บหมายเลขโทรศัพท์ที่ถูกสุขอนามัย ช่วยเร่งการค้นหาและเปรียบเทียบ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_follower_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_partner_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (คู่ค้า)"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__has_message
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__id
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__id
msgid "ID"
msgstr "รหัส"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_unread
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"หากหมายเลขโทรศัพท์ที่ถูกฆ่าเชื้ออยู่ในบัญชีดำ ผู้ติดต่อจะไม่ได้รับ SMS "
"ทางไปรษณีย์อีกต่อไปจากรายชื่อใดๆ"

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: probably invalid number of digits."
msgstr "หมายเลขที่เป็นไปไม่ได้ %s: จำนวนหลักอาจไม่ถูกต้อง"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"ระบุว่าหมายเลขโทรศัพท์ที่ถูกฆ่าเชื้อในบัญชีดำเป็นหมายเลขโทรศัพท์มือถือหรือไม่"
" "
"ช่วยแยกแยะว่าหมายเลขใดถูกขึ้นบัญชีดำเมื่อมีทั้งช่องโทรศัพท์มือถือและโทรศัพท์ในแบบจำลอง"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"ระบุว่าหมายเลขโทรศัพท์ที่ถูกฆ่าเชื้อในบัญชีดำเป็นหมายเลขโทรศัพท์หรือไม่ "
"ช่วยแยกแยะว่าหมายเลขใดถูกขึ้นบัญชีดำเมื่อมีทั้งช่องโทรศัพท์มือถือและโทรศัพท์ในแบบจำลอง"

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Invalid number %s: probably incorrect prefix."
msgstr "หมายเลข %s ไม่ถูกต้อง: คำนำหน้าอาจไม่ถูกต้อง"

#. module: phone_validation
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid "Invalid primary phone field on model %s"
msgstr "ฟิลด์โทรศัพท์หลักในรุ่นไม่ถูกต้อง %s"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_is_follower
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist____last_update
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_main_attachment_id
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: phone_validation
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid "Missing definition of phone fields."
msgstr "ไม่มีคำจำกัดความของฟิลด์โทรศัพท์"

#. module: phone_validation
#: model:ir.model.constraint,message:phone_validation.constraint_phone_blacklist_unique_number
msgid "Number already exists"
msgstr "มีหมายเลขอยู่แล้ว"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_unread_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__number
msgid "Number should be E164 formatted"
msgstr "ตัวเลขควรอยู่ในรูปแบบ E164"

#. module: phone_validation
#: model:ir.ui.menu,name:phone_validation.phone_menu_main
msgid "Phone / SMS"
msgstr "โทรศัพท์ / SMS"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist
#: model:ir.ui.menu,name:phone_validation.phone_blacklist_menu
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Phone Blacklist"
msgstr "แบล็คลิสต์โทรศัพท์"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_mail_thread_phone
msgid "Phone Blacklist Mixin"
msgstr "แบล็คลิสต์โทรศัพท์ Mixin"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "โทรศัพท์ขึ้นบัญชีดำ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__number
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__phone
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Phone Number"
msgstr "หมายเลขโทรศัพท์"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_mobile_search
msgid "Phone/Mobile"
msgstr "โทรศัพท์/มือถือ"

#. module: phone_validation
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr "กรุณากรอกอย่างน้อย 3 ตัวอักษรเมื่อค้นหาหมายเลขโทรศัพท์/โทรศัพท์มือถือ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Reason"
msgstr "เหตุผล"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist_remove
msgid "Remove phone from blacklist"
msgstr "ลบโทรศัพท์ออกจากบัญชีแบล็คลิสต์"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized
msgid "Sanitized Number"
msgstr "เบอร์สุขภัณฑ์"

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Unable to parse %(phone)s: %(error)s"
msgstr "ไม่สามารถแยกวิเคราะห์ %(phone)s ได้: %(error)s"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Unblacklist"
msgstr "ยกเลิกการแบล็คลิสต์"

#. module: phone_validation
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid "Unblacklisting Reason: %s"
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_unread
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_unread_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "phone_blacklist_removal"
msgstr "phone_blacklist_removal"
