<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="HeaderLockButton" owl="1">
        <div class="header-button lock-button" t-on-mouseover="onMouseOver(true)"
             t-on-click="showLoginScreen" t-on-mouseout="onMouseOver(false)">
            <span class="lock-button">
                <i class="fa"
                   t-att-class="{ 'fa-unlock': state.isUnlockIcon, 'fa-lock': !state.isUnlockIcon }"
                   role="img" t-att-aria-label="state.title" t-att-title="state.title"></i>
            </span>
        </div>
    </t>

</templates>
