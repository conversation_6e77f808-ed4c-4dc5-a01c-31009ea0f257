<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Account Tags -->
        <record id="account_tag_1" model="account.account.tag">
            <field name="name">Lonen en salarissen</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_2" model="account.account.tag">
            <field name="name">Huisvestingskosten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_3" model="account.account.tag">
            <field name="name">Financiële vaste activa</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_4" model="account.account.tag">
            <field name="name">Voorraden</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_5" model="account.account.tag">
            <field name="name">Kantoorkosten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_6" model="account.account.tag">
            <field name="name">Vervoerskosten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_7" model="account.account.tag">
            <field name="name">Verkoopkosten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_8" model="account.account.tag">
            <field name="name">Algemene Kosten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_9" model="account.account.tag">
            <field name="name">Eigen vermogen</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_10" model="account.account.tag">
            <field name="name">Kostprijs van de omzet</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_11" model="account.account.tag">
            <field name="name">Netto omzet</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_12" model="account.account.tag">
            <field name="name">Resultaat overige activa</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_13" model="account.account.tag">
            <field name="name">Overige kortlopende schulden</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_14" model="account.account.tag">
            <field name="name">Voorzieningen</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_15" model="account.account.tag">
            <field name="name">Langlopende schulden</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_16" model="account.account.tag">
            <field name="name">Overige bedrijfsopbrengsten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_17" model="account.account.tag">
            <field name="name">Immateriële vaste activa</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_18" model="account.account.tag">
            <field name="name">Sociale lasten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_19" model="account.account.tag">
            <field name="name">Overige personeelskosten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_20" model="account.account.tag">
            <field name="name">Pensioenlasten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_21" model="account.account.tag">
            <field name="name">Afschrijving materiële vaste activa</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_22" model="account.account.tag">
            <field name="name">Afschrijving immateriële vaste activa</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_23" model="account.account.tag">
            <field name="name">Materiële vaste activa</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_24" model="account.account.tag">
            <field name="name">Effecten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_25" model="account.account.tag">
            <field name="name">Liquide middelen</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_26" model="account.account.tag">
            <field name="name">Distributiekosten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_27" model="account.account.tag">
            <field name="name">Vorderingen</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_28" model="account.account.tag">
            <field name="name">Tussenrekeningen</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_29" model="account.account.tag">
            <field name="name">Foutenrekening</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_30" model="account.account.tag">
            <field name="name">Rente baten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_31" model="account.account.tag">
            <field name="name">Rente- en overige financiële lasten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_32" model="account.account.tag">
            <field name="name">Belastingen &#38; sociale lasten</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_33" model="account.account.tag">
            <field name="name">Belastingen</field>
            <field name="applicability">accounts</field>
        </record>

    </data>
</odoo>

