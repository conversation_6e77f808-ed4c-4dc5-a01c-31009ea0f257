# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from pytz import timezone
from odoo import models, fields, api, exceptions, _
from odoo.tools import format_datetime
from odoo.exceptions import ValidationError


class WorkingSchedual(models.Model):
    _inherit = "resource.calendar"

    there_is_letancy = fields.Boolean(string='يخضع لضوابط',default=False)

    checkin_tolarence = fields.Integer(string='سماحية الدخول المتأخر (دقائق)' ,default=0)
    checkout_tolarence = fields.Integer(string='سماحية الخروج المبكر (دقائق)',default=0)
    total_working_hour = fields.Float(string='مجموع ساعات العمل الاجبارية',default=0.0)

    penalty = fields.Integer(string="احتساب دقائق التأخير",default = 1)
    latency_approval_count = fields.Integer('عدد مرات سماحية تقديم اذونات التأخير والخروج المبكر شهرياً',default = 1)
    latency_allawence_approved = fields.Integer('عدد دقائق سماحية التأخير',default = 120)
    has_half_day_leave_start = fields.Boolean(string='اجازة نصف يوم')
    half_day_leave_start = fields.Float(string='توقيت بدأ اجازة النصف يوم')

    ########### Ramadan or Other Custom Date Attendance

    custom_calender = fields.Boolean('دوام مخصص')
    custom_calender_start = fields.Date('تاريخ بدأ الدوام المخصص')
    custom_calender_end = fields.Date('تاريخ انتهاء الدوام المخصص')
    custom_attendance_ids = fields.One2many('resource.calendar.attendance', 'custom_calendar_id', 'Custom Working Time')

    @api.constrains('custom_calender_start','custom_calender_end')
    def insure_custom_calender_dates(self):
        for elem in self:
            if elem.custom_calender_start and elem.custom_calender_end:
                if elem.custom_calender_start >= elem.custom_calender_end:
                    raise ValidationError('تاريخ بدأ الدوام المخصص يجب أن يكون اصغر من تاريخ انهاء الدوام المخصص')

    ###########################


class ResourceCalendarAttendance(models.Model):
    _inherit = "resource.calendar.attendance"

    calendar_id = fields.Many2one("resource.calendar", string="Resource's Calendar", required=False, ondelete='cascade')

    custom_calendar_id = fields.Many2one("resource.calendar", string="Resource's Calendar", required=True, ondelete='cascade')