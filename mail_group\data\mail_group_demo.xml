<odoo>
    <data>
        <!-- Group 1 -->
        <record id="mail_group_1" model="mail.group">
            <field name="name">My Company News</field>
            <field name="alias_name">newsletter</field>
            <field name="description">Receive news about "My Company"</field>
            <field name="moderation" eval="True"/>
            <field name="moderator_ids" eval="[(4, ref('base.user_admin'))]"/>
            <field name="access_mode">groups</field>
            <field name="access_group_id" ref="base.group_user"/>
        </record>
        <!-- Members of group 1 -->
        <record id="mail_group_member_1" model="mail.group.member">
            <field name="partner_id" ref="base.partner_admin"/>
            <field name="email"><EMAIL></field>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_member_2" model="mail.group.member">
            <field name="partner_id" ref="base.partner_demo"/>
            <field name="email"><EMAIL></field>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <!-- Attachment of messages -->
        <record id="ir_attachment_mail_group_message_1" model="ir.attachment">
            <field name="datas">U3VwZXIgc2VjcmV0IGF0dGFjaG1lbnQ=</field>
            <field name="name">attachment.txt</field>
        </record>
        <record id="ir_attachment_mail_group_message_2" model="ir.attachment">
            <field name="datas">QnV0IEphdmFzY3JpcHQgc3Vja3M=</field>
            <field name="name">attachment.txt</field>
        </record>
        <!-- Messages of group 1 -->
        <record id="mail_group_message_1" model="mail.group.message">
            <field name="subject">Important Announce</field>
            <field name="body">This weekend, you should all come to our barbecue!</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" eval="False"/>
            <field name="mail_group_id" ref="mail_group_1"/>
            <field name="attachment_ids" eval="[(4, ref('ir_attachment_mail_group_message_1'))]"/>
        </record>
        <record id="mail_group_message_1_1" model="mail.group.message">
            <field name="subject">Re: Important Announce</field>
            <field name="body">Will there be vegetarian food?</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" ref="mail_group_message_1"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_1_1_1" model="mail.group.message">
            <field name="subject">Re: Important Announce</field>
            <field name="body">Yes of course, and for those who are allergic bring your own food</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" ref="mail_group_message_1_1"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_1_2" model="mail.group.message">
            <field name="subject">Re: Important Announce</field>
            <field name="body">Can I come with my dog ?</field>
            <field name="email_from"><EMAIL></field>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" ref="mail_group_message_1"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_1_2_1" model="mail.group.message">
            <field name="subject">Re: Important Announce</field>
            <field name="body">No animals please.</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" ref="mail_group_message_1_2"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_2" model="mail.group.message">
            <field name="subject">Recruitment</field>
            <field name="body">We are pleased to announce that we have hired 10 new people this month.</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" eval="False"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_3" model="mail.group.message">
            <field name="subject">Relocation</field>
            <field name="body">We are moving our office to Brussels. Take back all your remaining stuff.</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" eval="False"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_3_1" model="mail.group.message">
            <field name="subject">Re: Relocation</field>
            <field name="body">Is there a swimming pool in this new office?</field>
            <field name="email_from"><EMAIL></field>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" ref="mail_group_message_3"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_3_1_1" model="mail.group.message">
            <field name="subject">Re: Relocation</field>
            <field name="body">Of course!</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" ref="mail_group_message_3_1"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_3_2" model="mail.group.message">
            <field name="subject">Re: Relocation</field>
            <field name="body">What is the deadline for taking back my stuff?</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" ref="mail_group_message_3"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_3_2_1" model="mail.group.message">
            <field name="subject">Re: Relocation</field>
            <field name="body">In 4 weeks.</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="moderation_status">pending_moderation</field>
            <field name="group_message_parent_id" ref="mail_group_message_3_2"/>
            <field name="mail_group_id" ref="mail_group_1"/>
        </record>
        <record id="mail_group_message_4" model="mail.group.message">
            <field name="subject">I really like CSS &amp; HTML</field>
            <field name="body">
                &lt;p style=&quot;margin:0px; font-size:13px;&quot;&gt;Hi,&lt;/p&gt;&lt;br&gt; &lt;p style=&quot;margin:0px; font-size:13px;&quot;&gt; We &lt;u style=&quot;font-size:14px&quot;&gt;really&lt;/u&gt; like &lt;font style=&quot;color:rgb(57, 123, 33); font-weight:bolder&quot;&gt;colors&lt;/font&gt; and &lt;span style=&quot;font-weight:bolder&quot;&gt;&lt;font style=&quot;color:rgb(255, 0, 0)&quot;&gt;CSS&lt;/font&gt;&lt;/span&gt;. &lt;/p&gt; &lt;p style=&quot;margin:0px; font-size:13px;&quot;&gt;Do you know you can add &lt;/p&gt; &lt;a href=&quot;https://example.com&quot; target=&quot;_blank&quot; class=&quot;btn btn-primary flat btn-sm&quot;&gt;link in email&lt;/a&gt; ? &lt;ol&gt; &lt;li&gt;Also image&lt;/li&gt; &lt;li&gt;List&lt;/li&gt; &lt;li&gt;Any HTML code in the end...&lt;/li&gt; &lt;/ol&gt;

            </field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_admin"/>
            <field name="moderation_status">pending_moderation</field>
            <field name="group_message_parent_id" eval="False"/>
            <field name="mail_group_id" ref="mail_group_1"/>
            <field name="attachment_ids" eval="[(4, ref('ir_attachment_mail_group_message_2'))]"/>
        </record>
        <!-- Group 2 -->
        <record id="mail_group_2" model="mail.group">
            <field name="name">Public Mailing List</field>
            <field name="alias_name">public_group</field>
            <field name="description">Get the patch notes of our amazing product.</field>
            <field name="moderation" eval="True"/>
            <field name="moderator_ids" eval="[(4, ref('base.user_admin'))]"/>
        </record>
        <!-- Message of Group 2 -->
        <record id="mail_group_message_5" model="mail.group.message">
            <field name="subject">Best patch ever</field>
            <field name="body">In this patch, we have cleaned the CSS!</field>
            <field name="email_from"><EMAIL></field>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="moderation_status">accepted</field>
            <field name="group_message_parent_id" eval="False"/>
            <field name="mail_group_id" ref="mail_group_2"/>
        </record>
    </data>
</odoo>
