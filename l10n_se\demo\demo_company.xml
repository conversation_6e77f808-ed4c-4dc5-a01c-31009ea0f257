<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_se" model="res.partner">
        <field name="name">SE Company</field>
        <field name="vat">SE810354746201</field>
        <field name="street"></field>
        <field name="city">Ängelholms kommun</field>
        <field name="country_id" ref="base.se"/>
        
        <field name="zip">262 64</field>
        <field name="phone">+46 70 123 45 67</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.seexample.com</field>
    </record>

    <record id="demo_company_se" model="res.company">
        <field name="name">SE Company</field>
        <field name="partner_id" ref="partner_demo_company_se"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_se')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_se.demo_company_se'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_se.l10nse_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_se.demo_company_se')"/>
    </function>
</odoo>
