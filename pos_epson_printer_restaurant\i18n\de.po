# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer_restaurant
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_epson_printer_restaurant
#: model:ir.model.fields,field_description:pos_epson_printer_restaurant.field_restaurant_printer__epson_printer_ip
msgid "Epson Receipt Printer IP Address"
msgstr "Epson Bondrucker IP Adresse"

#. module: pos_epson_printer_restaurant
#: model:ir.model.fields,help:pos_epson_printer_restaurant.field_restaurant_printer__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Lokale IP Adresse des Epson Bondruckers."

#. module: pos_epson_printer_restaurant
#: model:ir.model.fields,field_description:pos_epson_printer_restaurant.field_restaurant_printer__printer_type
msgid "Printer Type"
msgstr "Druckermodell"

#. module: pos_epson_printer_restaurant
#: model:ir.model,name:pos_epson_printer_restaurant.model_restaurant_printer
msgid "Restaurant Printer"
msgstr "Restaurant-Drucker"

#. module: pos_epson_printer_restaurant
#: model:ir.model.fields.selection,name:pos_epson_printer_restaurant.selection__restaurant_printer__printer_type__epson_epos
msgid "Use an Epson printer"
msgstr "Epson Drucker benutzen"
