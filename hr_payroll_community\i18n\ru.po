# Translation of Odoo Server.
 # This file contains the translation of the following modules:
 # * hr_payroll_community_v13
 # 
 # Translators:
 # <PERSON>, 2018
 # <PERSON><PERSON><PERSON><PERSON> <koro<PERSON><EMAIL>>, 2019
 # 
 msgid ""
 msgstr ""
 "Project-Id-Version: Odoo Server 13.0\n"
 "Report-Msgid-Bugs-To: \n"
 "POT-Creation-Date: 2019-01-09 10:31+0000\n"
 "PO-Revision-Date: 2018-08-24 09:19+0000\n"
 "Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019\n"
 "Language-Team: Russian (https://www.transifex.com/odoo/teams/41243/ru/)\n"
 "MIME-Version: 1.0\n"
 "Content-Type: text/plain; charset=UTF-8\n"
 "Content-Transfer-Encoding: \n"
 "Language: ru\n"
 "Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:42
 #, python-format
 msgid "%s (copy)"
 msgstr "%s (копия)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__state
 msgid ""
 "* When the payslip is created the status is 'Draft'\n"
 "                \n"
 "* If the payslip is under verification, the status is 'Waiting'.\n"
 "                \n"
 "* If the payslip is confirmed then status is set to 'Done'.\n"
 "                \n"
 "* When user cancel payslip the status is 'Rejected'."
 msgstr ""
 "* Созданная расчётная ведомость имеет статус «Черновик»\n"
 "                \n"
 "* Если ведомость в процессе сверки — «Ожидание».\n"
 "                \n"
 "* Если ведомость подтверждена — «Готово».\n"
 "                \n"
 "* Когда пользователь отменяет ведомость — «Отклонено»."
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "<span class=\"o_form_label\">Payroll Rules</span>"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid ""
 "<span colspan=\"4\" nolabel=\"1\">This wizard will generate payslips for all"
 " selected employee(s) based on the dates and credit note specified on "
 "Payslips Run.</span>"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Address</strong>"
 msgstr "<strong>Адрес</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Authorized signature</strong>"
 msgstr "<strong>Авторизированная подпись</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Bank Account</strong>"
 msgstr "<strong>Банковский счёт</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Date From:</strong>"
 msgstr "<strong>Дата от:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Date From</strong>"
 msgstr "<strong>Дата от</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Date To:</strong>"
 msgstr "<strong>Дата до:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Date To</strong>"
 msgstr "<strong>Дата до</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Designation</strong>"
 msgstr "<strong>Обозначение</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Email</strong>"
 msgstr "<strong>Email</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Identification No</strong>"
 msgstr "<strong>Табельный номер</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Name</strong>"
 msgstr "<strong>Имя</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Reference</strong>"
 msgstr "<strong>Описание</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Register Name:</strong>"
 msgstr "<strong>Регистрационное имя:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Total</strong>"
 msgstr "<strong>Итого</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.actions.act_window,help:hr_payroll_community_v13.action_contribution_register_form
 msgid ""
 "A contribution register is a third party involved in the salary\n"
 "            payment of the employees. It can be the social security, the\n"
 "            state or anyone that collect or inject money on payslips."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_account_accountant
 msgid "Account Accountant"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Accounting"
 msgstr "Бухгалтерский учёт"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Accounting Information"
 msgstr "Учётная информация"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__active
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__active
 msgid "Active"
 msgstr "Активно"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.actions.act_window,help:hr_payroll_community_v13.action_contribution_register_form
 msgid "Add a new contribution register"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Add an internal note..."
 msgstr "Добавить внутреннюю заметку..."
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contract_advantage_template_view_form
 msgid "Advantage Name"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_children_salary_rules
 msgid "All Children Rules"
 msgstr "Все дочерние правила"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.ALW
 msgid "Allowance"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Always True"
 msgstr "Всегда верно"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__amount
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Amount"
 msgstr "Сумма"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_select
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_select
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Amount Type"
 msgstr "Тип суммы"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Annually"
 msgstr "Ежегодно"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__appears_on_payslip
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__appears_on_payslip
 msgid "Appears on Payslip"
 msgstr "Появилось в платежной ведомости"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_python
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_python
 msgid ""
 "Applied this rule for calculation if condition is true. You can specify "
 "condition like basic > 1000."
 msgstr ""
 "Примените это правило для вычислений если условие справедливо. Вы можете для"
 " основы установить значение > 1000."
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.BASIC
 msgid "Basic"
 msgstr "Основной"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_basic
 msgid "Basic Salary"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_be_hr_payroll_community_v13
 msgid "Belgium Payroll"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Bi-monthly"
 msgstr "Раз в два месяца"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Bi-weekly"
 msgstr "Раз в две недели"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_form
 msgid "Calculations"
 msgstr "Расчёты"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Cancel"
 msgstr "Отменить"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Cancel Payslip"
 msgstr "Отклонить платежную ведомость"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:96
 #, python-format
 msgid "Cannot cancel a payslip that is done."
 msgstr "Невозможно отклонить проведённую ведомость."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__category_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__category_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Category"
 msgstr "Категория"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Child Rules"
 msgstr "Подчиненные правила"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__child_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__child_ids
 msgid "Child Salary Rule"
 msgstr "Подчинённые правила заработной платы"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__children_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__children_ids
 msgid "Children"
 msgstr "Дочерний"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Children Definition"
 msgstr "Дочернее определение"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Choose a Payroll Localization"
 msgstr "Выберите локализацию расчетного листа"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.run,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 msgid "Close"
 msgstr "Закрыть"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__code
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Code"
 msgstr "Код"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payroll_community_v13_structure_view_kanban
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_view_kanban
 msgid "Code:"
 msgstr "Код:"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Companies"
 msgstr "Компании"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__company_id
 msgid "Company"
 msgstr "Компания"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.COMP
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Company Contribution"
 msgstr "Вклад организации"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Computation"
 msgstr "Вычисления"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Compute Sheet"
 msgstr "Вычислить ведомость"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_select
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_select
 msgid "Condition Based on"
 msgstr "Условие основанное на"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Conditions"
 msgstr "Условия"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_res_config_settings
 msgid "Config Settings"
 msgstr "Настройки конфигурации"
 
 #. module: hr_payroll_community_v13
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_configuration
 msgid "Configuration"
 msgstr "Настройка"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Confirm"
 msgstr "Подтвердить"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__contract_id
 msgid "Contract"
 msgstr "Договор"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.hr_contract_advantage_template_action
 #: model:ir.ui.menu,name:hr_payroll_community_v13.hr_contract_advantage_template_menu_action
 msgid "Contract Advantage Templates"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_form
 msgid "Contribution"
 msgstr "Авторы"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contribution_register
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__register_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__register_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Contribution Register"
 msgstr "Регистрация выплат"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Contribution Register's Payslip Lines"
 msgstr "Строки платежных ведомостей регистрации выплат"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_contribution_register_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_action_hr_contribution_register_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_tree
 msgid "Contribution Registers"
 msgstr "Регистрации выплат"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_convanceallowance1
 msgid "Conveyance Allowance"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_ca_gravie
 msgid "Conveyance Allowance For Gravie"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__create_uid
 msgid "Created by"
 msgstr "Создано"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__create_date
 msgid "Created on"
 msgstr "Создан"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__credit_note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__credit_note
 msgid "Credit Note"
 msgstr "Кредит-нота"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__date_from
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__date_start
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__date_from
 msgid "Date From"
 msgstr "Дата с"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__date_to
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__date_end
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__date_to
 msgid "Date To"
 msgstr "До даты"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.DED
 msgid "Deduction"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__default_value
 msgid "Default value for this advantage"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract__schedule_pay
 msgid "Defines the frequency of the wage payment."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__struct_id
 msgid ""
 "Defines the rules that have to be applied to this payslip, accordingly to "
 "the contract chosen. If you let empty the field contract, this field isn't "
 "mandatory anymore and thus the rules applied will be all the rules set on "
 "the structure of all contracts of the employee valid for the chosen period"
 msgstr ""
 "Определяет правила, которые должны быть применены к этой платежной "
 "ведомости, в соответствии с выбранным контрактом. Если вы если вы оставите "
 "поле контракта пустым, это поле не является обязательным больше и, таким "
 "образом, применяемыми правилами, будут все правила, установленные на "
 "структуре всех контрактов работника действительных для выбранного периода"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__note
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Description"
 msgstr "Описание"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Details By Salary Rule Category"
 msgstr "Подробности категории правила начисления зарплаты"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__details_by_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Details by Salary Rule Category"
 msgstr "Подробности категории правила начисления зарплаты"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_contributionregister__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_payslipdetails__display_name
 msgid "Display Name"
 msgstr "Отображаемое Имя"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Done"
 msgstr "Сделано"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Done Payslip Batches"
 msgstr "Готовые платежные ведомости"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Done Slip"
 msgstr "Готовая ведомость"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0 selection:hr.payslip.run,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Draft"
 msgstr "Черновик"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Draft Payslip Batches"
 msgstr "Черновая платежная ведомость"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Draft Slip"
 msgstr "Черновая ведомость"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_employee
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__employee_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__employee_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Employee"
 msgstr "Сотрудник"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contract
 msgid "Employee Contract"
 msgstr "Договор с сотрудником"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_employee_grade_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_v13_structure_list_view
 msgid "Employee Function"
 msgstr "Трудовая функция работника"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_view_hr_payslip_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_department_tree
 msgid "Employee Payslips"
 msgstr "Платежные ведомости сотрудников"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contract_advantage_template
 msgid "Employee's Advantage on Contract"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract__resource_calendar_id
 msgid "Employee's working schedule."
 msgstr "График работы сотрудника"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__employee_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Employees"
 msgstr "Сотрудники"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:92
 #, python-format
 msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:179
 #, python-format
 msgid "Error! You cannot create recursive hierarchy of Salary Rules."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__register_id
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__register_id
 msgid "Eventual third party involved in the salary payment of the employees."
 msgstr ""
 "Иногда в выплате заработной платы сотрудникам участвует третья сторона."
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_fix
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_fix
 msgid "Fixed Amount"
 msgstr "Фиксированная величина"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage
 msgid "For example, enter 50.0 to apply a percentage of 50%"
 msgstr ""
 "Например, можно ввести 50,0 чтобы применить процентное соотношение 50%"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/report/report_contribution_register.py:35
 #, python-format
 msgid "Form content is missing, this report cannot be printed."
 msgstr "Не найдены данные в форме, отчет не сформирован"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_fr_hr_payroll_community_v13
 msgid "French Payroll"
 msgstr "Французская Платежная ведомость"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "General"
 msgstr "Общее"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid "Generate"
 msgstr "Генерировать"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payslip_by_employees
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 msgid "Generate Payslips"
 msgstr "Сгенерировать ведомости"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_employees
 msgid "Generate payslips for all selected employees"
 msgstr "Сгенерировать платежные ведомости для всех выбранных сотрудников"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_sales_commission
 msgid "Get 1% of sales"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:182
 #, python-format
 msgid "Global Leaves"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_taxable
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.GROSS
 msgid "Gross"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Group By"
 msgstr "Группировать"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_houserentallowance1
 msgid "House Rent Allowance"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_contributionregister__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_payslipdetails__id
 msgid "ID"
 msgstr "Номер"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_run__credit_note
 msgid ""
 "If its checked, indicates that all payslips generated from here are refund "
 "payslips."
 msgstr ""
 "Если все проверено, пометьте что все платежные ведомости отсюда являются "
 "оплаченными ведомостями."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__active
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__active
 msgid ""
 "If the active field is set to false, it will allow you to hide the salary "
 "rule without removing it."
 msgstr ""
 "Если в поле «Активно» имеет значение «Ложь», вы сможете скрыть правило "
 "начисления заработной платы не удаляя его."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_in_hr_payroll_community_v13
 msgid "Indian Payroll"
 msgstr "Индийская платежная ведомость"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__credit_note
 msgid "Indicates this payslip has a refund of another"
 msgstr "Отмечает что эта платежная ведомость была выплачена другим"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Input Data"
 msgstr "Входящая дата"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__input_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__input_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Inputs"
 msgstr "исходные данные"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__note
 msgid "Internal Note"
 msgstr "Внутреннее примечание"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_view_kanban
 msgid "Is a Blocking Reason?"
 msgstr "Является причиной блокировки?"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__quantity
 msgid ""
 "It is used in computation for percentage and fixed amount. For e.g. A rule "
 "for Meal Voucher having fixed amount of 1€ per worked day can have its "
 "quantity defined in expression like worked_days.WORK100.number_of_days."
 msgstr ""
 "Используется для расчёта процентов и фиксированного количества. Для т. н. "
 "«Правила мучного ваучера» указано 1 € за рабочий день и может быть "
 "определено общее количество в выражении worked_days.WORK100.number_of_days."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__amount
 msgid ""
 "It is used in computation. For e.g. A rule for sales having 1% commission of"
 " basic salary for per product can defined in expression like result = "
 "inputs.SALEURO.amount * contract.wage*0.01."
 msgstr ""
 "Используется в вычислении. Для например, Правила для продаж, имеющих 1% "
 "комиссионных базового оклада для каждого программного продукта может "
 "определены в выражении типа result=inputs.SALEURO.amount*contract.wage*0,01."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_contributionregister____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_payslipdetails____last_update
 msgid "Last Modified on"
 msgstr "Последнее изменение"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__write_uid
 msgid "Last Updated by"
 msgstr "Последний раз обновил"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__write_date
 msgid "Last Updated on"
 msgstr "Последнее обновление"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule_category__parent_id
 msgid ""
 "Linking a salary category to its parent is used only for the reporting "
 "purpose."
 msgstr ""
 "Присоединение зарплатной категории к ее руководству используется только в "
 "целях отчетности."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__lower_bound
 msgid "Lower Bound"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract_advantage_template__lower_bound
 msgid "Lower bound authorized by the employer for this advantage"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__paid
 msgid "Made Payment Order ? "
 msgstr "Создать платежное поручение? "
 
 #. module: hr_payroll_community_v13
 #: model:res.groups,name:hr_payroll_community_v13.group_hr_payroll_community_v13_manager
 msgid "Manager"
 msgstr "Менеджер"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range_max
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range_max
 msgid "Maximum Range"
 msgstr "Максимальный период"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_meal_voucher
 msgid "Meal Voucher"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range_min
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range_min
 msgid "Minimum Range"
 msgstr "Минимальный период"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Miscellaneous"
 msgstr "Разное"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Monthly"
 msgstr "Ежемесячно"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__name
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Name"
 msgstr "Название"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.NET
 msgid "Net"
 msgstr "Чистая"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_net
 msgid "Net Salary"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:201
 #, python-format
 msgid "Normal Working Days paid at 100%"
 msgstr "Обычные рабочие дни оплачиваются 100%"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_form
 msgid "Notes"
 msgstr "Заметки"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__number_of_days
 msgid "Number of Days"
 msgstr "Кол-во дней"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__number_of_hours
 msgid "Number of Hours"
 msgstr "Количество часов"
 
 #. module: hr_payroll_community_v13
 #: model:res.groups,name:hr_payroll_community_v13.group_hr_payroll_community_v13_user
 msgid "Officer"
 msgstr "Должностное лицо"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Other Inputs"
 msgstr "Другие исходные данные"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__parent_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__parent_id
 msgid "Parent"
 msgstr "Родитель"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__parent_rule_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__parent_rule_id
 msgid "Parent Salary Rule"
 msgstr "Правило начисление заработной платы для руководства"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__partner_id
 msgid "Partner"
 msgstr "Партнёр"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__payslip_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__slip_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__payslip_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Pay Slip"
 msgstr "Расчетный листок"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "PaySlip Batch"
 msgstr "пакет платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.payslip_details_report
 msgid "PaySlip Details"
 msgstr "Подробности платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_payslip_lines_contribution_register
 msgid "PaySlip Lines"
 msgstr "Строки платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.action_contribution_register
 msgid "PaySlip Lines By Conribution Register"
 msgstr "Строки платежных ведомостей в реестре выплат"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "PaySlip Lines by Contribution Register"
 msgstr "Строки платежных ведомостей в реестре выплат"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "PaySlip Name"
 msgstr "Название расчетного листка"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.open_payroll_modules
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_root
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll"
 msgstr "Платёжная ведомость"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_report_hr_payroll_community_v13_report_contributionregister
 msgid "Payroll Contribution Register Report"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll Entries"
 msgstr "Строки платежной ведомости"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_v13_structure_filter
 msgid "Payroll Structures"
 msgstr "Структуры платежной ведомости"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll rules that apply to your country"
 msgstr "Правила составления платежной ведомости в вашей стране"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.action_report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Payslip"
 msgstr "Расчетный листок"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:82
 #, python-format
 msgid "Payslip 'Date From' must be earlier 'Date To'."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_run
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__payslip_run_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Payslip Batches"
 msgstr "Пакеты платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_payslip_lines
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__payslip_count
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Payslip Computation Details"
 msgstr "Детали вычисления платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_employee__payslip_count
 msgid "Payslip Count"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_report_hr_payroll_community_v13_report_payslipdetails
 msgid "Payslip Details Report"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_input
 msgid "Payslip Input"
 msgstr "Исходные данные платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__input_line_ids
 msgid "Payslip Inputs"
 msgstr "Исходные данные платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_line
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_form
 msgid "Payslip Line"
 msgstr "Строка расчетного листка"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_contribution_reg_payslip_lines
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__line_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Payslip Lines"
 msgstr "Строки платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Payslip Lines by Contribution Register"
 msgstr "Строки платежных ведомостей в реестре выплат"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_payslip_lines_contribution_register
 msgid "Payslip Lines by Contribution Registers"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__name
 msgid "Payslip Name"
 msgstr "Название платежной ведомости"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_worked_days
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__worked_days_line_ids
 msgid "Payslip Worked Days"
 msgstr "Рабочие дни платежной ведомости"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_hr_employee_payslip_list
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_employee__slip_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__slip_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.payroll_hr_employee_view_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_tree
 msgid "Payslips"
 msgstr "Расчетные листки"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payslip_run_tree
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payslip_run
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_tree
 msgid "Payslips Batches"
 msgstr "Пакеты платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid "Payslips by Employees"
 msgstr "Платежные ведомости сотрудников"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage
 msgid "Percentage (%)"
 msgstr "Процентное соотношение (%)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage_base
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage_base
 msgid "Percentage based on"
 msgstr "Процентное соотношение основано на"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Period"
 msgstr "Период"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Post payroll slips in accounting"
 msgstr "Отправить расчетники в бухгалтерию"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Print"
 msgstr "Печать"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_professionaltax1
 msgid "Professional Tax"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_providentfund1
 msgid "Provident Fund"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_python_compute
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_python_compute
 msgid "Python Code"
 msgstr "Код на Python"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_python
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_python
 msgid "Python Condition"
 msgstr "Условие для Python"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Python Expression"
 msgstr "Выражение для Python"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__quantity
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__quantity
 msgid "Quantity"
 msgstr "Количество"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "Quantity/Rate"
 msgstr "Количество/ ставка"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Quantity/rate"
 msgstr "Количество/ ставка"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Quarterly"
 msgstr "Ежеквартально"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Range"
 msgstr "Диапазон"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range
 msgid "Range Based on"
 msgstr "Диапазон основан на"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__rate
 msgid "Rate (%)"
 msgstr "Ставка (%)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__number
 msgid "Reference"
 msgstr "Ссылка"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Refund"
 msgstr "Возврат"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:102
 #, python-format
 msgid "Refund: "
 msgstr "Возврат: "
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__register_line_ids
 msgid "Register Line"
 msgstr "Строка регистрации"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 msgid "Rejected"
 msgstr "Отклонено"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__salary_rule_id
 msgid "Rule"
 msgstr "Правило"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_form
 msgid "Salary Categories"
 msgstr "Зарплатные категории"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Salary Computation"
 msgstr "Вычисление заработной платы"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_salary_rule
 msgid "Salary Rule"
 msgstr "Правило выплаты заработной платы"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_salary_rule_category
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_salary_rule_category_filter
 msgid "Salary Rule Categories"
 msgstr "Категории правила начисления зарплаты"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Salary Rule Category"
 msgstr "Категория правила начисления зарплаты"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_rule_input
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__input_id
 msgid "Salary Rule Input"
 msgstr "Исходные данные правила начисления зарплаты"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_salary_rule_form
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__rule_ids
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_action_hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_list
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_employee_grade_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Salary Rules"
 msgstr "Правила начисления зарплаты"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:403
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:453
 #, python-format
 msgid "Salary Slip of %s for %s"
 msgstr "Зарплатный листок от %s для %s"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payroll_community_v13_structure
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__struct_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_v13_structure_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_tree
 msgid "Salary Structure"
 msgstr "Структура зарплаты"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_view_hr_payroll_community_v13_structure_list_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_structure_view
 msgid "Salary Structures"
 msgstr "Структуры заработной платы"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__schedule_pay
 msgid "Scheduled Pay"
 msgstr "Оплата по графику"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Search Payslip Batches"
 msgstr "Поиск пакетов платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Search Payslip Lines"
 msgstr "Поиск строк платежных ведомостей"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Search Payslips"
 msgstr "Поиск по расчётным ведомостям"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Search Salary Rule"
 msgstr "Поиск правила начисления зарплаты"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Semi-annually"
 msgstr "Раз в полугодие"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__sequence
 msgid "Sequence"
 msgstr "Нумерация"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Set to Draft"
 msgstr "Сделать черновиком"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payroll_community_v13_configuration
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_global_settings
 msgid "Settings"
 msgstr "Настройки"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "States"
 msgstr "Регионы"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__state
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__state
 msgid "Status"
 msgstr "Статус"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__struct_id
 msgid "Structure"
 msgstr "Структура"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__code
 msgid ""
 "The code of salary rules can be used as reference in computation of other "
 "rules. In that case, it is case sensitive."
 msgstr ""
 "Код правил начисления заработных плат может быть использован как ссылка при "
 "выведении других правил. В таком случае, он чувствителен."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_worked_days__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_rule_input__code
 msgid "The code that can be used in the salary rules"
 msgstr "Этот код может быть использован в правилах начисления заработных плат"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_select
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_select
 msgid "The computation method for the rule amount."
 msgstr "Метод вычисления для выведения правила."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__contract_id
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_worked_days__contract_id
 msgid "The contract for which applied this input"
 msgstr "Контракт для которого использованы эти исходные данные"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range_max
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range_max
 msgid "The maximum amount, applied for this rule."
 msgstr "Максимальная сумма, применяемая для данного правила."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range_min
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range_min
 msgid "The minimum amount, applied for this rule."
 msgstr "Минимальная сумма, применяемая для данного правила."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range
 msgid ""
 "This will be used to compute the % fields values; in general it is on basic,"
 " but you can also use categories code fields in lowercase as a variable "
 "names (hra, ma, lta, etc.) and the variable basic."
 msgstr ""
 "Эта информация будет использоваться для вычисления % значений полей; в общем"
 " это основной, но вы также можете использовать поля категории кода в нижнем "
 "регистре как имена переменных (HRA, MA, LTA и т.д.) и различных оснований."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__total
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Total"
 msgstr "Всего"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Total Working Days"
 msgstr "Итого рабочих дней"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__upper_bound
 msgid "Upper Bound"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract_advantage_template__upper_bound
 msgid "Upper bound authorized by the employer for this advantage"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__sequence
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__sequence
 msgid "Use to arrange calculation sequence"
 msgstr "Используется для организации последовательности вычислений"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__appears_on_payslip
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__appears_on_payslip
 msgid "Used to display the salary rule on payslip."
 msgstr ""
 "Используется для выведения правила начисления зарплаты на расчетный листок."
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 msgid "Waiting"
 msgstr "Ожидание"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Weekly"
 msgstr "Еженедельно"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Day"
 msgstr "Отработанный день"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Days"
 msgstr "Отработанные дни"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Days & Inputs"
 msgstr "Отработанные дни и исходные данные"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__resource_calendar_id
 msgid "Working Schedule"
 msgstr "График работы"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:211
 #, python-format
 msgid "Wrong percentage base or quantity defined for salary rule %s (%s)."
 msgstr ""
 "Неправильный процент основания или величина, определяемая для Правила "
 "начисления зарплаты %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:217
 #, python-format
 msgid "Wrong python code defined for salary rule %s (%s)."
 msgstr ""
 "Неверный код python определенный для правила начисления зарплаты %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:240
 #, python-format
 msgid "Wrong python condition defined for salary rule %s (%s)."
 msgstr ""
 "Неверное условие для  python, определенное для правила начисления зарплаты "
 "%s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:204
 #, python-format
 msgid "Wrong quantity defined for salary rule %s (%s)."
 msgstr ""
 "Неверное количество, определенное для правила начисления зарплаты %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:234
 #, python-format
 msgid "Wrong range condition defined for salary rule %s (%s)."
 msgstr ""
 "Неверное условие диапазона, определенное для правила начисления зарплаты %s "
 "(%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:36
 #, python-format
 msgid "You cannot create a recursive salary structure."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:127
 #, python-format
 msgid "You cannot delete a payslip which is not draft or cancelled!"
 msgstr ""
 "Вы не можете удалить расчетный листок, который не является черновиком или не"
 " отменен!"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/wizard/hr_payroll_community_v13_payslips_by_employees.py:24
 #, python-format
 msgid "You must select employee(s) to generate payslip(s)."
 msgstr "Вы должны выбрать сотрудника(ов) чтобы создать расчетный листок(ки)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:525
 #, python-format
 msgid "You must set a contract to create a payslip line."
 msgstr "Нужно указать договор для создания расчётного листа."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage_base
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage_base
 msgid "result will be affected to a variable"
 msgstr "результат повлияет на переменную"
 