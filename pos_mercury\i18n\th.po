# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_mercury
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Buy a card reader"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>ซื้อเครื่องอ่านบัตร"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"<i>Vantiv Configurations</i> define what Vantiv account will be used when\n"
"                                processing credit card transactions in the Point Of Sale. Setting up a Vantiv\n"
"                                configuration will enable you to allow payments with various credit cards\n"
"                                (eg. Visa, MasterCard, Discovery, American Express, ...). After setting up this\n"
"                                configuration you should associate it with a Point Of Sale payment method."
msgstr ""
"<i>กำหนดค่า Vantiv</i> กำหนดว่าบัญชี Vantiv จะใช้เมื่อ\n"
"                               มีการประมวลผลธุรกรรมบัตรเครดิตในการขายหน้าร้าน การตั้งค่า Vantiv\n"
"                               การกำหนดค่าจะช่วยให้คุณสามารถชำระเงินด้วยบัตรเครดิตต่างๆ ได้\n"
"                                (เช่น Visa, MasterCard, Discovery, American Express, ...) หลังจากตั้งค่านี้แล้ว\n"
"                               การกำหนดค่าคุณควรเชื่อมโยงกับวิธีการชำระเงินการขายหน้าร้าน"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "ABOVE AMOUNT PURSUANT"
msgstr "สูงกว่าจำนวนตามที่"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "APPROVAL CODE:"
msgstr "รหัสการอนุมัติ:"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_barcode_rule
msgid "Barcode Rule"
msgstr "กฎของบาร์โค้ด"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "CARDHOLDER WILL PAY CARD ISSUER"
msgstr "ผู้ถือบัตรจะเป็นผู้จ่ายบัตร"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Cancel"
msgstr "ยกเลิก"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_brand
msgid "Card Brand"
msgstr "แบรนด์บัตร"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_number
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_order
msgid "Card Number"
msgstr "หมายเลขบัตร"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "Card Number Prefix"
msgstr "คำนำหน้าหมายเลขบัตร"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "Card Owner Name"
msgstr "ชื่อเจ้าของบัตร"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_tree
msgid "Card Reader"
msgstr "เครื่องอ่านบัตร"

#. module: pos_mercury
#: model_terms:ir.actions.act_window,help:pos_mercury.action_configuration_form
msgid "Configure your card reader"
msgstr "กำหนดค่าเครื่องอ่านบัตร"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Could not read card"
msgstr "ไม่สามารถอ่านบัตร"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: pos_mercury
#: model:ir.model.fields.selection,name:pos_mercury.selection__barcode_rule__type__credit
msgid "Credit Card"
msgstr "บัตรเครดิต"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"Credit card refunds are not supported. Instead select your credit card "
"payment method, click 'Validate' and refund the original charge manually "
"through the Vantiv backend."
msgstr ""
"ไม่รองรับการคืนเงินด้วยบัตรเครดิต "
"แทนที่จะเลือกวิธีการชำระเงินด้วยบัตรเครดิตของคุณ ให้คลิก 'ตรวจสอบ' "
"และคืนเงินค่าบริการเดิมด้วยตนเองผ่านส่วนหลังของ Vantiv"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__display_name
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"For quickly handling orders: just swiping a credit card when on the payment screen\n"
"                                (without having pressed anything else) will charge the full amount of the order to\n"
"                                the card."
msgstr ""
"สำหรับการจัดการคำสั่งอย่างรวดเร็ว: เพียงแค่รูดบัตรเครดิตเมื่ออยู่บนหน้าจอการชำระเงิน\n"
"                               (โดยไม่ต้องกดอย่างอื่น) จะเรียกเก็บเงินเต็มจำนวนที่สั่งไป\n"
"                                ที่บัตร"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/ProductScreen.js:0
#, python-format
msgid "Go to payment screen to use cards"
msgstr "ไปที่หน้าจอการชำระเงินเพื่อใช้บัตร"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Handling transaction..."
msgstr "กำลังจัดการธุรกรรม..."

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__id
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__id
msgid "ID"
msgstr "ไอดี"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "ID of the merchant to authenticate him on the payment provider server"
msgstr "ไอดีของผู้ค้าเพื่อตรวจสอบผู้ค้าบนเซิร์ฟเวอร์ผู้ให้บริการชำระเงิน"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"If you don't already have a Vantiv account, contact Vantiv at +1 (800) 846-4472\n"
"                                to create one."
msgstr ""
"ถ้าคุณยังไม่มีบัญชี Vantiv โปรดติดต่อ Vantiv ที่ +1 (800) 846-4472\n"
"                                เพื่อสร้าง"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Invoice number from Vantiv Pay"
msgstr "หมายเลขใบแจ้งหนี้จาก Vantiv Pay"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration____last_update
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "Merchant ID"
msgstr "ไอดีผู้ค้า"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid "Merchant Password"
msgstr "รหัสผู้ค้า"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__name
msgid "Name"
msgstr "ชื่อ"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__name
msgid "Name of this Vantiv configuration"
msgstr "ชื่อของการกำหนดค่า Vantiv นี้"

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No Vantiv configuration associated with the payment method."
msgstr "ไม่มีการกำหนดค่า Vantiv ที่เกี่ยวข้องกับวิธีการชำระเงิน"

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No opened point of sale session for user %s found."
msgstr "ไม่พบเซสชั่นการขายหน้าร้านที่เปิดอยู่สำหรับผู้ใช้%s "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from Vantiv (Vantiv down?)"
msgstr "ไม่มีการตอบสนองจาก Vantiv (Vantiv ตก?)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from server (connected to network?)"
msgstr "ไม่ได้รับการตอบกลับจากเซิร์ฟเวอร์ (เชื่อมต่อเครือข่าย?)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Odoo error while processing transaction."
msgstr "ข้อผิดพลาด Odoo ขณะประมวลผลธุรกรรม"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Ok"
msgstr "ตกลง"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Online Payment"
msgstr "ชำระเงินออนไลน์"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Partially approved"
msgstr "ได้รับการอนุมัติบางส่วน"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid ""
"Password of the merchant to authenticate him on the payment provider server"
msgstr "รหัสผ่านของร้านค้าเพื่อยืนยันตัวตนบนเซิร์ฟเวอร์ผู้ให้บริการชำระเงิน"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Pay with: "
msgstr "จ่ายด้วย: "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_record_no
msgid "Payment record number from Vantiv Pay"
msgstr "หมายเลขบันทึกการชำระเงินจาก Vantiv Pay"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Payment reference number from Vantiv Pay"
msgstr "หมายเลขอ้างอิงการชำระเงินจาก Vantiv Pay"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Please setup your Vantiv merchant account."
msgstr "โปรดตั้งค่าบัญชีผู้ค้า Vantiv ของคุณ"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_order
msgid "Point of Sale Orders"
msgstr "คำสั่งขายหน้าร้าน"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "วิธีการชำระเงินการขายหน้าร้าน"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment
msgid "Point of Sale Payments"
msgstr "การชำระเงินหน้าร้าน"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_configuration
msgid "Point of Sale Vantiv Configuration"
msgstr "การกำหนดค่าการขายหน้าร้าน Vantiv"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_mercury_transaction
msgid "Point of Sale Vantiv Transaction"
msgstr "ธุรกรรมการขายหน้าร้านของ Vantiv"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Refunds not supported"
msgstr "ไม่รองรับการคืนเงิน"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal failed, sending VoidSale..."
msgstr "การส่งกลับรายการล้มเหลว กำลังส่ง VoidSale..."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal succeeded"
msgstr "การส่งกลับสำเร็จ"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Sending reversal..."
msgstr "กำลังส่งกลับรายการ..."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "TO CARDHOLDER AGREEMENT"
msgstr "ข้อตกลงผู้ถือบัตร"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr "แบรนด์ของบัตรชำระเงิน (เช่น Visa, AMEX, ...)"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "The card number used for the payment."
msgstr "หมายเลขบัตรที่ใช้ชำระเงิน"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "The configuration of Vantiv used for this journal"
msgstr "การกำหนดค่าของ Vantiv ที่ใช้สำหรับรายการบันทึกนี้"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_number
msgid "The last 4 numbers of the card used to pay"
msgstr "เลขบัตร 4 ตัวท้ายเพื่อใช้ชำระ"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "The name of the card owner"
msgstr "ชื่อเจ้าของบัตร"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"This can be caused by a badly executed swipe or by not having your keyboard "
"layout set to US QWERTY (not US International)."
msgstr ""
"ซึ่งอาจเกิดจากการรูดอย่างไม่ถูกต้องหรือไม่ได้ตั้งค่ารูปแบบแป้นพิมพ์เป็น US "
"QWERTY (ไม่ใช่ US International)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Transaction approved"
msgstr "อนุมัติธุรกรรม"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_barcode_rule__type
msgid "Type"
msgstr "ประเภท"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"Using the Vantiv integration in the Point Of Sale is easy: just press the\n"
"                                associated payment method. After that the amount can be adjusted (eg. for cashback)\n"
"                                just like on any other payment line. Whenever the payment line is set up, a card\n"
"                                can be swiped through the card reader device."
msgstr ""
"ใช้การผสาน Vantiv กับการขายหน้าร้านนั้นเป็นเรื่องง่าย: เพียงกด\n"
"                                วิธีการชำระเงินที่เกี่ยวข้อง หลังจากนั้นสามารถปรับเปลี่ยนจำนวนเงินได้ (เช่น รับเงินคืน)\n"
"                                เช่นเดียวกับในไลน์การชำระเงินอื่น ๆ เมื่อใดก็ตามที่ตั้งค่าไลน์การชำระเงิน บัตร\n"
"                               สามารถรูดผ่านเครื่องอ่านบัตรได้"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "Vantiv Accounts"
msgstr "บัญชี Vantiv"

#. module: pos_mercury
#: model:ir.actions.act_window,name:pos_mercury.action_configuration_form
#: model:ir.ui.menu,name:pos_mercury.menu_pos_pos_mercury_config
msgid "Vantiv Configurations"
msgstr "กำหนดค่า Vantiv"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "Vantiv Credentials"
msgstr "ข้อมูลประจำตัว Vantiv"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Vantiv invoice number"
msgstr "หมายเลขใบแจ้งหนี้ Vantiv"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_record_no
msgid "Vantiv record number"
msgstr "หมายเลขบันทึก Vantiv"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Vantiv reference number"
msgstr "หมายเลขอ้างอิง Vantiv"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "VoidSale succeeded"
msgstr "VoidSale สำเร็จ"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "WAITING FOR SWIPE"
msgstr "กำลังรอรูด"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"We currently support the MagTek Dynamag card reader device. It can be connected\n"
"                                directly to the Point Of Sale device or it can be connected to the IoTBox."
msgstr ""
"ขณะนี้เรารองรับอุปกรณ์อ่านบัตร MagTek Dynamag ซึ่งสามารถเชื่อมต่อได้\n"
"                               โดยตรงกับอุปกรณ์การขายหน้าร้าน หรือสามารถเชื่อมต่อกับกล่องไอโอที"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "X______________________________"
msgstr "X______________________________"
