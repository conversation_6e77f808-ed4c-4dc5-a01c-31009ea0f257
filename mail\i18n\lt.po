# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <edgara<PERSON>@focusate.eu>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# Anatolij, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# UAB "Draugi<PERSON><PERSON> sprendimai" <<EMAIL>>, 2022
# <PERSON>dr<PERSON>lenskis <<EMAIL>>, 2022
# <PERSON>run<PERSON>. <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>as Versada <<EMAIL>>, 2022
# <PERSON>evicius <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Ramunė ViaLaurea <<EMAIL>>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr "Šis kanalas yra privatus. Norintys prisijungti turi būti pakviesti."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires \"%s\" access"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires microphone access"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "%(activity_name)s: %(summary)s assigned to you"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr "%(user_name)s pakvietė jus sekti %(document)s dokumentą: %(title)s"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s pakvietė jus sekti naują dokumentą."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Message"
msgstr "%d Žinutė"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Messages"
msgstr "%d Žinutės"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "%d days overdue"
msgstr "vėluoja %d dienų"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "%d days overdue:"
msgstr "vėluoja %d dienų:"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s ir %s rašo..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s and %s have reacted with %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s connected"
msgstr "%s prisijungė"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s sukurta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s from %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "%s has a request"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s has reacted with %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s rašo..."

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "%s started a live conference"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s ir kiti rašo..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and %s other persons have reacted with %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and 1 other person have reacted with %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s have reacted with %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "(from"
msgstr "(nuo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid ", "
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid ", enter to"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "-&gt;"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid ". Narrow your search to see more choices."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ".<br/>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Future"
msgstr "0 Ateityje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Late"
msgstr "0 Vėluoja"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Today"
msgstr "0 Šiandien"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b attrs=\"{'invisible': [('no_record', '=', False)]}\" class=\"text-"
"warning\">No record for this model</b>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<br><br>\n"
"            Type <b>@username</b> to mention someone, and grab his attention.<br>\n"
"            Type <b>#channel</b> to mention a channel.<br>\n"
"            Type <b>/command</b> to execute a command.<br>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">sukūrė <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">invited <a href=\"#\" data-oe-"
"model=\"res.partner\" data-oe-"
"id=\"%(new_partner_id)d\">%(new_partner_name)s</a> to the channel</div>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">joined the channel</div>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">left the channel</div>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p><b>Susirašinėkite su kolegomis</b>realiu laiku naudodami tiesiogines "
"žinutes.</p><p><i>Pirmiausia gali reikėti pakviesti vartotojus per nustatymų"
" programą.</i></p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"Čia <p><b>rašykite žinutę</b> kanalo nariams.</p> <p>Galite išsiųsti "
"pranešimą kam nors naudodami <i>'@'</i> ar susieti kitą kanalą naudodami "
"<i>'#'</i>. Pradėkite žinutę <i>'/'</i> , jei norite gauti visų įmanomų "
"komandų sąrašą.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Kanalai padeda lengvai organizuoti informaciją skirtingose temose ir "
"grupėse.</p> <p>Pabandykite <b>sukurti savo pirmąjį kanalą</b> (pvz., "
"pardavimai, rinkodara, produktas ABC, vakarėlis po darbo ar pan).</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a channel here.</p>"
msgstr "<p>Sukurkite kanalą čia.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a public or private channel.</p>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"<p>Dear Sender,<br /><br />\n"
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"Please make sure you are using the correct address or contact us at %(default_email)s instead.<br /><br />\n"
"Kind Regards,</p>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Masinis laiškų siuntimas</strong>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">pasirinktuose įrašuose</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">su esamu paieškos filtru</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Dokumento sekėjai ir</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    Jei norite tai išsiųsti visiems įrašams, sutampantiems su jūsų paieškos kriterijais, pažymėkite šį laukelį:\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    Jei norite naudoti tik pasirinktus įrašus, atžymėkite šį pasirinkimų laukelį :\n"
"                                </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "<span class=\"col-md-5 col-lg-4 col-sm-12 pl-0\">Force a language: </span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."
msgstr ""
"<span class=\"fa fa-info-circle\"/>Dėmesio: Nebus įmanoma siųsti šio laiško "
"pakartotinai tiems gavėjams, kurių nepasirinkote."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Activities</span>"
msgstr "<span class=\"o_form_label\">Veiklos</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Custom ICE server list</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Pridėti</span>\n"
"                                    <span class=\"o_stat_text\">Kontekstinį veiksmą</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Pašalinti</span>\n"
"                                    <span class=\"o_stat_text\">Kontekstinį veiksmą</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span>@</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""
"<strong>\n"
"                                    Visi įrašai, sutampantys su esamu paieškos filtru, bus išsiųsti,\n"
"                                    ne tik ID, kurie pažymėti sąrašo peržiūroje.\n"
"                                </strong><br/>\n"
"                                Laiškas bus išsiųstas visiems įrašams, pasirinktiems sąraše.<br/>\n"
"                                Šio vedlio patvirtinimas gali užtrukti kelias minutes, taip sustabdant jūsų naršyklę."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""
"<strong>Vidinė komunikacija:</strong>: atsakymas paskelbs vidinę pastabą. "
"Sekėjai negaus jokio pranešimo el. paštu."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""
"<strong>Bus naudojami tik įrašai, pažymėti sąrašo peržiūroje.</strong><br/>\n"
"                                Šis laiškas bus išsiųstas visiems įrašams, pažymėtiems sąraše."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr "<strong>Rekomenduojamos veiklos</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Python žodynas, kuris bus įvertinamas numatytųjų reikšmių nustatymui, kai "
"šiam pseudonimui sukuriami nauji įrašai."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_partner_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "A channel of type 'chat' cannot have more than two users."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "A next activity can only be planned on models that use the chatter"
msgstr ""
"Kitas veiksmas gali būti suplanuotas tik modeliams, kurie naudoja "
"susirašinėjimą"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr ""
"Trumpasis kodas yra klaviatūros kombinacija. Pavydžiui, jei parašote #gm, "
"tai bus pakeista į \"Good Morning\"."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Accept"
msgstr "Priimti"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Prieigos grupės"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "Prieigos raktas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "Veiksmas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Reikalingas veiksmas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
msgid "Action To Do"
msgstr "Atliktinas veiksmas"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Veiksmo lango peržiūra"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Numatytasis aktyvavimas prenumeruojant."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_channel__active
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr "Aktyvus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__active_domain
msgid "Active domain"
msgstr "Aktyvus domenas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#, python-format
msgid "Activities"
msgstr "Veiklos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_view.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
#, python-format
msgid "Activity"
msgstr "Veikla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Veiklos Išimties Dekoravimas"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Veiklos Mixin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Veiklos būsena"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Veiklos tipas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "Veiklos tipo ikona"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Veiklos tipai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "Activity User Type"
msgstr "Veiklos vartotojo tipas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "Veiklos tipas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "Pridėti paštą į juodąjį sąrašą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Pridėti sekėjų"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr ""
"Sekėjų pridėjimas gali būti atliktas tik el. pašto susirašinėjimo modeliui"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_mail__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_message__add_sign
msgid "Add Sign"
msgstr "Pridėti ženklą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.js:0
#, python-format
msgid "Add a Reaction"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add a description"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "Pridėti naują %(document)s arba siųsti laišką %(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/text_emojis.xml:0
#, python-format
msgid "Add an emoji"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Add attachment"
msgstr "Pridėti prisegtuką"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#, python-format
msgid "Add attachments"
msgstr "Pridėti prisegtukus"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Pridėti kontaktus, kuriuos norite informuoti..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Add or join a channel"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add users"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Papildomi kontaktai"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Išplėstinis"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr "Išplėstiniai nustatymai"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
msgid "Alert"
msgstr "Įspėjimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr "Pseudonimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_contact
msgid "Alias Contact Security"
msgstr "Pseudonimo kontakto apsauga"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "Pseudonimo domenas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_name
msgid "Alias Name"
msgstr "Pseudonimo vardas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_domain
msgid "Alias domain"
msgstr "Pseudonimo domenas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_model_id
msgid "Aliased Model"
msgstr "Pseudonimo modelis"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Pseudonimai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "All"
msgstr "Visi"

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "Kiekvienam priedui turi būti pateiktas prieigos simbolis (token)."

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "An email is required for find_or_create to work"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending an email."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "An error occurred while fetching messages."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "An unexpected error occurred during the creation of the chat."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And 1 other member."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Anonymous"
msgstr "Anoniminis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Taikomas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Apply"
msgstr "Taikyti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Archived"
msgstr "Archyvuotas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid "Are you sure you want to delete this message?"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_resend_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s mail delivery failures? You won't be "
"able to re-send these mails later!"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign to ..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign/unassign to me"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#, python-format
msgid "Assigned to"
msgstr "Priskirta"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""
"Priskirtas vartotojas %s neturi prieigos prie dokumento ir negali tvarkyti "
"šios veiklos."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Prisegti failą"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "Prisegtukas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Prisegtukų skaičius"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Attachment counter loading..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Prisegtukai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""
"Prisegtukai yra susieti su dokumentu per model / res_id ir su žinute per šį "
"lauką."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Autentifikuoti partneriai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Autorius"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Žinutės autorius. Jei nenustatyta, email_from gali turėti el. pašto adresą, "
"kuris nesutampa su partneriais."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Autoriaus atvaizdas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_public_id
msgid "Authorized Group"
msgstr "Leistina grupė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Automatinis trynimas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "Automatiškai prenumeruoti grupes"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_ids
msgid "Auto Subscription"
msgstr "Automatinė prenumerata"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Automatinė prenumerata"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "Automatizuota veikla"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
#, python-format
msgid "Avatar"
msgstr "Portretas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Avatar of OdooBot"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of guest"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of user"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "Išėjęs"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Bazė"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Best regards,"
msgstr "Pagarbiai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "Juodasis sąrašas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Juodojo sąrašo data"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "Turinys"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Bot"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
msgid "Bounce"
msgstr "Atmetimas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "Atmesta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Browser default"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread_cc.py:0
#, python-format
msgid "CC Email"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Skambutis"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Camera is off"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
msgid "Can Write"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Atšaukti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Atšaukti el. laišką"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Cancel notification in failure"
msgstr "Atšaukti nesėkmės pranešimą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "Atšaukta"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "Atšauktas"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Įrašytas atsakymas / Trumpas kodas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__canned_response_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__canned_response_ids
msgid "Canned Responses"
msgstr "Įrašyti atsakymai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Žinutės kopijos gavėjai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "Žinutės kopijos gavėjai (čia gali būti panaudotos vietos žymos)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Change Layout"
msgstr "Keisti išdėstymą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Change layout"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "Pakeiskite su šiuo tipu susijusių veiklų fono spalvą."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Changed"
msgstr "Pakeista"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__channel_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "Kanalas"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(guest_names)s"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(partner_names)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_partner_id
msgid "Channel Partner"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Kanalo tipas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Channel settings"
msgstr "Kanalo nustatymai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr "Kanalai"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr "Kanalai / partneris"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__chat
#, python-format
msgid "Chat"
msgstr "Pokalbis"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr "Pokalbio trumpas kodas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Dukteriniai pranešimai"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Choose an example"
msgstr "Pasirinkite pavyzdį"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "Click here to retry"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Click on your message"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Click to see the attachments"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
#, python-format
msgid "Close"
msgstr "Uždaryti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Close (Esc)"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close chat window"
msgstr "Uždaryti susirašinėjimo langą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close conversation"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__closed
msgid "Closed"
msgstr "Uždaryta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Kableliu atskirti žinutės kopijos gavėjų adresai"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Kableliu atskirti gavėjų partnerių ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"Kableliu atskirti gavėjų partnerių ID (čia gali būti panaudotos vietos "
"žymos)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Kableliu atskirti gavėjų adresai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""
"Kableliu atskirti gavejų adresai (vietos žymos gali būti panaudotos čia)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Komentaras"

#. module: mail
#: model:ir.model,name:mail.model_res_company
msgid "Companies"
msgstr "Įmonės"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/mail_template/mail_template.js:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "Rašyti el. laišką"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Kūrimo režimas"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Konfigūruoti veiklos tipus"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "Konfigūruokite savo el. pašto serverius"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Confirm"
msgstr "Patvirtinti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Patvirtinimas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Congratulations, you're done with your activities."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "Sveikiname, jūsų laiškų dėžutė - tuščia! "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/widgets/discuss/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "Sveikiname, jūsų laiškų dėžutė - tuščia! "

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Ryšio klaida (išeinančio pašto serverio problema)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Kontaktas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Kontaktai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_partner__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this partner. This includes: creating, joining, pinning, "
"and new message posted."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Turinys"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "Turinys"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fold_state
msgid "Conversation Fold State"
msgstr "Pokalbio sutraukimo būsena"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_minimized
msgid "Conversation is minimized"
msgstr "Pokalbis sumažintas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Conversations"
msgstr "Pokalbiai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Šiam kontaktui nepristatytų laiškų kiekio skaičiavimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "Valstybė"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Create"
msgstr "Sukurti"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Next Activity"
msgstr "Sukurti kitą veiksmą"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Create group chat"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Create or search channel..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Created"
msgstr "Sukurtas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Sukūrė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Creating a new record..."
msgstr "Kuria naują įrašą..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Sukūrimo data"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "Valiuta"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"Esamas vartotojas turi žvaigždute pažymėtą pranešimą, susietą su šia žinute"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Pasirinktinis peradresuotas pranešimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__custom_channel_name
msgid "Custom channel name"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Data"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Dienos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Deadline"
msgstr "Galutinis terminas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Deafen"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Gerb."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Dekoravimo tipas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Numatytasis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__default_display_mode
msgid "Default Display Mode"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Standartinis vartotojas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_template__null_value
msgid "Default Value"
msgstr "Numatytoji reikšmė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_defaults
msgid "Default Values"
msgstr "Numatytosios reikšmės"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Numatytieji gavėjai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Numatytieji įrašo gavėjai:\n"
"- partneris (naudojant iD partneriui arba partner_id lauką) ARBA\n"
"- el. paštas (naudojant email_from aba email lauką)"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Define a new chat shortcode"
msgstr "Nustatyti naują susirašinėjimo trumpąjį kodą"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "Delsimo tipas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Delay after releasing push-to-talk"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "Delsimo vienetai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "Trinti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Ištrinti el. laiškus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr "Ištrinti pranešimo kopiją"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Pristatyti nepavyko"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Delivery failure"
msgstr "Pristatymo klaida"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Aprašymas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Aprašymas, kuris bus pridėtas žinutėje, paskelbtoje šiam potipiui. Jei "
"tuščia, bus pridedamas vardas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Direct Messages"
msgstr "Tiesioginės žinutės"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Discard"
msgstr "Atmesti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Discard delivery failures"
msgstr "Atmesti pristatymo klaidas"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_cancel_action
msgid "Discard mail delivery failures"
msgstr "Atmesti pašto pristatymo klaidas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Discard message delivery failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
#, python-format
msgid "Disconnect"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "Disconnected from the RTC call by the server"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: code:addons/mail/static/src/models/discuss/discuss.js:0
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Discuss"
msgstr "Diskusija"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion Channel"
msgstr "Diskusijų kanalas"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Diskusijos"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_cancel
msgid "Dismiss notification for resend by model"
msgstr "Pašalinti pranešimą apie modelio pakartotinio siuntimą"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Susijusiuose dokumentuose su šiuo šablonu rodyti pasirinkimą atidaryti "
"kūrimo vedlį"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__res_name
msgid "Display name of the related document."
msgstr "Rodomas susieto dokumento pavadinimas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"Nelaikyti laiško kopijos dokumento komunikacijos istorijoje (tik masiniame "
"laiškų siuntime)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#, python-format
msgid "Do you really want to delete \"%s\"?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Dokumentas"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Dokumento sekėjai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
msgid "Document Model"
msgstr "Dokumento modelis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Dokumento pavadinimas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentacija"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Done"
msgstr "Atlikta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Launch Next"
msgstr "Atlikti ir paleisti kitą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "Baigti ir suplanuoti kitą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Download"
msgstr "Atsisiųsti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Download logs"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/drop_zone/drop_zone.xml:0
#, python-format
msgid "Drag Files Here"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Dropdown menu"
msgstr "Išsiskleidžiantis meniu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
msgid "Due Date"
msgstr "Pabaigos terminas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "Termino data po"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Due in %d days"
msgstr "Terminas - %d dienų"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Due in %d days:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Due on"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "Termino tipas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr "Dinaminio vietos rezervavimo ženklo generatorius"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Edit"
msgstr "Redaguoti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Redaguoti partnerius"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Edit Subscription of"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Edit subscription"
msgstr "Redaguoti prenumeratą"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "El. paštas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "El. pašto adresas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr "El. pašto pseudonimas"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "El. pašto pseudonimai"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "El. pašto pseudonimų Mixin"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "El. pašto juodasis sąrašas"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "El. pašto konfigūracija"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "El. laiško peržiūra"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "El. pašto paieška"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "El. laiško šablonas"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "El. laiško šablono peržiūra"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
msgid "Email Templates"
msgstr "El. laiškų šablonai"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "El. pašto diskusija"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "El. paštas jau yra!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Siuntėjo el. pašto adresas. Šis laukas yra nustatomas, kai nerandamas "
"sutampantis partneris, ir jis pakeičia autoriaus ID lauką susirašinėjime."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to which replies will be redirected"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Email alias %(alias_name)s cannot be used on %(count)d records at the same "
"time. Please update records one by one."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "El. Pašto CC"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "El. laiško kūrimo vedlys"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "Laiško žinutė"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "El. pašto pakartotinio siuntimo vedlys"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "El. laiškai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Emojis"
msgstr "Šypsenėlės"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Enable desktop notifications to chat."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Envelope Example"
msgstr "Voko pavyzdys"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "Klaida"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "Klaidos pranešimas"

#. module: mail
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "Klaida susisiekiant su platintojo garantijos serveriu."

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"Klaida be išimties. Tikriausiai dėl pasikartojančio prieigos atnaujinimo "
"pranešimų įrašuose. Kreipkitės į administratorių."

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr "Klaida be išimties. Tikėtina, kad dėl siuntimo nesuskaičiavus gavėjų."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Klaida, partneris negali sekti to paties projekto du kartus."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__public
msgid "Everyone"
msgstr "Visi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "Išimtis"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__member_count
msgid "Excluding guests from count."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Exit full screen"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Išplėstiniai filtrai..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Nepavykę laiškai"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Nepavyko"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render QWeb template : %s)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template : %s)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render template : %(xml_id)s (%(view_id)d)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Nesėkmės priežastis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "Klaidos priežastis"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Nesėkmės priežastis. Tai dažniausiai yra išimtinė situacija, atmesta el. "
"pašto serverio, saugoma tam, kad palengvintų laiškų klaidų šalinimą."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Klaidos tipas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Pamėgo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Feedback"
msgstr "Atsiliepimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field
msgid "Field"
msgstr "Laukas"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "Laukas \"Laiškų gija\" negali būti pakeistas į neigiamą."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_desc
msgid "Field Description"
msgstr "Lauko aprašymas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_type
msgid "Field Type"
msgstr "Lauko tipas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Laukas, kuris naudojamas susieti susijusį modelį su potipio modeliu, kai "
"susijusiam dokumentui naudojama automatinė prenumerata. Šis laukas taip pat "
"naudojamas suskaičiuoti getattr(related_document.relation_field)."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Laukai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr "Galutinė vietos ženklo išraiška, kopijuojama į norimą šablono lauką."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or create a channel..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or start a conversation..."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__folded
msgid "Folded"
msgstr "Sutrauktas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Follow"
msgstr "Sekti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
#, python-format
msgid "Followers"
msgstr "Sekėjai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekėjai (partneriai)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Sekėjų forma"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Followers of"
msgstr "Sekėjai"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Tik sekėjams"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Following"
msgstr "Sekama"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome piktograma, pvz., fa-tasks"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "Formatuotas el. paštas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "Nuo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Full composer"
msgstr "Pilnas kūrimas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Full screen"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Future"
msgstr "Būsimi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Būsimos veiklos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "Sietuvas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Generic User From Record"
msgstr "Standartinis vartotojas iš įrašo"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Eiti į nustatymų skydelį"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__group
msgid "Group"
msgstr "Grupė"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Grupuoti pagal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Group Name"
msgstr "Grupės pavadinimas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Grupuoti pagal..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Grouped Chat"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr "Grupės"

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
#, python-format
msgid "Guest"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name cannot be empty."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name is too long."
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.mail_guest_menu
msgid "Guests"
msgstr "Svečiai"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP nukreipimas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "Tvarkyti pagal el. paštą"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Tvarkyti \"Odoo\""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__has_cancel
msgid "Has Cancel"
msgstr "Turi atšaukimą"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Yra paminėtas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "Turi žinutę"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
#: model:ir.model.fields,help:mail.field_mail_mail__has_error
#: model:ir.model.fields,help:mail.field_mail_message__has_error
msgid "Has error"
msgstr "Turi klaidą"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Antraštės"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "Sveiki"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Hello,"
msgstr "Sveiki,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__help_message
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Help message"
msgstr "Pagalbos žinutė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Paslėpta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Hide Member List"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Prenumeratoriaus nustatymuose paslėpti potipį"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "History"
msgstr "Istorija"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.mail_channel_ice_servers_menu
msgid "ICE servers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_channel__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Tėvinio įrašo ID, kuris laiko savyje dukterinį įrašą (pvz.: projektas, "
"laikantis sukurtas užduotis)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "Piktograma"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Išimties veiklą žyminti piktograma."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Sekamo ištekliaus ID"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "Identity"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Idle"
msgstr "Pasyvus"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_unread
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeigu pažymėta, naujiems pranešimams reikės jūsų dėmesio."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jei pažymėta, yra žinučių, turinčių pristatymo klaidų."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr ""
"Jei pažymėta, partneriai gaus laišką, perspėjantį, kad jie buvo pridėti prie"
" dokumento sekėjų."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked in the chatter. "
"Value is used to order tracking values."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expressions "
"expression."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,help:mail.field_mail_channel__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Jei nustatyta, šis turinys bus automatiškai išsiųstas neįgaliotiems "
"vartotojams, vietoj numatyto pranešimo."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Jei el. paštas yra juodajame sąraše, kontaktas negaus jokių masinių laiškų "
"iš jokių sąrašų"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Jei nustatėte visų laiškų gavimo domeną, nukreiptą į \"Odoo\" serverį, "
"įveskite to domeno pavadinimą čia."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red envelope next"
" to each message."
msgstr ""
"Jei norite siųsti pakartotinai, paspauskite \"Atšaukti dabar\", tada "
"paspauskite pranešimą ir peržiūrėkite juos po vieną ir paspauskite raudoną "
"voką prie kiekvienos žinutės."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all failures"
msgstr "Ignoruoti visas klaidas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#, python-format
msgid "Image"
msgstr "Paveikslėlis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "Paveikslėlis "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "Paveikslėlis 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "Paveikslėlis 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "Paveikslėlis 512"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "Neaktyvus pseudonimas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "Gauti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Incoming Call..."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr "Parodo, kad ši veikla buvo sukurta automatiškai, o ne vartotojo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Info"
msgstr "Informacija"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "Pradinis modelis"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,help:mail.field_mail_mail__parent_id
#: model:ir.model.fields,help:mail.field_mail_message__parent_id
msgid "Initial thread message."
msgstr "Pradinė susirašinėjimo žinutė."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Input device"
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr "Integracijos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Tik vidinis"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Negalimas el. pašto adresas"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "Negalimas el. pašto adresas %r"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Negalima išraiška, tai turi būti raidinė Python žodyno reikšmė, pvz.  "
"\"{'field': 'value'}\""

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr "Negalimas pirminis el. pašto laukas modelyje %s"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invitation Link"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Invite Follower"
msgstr "Kviesti sekėją"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invite people"
msgstr "Kviesti žmones"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to Channel"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to group chat"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Kvietimo vedlys"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__private
msgid "Invited people only"
msgstr "Tik pakviestiems žmonėms"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "Yra sekėjas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_member
msgid "Is Member"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Yra perskaityta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_chat
msgid "Is a chat"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_pinned
msgid "Is pinned on the interface"
msgstr "Yra prisegtas sąsajoje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "Issue with audio"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr "Prisijungti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Call"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Join Channel"
msgstr "Prisijungti prie kanalo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Video Call"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr "Prisijungti prie grupės"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "LIVE"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Kalba"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fetched_message_id
msgid "Last Fetched"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__last_interest_dt
msgid "Last Interest"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session____last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers____last_update
#: model:ir.model.fields,field_description:mail.field_mail_guest____last_update
#: model:ir.model.fields,field_description:mail.field_mail_ice_server____last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype____last_update
#: model:ir.model.fields,field_description:mail.field_mail_notification____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template_preview____last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value____last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__seen_message_id
msgid "Last Seen"
msgstr "Paskutinį kartą matytas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "Paskutinį kartą atnaujinta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Late"
msgstr "Vėluoja"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Vėluojančios veiklos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__layout
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Išdėstymas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Leave"
msgstr "Laisvadienis"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Leave this channel"
msgstr "Palikti šį kanalą"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_cc__email_cc
msgid "List of cc from incoming emails."
msgstr "Gaunamų el. laiškų kopijų sąrašas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr ""
"Sąrašas partnerių, kurie bus pridėti prie esamo dokumento kaip klausytojai."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "List users in the current channel"
msgstr "Parodyti esamo kanalo vartotojų sąrašą"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Kanalo klausytojai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Load more"
msgstr "Įkelti daugiau"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Loading"
msgstr "Kraunama"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Loading..."
msgstr "Kraunama..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Registruoti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log a note"
msgstr "Registruoti pastabą"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Log a note..."
msgstr "Registruoti pastabą..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr "Registruoti veiklą"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr "Registruoti vidinę pastabą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Log an internal note..."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Log in the original discussion thread"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log note"
msgstr "Registruoti pastabą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Logged as"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
#, python-format
msgid "Mail"
msgstr "Paštas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Pašto veiklos tipas"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "Pašto juodasis sąrašas"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "Pašto juodojo sąrašo Mixin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr "Laiško kanalo forma"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "Mail Failures"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_rtc_session
msgid "Mail RTC session"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "Laiško šablonas"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "El. pašto susirašinėjimas"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Laiško sekimo reikšmė"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"Laiškas buvo sukurtas pranešti žmonėms apie egzistuojančią pašto žinutę"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr "Paštas: El. pašto eilių valdiklis"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "Pašto dėžutė nepasiekiama - %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Mailboxes"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_partner__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_users__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pagrindinis prisegtukas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "Manage Messages"
msgstr "Valdyti pranešimus"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Mark Done"
msgstr "Pažymėti kaip atliktą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Mark all read"
msgstr "Pažymėti visus skaitytus"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr "Pažymėti kaip atliktą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Mark as Read"
msgstr "Payžmėti kaip skaitytą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "Pažymėti kaip atliktiną"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Mark as done"
msgstr "Pažymėti kaip atliktą"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Susitikimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__member_count
msgid "Member Count"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr "Nariai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Šių grupių nariai automatiškai taps prenumeratoriais. Turėkite omenyje, kad "
"jie galės valdyti savo prenumeratą savarankišai."

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "Partnerių sujungimo vedlys"

#. module: mail
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "Sujungta su šiais partneriais:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#, python-format
msgid "Message"
msgstr "Žinutė"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message #%s..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message %s..."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Žinutės pristatymo klaida"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "Žinutės ID"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Žinučių pranešimai"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Žinutės įrašo pavadinimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Žinutės tipas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Message delivery failure image"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__description
#: model:ir.model.fields,help:mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr "Žinutės aprašymas: arba tema, arba aprašymo pradžia"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Message posted on \"%s\""
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Žinutės gavėjai (el. pašto adresai)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "Žinučių nuorodos, tokios kaip buvusių žinučių identifikatoriai"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid EmailMessage instance"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Žinutės potipis suteikia tikslesnį tipą žinutei,  ypatingai sistemos "
"pranešimams. Pavyzdžiui, tai gali būti pranešimas, susijęs su nauju įrašu "
"(Naujas) ar su stadijos pasikeitimu procese (Stadijos pasikeitimas). Žinučių"
" potipiai leidžia tiksliai nustatyti, kokius pranešimus vartotojas nori "
"matyti savo sienoje."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Žinučių potipiai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Sekami žinutės potipiai, reiškiantys potipius, kurie bus rodomi vartotojo "
"sienoje."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Žinutės tipas: el. paštas pašto žinutei, pranešimas sistemos žinutei, "
"komentaras kitoms žinutėms, tokioms, kaip vartotojų atsakymai"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Unikalus žinutės identifikatorius"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Message-Id"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "Žinutės"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Žinutės paieška"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr ""
"Žinutės gali būti <b>pažyminmos žvaigždute</b>, kad vėliau būtų lengviau "
"prisiminti patikrinti jas."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Žinutės su vidiniais potipiais bus matomos tik darbuotoju, t.y., base_user "
"grupės nariams"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Messages with tracking values cannot be modified"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Minimum activity for voice detection"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email addresss"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr "Šablonas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "Modelis turi pakeitimą"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Prenumeruojamų išteklių modelis"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Modelis, kuriam taikomas šis potipis. Jei neigiamas, šis potipis taikomas "
"visiems modeliams."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Modeliai"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"Modelio keitimas gali turėti įtakos esamoms šio tipo veikloms, būkite "
"atsargūs."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Modulio ištrynimas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Mėnesiai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "More"
msgstr "Daugiau"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Mute"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Veiklos paskutinis terminas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_channel__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
msgid "Name"
msgstr "Pavadinimas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,help:mail.field_mail_mail__record_name
#: model:ir.model.fields,help:mail.field_mail_message__record_name
msgid "Name get of the related document."
msgstr "Susijusio dokumento pavadinimo gavimas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""
"Pavadinimas, naudojamas sugeneruotam ataskaitos failui (gali turėti vietos ženklų)\n"
"Plėtinys gali būti neįtrauktas ir tokiu atveju ateis iš ataskaitos tipo."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model:ir.model.fields,help:mail.field_mail_mail__needaction
#: model:ir.model.fields,help:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Reikia veiksmo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "New Channel"
msgstr "Naujas kanalas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Naujos vertės ženklas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Nauja datos reikšmė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Nauja skaičiaus su kableliu reikšmė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Nauja sveikojo skaičiaus reikšmė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_monetary
msgid "New Value Monetary"
msgstr "Naujos vertės valiuta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Naujos reikšmės tekstas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/chat_window/chat_window.js:0
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "New message"
msgstr "Nauja žinutė"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages"
msgstr "Naujos žinutės"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages appear here."
msgstr "Naujos žinutės atsiranda čia"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Next"
msgstr "Kitas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Next (Right-Arrow)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Kitos veiklos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#, python-format
msgid "Next Activity"
msgstr "Kitas veiksmas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Kito veiksmo terminas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Kito veiksmo santrauka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Kito veiksmo tipas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Kitos galimos veiklos"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr "Nėra klaidos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "No IM status available"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No channel found"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "No conversation selected."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_list/notification_list.xml:0
#, python-format
msgid "No conversation yet..."
msgstr "Dar nėra pokalbio..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No history messages"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No starred messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "Atsakymams nėra diskusijos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No user found"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "No user found that is not already a member of this channel."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "No users found"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
msgid "None"
msgstr "Nieko"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:mail.message.subtype,name:mail.mt_note
#, python-format
msgid "Note"
msgstr "Pastaba"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Pranešimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_delete_notification
#: model:ir.cron,name:mail.ir_cron_delete_notification
msgid "Notification: Delete Notifications older than 6 Month"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "Pranešimai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notify
msgid "Notify followers"
msgstr "Informuoti sekėjus"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Informuoti prenumeratorius apie dokumentą (tik masiniams įrašams)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Veiksmų skaičius"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"Dienų/savaičių/mėnesių skaičius prieš įvykdant veiksmą. Tai leidžia planuoti"
" veiksmo terminą."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Klaidų kiekis"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Žinučių, kurioms reikia jūsų veiksmo, skaičius"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Žinučių su pristatymo klaida skaičius"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_users__message_unread_counter
msgid "Number of unread messages"
msgstr "Neperskaitytų žinučių skaičius"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_alert/notification_alert.xml:0
#, python-format
msgid ""
"Odoo Push notifications have been blocked. Go to your browser settings to "
"allow them."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid ""
"Odoo will not have the permission to send native notifications on this "
"device."
msgstr "Odoo neturės leidimo siųsti jums pranešimų į šį įrenginį."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Offline"
msgstr "Atsijungęs"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "Gerai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Senos vertės ženklas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Sena datos lauko reikšmė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Sena skaičiaus su kableliu reikšmė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Sena sveikojo skaičiaus reikšmė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_monetary
msgid "Old Value Monetary"
msgstr "Senos vertės valiuta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Senos vertės tekstas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""
"Kai žinutė pažymima žvaigždute, galite bet kada sugrįžti ir ją čia "
"peržiūrėti. "

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_unique_user_id
msgid "One user should only have one mail user settings."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Online"
msgstr "Prisijungęs"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to use grouped read on message model"
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "Tik nestandartiniai modeliai gali būti redaguojami."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only logged notes can have their content updated on model '%s'"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only messages type comment can have their content updated"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Only messages type comment can have their content updated on model "
"'mail.channel'"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Only users belonging to the \"%s\" group can modify dynamic templates."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__open
msgid "Open"
msgstr "Atidaryti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr "Atidaryti dokumentą"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr "Atidaryti tėvinį dokumentą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "Open chat"
msgstr "Atidaryti pokalbį"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_activity_notice/rtc_activity_notice.xml:0
#, python-format
msgid "Open conference:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Open in Discuss"
msgstr "Atidaryti Diskusijose"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Open profile"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Pasirinktinis įrašo ID, prie kurio bus prisegti visi įeinantys laiškai, net "
"jei į jį ir neatsakė. Jei nustatyta, tai visiškai išjungs naujų įrašų "
"kūrimą."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Pasirinktinis pageidaujamas serveris išeinantiems laiškams. Jei nenustatyta,"
" bus naudojamas tas, kurio prioritetas aukščiausias."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template
msgid "Optional report to print and attach"
msgstr "Papildoma ataskaita prisegimui ir spausdinimui"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Papildoma reikšmė nustatymui, jei nurodytas laukas yra tuščias"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Original message was deleted"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Išeinantys"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Išsiunčiamų laiškų serveris"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Išeinantys laiškai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Išsiunčiamų laiškų serveris"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "Vėluoja"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Nustelbti autoriaus el. laišką"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_user_id
msgid "Owner"
msgstr "Savininkas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "PDF file"
msgstr "PDF failas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Tėvinis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Tėvinis pranešimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_model_id
msgid "Parent Model"
msgstr "Tėvinis modelis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Tėvinio įrašo temos ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Tėvinis modelis, turintis pseudonimą. Modelis turintis pseudonimo nurodą "
"nebūtinai yra modelis, suteiktas alias_model_id (pvz.: projektas "
"(parent_model) ir užduotis (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Tėvinis potipis, naudojamas automatiniam prenumeravimui. Šis laukas "
"neteisingai pavadintas. Pavyzdžiui, projekte projekto potipių parent_id "
"nurodo su užduotimi susijusius potipius."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
msgid "Partner"
msgstr "Partneris"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "Partnerio profilis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
msgid "Partner Readonly"
msgstr "Partneris tik skaitymui"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Partneriai, reikalaujantys veiksmo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid ""
"Pay attention: The followers of this document who were notified by email "
"will still be able to read the content of this message and reply to it."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "Permission denied"
msgstr "Leidimas neduotas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "Telefonas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template__copyvalue
msgid "Placeholder Expression"
msgstr "Vietaženklio išraiška"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#, python-format
msgid "Planned"
msgstr "Suplanuota"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_box/activity_box.xml:0
#, python-format
msgid "Planned activities"
msgstr "Suplanuotos veiklos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "Suplanuota"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient/composer_suggested_recipient.js:0
#, python-format
msgid "Please complete customer's information"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "Susisiekite su mumis, užuot naudoję"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.js:0
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Please wait while the file is uploading."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_container/chatter_container.xml:0
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Please wait..."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"Politika susirašinėjimo pranešimų valdymui:\n"
"- Tvarkyti pagal el. paštus: pranešimai siunčiami į jūsų paštą\n"
"- Tvarkyti per \"Odoo\": pranešimai pasirodo jūsų \"Odoo\" dėžutėje"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Žinutės paskelbimo prie dokumento politika naudojant pašto serverį.\n"
"- visi: visi gali paskelbti\n"
"- partneriai: tik patvirtinti partneriai\n"
"- sekėjai: tik susijusio dokumento sekėjai arba sekančių kanalų nariai\n"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Granted"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Revoked"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_post
msgid "Post on Multiple Documents"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Post your message on the thread"
msgstr "Paskelbkite savo žinutę susirašinėjime"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message with channels as listeners is not supported since Odoo "
"14.3+. Please update code accordingly."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Powered by"
msgstr "Technologija"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Ankstesnės veiklos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Press a key to register it as the push-to-talk shortcut"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Peržiūra"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "Peržiūra"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Previous"
msgstr "Ankstesnis"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Previous (Left-Arrow)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Ankstesnės veiklos tipas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Print"
msgstr "Spausdinti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr "Privatumas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Private channel"
msgstr "Privatus kanalas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category/discuss_sidebar_category.js:0
#, python-format
msgid "Public Channels"
msgstr "Vieši kanalai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Public channel"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Leidėjo garantijos kontraktas"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr "Leidėjas: atnaujinimo pranešimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Push-to-talk key"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Quick search..."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_session_ids
msgid "RTC Sessions"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.mail_channel_rtc_session_menu
msgid "RTC sessions"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Re:"
msgstr "Re:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reaction"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
msgid "Reactions"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#, python-format
msgid "Ready"
msgstr "Paruošta"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Paruošta siųsti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "Priežastis"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Gauta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s and %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s, %s and more"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by Everyone"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Gavėjas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "Gavėjai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Rekomenduojamas veiklos tipas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "Įrašas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Įrašo temos ID"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Redirect to another email address"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Rekomendacijos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Refuse"
msgstr "Atmesti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "Linkėjimai,"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Register new key"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Reject"
msgstr "Atmesti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "Susijusi įmonė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "Susijusio dokumento ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Susijusio dokumento modelis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Susijusio dokumento modelio pavadinimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Susijusi žinutė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Susijęs partneris"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Sąryšio laukas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Remove"
msgstr "Pašalinti"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Norėdami naudoti šį šabloną susijusiems dokumentams, pašalinkite kontekstinį"
" veiksmą."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Remove this follower"
msgstr "Pašalinti šį sekėją"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Atsakyti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "Atsakyti (kam)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Atsakymo el. pašto adresas. reply_to nustatymas apeina automatinį "
"susirašinėjimo gijos sukūrimą."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "Atsakyti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Replying to"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_name
msgid "Report Filename"
msgstr "Ataskaitos failo pavadinimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__res_users_settings_ids
msgid "Res Users Settings"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Resend mail"
msgstr "Persiųsti paštą"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Resend to selected"
msgstr "Persiųsti pasirinktiems"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "Persiuntimo vedlys"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Reset Zoom"
msgstr "Atstatyti priartinimą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Reset Zoom (0)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
msgid "Responsible"
msgstr "Atsakingas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Atsakingas vartotojas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates and Jinja rendering."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Pakartoti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Rich-text Contents"
msgstr "Raiškaus teksto turinys"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Raiškus tekstas/HTML žinutė"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_inviting_session_id
msgid "Ringing session"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Rotate"
msgstr "Pasukti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Rotate (r)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__rtc_session_ids
msgid "Rtc Session"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP serveris"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Pardavėjas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Save"
msgstr "Išsaugoti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Išsaugoti kaip naują šabloną"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr "Išsaugoti kaip naują šabloną"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Suplanuoti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/activity/activity.js:0
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Schedule Activity"
msgstr "Suplanuoti veiklą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule activities to help you get things done."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Schedule activity"
msgstr "Suplanuoti veiklą"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "Planuoti veiklą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "Suplanuoti veiklą"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "Suplanuota data"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Suplanuota siuntimo data"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr "Ieškoti pseudonimo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr "Ieškoti grupėse"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.js:0
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#, python-format
msgid "Search user..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s and %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s, %s and more"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by Everyone"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Select a user..."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Pasirinkite tikslinį lauką iš susijusio dokumento modelio.\n"
"Jei tai yra santykio laukas, galėsite pasirinkti tikslinį lauką santykio tiksle."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"Select the action to do on each mail and correct the email address if "
"needed. The modified address will be saved on the corresponding contact."
msgstr ""
"Pasirinkite atliekamą veiksmą kiekvienam laiškui ir, jei reikia, teisingą "
"pašto adresą. Pakeistas adresas bus išsaugotas susijusiame kontakte."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__groups
#, python-format
msgid "Selected group of users"
msgstr "Pasirinkta vartotojų grupė"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Selected users:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Siųsti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Send Again"
msgstr "Siųsti dar kartą"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__send_mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__email
msgid "Send Email"
msgstr "Siųsti el. laišką"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "Siųsti laišką (%s)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Siųsti dabar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send a message"
msgstr "Rašyti žinutę"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Send a message to followers..."
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send message"
msgstr "Siųsti žinutę"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Siuntėjo adresas (čia gali būt naudojami vietos ženklai). Jei nenustatyta, "
"numatytoji reikšmė bus autoriaus el. pašto pseudonimas (jei sukonfigūruotas)"
" arba el. pašto adresas."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "Išsiųsta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Seka"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Serverio veiksmas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Settings"
msgstr "Nustatymai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Share screen"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift left"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift right"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__description
#: model:ir.model.fields,field_description:mail.field_mail_message__description
msgid "Short description"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Trumpieji kodai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Trumpinys"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show All"
msgstr "Rodyti viską"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#, python-format
msgid "Show Followers"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Show Member List"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Show a helper message"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr ""
"Rodyti visus įrašus, kurių sekančio veiksmo data yra ankstesnė nei šiandiena"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show less"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show more"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show only video"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Showing"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Sidebar"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Šoninės juostos veiksmas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Šoninės juostos veiksmas, padarantis šį šabloną pasiekiamą susijusių "
"dokumentų modelio įrašuose"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "Šaltinis"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "Konkretus vartotojas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"Nurodykite modelį, jei veikla turėtų būti konkreti modeliui ir negalima "
"valdant kitų modelių veiklas."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Spotlight"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Pažymėtas žvaigždute"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Call"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Video Call"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Start a conversation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Start a meeting"
msgstr "Pradėti susitikimą"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "State"
msgstr "Regionas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Būsena"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Būsena, paremta veiklomis\n"
"Vėluojantis: Termino data jau praėjo\n"
"Šiandien: Veikla turi būti baigta šiandien\n"
"Suplanuotas: Ateities veiklos."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Stop adding users"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop camera"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Stop replying"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop screen sharing"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_model_object_field
msgid "Sub-field"
msgstr "Polaukis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_object
msgid "Sub-model"
msgstr "Submodelis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "Tema"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr "Tema (vietos žymos gali būti panaudotos čia)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr "Tema..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Subject:"
msgstr "Tema:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Pakaitalas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Potipis"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Potipiai"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
msgid "Summary"
msgstr "Santrauka"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "Sistemos parametrai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
#, python-format
msgid "System notification"
msgstr "Sistemos pranešimas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "PVM mokėtojo kodas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__has_recommended_activities
msgid "Technical field for UX purpose"
msgstr "Techninins laukas UX reikmėms"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_change
msgid "Technical field for UX related behaviour"
msgstr "Techninins laukas UX elgesiui"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__can_write
msgid "Technical field to hide buttons if the current user has no access."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr "Techninis vartotojo vardas įraše"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "Šablono peržiūra"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "Šablono peržiūros kalba"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering for language should be called with a list of IDs."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called on a valid record IDs."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called only using on a list of IDs."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw)."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Šablonai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Text file"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr " "

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr "Termino datos reikšmė negali būti neigiama."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "The FullScreen mode was denied by the browser"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "Priedas %s neegzistuoja arba neturite prieigos teisių. "

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "The attachment %s does not exist."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already linked with "
"%(alias_model_name)s. Choose another alias or change it on the linked model."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used as "
"%(alias_duplicate)s alias. Please choose another alias."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used by the "
"%(document_name)s %(model_name)s. Choose another alias or change it on the "
"other document."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "Laiškas išsiųstas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid "The escaped html code replacing the shortcut"
msgstr "HTML kodas, pakeičiantis trumpąją nuorodą"

#. module: mail
#: code:addons/mail/models/mail_composer_mixin.py:0
#, python-format
msgid "The field %s does not exist on the model %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Modelis (\"Odoo\" dokumento rūšis), su kuriuo susijęs šis pseudonimas. Visi "
"gaunami laiškai, kurie neatsako į egzistuojantį įrašą, sukurs naują šio "
"modelio įrašą (pvz. projekto užduotį)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_channel__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El. pašto pseudonimo pavadinimas, pvz. \"darbai\", jei norite gauti "
"<<EMAIL>>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Įrašų, sukuriamų gaunant laiškus šiuo vardu, šeimininkas. Jei šis laukas "
"nėra nustatytas, sistema bandys surasti tikrąjį šeimininką pagal siuntėjo "
"(nuo) adresą arba naudos administratoriaus paskyrą, jei tam adresui "
"sistemoje nerandamas vartotojas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"Pageidaujama operacija negali būti įvykdyta dėl saugos apribojimų. Prašome susisiekti su savo sistemos administratoriumi.\n"
"\n"
"(Dokumento tipas: %s, Operacija: %s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr "Trumpoji nuoroda, kuri turi būti pakeista susirašinėjimo žinutėse"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/follower/follower.js:0
#, python-format
msgid "The subscription preferences were successfully applied."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__model_id
#: model:ir.model.fields,help:mail.field_mail_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "Dokumento tipas, su kuriuo gali būti naudojamas šis šablonas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "There are no messages in this conversation."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_rtc_session_channel_partner_unique
msgid "There can only be one rtc session per channel partner"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Šis"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "This action will send an email."
msgstr "Šis veiksmas išsiųs laišką."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "Didžiosios raidės nesvarbu."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr ""
"Ši grupė yra matoma ne nariams. Nematomos grupės gali pridėti narius "
"naudojant pakvietimo mygtuką."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "This is their first connection. Wish them luck."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "This record has an exception activity."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Save the record before scheduling an activity!"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "This user can not be added in this channel"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Diskusija"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Thread Image"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Tiled"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "Laiko juosta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
msgid "To"
msgstr " Kam"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Kam (el. laiškai)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "Kam (partneriai)"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To Do"
msgstr "Reikia atlikti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "To:"
msgstr "Kam:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#, python-format
msgid "Today"
msgstr "Šiandien"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Šiandienos veiklos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Today:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Tomorrow"
msgstr "Rytoj"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Tomorrow:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Temos, kuriomis diskutuojamos šioje grupėje..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Sekamos vertės yra laikomos atskirame modelyje. Šis laukas leidžia "
"rekonstruoti sekimą ir generuoti modelio statistiką."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "Sekimas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Sekimo reikšmė"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Sekimo reikšmės"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__tracking_sequence
msgid "Tracking field sequence"
msgstr "Sekamo lauko seka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Sekimo reikšmės"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "Iššaukimas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Turn camera on"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Tipas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "Vėlavimo tipas"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Įrašytos išimties veiklos tipas."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Type the name of a person"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "Nepavyksta prisijungti prie SMTP serverio"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to log message, please configure the sender's email address."
msgstr "Nepavyko registruoti žinutės, nustatykite siuntėjo el. pašto adresą."

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "Nepavyko paskelbti žinutės, nustatykite siuntėjo pašto adresą."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Unblacklisting Reason: %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Undeafen"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Unfollow"
msgstr "Nebesekti"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_alias_unique
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr "Deja, bet šis pseudonimas jau yra naudojamas, tad pasirinkite kitą"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "Vėlavimo vienetas"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "Nežinoma klaida"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Unmute"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Unpin Conversation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread
msgid "Unread Messages"
msgstr "Neperskaitytos žinutės"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Neperskaitytų žinučių skaičiavimas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "Neperskaitytos žinutės"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Unstar all"
msgstr "Pašalinti visus žymėjimus"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "Rastas nepalaikomas ataskaitos tipas %s."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
#, python-format
msgid "Upload Document"
msgstr "Įkelti dokumentą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Upload file"
msgstr "Įkelti failą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#, python-format
msgid "Uploaded"
msgstr "Įkelta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Uploading"
msgstr "Įkeliama"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""
"Naudokite \"konkretus vartotojas\", kad priskirtumėte tą patį vartotoją "
"kitam veiksmui. Naudokite \"standartinis vartotojas iš įrašo\", kad "
"nurodytumėte vartotojo lauko pavadinimą pasirinkimui įraše."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Use Push-to-talk"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr "Naudoti aktyvų domeną"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Naudoti šabloną"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Naudojama potipių rikiavimui."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "Vartotojas"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "Vartotojo aktyvumas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User field name"
msgstr "Vartotojo lauko pavadinimas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is a bot"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is idle"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is offline"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is online"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "Vartotojas:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "Vartotojo vardas"

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr "Vartotojai"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Users in this channel: %(members)s %(dots)s and you."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"\"Community\" ir \"Enterprise\" versijose laiškams siųsti ir gauti "
"reikalingas nuosavas el. pašto serveris. Internetiniai vartotojai jau dabar "
"jaučia paruošto naudoti serverio (@mycompany.odoo.com) naudą."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Value for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Video"
msgstr "Video"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr "Peržiūrėti"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "Rodinys %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Rodinio tipas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "View image"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category/discuss_sidebar_category.xml:0
#, python-format
msgid "View or join channels"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Viewer"
msgstr "Žiūrėtojas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "Tūris"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Warning"
msgstr "Įspėjimas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Savaitės"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "What's your name?"
msgstr "Koks Jūsų vardas?"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Kai santykio laukas yra  pasirinktas kaip pirmasis laukas, šis laukas "
"leidžia jums pasirinkti tikslinį lauką tikslo dokumento modelyje ar "
"submodelyje."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Kai sąryšio laukas yra pasirinktas kaip pirmas laukas, šis laukas nurodo to "
"sąryšio dokumento modelį."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr "Ar žinutė yra vidinis pranešimas (tik komentarų režimas)"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_activity
msgid "Whether this model supports activities."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_blacklist
msgid "Whether this model supports blacklist."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr "Ar šis modelis palaiko žinutes ir pranešimus."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr "Kas gali sekti grupės veiklą?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Write Feedback"
msgstr "Palikti atsiliepimą"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Wrong operation name (%s)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Yesterday"
msgstr "Vakar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Yesterday:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "Šiame kanale esate vieni."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr "Esate privačiame pokalbyje su <b>@%s</b>."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr "Esate <b>#%s</b> kanale."

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"Galite pažymėti bet kurią žinutę žvaigždute ir ji pasirodys šioje dėžutėje."

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "You can not write on %(field_name)s."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only chat with existing users."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/partner/partner.js:0
#, python-format
msgid "You can only chat with partners that have a dedicated user."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging/messaging.js:0
#, python-format
msgid "You can only open the profile of existing channels."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only open the profile of existing users."
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""
"Čia negalite sukurti naujo vartotojo. \n"
"Norint sukurti naują vartotoją, eikite į konfigūravimo skydelį."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Jūs negalite ištrinti šitų grupių, nes grupė \"Visa įmonė\" yra reikalinga "
"kitiems moduliams."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address (%s)."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "Jūs buvote priskirtas prie %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Jūs buvote priskirtas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You have been invited to #%s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Jūs galite prisegti failus prie šio šablono ir jie bus prisegami prie "
"kiekvieno laiško, kuriamo pagal šį šabloną"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unpinned your conversation with %s."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unsubscribed from %s."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a chat!"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a meeting!"
msgstr "Jūs buvote pakviestas(-a) į susitikimą"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_author_prefix/message_author_prefix.xml:0
#, python-format
msgid "You:"
msgstr "Jūs:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr "Jūsų"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Your browser does not support videoconference"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "Your browser does not support voice activation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Your browser does not support webRTC."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Your name"
msgstr "Jūsų vardas"

#. module: mail
#: code:addons/mail/controllers/home.py:0
#, python-format
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom In"
msgstr "Priartinti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom In (+)"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Atitolinti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom Out (-)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered partners"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "po ankstesnės veiklos termino"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %(name)s: %(error)s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "assigned you an activity"
msgstr "priskyrė jums veiklą"

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr "Valdybos posėdžiai"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "bounce"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "pagal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "cancel"
msgstr "atšaukti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"negalėjo būti apdorotas. Šis adresas\n"
"yra naudojamas atsakymų surinkimui ir neturėtų būti naudojamas tiesioginiam kontaktui"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "catchall"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "channel"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "dienos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "deaf"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_thread.py:0
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "document"
msgstr "dokumentas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "atlikta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. \"mycompany.com\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Calendar: Reminder"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "pvz. pasiūlymo aptarimas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Users"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "e.g. support"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "escape to"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "for %s"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr "bendras"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr ""

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "neteisingai sukonfigūruotas pseudonimas"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "neteisingai sukonfigūruotas pseudonimas (nežinomas nuorodos įrašas)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "live"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support model and res_id parameters anymore. Please "
"call message_post on record."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support subtype parameter anymore. Please give a valid"
" subtype_id or subtype_xmlid value instead."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "message_post partner_ids and must be integer list, not commands."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "modelis %s nepriima sukurto dokumento"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "mėnesiai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "ms"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "muted"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/utils.js:0
#, python-format
msgid "now"
msgstr "dabar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "esantis"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "on:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "or"
msgstr "arba"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "other members."
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr "rd"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read less"
msgstr "rodyti mažiau"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read more"
msgstr "rodyti daugiau"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "record:"
msgstr "įrašas:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "restricted to channel members"
msgstr "tik kanalo nariams"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to followers"
msgstr "tik sekėjams"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to known authors"
msgstr "tik žinomiems autoriams"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "results out of"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr "pardavimai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "save"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "some specific addresses"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "komanda."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "this document"
msgstr "šis dokumentas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "to close for"
msgstr "uždaryti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "toggle push-to-talk"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "nežinoma klaida"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "nežinomos paskirties modelis %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "using"
msgstr "naudoja"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/many2one_avatar_user.xml:0
#, python-format
msgid "value"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "savaitės"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr ""
