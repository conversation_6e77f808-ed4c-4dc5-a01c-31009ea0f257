<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- Index -->
<template id="index" name="Events">
    <t t-call="website.layout">
        <div id="wrap" class="o_wevent_index">
            <!-- Options -->
            <t t-set="opt_events_list_cards" t-value="is_view_active('website_event.opt_events_list_cards')"/>
            <t t-set="opt_events_list_columns" t-value="is_view_active('website_event.opt_events_list_columns')"/>
            <!-- Topbar -->
            <t t-call="website_event.index_topbar">
                <t t-set="search" t-value="original_search or search or searches['search']"/>
            </t>
            <!-- Drag/Drop Area -->
            <div id="oe_structure_we_index_1" class="oe_structure oe_empty"/>
            <!-- Content -->
            <div t-attf-class="o_wevent_events_list #{opt_events_list_cards and 'opt_event_list_cards_bg'}">
                <div class="container">
                    <div class="row">
                        <t t-call="website_event.searched_tags"/>
                    </div>
                    <div class="row">
                        <div id="o_wevent_index_main_col" t-attf-class="col-md my-5 #{opt_events_list_columns and 'opt_events_list_columns' or 'opt_events_list_rows'}">
                            <div class="row">
                                <!-- Events List -->
                                <t t-call="website_event.events_list"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Drag/Drop Area -->
            <div id="oe_structure_we_index_2" class="oe_structure oe_empty"/>
        </div>
    </t>
</template>

<!-- Index - OPTION - Sidebar -->
<template id="opt_index_sidebar" inherit_id="website_event.index" active="False" customize_show="True" name="Show Sidebar">
    <xpath expr="//div[@id='o_wevent_index_main_col']" position="after">
        <t t-call="website_event.index_sidebar"/>
    </xpath>
</template>

<!-- Index Topbar -->
<template id="index_topbar" name="Topbar">
    <nav class="navbar navbar-light border-top shadow-sm d-print-none">
        <div class="container">
            <div class="d-flex flex-column flex-sm-row justify-content-between w-100">
                <span class="navbar-brand h4 my-0 mr-auto">Events</span>
                <ul class="o_wevent_index_topbar_filters nav">
                    <t t-foreach="categories" t-as="category">
                        <li t-if="category.is_published and category.tag_ids and any(tag.color for tag in category.tag_ids)" class="nav-item dropdown mr-2 my-1">
                            <a href="#" role="button" class="btn dropdown-toggle" data-toggle="dropdown">
                                <i class="fa fa-folder-open"/>
                                <t t-esc="category.name"/>
                            </a>
                            <div class="dropdown-menu">
                                <t t-foreach="category.tag_ids" t-as="tag">
                                    <a t-if="tag.color"
                                        t-att-href="'/event?%s' % keep_query('*', tags=str((search_tags - tag).ids if tag in search_tags else (tag | search_tags).ids))"
                                        t-attf-class="dropdown-item d-flex align-items-center justify-content-between #{'active' if tag in search_tags else ''}">
                                        <t t-esc="tag.name"/>
                                    </a>
                                </t>
                            </div>
                        </li>
                    </t>
                </ul>
                <div class="d-flex align-items-center flex-wrap pl-sm-3 pr-0">
                    <t t-call="website_event.events_search_box_input"/>
                </div>
            </div>
        </div>
    </nav>
</template>

<template id="searched_tags" name="Searched tags">
    <div class="d-flex align-items-center mt16">
        <t t-foreach="search_tags" t-as="tag">
            <span class="align-items-baseline border d-inline-flex pl-2 rounded ml16 mb-2 bg-white">
                <i class="fa fa-tag mr-2 text-muted"/>
                <t t-esc="tag.display_name"/>
                <a t-att-href="'/event?%s' % keep_query('*', tags=str((search_tags - tag).ids))" class="btn border-0 py-1">&#215;</a>
            </span>
        </t>
    </div>
</template>

<!-- Filter - Date -->
<template id="event_time" inherit_id="website_event.index_topbar" customize_show="True" name="Filter by Date">
    <xpath expr="//ul[hasclass('o_wevent_index_topbar_filters')]" position="inside">
        <li class="nav-item dropdown mr-2 my-1">
            <a href="#" role="button" class="btn dropdown-toggle" data-toggle="dropdown">
                <i class="fa fa-calendar"/>
                <t t-if="current_date" t-esc="current_date"/>
                <t t-else="">Upcoming Events</t>
            </a>
            <div class="dropdown-menu">
                <t t-foreach="dates" t-as="date">
                    <t t-if="date[3] or (date[0] in ('old','all'))">
                        <a t-att-href="keep('/event', date=date[0])" t-attf-class="dropdown-item d-flex align-items-center justify-content-between #{searches.get('date') == date[0] and 'active'}">
                            <t t-esc="date[1]"/>
                            <span t-if="date[3]" t-esc="date[3]" class="badge badge-pill badge-primary ml-3"/>
                        </a>
                    </t>
                </t>
            </div>
        </li>
    </xpath>
</template>

<!-- Filter - Location -->
<template id="event_location" inherit_id="website_event.index_topbar" active="False" customize_show="True" name="Filter by Country">
    <xpath expr="//ul[hasclass('o_wevent_index_topbar_filters')]" position="inside">
        <li class="nav-item dropdown mr-2 my-1">
            <a href="#" role="button" class="btn dropdown-toggle" data-toggle="dropdown">
                <i class="fa fa-map-marker"/>
                <t t-if="current_country" t-esc="current_country.name"/>
                <t t-else="">All countries</t>
            </a>
            <div class="dropdown-menu">
                <t t-foreach="countries" t-as="country">
                    <t t-if="country['country_id']">
                        <a t-att-href="keep('/event', country=country['country_id'][0])" t-attf-class="dropdown-item d-flex align-items-center justify-content-between #{searches.get('country') == str(country['country_id'] and country['country_id'][0]) and 'active'}">
                            <t t-esc="country['country_id'][1]"/>
                            <span t-esc="country['country_id_count']" class="badge badge-pill badge-primary ml-auto"/>
                        </a>
                    </t>
                    <t t-else="">
                        <a t-att-href="keep('/event', country='online')" t-attf-class="dropdown-item d-flex align-items-center justify-content-between #{searches.get('country') == 'online' and 'active'}">
                            <span>Online Events</span>
                            <span t-esc="country['country_id_count']" class="badge badge-pill badge-primary ml-3"/>
                        </a>
                    </t>
                </t>
            </div>
        </li>
    </xpath>
</template>

<!-- Index - Events list -->
<template id="events_list" name="Events list">
    <!-- Options -->
    <t t-set="opt_index_sidebar" t-value="is_view_active('website_event.opt_index_sidebar')"/>
    <t t-if="opt_events_list_columns" t-set="opt_event_size" t-value="opt_index_sidebar and 'col-md-6' or 'col-md-6 col-lg-4'"/>
    <t t-else="" t-set="opt_event_size" t-value="opt_index_sidebar and 'col-12' or 'col-xl-10 offset-xl-1'"/>
    <!-- No events -->
    <t t-if="not event_ids">
        <div class="col-12">
            <div class="h2 mb-3">No events found.</div>
            <div class="alert alert-info text-center" groups="event.group_event_user">
                <p class="m-0">Use the top button '<b>+ New</b>' to create an event.</p>
            </div>
        </div>
    </t>
    <!-- Fuzzy search -->
    <div t-if="event_ids and original_search" class="col-12 alert alert-warning mt8">
        No results found for '<span t-esc="original_search"/>'. Showing results for '<span t-esc="searches['search']"/>'.
    </div>
    <!-- List -->
    <div t-foreach="event_ids" t-as="event" t-attf-class=" #{opt_event_size} mb-4">
        <a t-attf-href="/event/#{ slug(event) }/#{(not event.menu_id) and 'register'}" class="text-decoration-none" t-att-data-publish="event.website_published and 'on' or 'off'">
            <article t-attf-class="h-100 #{opt_events_list_cards and 'card border-0 shadow-sm'}" itemscope="itemscope" itemtype="http://schema.org/Event">
                <div class="h-100 row no-gutters">
                    <!-- Header -->
                    <header t-attf-class="overflow-hidden bg-secondary #{opt_events_list_columns and 'col-12' or 'col-sm-4 col-lg-3'} #{(not opt_events_list_cards) and 'shadow'}">
                        <!-- Image + Link -->
                        <div class="d-block h-100 w-100">
                            <t t-call="website.record_cover">
                                <t t-set="_record" t-value="event"/>

                                <!-- Short Date -->
                                <div class="o_wevent_event_date position-absolute bg-white shadow-sm text-dark">
                                    <span t-field="event.with_context(tz=event.date_tz).date_begin" t-options="{'format': 'LLL'}" class="o_wevent_event_month"/>
                                    <span t-field="event.with_context(tz=event.date_tz).date_begin" t-options="{'format': 'dd'}" class="o_wevent_event_day oe_hide_on_date_edit"/>
                                </div>
                                <!-- Participating -->
                                <small t-if="event.is_participating" class="o_wevent_participating bg-success">
                                    <i class="fa fa-check mr-2"/>Registered
                                </small>
                                <!-- Unpublished -->
                                <small t-if="not event.website_published" class="o_wevent_unpublished bg-danger">
                                    <i class="fa fa-ban mr-2"/>Unpublished
                                </small>
                            </t>
                        </div>
                    </header>
                    <div t-att-class="'%s %s' % (
                        opt_events_list_columns and 'col-12' or 'col',
                        opt_events_list_columns and event.event_registrations_open and not event.event_registrations_sold_out and 'h-100' or '')">
                        <!-- Body -->
                        <main t-attf-class="#{opt_events_list_cards and 'card-body' or (opt_events_list_columns and 'py-3' or 'px-4')}">
                            <!-- Title -->
                            <h5 t-attf-class="card-title mt-2 mb-0 text-truncate #{(not event.website_published) and 'text-danger'}">
                                <span t-field="event.name" itemprop="name"/>
                            </h5>
                            <!-- Start Date & Time -->
                            <time itemprop="startDate" t-att-datetime="event.date_begin">
                                <span t-field="event.with_context(tz=event.date_tz).date_begin" t-options="{'date_only': 'true', 'format': 'long'}"/> -
                                <span t-field="event.with_context(tz=event.date_tz).date_begin" t-options="{'time_only': 'true', 'format': 'short'}" class="oe_hide_on_date_edit"/>
                            </time>
                            <!-- Location -->
                            <div itemprop="location" t-field="event.address_id" t-options="{'widget': 'contact', 'fields': ['city'], 'no_marker': 'true'}"/>
                            <div class="mt8">
                                <t t-foreach="event.tag_ids.filtered(lambda tag: tag.category_id.is_published)" t-as="tag">
                                    <span t-if="tag.color"
                                        t-attf-class="badge mr4 #{'badge-primary' if tag in search_tags else 'badge-light'} #{'o_tag_color_%s' % tag.color if tag.color else ''}">
                                        <span t-esc="tag.name"/>
                                    </span>
                                </t>
                            </div>
                        </main>
                    </div>
                    <!-- Footer -->
                    <footer t-if="not event.event_registrations_open or event.event_registrations_sold_out"
                        t-att-class="'small align-self-end w-100 %s %s' % (
                            opt_events_list_cards and 'card-footer' or (not opt_events_list_columns and 'mx-4 mt-auto pt-2') or 'py-2',
                            opt_events_list_cards and 'border-top' or '',
                        )">
                        <span t-if="not event.event_registrations_open" class="text-danger">
                            <t t-if="not event.event_registrations_started">
                                Registrations not yet open
                            </t>
                            <t t-elif="event.event_registrations_sold_out">
                                Sold Out
                            </t>
                            <t t-else="">
                                Registrations Closed
                            </t>
                        </span>
                    </footer>
                </div>
            </article>
        </a>
    </div>
    <!-- Pager -->
    <div class="form-inline justify-content-center my-3">
        <t t-call="website.pager"/>
    </div>
</template>

<template id="opt_events_list_columns" inherit_id="website_event.events_list" active="True" customize_show="True" name="Layout • Columns"/>

<template id="opt_events_list_cards" inherit_id="website_event.events_list" active="True" customize_show="True" name="'Cards' Design"/>

<template id="opt_events_list_categories" inherit_id="website_event.events_list" active="False" customize_show="True" name="Show Templates">
    <xpath expr="//main/*" position="before">
        <span t-if="event.event_type_id" t-attf-href="/event?type=#{event.event_type_id.id}" t-attf-class="badge bg-secondary o_wevent_badge #{opt_events_list_columns and 'o_wevent_badge_event' or 'float-right'}" t-field="event.event_type_id"/>
    </xpath>
</template>

<!-- Index - Sidebar -->
<template id="index_sidebar" name="Sidebar">
    <div id="o_wevent_index_sidebar" class="col-lg-4 ml-lg-3 ml-xl-5 my-5"/>
</template>

<!-- Index - Sidebar - About us -->
<template id="index_sidebar_about_us" inherit_id="website_event.index_sidebar" active="True" customize_show="True" name="About us" priority="20">
    <xpath expr="//div[@id='o_wevent_index_sidebar']" position="inside">
        <div class="o_wevent_sidebar_block">
            <h6 class="o_wevent_sidebar_title">About us</h6>
            <p>Use this paragrah to write a short text about your events or company.</p>
        </div>
        <div id="oe_structure_website_event_about_us_1" class="oe_structure"/>
    </xpath>
</template>

<!-- Index - Sidebar - Follow us -->
<template id="index_sidebar_follow_us" inherit_id="website_event.index_sidebar" active="True" customize_show="True" name="Follow us" priority="30">
    <xpath expr="//div[@id='o_wevent_index_sidebar']" position="inside">
        <div class="o_wevent_sidebar_block">
            <h6 class="o_wevent_sidebar_title">Follow Us</h6>
            <div class="o_wevent_sidebar_social mx-n1">
                <a t-if="website.social_facebook" t-att-href="website.social_facebook" class="o_wevent_social_link"><i class="fa fa-facebook text-facebook" aria-label="Facebook" title="Facebook"/></a>
                <a t-if="website.social_twitter" t-att-href="website.social_twitter" class="o_wevent_social_link"><i class="fa fa-twitter text-twitter" aria-label="Twitter" title="Twitter"/></a>
                <a t-if="website.social_linkedin" t-att-href="website.social_linkedin" class="o_wevent_social_link"><i class="fa fa-linkedin text-linkedin" aria-label="LinkedIn" title="LinkedIn"/></a>
                <a t-if="website.social_youtube" t-att-href="website.social_youtube" class="o_wevent_social_link"><i class="fa fa-youtube-play text-youtube" aria-label="Youtube" title="Youtube"/></a>
                <a t-if="website.social_github" t-att-href="website.social_github" class="o_wevent_social_link"><i class="fa fa-github text-github" aria-label="Github" title="Github"/></a>
                <a t-if="website.social_instagram" t-att-href="website.social_instagram" class="o_wevent_social_link"><i class="fa fa-instagram text-instagram" aria-label="Instagram" title="Instagram"/></a>
            </div>
        </div>
        <div id="oe_structure_website_event_follow_us_1" class="oe_structure"/>
    </xpath>
</template>

<!-- Index - Sidebar - Photos -->
<template id="index_sidebar_photos" inherit_id="website_event.index_sidebar" active="True" customize_show="True" name="Photos" priority="40">
    <xpath expr="//div[@id='o_wevent_index_sidebar']" position="inside">
        <h6 class="o_wevent_sidebar_title">Photos</h6>
        <a href="/event">
            <figure class="o_wevent_sidebar_block o_wevent_sidebar_figure figure">
                <img class="figure-img img-fluid rounded" src="/website_event/static/src/img/event_past_0.jpg" alt=""/>
                <figcaption class="figure-caption text-muted">A past event</figcaption>
            </figure>
        </a>
        <a href="/event">
            <figure class="o_wevent_sidebar_block o_wevent_sidebar_figure figure">
                <img class="figure-img img-fluid rounded" src="/website_event/static/src/img/event_training_0.jpg" alt=""/>
                <figcaption class="figure-caption text-muted">Our Trainings</figcaption>
            </figure>
        </a>
    </xpath>
</template>

<!-- Index - Sidebar - Quotes -->
<template id="index_sidebar_quotes" inherit_id="website_event.index_sidebar" active="True" customize_show="True" name="Quotes" priority="60">
    <xpath expr="//div[@id='o_wevent_index_sidebar']" position="inside">
        <div class="o_wevent_sidebar_block card">
            <div class="card-body">
                <blockquote class="blockquote mb-0">
                    <p><em>Write here a quote from one of your attendees. It gives confidence in your events.</em></p>
                    <footer class="blockquote-footer text-muted">Author</footer>
                </blockquote>
            </div>
        </div>
    </xpath>
</template>

<!-- Index - Sidebar - Snippet - Country Events -->
<template id="index_sidebar_country_event" inherit_id="website_event.index_sidebar" active="False" customize_show="True" name="Country Events" priority="70">
    <xpath expr="//div[@id='o_wevent_index_sidebar']" position="inside">
        <div class="o_wevent_sidebar_block">
            <t t-snippet-call="website_event.s_country_events"/>
        </div>
    </xpath>
</template>

</odoo>
