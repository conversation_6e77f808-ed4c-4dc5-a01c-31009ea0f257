# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_latam_base
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-22 13:00+0000\n"
"PO-Revision-Date: 2019-08-22 13:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__active
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_l10n_latam_identification_type_search
msgid "Active"
msgstr ""

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_l10n_latam_identification_type_search
msgid "Archived"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model,name:l10n_latam_base.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model,name:l10n_latam_base.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__country_id
msgid "Country"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__create_date
msgid "Created on"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__description
msgid "Description"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_latam_base
#: model:l10n_latam.identification.type,name:l10n_latam_base.it_fid
msgid "Foreign ID"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__id
msgid "ID"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_res_partner__vat
#: model:ir.model.fields,field_description:l10n_latam_base.field_res_users__vat
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_partner_latam_form
msgid "Identification Number"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,help:l10n_latam_base.field_res_partner__vat
#: model:ir.model.fields,help:l10n_latam_base.field_res_users__vat
msgid "Identification Number for selected type"
msgstr ""

#. module: l10n_latam_base
#: model:ir.actions.act_window,name:l10n_latam_base.action_l10n_latam_identification_type
#: model:ir.model.fields,field_description:l10n_latam_base.field_res_partner__l10n_latam_identification_type_id
#: model:ir.model.fields,field_description:l10n_latam_base.field_res_users__l10n_latam_identification_type_id
#: model:ir.ui.menu,name:l10n_latam_base.menu_l10n_latam_identification_type
msgid "Identification Type"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model,name:l10n_latam_base.model_l10n_latam_identification_type
msgid "Identification Types"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__is_vat
msgid "Is Vat"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__name
msgid "Name"
msgstr ""

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_partner_latam_form
msgid "Number"
msgstr ""

#. module: l10n_latam_base
#: model:l10n_latam.identification.type,name:l10n_latam_base.it_pass
msgid "Passport"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__sequence
msgid "Sequence"
msgstr ""

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_l10n_latam_identification_type_search
msgid "Show active identification types"
msgstr ""

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_l10n_latam_identification_type_search
msgid "Show archived identification types"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,help:l10n_latam_base.field_res_partner__l10n_latam_identification_type_id
#: model:ir.model.fields,help:l10n_latam_base.field_res_users__l10n_latam_identification_type_id
msgid "The type of identification"
msgstr ""

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_partner_latam_form
msgid "Type"
msgstr ""

#. module: l10n_latam_base
#: model:l10n_latam.identification.type,name:l10n_latam_base.it_vat
msgid "VAT"
msgstr ""
