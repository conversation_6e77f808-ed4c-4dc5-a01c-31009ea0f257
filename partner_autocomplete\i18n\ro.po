# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_autocomplete
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__additional_info
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__additional_info
msgid "Additional info"
msgstr "Informații adiționale"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Buy more credits"
msgstr "Cumpără mai mult credit"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: partner_autocomplete
#: code:addons/partner_autocomplete/models/res_company.py:0
#, python-format
msgid "Company auto-completed by Odoo Partner Autocomplete Service"
msgstr "Companie auto-completată de serviciul Odoo Partner Autocomplete"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__partner_gid
msgid "Company database ID"
msgstr "ID-ul bazei de date a companiei"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_date
msgid "Created on"
msgstr "Creat în"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__iap_enrich_auto_done
msgid "Enrich Done"
msgstr "Îmbogățire efectuată"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_ir_http
msgid "HTTP Routing"
msgstr "Rutare HTTP"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "IAP Account Token missing"
msgstr "Lipsește token-ul contului IAP"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_iap_autocomplete_api
msgid "IAP Partner Autocomplete API"
msgstr "API-ul de auto-completare a partenerilor IAP"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__id
msgid "ID"
msgstr "ID"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr "Credit insuficient"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__synched
msgid "Is synched"
msgstr "Este sincronizat"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: partner_autocomplete
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
#, python-format
msgid "No account token"
msgstr "Nu există token de cont"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "Not enough credits for Partner Autocomplete"
msgstr "Nu sunt suficiente credite pentru completarea automată a partenerului"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__partner_id
msgid "Partner"
msgstr "Partener"

#. module: partner_autocomplete
#: model:ir.actions.server,name:partner_autocomplete.ir_cron_partner_autocomplete_ir_actions_server
#: model:ir.cron,cron_name:partner_autocomplete.ir_cron_partner_autocomplete
#: model:ir.cron,name:partner_autocomplete.ir_cron_partner_autocomplete
msgid "Partner Autocomplete : Sync with remote DB"
msgstr "Auto-completare partener: Sincronizare cu baza de date la distanță"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner_autocomplete_sync
msgid "Partner Autocomplete Sync"
msgstr "Sincronizare auto-completare partener"

#. module: partner_autocomplete
#: code:addons/partner_autocomplete/models/res_partner.py:0
#, python-format
msgid "Partner created by Odoo Partner Autocomplete Service"
msgstr "Partener creat de serviciul de auto-completare a partenerilor Odoo"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Placeholder"
msgstr "Substitut"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_many2one.js:0
#, python-format
msgid "Searching Autocomplete..."
msgstr "Se caută auto-completarea..."

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Set Your Account Token"
msgstr "Setați token-ul contului"

#. module: partner_autocomplete
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
#, python-format
msgid "Test mode"
msgstr "Mod test"
