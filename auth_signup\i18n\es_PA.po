# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * auth_signup
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-10 15:14+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Panama) (http://www.transifex.com/odoo/odoo-9/"
"language/es_PA/)\n"
"Language: es_PA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.reset_password_email
msgid ""
"\n"
"<div style=\"padding:0px;width:600px;margin:auto;background: #FFFFFF repeat "
"top /100%;color:#777777\">\n"
"    <table cellspacing=\"0\" cellpadding=\"0\" style=\"width:600px;border-"
"collapse:collapse;background:inherit;color:inherit\">\n"
"        <tbody><tr>\n"
"            <td valign=\"center\" width=\"200\" style=\"padding:10px 10px "
"10px 5px;font-size: 12px\">\n"
"                <img src=\"/logo.png\" style=\"padding: 0px; margin: 0px; "
"height: auto; width: 80px;\" alt=\"${user.company_id.name}\">\n"
"            </td>\n"
"        </tr></tbody>\n"
"    </table>\n"
"</div>\n"
"<div style=\"padding:0px;width:600px;margin:auto;background: #FFFFFF repeat "
"top /100%;color:#777777\">\n"
"    <p>Dear ${object.name},</p>\n"
"    <p>A password reset was requested for the Odoo account linked to this "
"email.</p>\n"
"    <p>You may change your password by following this link which will remain "
"valid during 24 hours:</p>\n"
"    <div style=\"text-align: center; margin-top: 16px;\">\n"
"        <a href=\"${object.signup_url}\" style=\"padding: 5px 10px; font-"
"size: 12px; line-height: 18px; color: #FFFFFF; border-color:#a24689; text-"
"decoration: none; display: inline-block; margin-bottom: 0px; font-weight: "
"400; text-align: center; vertical-align: middle; cursor: pointer; white-"
"space: nowrap; background-image: none; background-color: #a24689; border: "
"1px solid #a24689; border-radius:3px\">Change password</a>\n"
"    </div>\n"
"    <p>If you do not expect this, you can safely ignore this email.</p>\n"
"    <p>Best regards,</p>\n"
"</div>\n"
"<div style=\"padding:0px;width:600px;margin:auto; margin-top: 10px; "
"background: #fff repeat top /100%;color:#777777\">\n"
"    ${user.signature | safe}\n"
"    <p style=\"font-size: 11px; margin-top: 10px;\">\n"
"        <strong>Sent by ${user.company_id.name} using <a href=\"www.odoo.com"
"\" style=\"text-decoration:none; color: #a24689;\">Odoo</a></strong>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.set_password_email
msgid ""
"\n"
"<div style=\"padding:0px;width:600px;margin:auto;background: #FFFFFF repeat "
"top /100%;color:#777777\">\n"
"    <table cellspacing=\"0\" cellpadding=\"0\" style=\"width:600px;border-"
"collapse:collapse;background:inherit;color:inherit\">\n"
"        <tbody><tr>\n"
"            <td valign=\"center\" width=\"200\" style=\"padding:10px 10px "
"10px 5px;font-size: 12px\">\n"
"                <img src=\"/logo.png\" style=\"padding: 0px; margin: 0px; "
"height: auto; width: 80px;\" alt=\"${user.company_id.name}\">\n"
"            </td>\n"
"        </tr></tbody>\n"
"    </table>\n"
"</div>\n"
"<div style=\"padding:0px;width:600px;margin:auto;background: #FFFFFF repeat "
"top /100%;color:#777777\">\n"
"<p>Dear ${object.name},</p>\n"
"    <p>\n"
"        You have been invited to connect to \"${object.company_id.name}\" in "
"order to get access to your documents in Odoo.\n"
"    </p>\n"
"    <p>\n"
"        To accept the invitation, click on the following link:\n"
"    </p>\n"
"    <div style=\"text-align: center; margin-top: 16px;\">\n"
"         <a href=\"${object.signup_url}\" style=\"padding: 5px 10px; font-"
"size: 12px; line-height: 18px; color: #FFFFFF; border-color:#a24689; text-"
"decoration: none; display: inline-block; margin-bottom: 0px; font-weight: "
"400; text-align: center; vertical-align: middle; cursor: pointer; white-"
"space: nowrap; background-image: none; background-color: #a24689; border: "
"1px solid #a24689; border-radius:3px\">Accept invitation to \"${object."
"company_id.name}\"</a>\n"
"    </div>\n"
"    <p>Best regards,</p>\n"
"</div>\n"
"<div style=\"padding:0px;width:600px;margin:auto; margin-top: 10px; "
"background: #fff repeat top /100%;color:#777777\">\n"
"    ${user.signature | safe}\n"
"    <p style=\"font-size: 11px; margin-top: 10px;\">\n"
"        <strong>Sent by ${user.company_id.name} using <a href=\"www.odoo.com"
"\" style=\"text-decoration:none; color: #a24689;\">Odoo</a></strong>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: auth_signup
#: model:mail.template,subject:auth_signup.set_password_email
msgid "${object.company_id.name} invitation to connect on Odoo"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_form_view
msgid ""
"<strong>A password reset has been requested for this user. An email "
"containing the following link has been sent:</strong>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_form_view
msgid ""
"<strong>An invitation email containing the following subscription link has "
"been sent:</strong>"
msgstr ""

#. module: auth_signup
#: selection:res.users,state:0
msgid "Activated"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_base_config_settings_auth_signup_uninvited
msgid "Allow external users to sign up"
msgstr ""

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:61
#, python-format
msgid "An email has been sent with credentials to reset your password"
msgstr ""

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:38
#, python-format
msgid "Another user is already registered using this email address."
msgstr ""

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:111
#, python-format
msgid "Authentication Failed."
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Back to Login"
msgstr ""

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:135
#, python-format
msgid "Cannot send email: user %s has no email address."
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Confirm"
msgstr "Confirmar"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Confirm Password"
msgstr ""

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:41
#, python-format
msgid "Could not create a new account."
msgstr ""

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:63
#, python-format
msgid "Could not reset your password"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_base_config_settings_auth_signup_reset_password
msgid "Enable password reset from Login page"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,help:auth_signup.field_base_config_settings_auth_signup_uninvited
msgid "If unchecked, only invited users may sign up."
msgstr ""

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:91
#, python-format
msgid "Invalid signup token"
msgstr ""

#. module: auth_signup
#: selection:res.users,state:0
msgid "Never Connected"
msgstr ""

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_partner
msgid "Partner"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Password"
msgstr ""

#. module: auth_signup
#: model:mail.template,subject:auth_signup.reset_password_email
msgid "Password reset"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
msgid "Reset Password"
msgstr ""

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:108
#, python-format
msgid "Reset password: invalid username or email"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users_self
msgid "Self"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_form_view
msgid "Send Reset Password Instructions"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_form_view
msgid "Send an Invitation Email"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Sign up"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner_signup_type
#: model:ir.model.fields,field_description:auth_signup.field_res_users_signup_type
msgid "Signup Token Type"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner_signup_valid
#: model:ir.model.fields,field_description:auth_signup.field_res_users_signup_valid
msgid "Signup Token is Valid"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner_signup_url
#: model:ir.model.fields,field_description:auth_signup.field_res_users_signup_url
msgid "Signup URL"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner_signup_expiration
#: model:ir.model.fields,field_description:auth_signup.field_res_users_signup_expiration
msgid "Signup expiration"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner_signup_token
#: model:ir.model.fields,field_description:auth_signup.field_res_users_signup_token
msgid "Signup token"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users_state
msgid "Status"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_base_config_settings_auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,help:auth_signup.field_res_users_website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,help:auth_signup.field_base_config_settings_auth_signup_reset_password
msgid "This allows users to trigger a password reset from the Login page."
msgstr ""

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_users
msgid "Users"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users_website_published
msgid "Visible in Website"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users_website_description
msgid "Website Partner Full Description"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users_website_short_description
msgid "Website Partner Short Description"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users_website_url
msgid "Website URL"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users_website_meta_description
msgid "Website meta description"
msgstr "Meta descripción del sitio web"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users_website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta palabras clave del sitio web"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users_website_meta_title
msgid "Website meta title"
msgstr "Meta título del sitio web"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Your Email"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Your Name"
msgstr "Su nombre"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_base_config_settings
msgid "base.config.settings"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "e.g. John Doe"
msgstr ""
