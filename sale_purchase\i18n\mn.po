# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <bask<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# hish, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Batmunkh Ganbat <<EMAIL>>, 2021\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            Гар үйлдэл шаардагдана."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.sale_order_inherited_form_purchase
msgid "<span class=\"o_stat_text\">Purchase</span>"
msgstr ""

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.purchase_order_inherited_form_sale
msgid "<span class=\"o_stat_text\">Sale</span>"
msgstr ""

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "Худалдан авалтанд тохиосон саатал:"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Борлуулалтын захиалганд тохиосон саатал:"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s):"
msgstr "Саатал:"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_ids
msgid "Generated Purchase Lines"
msgstr "Үүссэн худалдан авалтын мөр"

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_template__service_to_purchase
msgid ""
"If ticked, each time you sell this product through a SO, a RfQ is "
"automatically created to buy the product. Tip: don't forget to set a vendor "
"on the product."
msgstr ""
"Сонгосон бол, энэ барааг захиалганд бичиж зарах бүрд худалдан авалтын RfQ "
"автоматаар үүснэ. Зөвлөмж: бараан дээр нийлүүлэгчийн мэдээлэл тохируулахаа "
"бүү мартаарай."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Manual actions may be needed."
msgstr "Гар ажиллагаа шаардагдаж магадгүй."

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order__purchase_order_count
msgid "Number of Purchase Order Generated"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order__sale_order_count
msgid "Number of Source Sale"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_count
msgid "Number of generated purchase items"
msgstr "Үүссэн худалдан авалтын мөрийн тоо"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid "Ordered quantity decreased!"
msgstr "Захиалсан тоо багассан!"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_line_id
msgid "Origin Sale Item"
msgstr "Эх баримт: Борлуулалтын мөр"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_product_template
msgid "Product Template"
msgstr "Барааны загвар"

#. module: sale_purchase
#: model:ir.model.constraint,message:sale_purchase.constraint_product_template_service_to_purchase
msgid "Product that is not a service can not create RFQ."
msgstr "Үйлчилгээ биш барааны хувьд RfQ үүсэх боломжгүй."

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "Худалдан авалтын захиалга"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Худалдан авалтын захиалгын мөр"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid "Purchase Order generated from %s"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_sale_order_line__purchase_line_ids
msgid ""
"Purchase line generated by this Sales item on order confirmation, or when "
"the quantity was increased."
msgstr ""
"Борлуулалтын захиалга батлагдах үед эсвэл захиалгын тоог өсгөсөн үед уг "
"захиалгын мөрөөс улбаалан үүссэн худалдан авалтын захиалгын мөр."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.product_template_form_view_inherit
msgid "Reordering"
msgstr "Дахин захиалах"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_order_id
msgid "Sale Order"
msgstr "Борлуулалтын захиалга"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order
msgid "Sales Order"
msgstr "Борлуулалтын захиалга"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order_line
msgid "Sales Order Line"
msgstr "Борлуулалтын захиалгын мөр"

#. module: sale_purchase
#: code:addons/sale_purchase/models/purchase_order.py:0
#, python-format
msgid "Sources Sale Orders %s"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_template__service_to_purchase
msgid "Subcontract Service"
msgstr ""

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid ""
"There is no vendor associated to the product %s. Please define a vendor for "
"this product."
msgstr ""
"%s бараанд холбогдох нийлүүлэгч алга. Энэ бараанд нийлүүлэгчийн мэдээлэл "
"тохируулна уу."

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the purchase order if needed."
msgstr ""
"Та захиалсан тоог бууруулж байна. Энэ тохиолдолд хэрэв шаардлагатай бол "
"холбогдох худалдан авалтын захиалгын мэдээллийг шинэчлэхээ бүү мартаарай!"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "cancelled"
msgstr "цуцлагдсан"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "of"
msgstr "ын"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "ordered instead of"
msgstr "оронд нь захиалагдсан"
