<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_es" model="res.partner">
        <field name="name">ES Company</field>
        <field name="vat">ES59962470K</field>
        <field name="street">A</field>
        <field name="city">Candasnos</field>
        <field name="country_id" ref="base.es"/>
        <field name="state_id" ref="base.state_es_z"/>
        <field name="zip"></field>
        <field name="phone">+34 612 34 56 78</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.esexample.com</field>
    </record>

    <record id="demo_company_es" model="res.company">
        <field name="name">ES Company</field>
        <field name="partner_id" ref="partner_demo_company_es"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_es')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_es.demo_company_es'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_es.account_chart_template_assoc')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_es.demo_company_es')"/>
    </function>
</odoo>
