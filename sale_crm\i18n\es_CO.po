# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_crm
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-11-28 05:19+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_issue_count
msgid "# Issues"
msgstr "# Incidencias"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_task_count
msgid "# Tasks"
msgstr "# Tareas"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_sale_order_count
msgid "# of Sales Order"
msgstr "# de Órdenes de Venta"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
msgid "<span class=\"o_stat_text\"> Orders</span>"
msgstr "<span class=\"o_stat_text\"> Órdenes</span>"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
msgid "<span class=\"o_stat_text\"> Quote(s) </span>"
msgstr "<span class=\"o_stat_text\"> Cotización(es) </span>"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_associate_member
msgid ""
"A member with whom you want to associate your membership.It will consider "
"the membership state of the associated member."
msgstr ""
"Un miembro con el que desea asociar su membresía. Se tendrá en cuenta el "
"estado de socio del miembro asociado."

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_activation
#, fuzzy
msgid "Activation"
msgstr "Cotización"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_associate_member
msgid "Associate Member"
msgstr "Miembro Asociado"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_cancel
msgid "Cancel Membership Date"
msgstr "Fecha de Cancelación de la Membresía"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_payment_method_count
msgid "Count Payment Method"
msgstr "Conteo del Método de Pago"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_state
msgid "Current Membership Status"
msgstr "Estado Actual de la Membresía"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_property_stock_customer
msgid "Customer Location"
msgstr "Ubicación del Cliente"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_start
msgid "Date from which membership becomes active."
msgstr "Fecha desde la que la membresía se activa."

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_cancel
msgid "Date on which membership has been cancelled"
msgstr "Fecha en la que la membresía ha sido cancelada."

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_stop
msgid "Date until which membership remains active."
msgstr "Fecha hasta la que la membresía permanece activa."

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_free_member
msgid "Free Member"
msgstr "Miembro Gratuito"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_partner_weight
msgid ""
"Gives the probability to assign a lead to this partner. (0 means no "
"assignation.)"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_implemented_partner_ids
msgid "Implementation References"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_assigned_partner_id
msgid "Implemented by"
msgstr ""

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_account_invoice
msgid "Invoice"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_target_sales_invoiced
msgid "Invoiced in Sale Orders Target"
msgstr "Facturado en Objetivos de Órdenes de Venta"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_state
msgid ""
"It indicates the membership state.\n"
"-Non Member: A partner who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose "
"invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paying member: A member who has paid the membership fee."
msgstr ""
"Indica el estado de la afiliación.\n"
"- No Miembro: Un asociado que no ha solicitado membresía.\n"
"- Miembro Cancelado: Un miembro que ha cancelado su membresía.\n"
"- Antiguo Miembro: Un miembro cuya membresía ha expirado.\n"
"- Miembro en Espera: Un miembro que ha solicitado la membresía y cuya "
"factura va a crearse.\n"
"- Miembro Facturado: Un miembro cuya factura se ha creado.\n"
"- Miembro Pagado: Un miembro que ha pagado su cuota de membresía."

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_date_review
msgid "Latest Partner Review"
msgstr ""

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Iniciativa/Oportunidad"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_grade_id
msgid "Level"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_partner_weight
msgid "Level Weight"
msgstr ""

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.sale_view_inherit123
msgid "Log in the chatter from which opportunity the order originates"
msgstr "Registre en los comentarios desde cual oportunidad se origina la orden"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_member_lines
msgid "Membership"
msgstr "Membresía"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_amount
msgid "Membership Amount"
msgstr "Monto de la Membresía"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_stop
msgid "Membership End Date"
msgstr "Fecha de Fin de la Membresía"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_start
msgid "Membership Start Date"
msgstr "Fecha de Inicio de la Membresía"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
msgid "New Quotation"
msgstr "Nueva Cotización"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_date_review_next
msgid "Next Partner Review"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead_sale_number
msgid "Number of Quotations"
msgstr "Número de Cotizaciones"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_sale_order_opportunity_id
msgid "Opportunity"
msgstr "Oportunidad"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead_order_ids
msgid "Orders"
msgstr "Órdenes"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_date_partnership
#, fuzzy
msgid "Partnership Date"
msgstr "Fecha de Cancelación de la Membresía"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_payment_method_ids
msgid "Payment Methods"
msgstr "Métodos de Pago"

#. module: sale_crm
#: model:ir.actions.act_window,name:sale_crm.sale_action_quotations_new
msgid "Quotation"
msgstr "Cotización"

#. module: sale_crm
#: model:ir.actions.act_window,name:sale_crm.sale_action_quotations
msgid "Quotations"
msgstr "Cotizaciones"

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_sale_order
#: model:ir.model.fields,field_description:sale_crm.field_res_users_sale_order_ids
msgid "Sales Order"
msgstr "Pedido de Venta"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_free_member
msgid "Select if you want to give free membership."
msgstr "Marque si quiere dar membresía gratuita."

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead_sale_amount_total
msgid "Sum of Orders"
msgstr "Suma de las Órdenes"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_sale_order_tag_ids
msgid "Tags"
msgstr "Etiquetas"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_task_ids
msgid "Tasks"
msgstr "Tareas"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_amount
msgid "The price negotiated by the partner"
msgstr "El precio negociado por el asociado"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_property_stock_customer
msgid ""
"This stock location will be used, instead of the default one, as the "
"destination location for goods you send to this partner"
msgstr ""
"Se utilizará esta ubicación de existencias en lugar de la ubicación por "
"defecto, como la ubicación de destino para enviar mercancías a este asociado"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_property_stock_supplier
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for goods you receive from the current partner"
msgstr ""
"Se utiilzará esta ubicación de existencias en lugar de la ubicación por "
"defecto, como la ubicación de origen para recibir mercancías desde este "
"asociado"

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_property_stock_supplier
msgid "Vendor Location"
msgstr "Ubicación del Proveedor"

#~ msgid "# Claims"
#~ msgstr "# Reclamos"

#~ msgid "Campaign"
#~ msgstr "Campaña"

#~ msgid "Geo Latitude"
#~ msgstr "Geo Latitud"

#~ msgid "Geo Localization Date"
#~ msgstr "Fecha Geo Localización"

#~ msgid "Geo Longitude"
#~ msgstr "Geo Longitud"

#~ msgid "Medium"
#~ msgstr "Media"

#~ msgid "Source"
#~ msgstr "Fuente"

#~ msgid ""
#~ "This is a name that helps you keep track of your different campaign "
#~ "efforts Ex: Fall_Drive, Christmas_Special"
#~ msgstr ""
#~ "Éste es un nombre que le ayuda a mantener rastro de sus diferentes "
#~ "esfuerzos en las campañas. Por ejemplo: Rebajas_Otoño, Especial_Navidad"

#~ msgid "This is the method of delivery. Ex: Postcard, Email, or Banner Ad"
#~ msgstr ""
#~ "Éste es el método de entrega. Por ejemplo: Tarjeta postal, Correo "
#~ "electrónico, o Banner de publicidad"

#~ msgid ""
#~ "This is the source of the link Ex: Search Engine, another domain, or name "
#~ "of email list"
#~ msgstr ""
#~ "Ésta es la fuente del enlace. Ej: motor de búsqueda, otro dominio, o "
#~ "nombre de la lista de correo"
