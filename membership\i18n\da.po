# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* membership
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_invoiced
msgid "# Invoiced"
msgstr "# Faktureret"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_paid
msgid "# Paid"
msgstr "# Betalt"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_waiting
msgid "# Waiting"
msgstr "# Venter"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" "
"title=\"Period\"/><strong> From: </strong>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" "
"title=\"Period\"/><strong> Fra: </strong>"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Price\" title=\"Price\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Price\" title=\"Price\"/>"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<strong> To:</strong>"
msgstr "<strong> Til:</strong>"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__associate_member
#: model:ir.model.fields,help:membership.field_res_users__associate_member
msgid ""
"A member with whom you want to associate your membership.It will consider "
"the membership state of the associated member."
msgstr ""
"Et medlem med hvem du vil tilknytte dit medlemskab. Det vil tage hensyn til "
"tilstanden for medlemskabet for det tilknyttede medlem."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__account_invoice_line
msgid "Account Invoice line"
msgstr "Konto fakturalinje"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Add a description..."
msgstr "Tilføj en beskrivelse"

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid "Add a new member"
msgstr "Tilføj nyt medlem"

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__member_price
msgid "Amount for the membership"
msgstr "Beløb for medlemsskab"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__associate_member_id
#: model:ir.model.fields,field_description:membership.field_res_partner__associate_member
#: model:ir.model.fields,field_description:membership.field_res_users__associate_member
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Associate Member"
msgstr "Tilknyttet medlem"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Associated Partner"
msgstr "Tilknyttet partner"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Buy Membership"
msgstr "Køb medlemskab"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Cancel"
msgstr "Annullér"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_cancel
#: model:ir.model.fields,field_description:membership.field_res_users__membership_cancel
msgid "Cancel Membership Date"
msgstr "Annuller dato for medlemskab"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_cancel
msgid "Cancel date"
msgstr "Annuller dato"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__canceled
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__canceled
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__canceled
msgid "Cancelled Member"
msgstr "Annulleret medlem"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Category"
msgstr "Kategori"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership
#: model:ir.model.fields,help:membership.field_product_template__membership
msgid "Check if the product is eligible for membership."
msgstr "Tjek om produktet er berettiget for medlemsskabet. "

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__company_id
#: model:ir.model.fields,field_description:membership.field_report_membership__company_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Company"
msgstr "Virksomhed"

#. module: membership
#: model:ir.ui.menu,name:membership.menu_marketing_config_association
msgid "Configuration"
msgstr "Konfiguration"

#. module: membership
#: model:ir.model,name:membership.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_tree
msgid "Contacts"
msgstr "Adressebog"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__create_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__create_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__membership_state
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Current Membership State"
msgstr "Medlemsskabets nuværende tilstand"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_state
#: model:ir.model.fields,field_description:membership.field_res_users__membership_state
msgid "Current Membership Status"
msgstr "Nuværende status for medlemskab"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Customers"
msgstr "Kunder"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership_date_from
#: model:ir.model.fields,help:membership.field_product_template__membership_date_from
#: model:ir.model.fields,help:membership.field_res_partner__membership_start
#: model:ir.model.fields,help:membership.field_res_users__membership_start
msgid "Date from which membership becomes active."
msgstr "Dato for hvornår medlemskabet bliver aktivt."

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__date
msgid "Date on which member has joined the membership"
msgstr "Dato for hvornår medlemmet er blevet en del af medlemskabet"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_cancel
#: model:ir.model.fields,help:membership.field_res_users__membership_cancel
msgid "Date on which membership has been cancelled"
msgstr "Dato for hvilket medlemskab er blevet annulleret."

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership_date_to
#: model:ir.model.fields,help:membership.field_product_template__membership_date_to
#: model:ir.model.fields,help:membership.field_res_partner__membership_stop
#: model:ir.model.fields,help:membership.field_res_users__membership_stop
msgid "Date until which membership remains active."
msgstr "Datoen, indtil medlemskabet forbliver aktivt."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__display_name
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__display_name
#: model:ir.model.fields,field_description:membership.field_report_membership__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__tot_earned
msgid "Earned Amount"
msgstr "Optjent beløb"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__date_to
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Date"
msgstr "Slut dato"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Membership Date"
msgstr "Dato for ophør af medlemskab"

#. module: membership
#: model:ir.model.fields,help:membership.field_report_membership__date_to
msgid "End membership date"
msgstr "Dato for ophør af medlemskab"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Ending Date Of Membership"
msgstr "Dato for ophør af medlemskab"

#. module: membership
#: model:ir.model.constraint,message:membership.constraint_product_template_membership_date_greater
msgid "Error ! Ending Date cannot be set before Beginning Date."
msgstr "Fejl ! Slutdato kan ikke blive indstillet til før startdato."

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Forecast"
msgstr "Forecast"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__free_member
#: model:ir.model.fields,field_description:membership.field_res_users__free_member
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__free
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__free
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__free
msgid "Free Member"
msgstr "Gratis medlem"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_from
msgid "From"
msgstr "Fra"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Group By"
msgstr "Sortér efter"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Group by..."
msgstr "Sortér efter"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__id
#: model:ir.model.fields,field_description:membership.field_report_membership__id
msgid "ID"
msgstr "ID"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Inactive"
msgstr "Inaktiv"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__account_invoice_id
msgid "Invoice"
msgstr "Faktura"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Invoice Membership"
msgstr "Faktura medlemskab"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__invoiced
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__invoiced
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__invoiced
msgid "Invoiced Member"
msgstr "Faktureret medlem"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Invoiced/Paid/Free"
msgstr "Faktureret/Betalt/Gratis"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_state
#: model:ir.model.fields,help:membership.field_res_users__membership_state
msgid ""
"It indicates the membership state.\n"
"-Non Member: A partner who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paying member: A member who has paid the membership fee."
msgstr ""
"Det indikerer medlemskabets status.\n"
"-Ikke-medlem: En partner som ikke har ansøgt om nogen form for medlemskab.\n"
"-Annulleret medlem: Et medlem som har annulleret sit medlemskab.\n"
"-Forældet medlem: Et medlem hvor datoen for medlemskabet er udløbet.\n"
"-Afventende medlem: Et medlem som har ansøgt om medlemskab og hvis faktura er ved at blive oprettet.\n"
"-Faktureret medlem: Et medlem hvis faktura er blevet oprettet.\n"
"-Betalt medlem: Et medlem som har betalt den mængde medlemskabet koster."

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__state
msgid ""
"It indicates the membership status.\n"
"-Non Member: A member who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paid Member: A member who has paid the membership amount."
msgstr ""
"Det indikerer medlemskabets status.\n"
"-Ikke-medlem: Et medlem som ikke har ansøgt om nogen form for medlemskab.\n"
"-Annulleret medlem: Et medlem som har annulleret sit medlemskab.\n"
"-Forældet medlem: Et medlem hvor datoen for medlemskabet er udløbet.\n"
"-Afventende medlem: Et medlem som har ansøgt om medlemskab og hvis faktura er ved at blive oprettet.\n"
"-Faktureret medlem: Et medlem hvis faktura er blevet oprettet.\n"
"-Betalt medlem: Et medlem som har betalt den mængde medlemskabet koster."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date
msgid "Join Date"
msgstr "Dato for oprettelse af medlemskab"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_invoice_view
msgid "Join Membership"
msgstr "Deltag i medlemskab "

#. module: membership
#: model:ir.model,name:membership.model_account_move
msgid "Journal Entry"
msgstr "Postering"

#. module: membership
#: model:ir.model,name:membership.model_account_move_line
msgid "Journal Item"
msgstr "Journalpost"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice____last_update
#: model:ir.model.fields,field_description:membership.field_membership_membership_line____last_update
#: model:ir.model.fields,field_description:membership.field_report_membership____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__write_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__write_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__partner_id
msgid "Member"
msgstr "Medlem"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__member_price
msgid "Member Price"
msgstr "Pris for medlemskab"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_members
#: model:ir.ui.menu,name:membership.menu_association
#: model:ir.ui.menu,name:membership.menu_membership
#: model_terms:ir.ui.view,arch_db:membership.membership_members_tree
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Members"
msgstr "Medlemmer"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_report_membership_tree
msgid "Members Analysis"
msgstr "Medlems analyse"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__product_id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__membership_id
#: model:ir.model.fields,field_description:membership.field_product_product__membership
#: model:ir.model.fields,field_description:membership.field_product_template__membership
#: model:ir.model.fields,field_description:membership.field_res_partner__member_lines
#: model:ir.model.fields,field_description:membership.field_res_users__member_lines
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_graph1
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_pivot
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership"
msgstr "Medlemskab"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_amount
#: model:ir.model.fields,field_description:membership.field_res_users__membership_amount
msgid "Membership Amount"
msgstr "Medlemsbeløb"

#. module: membership
#: model:ir.model,name:membership.model_report_membership
msgid "Membership Analysis"
msgstr "Medlemskabs analyse"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Membership Duration"
msgstr "Varighed af medlemskab"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product__membership_date_to
#: model:ir.model.fields,field_description:membership.field_product_template__membership_date_to
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_stop
#: model:ir.model.fields,field_description:membership.field_res_users__membership_stop
msgid "Membership End Date"
msgstr "Medlemskabets slutdato"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__member_price
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership Fee"
msgstr "Medlemskabs gebyr"

#. module: membership
#: model:ir.model,name:membership.model_membership_invoice
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Membership Invoice"
msgstr "Medlemskabs faktura"

#. module: membership
#: model:ir.model,name:membership.model_membership_membership_line
msgid "Membership Line"
msgstr "Medlemskablinje"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership Partners"
msgstr "Medlemskabs partnere"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__membership_id
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership Product"
msgstr "Medlemskabs produkt"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_products
#: model:ir.ui.menu,name:membership.menu_membership_products
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Membership Products"
msgstr "Medlemskabs produkter"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product__membership_date_from
#: model:ir.model.fields,field_description:membership.field_product_template__membership_date_from
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_start
#: model:ir.model.fields,field_description:membership.field_res_users__membership_start
msgid "Membership Start Date"
msgstr "Medlemskabets startdato"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership State"
msgstr "Medlemskabets tilstand"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__state
msgid "Membership Status"
msgstr "Medlemskabets status"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership products"
msgstr "Medlemskabets produkter"

#. module: membership
#: model:ir.actions.server,name:membership.ir_cron_update_membership_ir_actions_server
#: model:ir.cron,cron_name:membership.ir_cron_update_membership
#: model:ir.cron,name:membership.ir_cron_update_membership
msgid "Membership: update memberships"
msgstr "Medlemskab: opdatér medlemskaber"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Memberships"
msgstr "Medlemskaber"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Month"
msgstr "Måned"

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_report_membership_tree
msgid "No data yet!"
msgstr "Ingen data endnu!"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__none
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__none
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__none
msgid "Non Member"
msgstr "Ikke-medlemmer"

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid ""
"Odoo helps you easily track all activities related to a member: \n"
"                  Current Membership Status, Discussions and History of Membership, etc."
msgstr ""
"Odoo hjælper dig med at få et overblik over alle aktiviteter, som er relateret til et medlem:\n"
"  Nuværende status af medlemskab, diskussioner, historik over medlemskab, osv."

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__old
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__old
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__old
msgid "Old Member"
msgstr "Gammelt medlem"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__paid
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__paid
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__paid
msgid "Paid Member"
msgstr "Betalt medlem"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__partner
msgid "Partner"
msgstr "Kontakt"

#. module: membership
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "Partner doesn't have an address to make the invoice."
msgstr "Partner har ikke en adresse til at oprette fakturaren."

#. module: membership
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "Partner is a free Member."
msgstr "Partneren er et gratis medlem."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__tot_pending
msgid "Pending Amount"
msgstr "Mængde som er afventende"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Product Name"
msgstr "Produktnavn"

#. module: membership
#: model:ir.model,name:membership.model_product_template
msgid "Product Template"
msgstr "Produktskabelon"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__quantity
msgid "Quantity"
msgstr "Antal"

#. module: membership
#: model:ir.ui.menu,name:membership.menu_report_membership
msgid "Reporting"
msgstr "Rapportering"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Revenue Done"
msgstr "Indtægt færdig"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__user_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Salesperson"
msgstr "Sælger"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__free_member
#: model:ir.model.fields,help:membership.field_res_users__free_member
msgid "Select if you want to give free membership."
msgstr "Vælg hvis du vil tildele gratis medlemskab."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__start_date
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Start Date"
msgstr "Start dato"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Starting Date Of Membership"
msgstr "Dato for opstart af medlemskab"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of # Invoiced"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of # Paid"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of Earned Amount"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.report_membership_view_tree
msgid "Sum of Quantity"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Taxes"
msgstr "Moms"

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__account_invoice_id
msgid "The move of this entry line."
msgstr "Bevægelser på denne posteringslinje."

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_amount
#: model:ir.model.fields,help:membership.field_res_users__membership_amount
msgid "The price negotiated by the partner"
msgstr "Pris forhandlet af partneren"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "This note will be displayed on quotations..."
msgstr "Denne note vil blive vist på tilbudsanmodninger..."

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display paid, old and total earned columns"
msgstr "Dette vil vise kolonnerne betalt, gamle og tjent i alt"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display waiting, invoiced and total pending columns"
msgstr "Dette vil vise kolonnerne ventende, fakturerede  og igangværende"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_to
msgid "To"
msgstr "Til"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Vendors"
msgstr "Leverandører"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__waiting
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__waiting
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__waiting
msgid "Waiting Member"
msgstr "Afventende medlem"

#. module: membership
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "You cannot create recursive associated members."
msgstr "Du kan ikke oprette rekursive associerede medlemmer."
