# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_iban
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: Ko<PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_iban
#. openerp-web
#: code:addons/base_iban/static/src/js/iban_widget.js:0
#, python-format
msgid "Account isn't IBAN compliant."
msgstr ""

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Τραπεζικοί Λογαριασμοί"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr ""
"Αδύνατος ο υπολογισμός του BBAN επειδή ο αριθμός λογαριασμού δεν είναι ΙΒΑΝ."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "IBAN"
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""
"Ο IBAN δεν φαίνεται να είναι σωστός. Θα έπρεπε να καταχωρήσετε κάτι σαν αυτό %s\n"
"Όπου B = Εθνικός κωδικός τράπεζας, S = Κωδικός τράπεζας, C = Αριθμός λοαγαριασμού, k = Check digit"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr "Ο ΙΒΑΝ είναι μη έγκυρος, θα έπρεπε να ξεκινά με τον κωδικό της χώρας"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "There is no IBAN code."
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr ""
"Αυτός ο ΙΒΑΝ δεν περνά τον έλεγχο εγκυρότητας, παρακαλώ επαληθεύστε τον."
