# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <and<PERSON>.<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 01:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "# Messages"
msgstr "# Meddelanden "

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "# Betyg"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "# av sessioner"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "# av talare"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% Glad"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr "% av Glädje"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s och %s skriver..."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s skriver..."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s och flera skriver..."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Display the button' displays the chat button on the pages.\n"
"* 'Auto popup' displays the button and automatically open the conversation pane.\n"
"* 'Hide the button' hides the chat button on the pages."
msgstr ""
"* 'Visa knappen' visar chattknappen på sidorna.\n"
"* 'Automatisk popup' visar knappen och öppnar automatiskt ett konversationsfönster.\n"
"* 'Dölj knappen' döljer chattknappen från sidorna."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ", på"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- Visa äldre meddelanden --------"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"
msgstr "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"<span class=\"text-muted\">Define rules for your live support channel. You "
"can apply an action for the given URL, and per country.<br/>To identify the "
"country, GeoIP must be installed on your server, otherwise, the countries of"
" the rule will not be taken into account.</span>"
msgstr ""
"<span class=\"text-muted\">Definiera regler för din supportkanal. Du kan "
"applicera en händelse per URL, och per land.<br/>För att identifiera land så"
" måste GeoIP vara installerad på din server, annars kommer regelns land inte"
" räknas med.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr "<span style=\"font-size: 10px;\">Livechat Konversation</span><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr "<span>Vänliga hälsningar,</span><br/><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr "<span>Hej,</span><br/>Här är en kopia av din konversation med"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""
"A session is without answer if the operator did not answer. \n"
"If the visitor is also the operator, the session will always be answered."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Action"
msgstr "Åtgärd"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "Administratör"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Anonymous"
msgstr "Anonym"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__anonymous_name
msgid "Anonymous Name"
msgstr "Anonymt namn"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "Är du inne i matrix?"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Ask something ..."
msgstr "Fråga något..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Attendees"
msgstr "Deltagare"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Auto popup"
msgstr "Automatisk popup"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Auto popup timer"
msgstr "Automatisk popup timer"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "Genomsnittlig varaktighet"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "Genomsnittlig meddelande "

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr ""
"Genomsnittlig tid i sekunder för att ge det första svaret till en besökare"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "Genomsnittlig tid för att ge det första svaret till en besökare."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bad"
msgstr "Dålig"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bounced"
msgstr "Studsade"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "Knapp backgrundsfärg"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "Knapp textfärg"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Canceled"
msgstr "Avbruten"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_canned_response_action
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "Standardiserade svar"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""
"Standardiserade svar tillåterdig att infoga förskrivna svar i\n"
"              dina meddelanden genom att skiva <i>:genväg</i>. Genvägen är\n"
"\n"
"Canned responses allow you to insert prewritten responses in\n"
"your messages by typing :shortcut. The shortcut is\n"
"replaced directly in your message, so that you can still edit\n"
"it before sending."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Changed"
msgstr "Ändrad"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Channel"
msgstr "Säljkanal"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr "Kanalhuvud Färg"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "Kanalnamn"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "Kanalregel"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "Kanalregler"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Kanaltyp"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
msgid "Channels"
msgstr "Kanaler"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "Chattmeddelande Platshållare"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Chatten är privat och unik mellan 2 personer. Gruppen är privat bland "
"inbjudna personer. Kanalen kan anslutas till fritt (beroende på dess "
"konfiguration)."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Chat with one of our collaborators"
msgstr "Chatta med en av våra medarbertare"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close"
msgstr "Stäng"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close chat window"
msgstr "Stäng chattfönster"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close conversation"
msgstr "Stäng konversation"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "Kod"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "Konfiguration"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "Konversation"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Conversation Sent"
msgstr "Konversation Skickad"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr "Konversation med %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr "Konversation Hanterad"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr "Kopiera och klistra in den här koden i din webbsida, inuti taggen:"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__country_id
msgid "Country"
msgstr "Land"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "Land av besökaren"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "Land av besökaren av kanalen"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr "Skapa en kanal och starta prata för att fylla up din historia."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid "Create a new canned response"
msgstr "Skapa ett nytt standardiserat svar "

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation Date"
msgstr "Registeringsdatum"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "Skapelse datum"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "Skapelse datum (timme)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "Kundbetyg"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "Dagnummer"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day number of the session (1 is Monday, 7 is Sunday)"
msgstr "Dagnummer av smatalet (1 är Måndag, 7 är Söndag)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "Dagar av aktivetet"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "Standard bakgrundsfärg för livechatt knappen"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr "Standard bakgrundsfärg för kanalhuvud efter öppning"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "Standard textfärg för livechatt knappen"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "Standard text för livechatt knappen"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr "Standard bakgrundsfärg för efter öppning"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "Definiera en ny live chat kanal."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Auto popup' otherwise this parameter will not be "
"taken into account."
msgstr ""
"fördröjning (i sekunder) tills konversationsfönstret öppnas automatiskt. "
"Obs: Den valda åtgärden måste vara \"auto popup\", annars kommer denna "
"parameter inte att användas."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Delete"
msgstr "Ta bort"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Did we correctly answer your question ?"
msgstr "Gav vi ett korrekt svar till din fråga?"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "Sammanställning"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr "Diskussionskanal"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Display the button"
msgstr "Visa knappen"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Document not downloadable"
msgstr "Document är inte nedladdningsbart"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Download"
msgstr "Hämta"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "Konversationens varaktighet (i sekunder)"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Error"
msgstr "Fel"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Explain your note"
msgstr "Förklara din anteckning"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr ""
"För webbsidor som byggts med Odoo CMS, gå till Webbplats > Konfiguration > "
"Inställningar och välj Livechatt Kanalen som du vill lägga till på din "
"webbsida."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""
"Med tanke på ordern att hitta en matchande regel. Om 2 regler matchas för "
"den givna webbadressen / landet, kommer den med den lägsta sekvensen att "
"väljas."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Good"
msgstr "Bra"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Group By..."
msgstr "Gruppera efter..."

#. module: im_livechat
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
#, python-format
msgid "Have a Question? Chat with us."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr "Header bakgrundsfärg"

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide the button"
msgstr "Göm knappen"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "History"
msgstr "Historik"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "Starttiden"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "How may I help you?"
msgstr "Hur kan jag hjälpa dig."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "Hur använder man Websida Livechatt widgeten?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Idle"
msgstr "inaktiv"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
#, python-format
msgid "Image"
msgstr "Bild"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Invalid email address"
msgstr "Ogiltig e-postadress"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr "Är kategori livechat öppen"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "Är chatten pågående?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "Är besökaren anonym"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_3
msgid "Joel Willis, Marc Demo"
msgstr "Joel Willis, Marc Demo"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_2
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_6
msgid "Joel Willis, Mitchell Admin"
msgstr "Joel Willis, Mitchell Admin"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "Gå med"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "Gå med i Kanalen"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr "KPI Chat-konversationsvärdet"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr "KPI Chat-värdet"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr "KPI Chat-svarsvärde"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "Sista 24t"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator____last_update
msgid "Last Modified on"
msgstr "Senast redigerad"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "Ledig"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "Lämna Kanal"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Lyssnare av Kanalen"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "Livechatt"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "Livechatt Kanalsökning"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/components/discuss/discuss.js:0
#: code:addons/im_livechat/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr "Livechat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr "Livechatt Knapp"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "Livechatt Knapp Färg"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Livechatt Kanal"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "Livechatt Kanal Regler"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__mail_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "Livechatt Konversation"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_mail_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "Livechatt Operator ID krävs för en kanal av typ Livechatt."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "Livechatt supportkanals rapport. "

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audiance, the duration of a session, etc."
msgstr ""
"Livechatt Supportkanals statistik gör att du enkelt kan kontrollera och "
"analysera ditt företags Livechatt sessioner. Extrahera information om de "
"missade sessionerna, publiken, varaktigheten av en session, etc."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "Livechatt Supportoperatörsrapport"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Livechatt supportstatistik."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
msgid "Livechat Username"
msgstr "Livechatt Användarnamn"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr "Livechatt Fönster"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_active
msgid "Livechat session is active until visitor leave the conversation."
msgstr ""
"Livechatt sessioner är aktiva tills en besökare lämnar konversationen."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading"
msgstr "Laddar"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading older messages..."
msgstr "Laddar äldre meddelanden."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Read"
msgstr "Markera som Läst"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "Mark as Todo"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as todo"
msgstr "Markera som todo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "Matchningsordning"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
msgid "Message"
msgstr "Meddelande"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "Missade sessioner"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
msgid "Name"
msgstr "Namn"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "New messages"
msgstr "Nya meddelanden"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Next"
msgstr "Nästa"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr "Inga tillgängliga medarbetare, var vänlig att försöka senare."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr "Inga kundbetyg på denna livechatt session än."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "Ingen data ännu!"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "No history found"
msgstr "Ingen historia hittad"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Note by"
msgstr "Skrivet av"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "Number of conversation"
msgstr "Nummer av konversationer"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "Nummer av dagar sedan första sessionen för medarbetaren."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "Nummer av olika talare"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "Nummer av meddelanden i konversationen."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Offline"
msgstr "Offline"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Online"
msgstr "Uppkopplad"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "Onlinechattsnamn"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Oops! Something went wrong."
msgstr "Oops! Något gick fel."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_operator_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "Operatör"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "Operatorsanalys"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_operator_id
msgid "Operator for this specific channel"
msgstr "Operatör av den här specifika kanalen"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "Operatorer"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"Operators\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"
msgstr ""
"Operatorer\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""
"Operatörer som är inaktiva i Odoo i mer än 30 minuter anses vara "
"frånkopplade."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "Optioner"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "PDF file"
msgstr "PDF fil"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Procent av glada betyg"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please check your internet connection."
msgstr "Kontrollera din internetanslutning."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait"
msgstr "Var vänlig att vänta"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait..."
msgstr "Var vänlig att vänta..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "Drivs med"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Previous"
msgstr "Föregående"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Print"
msgstr "Skriv ut"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_form_livechat
msgid "Rating"
msgstr "Betyg"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_avg
msgid "Rating Average"
msgstr "Betyg Medeltal"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Betyg Senaste Feedback"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Betyg Senaste Bild"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Betyg Senaste Värde"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Betyggsättning"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_count
msgid "Rating count"
msgstr "Betyg Antal"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Rating: %s"
msgstr "Betyg: %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
msgid "Ratings"
msgstr "Betyg"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "Betyg för livechattskanal."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Ready"
msgstr "Redo"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "Orsaken till betyget"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Receive a copy of this conversation"
msgstr "Få en kopia av denna konversation"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by Everyone"
msgstr "Mottagen av alla"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by:"
msgstr "Mottagen av:"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""
"Regular expression specificerar vilka webbplatser som denna regel kommer bli"
" applicerad på.  "

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reply"
msgstr "Besvara"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "Rapport"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reset Zoom"
msgstr "Återställ Zoom"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Reset to default colors"
msgstr "Återställ till standard färger"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Rotate"
msgstr "Rotera"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "Regler"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "Nöjdhetsgrad"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "Spara din kanal för att see din konfigurerings widget."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Say something"
msgstr "Säg något."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "Skript (externt)"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Search history"
msgstr "Sökhistorik"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "Sökrapport"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr "Se de 15 senaste besökta sidorna."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by Everyone"
msgstr "Sett av alla"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by:"
msgstr "Sedd av:"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Send"
msgstr "Skicka"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Sent"
msgstr "Skickat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Session Date"
msgstr "Sessiondatum"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Session Form"
msgstr "Sessionsform"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "Sessionsstatistik."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Session expired... Please refresh and try again."
msgstr "Session har gått ut ... Uppdatera och försök igen."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "Session inte betygsatt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "Sessioner utan svar"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "Sessioner"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "Sessions Historia"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "Sessions startdatum"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "Sessions starttimme"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start date of the conversation"
msgstr "Konversationsstartdatum"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start hour of the conversation"
msgstr "Konversationsstarttimme"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "Knappens Text"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "Text som uppmanar användaren att initiera chatten."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Thank you for your feedback"
msgstr "Tack för din återkoppling"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "Kanalen för regeln."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__name
msgid "The name of the channel"
msgstr "Namnet för kanalen."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide Button', "
"the chat button will be hidden on the specified URL from the visitors "
"located in these 2 countries. This feature requires GeoIP installed on your "
"server."
msgstr ""
"Regeln kommer endast appliceras för dessa länder. Exempel: om du väljer "
"'Sverige' och 'Finland' och sätter händelsen till \"Dölj knappen\", så "
"kommer chattknappen bara vara dold vid specificerad URL för besökare från "
"dessa 2 länder. Denna funktion kräver att GeoIP är installerad på din "
"server."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "Användaren kommer att kunna ta bort support kanalen."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "Användaren kommer att kunna gå med support kanaler."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "Det finns inget betyg för den här kanalen vid detta tillfälle."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "Den här veckan"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""
"Detta är ett automatiskt 'Välkomst' meddelande som dina besökare kommer se "
"när de startar en ny konversation."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr ""
"Detta användarnamn kommer att användas som ditt namn i livechatt kanaler."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "Tid till svar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr "Tid att svara(sekunder)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer the user in second."
msgstr "Tid till svar till användaren i sekunder."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer(sec)"
msgstr "Tid att svara(sekunder)"

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr "Tips: Använd standardiserade svar för att chatta snabbare"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr "Title Färg"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Today"
msgstr "Idag"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "Behandlade sessioner"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Try again"
msgstr "Försök Igen"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Type <b>:shortcut</b> to insert a canned response in your message.<br>"
msgstr ""
"Skriv <b>:shortcut</b> för att infoga ett standardsvar i ditt "
"meddelande.<br>"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "URL Regex"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""
"URL till en statisk sida där din klient kan prata med operatören av kanalen."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Undefined"
msgstr "Odefinierad"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploaded"
msgstr "Uppladdad"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploading"
msgstr "Laddar Upp"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""
"Använd standardsvar för att definiera mallar för meddelanden i livechat-"
"appen. För att ladda ett standardsvar, börja din mening med ':' och välj "
"mallen."

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "Användare"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr "Användare Livechat Användarnamn"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "Användarinställningar"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is idle"
msgstr "Användaren är inaktiv"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is offline"
msgstr "Användaren är offline"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is online"
msgstr "Användaren är online"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
msgid "Users"
msgstr "Användare"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Video"
msgstr "Video"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Viewer"
msgstr "Åskådare"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Visitor"
msgstr "Besökare"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_0
msgid "Visitor #234, Mitchell Admin"
msgstr "Besökare #234, Mitchell Admin"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_1
msgid "Visitor #323, Marc Demo"
msgstr "Besökare #323, Marc Demo"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_4
msgid "Visitor #532, Mitchell Admin"
msgstr "Besökare #532, Mitchell Admin"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_5
msgid "Visitor #649, Mitchell Admin"
msgstr "Besökare #649, Mitchell Admin"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_7
msgid "Visitor #722, Marc Demo"
msgstr "Besökare #722, Marc Demo"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Visitor has left the conversation."
msgstr "Besökare har lämnat konversationen."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr "Besökare är Glad"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.mail_channel_livechat_1
msgid "Visitor, Mitchell Admin"
msgstr "Besökare, Mitchell Admin"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "Webbsida"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "Websida Livechatt kanaler"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "Välkomst Meddelande"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "Widget"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Yesterday"
msgstr "Igår"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Your chatter history is empty"
msgstr "Din chatter historik är tom"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom In"
msgstr "Zooma in"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Zooma ut"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr "t.ex. /contactus"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "t.ex. Hej, hur kan jag hjälpa dig?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "t.ex. DinWebbsida.se"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "from"
msgstr "från"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "on"
msgstr "på"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""
"eller kopiera denna URL och skicka den via e-post till dina  kunder och "
"leverantörer."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read less"
msgstr "läs mindre"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read more"
msgstr "läs mer"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "sekunder"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "unnamed"
msgstr "namnlös"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr "{{författarens_namn}}"
