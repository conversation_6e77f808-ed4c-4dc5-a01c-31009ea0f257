# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import_module
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: Minj P <<EMAIL>>, 2021\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "Цуцлах"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr "Хаах"

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Could not select database '%s'"
msgstr "'%s' өгөгдлийн баазыг сонгож чадсангүй"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "File '%s' exceed maximum allowed file size"
msgstr "'%s' файл нь зөвшөөрсөн хэмжээнээс давсан байна"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr "init албадах"

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid ""
"Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr ""
"Суулгасан байсан ч init горимыг албадах. (`noupdate='1'` бичлэгүүдийг "
"шинэчлэнэ)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr "ID"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import App"
msgstr "Апп импортлох"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr "Зурвас импортлох"

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import Module"
msgstr "Модул импортлох"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import module"
msgstr "Модуль импортлох"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr "Импортлосон Модуль"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr "Модуль"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr "Модул .ZIP файл"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "No file sent."
msgstr "Файл илгээгдээгүй."

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr ""
"Тэмдэглэл: Та зөвхөн өгөгдлийн модулийг импортлох боломжтой (.xml файл ба үл"
" хөдлөх хөрөнгүүд)"

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Only administrators can upload a module"
msgstr "Зөвхөн администратор л модуль ачааллах боломжтой"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Only zip files are supported."
msgstr "Зөвхөн зиплэсэн файлыг дэмждэг."

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Open Modules"
msgstr "Модул нээх"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Select module package to import (.zip file):"
msgstr "Импортлох модулиа сонго (.zip file):"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "Төлөв"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Studio customizations require Studio"
msgstr "Студи төрлийн өөрчлөлт хийхэд Студи апп шаардана"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Studio customizations require the Odoo Studio app."
msgstr "Студи төрлийн өөрчлөлт хийхэд Odoo Студи апп шаардана."

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"Unmet module dependencies: \n"
"\n"
" - %s"
msgstr ""

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr "Харах"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr "дууссан"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr "init"
