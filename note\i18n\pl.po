# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* note
# 
# Translators:
# <PERSON><PERSON><PERSON> <stre<PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.war<PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <judyta.kaz<PERSON><PERSON><PERSON>@openglobe.pl>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Tadeusz Karpiński <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "<i class=\"fa fa-check\" role=\"img\" aria-label=\"Opened\" title=\"Opened\"/>"
msgstr "<i class=\"fa fa-check\" role=\"img\" aria-label=\"Opened\" title=\"Opened\"/>"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "<i class=\"fa fa-undo\" role=\"img\" aria-label=\"Closed\" title=\"Closed\"/>"
msgstr "<i class=\"fa fa-undo\" role=\"img\" aria-label=\"Closed\" title=\"Closed\"/>"

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity_type__category
msgid "Action"
msgstr "Akcja"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction
msgid "Action Needed"
msgstr "Wymagane działanie"

#. module: note
#: model:ir.model.fields,help:note.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Akcje mogą wyzwalać określone zachowanie, takie jak otwieranie widoku "
"kalendarza lub automatyczne oznaczanie jako zrobione podczas przesyłania "
"dokumentu"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__open
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Active"
msgstr "Aktywne"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_ids
msgid "Activities"
msgstr "Czynności"

#. module: note
#: model:ir.model,name:note.model_mail_activity
msgid "Activity"
msgstr "Czynność"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekoracja wyjątku aktywności"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_state
msgid "Activity State"
msgstr "Stan aktywności"

#. module: note
#: model:ir.model,name:note.model_mail_activity_type
msgid "Activity Type"
msgstr "Typ aktywności"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktywności"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid "Add a new personal note"
msgstr "Dodaj nową notatkę"

#. module: note
#: model_terms:ir.actions.act_window,help:note.note_tag_action
msgid "Add a new tag"
msgstr "Dodaj nowy znacznik"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Add a note"
msgstr "Dodaj notatkę"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Add new note"
msgstr "Dodaj nową notatkę"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Archive"
msgstr "Archiwizuj"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_attachment_count
msgid "Attachment Count"
msgstr "Liczba załączników"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "By sticky note Category"
msgstr "Według kategorii karteczki"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Channel"
msgstr "Kanał"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__color
#: model:ir.model.fields,field_description:note.field_note_tag__color
msgid "Color Index"
msgstr "Indeks kolorów"

#. module: note
#: model:ir.ui.menu,name:note.menu_note_configuration
msgid "Configuration"
msgstr "Konfiguracja"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_uid
#: model:ir.model.fields,field_description:note.field_note_stage__create_uid
#: model:ir.model.fields,field_description:note.field_note_tag__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_date
#: model:ir.model.fields,field_description:note.field_note_stage__create_date
#: model:ir.model.fields,field_description:note.field_note_tag__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__date_done
msgid "Date done"
msgstr "Data wykonania"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Delete"
msgstr "Usuń"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__display_name
#: model:ir.model.fields,field_description:note.field_note_stage__display_name
#: model:ir.model.fields,field_description:note.field_note_tag__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Dropdown menu"
msgstr "Menu rozwijane"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__fold
msgid "Folded by Default"
msgstr "Domyślnie zwinięte"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Follower"
msgstr "Obserwator"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_follower_ids
msgid "Followers"
msgstr "Obserwatorzy"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_partner_ids
msgid "Followers (Partners)"
msgstr "Obserwatorzy (partnerzy)"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikona Font awesome np. fa-tasks"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Future Activities"
msgstr "Przyszłe czynności"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Group By"
msgstr "Grupuj wg"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__has_message
msgid "Has Message"
msgstr "Ma wiadomość"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__id
#: model:ir.model.fields,field_description:note.field_note_stage__id
#: model:ir.model.fields,field_description:note.field_note_tag__id
msgid "ID"
msgstr "ID"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona wskazująca na wyjątek aktywności."

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction
#: model:ir.model.fields,help:note.field_note_note__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeśli zaznaczone, nowe wiadomości wymagają twojej uwagi."

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Jeśli zaznaczone, niektóre wiadomości napotkały błędy podczas doręczenia."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_is_follower
msgid "Is Follower"
msgstr "Jest obserwatorem"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note____last_update
#: model:ir.model.fields,field_description:note.field_note_stage____last_update
#: model:ir.model.fields,field_description:note.field_note_tag____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_uid
#: model:ir.model.fields,field_description:note.field_note_stage__write_uid
#: model:ir.model.fields,field_description:note.field_note_tag__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_date
#: model:ir.model.fields,field_description:note.field_note_stage__write_date
#: model:ir.model.fields,field_description:note.field_note_tag__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Late Activities"
msgstr "Czynności zaległe"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_main_attachment_id
msgid "Main Attachment"
msgstr "Główny załącznik"

#. module: note
#: model:note.stage,name:note.note_stage_01
msgid "Meeting Minutes"
msgstr "Notatki ze spotkań"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error
msgid "Message Delivery error"
msgstr "Błąd doręczenia wiadomości"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_ids
msgid "Messages"
msgstr "Wiadomości"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Ostateczny terminin moich aktywności"

#. module: note
#: model:note.stage,name:note.note_stage_00
msgid "New"
msgstr "Nowe"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Następna Czynność wydarzenia w kalendarzu"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Termin kolejnej czynności"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_summary
msgid "Next Activity Summary"
msgstr "Podsumowanie kolejnej czynności"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_type_id
msgid "Next Activity Type"
msgstr "Typ następnej czynności"

#. module: note
#: model:ir.model,name:note.model_note_note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Note"
msgstr "Notatka"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__memo
msgid "Note Content"
msgstr "Treść notatki"

#. module: note
#: model:ir.model,name:note.model_note_stage
msgid "Note Stage"
msgstr "Etap notatki"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__name
msgid "Note Summary"
msgstr "Podsumowanie notatki"

#. module: note
#: model:ir.model,name:note.model_note_tag
msgid "Note Tag"
msgstr "Tagi notatki"

#. module: note
#: code:addons/note/models/res_users.py:0
#: model:ir.actions.act_window,name:note.action_note_note
#: model:ir.ui.menu,name:note.menu_note_notes
#: model:note.stage,name:note.note_stage_02
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#, python-format
msgid "Notes"
msgstr "Notatki"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"Notes are private, unless you share them by inviting follower on a note.\n"
"            (Useful for meeting minutes)."
msgstr ""
"Notatki są prywatne, chyba że udostępnisz je zapraszając obserwatora na notatkę.\n"
"            (Przydatne dla notatek ze spotkań)."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction_counter
msgid "Number of Actions"
msgstr "Liczba akcji"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error_counter
msgid "Number of errors"
msgstr "Liczba błędów"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Liczba wiadomości wymagających akcji"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Liczba wiadomości z błędami przy doręczeniu"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_unread_counter
msgid "Number of unread messages"
msgstr "Liczba nieprzeczytanych wiadomości"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__user_id
#: model:ir.model.fields,field_description:note.field_note_stage__user_id
msgid "Owner"
msgstr "Właściciel"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__user_id
msgid "Owner of the note stage"
msgstr "Właściciel etapu"

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity__note_id
msgid "Related Note"
msgstr "Notatka powiązana"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Remember..."
msgstr "Pamiętaj..."

#. module: note
#: model:ir.model.fields.selection,name:note.selection__mail_activity_type__category__reminder
#: model:mail.activity.type,name:note.mail_activity_data_reminder
msgid "Reminder"
msgstr "Przypomnienie"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_user_id
msgid "Responsible User"
msgstr "Użytkownik odpowiedzialny"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "SAVE"
msgstr "ZAPISZ"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__sequence
#: model:ir.model.fields,field_description:note.field_note_stage__sequence
msgid "Sequence"
msgstr "Sekwencja"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Set date and time"
msgstr "Ustaw datę i czas"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Pokaż wszystkie rekordy, których data kolejnej czynności przypada przed "
"dzisiaj"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_id
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Stage"
msgstr "Etap"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__name
msgid "Stage Name"
msgstr "Nazwa etapu"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_form
msgid "Stage of Notes"
msgstr "Etap notatek"

#. module: note
#: model:ir.actions.act_window,name:note.action_note_stage
#: model:ir.ui.menu,name:note.menu_notes_stage
#: model_terms:ir.ui.view,arch_db:note.view_note_note_tree
msgid "Stages"
msgstr "Etapy"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_tree
msgid "Stages of Notes"
msgstr "Etapy notatek"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_ids
msgid "Stages of Users"
msgstr "Etapy użytkowników"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status na podstawie czynności\n"
"Zaległe: Termin już minął\n"
"Dzisiaj: Data czynności przypada na dzisiaj\n"
"Zaplanowane: Przyszłe czynności."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_tag__name
msgid "Tag Name"
msgstr "Nazwa tagu"

#. module: note
#: model:ir.model.constraint,message:note.constraint_note_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Nazwa znacznika już istnieje!"

#. module: note
#: model:ir.actions.act_window,name:note.note_tag_action
#: model:ir.model.fields,field_description:note.field_note_note__tag_ids
#: model:ir.ui.menu,name:note.notes_tag_menu
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_form
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_tree
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Tags"
msgstr "Tagi"

#. module: note
#. openerp-web
#: code:addons/note/static/src/js/systray_activity_menu.js:0
#, python-format
msgid "Today"
msgstr "Na dzisiaj"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Today Activities"
msgstr "Dzisiejsze czynności"

#. module: note
#: model:note.stage,name:note.note_stage_03
msgid "Todo"
msgstr "Do zrobienia"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ wyjątku działania na rekordzie."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread
msgid "Unread Messages"
msgstr "Nieprzeczytane wiadomości"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Licznik nieprzeczytanych wiadomości"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__sequence
msgid "Used to order the note stages"
msgstr "Stosowane do porządkowania etapów notatek"

#. module: note
#: model:ir.model,name:note.model_res_users
msgid "Users"
msgstr "Użytkownicy"
