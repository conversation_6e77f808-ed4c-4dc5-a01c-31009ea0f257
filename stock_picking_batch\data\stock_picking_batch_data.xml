<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">
    <!-- Batch picking related subtypes for messaging / Chatter -->
    <record id="mt_batch_state" model="mail.message.subtype">
        <field name="name">Stage Changed</field>
        <field name="res_model">stock.picking.batch</field>
        <field name="default" eval="False"/>
        <field name="description">Stage Changed</field>
    </record>

    <record id="seq_picking_batch" model="ir.sequence">
        <field name="name">Batch Transfer</field>
        <field name="code">picking.batch</field>
        <field name="prefix">BATCH/</field>
        <field name="padding">5</field>
        <field name="company_id" eval="False"/>
    </record>

    <record id="seq_picking_wave" model="ir.sequence">
        <field name="name">Wave Transfer</field>
        <field name="code">picking.wave</field>
        <field name="prefix">WAVE/</field>
        <field name="padding">5</field>
        <field name="company_id" eval="False"/>
    </record>
</data></odoo>
