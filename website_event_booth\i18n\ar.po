# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                    <span>Sorry, several booths are now sold out. Please change your choices before validating again.</span>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                    <span>عذراً، لقد بيعت العديد من الأجنحة بالفعل. يرجى تغيير خياراتك قبل التصديق مجدداً.</span> "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event booths\"/><em>Configure Booths</em>"
msgstr ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"تهيئة أجنحة الفعالية \"/><em>تهيئة الأجنحة</em> "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span class=\"text-nowrap\">Sold Out</span>"
msgstr "<span class=\"text-nowrap\">نفدت الكمية</span> "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span>Book my Booths</span>"
msgstr "<span>حجز أجنحتي</span> "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>البريد الإكتروني</span>\n"
"                            <span> *</span> "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>الاسم</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid ""
"<span>This event is finished. It's no longer possible to book a "
"booth.</span>"
msgstr "<span>هذه الفعالية منتهية. لم يعد من الممكن حجز جناح.</span> "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "<strong>Contact Details</strong>"
msgstr "<strong>تفاصيل التواصل</strong> "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Book my Booths"
msgstr "حجز أجنحتي "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_type_view_form
msgid "Booth Menu Item"
msgstr "عنصر قائمة الجناح "

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu
msgid "Booth Register"
msgstr "التسجيل للجناح "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_complete
msgid "Booth Registration completed!"
msgstr "تم تسجيل الجناح! "

#. module: website_event_booth
#: code:addons/website_event_booth/controllers/event_booth.py:0
#, python-format
msgid "Booth registration failed. Please try again."
msgstr "فشل تسجيل الجناح. يرجى المحاولة مجدداً. "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Booths"
msgstr "الأجنحة "

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_type__booth_menu
msgid "Booths on Website"
msgstr "الأجنحة في الموقع الإلكتروني "

#. module: website_event_booth
#: code:addons/website_event_booth/controllers/event_booth.py:0
#, python-format
msgid "Booths should belong to the same category."
msgstr "يجب أن تنتمي الأجنحة إلى نفس الفئة. "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Contact Us"
msgstr "تواصل معنا"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_event
msgid "Event"
msgstr "الفعالية"

#. module: website_event_booth
#: model:ir.model.fields.selection,name:website_event_booth.selection__website_event_menu__menu_type__booth
msgid "Event Booth Menus"
msgstr "قوائم أجنحة الفعالية "

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu_ids
msgid "Event Booths Menus"
msgstr "قوائم أجنحة الفعالية "

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_type
msgid "Event Template"
msgstr "قالب الفعالية "

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__exhibition_map
msgid "Exhibition Map"
msgstr "خريطة المعرض "

#. module: website_event_booth
#: code:addons/website_event_booth/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
#, python-format
msgid "Get A Booth"
msgstr "احصل على جناح "

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "نوع القائمة "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Mobile"
msgstr "الهاتف المحمول"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Phone"
msgstr "رقم الهاتف"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Sorry, all the booths are sold out."
msgstr "عذراً، لقد بيعت كافة الأجنحة. "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "This event is not open to exhibitors registration,"
msgstr "هذه الفعالية غير مفتوحة لتسجيل العارضين، "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "View Floor Plan"
msgstr "عرض مخطط الطابق "

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_website_event_menu
msgid "Website Event Menu"
msgstr "قائمة فعالية الموقع الإلكتروني "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "check our"
msgstr "ألقِ نظرة على "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "for this event."
msgstr "لهذه الفعالية. "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "if you have any question."
msgstr "إذا كانت لديك أي أسئلة. "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "list of future events"
msgstr "قائمة الفعاليات المستقبلية "

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "you can"
msgstr "يمكنك "
