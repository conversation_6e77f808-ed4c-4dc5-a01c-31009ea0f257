# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-06-10 12:31+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/odoo/odoo-9/language/"
"mk/)\n"
"Language: mk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  mk.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"
"#-#-#-#-#  mk.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "&amp;times;"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.sitemap_index_xml
#: model_terms:ir.ui.view,arch_db:website.sitemap_xml
msgid "&lt;?xml version=\"1.0\" encoding=\"UTF-8\"?&gt;"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:17
#: code:addons/website/static/src/xml/website.gallery.xml:97
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ""
",\n"
"                                updated:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid ""
",\n"
"                the #1"
msgstr ""
",\n"
"                 #1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", автор:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", updated:"
msgstr ", ажурирано:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "- The Odoo Team"
msgstr "- Odoo тимот"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"1. Add your <strong>desired languages</strong><br/>\n"
"                                Use the link in the footer of your website "
"(when logged in) to add\n"
"                                languages from the available list. Also, you "
"can change the\n"
"                                default language in"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:17
#, python-format
msgid "1. Define Keywords"
msgstr "1. Дефинирајте клучни зборови"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "10s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "12"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "1s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "2. <strong>Setup Gengo</strong><br/>"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:45
#, python-format
msgid "2. Reference Your Page"
msgstr "2. Референца за вашата страна"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "2s"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:62
#, python-format
msgid "3. Preview"
msgstr "3. Преглед"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "3s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
msgid "403: Forbidden"
msgstr "403: Забрането"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.404
msgid "404: Page not found!"
msgstr "404: Страната не е пронајдена!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "5s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "<b>A Small Subtitle</b>"
msgstr "<b>Мал превод...</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<b>Choose an image...</b>"
msgstr "<b>Одберете слика...</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"<b>Great stories are for everyone even when only written for\n"
"                        just one person.</b> If you try to write with a wide "
"general\n"
"                        audience in mind, your story will ring false and be "
"bland.\n"
"                        No one will be interested. Write for one person. If "
"it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"<b> Одлични приказни се за секого, дури и кога се пишувани само за \n"
"одредена личност. </b> ако пробате да пишувате со имајќи голема публика на\n"
" ум, вашата приказна нема да ви биде толку успешна.\n"
"Пишувајте за една личност. Ако е пишана за еден тогаш е пишана за сите."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
#, fuzzy
msgid ""
"<b>Great stories have personality.</b> Consider telling\n"
"                        a great story that provides personality. Writing a "
"story\n"
"                        with personality for potential clients will asists "
"with\n"
"                        making a relationship connection. This shows up in "
"small\n"
"                        quirks like word choices or phrases. Write from your "
"point\n"
"                        of view, not from someone else's experience."
msgstr ""
"<b>Одличните приказни имаат карактер.</b> Земете го ова во предвид кога ја "
"пишувате Вашата приказна. Пишувајќи приказна\n"
"со карактер за потенцијалните клиенти ќе ви помогне во \n"
"создавањето на подобрена односна врска.\n"
" Ова се однесува на малите делови како што се изборот на зборови или фрази.\n"
"Пишувајте од Ваша сопствена гледна точка а не од нечие туѓо искуство."

#. module: website
#: model_terms:ir.actions.act_window,help:website.action_module_theme
msgid "<b>No theme module found!</b>"
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.action_module_website
msgid "<b>No website module found!</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_sign_in
msgid "<b>Sign in</b>"
msgstr "<b>Најави се</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_button
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Contact Us Now"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-check-square\"/> Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
#, fuzzy
msgid "<i class=\"fa fa-envelope-o\"/> Email Our Website Expert"
msgstr "<span class=\"fa fa-comment-o\"/> Чат во живо на вебстрана на"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-indent\"/> Content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-magic icon-fix\"/> Effect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"                Add a language..."
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"                Додајте јазик..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-th-large\"/> Structure"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<i class=\"fa fa-th-large\"/> WEBSITE <b class=\"caret\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>\n"
"                        The whole process may take a few hours, some "
"discussions with your colleagues and\n"
"                    several cups of coffee to go through. But don't worry, "
"you can return to this tool at any time.</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>\n"
"                        Whether you're a beginner or a pro, we have "
"everything you need to plan, create,\n"
"                    publish and grow your site, blog or online store.</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>\n"
"                    This Planner will help you to think about your website "
"and provide tips and ideas to inspire you. There are examples and "
"explanations to guide you through the process of creating a top-notch, high "
"quality site that meets all your needs.</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>Congratulations on taking the leap and deciding to build your own website!"
"</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<i>We wish you a fun time!</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_floating
msgid ""
"<small class=\"text-muted\">A great way to catch your reader's attention is "
"to tell a story. Everything you consider writing can be told as a story.</"
"small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
#: model_terms:ir.ui.view,arch_db:website.s_quote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_slider
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "<small>Author of this quote</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
#: model_terms:ir.ui.view,arch_db:website.s_quotes_slider
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "<small>John Doe, CEO</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install now"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> Инсталирај сега"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span class=\"fa fa-comment-o\"/> Website Live Chat on"
msgstr "<span class=\"fa fa-comment-o\"/> Чат во живо на вебстрана на"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                                <strong>Tips for a good domain:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        <span>Try to limit yourself to 1 testing variable, "
"otherwise you won't be able to clearly interpret the results of your "
"modifications.</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "<span class=\"fa fa-pencil\"/> EDIT PAGE"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> Инсталирај сега"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-plus\"/> NEW PAGE"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span class=\"fa fa-thumbs-o-down\"/> <strong> Bad practices</strong>"
msgstr "<span class=\"fa fa-thumbs-o-down\"/> <strong> Лоши вежби</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span class=\"fa fa-thumbs-o-up\"/> <strong> Good practices</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-cogs\"/>\n"
"                                        <strong>Machine translation</"
"strong><br/>Free, but quality may vary\n"
"                                    </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-user\"/>\n"
"                                        <strong>Human translation</"
"strong><br/>Pay for professionnal quality\n"
"                                    </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-code\"/><strong> 2. "
"Customize its appearance</strong></span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-magic\"/><strong> 1. Choose "
"your theme</strong></span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid ""
"<span class=\"sr-only\">Toggle navigation</span>\n"
"                                    <span class=\"icon-bar\"/>\n"
"                                    <span class=\"icon-bar\"/>\n"
"                                    <span class=\"icon-bar\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<span>$</span><b style=\"font-size: 60px\">125</b><small>.00</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<span>$</span><b style=\"font-size: 60px\">35</b><small>.00</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<span>$</span><b style=\"font-size: 60px\">65</b><small>.00</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span>3. Select a <strong>translation method</strong></span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>... and your location too!</strong><br/>\n"
"                                Millions of people use Google Maps everyday. "
"Whether you are a restaurant\n"
"                                or a huge business, you have no excuse not "
"to register your location in"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>1. Define what to test</strong><br/>\n"
"                                Your choice of what to test will obviously "
"depend on your goals, but should\n"
"                                always be linked to one of your business "
"objectives (don't spend energy\n"
"                                improving something that brings you no ROI). "
"Then, find the best page to\n"
"                                test and the variation you think will bring "
"the best results.Here are a\n"
"                                few examples of good testing variables:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>1. Your main menu items</strong> and 2. Your secondary menu items "
"(optional):"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>2. Create the page variation</strong><br/>\n"
"                                Once you're clear on the measurable "
"objective, the page and the variable\n"
"                                you want to test, duplicate the page in Odoo "
"using the <strong>'Versions'</strong>\n"
"                                menu and apply the modification you decided."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>3. Configure a Google Analytics account</strong><br/>\n"
"                                This is necessary in order to record the "
"statistics of your test."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>3. Your footer titles</strong> and 4. Your footer links:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>4. Create your campaign in Google Analytics</strong><br/>\n"
"                                Simply follow the wizard by creating a new "
"experiment in the <strong>Behavior &gt;\n"
"                                Experiments</strong> menu of"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_button
msgid "<strong>50,000+ companies run Odoo to grow their businesses.</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Activate now</strong> the optional modules you'll need:"
msgstr "<strong>Активирај сега</strong> споредните модули кои што ви требаат:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Add features and content</strong><br/>\n"
"                                You can put more content in the intermediate "
"section of your homepage (one\n"
"                                or two scrolls down from the visible "
"section), but try to keep your\n"
"                                paragraphs easy to read and avoid 'walls of "
"text'."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Add relevant keywords</strong><br/>\n"
"                                Adding relevant keywords to a web page's "
"meta data, including the title tag\n"
"                                and meta description, will tend to improve "
"the relevancy of a site's search\n"
"                                listings, also increasing your traffic."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Advertise to get the first visitors</strong><br/>\n"
"                                If you have a small budget to allocate to "
"advertising (around $100), you\n"
"                                should start a Google Adwords campaign."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Be announced on the Odoo Twitter account</strong><br/>\n"
"                                We like to hear from people using Odoo to "
"build their website. Tweet us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Be linked by trusted websites</strong><br/>\n"
"                                The more sites that link to your own site, "
"the more Google trusts your site\n"
"                                to be worthwhile:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Blogs</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Community builder</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Contact form</strong>"
msgstr "<strong>Контакт форма</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Contact us for a custom-made theme:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Contact us now:</strong><br/>"
msgstr "<strong>Контактирајте нè сега:</strong><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Create an online Q&amp;A</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Discuss live</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Display social proof</strong><br/>\n"
"                                Below your main banner or call-to-action, "
"it's a good practice to put\n"
"                                social proofs about your products or "
"services; customer quotes, videos,\n"
"                                photos of your products in actions, twitter "
"quotes, etc."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Do many A/B tests.</strong><br/>\n"
"                                Chances are, your first A/B test will turn "
"out a lemon. But don’t despair. An A/B test can have only three outcomes: no "
"result, a negative result or a\n"
"                                positive result. The key to optimizing "
"conversion rates is to do a ton of A/B\n"
"                                tests, so that all positive results add up "
"to a huge boost to your sales and\n"
"                                achieved goals."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Don't test versions at different time periods.</strong><br/>If you\n"
"                                test one version one week and the second the "
"next, your results won't be\n"
"                                accurate, or worse, wrong!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Don’t conclude too early.</strong><br/>\n"
"                                Wait for your test results to be "
"significant. Take a look at the statistical\n"
"                                confidence in Google Analytics, it should be "
"at least 95%."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Don’t surprise regular visitors.</strong><br/>\n"
"                                If you are testing a core part of your "
"website, include only new visitors in\n"
"                                the test. You want to avoid shocking regular "
"visitors, especially because the\n"
"                                variations may not ultimately be implemented."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Encourage to action</strong><br/>\n"
"                                Add  one, and only one, call-to-action on "
"your homepage. Your homepage is\n"
"                                the page that gets the most visitors.<br/>\n"
"                                It's good practice to try to engage with "
"your visitors on the homepage:\n"
"                                Contact Us, Subscribe to Get More "
"Information, Check the Catalog, etc."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "<strong>Error message:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Find 3 websites that you like</strong> and write down what you like "
"about them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Find a catchy headline</strong><br/>\n"
"                                Make sure anyone who visits your site "
"understands what it’s about without having to do a lot of reading.<br/>\n"
"                                To do that, the visible section of your "
"homepage (above the fold) needs to be simple and straightforward. For "
"example, use pictures of your work, screenshots of your app, a short and "
"catchy hook."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>How to get a custom domain name ?</strong><br/>"
msgstr "<strong>Како да добиете костумизирано име на домен ?</strong><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Initial Teaser</strong><br/>\n"
"                                Before launching to the masses, it's good "
"practice to tease around 100 of\n"
"                                your contacts. Start with your friends, "
"colleagues and family. Whether or\n"
"                                not they're interested in your area of "
"business, they will be ready to help\n"
"                                you with feedback if you engage in a "
"personal discussion with them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Jobs</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Know how long to run a test before giving up.</strong><br/>\n"
"                                Giving up too early can cost you because you "
"may have gotten meaningful\n"
"                                results had you waited a little longer. "
"Giving up too late isn’t good either,\n"
"                                because poorly performing variations could "
"cost you conversions and sales."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Now try to write down you footer structure:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Optimize your content</strong><br/>\n"
"                                Writing content that includes frequently "
"searched-for keywords and phrases\n"
"                                tends to increase  traffic to your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Organize physical events</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Reference your images...</strong><br/>\n"
"                                If you are an artist or a photographer, you "
"can potentially bring in a lot\n"
"                                of  visitors by referencing your images so "
"they are displayed in image\n"
"                                search engines, like Google Images. To do "
"that, simply add an 'Alt text'\n"
"                                containing descriptions and keywords for "
"relevant pictures."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Start a blog</strong><br/>\n"
"                                Google loves websites which are updated "
"often or ones where new content is\n"
"                                added regularly. This is why starting a blog "
"is an excellent way of\n"
"                                improving your search ranking. If you have "
"the time and dedication to write\n"
"                                good articles,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>The big launch</strong><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Use Google Webmaster</strong><br/>\n"
"                                This tool will help you understand your "
"search traffic and learn how users\n"
"                                are finding your site and make optimizations "
"to help Google better\n"
"                                understand and represent your site."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Use contact forms:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>With that in mind, try to define the structure of your main menu:</"
"strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>You have your domain name ?</strong><br/>"
msgstr "<strong>Го имате вашето име на домен ?</strong><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Your website objectives</strong> by priority:"
msgstr "<strong>Вашите цели на вебстрана</strong> по приоритет:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>e-Commerce</strong>"
msgstr "<strong>е-Комерс</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "@odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid "A Great Headline"
msgstr "Добар наслов"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "A Punchy Headline"
msgstr "Интересен наслов"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "Поднаслов на секција"

#. module: website
#: model:ir.model.fields,help:website.field_ir_act_server_website_published
#, fuzzy
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"Серверска код акција може да се изврши од вебсајтот, преку посветен "
"контролер. Адресата е <base>/website/action/<website_path>. Поставете го ова "
"поле како Вистинито да им дозволите на корисниците да ја стартуваат оваа "
"акција. Ако е подесено на Погрешно акцијата нема да може да се стартува "
"преку вебсајтот."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"A footer, as its name implies, is located at the very bottom of your "
"website.\n"
"                        Because of that, your visitors won't see it "
"immediately so it's very useful for linking\n"
"                        pages you don't want in your main menu e.g. social "
"media, terms and conditions, privacy\n"
"                        terms, etc."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid "A good subtitle"
msgstr "Добар поднаслов"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"A good way to start is to use machine translation then apply corrections "
"yourself."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"A great way to catch your reader's attention is to tell a story.\n"
"                        Everything you consider writing can be told as a "
"story."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great<br/>feature, in clear words."
msgstr "Мало објаснување на оваа <br/> функција, во чист текст."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "A/B Testing"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_module_website_version
msgid "A/B testing and versioning"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"A/B testing is the process of testing two versions of a page to see which "
"one performs\n"
"                        better in reaching your business objectives. For "
"example, imagine you have an existing\n"
"                        page (A) where you ask your customers to subscribe "
"to your mailing list.\n"
"                        You create a variation (B) of this page with a "
"slightly different subscription message.\n"
"                        Then your randomly display this page to half the "
"visitors of your website, and after a\n"
"                        while, you analyze the subscription statistics and "
"see that page B performs 10% better\n"
"                        than page A. Of course, you now decide to make B "
"your new current page!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid "About us"
msgstr "За нас"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cdn_activated
msgid "Activate CDN for assets"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit you design need.\n"
"                        To duplicate, delete or move columns, select the\n"
"                        column and use the top icons to perform your action."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:37
#, python-format
msgid "Add"
msgstr "Додади"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.snippets.gallery.js:248
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#, python-format
msgid "Add Images from the 'Customize' menu"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:55
#, python-format
msgid "Add Menu Entry"
msgstr "Додајте внес во мени"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Slide"
msgstr "Додадете слајд"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Add a great slogan"
msgstr "Додадете добар слоган"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:18
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Add images"
msgstr "Додај слики"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:20
#, python-format
msgid "Add keyword:"
msgstr "Додади клучен збор:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:111
#, fuzzy, python-format
msgid "Add new pages"
msgstr "Додадете нови страни и мениа"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:68
#, python-format
msgid "Add page in menu"
msgstr "Додади страна во менито"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Advanced"
msgstr "Напредно"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"After working on this step, you may want to go back and refine your "
"objectives or modify your visitors interests. Those 3 first steps are "
"essentially linked, and the more time your spend on polishing them, the "
"better your website will be!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Aim for a .com and/or your country extension"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid ""
"All these icons are licensed under creative commons so that you can use them."
msgstr ""
"Сите овие икони се лиценцирани под креативно заедништво за да можете да ги "
"користите."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Allows your customers and the whole community to ask and answer questions "
"about their interests."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Also, don't hesitate to post on other people blogs, forums, websites, but "
"don't go overboard and post everywhere without considering whether it's a "
"good place for you to be."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Alternatively, you can write a good article about your business and publish "
"it on an article database websites (ex: squidoo.com) or send it to a press "
"website and your local newspapers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Amount of text on the page (short vs. long)."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "An error occured while rendering the template"
msgstr "Се случи грешка при рендерирање на урнек"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "And a great subtitle too"
msgstr "И добар поднаслов иссто така"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Announce it on social media (and update your profiles): Twitter,Facebook, "
"LinkedIn, Youtube, etc."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Apply"
msgstr "Примени"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Aqua"
msgstr "Аква"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"As you add more pages to your website, your main menu will begin to feel "
"overcrowded.\n"
"                            You can regroup similar pages into a sub-menu, "
"so they will appear under a unique drop-down menu."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment_website_url
msgid "Attachment URL"
msgstr "Адреса за прикачување"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Attract more leads with a winning content marketing strategy."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Attract new leads"
msgstr "Привлечи нови траги"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_act_server_website_published
msgid "Available on the Website"
msgstr "Достапно на вебсајтот"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Avoid special characters"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Baby Blue"
msgstr "Бебешко сина"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Back"
msgstr "Назад"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, fuzzy
msgid "Background Color"
msgstr "Позадина"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, fuzzy
msgid "Background Image"
msgstr "Позадина"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Background Image Options"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Background image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Banner Odoo Image"
msgstr "Слика за Odoo банер"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:70
#, python-format
msgid "Be careful !"
msgstr "Бидете претпазливи"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Be mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Before starting to work on your website, make sure you have all the right "
"applications installed.\n"
"                        It will be much easier for you to organize and "
"create your content if you have all the options activated from the start."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Before starting,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "Почетник"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Being clear on why you're creating your site and your goals is an essential "
"first step to ensure your content and structure meet business objectives."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bigger Text"
msgstr "Поголем текст"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Black"
msgstr "Црно"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Boost your online sales with sleek product pages."
msgstr "Подобрете ги вашите онлајн продажби со штосните страни на производите."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:14
#, python-format
msgid "Build a page"
msgstr "Изгради страна"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Build and engage with communities with forums, Q&amp;A, mailing lists."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Build awareness and credibility"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Business Guy"
msgstr "Бизнисмен"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cdn_url
#: model:ir.model.fields,field_description:website.field_website_config_settings_cdn_url
msgid "CDN Base URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cdn_filters
#: model:ir.model.fields,field_description:website.field_website_config_settings_cdn_filters
msgid "CDN Filters"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Can I afford to advertise on the keyword?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Can I use it to manage projects based on agile methodologies?"
msgstr ""
"Може ли да го користам за да управувам со проекти базирани на agile "
"методологии?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:63
#: model_terms:ir.ui.view,arch_db:website.500
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "Cancel"
msgstr "Откажи"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Change Background"
msgstr "Промени позадина"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Change Icons"
msgstr "Промени икони"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:96
#, python-format
msgid "Check Mobile Preview"
msgstr "Провери преглед од мобилен"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Check its availability"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Check your references before deciding to work with you"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_child_id
msgid "Child Menus"
msgstr "Подмениа"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph\n"
"                        about it. It does not have to be long, but it "
"should\n"
"                        reinforce your image."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Circle"
msgstr "Круг"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:43
#, python-format
msgid "Click in the text and start editing it."
msgstr "Кликнете на текстот и почнете со уредување."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Click on 'Translate' on the menu bar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Click on the icon to adapt it to your feature"
msgstr "Кликнете на иконката за да ја присвоите за вашата функција"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:49
#: model_terms:ir.ui.view,arch_db:website.s_banner
#: model_terms:ir.ui.view,arch_db:website.website2_homepage
#, python-format
msgid "Click to customize this text"
msgstr "Кликнете да го персонализирате овој текст"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.ace.xml:14
#: code:addons/website/static/src/xml/website.gallery.xml:97
#: code:addons/website/static/src/xml/website.translator.xml:13
#, python-format
msgid "Close"
msgstr "Затвори"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:113
#, python-format
msgid "Close Tutorial"
msgstr "Затвори прирачник"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color Splash"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Colors palette"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Communicate"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_company_id
msgid "Company"
msgstr "Компанија"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Company name"
msgstr "Име на компанија"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_compress_html
msgid "Compress HTML"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_compress_html
msgid "Compress rendered HTML for a better Google PageSpeed result"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "Конфигурација"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Configure Website"
msgstr "Конфигурирај веб сајт"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Configure all your social media (Twitter, Facebook, LinkedIn) so that the "
"address of your website appears on them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Configure website menus"
msgstr "Конфигурирај мениа на веб сајт"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Configure your Gengo API key in"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Congratulations, you're done !"
msgstr "Честитки, завршивте !"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid "Connect with us"
msgstr "Поврзи се со нас"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403 model_terms:ir.ui.view,arch_db:website.404
msgid "Contact Us"
msgstr "Контактирајте не"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Contact a Domain Manager in your country to buy it"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_default
#: model_terms:ir.ui.view,arch_db:website.s_banner
#: model_terms:ir.ui.view,arch_db:website.s_big_message
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.website2_homepage
#: model_terms:ir.ui.view,arch_db:website.website_planner
#: model:website.menu,name:website.menu_contactus
#: model:website.menu,name:website.website2_menu_contactus
msgid "Contact us"
msgstr "Контактирајте не"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "Contact us about anything related to our company or services."
msgstr "Контактирајте не за било што поврзано со нашата компанија или услуги."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "Contact us »"
msgstr "Контактирајте нé »"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "Content"
msgstr "Продолжи"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:24
#, python-format
msgid "Content to translate"
msgstr "Содржина да се преведе"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:61
#: code:addons/website/static/src/js/website.tour.banner.js:83
#: code:addons/website/static/src/js/website.tour.banner.js:105
#: code:addons/website/static/src/xml/website.contentMenu.xml:28
#: code:addons/website/static/src/xml/website.xml:62
#, python-format
msgid "Continue"
msgstr "Продолжи"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Copyright &amp;copy;"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "Креирај страна"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Create a community"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Create a new project (this may take some time)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.editor.js:133
#, python-format
msgid "Create page '%s'"
msgstr "Креирај страна '%s'"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_create_uid
#: model:ir.model.fields,field_description:website.field_website_create_uid
#: model:ir.model.fields,field_description:website.field_website_menu_create_uid
msgid "Created by"
msgstr "Креирано од"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_create_date
#: model:ir.model.fields,field_description:website.field_website_create_date
#: model:ir.model.fields,field_description:website.field_website_menu_create_date
msgid "Created on"
msgstr "Креирано на"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Creating your main menu is an important and difficult stage in building your "
"website.<br/>\n"
"                        It needs to be organized, so that your visitors can "
"see all the main content you offer at a glance.<br/>\n"
"                        It needs to be clear, so that when they click on an "
"item, they get the content they expected.<br/>\n"
"                        In fact, you need to build your navigation menu for "
"your visitors and not for your own purposes or objectives."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "Customize"
msgstr "Персонализирајте го банерот"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize Theme"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:60
#, python-format
msgid ""
"Customize any block through this menu. Try to change the background of the "
"banner."
msgstr ""
"Костумизирај било кој прозор во ова мени. Пробај да ја смениш позадината на "
"банерот."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:42
#, python-format
msgid "Customize banner's text"
msgstr "Персонализирајте текст на банер"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:59
#, python-format
msgid "Customize the banner"
msgstr "Персонализирајте го банерот"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_default_lang_id
#: model:ir.model.fields,field_description:website.field_website_default_lang_id
msgid "Default language"
msgstr "Стандарден јазик"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_default_lang_code
#: model:ir.model.fields,field_description:website.field_website_default_lang_code
msgid "Default language code"
msgstr "Стандарден јазичен код"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Delete Blocks"
msgstr "Избриши прозори"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:129
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Delete Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture\n"
"                        that illustrates your message. Click on the picture "
"to\n"
"                        change it's <em>rounded corner</em> style."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Deploy new stores with just an internet connection: no\n"
"                                installation, no specific hardware required. "
"It works with any\n"
"                                iPad, Tablet PC, laptop or industrial POS "
"machine."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:54
#, python-format
msgid "Description"
msgstr "Опис"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:109
#, python-format
msgid "Description..."
msgstr "Опис..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Design"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disable autoplay"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:364
#, python-format
msgid "Discard"
msgstr "Отфрли"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Discuss and Comments"
msgstr "Дискусии и коментари"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Discuss with propects for a face-to-face sales pitch."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_display_name
#: model:ir.model.fields,field_description:website.field_website_display_name
#: model:ir.model.fields,field_description:website.field_website_menu_display_name
#: model:ir.model.fields,field_description:website.field_website_published_mixin_display_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_display_name
msgid "Display Name"
msgstr "Прикажи име"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:43
#, python-format
msgid "Do not show this dialog later."
msgstr "Не го покажувај овој дијалог подоцна."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Does it works offline?"
msgstr "Дали работи без интернет?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Domain"
msgstr "Домен"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Don't hesitate to"
msgstr "Не воздржувајте се да"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Download the technical documentation of a product"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:67
#, python-format
msgid "Drag & Drop This Block"
msgstr "Влечи и постави го овој прозор"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:34
#, python-format
msgid "Drag & Drop a Banner"
msgstr "Повлечи и пушти банер"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:50
#, python-format
msgid "Drag a menu to the right to create a sub-menu"
msgstr "Извлечете го менито на десно за да креирате подмени"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:68
#, python-format
msgid "Drag the <em>'Features'</em> block and drop it below the banner."
msgstr ""
"Влечeте го <em>'Карактеристики'</em> прозорот и поставете го под банерот."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:35
#, python-format
msgid "Drag the Banner block and drop it in your page."
msgstr "Влечете го банер прозорот и поставете го на вашата страна."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Duplicate"
msgstr "Дуплирај"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Duplicate blocks to add more features."
msgstr "Дуплицирајте прозори за додавање на повеќе карактеристики."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Easy to remember and spell"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit"
msgstr "Уреди"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:38
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr "Уреди го менито"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, fuzzy
msgid "Edit Top Menu"
msgstr "Уреди го менито"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit in backend"
msgstr "Ажурирај во backend"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"Edit the content below this line to adapt the default \"page not found\" "
"page."
msgstr ""
"Уредете ја содржината подолу за да ја промените стандардната \"страната не е "
"пронајдена\" страна."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:27
#, python-format
msgid "Edit this page"
msgstr "Уреди на оваа страна"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "End"
msgstr "Крај"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Enterprise package"
msgstr "Корпоративен пакет"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "Error"
msgstr "Грешка"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:28
#, python-format
msgid ""
"Every page of your website can be modified through the <i>Edit</i> button."
msgstr ""
"Секоја страна од вашиот вебсајт може да биде модифицирана преку <i>Уреди</i> "
"копчето."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Example of Good Footer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Example of Good Homepage"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Example of Good Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Examples"
msgstr "Примери"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "Експерт"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_well
msgid ""
"Explain the benefits you offer. Don't write about products or\n"
"            services here, write about solutions."
msgstr ""
"Објаснете ги бенефитите што ги нудите. не пишувајте\n"
"            за производи или услуги овде, пишувајте за решенија."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Explain to them why you launched your new website and ask them for their\n"
"                                    views on it. Avoid using a mass survey, "
"it's a lot more efficient to have\n"
"                                    personal discussions with everyone. If "
"you efficiently engage them in the\n"
"                                    reviewing process, they will even help "
"you promote your website when it's\n"
"                                    ready for the big launch."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Extra Features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_facebook
#: model:ir.model.fields,field_description:website.field_website_social_facebook
msgid "Facebook Account"
msgstr "Фејсбук налог"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fast"
msgstr "Брзо"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "Функција еден"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "Функција три"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_panel
msgid "Feature Title"
msgstr "Наслов на функција"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "Функција два"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Finally don't try to make all your content accessible from the main menu or "
"the footer.\n"
"                            Indeed, a page can also be accessible through a "
"direct link or button, from any other page of your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Find Inspiration"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Find a good domain name (see tips on the right)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "Прва функција"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fixed"
msgstr "Фиксно"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Float"
msgstr "Децимален"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flowers Field"
msgstr "Поле со цвекиња"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Folded list"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Fonts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"For example, use photos to help break up large areas of text by altering\n"
"                                    Text-Image and Image-Text blocks. Or if "
"you prefer to use icons, use the Features or Feature Grid blocks."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"For example, you'll want your company name, your country/city and a few\n"
"                                    words describing your business in your "
"homepage title so that potential\n"
"                                    customers near your location will find "
"your website easily.\n"
"                                    WIth Odoo, this can be done easily by "
"using the Promote menu."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""
"За Odoo тимот,<br/>\n"
"Fabien Pinckaers, основач"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_module_website_form_editor
msgid "Form builder: create and customize forms"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.ace.xml:13
#, python-format
msgid "Format"
msgstr "Формат"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Form’s length and types of fields,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid ""
"From the main container, you can change the background to highlight features."
msgstr ""
"Од главниот контејнер, можете да ја промените позадината за да означите "
"функции."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Gengo module"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "Земи пристап до сите модули"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "Земи пристап до сите модули и нивните карактеристики"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Get an understanding of your methodology"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:52
#, python-format
msgid "Get banner properties"
msgstr "Види својства на банер"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:361
#, python-format
msgid ""
"Get this page efficiently referenced in Google to attract more visitors."
msgstr ""
"Ефикасно регистрирајте ја страната на Google за да привлечете повеќе "
"посетители."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_github
#: model:ir.model.fields,field_description:website.field_website_social_github
msgid "GitHub Account"
msgstr "GitHub Налог"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Go to the page you want to translate"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:81
#, python-format
msgid "Good Job!"
msgstr "Добра работа!"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_google_analytics_key
#: model:ir.model.fields,field_description:website.field_website_google_analytics_key
msgid "Google Analytics Key"
msgstr "Клуч од Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Google Analytics."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Google Business"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Google's developer console"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_googleplus
#: model:ir.model.fields,field_description:website.field_website_social_googleplus
msgid "Google+ Account"
msgstr "Google+ налог"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Great Value"
msgstr "Добра вредност"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
msgid "Great products for great people"
msgstr "Супер производи за супер луѓе"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Greenfields"
msgstr "Greenfields"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grid"
msgstr "Мрежа"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Group By"
msgstr "Групирај по"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Grow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HELP &amp; TUTORIALS"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML Editor"
msgstr "Уредувач на HTML"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP routing"
msgstr "HTTP рутирање"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Headline or product description,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Help"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:21
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr "Ова се визуелните помагала за да преведувате поефикасно:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Here is a basic guide for your first A/B testing:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
#: model_terms:ir.ui.view,arch_db:website.footer_default
#: model:website.menu,name:website.menu_homepage
#: model:website.menu,name:website.website2_menu_homepage
msgid "Home"
msgstr "Дома"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403 model_terms:ir.ui.view,arch_db:website.404
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Homepage"
msgstr "Почетна страна"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website2_homepage
msgid "Homepage 0.0.0.0"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:110
#, python-format
msgid "I'm sure, I want to delete this page definitively"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_id
#: model:ir.model.fields,field_description:website.field_website_id
#: model:ir.model.fields,field_description:website.field_website_menu_id_2055
#: model:ir.model.fields,field_description:website.field_website_published_mixin_id
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_id
msgid "ID"
msgstr "ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset one or more templates to their <strong>factory "
"settings</strong>."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"If you answer yes to these 3 questions, then go ahead and position your "
"website on these keywords."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"If you have a lot of pages in your website, you can create a small top-menu "
"to focus\n"
"                            on the main six pages and make all secondary "
"pages accessible from a footer. You can\n"
"                            have a look at the Odoo.com website, it's a "
"great example of this type of structure."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Images on landing and product pages,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images spacing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Improve your conversion from visitor to customer."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"In the left-side menu of the project, go to <strong>APIs and auth &gt; APIs</"
"strong> and activate <strong>Analytics API</strong> by clicking on the"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"In the pop-up window, simply enter your domain name in the first textbox "
"(the second will complete automatically)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:32
#, python-format
msgid ""
"In this mode, you can only translate texts.  To\n"
"                            change the structure of the page, you must edit "
"the\n"
"                            master page. Each modification on the master "
"page\n"
"                            is automatically applied to all translated\n"
"                            versions."
msgstr ""
"Во овој мод, можете само да преведувате текстови.  Да\n"
"                            ја промените структурата на страната, морате да\n"
"                            ја уредите главната страна. Секоја промена на "
"главната\n"
"                            страна автоматски се применува на сите "
"преведени\n"
"                            верзии."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.ace.xml:10
#, python-format
msgid "Include Asset Bundles"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "Информации за"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Install Apps"
msgstr "Инсталирај апликации"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "Инсталирај јазик"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Install the"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "Инсталирани апликации"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Modules"
msgstr "Инсталирани модули"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Integrate a contact form that automatically creates leads."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Internal Server Error"
msgstr "Внатрешна серверска грешка"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Is the keyword searched for in Google? If there is no search volume,\n"
"                                    then that tells you no one is typing "
"that phrase into Google. There is no\n"
"                                    point in advertising on keywords no one "
"is searching for."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Is the person searching with this keyword likely to buy my product or\n"
"                                    service? Or is the person more likely to "
"be just carrying out research\n"
"                                    with no intention of making a purchase? "
"In other words, what is the\n"
"                                    intent of the keyword? When starting "
"out, you’ll want to advertise on\n"
"                                    what we call “buying intent” keywords - "
"that is, ones where the person\n"
"                                    is clearly looking to buy."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:19
#, fuzzy, python-format
msgid "It might be possible to edit the relevant items or fix the issue in"
msgstr ""
"Можно е да ги уредите релевантните објекти\n"
"                                или да го поправите проблемот во"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"It's difficult to change your theme after creating your website, so take the "
"time to look at all the themes, and be sure that you're happy with your "
"choice!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"It's often much easier not to start with a blank page. Take a look at your "
"competitors\n"
"                        and similar companies around the world. They "
"probably have the same objectives and\n"
"                        visitors that you do. How did they transform that "
"into a website?<br/>\n"
"                        Try to find 3 websites that have remarkable "
"characteristics:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_button
msgid "Join us and make your company a better place."
msgstr "Придружете ни се и направете ја вашата компанија подобро место."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Landscape"
msgstr "Хоризонтално"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Language"
msgstr "Јазик"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:28
#, python-format
msgid "Language:"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_language_ids
#: model:ir.model.fields,field_description:website.field_website_language_ids
msgid "Languages"
msgstr "Јазици"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Large"
msgstr "Големо"

#. module: website
#: model:ir.model.fields,field_description:website.field_website___last_update
#: model:ir.model.fields,field_description:website.field_website_config_settings___last_update
#: model:ir.model.fields,field_description:website.field_website_menu___last_update
#: model:ir.model.fields,field_description:website.field_website_published_mixin___last_update
#: model:ir.model.fields,field_description:website.field_website_seo_metadata___last_update
msgid "Last Modified on"
msgstr "Последна промена на"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_write_uid
#: model:ir.model.fields,field_description:website.field_website_menu_write_uid
#: model:ir.model.fields,field_description:website.field_website_write_uid
msgid "Last Updated by"
msgstr "Последно ажурирање од"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_write_date
#: model:ir.model.fields,field_description:website.field_website_menu_write_date
#: model:ir.model.fields,field_description:website.field_website_write_date
msgid "Last Updated on"
msgstr "Последно ажурирање на"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Launch"
msgstr "Пушти"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Launching your website is an important step.<br/>\n"
"                        Here is a checklist of actions to help you launch a "
"new website efficiently:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Learn what your visitors are thinking, what question they have, what "
"problems they encounter."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "Лево"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:90
#, python-format
msgid "Let's check how your homepage looks like on mobile devices."
msgstr "Да провериме како вашата почетна страница изгледа на мобилни уреди."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_linkedin
#: model:ir.model.fields,field_description:website.field_website_social_linkedin
msgid "LinkedIn Account"
msgstr "LinkedIn налог"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "List of Features"
msgstr "Листа на функции"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:83
#, python-format
msgid "Loading..."
msgstr "Се вчитува..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Logo"
msgstr "Лого"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Logout"
msgstr "Одјави се"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_id
msgid "Main Menu"
msgstr "Главно мени"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mango"
msgstr "Манго"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Margin"
msgstr "Маржа"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Masonry"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403 model_terms:ir.ui.view,arch_db:website.404
msgid "Maybe you were looking for one of these popular pages ?"
msgstr "Можеби баравте една од овие популарни страни."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "Средно"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Meet your existing customers and encourage them to 'repeat buy'."
msgstr ""

#. module: website
#: code:addons/website/models/website.py:320
#: model:ir.model.fields,field_description:website.field_website_menu_name
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#, python-format
msgid "Menu"
msgstr "Мени"

#. module: website
#: code:addons/website/models/website.py:324
#, python-format
msgid "Menu <b>%s</b> seems to have a link to this page !"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Message"
msgstr "Порака"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.mobile.js:42
#, python-format
msgid "Mobile preview"
msgstr "Преглед на мобилен"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mode"
msgstr "Мод"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "More info on pricing:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "More than 500 happy customers."
msgstr "Повеќе од 500 среќни клиенти."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:41
#, python-format
msgid "Most searched topics related to your keywords, ordered by importance:"
msgstr ""
"Најпребарувани теми поврзани со вашите клучни зборови, наредени по важност:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mountains"
msgstr "Планини"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to first"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to last"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to next"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to previous"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "My Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "Накосено"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:125
#, python-format
msgid "New Name"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:60
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "New Page"
msgstr "Нова страна"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_new_window
msgid "New Window"
msgstr "Нов прозорец"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:123
#, python-format
msgid "New name"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.editor.js:118
#, python-format
msgid "New or existing page"
msgstr "Нова или постоечка страна"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
#: model_terms:ir.ui.view,arch_db:website.pager
msgid "Next"
msgstr "Следно"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "Нема поддршка"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "None"
msgstr "Ништо"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.backend.js:22
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Not Published"
msgstr "Необјавено"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr ""
"Забелешка: За да ја сокриете оваа страна, одштиклирајте ја од главното "
"Персонализирај мени."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Note: To use those features, you need to install the"
msgstr "Забелешка: За да ги користите тие својства, треба да инсталирате"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Odoo"
msgstr "Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 1 for three columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 2 for three columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 3 for three columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "Odoo CMS - a big picture"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_floating
msgid "Odoo CMS- Sample image floating"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Odoo верзија"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Odoo image and text block"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
msgid ""
"Odoo provides essential platform for our project management.\n"
"                                                Things are better organized "
"and more visible with it."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_slider
msgid ""
"Odoo provides essential platform for our project management.\n"
"                                        Things are better organized and more "
"visible with it."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid ""
"Odoo provides essential platform for our project management.\n"
"                            Things are better organized and more visible "
"with it."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Odoo text and image block"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Odoo's Google Analytics settings"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Odoo's POS is a web application that can run on any device that\n"
"                                can display websites with little to no setup "
"required."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Off"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:46
#, python-format
msgid "Ok"
msgstr "Во ред"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:5
#, python-format
msgid "On Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Once you’ve launched your website, it’s time to grow your traffic."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "Open Source ERP"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Open Source eCommerce"
msgstr "Отворен код Е-Комерс"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "Optimization"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Optimize AdWords Campaign"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Optimize SEO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Optimize your AdWords account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Orange Red"
msgstr "Портокалово црвено"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "Нарачај веднаш"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Organize your job board and promote your job announces easily."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Other Info"
msgstr "Други информации"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Our Offers"
msgstr "Наши понуди"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid "Our Products &amp; Services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr "Наши препораки"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
msgid "Our Team"
msgstr "Нашиот тим"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
msgid ""
"Our products are designed for small to medium size companies willing to "
"optimize\n"
"                                      their performance."
msgstr ""
"Нашите продукти се дизајнирани за мали и средни претпријатија кои сакаат\n"
"                                      да ја оптимизираат нивната изведба."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid ""
"Our products are designed for small to medium size companies willing to "
"optimize\n"
"                            their performance."
msgstr ""
"Нашите производи се дизајнирани за мали и средни претпријатија кои сакаат\n"
"                            да ја оптимизираат нивната изведба."

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:298
#: code:addons/website/static/src/xml/website.editor.xml:11
#, python-format
msgid "Page"
msgstr "Страница"

#. module: website
#: code:addons/website/models/website.py:303
#, python-format
msgid "Page <b>%s</b> seems to have a link to this page !"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:61
#, python-format
msgid "Page Title"
msgstr "Наслов на страница"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:103
#, fuzzy, python-format
msgid "Pages and menus"
msgstr "Додадете нови страни и мениа"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_panel
msgid ""
"Panels are a great tool to compare offers or to emphasize on\n"
"                key features. To compare products, use the inside columns."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_parent_left
msgid "Parent Left"
msgstr "Лев надреден елемент"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_parent_id
msgid "Parent Menu"
msgstr "Над-мени"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_parent_right
msgid "Parent Right"
msgstr "Десен надреден елемент"

#. module: website
#: model:ir.model,name:website.model_res_partner
msgid "Partner"
msgstr "Партнер"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_post
msgid "Partner Detail"
msgstr "Детали за партнер"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Partners"
msgstr "Партнери"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Plan"
msgstr ""

#. module: website
#: model:web.planner,tooltip_planner:website.planner_website
msgid ""
"Plan your website strategy, design your pages, sell your products and grow "
"your online business!"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_web_planner
msgid "Planner"
msgstr "Планер"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Please install a theme in order to customize your website."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.theme.js:277
#, python-format
msgid "Please install or update node-less"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Please note that you'll need an Odoo Standard or Business subscriptions for "
"that"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Point of Sale Questions <small>v7</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Powered by"
msgstr "Овозможено од"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
#: model_terms:ir.ui.view,arch_db:website.pager
msgid "Prev"
msgstr "Претходно"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Product pricing and promotional offers,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "Професионално"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:5
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_planner
#, python-format
msgid "Promote"
msgstr "Промовирај"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:360
#, python-format
msgid "Promote This Page"
msgstr "Промовирај ја оваа страна"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:5
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Promote page on the web"
msgstr "Промовирај ја страната на интернет"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Promote your catalog of services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Provide fast, professional and accurate information to your visitors and "
"customers."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_partner_id
msgid "Public Partner"
msgstr "Јавен партнер"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_user_id
msgid "Public User"
msgstr "Јавен корисник"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:76
#, python-format
msgid "Publish your page by clicking on the <em>'Save'</em> button."
msgstr "Објавете ја вашата страна со кликање на <em>'Сними'</em> копчето."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.backend.js:18
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Published"
msgstr "Објавен"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Purple"
msgstr "Виолетов"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "QWeb"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Quote"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Re-order"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Ready For Launch!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Real professional translators will translate all your contents"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Recipient"
msgstr "Примач"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Reduce the time and resources spent on support."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:96
#, python-format
msgid "Reference(s) found:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr "Одстрани слајд"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove all images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove from gallery"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:95
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Rename Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Reset selected templates"
msgstr "Ресетирај ги избраните урнеци"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Reset templates"
msgstr "Ресетирај урнеци"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded corners"
msgstr "Заоблени ќошеви"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Sample images"
msgstr "Пример слики"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.seo.js:363
#: code:addons/website/static/src/xml/website.ace.xml:12
#, python-format
msgid "Save"
msgstr "Зачувај"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:75
#, python-format
msgid "Save your modifications"
msgstr "Снимете ги вашите промени"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Speed"
msgstr "Брзина на движење"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:97
#, python-format
msgid "Scroll to check rendering and then close the mobile preview."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "Пребарај..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "Втора функција"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Second List"
msgstr "Втора листа"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "See and buy your products"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:27
#, python-format
msgid "Select a Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Select and delete blocks to remove some features."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:53
#, python-format
msgid "Select the parent container to get the global options of the banner."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Select the untranslated language"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_message
msgid "Sell Online. Easily."
msgstr "Продавајте преку интернет. Лесно."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Sell more online"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Send <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Send a Message to our Partners"
msgstr "Испратете порака на нашите партнери"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Send an email to all your contacts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "Send us an email"
msgstr "Испратете ни е-маил"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_sequence
msgid "Sequence"
msgstr "Секвенца"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Settings &gt; Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shadows"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share
msgid "Share"
msgstr "Сподели"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Share experience on similar projects"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view_customize_show
msgid "Show As Optional Inherit"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Simple and obvious"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:21
#, python-format
msgid "Skip It"
msgstr "Прескокни"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideshow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideshow speed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slow"
msgstr "Бавно"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Small"
msgstr "Мало"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Social Media"
msgstr "Социјална Медија"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:70
#, python-format
msgid "Some dependencies can exist ..."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Special effects and animations"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Square"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:21
#, python-format
msgid "Start Tutorial"
msgstr "Активирај го прирачникот"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Start by looking how often people search phrases related to your business,\n"
"                                    how competitive the keywords are in "
"AdWords, and how much it’ll cost to\n"
"                                    advertise on each keyword. All of this "
"information will  help you determine\n"
"                                    which keywords you want to use in your "
"first campaign."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Start with the customer – find out what they want\n"
"                        and give it to them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Starter package"
msgstr "Стартен пакет"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Static"
msgstr "Статичко"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Still in the left-side menu, go to <strong>APIs and auth &gt; Credentials</"
"strong> and click on <strong>'Create New Client ID'</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Style"
msgstr "Стил"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Styling"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Subtitle"
msgstr "Поднаслов"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Subtitle 2"
msgstr "Поднаслов 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Subtitle 3"
msgstr "Поднаслов 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sunflower"
msgstr "Сончоглед"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "Техничко име:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid ""
"Tell features the visitor would like to know, not what you'd like to say."
msgstr ""
"Кажете особини што посетителот би сакал да ги знае, не тие што вие сакате да "
"ги кажете."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the<br/>customer for this feature."
msgstr ""

#. module: website
#: code:addons/website/models/website.py:308
#, python-format
msgid "Template <b>%s (id:%s)</b> seems to have a link to this page !"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.ace.js:246
#, python-format
msgid "Template ID: %s"
msgstr "ID на урнек: %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Template fallback"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:89
#, python-format
msgid "Test Your Mobile Version"
msgstr "Тестирајте ја вашата верзија за мобилен"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_post
msgid "Thank you for posting a message !"
msgstr "Ви благодариме што пративте порака !"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:104
#, fuzzy, python-format
msgid ""
"The 'Content' menu allows you to rename and delete pages or add them to the "
"top menu."
msgstr ""
"Менито 'Содржина' ви дозволува да додавате страни или да го додадете "
"главното мени."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The Banner or Big Picture building blocks are good choices for that."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"The Point of Sale works perfectly on any kind of touch enabled\n"
"                                device, whether it's multi-touch tablets "
"like an iPad or\n"
"                                keyboardless resistive touchscreen terminals."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"The best and simplest way to start is to create a few pages and link them "
"directly from the main menu.\n"
"                            Just try not to go over 6 items, otherwise your "
"main menu will be difficult to use."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The building blocks"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"The call to action’s (i.e. the button’s) wording, size, color and placement,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The colors palettes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"The cost is from $0.05 to $0.15 per word, depending on the translator's "
"level of expertise"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "The error occured while rendering the template"
msgstr "Грешката се случи при рендерирање на урнекот"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "The following error was raised in the website controller"
msgstr "Следната грешка се појави во вебсајт контролерот"

#. module: website
#: model:ir.model.fields,help:website.field_blog_post_website_url
#: model:ir.model.fields,help:website.field_delivery_carrier_website_url
#: model:ir.model.fields,help:website.field_event_event_website_url
#: model:ir.model.fields,help:website.field_event_track_website_url
#: model:ir.model.fields,help:website.field_hr_employee_website_url
#: model:ir.model.fields,help:website.field_hr_job_website_url
#: model:ir.model.fields,help:website.field_im_livechat_channel_website_url
#: model:ir.model.fields,help:website.field_payment_acquirer_website_url
#: model:ir.model.fields,help:website.field_product_template_website_url
#: model:ir.model.fields,help:website.field_project_project_website_url
#: model:ir.model.fields,help:website.field_res_partner_grade_website_url
#: model:ir.model.fields,help:website.field_res_partner_tag_website_url
#: model:ir.model.fields,help:website.field_res_partner_website_url
#: model:ir.model.fields,help:website.field_slide_channel_website_url
#: model:ir.model.fields,help:website.field_slide_slide_website_url
#: model:ir.model.fields,help:website.field_website_published_mixin_website_url
msgid "The full URL to access the document through the website."
msgstr "Целосната URL адреса за пристап на документот преку веб страната."

#. module: website
#: model:ir.model.fields,help:website.field_ir_act_server_website_url
msgid "The full URL to access the server action through the website."
msgstr "Полна адреса за пристап на серверска акција преку вебсајт."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
msgid "The page you were looking for could not be authorized."
msgstr "Страната што ја барате не може да биде авторизирана."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.404
msgid ""
"The page you were looking for could not be found; it is possible you have\n"
"                        typed the address incorrectly, but it has most "
"probably been removed due\n"
"                        to the recent website reorganisation."
msgstr ""
"Страната што ја баравте не е пронајдена; можно е да сте\n"
"                        ја напишале погрешно адресата, но најверојатно е "
"одстранета\n"
"                        поради неодамнешна реорганизација на веб сајтот."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The pre-loaded images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "The selected templates will be reset to their factory settings."
msgstr "Избраните урнеци ќе бидат ресетирани на нивните фабрички подесувања."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The shorter the better"
msgstr "Што пократко толку подобро"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"The theme you choose sets the tone for the overall look and feel of your "
"site design."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:18
#, python-format
msgid "The web site has encountered an error."
msgstr "Вебсајтот наиде на грешка."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.theme.js:280
#, python-format
msgid "Theme Error"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Theme Selection"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Then simply"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Then, copy the <strong>Client ID</strong> and <strong>Client Secret</strong> "
"codes and paste them in"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Then, go to"
msgstr "Потоа, одете на"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"There are three questions you should ask before deciding whether or not to "
"advertise on a particular keyword:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"They allow a detailed qualification of the visitor, which is perfect to link "
"them later to marketing campaigns."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "Трета функција"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exists, but you can create it as you are administrator of "
"this site."
msgstr ""
"Оваа страна не постои, но можете да ја креирате бидејќи вие сте "
"администратор на овој сајт."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:20
#, python-format
msgid ""
"This tutorial will guide you to build your home page. We will start by "
"adding a banner."
msgstr ""
"Овој прирачник ќе ве води при создавањето на вашата почетна страна. Ќе "
"започнеме со додавање на банер."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:48
#, python-format
msgid "Title"
msgstr "Титула"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these\n"
"                        three columns using the right icon of each block.\n"
"                        Then, duplicate one of the column to create a new\n"
"                        one as a copy."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "To do that, simply use the Promote menu on each page of your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"To get an external assessment of your website, you can also submit it to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "To learn more, take a look at their"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.contentMenu.js:175
#: model:website.menu,name:website.main_menu
#: model:website.menu,name:website.website2_main_menu
#, python-format
msgid "Top Menu"
msgstr "Главно мени"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "Traceback"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.snippets.editor.js:771
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Translate"
msgstr "Преведи"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:14
#, python-format
msgid "Translate this page"
msgstr "Преведи ја оваа страна"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:27
#, python-format
msgid "Translated content"
msgstr "Преведена содржина"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Translated versions are updated automatically after 32 hours on average."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Translating your website into other languages is the best way to broaden its "
"audience.\n"
"                        In Odoo, you can either manually translate your "
"pages as they are displayed, use an\n"
"                        automatic machine translation service for free or "
"pay professional translators to do\n"
"                        it for you."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_twitter
#: model:ir.model.fields,field_description:website.field_website_social_twitter
msgid "Twitter Account"
msgstr "Twitter налог"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "UA-XXXXXXXX-Y"
msgstr "UA-XXXXXXXX-Y"

#. module: website
#: model:ir.model.fields,help:website.field_website_cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Ultimate package"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Update your internal documents: footer of sales order, contracts,invoices, "
"business cards, etc."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:32
#, python-format
msgid ""
"Upload failed, some images might not have been uploaded. Check your network "
"connectivity."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:31
#, python-format
msgid "Upload successful."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_url
msgid "Url"
msgstr "Url"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Use Google Adwords"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_cdn_activated
msgid "Use a Content Delivery Network (CDN)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Use a free, automatic machine translation (quality will vary depending on "
"languages)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Use the <i>Customize</i> menu to change the look of your theme:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Use the Newsletter subscription (from mass mailing application) or the Big "
"button building block for that."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:112
#, python-format
msgid "Use this button to add pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.robots
msgid ""
"User-agent: *\n"
"Sitemap:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Velour"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Fast"
msgstr "Многу брзо"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Slow"
msgstr "Многу бавно"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "View our themes selection"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_post_website_published
#: model:ir.model.fields,field_description:website.field_delivery_carrier_website_published
#: model:ir.model.fields,field_description:website.field_event_track_website_published
#: model:ir.model.fields,field_description:website.field_hr_employee_website_published
#: model:ir.model.fields,field_description:website.field_hr_job_website_published
#: model:ir.model.fields,field_description:website.field_im_livechat_channel_website_published
#: model:ir.model.fields,field_description:website.field_product_template_website_published
#: model:ir.model.fields,field_description:website.field_project_project_website_published
#: model:ir.model.fields,field_description:website.field_res_partner_grade_website_published
#: model:ir.model.fields,field_description:website.field_res_partner_tag_website_published
#: model:ir.model.fields,field_description:website.field_res_partner_website_published
#: model:ir.model.fields,field_description:website.field_slide_channel_website_published
#: model:ir.model.fields,field_description:website.field_slide_slide_website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin_website_published
msgid "Visible in Website"
msgstr "Видливо на вебсајтот"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Visitors might not be interested in your products or services when they come "
"to your site. They could want to learn something, improve their life, grow "
"their business, find out more about you, etc. What great content can you "
"offer your visitors? Why should they stay on your website?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                                      life through disruptive products. We "
"build great products to solve your\n"
"                                      business problems."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                            life through disruptive products. We build great "
"products to solve your\n"
"                            business problems."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"We can create a customized theme for you with your company colors, logo and "
"images from your library."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "We hope this planner helped you to create your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "We'll do our best to get back to you as soon as possible."
msgstr "Ќе се потрудиме да ви одговориме што е можно поскоро."

#. module: website
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_id
#: model:ir.model.fields,field_description:website.field_website_menu_website_id
#: model:ir.ui.menu,name:website.menu_website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
msgid "Website"
msgstr "Вебсајт"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_configuration
msgid "Website Admin"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_module_website
msgid "Website Apps"
msgstr "Апликации за вебсајт"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_domain
msgid "Website Domain"
msgstr ""

#. module: website
#: model:ir.actions.act_url,name:website.action_website_homepage
msgid "Website Homepage"
msgstr "Почетна страна на вебсајт"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "Мени на вебсајт"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_website_name
#: model:ir.model.fields,field_description:website.field_website_name
msgid "Website Name"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.action_partner_post
msgid "Website Partner Post and Thanks Demo"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.action_partner_comment
msgid "Website Partners Comment Form"
msgstr "Вебсајт формулар за коментари на партнери"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_act_server_website_path
msgid "Website Path"
msgstr "Патека на вебсајт"

#. module: website
#: model:crm.team,name:website.salesteam_website_sales
msgid "Website Sales"
msgstr "Вебсајт продажби"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Website Settings"
msgstr "Подесувања на вебсајт"

#. module: website
#: model:ir.actions.act_window,name:website.action_module_theme
msgid "Website Theme"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_post_website_url
#: model:ir.model.fields,field_description:website.field_delivery_carrier_website_url
#: model:ir.model.fields,field_description:website.field_event_event_website_url
#: model:ir.model.fields,field_description:website.field_event_track_website_url
#: model:ir.model.fields,field_description:website.field_hr_employee_website_url
#: model:ir.model.fields,field_description:website.field_hr_job_website_url
#: model:ir.model.fields,field_description:website.field_im_livechat_channel_website_url
#: model:ir.model.fields,field_description:website.field_ir_act_server_website_url
#: model:ir.model.fields,field_description:website.field_payment_acquirer_website_url
#: model:ir.model.fields,field_description:website.field_product_template_website_url
#: model:ir.model.fields,field_description:website.field_project_project_website_url
#: model:ir.model.fields,field_description:website.field_res_partner_grade_website_url
#: model:ir.model.fields,field_description:website.field_res_partner_tag_website_url
#: model:ir.model.fields,field_description:website.field_res_partner_website_url
#: model:ir.model.fields,field_description:website.field_slide_channel_website_url
#: model:ir.model.fields,field_description:website.field_slide_slide_website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin_website_url
msgid "Website URL"
msgstr "Адреса на вебсајт"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Website Versioning."
msgstr ""

#. module: website
#: model:ir.actions.act_url,name:website.action_website_tutorial
msgid "Website With Tutorial"
msgstr "Вебсајт со прирачник"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "Мени на вебсајт"

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_blog_website_meta_description
#: model:ir.model.fields,field_description:website.field_blog_post_website_meta_description
#: model:ir.model.fields,field_description:website.field_blog_tag_website_meta_description
#: model:ir.model.fields,field_description:website.field_event_event_website_meta_description
#: model:ir.model.fields,field_description:website.field_event_track_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_documentation_toc_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_forum_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_post_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_tag_website_meta_description
#: model:ir.model.fields,field_description:website.field_hr_job_website_meta_description
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_meta_description
#: model:ir.model.fields,field_description:website.field_product_public_category_website_meta_description
#: model:ir.model.fields,field_description:website.field_product_template_website_meta_description
#: model:ir.model.fields,field_description:website.field_res_partner_website_meta_description
#: model:ir.model.fields,field_description:website.field_slide_channel_website_meta_description
#: model:ir.model.fields,field_description:website.field_slide_slide_website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_website_meta_description
msgid "Website meta description"
msgstr "Информациона дескрипција на Веб-страна"

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_blog_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_blog_post_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_blog_tag_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_event_event_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_event_track_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_documentation_toc_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_forum_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_post_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_tag_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_hr_job_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_product_public_category_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_product_template_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_res_partner_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_slide_channel_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_slide_slide_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_blog_website_meta_title
#: model:ir.model.fields,field_description:website.field_blog_post_website_meta_title
#: model:ir.model.fields,field_description:website.field_blog_tag_website_meta_title
#: model:ir.model.fields,field_description:website.field_event_event_website_meta_title
#: model:ir.model.fields,field_description:website.field_event_track_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_documentation_toc_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_forum_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_post_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_tag_website_meta_title
#: model:ir.model.fields,field_description:website.field_hr_job_website_meta_title
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_meta_title
#: model:ir.model.fields,field_description:website.field_product_public_category_website_meta_title
#: model:ir.model.fields,field_description:website.field_product_template_website_meta_title
#: model:ir.model.fields,field_description:website.field_res_partner_website_meta_title
#: model:ir.model.fields,field_description:website.field_slide_channel_website_meta_title
#: model:ir.model.fields,field_description:website.field_slide_slide_website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_website_meta_title
msgid "Website meta title"
msgstr "Информационен наслов на Веб-страна"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "Вебсајтови"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install_website_ids
msgid "Websites to translate"
msgstr "Вебсајтови за превод"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Welcome"
msgstr "Добредојдовте"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:19
#, python-format
msgid "Welcome to your website!"
msgstr "Добредојдовте на нашиот вебсајт!"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:82
#, python-format
msgid "Well done, you created your homepage."
msgstr "Добро сторено, ја креиравте вашата почетна страна."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "What may <strong>interest your visitors?</strong>"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view_page
msgid "Whether this view is a web page template (complete)"
msgstr "Дали овој поглед е урнек за вебстрана (комплетно)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Which hardware does Odoo POS support?"
msgstr "Кој хардвер е подржан од Odoo POS?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"While an internet connection is required to start the Point of\n"
"                                Sale, it will stay operational even after a "
"complete disconnection."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"With Odoo, you can automatically check which keywords are ranked best for a\n"
"                                    specific query, then add them in the "
"content of your page."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Wood"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
msgid ""
"Write a quote here from one of your customers. Quotes are a\n"
"                                                great way to build "
"confidence in your products or services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_slider
msgid ""
"Write a quote here from one of your customers. Quotes are a\n"
"                                        great way to build confidence in "
"your products or services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid ""
"Write a quote here from one of your customers. Quotes are a\n"
"                            great way to build confidence in your products "
"or services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quote
msgid ""
"Write a quote here from one of your customers. Quotes are a\n"
"            great way to build confidence in your products or services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or\n"
"                        services. To be successful your content needs to be\n"
"                        useful to your readers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid ""
"Write one or two paragraphs describing your product,\n"
"                        services or a specific feature. To be successful\n"
"                        your content needs to be useful to your readers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_message
msgid "Write one sentence to convince visitor about your message."
msgstr "Напишете една реченица за да ги уверите посетителите за вашата порака."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know,<br/>not what you want to show."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Yellow Green"
msgstr "Жолто зелено"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Yes"
msgstr "Да"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:18
#, python-format
msgid "You are about to enter the translation mode."
msgstr "Влегувате во мод за преведување."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can also place online advertisements on web pages that show results from "
"search\n"
"                        engine queries: this is called SEA."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can also use the <i>integrated HTML Editor(from the Customize menu)</i> "
"to modify the code directly."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can do this by optimizing your referencing by search engines like "
"Google: it's\n"
"                        called SEO. This  is the process of increasing the "
"visibility of a website or a web\n"
"                        page in a search engine's natural and unpaid "
"(\"organic\") search results. In general,\n"
"                        the earlier (or higher ranked on the search results "
"page), and more frequently a site\n"
"                        appears in the search results list, the more "
"visitors it will receive from the search\n"
"                        engine."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "You can retrieve a 75$ coupon code to start your campaign here:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can set Odoo on a custom domain name (e.g. yourcompany.com) for both the "
"website\n"
"                        and your emails. Because your website address is as "
"important to your branding as the\n"
"                        name of your business or organization, you’ll want "
"to put some thought into changing it\n"
"                        for a proper domain, or change it to one you already "
"own."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You have a lot of choices for that: the References, Quotes Slider, Twitter "
"Scroller,..."
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.action_module_theme
#: model_terms:ir.actions.act_window,help:website.action_module_website
#, fuzzy
msgid "You should try other search criteria."
msgstr "Треба да пробате со други пребарувачки критериуми."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You will get your leads filled up automatically in our integrated CRM "
"application."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/website.tour.banner.js:48
#: model_terms:ir.ui.view,arch_db:website.s_banner
#, python-format
msgid "Your Banner Title"
msgstr "Наслов на вашиот банер"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Domain Name"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Footer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Homepage"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Main Menu"
msgstr "Вашето главно мени"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your Objectives"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "Your Website Title"
msgstr "Наслов на вашиот вебсајт"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Your homepage is the most important page of your website. This is where "
"visitors get their first impressions of you. An excellent homepage will "
"encourage them to stay on your site, guide them towards your content, "
"reinforce your company's branding and more.<br/>\n"
"                        Here are some pointers to help you get started."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Your theme selection will define:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Your website is an important part of your online business strategy, but it "
"shouldn't be\n"
"                        the only one. There are so many ways to communicate "
"with your customers, visitors and\n"
"                        prospects that it's sometimes difficult to know what "
"to put your energy into. Here is\n"
"                        some advice on what to focus on next."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_social_youtube
#: model:ir.model.fields,field_description:website.field_website_social_youtube
msgid "Youtube Account"
msgstr "Youtube налог"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"a reference to your new website. We will retweet it (we have 30,000 "
"followers) and feature it in our different communications."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "and connect with the Google account you created"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "and evaluating the following expression:"
msgstr "и евалуација на следниот израз:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "and we'll configure your website and/or email for you."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "button."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "check out our blog application"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "create a Google Analytics account"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:17
#, python-format
msgid "describing your page content"
msgstr "опишувајќи ја содржината на вашата страна"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: About us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: Blog, Success stories, References, Events, Jobs"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: Contact us, Our Customers, Privacy Policy, Events, Blog, Jobs"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: interesting contents, texts and articles"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "for a free evaluation of the usability of your homepage."
msgstr "за бесплатна евалуација на користабилноста на Вашиот homepage."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:62
#, python-format
msgid "how your page will be listed on Google"
msgstr "како вашата страна ќе биде прикажена на Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "http://gengo.com/pricing-languages/"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "http://www.google.com/ads/adwords-coupon.html"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "http://www.google.com/webmasters/tools/"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://plus.google.com/+Odooapps"
msgstr "https://plus.google.com/+Odooapps"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://twitter.com/Odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
msgid "https://www.facebook.com/Odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://www.facebook.com/odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://www.linkedin.com/company/odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://www.youtube.com/user/OpenERPonline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_config_settings
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://youraccount.github.io"
msgstr "https://youraccount.github.io"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "if you don't have one yet."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "in Odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "инстанца на Odoo"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
msgid "ir.actions.server"
msgstr "ir.actions.server"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "ir.attachment"
msgstr "ir.прилог"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "ir.qweb"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "name"
msgstr "име"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "online help"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.translator.xml:5
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "or Edit Master"
msgstr "или уреди главна"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "or see who currently owns it"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "peek.usertesting.com"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "send us an email"
msgstr "Испратете ни е-порака"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.robots
msgid "sitemap.xml"
msgstr "sitemap.xml"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:19
#, python-format
msgid "the classic Odoo interface"
msgstr "класичен Odoo интерфејс"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"to describe\n"
"                    <br/> your experience or to suggest improvements !"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"to do that (make sure the Advanced Options are set to your country and\n"
"                                    language)."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "to get started."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "url"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "user / month (billed annually)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:45
#, python-format
msgid "using above suggested keywords"
msgstr "употребувајќи ги погоре наведените клучни зборови"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_config_settings_website_id
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "website"
msgstr "веб сајт"

#. module: website
#: model:ir.model,name:website.model_website_config_settings
msgid "website.config.settings"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "website.published.mixin"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "yes"
msgstr "да"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "your Company details form"
msgstr ""

#~ msgid ".xml"
#~ msgstr ".xml"

#~ msgid "<strong>Click Here</strong>"
#~ msgstr "<strong>Кликни овде</strong>"

#~ msgid "Color"
#~ msgstr "Боја"

#~ msgid "Discover more about Odoo"
#~ msgstr "Откријте повеќе за Odoo"

#~ msgid "Edit Menu Entry"
#~ msgstr "Уреди внес во мени"

#~ msgid "Hide link"
#~ msgstr "Скриј линк"

#~ msgid "Menu Label"
#~ msgstr "Наслов на мени"

#~ msgid "Reset Transformation"
#~ msgstr "Ресетирај трансформација"

#~ msgid "Transform"
#~ msgstr "Трансформирај"

#~ msgid "Yes."
#~ msgstr "Да."

#~ msgid "or"
#~ msgstr "или"

#~ msgid "sitemap-"
#~ msgstr "sitemap-"
