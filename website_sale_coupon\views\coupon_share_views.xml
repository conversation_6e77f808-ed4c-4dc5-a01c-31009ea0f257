<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="coupon_share_view_form" model="ir.ui.view">
        <field name="name">coupon.share.form</field>
        <field name="model">coupon.share</field>
        <field name="arch" type="xml">
            <form string="Share Coupon" edit="1" create="0">
                <field name="program_website_id" invisible="1"/>
                <group attrs="{'invisible': [('website_id', '=', False)]}">
                    <div colspan="2">
                        <p>
                            You can share this promotion with your customers.
                            It will be applied at checkout when the customer uses this link.
                        </p>
                    </div>
                    <field name="share_link" widget="CopyClipboardURL" nolabel="1"/>
                </group>
                <hr attrs="{'invisible': ['|', ('website_id', '=', False), ('id', '!=', False)]}"/>
                <group attrs="{'invisible': [('id', '!=', False)]}">
                    <field name="website_id" groups="website.group_multi_website" widget="selection"
                           attrs="{'invisible': [('program_website_id', '!=', False)]}"/>
                    <field name="redirect"/>
                </group>
                <footer>
                    <button string="Done" class="btn-primary" special="cancel" data-hotkey="z"/>
                    <button string="Generate Short Link" class="btn-secondary" type="object" name="action_generate_short_link"
                            attrs="{'invisible': ['|', ('website_id', '=', False), ('id', '!=', False)]}" data-hotkey="g"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
