# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_timesheet
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-04-21 09:27+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/odoo/"
"odoo-9/language/en_GB/)\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_company
msgid "Companies"
msgstr "Companies"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_hr_employee
msgid "Employee"
msgstr "Employee"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product Template"
msgstr "Product Template"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "Project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_project_project_id
#, fuzzy
msgid "Project associated to this sale"
msgstr "Timesheet activities associated to this sale"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order
msgid "Sales Order"
msgstr "Sales Order"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "Sales Order Line"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.project_time_mode_id_duplicate_xmlid
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right "
"unit of measure in your employees."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_hr_employee_timesheet_cost
msgid "Timesheet Cost"
msgstr "Timesheet Cost"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.project_time_mode_id_duplicate_xmlid
#, fuzzy
msgid "Timesheet UoM"
msgstr "Timesheet"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_timesheet_count
msgid "Timesheet activities"
msgstr "Timesheet activities"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr "Timesheet activities associated to this sale"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_employee_extd_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "Timesheets"
msgstr "Timesheets"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_timesheet.py:128
#, python-format
msgid ""
"You can use only one product on timesheet within the same sale order. You "
"should split your order to include only one contract based on time and "
"material."
msgstr ""
"You can use only one product on timesheet within the same sale order. You "
"should split your order to include only one contract based on time and "
"material."

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
#, fuzzy
msgid "account analytic line"
msgstr "Analytic Line"
