<?xml version="1.0" encoding="utf-8"?>
<odoo>
   <record id="sale_order_view_form" model="ir.ui.view">
         <field name="name">sale.order.form.inherit.coupon</field>
         <field name="model">sale.order</field>
         <field name="inherit_id" ref="sale.view_order_form" />
         <field name="priority">10</field>
         <field name="arch" type="xml">
            <xpath expr="//group[@name='note_group']" position="before">
                <div class="oe_right">
                    <button name="%(sale_coupon.sale_coupon_apply_code_action)d" class="btn btn-secondary"
                            string="Coupon" type="action" groups="base.group_user" states="draft,sent,sale"/>
                    <button name="recompute_coupon_lines" class="btn btn-secondary" string="Promotions"
                            help="When clicked, the content of the order will be checked to detect (and apply) possible promotion programs."
                            type="object" states="draft,sent,sale"/>
                </div>
            </xpath>
         </field>
    </record>

    <record id="sale_order_action" model="ir.actions.act_window">
        <field name="name">Sales Order</field>
        <field name="res_model">sale.order</field>
        <field name="view_id" ref="sale.view_order_tree"/>
        <field name="domain">[('state', '!=', 'cancel')]</field>
        <field name="context">{}</field>
    </record>
</odoo>
