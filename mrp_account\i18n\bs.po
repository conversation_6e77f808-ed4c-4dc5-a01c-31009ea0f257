# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mrp_bom_cost
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:06+0000\n"
"PO-Revision-Date: 2018-10-02 10:06+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: mrp_bom_cost
#: model:ir.actions.server,name:mrp_bom_cost.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_bom_cost.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_bom_cost.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_bom_cost.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_bom_cost.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr ""

#. module: mrp_bom_cost
#: model_terms:ir.ui.view,arch_db:mrp_bom_cost.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_bom_cost.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_bom_cost.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""

#. module: mrp_bom_cost
#: model:ir.model,name:mrp_bom_cost.model_product_product
msgid "Product"
msgstr "Proizvod"

#. module: mrp_bom_cost
#: model:ir.model,name:mrp_bom_cost.model_product_template
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: mrp_bom_cost
#: code:addons/mrp_bom_cost/models/product.py:40
#, python-format
msgid ""
"The inventory valuation of some products %s is automated. You can only "
"update their cost from the product form."
msgstr ""
