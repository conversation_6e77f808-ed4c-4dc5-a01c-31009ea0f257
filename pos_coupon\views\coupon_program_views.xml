<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Menu Items -->
    <menuitem
        id="menu_coupon_type_config"
        action="coupon.coupon_program_action_coupon_program"
        parent="point_of_sale.pos_config_menu_catalog"
        name="Coupon Programs"
        groups="point_of_sale.group_pos_manager"
        sequence="91"
    />

    <menuitem
        id="menu_promotion_type_config"
        action="coupon.coupon_program_action_promo_program"
        parent="point_of_sale.pos_config_menu_catalog"
        name="Promotion Programs"
        groups="point_of_sale.group_pos_manager"
        sequence="90"
    />

    <!-- Form Views -->
    <record id="pos_coupon_program_view_coupon_program_form" model="ir.ui.view">
        <field name="name">coupon.program.form</field>
        <field name="model">coupon.program</field>
        <field name="inherit_id" ref="coupon.coupon_program_view_coupon_program_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='coupon_count']/.." position="before">
                <button class="oe_stat_button" type="object" icon="fa-usd" name="action_view_pos_orders">
                    <field name="pos_order_count" string="PoS Sales" widget="statinfo"/>
                </button>
            </xpath>
        </field>
    </record>

    <record id="pos_coupon_program_view_promo_program_form" model="ir.ui.view">
        <field name="name">coupon.program.form</field>
        <field name="model">coupon.program</field>
        <field name="inherit_id" ref="coupon.coupon_program_view_promo_program_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='promo_code']" position="after">
                <field name="promo_barcode" attrs="{'invisible': [('promo_code_usage', '=', 'no_code_needed')]}" readonly="1" groups="base.group_no_one"/>
            </xpath>
            <xpath expr="//field[@name='coupon_count']/.." position="before">
                <button class="oe_stat_button" type="object" icon="fa-usd" name="action_view_pos_orders">
                    <field name="pos_order_count" string="PoS Sales" widget="statinfo"/>
                </button>
            </xpath>
        </field>
    </record>
</odoo>
