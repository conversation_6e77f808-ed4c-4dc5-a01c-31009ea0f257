<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="payroll_allowances_form_wizard" model="ir.ui.view">
            <field name="name">Payroll Allowances</field>
            <field name="model">payroll.allowances</field>
            <field name="arch" type="xml">
                <form string="Payroll allowances Report">
                    <group>
                        <group>
                            <field name="employee_id" class="oe_inline"/>
                        </group>
                    </group>

                    <group>
                        <group>
                            <field name="date_from" class="oe_inline"/>
                        </group>
                        <group>
                            <field name="date_to" class="oe_inline"/>
                        </group>
                    </group>


                    <footer>
                        <button name="generate_payroll_allowances" type="object" string="Print" class="oe_highlight"/>
                        <button type="object" string="Cancel" special="cancel"/>
                    </footer>

                </form>
            </field>
        </record>

        <record id="payroll_allowances_action_wizard" model="ir.actions.act_window">
            <field name="name">كشف بالمرتب وتفصيل الإضافات</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">payroll.allowances</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="payroll_allowances_form_wizard"/>
            <field name="target">new</field>
        </record>

        <!-- This Menu Item Must have a parent -->
        <menuitem id="payroll_reports_categ" name="Reporting"
                  parent="hr_payroll_community.menu_hr_payroll_community_root"
                  sequence="99"/>
        <menuitem id="create_payroll_allowances" name="كشف بالمرتب وتفصيل الإضافات"
                  parent="payroll_reports_categ"
                  action="payroll_allowances_action_wizard"
                  sequence="2"/>


    </data>
</odoo>