# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_holidays
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Spellbound Soft Solutions <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allocation_type
msgid ""
"\tFixed by HR: allocated by HR and cannot be bypassed; users can request "
"leaves;\tFixed by HR + allocation request: allocated by HR and users can "
"request leaves and allocations;\tNo allocation: no allocation by default, "
"users can freely request leaves;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important &gt;&lt;/td&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important /&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important/&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 10px\" &gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 8px; min-width: 18px\"&gt;"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_type.py:212
#, python-format
msgid "%g remaining out of %g"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:428
#, python-format
msgid "%s : %.2f day(s)"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:440
#, python-format
msgid "%s on %s :%.2f day(s)"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/td&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/th&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;td style=background-color:"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;th class=\"text-center\" colspan="
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "0:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "0:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "10:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "10:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "10:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "10:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "11:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "11:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "11:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "11:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "12:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "12:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "1:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "1:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "1:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "1:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "2:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "2:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "2:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "2:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "3:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "3:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "3:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "3:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "4:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "4:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "4:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "4:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "5:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "5:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "5:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "5:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "6:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "6:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "6:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "6:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "7:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "7:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "7:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "7:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "8:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "8:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "8:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "8:30 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "9:00 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "9:00 PM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "9:30 AM"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_hour_from:0 selection:hr.leave,request_hour_to:0
msgid "9:30 PM"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_kanban
msgid "<small class=\"text-muted\">from</small>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_kanban
msgid "<small class=\"text-muted\">to</small>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '=', 'hour')]}\">Days</span>\n"
"                                <span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '!=', 'hour')]}\">Hours</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "<span class=\"ml8\">Days</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "<span class=\"ml8\">Hours</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "<span class=\"ml8\">of leaves every</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Days Allocated</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Group Leaves</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span class=\"oe_inline\" attrs=\"{'invisible': ['|', ('request_unit_half', '=', True), ('request_unit_hours', '=', True)]}\">\n"
"                                    To\n"
"                                </span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">from</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">to</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "<span>Add</span>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "<strong>Departments and Employees</strong>"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:516
#: code:addons/hr_holidays/models/hr_leave_allocation.py:359
#, python-format
msgid "A leave cannot be duplicated."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__show_leaves
msgid "Able to see Remaining Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid "Absence"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__absence_of_today
msgid "Absence by Today"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid ""
"Absent Employee(s), Whose leaves request are either confirmed or validated "
"on today"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_employee_action_from_department
msgid "Absent Employees"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__is_absent_totay
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "Absent Today"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__accrual
msgid "Accrual"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Accrual Allocations"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_allocation_cron_accrual_ir_actions_server
#: model:ir.cron,cron_name:hr_holidays.hr_leave_allocation_cron_accrual
#: model:ir.cron,name:hr_holidays.hr_leave_allocation_cron_accrual
msgid "Accrual Leave: Updates the number of leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "Action Needed"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__active
msgid "Active"
msgstr "સક્રિય"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Allocations"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Active Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Types"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_ids
msgid "Activities"
msgstr "પ્રવૃત્તિઓ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_state
msgid "Activity State"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.mail_activity_type_action_config_hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_config_activity_type
msgid "Activity Types"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Add a reason..."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__validity_start
msgid ""
"Adding validity to types of leaves so that it cannot be selected outside "
"this time period"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_date_from_period:0
msgid "Afternoon"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_all
msgid "All"
msgstr "બધા"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_all
msgid "All Allocations"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_dashboard
msgid "All Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Allocated Days"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Allocation"
msgstr "ફાળવણી"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_approval
msgid "Allocation Approval"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,name:hr_holidays.mt_department_leave_allocation_approved
msgid "Allocation Approved"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_type
msgid "Allocation Mode"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,name:hr_holidays.mt_department_leave_allocation_refused
msgid "Allocation Refused"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.report,type:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Allocation Request"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Allocation Requests"
msgstr ""

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_second_approval
msgid "Allocation Second Approve"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:302
#, python-format
msgid "Allocation of %s : %.2f %s to %s"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__allocation_to_approve_count
msgid "Allocation to Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_all_allocations
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_approve_allocations
msgid "Allocations"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_open_allocation
msgid "Allocations Requests"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__holiday_type
msgid ""
"Allow to create requests in batchs:\n"
"- By Employee: for a specific employee\n"
"- By Company: all employees of the specified company\n"
"- By Department: all employees of the specified department\n"
"- By Employee Tag: all employees of the specific employee group category"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Analyze from"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_evaluation_report_graph
msgid "Appraisal Analysis"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:778
#: code:addons/hr_holidays/models/hr_leave_allocation.py:562
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Approve"
msgstr ""

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0
#: selection:hr.holidays.summary.dept,holiday_type:0
#: selection:hr.holidays.summary.employee,holiday_type:0
#: selection:hr.leave,state:0 selection:hr.leave.allocation,state:0
#: selection:hr.leave.report,state:0
#: model:mail.message.subtype,name:hr_holidays.mt_leave_allocation_approved
#: model:mail.message.subtype,name:hr_holidays.mt_leave_approved
msgid "Approved"
msgstr "મંજૂર થઇ ચૂકી છે"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Approved Allocations"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Approved Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Approved Requests"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_attachment_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__accrual_limit
msgid "Balance limit"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Black"
msgstr "કાળો"

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Blue"
msgstr "વાદળી"

#. module: hr_holidays
#: selection:hr.holidays.summary.dept,holiday_type:0
#: selection:hr.holidays.summary.employee,holiday_type:0
msgid "Both Approved and Confirmed"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Brown"
msgstr "તપખીરિયા રંગનું"

#. module: hr_holidays
#: selection:hr.leave,holiday_type:0
#: selection:hr.leave.allocation,holiday_type:0
msgid "By Company"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,holiday_type:0
#: selection:hr.leave.allocation,holiday_type:0
msgid "By Department"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,holiday_type:0
#: selection:hr.leave.allocation,holiday_type:0
#: selection:hr.leave.report,holiday_type:0
msgid "By Employee"
msgstr "કર્મચારી દ્વારા"

#. module: hr_holidays
#: selection:hr.leave,holiday_type:0
#: selection:hr.leave.allocation,holiday_type:0
#: selection:hr.leave.report,holiday_type:0
msgid "By Employee Tag"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__holiday_type
msgid ""
"By Employee: Allocation/Request for individual Employee, By Employee Tag: "
"Allocation/Request for group of employees in category"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Calendar"
msgstr "કેલેન્ડર"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_approve
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_approve
msgid "Can Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_reset
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_reset
msgid "Can reset"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_dept
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Cancel"
msgstr "રદ કરો"

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0 selection:hr.leave,state:0
#: selection:hr.leave.allocation,state:0 selection:hr.leave.report,state:0
msgid "Cancelled"
msgstr "રદ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Category"
msgstr "વર્ગ"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__category_id
msgid "Category of Employee"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Color"
msgstr "રંગ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color_name
msgid "Color in Report"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
msgid "Comment by Manager"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__mode_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__mode_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__company_id
msgid "Company"
msgstr "કંપની"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_comp
msgid "Compensatory Days"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_configuration
msgid "Configuration"
msgstr "રુપરેખાંકન"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Confirm"
msgstr "ખાતરી"

#. module: hr_holidays
#: selection:hr.holidays.summary.dept,holiday_type:0
#: selection:hr.holidays.summary.employee,holiday_type:0
msgid "Confirmed"
msgstr "સમર્થિત"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
msgid "Create a new leave allocation"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid "Create a new leave allocation request"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_payroll
#: model_terms:ir.actions.act_window,help:hr_holidays.open_company_allocation
msgid "Create a new leave request"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_uid
msgid "Created by"
msgstr "બનાવનાર"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_date
msgid "Created on"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_state
msgid "Current Leave Status"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_id
msgid "Current Leave Type"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Current Year"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_hours
msgid "Custom Hours"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from_period
msgid "Date Period Start"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__nextcall
msgid "Date of the next accrual allocation"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,request_unit:0
msgid "Day"
msgstr "દિવસ"

#. module: hr_holidays
#: selection:hr.leave.allocation,unit_per_interval:0
msgid "Day(s)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_allocation
msgid "Days Allocated"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_custom
msgid "Days-long custom hours"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Delete"
msgstr "કાઢી નાંખો"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__department_id
msgid "Department"
msgstr "વિભાગ"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_approve_department
msgid "Department Allocation to Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_approve_department
msgid "Department Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept__depts
msgid "Department(s)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Description"
msgstr "વર્ણન"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_report_hr_holidays_report_holidayssummary__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: hr_holidays
#: selection:hr.leave.type,validation_type:0
msgid "Double Validation"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Dropdown menu"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Duration"
msgstr "સમયગાળો"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days
msgid "Duration (Days)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid "Duration (days)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid "Duration (hours)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days_display
msgid "Duration in days"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days
msgid "Duration in days. Reference field to use when necessary."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours_display
msgid "Duration in hours"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
msgid "Edit Allocation"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Edit Leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Employee"
msgstr "કર્મચારી"

#. module: hr_holidays
#: selection:hr.leave.type,validation_type:0
msgid "Employee Manager"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__category_id
msgid "Employee Tag"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__emp
msgid "Employee(s)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__validity_stop
msgid "End Date"
msgstr "અંતિમ તારીખ"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_type.py:93
#, python-format
msgid "End of validity period should be greater than start of validity period"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid ""
"Filters only on allocations that belong to an leave type that is 'active' "
"(active field is True)"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid ""
"Filters only on requests that belong to an leave type that is 'active' "
"(active field is True)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__first_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__first_approver_id
msgid "First Approval"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,allocation_type:0
msgid "Fixed by HR"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,allocation_type:0
msgid "Fixed by HR + allocation request"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_follower_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_follower_ids
msgid "Followers"
msgstr "અનુયાયીઓ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_channel_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_partner_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/report/holidays_summary_report.py:113
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__date_from
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "From"
msgstr "તરફથી"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_from
msgid "From Date"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Future Activities"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__payslip_status
msgid ""
"Green this button when the leave has been taken into account in the payslip."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Group By"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_leave
msgid "Group Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__report_note
msgid "HR Comments"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_department
msgid "HR Department"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_dept
msgid "HR Leaves Summary Report By Department"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_employee
msgid "HR Leaves Summary Report By Employee"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_half
msgid "Half Day"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_report_hr_holidays_report_holidayssummary
msgid "Holidays Summary Report"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_from
msgid "Hour from"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_to
msgid "Hour to"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.allocation,unit_per_interval:0
msgid "Hour(s)"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,request_unit:0
msgid "Hours"
msgstr "કલાક"

#. module: hr_holidays
#: selection:hr.leave.type,validation_type:0
msgid "Human Resource officer"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__id
#: model:ir.model.fields,field_description:hr_holidays.field_report_hr_holidays_report_holidayssummary__id
msgid "ID"
msgstr "ઓળખ"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_unread
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_unread
msgid "If checked new messages require your attention."
msgstr "જો ચેક કરેલા નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "If checked, new messages require your attention."
msgstr "જો ચેક કરેલું હોય, તો નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "જો ચેક કરેલું હોય, તો કેટલાક મેસેજીસમાં ડિલિવરીની ભૂલ છે."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "If no value set, runs indefinitely"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the leave "
"type without removing it."
msgstr ""

#. module: hr_holidays
#: sql_constraint:hr.leave:0
msgid ""
"If you want to change the number of days you should use the 'period' mode"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "In"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_is_follower
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__unpaid
msgid "Is Unpaid"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Ivory"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__time_type
msgid "Kind of Leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_report_hr_holidays_report_holidayssummary____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Late Activities"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Lavender"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,time_type:0
#: model:ir.model,name:hr_holidays.model_hr_leave
msgid "Leave"
msgstr "છોડો"

#. module: hr_holidays
#: selection:hr.leave.report,type:0
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar_leaves__holiday_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Leave Request"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Leave Requests"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_report
msgid "Leave Summary / Report"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
msgid "Leave Type"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_holiday_status
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_status_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Leave Types"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:623
#: code:addons/hr_holidays/models/hr_leave_allocation.py:409
#, python-format
msgid "Leave request must be confirmed (\"To Approve\") in order to approve it."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:636
#: code:addons/hr_holidays/models/hr_leave_allocation.py:423
#, python-format
msgid "Leave request must be confirmed in order to approve it."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:671
#: code:addons/hr_holidays/models/hr_leave_allocation.py:461
#, python-format
msgid "Leave request must be confirmed or validated in order to refuse it."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:613
#: code:addons/hr_holidays/models/hr_leave_allocation.py:399
#, python-format
msgid ""
"Leave request must be in Draft state (\"To Submit\") in order to confirm it."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:597
#, python-format
msgid ""
"Leave request state must be \"Refused\" or \"To Approve\" in order to be "
"reset to draft."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:383
#, python-format
msgid ""
"Leave request state must be \"Refused\" or \"To Approve\" in order to reset "
"to Draft."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__leave_to_approve_count
msgid "Leave to Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.act_hr_employee_holiday_request
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_all
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_root
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_approve
#: model:ir.ui.menu,name:hr_holidays.menu_open_employee_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation
msgid "Leaves Allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leaves_taken
msgid "Leaves Already Taken"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_available_holidays_report
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_department
#: model:ir.ui.menu,name:hr_holidays.menu_hr_available_holidays_report_tree
msgid "Leaves Analysis"
msgstr ""

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_approval
msgid "Leaves Approval"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,name:hr_holidays.mt_department_leave_approved
msgid "Leaves Approved"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Leaves Left"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,name:hr_holidays.mt_department_leave_refused
msgid "Leaves Refused"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_payroll
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_my
msgid "Leaves Requests"
msgstr ""

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_second_approval
msgid "Leaves Second Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_employee
#: model:ir.actions.act_window,name:hr_holidays.open_company_allocation
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary2
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_pivot
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Leaves Summary"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Leaves Taken:"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_dept
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_dept
msgid "Leaves by Department"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Leaves of Your Team Member"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_payroll_to_report
msgid "Leaves to report"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Leaves."
msgstr ""

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_cl
msgid "Legal Leaves 2018"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Light Blue"
msgstr "આછો વાદળી"

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Light Coral"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Light Cyan"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Light Green"
msgstr "આછો લીલો"

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Light Pink"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Light Salmon"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Light Yellow"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__linked_request_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__linked_request_ids
msgid "Linked Requests"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Magenta"
msgstr "જાંબલી"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__manager_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model:res.groups,name:hr_holidays.group_hr_holidays_manager
msgid "Manager"
msgstr "વ્યવસ્થાપક"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_approvals
msgid "Managers"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Max Leaves:"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_leaves
msgid "Maximum Allowed"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__remaining_leaves
msgid "Maximum Leaves Allowed - Leaves Already Taken"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid ""
"Maximum Leaves Allowed - Leaves Already Taken - Leaves Waiting Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__accrual_limit
msgid "Maximum of allocation for accrual; 0 means no maximum."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__meeting_id
msgid "Meeting"
msgstr "સભા"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__categ_id
msgid "Meeting Type"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_ids
msgid "Messages"
msgstr "સંદેશાઓ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Mode"
msgstr "નમુનો"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Month"
msgstr "મહિનો"

#. module: hr_holidays
#: selection:hr.leave.allocation,interval_unit:0
msgid "Month(s)"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,request_date_from_period:0
msgid "Morning"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_my
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Allocations"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Department Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_my_leaves
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "My Requests"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Team Leaves"
msgstr ""

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0
msgid "New"
msgstr "નવું"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_new_request
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_new_request
msgid "New Request"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_summary
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_summary
msgid "Next Activity Summary"
msgstr "આગલું પ્રવૃત્તિ સારાંશ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,allocation_type:0
msgid "No allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Number of Days"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leaves_count
msgid "Number of Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days
msgid ""
"Number of days of the leave request according to your working schedule."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days_display
msgid "Number of days of the leave request. Used for interface."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of error"
msgstr "ભૂલની સંખ્યા"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours_display
msgid ""
"Number of hours of the leave request according to your working schedule. "
"Used for interface."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "સંદેશાઓની સંખ્યા જે ક્રિયા માટે જરૂરી છે"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "વિતરણ ભૂલ સાથે સંદેશાઓની સંખ્યા"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__interval_number
msgid "Number of unit between two intervals"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_per_interval
msgid "Number of unit per interval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_unread_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_unread_counter
msgid "Number of unread messages"
msgstr "ન વાંચેલા સંદેશાની સંખ્યા"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_user
msgid "Officer"
msgstr "અધિકારી"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__categ_id
msgid ""
"Once a leave is validated, Odoo will create a corresponding meeting of this "
"type in the calendar."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_payroll
msgid ""
"Once you have recorded your leave request, it will be sent\n"
"                to a manager for validation. Be sure to set the right leave\n"
"                type (recuperation, legal leaves, sickness) and the exact\n"
"                number of open days related to your leave."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:709
#: code:addons/hr_holidays/models/hr_leave_allocation.py:495
#, python-format
msgid "Only a Leave Manager can approve its own requests."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:698
#: code:addons/hr_holidays/models/hr_leave_allocation.py:484
#, python-format
msgid "Only a Leave Manager can reset other people leaves."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:702
#: code:addons/hr_holidays/models/hr_leave_allocation.py:488
#, python-format
msgid "Only a Leave Officer or Manager can approve or refuse leave requests."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:718
#: code:addons/hr_holidays/models/hr_leave_allocation.py:504
#, python-format
msgid "Only an Leave Manager can apply the second approval on leave requests."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Options"
msgstr "વિકલ્પો"

#. module: hr_holidays
#: selection:hr.leave.type,time_type:0
msgid "Other"
msgstr "અન્ય"

#. module: hr_holidays
#: selection:hr.leave,activity_state:0
#: selection:hr.leave.allocation,activity_state:0
msgid "Overdue"
msgstr "મુદતવીતી"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.hr_holiday_status_hl
msgid "Overtime Compensation"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_dashboard
msgid "Overview"
msgstr ""

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_cla
msgid "Paid Time Off 2018"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__parent_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__parent_id
msgid "Parent"
msgstr "પિતૃ"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.hr_holiday_status_dv
msgid "Parental Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_payroll
msgid "Payroll"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,activity_state:0
#: selection:hr.leave.allocation,activity_state:0
msgid "Planned"
msgstr "આયોજિત"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_dept
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Print"
msgstr "છાપો"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__notes
msgid "Reasons"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Red"
msgstr "લાલ"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:781
#: code:addons/hr_holidays/models/hr_leave_allocation.py:565
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Refuse"
msgstr "નકારવા"

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0 selection:hr.leave,state:0
#: selection:hr.leave.allocation,state:0 selection:hr.leave.report,state:0
#: model:mail.message.subtype,name:hr_holidays.mt_leave_allocation_refused
#: model:mail.message.subtype,name:hr_holidays.mt_leave_refused
msgid "Refused"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__user_id
msgid "Related user name for the resource to manage its access."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Days"
msgstr "બાકીના દિવસો"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__remaining_leaves
msgid "Remaining Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__remaining_leaves
msgid "Remaining Legal Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Remaining leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_summary_dept
msgid "Report by Department"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__payslip_status
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__payslip_status
msgid "Reported in last payslips"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report
msgid "Reporting"
msgstr "અહેવાલીકરણ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_to
msgid "Request End Date"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from
msgid "Request Start Date"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__type
msgid "Request Type"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_allocation_approved
#: model:mail.message.subtype,description:hr_holidays.mt_leave_approved
msgid "Request approved"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_allocation_refused
#: model:mail.message.subtype,description:hr_holidays.mt_leave_refused
msgid "Request refused"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Reset to Draft"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar_leaves
msgid "Resource Leaves Detail"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_user_id
msgid "Responsible User"
msgstr "જવાબદાર વપરાશકર્તા"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Run Until"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Run until"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Search Leave"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Search Leave Type"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Search allocations"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,state:0 selection:hr.leave.allocation,state:0
#: selection:hr.leave.report,state:0
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__second_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__second_approver_id
msgid "Second Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__holiday_type
msgid "Select Leave Type"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__sequence
msgid "Sequence"
msgstr "ક્રમ"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_sl
msgid "Sick Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__validity_start
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Start Date"
msgstr "શરુઆતની તારીખ"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__state
msgid "Status"
msgstr "સ્થિતિ"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Sum"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_open_company_allocation
msgid "Summary"
msgstr "સાર"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__request_unit
msgid "Take Leaves in"
msgstr ""

#. module: hr_holidays
#: sql_constraint:hr.leave:0 sql_constraint:hr.leave.allocation:0
msgid ""
"The employee, department, company or employee category of this request is "
"missing. Please make sure that your user login is linked to an employee."
msgstr ""

#. module: hr_holidays
#: sql_constraint:hr.leave.allocation:0
msgid "The interval number should be greater than 0"
msgstr ""

#. module: hr_holidays
#: sql_constraint:hr.leave.allocation:0
msgid "The number of days must be greater than 0."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:407
#, python-format
msgid ""
"The number of remaining leaves is not sufficient for this leave type.\n"
"Please also check the leaves waiting for validation."
msgstr ""

#. module: hr_holidays
#: sql_constraint:hr.leave.allocation:0
msgid "The number per interval should be greater than 0"
msgstr ""

#. module: hr_holidays
#: sql_constraint:hr.leave:0
msgid "The start date must be anterior to the end date."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__state
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__state
msgid ""
"The status is set to 'To Submit', when a leave request is created.\n"
"The status is 'To Approve', when leave request is confirmed by user.\n"
"The status is 'Refused', when leave request is refused by manager.\n"
"The status is 'Approved', when leave request is approved by manager."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__sequence
msgid ""
"The type with the smallest sequence is the default value in leave request"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__first_approver_id
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__first_approver_id
msgid "This area is automatically filled by the user who validate the leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__second_approver_id
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__second_approver_id
msgid ""
"This area is automaticly filled by the user who validate the leave with "
"second level (If Leave type need second validation)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color_name
msgid ""
"This color will be used in the leaves summary located in Reporting > Leaves "
"by Department."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__valid
msgid "This indicates if it is still possible to use this type of leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__leaves_taken
msgid ""
"This value is given by the sum of all leaves requests with a negative value."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_leaves
msgid ""
"This value is given by the sum of all leaves requests with a positive value."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "To"
msgstr "પ્રતિ"

#. module: hr_holidays
#: selection:hr.leave,state:0 selection:hr.leave.allocation,state:0
#: selection:hr.leave.report,state:0
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_approve
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "To Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_to
msgid "To Date"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "To Do"
msgstr "કરવાનું"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "To Report in Payslip"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,state:0 selection:hr.leave.allocation,state:0
#: selection:hr.leave.report,state:0
msgid "To Submit"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave,activity_state:0
#: selection:hr.leave.allocation,activity_state:0
msgid "Today"
msgstr "આજે"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Today Activities"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__total_employee
msgid "Total Employee"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Total allocated days"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Total leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__remaining_leaves
msgid ""
"Total number of legal leaves allocated to this employee, change this value "
"to create allocation/leave request. Total based on all the leave types "
"without overriding limit."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Type"
msgstr "પ્રકાર"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid ""
"UX field allowing to see and modify the allocation duration, computed in "
"days."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid ""
"UX field allowing to see and modify the allocation duration, computed in "
"hours."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__unit_per_interval
msgid "Unit of time added at each interval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__interval_unit
msgid "Unit of time between two intervals"
msgstr ""

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_unpaid
msgid "Unpaid"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_unread
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_unread
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Unread Messages"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_unread_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__user_id
msgid "User"
msgstr "વપરાશકર્તા"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__valid
msgid "Valid"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Validate"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Validation"
msgstr "માન્યતા"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__validation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__validation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__validation_type
msgid "Validation By"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Validator"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Validity"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Violet"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid "Virtual Remaining Leaves"
msgstr ""

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0
msgid "Waiting Approval"
msgstr ""

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0
msgid "Waiting Second Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.allocation,interval_unit:0
msgid "Week(s)"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.type,color_name:0
msgid "Wheat"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__time_type
msgid ""
"Whether this should be computed as a holiday or as work time (eg: formation)"
msgstr ""

#. module: hr_holidays
#: selection:hr.leave.allocation,interval_unit:0
msgid "Year(s)"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:326
#, python-format
msgid "You can allocate %s only between %s and %s"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:397
#, python-format
msgid "You can not have 2 leaves that overlaps on the same day."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:463
#, python-format
msgid "You can take %s only between %s and %s"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:511
#: code:addons/hr_holidays/models/hr_leave_allocation.py:354
#, python-format
msgid "You cannot delete a leave which is in %s state."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/wizard/hr_holidays_summary_department.py:27
#, python-format
msgid "You have to select at least one department."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:714
#: code:addons/hr_holidays/models/hr_leave_allocation.py:500
#, python-format
msgid "You must be either %s's manager or Leave manager to approve this leave"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "days"
msgstr "દિવસો"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
msgid "e.g. Report to the next month..."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "of the"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "to"
msgstr "થી"
