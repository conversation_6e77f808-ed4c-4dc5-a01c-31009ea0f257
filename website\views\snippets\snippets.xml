<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- Snippets menu -->
<template id="snippets" inherit_id="web_editor.snippets" primary="True" groups="base.group_user">
    <xpath expr="//div[@id='snippets_menu']" position="inside">
        <button type="button" tabindex="3" class="o_we_customize_theme_btn text-uppercase"
                groups="website.group_website_designer" accesskey="2">
            <span>Theme</span>
        </button>
    </xpath>
    <xpath expr="//t[@id='default_snippets']" position="replace">
        <t id="default_snippets">
            <t t-set="cta_btn_text" t-value="False"/>
            <t t-set="cta_btn_href">/contactus</t>

            <div id="snippet_structure" class="o_panel">
                <div class="o_panel_header">Structure</div>
                <div class="o_panel_body">
                    <t t-snippet="website.s_banner" t-thumbnail="/website/static/src/img/snippets_thumbs/s_banner.svg">
                        <keywords>hero, jumbotron</keywords>
                    </t>
                    <t t-snippet="website.s_cover" t-thumbnail="/website/static/src/img/snippets_thumbs/s_cover.svg">
                        <keywords>hero, jumbotron</keywords>
                    </t>
                    <t t-snippet="website.s_text_image" t-thumbnail="/website/static/src/img/snippets_thumbs/s_text_image.svg">
                        <keywords>content</keywords>
                    </t>
                    <t t-snippet="website.s_image_text" t-thumbnail="/website/static/src/img/snippets_thumbs/s_image_text.svg">
                        <keywords>content</keywords>
                    </t>
                    <t t-snippet="website.s_title" t-thumbnail="/website/static/src/img/snippets_thumbs/s_title.svg">
                        <keywords>heading, h1</keywords>
                    </t>
                    <t t-snippet="website.s_text_block" t-thumbnail="/website/static/src/img/snippets_thumbs/s_text_block.svg">
                        <keywords>content</keywords>
                    </t>
                    <t t-snippet="website.s_numbers" t-thumbnail="/website/static/src/img/snippets_thumbs/s_numbers.svg">
                        <keywords>statistics, stats, KPI</keywords>
                    </t>
                    <t t-snippet="website.s_picture" t-thumbnail="/website/static/src/img/snippets_thumbs/s_picture.svg">
                        <keywords>image, media, illustration</keywords>
                    </t>
                    <t t-snippet="website.s_three_columns" t-thumbnail="/website/static/src/img/snippets_thumbs/s_three_columns.svg">
                        <keywords>columns, description</keywords>
                    </t>
                    <t t-snippet="website.s_color_blocks_2" t-thumbnail="/website/static/src/img/snippets_thumbs/s_color_blocks_2.svg">
                        <keywords>big</keywords>
                    </t>
                    <t t-snippet="website.s_features" t-thumbnail="/website/static/src/img/snippets_thumbs/s_features.svg">
                        <keywords>promotion, characteristic, quality</keywords>
                    </t>
                    <t t-snippet="website.s_masonry_block" t-thumbnail="/website/static/src/img/snippets_thumbs/s_masonry_block.svg">
                        <keywords>masonry, grid</keywords>
                    </t>
                    <t t-snippet="website.s_image_gallery" t-thumbnail="/website/static/src/img/snippets_thumbs/s_image_gallery.svg">
                        <keywords>gallery, carousel</keywords>
                    </t>
                    <t t-snippet="website.s_images_wall" t-thumbnail="/website/static/src/img/snippets_thumbs/s_images_wall.svg"/>
                    <t t-snippet="website.s_carousel" t-thumbnail="/website/static/src/img/snippets_thumbs/s_carousel.svg">
                        <keywords>gallery, carousel</keywords>
                    </t>
                    <t t-snippet="website.s_media_list" t-thumbnail="/website/static/src/img/snippets_thumbs/s_media_list.svg"/>
                    <t t-snippet="website.s_showcase" t-thumbnail="/website/static/src/img/snippets_thumbs/s_showcase.svg"/>
                    <t t-snippet="website.s_parallax" t-thumbnail="/website/static/src/img/snippets_thumbs/s_parallax.svg"/>
                </div>
            </div>

            <div id="snippet_feature" class="o_panel">
                <div class="o_panel_header">Features</div>
                <div class="o_panel_body">
                    <t t-snippet="website.s_comparisons" t-thumbnail="/website/static/src/img/snippets_thumbs/s_comparisons.svg">
                        <keywords>pricing</keywords>
                    </t>
                    <t t-snippet="website.s_company_team" t-thumbnail="/website/static/src/img/snippets_thumbs/s_company_team.svg">
                        <keywords>organization, structure</keywords>
                    </t>
                    <t t-snippet="website.s_call_to_action" t-thumbnail="/website/static/src/img/snippets_thumbs/s_call_to_action.svg">
                        <keywords>CTA</keywords>
                    </t>
                    <t t-snippet="website.s_references" t-thumbnail="/website/static/src/img/snippets_thumbs/s_references.svg">
                        <keywords>customers, clients</keywords>
                    </t>
                    <t t-snippet="website.s_faq_collapse" t-thumbnail="/website/static/src/img/snippets_thumbs/s_faq_collapse.svg">
                        <keywords>common answers, common questions</keywords>
                    </t>
                    <t t-snippet="website.s_features_grid" t-thumbnail="/website/static/src/img/snippets_thumbs/s_features_grid.svg"/>
                    <t t-snippet="website.s_table_of_content" t-thumbnail="/website/static/src/img/snippets_thumbs/s_table_of_content.svg"/>
                    <t t-snippet="website.s_product_catalog" t-thumbnail="/website/static/src/img/snippets_thumbs/s_product_catalog.svg">
                        <keywords>menu, pricing</keywords>
                    </t>
                    <t t-snippet="website.s_product_list" t-thumbnail="/website/static/src/img/snippets_thumbs/s_product_list.svg"/>
                    <t t-snippet="website.s_tabs" t-thumbnail="/website/static/src/img/snippets_thumbs/s_tabs.svg"/>
                    <t t-snippet="website.s_timeline" t-thumbnail="/website/static/src/img/snippets_thumbs/s_timeline.svg"/>
                    <t t-snippet="website.s_process_steps" t-thumbnail="/website/static/src/img/snippets_thumbs/s_process_steps.svg"/>
                    <t t-snippet="website.s_quotes_carousel" t-thumbnail="/website/static/src/img/snippets_thumbs/s_quotes_carousel.svg">
                        <keywords>testimonials</keywords>
                    </t>
                </div>
            </div>

            <div id="snippet_effect" class="o_panel">
                <div class="o_panel_header">Dynamic Content</div>
                <div class="o_panel_body">
                    <!-- This snippet cannot be used in sanitized fields -->
                    <!-- because it contains inputs that would be removed -->
                    <t t-snippet="website.s_website_form" t-thumbnail="/website/static/src/img/snippets_thumbs/s_website_form.svg" t-forbid-sanitize="form"/>
                    <t t-set="google_maps_api_key" t-value="request.env['website'].get_current_website().google_maps_api_key"/>
                    <t t-if="debug or not google_maps_api_key" t-snippet="website.s_map" t-thumbnail="/website/static/src/img/snippets_thumbs/s_map.svg"/>
                    <t t-if="debug or google_maps_api_key" t-snippet="website.s_google_map" t-thumbnail="/website/static/src/img/snippets_thumbs/s_google_map.svg"/>
                    <t t-if="debug" t-snippet="website.s_dynamic_snippet" t-thumbnail="/website/static/src/img/snippets_thumbs/s_dynamic_snippet.svg"/>
                    <t t-if="debug" t-snippet="website.s_dynamic_snippet_carousel" t-thumbnail="/website/static/src/img/snippets_thumbs/s_dynamic_carousel.svg"/>
                    <t id="sale_products_hook"/>
                    <!-- This snippet cannot be used in sanitized fields -->
                    <!-- because it contains an input that would be removed -->
                    <t t-snippet="website.s_searchbar" t-thumbnail="/website/static/src/img/snippets_thumbs/s_searchbar.svg" t-forbid-sanitize="form"/>
                    <t id="blog_posts_hook"/>
                    <t id="event_local_events_hook"/>
                    <t id="snippet_google_map_hook"/>
                    <t id="mass_mailing_newsletter_block_hook"/>
                    <t id="mass_mailing_newsletter_popup_hook"/>
                    <t t-snippet="website.s_popup" t-thumbnail="/website/static/src/img/snippets_thumbs/s_popup.svg"/>
                    <t t-snippet="website.s_facebook_page" t-thumbnail="/website/static/src/img/snippets_thumbs/s_facebook_page.svg"/>
                    <t t-snippet="website.s_countdown" t-thumbnail="/website/static/src/img/snippets_thumbs/s_countdown.svg">
                        <keywords>celebration, launch</keywords>
                    </t>
                    <t id="mail_group_hook"/>
                    <t id="twitter_favorite_tweets_hook"/>
                    <!-- This snippet cannot be used in sanitized fields -->
                    <!-- because it might have content that would be removed -->
                    <t t-snippet="website.s_embed_code" t-thumbnail="/website/static/src/img/snippets_thumbs/s_embed_code.svg" t-forbid-sanitize="true"/>
                    <t id="snippet_donation_hook"/>
                </div>
            </div>

            <div id="snippet_content" class="o_panel">
                <div class="o_panel_header">Inner content</div>
                <div class="o_panel_body">
                    <t t-snippet="website.s_hr" t-thumbnail="/website/static/src/img/snippets_thumbs/s_hr.svg">
                        <keywords>separator, divider</keywords>
                    </t>
                    <t t-snippet="website.s_alert" t-thumbnail="/website/static/src/img/snippets_thumbs/s_alert.svg"/>
                    <t t-snippet="website.s_rating" t-thumbnail="/website/static/src/img/snippets_thumbs/s_rating.svg">
                        <keywords>valuation, rank</keywords>
                    </t>
                    <t t-snippet="website.s_card" t-thumbnail="/website/static/src/img/snippets_thumbs/s_card.svg"/>
                    <t t-snippet="website.s_share" t-thumbnail="/website/static/src/img/snippets_thumbs/s_share.svg"/>
                    <!-- This snippet cannot be used in sanitized fields -->
                    <!-- because it contains an input that would be removed -->
                    <t t-snippet="website.s_searchbar_input" t-thumbnail="/website/static/src/img/snippets_thumbs/s_searchbar_inline.svg" t-forbid-sanitize="form"/>
                    <t id="mass_mailing_newsletter_hook"/>
                    <t t-snippet="website.s_text_highlight" t-thumbnail="/website/static/src/img/snippets_thumbs/s_text_highlight.svg"/>
                    <t t-snippet="website.s_chart" t-thumbnail="/website/static/src/img/snippets_thumbs/s_chart.svg">
                        <keywords>chart, table, diagram, pie</keywords>
                    </t>
                    <t t-snippet="website.s_progress_bar" t-thumbnail="/website/static/src/img/snippets_thumbs/s_progress_bar.svg">
                        <keywords>evolution, growth</keywords>
                    </t>
                    <t t-snippet="website.s_badge" t-thumbnail="/website/static/src/img/snippets_thumbs/s_badge.svg"/>
                    <t t-snippet="website.s_blockquote" t-thumbnail="/website/static/src/img/snippets_thumbs/s_blockquote.svg">
                        <keywords>cite</keywords>
                    </t>
                    <t id="event_speaker_bio_hook"/>
                    <t id="snippet_donation_button_hook"/>
                </div>
            </div>
        </t>
    </xpath>

    <xpath expr="//div[@id='snippet_options']/t" position="attributes">
        <attribute name="t-call">website.snippet_options</attribute>
    </xpath>
</template>

<template id="external_snippets" inherit_id="website.snippets" priority="8">
    <xpath expr="//div[@id='snippet_effect']//t[@t-snippet][last()]" position="after">
        <t id="newsletter_popup_snippet" t-install="mass_mailing" string="Newsletter Popup" t-thumbnail="/website/static/src/img/snippets_thumbs/newsletter_subscribe_popup.svg"/>
        <t t-install="website_mail_group" string="Discussion Group" t-thumbnail="/website/static/src/img/snippets_thumbs/s_group.svg"/>
        <t t-install="website_twitter" string="Twitter Scroller" t-thumbnail="/website/static/src/img/snippets_thumbs/s_twitter_scroll.svg"/>
        <t t-install="website_payment" string="Donation" t-thumbnail="/website/static/src/img/snippets_thumbs/s_donation.svg"/>
    </xpath>
    <xpath expr="//div[@id='snippet_content']//t[@t-snippet][last()]" position="after">
        <t id="newsletter_snippet" t-install="mass_mailing" string="Newsletter" t-thumbnail="/website/static/src/img/snippets_thumbs/s_newsletter_subscribe_form.svg"/>
        <t t-install="website_payment" string="Donation Button" t-thumbnail="/website/static/src/img/snippets_thumbs/s_donation_button.svg"/>
    </xpath>
</template>

<template id="snippet_options_background_options" inherit_id="web_editor.snippet_options_background_options">
    <xpath expr="//we-button[@data-toggle-bg-image]" position="after">
        <we-button title="Video" class="fa fa-fw fa-film"
                   data-name="bg_video_toggler_opt"
                   t-att-data-dependencies="images_dependencies"
                   data-toggle-bg-video="true"
                   data-no-preview="true"/>
    </xpath>
    <xpath expr="//t[@t-set='color_filter_dependencies']" position="after">
        <t t-set="color_filter_dependencies" t-valuef="#{color_filter_dependencies}, bg_video_toggler_opt"/>
    </xpath>
    <xpath expr="//div[@data-js='BackgroundOptimize']" position="after">
        <!-- Parallax -->
        <div data-js="Parallax"
             t-att-data-selector="selector"
             t-att-data-exclude="exclude"
             t-att-data-target="target">
            <!-- &emsp; -->
            <we-select string=" ⌙ Parallax"
                       data-attribute-name="scrollBackgroundRatio"
                       data-attribute-default-value="0"
                       data-parallax-type-opt="true"
                       data-no-preview="true"
                       data-dependencies="bg_image_opt">
                <we-button data-name="parallax_none_opt" data-select-data-attribute="0">None</we-button>
                <we-button data-select-data-attribute="1">Fixed</we-button>
                <we-button data-name="parallax_top_opt" data-select-data-attribute="1.5">Bottom to Top</we-button>
                <we-button data-name="parallax_bottom_opt" data-select-data-attribute="-1.5">Top to Bottom</we-button>
            </we-select>
            <we-range string="  ⌙ Intensity"
                      data-dependencies="parallax_top_opt"
                      data-select-data-attribute=""
                      data-attribute-name="scrollBackgroundRatio"
                      data-attribute-default-value="0"
                      data-no-preview="true"
                      data-min="0"
                      data-max="3"
                      data-step="0.15"/> <!-- Make sure this cannot land on 1 -->
            <we-range string="  ⌙ Intensity"
                      data-dependencies="parallax_bottom_opt"
                      data-select-data-attribute=""
                      data-attribute-name="scrollBackgroundRatio"
                      data-attribute-default-value="0"
                      data-no-preview="true"
                      data-min="0"
                      data-max="-3"
                      data-step="0.15"/> <!-- Make sure this cannot land on 1 -->
        </div>
        <div data-js="BackgroundVideo"
             t-att-data-selector="selector"
             t-att-data-exclude="exclude"
             t-att-data-target="target">
            <we-row string="⌙ Video">
                <we-videopicker title="Edit video"
                                data-background=""
                                data-name="bg_video_opt"
                                data-dependencies="bg_video_opt"/>
            </we-row>
        </div>
    </xpath>
</template>

<template id="snippet_options_border_line_widgets">
    <we-row t-att-string="label">
        <we-input data-name="border_width_opt"
                  t-att-data-apply-to="apply_to"
                  data-select-style="0"
                  t-attf-data-css-property="border-#{direction and ('%s-' % direction) or ''}width"
                  data-unit="px"
                  t-att-data-extra-class="with_bs_class and 'border'"
                  t-att-data-variable="width_variable"/>
        <we-select t-attf-data-css-property="border-#{direction and ('%s-' % direction) or ''}style"
                   data-dependencies="border_width_opt"
                   t-att-data-apply-to="apply_to"
                   t-att-data-variable="style_variable">
            <we-button title="Solid" data-select-style="solid"><div class="o_we_fake_img_item o_we_border_preview" style="border-style: solid;"/></we-button>
            <we-button title="Dashed" data-select-style="dashed"><div class="o_we_fake_img_item o_we_border_preview" style="border-style: dashed;"/></we-button>
            <we-button title="Dotted" data-select-style="dotted"><div class="o_we_fake_img_item o_we_border_preview" style="border-style: dotted;"/></we-button>
            <we-button title="Double" data-select-style="double"><div class="o_we_fake_img_item o_we_border_preview" style="border-style: double; border-left: none; border-right: none;"/></we-button>
        </we-select>
        <we-colorpicker data-dependencies="border_width_opt"
                        t-att-data-apply-to="apply_to"
                        data-select-style="true"
                        t-attf-data-css-property="border-#{direction and ('%s-' % direction) or ''}color"
                        data-color-prefix="border-"
                        t-att-data-color="color_variable"/>
    </we-row>
</template>

<template id="snippet_options_border_widgets">
    <t t-call="website.snippet_options_border_line_widgets">
        <t t-set="label">Border</t>
        <t t-set="with_bs_class" t-value="True"/>
    </t>
    <we-input string="Round Corners"
              t-att-data-apply-to="apply_to"
              t-att-data-dependencies="not so_rounded_no_dependencies and 'border_width_opt,bg_color_opt'"
              data-select-style="0" data-css-property="border-radius"
              data-unit="px" data-extra-class="rounded"
              t-att-data-variable="radius_variable"/>
</template>

<template id="snippet_options_shadow_widgets">
    <we-button-group string="Shadow" data-shadow-class="shadow" t-att-data-variable="shadow_variable">
        <we-button data-set-shadow="">None</we-button>
        <we-button data-set-shadow="outset"
                   data-img="/website/static/src/img/snippets_options/shadow_out.svg"
                   data-name="shadow_active_opt"
                   title="Outset"/>
        <we-button data-set-shadow="inset"
                   data-img="/website/static/src/img/snippets_options/shadow_in.svg"
                   data-name="shadow_inset_opt"
                   title="Inset"/>
    </we-button-group>
    <we-multi data-css-property="box-shadow" data-dependencies="shadow_active_opt, shadow_inset_opt" t-att-data-variable="shadow_variable">
        <we-colorpicker string="⌙ Color" data-select-style="" data-css-compatible=""/>
        <we-row string="⌙ Offset (X, Y)">
            <we-input data-select-style="" data-unit="px"/>
            <we-input data-select-style="" data-unit="px"/>
        </we-row>
        <we-input string="⌙ Blur" data-select-style="" data-unit="px"/>
        <we-input string="⌙ Spread" data-select-style="" data-unit="px"/>
        <!-- Inset parameter always hidden (as controlled above) but needed -->
        <!-- for the we-multi widget to work properly. -->
        <we-checkbox data-name="fake_inset_shadow_opt" data-select-style="inset"/>
    </we-multi>
</template>

<template id="snippet_options_conditional_visibility">
    <we-select t-att-string="option_name" t-att-data-attribute-name="attribute_rule" data-no-preview="true" data-dependencies="visibility_conditional" data-is-visibility-condition="true">
        <we-button data-select-data-attribute="">Visible for</we-button>
        <we-button data-select-data-attribute="hide">Hidden for</we-button>
    </we-select>
    <we-many2many string=" "
        data-dependencies="visibility_conditional"
        t-att-data-save-attribute="save_attribute"
        t-att-data-attribute-name="attribute_name"
        data-no-preview="true"
        t-att-data-model="model" t-att-data-call-with="call_with" data-select-record="" t-att-data-fields="data_fields" t-att-data-domain="domain"
        data-allow-delete="true" data-fakem2m="true"/>
</template>

<template id="snippet_options_header_brand">
    <we-select t-att-string="label" data-reload="/" t-att-data-dependencies="dependencies">
        <we-button data-customize-website-views="" data-name="option_header_brand_none">None</we-button>
        <we-button data-customize-website-views="website.option_header_brand_name">Text</we-button>
        <we-button data-customize-website-views="website.option_header_brand_logo">Image</we-button>
    </we-select>
</template>

<template id="snippet_options">
    <t t-call="web_editor.snippet_options"/>

    <!-- COLOR, BORDER, SHADOW | .s_three_columns | .s_comparisons -->
    <div data-js="Box"
         data-selector=".s_three_columns .row > div, .s_comparisons .row > div"
         data-target=".card">
        <we-colorpicker string="Colors"
            data-select-style="true"
            data-css-property="background-color"
            data-color-prefix="bg-"/>
        <t t-call="website.snippet_options_border_widgets">
            <t t-set="so_rounded_no_dependencies" t-value="True"/>
        </t>
        <t t-call="website.snippet_options_shadow_widgets"/>
    </div>

    <!-- COLOR | .s_cards -->
    <div data-selector=".s_card, .accordion .card">
        <we-colorpicker string="Color"
            data-select-style="true"
            data-css-property="background-color"
            data-color-prefix="bg-"/>
    </div>

    <!-- H-ALIGN -->
    <div id="so_text_align" data-selector=".s_share, .s_text_highlight">
        <we-button-group string="Alignment">
            <we-button class="fa fa-fw fa-align-left" title="Left" data-select-class="text-left"/>
            <we-button class="fa fa-fw fa-align-center" title="Center" data-select-class="text-center"/>
            <we-button class="fa fa-fw fa-align-right" title="Right" data-select-class="text-right"/>
        </we-button-group>
    </div>

    <div id="so_width" data-selector=".s_alert, .s_card, .s_blockquote, .s_text_highlight">
        <we-select string="Width">
            <we-button data-select-class="w-25">25%</we-button>
            <we-button data-select-class="w-50">50%</we-button>
            <we-button data-select-class="w-75">75%</we-button>
            <we-button data-select-class="w-100" data-name="so_width_100">100%</we-button>
        </we-select>
    </div>

    <div id="so_block_align" data-selector=".s_alert, .s_card, .s_blockquote, .s_text_highlight">
        <we-button-group string="Alignment" data-dependencies="!so_width_100">
            <we-button class="fa fa-fw fa-align-left" title="Left" data-select-class="mr-auto"/>
            <we-button class="fa fa-fw fa-align-center" title="Center" data-select-class="mx-auto"/>
            <we-button class="fa fa-fw fa-align-right" title="Right" data-select-class="ml-auto"/>
        </we-button-group>
    </div>

    <!-- Carousel | .s_carousel | .s_quotes_carousel -->
    <!-- Dedicated colorpicker so that there is not 3 level of
         o_colored_level. Use inline-style color for the same reason. -->
    <t t-call="web_editor.snippet_options_background_options">
        <t t-set="selector" t-value="'section'"/>
        <t t-set="target" t-value="'> .carousel'"/>
        <t t-set="with_colors" t-value="True"/>
        <t t-set="with_images" t-value="True"/>
        <t t-set="css_compatible" t-value="True"/>
        <t t-set="with_gradients" t-value="True"/>
    </t>
    <div data-js="Carousel"
         data-selector="section"
         data-target="> .carousel">
        <we-row string="Slide">
            <we-button data-add-slide="true" data-no-preview="true" class="o_we_bg_brand_primary">Add Slide</we-button>
        </we-row>
        <we-select string="Style">
            <we-button data-select-class="s_carousel_default">Default</we-button>
            <we-button data-select-class="s_carousel_bordered">Bordered</we-button>
            <we-button data-select-class="s_carousel_boxed">Boxed</we-button>
            <we-button data-select-class="s_carousel_rounded">Rounded</we-button>
        </we-select>
        <we-select string="Transition">
            <we-button data-select-class="slide">Slide</we-button>
            <we-button data-select-class="carousel-fade slide">Fade</we-button>
            <we-divider/>
            <we-button data-select-class="">None</we-button>
        </we-select>
        <we-input string="Speed"
                  data-select-data-attribute="0s" data-attribute-name="interval"
                  data-unit="s" data-save-unit="ms" data-step="0.1"/>
    </div>

    <div data-js="CarouselItem"
         data-selector=".s_carousel .carousel-item, .s_quotes_carousel .carousel-item">
        <we-button class="fa fa-fw fa-angle-left" data-slide="left" data-no-preview="true" data-tooltip="true" title="Move Backward"/>
        <we-button class="fa fa-fw fa-angle-right mr-2" data-slide="right" data-no-preview="true" data-tooltip="true" title="Move Forward"/>
        <we-button class="fa fa-fw fa-plus o_we_bg_success" data-add-slide-item="true" data-no-preview="true" data-tooltip="true" title="Add Slide"/>
        <we-button class="fa fa-fw fa-minus o_we_bg_danger" data-remove-slide="true" data-no-preview="true" data-tooltip="true" title="Remove Slide"/>
    </div>

    <!-- Accordion -->
    <div data-js="collapse"
         data-selector='.accordion > .card'
         data-drop-in='.accordion:has(> .card)'/>

    <div data-js="MultipleItems" data-selector=".s_faq_collapse">
        <we-row string="Item">
            <we-button data-add-item="" data-item=".accordion > .card:last" data-select-item="" data-no-preview="true" class="o_we_bg_brand_primary">
                Add Item
            </we-button>
        </we-row>
    </div>

    <div data-js="layout_column"
        data-selector="section"
        data-target="> *:has(> .row:not(.s_nb_column_fixed)), > .s_allow_columns"
        data-exclude=".s_masonry_block">
        <we-select string="Columns" data-no-preview="true">
            <we-button data-select-count="0" data-name="zero_cols_opt">None</we-button>
            <we-button data-select-count="1">1</we-button>
            <we-button data-select-count="2">2</we-button>
            <we-button data-select-count="3">3</we-button>
            <we-button data-select-count="4">4</we-button>
            <we-button data-select-count="5">5</we-button>
            <we-button data-select-count="6">6</we-button>
        </we-select>
    </div>

    <!-- Move snippets around -->
    <div data-js="SnippetMove" data-selector="section, .accordion > .card, .s_showcase .row:not(.s_col_no_resize) > div" data-no-scroll=".accordion > .card">
        <we-button class="fa fa-fw fa-angle-up" data-move-snippet="prev" data-no-preview="true" data-name="move_up_opt"/>
        <we-button class="fa fa-fw fa-angle-down" data-move-snippet="next" data-no-preview="true" data-name="move_down_opt"/>
    </div>
    <div data-js="SnippetMove"
         data-selector=".row:not(.s_col_no_resize) > div, .nav-item"
         data-exclude=".s_showcase .row > div"
         data-name="move_horizontally_opt">
        <we-button class="fa fa-fw fa-angle-left" data-move-snippet="prev" data-no-preview="true" data-name="move_left_opt"/>
        <we-button class="fa fa-fw fa-angle-right" data-move-snippet="next" data-no-preview="true" data-name="move_right_opt"/>
    </div>

    <!-- Background -->
    <t t-set="only_bg_color_selector" t-value="'section .row > div, .s_text_highlight, .s_mega_menu_thumbnails_footer'"/>
    <t t-set="only_bg_color_exclude" t-value="'.s_col_no_bgcolor, .s_col_no_bgcolor.row > div, .s_masonry_block .row > div, .s_color_blocks_2 .row > div, .s_image_gallery .row > div'"/>

    <t t-set="base_only_bg_image_selector" t-value="'.s_tabs .oe_structure > *, footer .oe_structure > *'"/>
    <t t-set="only_bg_image_selector" t-value="base_only_bg_image_selector"/>
    <t t-set="only_bg_image_exclude" t-value="''"/>

    <t t-set="both_bg_color_image_selector" t-value="'section, .carousel-item, .s_masonry_block .row > div, .s_color_blocks_2 .row > div, .parallax'"/>
    <t t-set="both_bg_color_image_exclude" t-value="base_only_bg_image_selector + ', .s_carousel_wrapper, .s_color_blocks_2, .s_image_gallery .carousel-item, .s_google_map, .s_map, [data-snippet] :not(.oe_structure) > [data-snippet], .s_masonry_block, .s_masonry_block .s_col_no_resize'"/>

    <t t-call="web_editor.snippet_options_background_options">
        <t t-set="selector" t-value="both_bg_color_image_selector"/>
        <t t-set="exclude" t-value="both_bg_color_image_exclude"/>
        <t t-set="with_colors" t-value="True"/>
        <t t-set="with_images" t-value="True"/>
        <t t-set="with_color_combinations" t-value="True"/>
        <t t-set="with_gradients" t-value="True"/>
    </t>

    <t t-call="web_editor.snippet_options_background_options">
        <t t-set="selector" t-value="only_bg_color_selector"/>
        <t t-set="exclude" t-value="only_bg_color_exclude"/>
        <t t-set="with_colors" t-value="True"/>
        <t t-set="with_images" t-value="False"/>
        <t t-set="with_color_combinations" t-value="True"/>
        <t t-set="with_gradients" t-value="True"/>
    </t>

    <t t-call="web_editor.snippet_options_background_options">
        <t t-set="selector" t-value="only_bg_image_selector"/>
        <t t-set="exclude" t-value="only_bg_image_exclude"/>
        <t t-set="with_colors" t-value="False"/>
        <t t-set="with_images" t-value="True"/>
    </t>

    <!-- Border | Columns -->
    <div data-js="Box"
         data-selector="section .row > div"
         data-exclude=".s_col_no_bgcolor, .s_col_no_bgcolor.row > div, .s_image_gallery .row > div, .s_masonry_block .s_col_no_resize">
        <t t-call="website.snippet_options_border_widgets"/>
        <t t-call="website.snippet_options_shadow_widgets"/>
    </div>

    <!-- Mobile display options -->
    <div data-js="MobileVisibility" data-selector="section, section .row > div"
         data-exclude=".s_col_no_resize.row > div, .s_masonry_block .s_col_no_resize">
        <we-button class="o_we_link o_we_mobile" data-show-on-mobile="true" data-no-preview="true"
                   data-img="/website/static/src/img/snippets_options/mobile_invisible.svg"
                   data-active-img="/website/static/src/img/snippets_options/mobile_visible.svg"/>
    </div>

    <div data-js="sizing_y"
        data-selector="section, .row > div, .parallax, .s_hr, .carousel-item, .s_rating"
        data-exclude="section:has(> .carousel), .s_image_gallery .carousel-item, .s_col_no_resize.row > div, .s_col_no_resize"/>

    <div data-js="sizing_x"
        data-selector=".row > div"
        data-drop-near=".row:not(.s_col_no_resize) > div"
        data-exclude=".s_col_no_resize.row > div, .s_col_no_resize"/>

    <t t-set="so_snippet_addition_selector" t-translation="off">section, .parallax, .s_popup</t>
    <div id="so_snippet_addition"
        t-att-data-selector="so_snippet_addition_selector"
        data-drop-in=":not(p).oe_structure:not(.oe_structure_solo), :not(.o_mega_menu):not(p)[data-oe-type=html], :not(p).oe_structure.oe_structure_solo:not(:has(> section, > div))"/>

    <t t-set="so_content_addition_selector" t-translation="off">blockquote, .s_card:not(.s_timeline_card), .s_alert, .o_facebook_page, .s_share, .s_rating, .s_hr, .s_google_map, .s_map, .s_countdown, .s_chart, .s_text_highlight, .s_progress_bar, .s_badge, .s_embed_code, .s_donation</t>
    <div id="so_content_addition"
        t-att-data-selector="so_content_addition_selector"
        t-attf-data-drop-near="p, h1, h2, h3, ul, ol, .row > div > img, #{so_content_addition_selector}"
        data-drop-in=".content, nav"/>
    <!-- TODO in master: remove the `.content` above, as it will not refer to
        anything when the `content` classes in carousel will be removed. -->

    <div data-js="SnippetSave"
        t-attf-data-selector="#{so_snippet_addition_selector}, #{so_content_addition_selector}"
        data-exclude=".o_no_save">
        <we-button class="fa fa-fw fa-save o_we_link o_we_hover_warning"
                   title="Save the block to use it elsewhere"
                   data-save-snippet=""
                   data-no-preview="true"/>
    </div>

    <div data-js="menu_data"
         data-selector="#top_menu li > a, [data-content_menu_id] li > a"
         data-exclude=".dropdown-toggle, li.o_header_menu_button a, [data-toggle], .o_offcanvas_logo"
         data-no-check="true"/>

    <div data-js="company_data"
         data-selector="[data-oe-expression='res_company.partner_id']"
         data-no-check="true"/>

    <div data-js="WebsiteLevelColor"
         data-selector="#wrapwrap > header"
         data-no-check="true"
         groups="website.group_website_designer">
        <we-select string="Template" data-variable="header-template" data-reload="/">
            <we-button title="Default"
                       data-name="header_default_opt"
                       data-customize-website-views="website.template_header_default"
                       data-customize-website-variable="'default'"
                       data-img="/website/static/src/img/snippets_options/header_template_default.svg"/>
            <we-button title="Hamburger menu"
                       data-name="header_hamburger_opt"
                       data-customize-website-views="website.template_header_hamburger"
                       data-customize-website-variable="'hamburger'"
                       data-img="/website/static/src/img/snippets_options/header_template_hamburger.svg"/>
            <we-button title="Vertical"
                       data-name="header_vertical_opt"
                       data-customize-website-views="website.template_header_vertical"
                       data-customize-website-variable="'vertical'"
                       data-img="/website/static/src/img/snippets_options/header_template_vertical.svg"/>
            <we-button title="Sidebar"
                       data-name="header_sidebar_opt"
                       data-customize-website-views="website.template_header_sidebar, website.no_autohide_menu"
                       data-customize-website-variable="'sidebar'"
                       data-img="/website/static/src/img/snippets_options/header_template_sidebar.svg"/>
            <we-button title="Slogan"
                       data-name="header_slogan_opt"
                       data-customize-website-views="website.template_header_slogan"
                       data-customize-website-variable="'slogan'"
                       data-img="/website/static/src/img/snippets_options/header_template_slogan.svg"/>
            <we-button title="Contact"
                       data-name="header_contact_opt"
                       data-customize-website-views="website.template_header_contact"
                       data-customize-website-variable="'Contact'"
                       data-img="/website/static/src/img/snippets_options/header_template_contact.svg"/>
            <we-button title="Boxed"
                       data-name="header_boxed_opt"
                       data-customize-website-views="website.template_header_boxed"
                       data-customize-website-variable="'boxed'"
                       data-img="/website/static/src/img/snippets_options/header_template_boxed.svg"/>
            <we-button title="Centered Logo"
                       data-name="header_centered_logo_opt"
                       data-customize-website-views="website.template_header_centered_logo"
                       data-customize-website-variable="'centered_logo'"
                       data-img="/website/static/src/img/snippets_options/header_template_centered_logo.svg"/>
            <we-button title="Image"
                       data-name="header_image_opt"
                       data-customize-website-views="website.template_header_image"
                       data-customize-website-variable="'image'"
                       data-img="/website/static/src/img/snippets_options/header_template_image.svg"/>
            <we-button title="Hamburger Full"
                       data-name="header_hamburger_full_opt"
                       data-customize-website-views="website.template_header_hamburger_full"
                       data-customize-website-variable="'hamburger-full'"
                       data-img="/website/static/src/img/snippets_options/header_template_hamburger_full.svg"/>
            <we-button title="Magazine"
                       data-name="header_magazine_opt"
                       data-customize-website-views="website.template_header_magazine"
                       data-customize-website-variable="'magazine'"
                       data-img="/website/static/src/img/snippets_options/header_template_magazine.svg"/>
        </we-select>

        <!-- Header Sidebar Template - Options -->
        <we-input string="⌙ Width"
                  data-dependencies="header_sidebar_opt"
                  data-customize-website-variable="null"
                  data-variable="sidebar-width"
                  data-unit="px"
                  data-save-unit="rem"/>

        <we-row string="Colors">
            <we-colorpicker data-customize-website-color="" data-color="menu"
                            data-customize-website-layer2-color="" data-layer-color="menu-custom" data-layer-gradient="menu-gradient"
                            data-no-bundle-reload="true"
                            data-null-value="'NULL'"
                            data-with-combinations="customizeWebsiteColor"
                            data-with-gradients="true"/>
            <we-colorpicker data-dependencies="header_boxed_opt"
                            data-customize-website-color="" data-color="header-boxed"
                            data-customize-website-layer2-color="" data-layer-color="header-boxed-custom" data-layer-gradient="header-boxed-gradient"
                            data-no-bundle-reload="true"
                            data-null-value="'NULL'"
                            data-with-combinations="customizeWebsiteColor"
                            data-with-gradients="true"/>
        </we-row>
    </div>

    <!-- Header - Borders & Shadows -->
    <div data-js="HeaderBox"
         id="option_header_shadow"
         data-selector="#wrapwrap > header"
         data-target="nav"
         data-no-check="true"
         groups="website.group_website_designer">
        <t t-call="website.snippet_options_border_widgets">
            <t t-set="so_rounded_no_dependencies" t-value="True"/>
            <t t-set="width_variable" t-value="'menu-border-width'"/>
            <t t-set="style_variable" t-value="'menu-border-style'"/>
            <t t-set="color_variable" t-value="'menu-border-color'"/>
            <t t-set="radius_variable" t-value="'menu-border-radius'"/>
        </t>
        <t t-call="website.snippet_options_shadow_widgets">
            <t t-set="shadow_variable" t-value="'menu-box-shadow'"/>
        </t>
    </div>

    <div data-selector="#wrapwrap > header"
         data-no-check="true"
         groups="website.group_website_designer">

        <we-select string="Scroll Effect" data-dependencies="!header_sidebar_opt" class="o_scroll_effects_selector">
            <t t-set="header_effect_standard_label">Standard</t>
            <t t-set="header_effect_scroll_label">Scroll</t>
            <t t-set="header_effect_fixed_label">Fixed</t>
            <t t-set="header_effect_disappears_label">Disappears</t>
            <t t-set="header_effect_fadeout_label">Fade Out</t>
            <we-button id="option_header_visibility_standard"
                       t-att-data-select-label="header_effect_standard_label"
                       class="o_we_img_animate"
                       data-name="header_visibility_standard_opt"
                       data-select-class="o_header_standard"
                       data-customize-website-views="website.header_visibility_standard"
                       data-img="/website/static/src/img/snippets_options/header_effect_standard.png">
                <span t-esc='header_effect_standard_label'/>
            </we-button>
            <we-button id="option_header_effect_scroll"
                       t-att-data-select-label="header_effect_scroll_label"
                       class="o_we_img_animate"
                       data-name="header_effect_scroll_opt"
                       data-select-class=""
                       data-customize-website-views=""
                       data-img="/website/static/src/img/snippets_options/header_effect_scroll.png">
                <span t-esc='header_effect_scroll_label'/>
            </we-button>
            <we-button id="option_header_effect_fixed"
                       t-att-data-select-label="header_effect_fixed_label"
                       class="o_we_img_animate"
                       data-name="header_effect_fixed_opt"
                       data-select-class="o_header_fixed"
                       data-customize-website-views="website.header_visibility_fixed"
                       data-img="/website/static/src/img/snippets_options/header_effect_fixed.png">
                <span t-esc='header_effect_fixed_label'/>
            </we-button>
            <we-button id="option_header_effect_disappears"
                       t-att-data-select-label="header_effect_disappears_label"
                       class="o_we_img_animate"
                       data-name="header_effect_disappears_opt"
                       data-select-class="o_header_disappears"
                       data-customize-website-views="website.header_visibility_disappears"
                       data-img="/website/static/src/img/snippets_options/header_effect_disappears.png">
                <span t-esc="header_effect_disappears_label" />
            </we-button>
            <we-button id="option_header_effect_fade_out"
                       t-att-data-select-label="header_effect_fadeout_label"
                       class="o_we_img_animate"
                       data-name="header_effect_fade_out_opt"
                       data-select-class="o_header_fade_out"
                       data-customize-website-views="website.header_visibility_fade_out"
                       data-img="/website/static/src/img/snippets_options/header_effect_fade_out.png">
               <span t-esc='header_effect_fadeout_label'/>
            </we-button>
        </we-select>
    </div>

    <div data-js="TopMenuVisibility"
         data-selector="[data-main-object^='website.page('] #wrapwrap > header"
         data-no-check="true">
        <we-select string="Header Position" id="option_header_visibility" data-no-preview="true">
            <we-button data-visibility="transparent">Over The Content</we-button>
            <we-button data-name="regular_header_visibility_opt"
                       data-visibility="regular">Regular</we-button>
            <we-button data-visibility="hidden">Hidden</we-button>
        </we-select>
    </div>

    <div data-js="topMenuColor"
         data-selector="[data-main-object^='website.page('] #wrapwrap > header"
         data-no-check="true">
        <we-colorpicker string="⌙ Background"
            id="option_header_transparent_color"
            data-select-style="true"
            data-css-property="background-color"
            data-color-prefix="bg-"
            data-excluded="theme, common, custom"/>
    </div>

    <!-- Header > Navbar Options -->
    <div data-js="HeaderNavbar"
         data-selector="#wrapwrap > header nav.navbar"
         data-no-check="true"
         groups="website.group_website_designer">
        <!-- Generic alignment option controling all the template at once. -->
        <!-- Currently needed to be this way as the SCSS variable controls -->
        <!-- the mobile alignement which is the same for all templates. -->
        <we-select string="Alignment"
                   data-name="header_alignment_opt"
                   data-variable="hamburger-position"
                   data-reload="/">
            <we-button data-customize-website-views="" data-customize-website-variable="'left'">Left</we-button>
            <we-button data-customize-website-views="website.template_header_default_align_center, website.template_header_hamburger_align_center, website.template_header_slogan_align_center" data-customize-website-variable="'center'">Center</we-button>
            <we-button data-customize-website-views="website.template_header_default_align_right, website.template_header_hamburger_align_right, website.template_header_slogan_align_right" data-customize-website-variable="'right'">Right</we-button>
        </we-select>

        <!-- Generic Hamburger Options (mobile or not) -->
        <we-select string="Hamburger Type"
                   data-name="header_hamburger_type_opt"
                   data-variable="hamburger-type"
                   data-reload="/">
            <we-button data-name="default_hamburger_opt"
                       data-customize-website-variable="'default'"
                       data-customize-website-views="">Default</we-button>
            <we-button data-name="off_canvas_menu_opt"
                       data-customize-website-variable="'off-canvas'"
                       data-customize-website-views="website.option_header_off_canvas, website.option_header_off_canvas_template_header_hamburger, website.option_header_off_canvas_template_header_sidebar, website.option_header_off_canvas_template_header_hamburger_full">Off-Canvas</we-button>
            <we-button data-name="no_hamburger_opt"
                       data-customize-website-variable="'no-hamburger'"
                       data-customize-website-views="website.option_header_no_mobile_hamburger">Same as desktop</we-button>
        </we-select>

        <we-select string="⌙ Off-Canvas Logo" data-dependencies="off_canvas_menu_opt" data-reload="/">
            <we-button data-customize-website-views="">Text</we-button>
            <we-button data-customize-website-views="website.option_header_off_canvas_logo_show">Image</we-button>
        </we-select>

        <we-row string="Font">
            <we-fontfamilypicker data-variable="navbar-font"/>
            <we-input data-customize-website-variable="null" data-variable="header-font-size" data-unit="px" data-save-unit="rem"/>
        </we-row>

        <we-select string="Links Style" data-variable="header-links-style" data-reload="/">
            <we-button data-name="option_header_navbar_links_default"
                       data-customize-website-views=""
                       data-customize-website-variable="'default'">Default</we-button>
            <we-button data-name="option_header_navbar_links_fill"
                       data-customize-website-views="website.header_navbar_pills_style"
                       data-customize-website-variable="'fill'">Fill</we-button>
            <we-button data-name="option_header_navbar_links_outline"
                       data-customize-website-views=""
                       data-customize-website-variable="'outline'">Outline</we-button>
            <we-button data-name="option_header_navbar_links_pills"
                       data-customize-website-views="website.header_navbar_pills_style"
                       data-customize-website-variable="'pills'">Pills</we-button>
            <we-button data-name="option_header_navbar_block"
                       data-customize-website-views="website.header_navbar_pills_style"
                       data-customize-website-variable="'block'">Block</we-button>
            <we-button data-name="option_header_navbar_border_bottom"
                       data-customize-website-views=""
                       data-customize-website-variable="'border-bottom'">Border Bottom</we-button>
        </we-select>

        <we-select id="option_header_dropdown" string="Sub Menus" data-dependencies="!header_hamburger_opt" data-no-preview="true">
            <we-button data-select-class="o_hoverable_dropdown"
                       data-customize-website-views="website.header_hoverable_dropdown">On Hover</we-button>
            <we-button data-select-class="" data-name="header_dropdown_on_click_opt">On Click</we-button>
        </we-select>

        <we-checkbox string="Show Sign In" data-customize-website-views="portal.user_sign_in" data-reload="/" data-no-preview="true"/>
        <we-checkbox string="Call to Action" data-customize-website-views="website.header_call_to_action"
                     data-reset-view-arch="true" data-reload="/"/>
        <we-select string="Language Selector" data-reload="/">
            <we-button data-name="header_language_selector_none_opt"
                       data-customize-website-views="">None</we-button>
            <we-button data-customize-website-views="website.header_language_selector">Dropdown</we-button>
            <we-button data-customize-website-views="website.header_language_selector, website.header_language_selector_inline">Inline</we-button>
        </we-select>
        <we-select string="⌙ Label" data-dependencies="!header_language_selector_none_opt" data-reload="/">
            <we-button data-customize-website-views="">Text</we-button>
            <we-button data-customize-website-views="website.header_language_selector_flag, website.header_language_selector_no_text">Flag</we-button>
            <we-button data-customize-website-views="website.header_language_selector_flag">Flag and Text</we-button>
        </we-select>
        <t t-call="website.snippet_options_header_brand">
            <t t-set="label">Logo Type</t>
            <t t-set="dependencies" t-valuef="option_header_brand_none"/>
        </t>
    </div>
    <!-- TODO adapt in master, the logo image options related to type and -->
    <!-- height are moved back in the navbar section in JS -->
    <div data-selector="#wrapwrap > header nav.navbar .navbar-brand img" data-no-check="true">
        <t t-call="website.snippet_options_header_brand">
            <t t-set="label">Type</t>
        </t>

        <we-input string="Height"
                  data-dependencies="!option_header_brand_none"
                  data-customize-website-variable="null"
                  data-variable="logo-height"
                  data-unit="px"
                  data-save-unit="rem"/>
        <we-input string="Height (Scrolled)"
                  data-name="option_logo_height_scrolled"
                  data-customize-website-variable="null"
                  data-variable="fixed-logo-height"
                  data-unit="px"
                  data-save-unit="rem"
                  data-dependencies="!header_effect_scroll_opt"/>
    </div>

    <!-- Footer - Colors & Layouts -->
    <div data-js="WebsiteLevelColor"
         data-selector="#wrapwrap > footer"
         data-no-check="true"
         groups="website.group_website_designer">

        <!-- Layouts -->
        <we-select string="Template"
            data-variable="footer-template"
            data-reload="/">
            <we-button title="Default"
                       data-customize-website-views="website.footer_custom"
                       data-customize-website-variable="'default'"
                       data-img="/website/static/src/img/snippets_options/footer_template_default.svg"/>
            <we-button title="Descriptive"
                       data-customize-website-views="website.template_footer_descriptive"
                       data-customize-website-variable="'descriptive'"
                       data-img="/website/static/src/img/snippets_options/footer_template_descriptive.svg"/>
            <we-button title="Centered"
                       data-customize-website-views="website.template_footer_centered"
                       data-customize-website-variable="'centered'"
                       data-img="/website/static/src/img/snippets_options/footer_template_centered.svg"/>
            <we-button title="Links"
                       data-customize-website-views="website.template_footer_links"
                       data-customize-website-variable="'links'"
                       data-img="/website/static/src/img/snippets_options/footer_template_links.svg"/>
            <we-button title="Minimalist"
                       data-customize-website-views="website.template_footer_minimalist"
                       data-customize-website-variable="'minimalist'"
                       data-img="/website/static/src/img/snippets_options/footer_template_minimalist.svg"/>
            <we-button title="Contact"
                       data-customize-website-views="website.template_footer_contact"
                       data-customize-website-variable="'contact'"
                       data-img="/website/static/src/img/snippets_options/footer_template_contact.svg"/>
            <we-button title="Call-to-action"
                       data-customize-website-views="website.template_footer_call_to_action"
                       data-customize-website-variable="'call_to_action'"
                       data-img="/website/static/src/img/snippets_options/footer_template_call_to_action.svg"/>
            <we-button title="Headline"
                       data-customize-website-views="website.template_footer_headline"
                       data-customize-website-variable="'headline'"
                       data-img="/website/static/src/img/snippets_options/footer_template_headline.svg"/>
        </we-select>
        <!-- Colors -->
        <we-colorpicker string="Colors"
                        data-customize-website-color="" data-color="footer"
                        data-customize-website-layer2-color="" data-layer-color="footer-custom" data-layer-gradient="footer-gradient"
                        data-no-bundle-reload="true"
                        data-null-value="'NULL'"
                        data-with-combinations="customizeWebsiteColor"
                        data-with-gradients="true"/>
        <we-select string="Slideout Effect" data-variable="footer-effect" data-reload="/">
            <we-button string="Regular"
                       data-customize-website-views=""
                       data-customize-website-variable=""/>
            <we-button string="Slide Hover"
                       data-customize-website-views="website.template_footer_slideout"
                       data-customize-website-variable="'slideout_slide_hover'"/>
            <we-button string="Shadow"
                       data-customize-website-views="website.template_footer_slideout"
                       data-customize-website-variable="'slideout_shadow'"/>
        </we-select>
        <we-checkbox string="Copyright"
                data-name="footer_copyright_opt"
                data-customize-website-views="website.footer_no_copyright|"
                data-no-preview="true"
                data-reload="/"/>
        <we-checkbox string="Language Selector"
                data-dependencies="!footer_copyright_opt"
                data-customize-website-views="website.footer_no_copyright|portal.footer_language_selector"
                data-no-preview="true"
                data-reload="/"/>
    </div>

    <!-- Footer - Borders & Shadows -->
    <div data-js="Box"
         data-selector="#wrapwrap > footer"
         data-target="#footer"
         data-no-check="true"
         groups="website.group_website_designer">
        <t t-call="website.snippet_options_border_widgets"/>
        <t t-call="website.snippet_options_shadow_widgets"/>
    </div>

    <!-- Scroll to Top -->
    <div data-selector="#wrapwrap > footer"
         data-no-check="true"
         groups="website.group_website_designer">
        <we-row string="Scroll Top Button">
            <we-checkbox data-name="footer_scrolltop_opt"
                         data-customize-website-views="website.option_footer_scrolltop"
                         data-customize-website-variable="false|true"
                         data-variable="footer-scrolltop"
                         data-reload="/"/>
            <we-select data-dependencies="footer_scrolltop_opt" data-apply-to="#o_footer_scrolltop_wrapper">
                <we-button string="Left" data-select-class="justify-content-start"/>
                <we-button string="Center" data-select-class="justify-content-center"/>
                <we-button string="Right" data-select-class="justify-content-end"/>
            </we-select>
        </we-row>
    </div>

    <div data-js="HideFooter"
        data-selector="[data-main-object^='website.page('] #wrapwrap > footer"
        data-no-check="true"
        groups="website.group_website_designer">
        <we-checkbox string="Page Visibility"
                     data-name="hide_footer_page_opt"
                     data-visibility="hidden|shown"
                     data-no-preview="true"/>
    </div>

    <!-- Copyright -->
    <div data-js="WebsiteLevelColor"
         data-selector=".o_footer_copyright"
         data-no-check="true"
         groups="website.group_website_designer">
        <we-colorpicker string="Colors"
                        data-customize-website-color="" data-color="copyright"
                        data-customize-website-layer2-color="" data-layer-color="copyright-custom" data-layer-gradient="copyright-gradient"
                        data-no-bundle-reload="true"
                        data-null-value="'NULL'"
                        data-with-combinations="customizeWebsiteColor"
                        data-with-gradients="true"/>
        <we-select string="Language Selector" data-reload="/">
            <we-button data-name="language_selector_none_opt"
                       data-customize-website-views="">None</we-button>
            <we-button data-customize-website-views="portal.footer_language_selector">Dropdown</we-button>
            <we-button data-customize-website-views="portal.footer_language_selector, website.footer_language_selector_inline">Inline</we-button>
        </we-select>
        <we-select string="⌙ Label" data-dependencies="!language_selector_none_opt" data-reload="/">
            <we-button data-customize-website-views="">Text</we-button>
            <we-button data-customize-website-views="website.footer_language_selector_flag, website.footer_language_selector_no_text">Flag</we-button>
            <we-button data-customize-website-views="website.footer_language_selector_flag">Flag and Text</we-button>
        </we-select>
    </div>

    <!-- Anchor Name -->
    <div data-js="anchor"
        data-selector=":not(p).oe_structure > *, :not(p)[data-oe-type=html] > *"
        data-exclude=".modal *, .oe_structure .oe_structure *, [data-oe-type=html] .oe_structure *, .s_popup">
        <we-button class="fa fa-fw fa-link o_we_link"
                   title="Create a link to target this section"
                   data-no-preview="true"/>
    </div>

    <!-- Mega Menu settings -->
    <div data-js="MegaMenuLayout" data-selector=".o_mega_menu">
        <we-select string="Template" data-name="mega_menu_template_opt">
            <t t-set="_label">Multi Menus</t>
            <we-button t-att-data-select-label="_label" data-select-template="website.s_mega_menu_multi_menus" data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_multi_menus.svg" t-out="_label"/>
            <t t-set="_label">Image Menu</t>
            <we-button t-att-data-select-label="_label" data-select-template="website.s_mega_menu_menu_image_menu" data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_menu_image_menu.svg" t-out="_label"/>
            <t t-set="_label">Odoo Menu</t>
            <we-button t-att-data-select-label="_label" data-select-template="website.s_mega_menu_odoo_menu" data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_odoo_menu.svg" t-out="_label"/>
            <t t-set="_label">Little Icons</t>
            <we-button t-att-data-select-label="_label" data-select-template="website.s_mega_menu_little_icons" data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_little_icons.svg" t-out="_label"/>
            <t t-set="_label">Big Icons Subtitles</t>
            <we-button t-att-data-select-label="_label" data-select-template="website.s_mega_menu_big_icons_subtitles" data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_big_icons_subtitles.svg" t-out="_label"/>
            <t t-set="_label">Images Subtitles</t>
            <we-button t-att-data-select-label="_label" data-select-template="website.s_mega_menu_images_subtitles" data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_images_subtitles.svg" t-out="_label"/>
            <t t-set="_label">Logos</t>
            <we-button t-att-data-select-label="_label" data-select-template="website.s_mega_menu_menus_logos" data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_menus_logos.svg" t-out="_label"/>
            <t t-set="_label">Thumbnails</t>
            <we-button t-att-data-select-label="_label" data-select-template="website.s_mega_menu_thumbnails" data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_thumbnails.svg" t-out="_label"/>
            <t t-set="_label">Cards</t>
            <we-button t-att-data-select-label="_label" data-select-template="website.s_mega_menu_cards" data-img="/website/static/src/img/snippets_thumbs/s_mega_menu_cards.svg" t-out="_label"/>
        </we-select>
        <we-select string="Size">
            <we-button data-select-class="">Full-Width</we-button>
            <we-button data-select-class="o_mega_menu_container_size">Narrow</we-button>
        </we-select>
    </div>

    <div data-js="MegaMenuNoDelete" data-selector=".o_mega_menu > section"/>

    <div data-selector=".o_mega_menu .nav > .nav-link"
         data-drop-in=".o_mega_menu nav"
         data-drop-near=".o_mega_menu .nav-link"/>

    <div data-js="CoverProperties" data-selector=".o_record_cover_container" data-no-check="true">
        <we-row string="Background" class="o_we_full_row">
            <t t-call="web_editor.snippet_options_background_color_widget">
                <t t-set="with_color_combinations" t-value="True"/>
                <t t-set="with_gradients" t-value="True"/>
            </t>
            <we-button-group class="ml-auto">
                <we-imagepicker title="Image" data-background="" data-button-style="true"/>
                <we-button class="fa fa-fw fa-ban" title="None" data-background="">
                </we-button>
            </we-button-group>
        </we-row>
        <we-select string="Size" data-cover-opt-name="size">
            <we-button data-select-class="o_full_screen_height">Full Screen</we-button>
            <we-button class="o_record_cover_opt_size_default" data-select-class="o_half_screen_height">Half Screen</we-button>
            <we-button data-select-class="cover_auto">Fit text</we-button>
        </we-select>
        <we-select string="Filter Intensity" data-cover-opt-name="filters">
            <we-button data-filter-value="0.0">None</we-button>
            <we-button data-filter-value="0.2">Low</we-button>
            <we-button data-filter-value="0.4">Medium</we-button>
            <we-button data-filter-value="0.6">High</we-button>
        </we-select>
        <we-select string="Text Alignment" data-cover-opt-name="text_align">
            <we-button data-select-class="">Left</we-button>
            <we-button data-select-class="text-center">Centered</we-button>
            <we-button data-select-class="text-right">Right</we-button>
        </we-select>
    </div>

    <!-- Stretch section -->
    <div data-js="ContainerWidth" data-selector="section, .s_carousel .carousel-item, s_quotes_carousel .carousel-item"
        data-exclude="[data-snippet] :not(.oe_structure) > [data-snippet]" data-target="> .container, > .container-fluid, > .o_container_small">
        <we-button-group string="Content Width">
            <we-button data-select-class="o_container_small"
                       data-img="/website/static/src/img/snippets_options/content_width_small.svg"
                       title="Small"/>
            <we-button data-select-class="container"
                       data-img="/website/static/src/img/snippets_options/content_width_normal.svg"
                       title="Regular"/>
            <we-button data-select-class="container-fluid"
                       data-img="/website/static/src/img/snippets_options/content_width_full.svg"
                       title="Full"/>
        </we-button-group>
    </div>

    <!-- Scroll to next section button (only for full height) -->
    <div data-js="ScrollButton" data-selector="section" data-exclude="[data-snippet] :not(.oe_structure) > [data-snippet]">
        <!-- Min height of section -->
        <!-- TODO adapt in master, this is patched in JS -->
        <we-button-group string="Height" data-show-scroll-button="">
            <we-button data-name="minheight_auto_opt" data-select-class="" title="Fit content">Auto</we-button>
            <we-button data-select-class="o_half_screen_height" title="Half screen">50%</we-button>
            <we-button data-select-class="o_full_screen_height" title="Full screen" data-name="full_height_opt" data-show-scroll-button="true">100%</we-button>
        </we-button-group>

        <we-checkbox string="Scroll down button"
                     data-toggle-button="true"
                     data-no-preview="true"
                     data-dependencies="full_height_opt"
                     data-name="scroll_button_opt"/>
        <!-- &emsp; -->
        <we-row string=" ⌙ Colors">
            <we-colorpicker
                data-select-style="true"
                data-css-property="background-color"
                data-color-prefix="bg-"
                data-apply-to=".o_scroll_button"
                data-dependencies="scroll_button_opt"/>
            <we-colorpicker data-select-style="true"
                data-css-property="color"
                data-color-prefix="text-"
                data-apply-to=".o_scroll_button"
                data-dependencies="scroll_button_opt"/>
        </we-row>
        <we-select string=" ⌙ Spacing"
                   data-apply-to=".o_scroll_button"
                   data-dependencies="scroll_button_opt">
            <we-button data-select-class="">None</we-button>
            <we-button data-select-class="mb-1">Extra-Small</we-button>
            <we-button data-select-class="mb-2">Small</we-button>
            <we-button data-select-class="mb-3">Medium</we-button>
            <we-button data-select-class="mb-4">Large</we-button>
            <we-button data-select-class="mb-5">Extra-Large</we-button>
        </we-select>
    </div>

    <!--  Vertical Alignment -->
    <div data-option-name="vAlignment" id="row_valign_snippet_option" data-selector=".s_text_image, .s_image_text, .s_three_columns" data-target=".row">
        <we-button-group string="Vert. Alignment" title="Vertical Alignment">
            <we-button title="Align Top"
                       data-select-class="align-items-start"
                       data-img="/website/static/src/img/snippets_options/align_top.svg"/>
            <we-button title="Align Middle"
                       data-select-class="align-items-center"
                       data-img="/website/static/src/img/snippets_options/align_middle.svg"/>
            <we-button title="Align Bottom"
                       data-select-class="align-items-end"
                       data-img="/website/static/src/img/snippets_options/align_bottom.svg"/>
            <we-button title="Stretch to Equal Height"
                       data-select-class="align-items-stretch"
                       data-img="/website/static/src/img/snippets_options/align_stretch.svg"/>
        </we-button-group>
    </div>

    <div data-js="ConditionalVisibility" data-selector="section">
        <t t-set="current_website" t-value="request.env['website'].get_current_website()"/>
        <we-collapse>
            <we-select string="Visibility" data-attribute-name="visibility" data-no-preview="true">
                <we-button data-select-data-attribute="">Always visible</we-button>
                <we-button data-select-data-attribute="conditional" data-select-class="o_snippet_invisible" data-name="visibility_conditional">Conditionally</we-button>
            </we-select>

            <t t-if="request.session.get('geoip', {}).get('country_code')">
                <t t-call="website.snippet_options_conditional_visibility">
                    <t t-set="option_name">⌙ Country</t>
                    <t t-set="attribute_rule" t-valuef="visibilityValueCountryRule"/>
                    <t t-set="save_attribute" t-valuef="visibilityValueCountry"/>
                    <t t-set="attribute_name" t-valuef="data-country"/>
                    <t t-set="model" t-valuef="res.country"/>
                    <t t-set="call_with" t-valuef="code"/>
                    <t t-set="data_fields" t-valuef="[&quot;code&quot;]"/>
                </t>
            </t>
            <t t-if="len(current_website.language_ids) > 1">
                <t t-call="website.snippet_options_conditional_visibility">
                    <t t-set="option_name">⌙ Languages</t>
                    <t t-set="attribute_rule" t-valuef="visibilityValueLangRule"/>
                    <t t-set="save_attribute" t-valuef="visibilityValueLang"/>
                    <t t-set="attribute_name" t-valuef="lang"/>
                    <t t-set="model" t-valuef="res.lang"/>
                    <t t-set="call_with" t-valuef="code"/>
                    <t t-set="domain" t-translation="off">[["id", "in", <t t-out="current_website.language_ids.ids"/>]]</t>
                    <t t-set="data_fields" t-valuef="[&quot;code&quot;]"/>
                </t>
            </t>
            <t t-call="website.snippet_options_conditional_visibility">
                <t t-set="option_name">⌙ UTM Campaign</t>
                <t t-set="attribute_rule" t-valuef="visibilityValueUtmCampaignRule"/>
                <t t-set="save_attribute" t-valuef="visibilityValueUtmCampaign"/>
                <t t-set="attribute_name" t-valuef="data-utm-campaign"/>
                <t t-set="model" t-valuef="utm.campaign"/>
                <t t-set="call_with" t-valuef="display_name"/>
            </t>
            <t t-call="website.snippet_options_conditional_visibility">
                <t t-set="option_name">⌙ UTM Medium</t>
                <t t-set="attribute_rule" t-valuef="visibilityValueUtmMediumRule"/>
                <t t-set="save_attribute" t-valuef="visibilityValueUtmMedium"/>
                <t t-set="attribute_name" t-valuef="data-utm-medium"/>
                <t t-set="model" t-valuef="utm.medium"/>
                <t t-set="call_with" t-valuef="display_name"/>
            </t>
            <t t-call="website.snippet_options_conditional_visibility">
                <t t-set="option_name">⌙ UTM Source</t>
                <t t-set="attribute_rule" t-valuef="visibilityValueUtmSourceRule"/>
                <t t-set="save_attribute" t-valuef="visibilityValueUtmSource"/>
                <t t-set="attribute_name" t-valuef="data-utm-source"/>
                <t t-set="model" t-valuef="utm.source"/>
                <t t-set="call_with" t-valuef="display_name"/>
            </t>
            <we-select string="⌙ Users"
                data-dependencies="visibility_conditional"
                data-attribute-name="data-logged"
                data-save-attribute="visibilityValueLogged"
                data-no-preview="true"
                data-attribute-default-value="">
                <we-button data-select-value="true">Visible for Logged In</we-button>
                <we-button data-select-value="false">Visible for Logged Out</we-button>
                <we-button data-select-value="">Visible for Everyone</we-button>
            </we-select>
        </we-collapse>
    </div>

    <!-- Cookies Bar -->
    <div data-selector="#website_cookies_bar" data-js="CookiesBar" data-target=".modal">
        <we-select string="Layout" class="o_we_inline">
            <we-button data-select-class="o_cookies_discrete" data-select-layout="discrete" data-trigger="position_bottom,s_popup_size_full">Discrete</we-button>
            <we-button data-select-class="o_cookies_classic" data-select-layout="classic" data-trigger="position_bottom,s_popup_size_full">Classic</we-button>
            <we-button data-name="layout_popup_opt" data-select-class="o_cookies_popup" data-select-layout="popup" data-trigger="position_middle,s_popup_size_md">Popup</we-button>
        </we-select>
    </div>

    <!-- Website Animate -->
    <div data-js="WebsiteAnimate"
         data-selector=".o_animable, section .row > div, img, .fa, .btn, .o_animated_text"
         data-exclude=".o_not-animable, .s_col_no_resize.row > div, .s_col_no_resize">
        <!-- TODO adapt in master, this is patched in JS to simulate a data-dependencies -->
        <we-select string="Animate" data-is-animation-type-selection="true">
            <we-button data-select-class="" data-name="no_animation_opt">No Animation</we-button>
            <we-divider/>
            <we-button data-select-class="o_anim_fade_in">Fade In</we-button>
            <we-button data-select-class="o_anim_fade_in_down">Fade In-Down</we-button>
            <we-button data-select-class="o_anim_fade_in_left">Fade In-Left</we-button>
            <we-button data-select-class="o_anim_fade_in_right">Fade In-Right</we-button>
            <we-button data-select-class="o_anim_fade_in_up">Fade In-Up</we-button>
            <we-divider/>
            <we-button data-select-class="o_anim_bounce_in">Bounce In</we-button>
            <we-button data-select-class="o_anim_bounce_in_down">Bounce In-Down</we-button>
            <we-button data-select-class="o_anim_bounce_in_left">Bounce In-Left</we-button>
            <we-button data-select-class="o_anim_bounce_in_right">Bounce In-Right</we-button>
            <we-divider/>
            <we-button data-select-class="o_anim_rotate_in">Rotate In</we-button>
            <we-button data-select-class="o_anim_rotate_in_down_left">Rotate In-Down-Left</we-button>
            <we-button data-select-class="o_anim_rotate_in_down_right">Rotate In-Down-Right</we-button>
            <we-divider/>
            <we-button data-select-class="o_anim_zoom_out">Zoom Out</we-button>
            <we-button data-select-class="o_anim_zoom_in">Zoom In</we-button>
            <we-button data-select-class="o_anim_zoom_in_down">Zoom In-Down</we-button>
            <we-button data-select-class="o_anim_zoom_in_left">Zoom In-Left</we-button>
            <we-button data-select-class="o_anim_zoom_in_right">Zoom In-Right</we-button>
            <we-divider/>
            <we-button data-select-class="o_anim_flash">Flash</we-button>
            <we-button data-select-class="o_anim_pulse">Pulse</we-button>
            <we-button data-select-class="o_anim_shake">Shake</we-button>
            <we-button data-select-class="o_anim_tada">Tada</we-button>
            <we-divider/>
            <we-button data-select-class="o_anim_flip_in_x">Flip-In-X</we-button>
            <we-button data-select-class="o_anim_flip_in_y">Flip-In-Y</we-button>
        </we-select>
        <we-select string="Animation Launch" data-dependencies="!no_animation_opt" data-name="animation_launch_opt">
            <we-button data-select-class="">First Time Only</we-button>
            <we-button data-select-class="o_animate_both_scroll">Every Time</we-button>
        </we-select>
        <we-input string="Animation Duration" data-dependencies="!no_animation_opt" class="o_we_small_input"
            data-select-style="0.4s" data-css-property="animation-duration" data-unit="s"/>
        <we-input string="Animation Delay" data-dependencies="!no_animation_opt" class="o_we_small_input"
            data-select-style="0s" data-css-property="animation-delay" data-unit="s"/>
    </div>

    <!-- Theme options -->
    <div data-js="ThemeColors" data-selector="theme-colors" data-no-check="true">
        <we-alert class="o_old_color_system_warning d-none">
            It appears your website is still using the old color system of
            Odoo 13.0 in some places. We made sure it is still working but
            we recommend you to try to use the new color system, which is
            still customizable.
        </we-alert>
        <we-row class="o_we_theme_colors_selector">
            <we-colorpicker data-name="color_1_opt" title="Primary"
                            data-customize-website-color="" data-color="o-color-1"
                            data-use-css-color="true" data-selected-tab="custom-colors"/>
            <we-colorpicker data-customize-website-color="" data-color="o-color-3"
                            data-use-css-color="true" data-selected-tab="custom-colors"/>
            <we-colorpicker data-name="color_2_opt" title="Secondary"
                            data-customize-website-color="" data-color="o-color-2"
                            data-use-css-color="true" data-selected-tab="custom-colors"/>
            <we-colorpicker data-customize-website-color="" data-color="o-color-4"
                            data-use-css-color="true" data-selected-tab="custom-colors"/>
            <we-colorpicker data-customize-website-color="" data-color="o-color-5"
                            data-use-css-color="true" data-selected-tab="custom-colors"/>
            <we-select data-img="/website/static/src/img/snippets_options/palette.svg" class="o_we_theme_colors_select" data-variable="color-palettes-name"/>
        </we-row>
    </div>
    <div data-js="OptionsTab" data-selector="theme-options" data-no-check="true">
        <we-checkbox string="Show Header"
                     data-customize-website-views="website.option_layout_hide_header|"
                     data-reload="/"/>
        <we-select string="Page Layout" data-variable="layout">
            <we-button data-customize-website-variable="'full'" data-name="layout_full_opt">Full</we-button>
            <we-button data-customize-website-variable="'boxed'">Boxed</we-button>
            <we-button data-customize-website-variable="'framed'">Framed</we-button>
            <we-button data-customize-website-variable="'postcard'">Postcard</we-button>
        </we-select>
        <we-row string="⌙ Background" data-no-preview="true">
            <we-colorpicker data-dependencies="!layout_full_opt"
                            data-customize-website-color=""
                            data-color="body"/>
            <we-button-group data-imagepicker="body_bg_image_opt">
                <we-button title="Image" class="fa fa-fw fa-camera"
                           data-customize-body-bg-type="'image'"/>
                <we-button title="Pattern" class="fa fa-fw fa-th"
                           data-customize-body-bg-type="'pattern'"/>
                <we-button title="None" class="fa fa-fw fa-ban"
                           data-customize-body-bg-type="NONE"/>
            </we-button-group>
            <!-- Hidden imagepicker enabled by above button-group -->
            <we-imagepicker data-name="body_bg_image_opt"
                            data-customize-body-bg=""/>
        </we-row>
        <we-input string="Font size"
                  data-customize-website-variable="null"
                  data-variable="font-size-base"
                  data-unit="px"
                  data-save-unit="rem"/>
        <we-collapse>
            <we-fontfamilypicker string="Font family" data-variable="font"/>
            <we-fontfamilypicker string="⌙ Headings" data-variable="headings-font"/>
            <we-fontfamilypicker string="⌙ Buttons" data-variable="buttons-font"/>
        </we-collapse>

        <!-- Buttons & Links -->
        <we-title>Buttons</we-title>
        <we-select string="Primary Style" data-variable="btn-primary-outline" class="">
            <we-button data-customize-website-variable="false">Fill</we-button>
            <we-button data-customize-website-variable="true">Outline</we-button>
        </we-select>
        <we-select string="Secondary Style" data-variable="btn-secondary-outline">
            <we-button data-customize-website-variable="false">Fill</we-button>
            <we-button data-customize-website-variable="true">Outline</we-button>
        </we-select>
        <we-collapse>
            <we-row string="Paddings">
                <we-input title="Y" data-customize-website-variable="" data-variable="btn-padding-y" data-unit="px" data-save-unit="rem"/>
                <we-input title="X" data-customize-website-variable="" data-variable="btn-padding-x" data-unit="px" data-save-unit="rem"/>
            </we-row>
            <we-row string="⌙ Small">
                <we-input title="Y" data-customize-website-variable="" data-variable="btn-padding-y-sm" data-unit="px" data-save-unit="rem"/>
                <we-input title="X" data-customize-website-variable="" data-variable="btn-padding-x-sm" data-unit="px" data-save-unit="rem"/>
            </we-row>
            <we-row string="⌙ Large">
                <we-input title="Y" data-customize-website-variable="" data-variable="btn-padding-y-lg" data-unit="px" data-save-unit="rem"/>
                <we-input title="X" data-customize-website-variable="" data-variable="btn-padding-x-lg" data-unit="px" data-save-unit="rem"/>
            </we-row>
        </we-collapse>
        <we-collapse>
            <we-input string="Font Size" data-customize-website-variable="" data-variable="btn-font-size" data-unit="px" data-save-unit="rem"/>
            <we-input string="⌙ Small" data-customize-website-variable="" data-variable="btn-font-size-sm" data-unit="px" data-save-unit="rem"/>
            <we-input string="⌙ Large" data-customize-website-variable="" data-variable="btn-font-size-lg" data-unit="px" data-save-unit="rem"/>
        </we-collapse>
        <we-input string="Border Width" data-customize-website-variable="" data-variable="btn-border-width" data-unit="px" data-save-unit="rem"/>>
        <we-collapse>
            <we-input string="Border Radius" data-customize-website-variable="" data-variable="btn-border-radius" data-unit="px" data-save-unit="rem"/>
            <we-input string="⌙ Small" data-customize-website-variable="" data-variable="btn-border-radius-sm" data-unit="px" data-save-unit="rem"/>
            <we-input string="⌙ Large" data-customize-website-variable="" data-variable="btn-border-radius-lg" data-unit="px" data-save-unit="rem"/>
        </we-collapse>
        <we-checkbox string="Ripple Effect"
                     data-customize-website-assets="website.ripple_effect_scss, website.ripple_effect_js"
                     data-customize-website-variable="false|true"
                     data-variable="btn-ripple"/>
        <we-select string="Link Style" data-variable="link-underline">
            <we-button data-customize-website-variable="'never'">Normal</we-button>
            <we-button data-customize-website-variable="'hover'">Underline On Hover</we-button>
            <we-button data-customize-website-variable="'always'">Always Underlined</we-button>
        </we-select>

        <!-- Inputs -->
        <we-title>Inputs</we-title>
        <we-collapse>
            <we-row string="Paddings">
                <we-input title="Y" data-customize-website-variable="" data-variable="input-padding-y" data-unit="px" data-save-unit="rem"/>
                <we-input title="X" data-customize-website-variable="" data-variable="input-padding-x" data-unit="px" data-save-unit="rem"/>
            </we-row>
            <we-row string="⌙ Small">
                <we-input title="Y" data-customize-website-variable="" data-variable="input-padding-y-sm" data-unit="px" data-save-unit="rem"/>
                <we-input title="X" data-customize-website-variable="" data-variable="input-padding-x-sm" data-unit="px" data-save-unit="rem"/>
            </we-row>
            <we-row string="⌙ Large">
                <we-input title="Y" data-customize-website-variable="" data-variable="input-padding-y-lg" data-unit="px" data-save-unit="rem"/>
                <we-input title="X" data-customize-website-variable="" data-variable="input-padding-x-lg" data-unit="px" data-save-unit="rem"/>
            </we-row>
        </we-collapse>
        <we-collapse>
            <we-input string="Font Size" data-customize-website-variable="" data-variable="input-font-size" data-unit="px" data-save-unit="rem"/>
            <we-input string="⌙ Small" data-customize-website-variable="" data-variable="input-font-size-sm" data-unit="px" data-save-unit="rem"/>
            <we-input string="⌙ Large" data-customize-website-variable="" data-variable="input-font-size-lg" data-unit="px" data-save-unit="rem"/>
        </we-collapse>
        <we-input string="Border Width" data-customize-website-variable="" data-variable="input-border-width" data-unit="px" data-save-unit="rem"/>
        <we-collapse>
            <we-input string="Border Radius" data-customize-website-variable="" data-variable="input-border-radius" data-unit="px" data-save-unit="rem"/>
            <we-input string="⌙ Small" data-customize-website-variable="" data-variable="input-border-radius-sm" data-unit="px" data-save-unit="rem"/>
            <we-input string="⌙ Large" data-customize-website-variable="" data-variable="input-border-radius-lg" data-unit="px" data-save-unit="rem"/>
        </we-collapse>

        <we-row string="Status Colors">
            <we-colorpicker title="Success" data-customize-website-color="" data-color-type="theme" data-color="success" data-selected-tab="custom-colors"/>
            <we-colorpicker title="Info" data-customize-website-color="" data-color-type="theme" data-color="info" data-selected-tab="custom-colors"/>
            <we-colorpicker title="Warning" data-customize-website-color="" data-color-type="theme" data-color="warning" data-selected-tab="custom-colors"/>
            <we-colorpicker title="Error" data-customize-website-color="" data-color-type="theme" data-color="danger" data-selected-tab="custom-colors"/>
        </we-row>
        <we-collapse>
            <we-row string="Grays" class="o_we_gray_preview o_we_collapse_toggler">
                <t t-foreach="9" t-as="i">
                    <t t-set="grayCode" t-value="str((9 - i) * 100)"/>
                    <span t-attf-title="Gray #{grayCode}"
                          t-attf-variable="#{grayCode}"
                          t-attf-class="o_we_user_value_widget o_we_gray_preview bg-#{grayCode}"/>
                </t>
            </we-row>
            <we-range string="⌙ Hue" class="o_we_slider_tint" data-customize-gray="" data-param="gray-hue" data-min="0" data-max="359.9" data-step="0.1"/>
            <we-range string="⌙ Saturation" data-customize-gray="" data-param="gray-extra-saturation" data-step="0.1"/>
        </we-collapse>
    </div>
    <div data-js="OptionsTab" data-selector="website-settings" data-no-check="true">
        <we-row string="Theme">
            <we-button data-switch-theme="" data-no-preview="true">Switch Theme</we-button>
        </we-row>
        <we-row string="Code Injection" title="Enter code that will be added into every page of your site">
            <we-button data-no-preview="true" data-open-custom-code-dialog="head">&amp;lt;head&amp;gt;</we-button>
            <we-button data-no-preview="true" data-open-custom-code-dialog="footer">&amp;lt;/body&amp;gt;</we-button>
        </we-row>
        <we-row string="Google Map">
            <we-button data-configure-api-key="" data-no-preview="true">Custom Key</we-button>
        </we-row>
    </div>
</template>
</odoo>
