<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="stock_valuation_layer_revaluation_form_view" model="ir.ui.view">
        <field name="name">stock.valuation.layer.revaluation.form</field>
        <field name="model">stock.valuation.layer.revaluation</field>
        <field name="arch" type="xml">
            <form string="Product Revaluation">
                <sheet>
                    <group>
                        <label for="current_value_svl" string="Current Value"/>
                        <div class="o_row">
                            <span><field name="current_value_svl" widget="monetary"/> for <field name="current_quantity_svl"/> <field name="product_uom_name" class="mx-1"/> </span>
                        </div>
                        <label for="added_value" string="Added Value"/>
                        <div class="o_row">
                            <span><field name="added_value" class="oe_inline"/> = <field name="new_value"/> (<field name="new_value_by_qty"/> by <field name="product_uom_name" class="mx-1"/>)
                            <small class="mx-2 font-italic">Use a negative added value to record a decrease in the product value</small></span>
                        </div>
                        <field name="company_id" invisible="1"/>
                        <field name="currency_id" invisible="1"/>
                        <field name="product_id" invisible="1"/>
                    </group>
                    <group>
                        <field name="property_valuation" invisible="1"/>
                        <group>
                            <field name="reason"/>
                            <field name="account_journal_id" attrs="{'invisible':[('property_valuation', '!=', 'real_time')], 'required': [('property_valuation', '=', 'real_time')]}"/>
                        </group>
                        <group>
                            <field name="account_id" attrs="{'invisible':[('property_valuation', '!=', 'real_time')], 'required': [('property_valuation', '=', 'real_time')]}"/>
                            <field name="date" attrs="{'invisible':[('property_valuation', '!=', 'real_time')]}"/>
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button name="action_validate_revaluation" string="Revalue" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z" />
                </footer>
            </form>
        </field>
    </record>

</odoo>
