# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_sa
#

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_chart_template_standard_liquidity_transfer
msgid "Liquidity Transfer"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_101060
msgid "VAT Paid to Customs"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201020
msgid "Withholding Tax Payable"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400073
msgid "Withholding Tax Expense"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400072
msgid "Zakat Expense"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201019
msgid "Zakat Provision"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_100101
msgid "Right of use Asset (IFRS 16)"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_100102
msgid "Accumulated Depreciation right use asset (IFRS 16)"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_100103
msgid "VAT Receivable"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_101005
msgid "Main Safe"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_101006
msgid "Main Safe - Foreign Currency"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_101007
msgid "Visa & Master Credit Cards"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_101008
msgid "Gateway Credit Cards"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_101009
msgid "Manual Visa & Master Cards"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_101010
msgid "PayPal Account"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_102011
msgid "Accounts Receivable"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_102012
msgid "Accounts Receivable (PoS)"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_102013
msgid "Post Dated Cheques Received"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_102014
msgid "Other Receivable"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_102015
msgid "Other Debtors"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_103016
msgid "Shipment Insurance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_103017
msgid "Shipments Documentation Charges"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_103018
msgid "Shipment Other Charges"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_103019
msgid "Handling Difference in Inventory"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_103020
msgid "Items Delivered to Customs on temprary Base"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104021
msgid "Prepaid Medical Insurance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104022
msgid "Prepaid Life Insurance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104023
msgid "Prepaid Office Rent"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104024
msgid "Prepaid Other Insurance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104025
msgid "Prepaid License Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104026
msgid "Prepaid Maintenance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104027
msgid "Prepaid Site Hosting Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104028
msgid "Prepaid Employees Housing"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104029
msgid "Prepaid Schooling Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104030
msgid "Prepaid Consultancy Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104031
msgid "Prepaid Legal Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104032
msgid "Prepaid Sponsorship Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104033
msgid "PrePaid Advertisement Expenses"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104034
msgid "Prepaid Bank Guarantee"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104035
msgid "Other Prepayments"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104036
msgid "Prepaid Finance charge for Loans"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104037
msgid "Deposit - Office Rent"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104038
msgid "Deposits - Customs"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104039
msgid "Deposit to Immigration (Visa)"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104040
msgid "Deposit Others"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_104041
msgid "VAT Input"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106001
msgid "Leasehold Improvement"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106002
msgid "Furniture and Equipment"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106003
msgid "Computer Hardware & Software"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106004
msgid "Motor Vehicles"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106005
msgid "Work In Progress"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106006
msgid "Amortisation on Leasehold Improvement"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106007
msgid "Acc.Deprn.of Furniture & Office Equipment"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106008
msgid "Acc. Deprn.Computer Hardware & Software"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106009
msgid "Acc. Depreciation of Motor Vehicles"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106010
msgid "Registration of Trademarks"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_106011
msgid "Computer Card Renewal"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201002
msgid "Payables"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201003
msgid "Credit Notes to Customers"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201004
msgid "Accrued - Salaries"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201005
msgid "Leave Tickets Provision"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201006
msgid "Leave Days Provision"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201007
msgid "Accrued - Commissions"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201008
msgid "Accrued Salaries Increment"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201009
msgid "Accrued-Staff Bonus"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201010
msgid "Accrued Other Personnel Cost"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201011
msgid "Accrued - Utilities"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201012
msgid "Accrued - Telephone"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201013
msgid "Accrued - Sponsorship"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201014
msgid "Accrued - Audit Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201015
msgid "Accrued - Office Rent"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201016
msgid "Accrued Others"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201017
msgid "VAT Output"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_201018
msgid "Deferred income"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_202001
msgid "End of Service Provision"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_202002
msgid "Reservations"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_202003
msgid "VAT Payable"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400001
msgid "Cost of Goods Sold in Trading"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400002
msgid "Cost Of Goods Sold I/C Sales"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400003
msgid "Basic Salary"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400004
msgid "Housing Allowance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400005
msgid "Transportation Allowance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400006
msgid "Leave Ticket"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400007
msgid "Leave Salary"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400008
msgid "End Of Service Indemnity"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400009
msgid "Medical Insurance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400010
msgid "Life Insurance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400011
msgid "Sales Commission"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400012
msgid "Staff Other Allowances"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400013
msgid "Uniform"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400014
msgid "Visa Expenses"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400015
msgid "Personnel Cost Others"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400016
msgid "Office Rent"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400017
msgid "Warehouse Rent"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400018
msgid "Water & Electricity"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400019
msgid "Other Utility Cahrges"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400020
msgid "Telephone"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400021
msgid "Courrier"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400022
msgid "Web Site Hosting Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400023
msgid "Others - Communication"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400024
msgid "Air tickets"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400025
msgid "Hotel"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400026
msgid "Meals"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400027
msgid "Per Diem"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400028
msgid "Others"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400029
msgid "Audit Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400030
msgid "Sponsorship Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400031
msgid "Legal fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400032
msgid "Trade License Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400033
msgid "Others - Professional Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400034
msgid "Other - Advertising Expenses"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400035
msgid "Write Off Receivables & Payables"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400036
msgid "Write Off Inventory"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400037
msgid "Amortisation of Preoperating Expenses"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400038
msgid "Cash Shortage"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400039
msgid "Others - Provision & Write off"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400040
msgid "Insurance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400041
msgid "Training"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400042
msgid "Maintenance"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400043
msgid "Security & Guard"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400044
msgid "Cleaning"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400045
msgid "Subscriptions"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400046
msgid "Gifts & Donations"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400047
msgid "Kitchen and Buffet Expenses"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400048
msgid "Vehicle Expenses"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400049
msgid "Convoyance Expenses"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400050
msgid "Others - Office Various Expenses"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400051
msgid "Other Bank Charges"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400052
msgid "Loss On Fixed Assets Disposal"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400053
msgid "Loss on Difference on Exchange"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400054
msgid "Disposal of Business Branch"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400055
msgid "Income Tax"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400056
msgid "Previous Year Adjustments Account"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400057
msgid "Other Non Operating Expenses"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400058
msgid "Credit Card Charges"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400059
msgid "Bank Finance & Loan Charges"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400060
msgid "Air Miles Card Charges"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400061
msgid "Credit Card Swipe Charges"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400062
msgid "PayPal Charges"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400063
msgid "Amortization on Leasehold Improvement"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400064
msgid "Depreciation Of Furniture & Office Equipment"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400065
msgid "Depreciation Of Computer Hard & Soft"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400066
msgid "Depreciation Of Motor Vehicles"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400067
msgid "Consultancy Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400068
msgid "Provision for Doubtful Debts"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400069
msgid "Closing Account"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_400070
msgid "Depreciation on right of use asset (IFRS 16)"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500001
msgid "Sales Account"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500002
msgid "Sales of I/C"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500003
msgid "Management Consultancy Fees"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500004
msgid "Sales from Other Region"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500005
msgid "Advertising Income"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500006
msgid "Branding Income"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500007
msgid "Space Rental Income"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500008
msgid "Service Income"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500009
msgid "Interest Revenue"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500010
msgid "Capital Gain"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500011
msgid "Gain On Difference Of Exchange"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_500013
msgid "Other Income"
msgstr ""

#. module: l10n_sa
#: model:account.account.template,name:l10n_sa.sa_account_999999
msgid "Undistributed Profits/Losses"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report,name:l10n_sa.tax_report_vat_filing
msgid "VAT Filing Report"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_vat_all_sales_base
msgid "VAT on Sales and all other Outputs (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_standard_rated_15_base
msgid "1. Standard Rated 15% (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_special_sales_to_locals_base
msgid "2. Special Sales to Locals (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_local_sales_subject_to_0_base
msgid "3. Local Sales Subject to 0% (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_export_sales_base
msgid "4. Export Sales (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_exempt_sales_base
msgid "5. Exempt Sales (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_net_sales_base
msgid "6. Net Sales (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_vat_all_expenses_base
msgid "VAT on Expenses and all other Inputs (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_standard_rated_15_purchases_base
msgid "7. Standard rated 15% Purchases (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_taxable_imports_15_paid_to_customs_base
msgid "8. Taxable Imports 15% Paid to Customs (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_imports_subject_tp_reverse_charge_mechanism_base
msgid "9. Imports subject to reverse charge mechanism (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_zero_rated_purchases_base
msgid "10. Zero Rated Purchases (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_exempt_purchases_base
msgid "11. Exempt Purchases (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_net_purchases_base
msgid "12. Net Purchases (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_vat_all_sales_tax
msgid "VAT on Sales and all other Outputs (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_standard_rated_15_tax
msgid "1. Standard Rated 15% (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_special_sales_to_locals_tax
msgid "2. Special Sales to Locals (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_local_sales_subject_to_0_tax
msgid "3. Local Sales Subject to 0% (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_export_sales_tax
msgid "4. Export Sales (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_exempt_sales_tax
msgid "5. Exempt Sales (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_net_sales_tax
msgid "6. Net Sales (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_vat_all_expenses_tax
msgid "VAT on Expenses and all other Inputs (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_standard_rated_15_purchases_tax
msgid "7. Standard rated 15% Purchases (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_taxable_imports_15_paid_to_customs_tax
msgid "8. Taxable Imports 15% Paid to Customs (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_imports_subject_tp_reverse_charge_mechanism_tax
msgid "9. Imports subject to reverse charge mechanism (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_zero_rated_purchases_tax
msgid "10. Zero Rated Purchases (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_exempt_purchases_tax
msgid "11. Exempt Purchases (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_net_purchases_tax
msgid "12. Net Purchases (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_net_vat_due
msgid "Net VAT Due"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_total_value_of_due_tax_for_the_period
msgid "Total value of due tax for the period"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_net_vat_due_or_reclaimed_for_the_period
msgid "Net VAT due (or reclaimed) for the period"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report,name:l10n_sa.tax_report_withholding_tax
msgid "Withholding Tax Report"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_on_purchased_services_base
msgid "Withholding Tax on Purchased Services (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_rental_base
msgid "Withholding Tax 5% (Rental) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_tickets_or_air_freight_base
msgid "Withholding Tax 5% (Tickets or Air Freight) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_tickets_or_sea_freight_base
msgid "Withholding Tax 5% (Tickets or Sea Freight)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_international_telecommunication_base
msgid "Withholding Tax 5% (International Telecommunication)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_distributed_profits_base
msgid "Withholding Tax 5% (Distributed Profits) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_consulting_and_technical_base
msgid "Withholding Tax 5% (Consulting and Technical) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_return_from_loans_base
msgid "Withholding Tax 5% (Return from Loans) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_insurance_and_reinsurance_base
msgid "Withholding Tax 5% (Insurance & Reinsurance) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_royalties_base
msgid "Withholding Tax 15% (Royalties)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_paid_services_from_main_branch_base
msgid "Withholding Tax 15% (Paid Services from Main Branch)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_paid_services_from_another_branch_base
msgid "Withholding Tax 15% (Paid Services from another branch)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_others_base
msgid "Withholding Tax 15% (Others)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_20_managerial_base
msgid "Withholding Tax 20% (Managerial)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_total_base
msgid "Withholding Tax Total (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_on_purchased_services_tax
msgid "Withholding Tax on Purchased Services (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_rental_tax
msgid "Withholding Tax 5% (Rental) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_tickets_or_air_freight_tax
msgid "Withholding Tax 5% (Tickets or Air Freight) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_tickets_or_sea_freight_tax
msgid "Withholding Tax 5% (Tickets or Sea Freight)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_international_telecommunication_tax
msgid "Withholding Tax 5% (International Telecommunication)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_distributed_profits_tax
msgid "Withholding Tax 5% (Distributed Profits) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_consulting_and_technical_tax
msgid "Withholding Tax 5% (Consulting and Technical) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_return_from_loans_tax
msgid "Withholding Tax 5% (Return from Loans) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_insurance_and_reinsurance_tax
msgid "Withholding Tax 5% (Insurance & Reinsurance) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_royalties_tax
msgid "Withholding Tax 15% (Royalties)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_paid_services_from_main_branch_tax
msgid "Withholding Tax 15% (Paid Services from Main Branch)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_paid_services_from_another_branch_tax
msgid "Withholding Tax 15% (Paid Services from another branch)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_others_tax
msgid "Withholding Tax 15% (Others)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_20_managerial_tax
msgid "Withholding Tax 20% (Managerial)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report.line,name:l10n_sa.tax_report_line_withholding_tax_total_tax
msgid "Withholding Tax Total (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_5_rental
msgid "Withholding Tax 5% (Rental)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_5_tickets_or_air_freight
msgid "Withholding Tax 5% (Tickets or Air Freight)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_5_tickets_or_sea_freight
msgid "Withholding Tax 5% (Tickets or Sea Freight)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_5_international_telecommunication
msgid "Withholding Tax 5% (International Telecommunication)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_5_distributed_profits
msgid "Withholding Tax 5% (Distributed Profits)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_5_consulting_and_technical
msgid "Withholding Tax 5% (Consulting and Technical)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_5_return_from_loans
msgid "Withholding Tax 5% (Return from Loans)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_5_insurance_amd_reinsurance
msgid "Withholding Tax 5% (Insurance & Reinsurance)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_15_royalties
msgid "Withholding Tax 15% (Royalties)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_15_paid_services_from_main_branch
msgid "Withholding Tax 15% (Paid Services from Main Branch)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_15_paid_services_from_another_branch
msgid "Withholding Tax 15% (Paid Services from another branch)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_15_others
msgid "Withholding Tax 15% (Others)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_withholding_tax_20_managerial
msgid "Withholding Tax 20% (Managerial)"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_sales_tax_15
msgid "Sales Tax 15%"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_local_sales_tax_0
msgid "Local Sales 0%"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_export_sales_tax_0
msgid "Export Sales 0%"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_exempt_sales_tax_0
msgid "Exempt Sales Tax 0%"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_purchase_tax_15
msgid "Purchase Tax 15%"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_rcp_tax_15
msgid "Reverse charge provision Tax 15%"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_import_tax_paid_15_paid_to_customs
msgid "Import tax 15% Paid to customs"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_purchases_tax_0
msgid "Purchases 0%"
msgstr ""

#. module: l10n_sa
#: model:account.tax.template,name:l10n_sa.sa_exempt_purchases_tax
msgid "Exempt Purchases"
msgstr ""

#. module: l10n_sa
#: model:res.country,vat_label:base.sa
msgid "VAT Number"
msgstr ""

#. module: l10n_sa
#: model:res.currency,currency_unit_label:base.SAR
msgid "Riyal"
msgstr ""

#. module: l10n_sa
#: model:res.currency,currency_subunit_label:base.SAR
msgid "Halala"
msgstr ""
