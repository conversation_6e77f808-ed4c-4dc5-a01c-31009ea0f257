# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale
# 
# Translators:
# <PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:48+0000\n"
"PO-Revision-Date: 2018-08-24 09:35+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_categories_recursive
msgid "#{'Unfold' if categ.id in parent_category_ids else 'Fold'}"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "&amp; Shipping"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid ""
",\n"
"                the #1"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "/shop/payment"
msgstr ""

#. module: website_sale
#: model:product.template.attribute.value,name:website_sale.product_template_attribute_value_1
msgid "2 year"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"30-day money-back guarantee<br/>\n"
"                              Free Shipping in U.S.<br/>\n"
"                              Buy now, get in 2 days"
msgstr ""

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<?xml version=\"1.0\"?>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    % set company = object.company_id or object.user_id.company_id or user.company_id\n"
"                    <span style=\"font-size: 10px;\">Your Cart</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br/><br/>\n"
"                    % if object.order_line:\n"
"                        % for line in object.order_line:\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img src=\"/web/image/product.product/${line.product_id.id}/image\" height=\"100px\" width=\"100px\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong>${line.product_id.display_name}</strong><br/>${line.name}\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        ${(line.product_uom_qty) | int} ${(line.product_uom.name)}\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        % endfor\n"
"                        <hr/>\n"
"                    % endif\n"
"                    <div style=\"text-align: center; margin: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a href=\"/shop/cart?access_token=${object.access_token}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with ${user.company_id.name}!</strong></div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${company.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${company.phone}\n"
"                    % if company.email\n"
"                        | <a href=\"'mailto:%s' % ${company.email}\" style=\"text-decoration:none; color: #454748;\">${company.email}</a>\n"
"                    % endif\n"
"                    % if company.website\n"
"                        | <a href=\"'%s' % ${company.website}\" style=\"text-decoration:none; color: #454748;\">${company.website}</a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<b>Shipping: </b>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "<b>Your order: </b>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<i class=\"fa fa-arrow-right\"/> Add payment acquirers"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "<i class=\"fa fa-check\"/> Ship to this address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                            <span>Back</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                    <span>Return to Cart</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<i class=\"fa fa-edit\"/> Edit"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-plus-square\"/>\n"
"                                                        <span>Add an address</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print\"/> Print"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header
msgid ""
"<i class=\"fa fa-shopping-cart\"/>\n"
"                    My Cart"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/crm_team.py:58
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"<small class=\"text-muted float-right\">Source: https://termsfeed.com/blog"
"/sample-terms-and-conditions-template</small>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                        <span class=\"fa fa-chevron-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                    <span class=\"fa fa-chevron-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                        <span class=\"\">Continue Shopping</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                    Continue<span class=\"d-none d-md-inline\"> Shopping</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"fa fa-chevron-left\"/> Previous"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Abandoned Carts</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Assignation</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Invoicing Policy</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<span>Confirm</span>\n"
"                                    <i class=\"fa fa-chevron-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Confirmed</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<span>Next</span>\n"
"                                            <i class=\"fa fa-chevron-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "<span>Process Checkout</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "<span>Sort by</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "<strong>Add to Cart</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Payment Information:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"A <b>Governing Law</b> will inform users which laws govern the agreement. "
"This should the country in which your company is headquartered or the "
"country from which you operate your web site and mobile app."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"A <b>Limit What Users Can Do</b> clause can inform users that by agreeing to"
" use your service, they’re also agreeing to not do certain things. This can "
"be part of a very long and thorough list in your Terms and Conditions "
"agreements so as to encompass the most amount of negative uses."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"A <b>Links To Other Web Sites</b> clause will inform users that you are not "
"responsible for any third party web sites that you link to. This kind of "
"clause will generally inform users that they are responsible for reading and"
" agreeing (or disagreeing) with the Terms and Conditions or Privacy Policies"
" of these third parties."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"A <b>Termination</b> clause will inform that users’ accounts on your website"
" and mobile app or users’ access to your website and mobile (if users can’t "
"have an account with you) can be terminated in case of abuses or at your "
"sole discretion."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "A document to provide"
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:37
#, python-format
msgid "AT A GLANCE"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
msgid "Abandoned Cart"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/models/crm_team.py:45
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:19
#: model:ir.actions.act_window,name:website_sale.action_abandoned_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#, python-format
msgid "Abandoned Carts"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_salesteams_view_kanban_inherit_website_sale
msgid "Abandoned Carts to Recover"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Abandoned carts are all carts left unconfirmed by website visitors. You can "
"find them in *Website > Orders > Abandoned Carts*. From there you can send "
"recovery emails to visitors who entered their contact details."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customerreviews the cart before payment (cross-"
"sell strategy)."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Account Number"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Add one"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Add to Cart"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_options.js:88
#, python-format
msgid "Add to cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Address"
msgstr "Heimilisfang"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_products_categories
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid "All Products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Allow to use on"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
msgid "Alternative Products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.recommended_products
msgid "Alternative Products:"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"An error occured while processing your payment. Please try again or contact "
"your administrator."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "Virkja"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Apply specific prices per country, discounts, etc."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignation of online orders"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_catalog_variants_action
msgid "Attribute Values"
msgstr "Gildi eiginleika"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Eiginleikar"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:91
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:92
#, python-format
msgid "Average Order"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Nafn banka"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr ""

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:103
#, python-format
msgid "Best Sellers"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Billing"
msgstr "Reikningagerð"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing Address"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:14
#, python-format
msgid "Capture order payments when the delivery is completed."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Cart is abandoned after"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:82
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:83
#, python-format
msgid "Carts"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Catalog price: High to Low"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Catalog price: Low to High"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Categories"
msgstr "Flokkar"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_chairs
msgid "Chairs"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "Undirflokkar"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_open_website_sale_onboarding_payment_acquirer_wizard
msgid "Choose a payment method"
msgstr ""

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "Staður"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:28
#, python-format
msgid "Click on <em>Continue</em> to create the product."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:63
#, python-format
msgid "Click on this button so your customers can see it."
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "Fyrirtæki"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid "Company Name"
msgstr "Nafn fyrirtækis"

#. module: website_sale
#: model:product.public.category,name:website_sale.Components
msgid "Components"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Confirm Order"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm Order <span class=\"fa fa-chevron-right\"/>"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:6
#, python-format
msgid "Confirm orders when you get paid."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmation Date"
msgstr "Dags Staðfestingar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed Orders"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "Tengiliður"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_options.js:87
#, python-format
msgid "Continue Shopping"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:95
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:96
#, python-format
msgid "Conversion"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "Land"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country..."
msgstr "Land..."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_style__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Currencies"
msgstr "Gjaldmiðlar"

#. module: website_sale
#: selection:website.sale.payment.acquirer.onboarding.wizard,payment_method:0
msgid "Custom payment instructions"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "Viðskiptavinur"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
msgid "Customers"
msgstr "Viðskiptavinir"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist"
msgstr "Sjálfgefinn verðlisti"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr ""

#. module: website_sale
#: selection:res.config.settings,sale_delivery_settings:0
msgid ""
"Delivery methods are only used internally: the customer doesn't pay for "
"shipping costs"
msgstr ""

#. module: website_sale
#: selection:res.config.settings,sale_delivery_settings:0
msgid ""
"Delivery methods are selectable on the website: the customer pays for "
"shipping costs"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_digital
msgid "Digital Content"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_style__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: website_sale
#: code:addons/website_sale/models/digest.py:16
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: website_sale
#: selection:res.company,website_sale_onboarding_payment_acquirer_state:0
msgid "Done"
msgstr "Lokið"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:38
#, python-format
msgid "Double click here to set an image describing your product."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:53
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr ""

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Edit"
msgstr "Skrifa"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:32
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "Tölvupóstur"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email Template"
msgstr "Email Template"

#. module: website_sale
#: model:ir.model,name:website_sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Email composition wizard"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:24
#, python-format
msgid "Enter a name for your new product"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/product.py:93
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "Villa! Ekki er hægt að búa til flokka endurkvæmt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Extra Images"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
msgid "Extra Info"
msgstr "Aukaupplýsingar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:10
#, python-format
msgid "Generate an invoice from orders ready for invoicing."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Give us your feedback"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Gefur röðun fyrir lista af vöruflokkum"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Group By"
msgstr "Hópa eftir"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style__html_class
msgid "HTML Classes"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "I agree to the"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "I have a promo code"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_style__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__id
msgid "ID"
msgstr "Auðkenni"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"If your website or mobile apps allows users to create content and make that "
"content public to other users, a <b>Content</b> section will inform users "
"that they own the rights to the content they have created.<br/>The “Content”"
" clause usually mentions that users must give you (the website or mobile app"
" developer) a license so that you can share this content on your "
"website/mobile app and to make it available to other users.<br/>Because the "
"content created by users is public to other users, a DMCA notice clause (or "
"Copyright Infringement ) section is helpful to inform users and copyright "
"authors that, if any content is found to be a copyright infringement, you "
"will respond to any DMCA take down notices received and you will take down "
"the content."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image
msgid "Image"
msgstr "Mynd"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_image_ids
msgid "Images"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__module_website_sale_stock
msgid "Installs the \"Website Delivery Information\" application"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:521
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "Ógilt Tölvupóstur! Vinsamlegast sláðu inn gilt netfang."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_stock
msgid "Inventory"
msgstr "Birgðir"

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_invoice
msgid "Invoice"
msgstr "Reikningur"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_invoices_ecommerce
msgid "Invoices"
msgstr "Reikningar"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "Reikningagerð"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:166
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr ""

#. module: website_sale
#: selection:res.company,website_sale_onboarding_payment_acquirer_state:0
msgid "Just done"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_public_category____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_style____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
msgid "Last Online Sales Order"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_style__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let returning shoppers save products in a wishlist"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:15
#, python-format
msgid "Let's create your first product."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:67
#, python-format
msgid ""
"Let's now take a look at your administration dashboard to get your eCommerce"
" website ready in no time."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Log In"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage availability of products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_medium
msgid "Medium-sized image"
msgstr "Mynd í miðstærð"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__image_medium
msgid ""
"Medium-sized image of the category. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""
"Miðstærð af mynd af flokknum. Henni er sjálfkrafa breytt í 128x128px mynd og"
" heldur hlutföllum. Nota þennarn reit í „form“ sýnum eða sumum „kanban“ "
"sýnum."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Greiðslumáti"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.js:31
#, python-format
msgid "My Cart"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.editor.js:31
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model_terms:ir.ui.view,arch_db:website_sale.address
#, python-format
msgid "Name"
msgstr "Nafn"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Name - A to Z"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Name - Z to A"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/controllers/main.py:993
#: code:addons/website_sale/static/src/js/website_sale.editor.js:30
#, python-format
msgid "New Product"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Next <span class=\"fa fa-chevron-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_abandoned_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for \""
msgstr ""

#. module: website_sale
#: selection:res.config.settings,sale_delivery_settings:0
msgid "No shipping management on website"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "None website"
msgstr ""

#. module: website_sale
#: selection:res.company,website_sale_onboarding_payment_acquirer_state:0
msgid "Not done"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__cart_abandoned_delay
msgid "Number of hours after which the cart is considered abandoned."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale.xml:12
#, python-format
msgid "OK"
msgstr "OK"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Odoo"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:58
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/crm_team.py:39
#: model:ir.ui.menu,name:website_sale.menu_report_sales
#, python-format
msgid "Online Sales"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:71
#, python-format
msgid "Open your website app here."
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:236
#, python-format
msgid "Option for:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_price
msgid "Option not available"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:238
#, python-format
msgid "Option:"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__option_line_ids
msgid "Options Linked"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Order Date"
msgstr "Dags. pöntunar"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_order_line
msgid ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "Order Total"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:66
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:67
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
#, python-format
msgid "Orders"
msgstr "Pantanir"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Orders Followup"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:11
#, python-format
msgid "Orders to Invoice"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:87
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:88
#, python-format
msgid "Orders/Day"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "Yfirflokkur"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now <span class=\"fa fa-chevron-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay with"
msgstr ""

#. module: website_sale
#: selection:website.sale.payment.acquirer.onboarding.wizard,payment_method:0
msgid "Pay with PayPal"
msgstr ""

#. module: website_sale
#: selection:website.sale.payment.acquirer.onboarding.wizard,payment_method:0
msgid "Pay with another payment acquirer"
msgstr ""

#. module: website_sale
#: selection:website.sale.payment.acquirer.onboarding.wizard,payment_method:0
msgid "Pay with credit card (via Stripe)"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_email_account
msgid "PayPal Email ID"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_acquirers
msgid "Payment Acquirers"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_icons
msgid "Payment Icons"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Payment Method"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:15
#, python-format
msgid "Payments to Capture"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Paypal Merchant ID"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "Paypal PDT Token"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "Sími"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Powered by"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:113
#, python-format
msgid "Previous Month"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:111
#, python-format
msgid "Previous Week"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:115
#, python-format
msgid "Previous Year"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "Price"
msgstr "Verð"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "Verðlisti"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_product_pricelist3
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Verðlistar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Pricing"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "Prenta"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_options.js:86
#, python-format
msgid "Proceed to Checkout"
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:106
#: model:ir.model,name:website_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#, python-format
msgid "Product"
msgstr "Vara"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "Vöruflokkur"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
msgid "Product Comparison Tool"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Product Name"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "Vöruverð"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_style
msgid "Product Style"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template
msgid "Product Template"
msgstr "Sniðmát vöru"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_variants
msgid "Product Variants"
msgstr "Vöruafbrigði"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Product not found!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product prices displaying in web catalog"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_settings
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Products"
msgstr "Vörur"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Promote"
msgstr "Promote"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Provide customers with product-specific links or downloadable content in the"
" confirmation page of the checkout process if the payment gets through. To "
"do so, attach some files to a product using the new Files button and publish"
" them."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
msgid "Published"
msgstr "Published"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push down"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to bottom"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to top"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push up"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Qty:"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:107
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#, python-format
msgid "Quantity"
msgstr "Magn"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Related Product"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Remove one"
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_reporting
msgid "Reporting"
msgstr "Skýrslur"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Return to the product list."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Review Order"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Sale"
msgstr "Sala"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sale Order"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Report"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:35
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:49
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#, python-format
msgid "Sales"
msgstr "Sala"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
msgid "Sales Channels"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Sales Order Line"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Report"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:32
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:47
#, python-format
msgid "Sales Since Last"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "Söluhópur"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "Söluaðili"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Saved Payment Data"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:20
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Select this address"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "Selectable"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell content to download or URL links"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell in several currencies"
msgstr ""

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:18
#, python-format
msgid "Send a recovery email to visitors who haven't completed their order."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send a recovery email when a cart is abandoned"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
msgid "Sequence"
msgstr "Runa"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "Þjónustur"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to sell variants"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Ship to the same address\n"
"                                                    <span class=\"ship_to_other text-muted\" style=\"display: none\">&amp;nbsp;(<i>Your shipping address will be requested later) </i></span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping Address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__sale_delivery_settings
msgid "Shipping Management"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model:website.menu,name:website_sale.menu_shop
msgid "Shop"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Acquirer"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Size"
msgstr "Size"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_small
msgid "Small-sized image"
msgstr "Lítil mynd"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__image_small
msgid ""
"Small-sized image of the category. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Lítil mynd af flokknum. Hún er sjálfvirkt smækkuð í 64x64px með tilliti til "
"lögunar, Notaðu þennan reit þar sem þörf er á lítilli mynd."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:75
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:108
#, python-format
msgid "Sold"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:539
#, python-format
msgid "Some required fields are empty."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Sorry, this product is not available anymore."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Sorting by :"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province..."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_company__website_sale_onboarding_payment_acquirer_state
msgid "State of the website sale onboarding payment acquirer step"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "Staða"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street 2"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street <span class=\"d-none d-md-inline\"> and Number</span>"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style__name
msgid "Style Name"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_style_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Styles"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal:"
msgstr "Alls:"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer(upsell strategy).Those product show up"
" on the product page."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested Accessories:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid "TIN / VAT"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes:"
msgstr "VSK:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid "Terms &amp;amp; Conditions"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.terms
msgid ""
"The <b>Intellectual Property</b> disclosure will inform users that the "
"contents, logo and other visual media you created is your property and is "
"protected by copyright laws."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "The SEPA QR Code informations are not set correctly."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned e-commerce category. Go "
"toShop > Customize and enable 'E-commerce categories' to view all e-commerce"
" categories."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_abandoned_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:54
#, python-format
msgid "There is no recent confirmed order."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "This adds the choice of a currency on pricelists."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"This email template is suggested by default when you send a recovery email."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__image
msgid ""
"This field holds the image used as image for the category, limited to "
"1024x1024px."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "True"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr ""

#. module: website_sale
#: model:product.product,uom_name:website_sale.product_product_1
#: model:product.template,uom_name:website_sale.product_product_1_product_template
msgid "Unit(s)"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:7
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#, python-format
msgid "Unpaid Orders"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:74
#, python-format
msgid "Untaxed Total Sold"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:45
#, python-format
msgid "Upload an image from your local library."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "View Cart ("
msgstr ""

#. module: website_sale
#: model:product.product,name:website_sale.product_product_1
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "Warranty"

#. module: website_sale
#: model:product.product,description_sale:website_sale.product_product_1
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_invoice__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Website"
msgstr "Vefsíða"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
msgid "Website meta description"
msgstr "Website meta description"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website meta keywords"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
msgid "Website meta title"
msgstr "Website meta title"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_price
msgid "Website price"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_price_difference
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_price_difference
msgid "Website price difference"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_public_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_public_price
msgid "Website public price"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_invoice__website_id
msgid "Website through which this invoice was created."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "vefsíður"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_crm_team__website_ids
msgid "Websites using this Sales Team"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr ""

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_abandoned_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Your Address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Your Address\n"
"                                        <small> or </small>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Your Reference"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "Your cart is empty!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Your payment has been authorized."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "code..."
msgstr ""

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce"
msgstr "eCommerce"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
msgid "eCommerce Categories"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_delivery
msgid "eCommerce Shipping Costs"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.search count
msgid "found)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "hour(s)."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "items)"
msgstr ""

#. module: website_sale
#: model:product.product,weight_uom_name:website_sale.product_product_1
#: model:product.template,weight_uom_name:website_sale.product_product_1_product_template
msgid "kg"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "ml-2"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "o_website_sale_search"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "terms &amp; conditions"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
msgid "website"
msgstr "website"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_payment_acquirer_onboarding_wizard
msgid "website.sale.payment.acquirer.onboarding.wizard"
msgstr ""
