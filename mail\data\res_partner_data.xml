<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">

    <record id="base.partner_root" model="res.partner">
        <field name="name">OdooBot</field>
        <field name="email"><EMAIL></field>
        <field name="image_1920" type="base64" file="mail/static/src/img/odoobot.png"/>
    </record>

    <!-- user root should not receive emails at creation -->
    <record id="base.user_root" model="res.users">
        <field name="notification_type">inbox</field>
    </record>

</data></odoo>
