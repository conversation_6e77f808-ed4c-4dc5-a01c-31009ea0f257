<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
<t t-name="website.contentMenu.dialog.submenu">
    <li t-att-data-menu-id="submenu.fields['id']" t-att-data-mega-menu="submenu.fields['is_mega_menu'] ? true : undefined">
        <div class="input-group">
            <div class="input-group-prepend">
                <span class="input-group-text fa fa-bars" role="img" aria-label="Dropdown menu" title="Dropdown menu"/>
            </div>
            <span class="form-control d-flex align-items-center">
                <span class="js_menu_label o_text_overflow flex-grow-1">
                    <t t-esc="submenu.fields['name']"/>
                </span>
                <span t-if="submenu.fields['is_mega_menu']" class="badge badge-primary">Mega Menu</span>
                <i t-if="submenu.is_homepage" class="fa fa-home ml-3" role="img" aria-label="Home" title="Home"/>
            </span>
            <span class="input-group-append">
                <button type="button" class="btn btn-primary js_edit_menu fa fa-pencil-square-o" aria-label="Edit Menu Item" title="Edit Menu Item"/>
                <button type="button" class="btn btn-danger js_delete_menu fa fa-trash-o" aria-label="Delete Menu Item" title="Delete Menu Item"/>
            </span>
        </div>
        <t t-set="children" t-value="submenu.children"/>
        <ul t-if="children">
            <t t-foreach="children" t-as="submenu">
                <t t-call="website.contentMenu.dialog.submenu"/>
            </t>
        </ul>
    </li>
</t>
<div t-name="website.contentMenu.dialog.select">
    <select class="form-control mb16" t-if="widget.roots">
        <t t-foreach="widget.roots" t-as="root">
            <option t-att-value="root.id"><t t-esc="root.name"/></option>
        </t>
    </select>
</div>
<div t-name="website.contentMenu.dialog.edit">
    <select class="form-control mb16" t-if="widget.roots">
        <t t-foreach="widget.roots" t-as="root">
            <option t-att-value="root.id"><t t-esc="root.name"/></option>
        </t>
    </select>
    <ul class="oe_menu_editor list-unstyled">
        <t t-foreach="widget.menu.children" t-as="submenu">
            <t t-call="website.contentMenu.dialog.submenu"/>
        </t>
    </ul>
    <div class="mt32">
        <small class="float-right text-muted">
            Drag to the right to get a submenu
        </small>
        <a href="#" class="js_add_menu">
            <i class="fa fa-plus-circle"/> Add Menu Item
        </a><br/>
        <a href="#" class="js_add_menu" data-type="mega">
            <i class="fa fa-plus-circle"/> Add Mega Menu Item
        </a>
    </div>
</div>
</templates>
