# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>lvi<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Anatolij, 2022
# <PERSON><PERSON> Grigonis <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>as Versada <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: Ramunė ViaLaurea <<EMAIL>>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed_slides_count
msgid "# Completed Slides"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
msgid "# Views"
msgstr "# peržiūrų"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__public_views
msgid "# of Public Views"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr "# svetainės peržiūrų"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completion
msgid "% Completed Slides"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "'. Showing results for '"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_slides_list.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "(empty)"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ". This way, they will be secured."
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_5
msgid "3 Main Methodologies"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "<b>%s</b> is requesting access to this course."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<b>(empty)</b>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<b>Order by</b>"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"<b>Save & Publish</b> your lesson to make it available to your attendees."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "<b>Save</b> your question."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<b>Uncategorized</b>"
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been invited to join a new course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_completed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br/>\n"
"                        <p><b>Congratulations!</b></p>\n"
"                        <p>You've completed the course <b t-out=\"object.channel_id.name or ''\">Basics of Gardening</b></p>\n"
"                        <p>Check out the other available courses.</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Explore courses\n"
"                            </a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <t t-out=\"object.slide_type or ''\">document</t> <strong t-out=\"object.name or ''\">Trees</strong> with you!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong t-out=\"object.name or ''\">Trees</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        There is something new in the course <strong t-out=\"object.channel_id.name or ''\">Trees, Wood and Gardens</strong> you are following:<br/><br/>\n"
"                        <center><strong t-out=\"object.name or ''\">Trees</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View content</a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"fa fa-arrow-right mr-1\"/>All Courses"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> Statistika"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Lessons</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-chevron-left mr-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-circle-o-notch fa-spin mr-2\"/><b>Loading...</b>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-clock-o mr-2\" aria-label=\"Create date\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-clock-o mr-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-cloud-upload mr-1\"/>Upload new content"
msgstr "<i class=\"fa fa-cloud-upload mr-1\"/>Įkelkite naują turinį"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-code\"/> Embed"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-comments\"/> Comments ("
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-desktop mr-2\"/>\n"
"                <span class=\"d-none d-sm-inline-block\">Fullscreen</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-envelope\"/> Email"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope\"/> Send Email"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-eraser mr-1\"/>Clear filters"
msgstr "<i class=\"fa fa-eraser mr-1\"/>Valyti filtrus"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "<i class=\"fa fa-eraser\"/> Clear filters"
msgstr "<i class=\"fa fa-eraser\"/>Valyti filtrus"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This document is private."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-eye mr-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-code-o mr-2\" aria-label=\"Webpage\" role=\"img\" "
"title=\"Webpage\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-image-o mr-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-pdf-o mr-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-video-o mr-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-flag mr-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<i class=\"fa fa-flag text-warning\"/> Quiz"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen_sidebar_category
msgid "<i class=\"fa fa-flag-checkered text-warning mr-2\"/>Quiz"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-folder mr-2\" aria-label=\"Open folder\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-folder-o mr-1\"/><span>Add Section</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-folder-o mr-1\"/>Add a section"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "<i class=\"fa fa-graduation-cap mr-1\"/>All courses"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> Apie"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-home\"/> Course"
msgstr "<i class=\"fa fa-home\"/>Kursas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block ml-1\">Back "
"to course</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-info-circle\"/>\n"
"                    The social sharing module will be unlocked when a moderator will allow your publication."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<i class=\"fa fa-plus mr-1\"/> <span class=\"d-none d-md-inline-block\">Add "
"Content</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-plus mr-1\"/><span>Add Content</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>Add Question</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>Add Quiz</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-question mr-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/> Dalintis"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-share-alt\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Share</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "<i class=\"fa fa-share-square fa-fw\"/> Share"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-share-square\"/> Share"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Exit Fullscreen</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star text-black-25\" aria-label=\"A star\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star\" aria-label=\"A star\" role=\"img\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star-half-o\" aria-label=\"Half a star\" role=\"img\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid ""
"<i class=\"fa fa-tag mr-2 text-muted\"/>\n"
"                      My Courses"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"Dislikes\" "
"title=\"Dislikes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"Dislikes\" "
"title=\"Dislikes\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"Likes\" title=\"Likes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"Likes\" "
"title=\"Likes\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid ""
"<small class=\"text-success\">\n"
"                                Request already sent\n"
"                            </small>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid ""
"<small><span class=\"badge badge-pill badge-success pull-right my-1 py-1 "
"px-2 font-weight-normal\"><i class=\"fa fa-check\"/> "
"Completed</span></small>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-danger\">Unpublished</span>"
msgstr "<span class=\"badge badge-danger\">Nepaskelbtas</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge badge-info badge-arrow-right font-weight-normal px-2 "
"py-1 m-1\">New</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-info\">Preview</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge badge-light badge-hide border font-weight-normal px-2 "
"py-1 m-1\">Add Quiz</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid ""
"<span class=\"badge badge-pill badge-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/> Completed</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid ""
"<span class=\"badge badge-pill badge-success py-1 px-2 mx-auto\" "
"style=\"font-size: 1em\"><i class=\"fa fa-check\"/> Completed</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<span class=\"badge badge-pill badge-success py-1 px-2\" style=\"font-size: "
"1em\"><i class=\"fa fa-check\"/> Completed</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-check\"/> "
"Completed</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\"><span>Preview</span></span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\">Preview</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge font-weight-bold px-2 py-1 m-1 badge-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"d-none d-sm-inline-block\">Next</span> <i class=\"fa fa-"
"chevron-right ml-2\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                    Create a Google Project and Get a Key"
msgstr ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                    Sukurkite \"Google\" projektą ir gaukite raktą"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "<span class=\"fa fa-clipboard\"> Copy Text</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "<span class=\"font-weight-bold text-muted mr-2\">Current rank:</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "<span class=\"font-weight-normal\">Last update:</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid ""
"<span class=\"form-text text-muted d-block w-100\">Send presentation through"
" email</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Skaidrės</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_partner_view_form
msgid "<span class=\"o_stat_text\">Courses</span>"
msgstr "<span class=\"o_stat_text\">Kursai</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card_information_arrow
msgid "<span class=\"o_wslides_arrow\">New Content</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training
msgid "<span class=\"p-2\">Course content</span>"
msgstr "<span class=\"p-2\">Kurso turinys</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "<span class=\"text-500 mx-2\">•</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted font-weight-bold mr-3\">Rating</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Attendees</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Common tasks for a computer scientist</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Contents</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Parts of computer science</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"first\" class=\"mr-1 mr-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"fullscreen\" class=\"ml-1 ml-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ml-2 mr-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ml-2 mr-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span name=\"done_members_count_label\" class=\"text-muted\">Finished</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span name=\"members_done_count_label\" class=\"o_stat_text\">Finished</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span> hours</span>"
msgstr "<span> valandos</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<span>Add Tag</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Answering Questions</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking Question</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking the right question</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Logic</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Mathematics</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "<span>Preview</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Science</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>XP</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "<span>×</span>"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_fullscreen_player.js:0
#: code:addons/website_slides/static/src/js/slides_share.js:0
#, python-format
msgid "<strong>Thank you!</strong> Mail has been sent."
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_1
msgid "A Mighty Forest from Ages"
msgstr ""

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_0
msgid "A fruit"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"A good course has a structure. Pick a name for your first section and click "
"<b>Save</b> to create it."
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_4
msgid "A little chat with Harry Potted"
msgstr ""

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_2_gard2
msgid ""
"A lot of nice documentation: trees, wood, gardens. A gold mine for "
"references."
msgstr ""

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_0
msgid "A shovel"
msgstr ""

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_exclusion_html_content_and_url
msgid ""
"A slide is either filled with a document url or HTML content. Not both."
msgstr ""

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_1
msgid "A spoon"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_5
msgid "A summary of know-how: how and what."
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_0
msgid ""
"A summary of know-how: how and what. All the basics for this course about "
"gardening."
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_0
msgid ""
"A summary of know-how: what are the main trees categories and how to "
"differentiate them."
msgstr ""

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_2
msgid "A table"
msgstr ""

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_tag_slide_tag_unique
msgid "A tag must be unique!"
msgstr "Žyma turi būti unikali!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_1
msgid "A vegetable"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr "API raktas"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Granted"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_groups
msgid "Access Groups"
msgstr "Prieigos grupės"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Refused"
msgstr ""

#. module: website_slides
#: model:mail.activity.type,name:website_slides.mail_activity_data_access_request
msgid "Access Request"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_requested_access
msgid "Access Requested"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Access Rights"
msgstr "Prieigos teisės "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr "Reikalingas veiksmas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Actions"
msgstr "Veiksmai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "Aktyvus"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_ids
msgid "Activities"
msgstr "Veiklos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Veiklos Išimties Dekoravimas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_state
msgid "Activity State"
msgstr "Veiklos būsena"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_icon
msgid "Activity Type Icon"
msgstr "Veiklos tipo ikona"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Add"
msgstr "Pridėti"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_comment
msgid "Add Comment"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Content"
msgstr "Pridėti turinį"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_review
msgid "Add Review"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Section"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Add Tag"
msgstr ""

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
msgid "Add a new lesson"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#, python-format
msgid "Add a section"
msgstr "Pridėti sekciją"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#, python-format
msgid "Add a tag"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add an answer below this one"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add comment on this answer"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Add existing contacts..."
msgstr "Pridėti egzistuojančias sutartis..."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid ""
"Add quizzes at the end of your lessons to evaluate what your students "
"understood."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_ids
msgid "Additional Resource for this slide"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Additional Resources"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_resource
msgid "Additional resource for a particular slide"
msgstr ""

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_advanced
msgid "Advanced"
msgstr "Išplėstinis"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "All Courses"
msgstr "Visi kursai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "All completed classes and earned karma will be lost."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__partner_ids
msgid "All members of the channel."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "All questions must be answered !"
msgstr ""

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_5_furn2
msgid "All you need to know about furniture creation."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow Download"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "Leisti peržiūrėti"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Allow Rating"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__allow_comment
msgid "Allow rating on Course"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Allow students to review your course"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow the user to download the content of the slide."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_allow_comment
msgid "Allows comment"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already Requested"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Already installing \"%s\"."
msgstr "Jau įdiegiama \"%s\"."

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already member"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Amazing!"
msgstr ""

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_2
msgid "And also bananas"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__text_value
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answer_ids
msgid "Answer"
msgstr "Atsakymas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Rodoma"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_visibility
msgid ""
"Applied directly as ACLs. Allow to hide channels and their content for non "
"members."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive"
msgstr "Archyvuoti"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive Slide"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr "Archyvuotas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to archive this slide ?"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to delete this category ?"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Are you sure you want to delete this question :"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Attachment"
msgstr "Prisegtukas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr "Prisegtukų skaičius"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__attachment_ids
msgid "Attachments"
msgstr "Prisegtukai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_avg
msgid "Attempts Avg"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_count
msgid "Attempts Count"
msgstr ""

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_attendees
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Attendees"
msgstr "Dalyviai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_done_count
msgid "Attendees Done Count"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_count
msgid "Attendees count"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Attendees of %s"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_group_ids
msgid "Auto Enroll Groups"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Badges"
msgstr "Ženkleliai"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_basic
msgid "Basic"
msgstr "Bazinis"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_5_furn2
msgid "Basics of Furniture Creation"
msgstr ""

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_0_gard_0
msgid "Basics of Gardening"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Be notified when a new content is added."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_comment
msgid "Can Comment"
msgstr "Gali komentuoti"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__can_edit_body
msgid "Can Edit Body"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_publish
msgid "Can Publish"
msgstr "Gali publikuoti"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_review
msgid "Can Review"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr "Gali įkelti"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_vote
msgid "Can Vote"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#, python-format
msgid "Cancel"
msgstr "Atšaukti"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_carpenter
msgid "Carpenter"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Catchy Headline"
msgstr "Pagauli antraštė"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_category_ids
msgid "Categories"
msgstr "Kategorijos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Category"
msgstr "Kategorija"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_1
msgid "Certification"
msgstr "Sertifikavimas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Certifications"
msgstr "Sertifikatas"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_certification
#: model:gamification.challenge.line,name:website_slides.badge_data_certification_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_certification_goal
msgid "Certified Knowledge"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Change video privacy settings"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Channel"
msgstr "Kanalas"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_invite
msgid "Channel Invitation Wizard"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Channel Member"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_type
msgid "Channel type"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__completed
msgid "Channel validated, even if slides / lessons are added once done."
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag_group
msgid "Channel/Course Groups"
msgstr "Kanalas/Kurso grupės"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag
msgid "Channel/Course Tag"
msgstr "Kanalas/Kurso žyma"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_invite
msgid "Channel: Invite by email"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__channel_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "Kanalai"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_cheatsheet
msgid "CheatSheet"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check Profile"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check answers"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check your answers"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Choose a <b>File</b> on your computer."
msgstr "Pasirinkti <b>failą</b> iš Jūsų kompiuterio."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Choose a PDF or an Image"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Choose a layout"
msgstr ""

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_3_furn0
msgid "Choose your wood !"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Clear filters"
msgstr "Valyti filtrus"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Click here to send a verification email allowing you to participate at the "
"eLearning."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to start the course"
msgstr "Paspauskite norėdami pradėti šį kursą"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Click on \"New\" in the top-right corner to write your first course."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on the <b>Create</b> button to create your first course."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on your <b>Course</b> to go back to the table of content."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
#, python-format
msgid "Close"
msgstr "Uždaryti"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Color"
msgstr "Spalva"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__color
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__color
msgid "Color Index"
msgstr "Spalvos indeksas"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_colorful
msgid "Colorful"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__comment
msgid "Comment"
msgstr "Komentaras"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Comments"
msgstr "Komentarai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering                               questions. In this course, you'll "
"study those topics with activities about mathematics, science and logic."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions. In this course, you'll study those topics with "
"activities about mathematics, science and logic."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Communication"
msgstr "Komunikacija"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_karma
#: model:gamification.challenge.line,name:website_slides.badge_data_karma_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_karma_goal
msgid "Community hero"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_company_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_company_count
msgid "Company Course Count"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_0
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_0
msgid "Comparing Hardness of Wood Species"
msgstr ""

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_course_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_course
msgid "Complete a course"
msgstr "Baigti kursą"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_profile_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_profile
msgid "Complete your profile"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Completed"
msgstr "Atlikta"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: model:mail.template,name:website_slides.mail_template_channel_completed
#, python-format
msgid "Completed Course"
msgstr "Pabaigtas kursas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_completed_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_completed_ids
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Completed Courses"
msgstr "Pabaigti kursai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completion
msgid "Completion"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed_template_id
msgid "Completion Email"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Completion Time"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Compose Email"
msgstr "Rašyti el. laišką"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Computer Science for kids"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_enroll
msgid "Condition to enroll: everyone, on invite, on payment (sale bridge)."
msgstr "Registracijos sąlyga: visiems, pakvietus, nusipirkus kursą."

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_configuration
msgid "Configuration"
msgstr "Konfigūracija"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_completed
msgid "Congratulation! You completed {{ object.channel_id.name }}"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations! Your first lesson is available. Let's see the options "
"available here. The tag \"<b>New</b>\" indicates that this lesson was "
"created less than 7 days ago."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Congratulations, you have reached the last rank!"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, you've created your first course.<br/>Click on the title of"
" this content to see it in fullscreen mode."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, your course has been created, but there isn't any content "
"yet. First, let's add a <b>Section</b> to give your course a structure."
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_partner
msgid "Contact"
msgstr "Kontaktas"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_6_furn3
#, python-format
msgid "Contact Responsible"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Contact all the members of a course via mass mailing"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Contact the responsible to enroll."
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Contact us"
msgstr "Susisiekite su mumis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__datas
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Content"
msgstr "Turinys"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Content Preview"
msgstr "Turinio peržiūra"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_question
msgid "Content Quiz Question"
msgstr ""

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_content_tags
msgid "Content Tags"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Title"
msgstr ""

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_slide_action
#: model:ir.actions.act_window,name:website_slides.slide_slide_action_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_content
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_contents
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Contents"
msgstr "Turinys"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Continue"
msgstr "Tęsti"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Copy Link"
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_1
msgid "Correct !"
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_0
msgid "Correct ! A shovel is the perfect tool to dig a hole."
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_0
msgid "Correct ! A strawberry is a fruit because it's the product of a tree."
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_0
msgid "Correct ! Congratulations you have time to loose"
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Correct ! You did it !"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Could not fetch data from url. Document or access right not available:\n"
"%s"
msgstr ""
"Iš nuorodos nepavyko gauti duomenų. Nepasiekiamas dokumentas arba netinkamos prieigos teisės:\n"
"%s"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Course"
msgstr "Kursas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_count
msgid "Course Count"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Group Name"
msgstr "Kursųų grupės pavadinimas"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_group_action
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_course_groups
msgid "Course Groups"
msgstr "Kursų grupės"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Course Name"
msgstr "Kurso pavadinimas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_form
msgid "Course Tag"
msgstr "Kursų žyma"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Tag Group"
msgstr "Kursų žymų grupė"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Course Tag Groups"
msgstr "Kursų žymų grupės"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_tree
msgid "Course Tags"
msgstr "Kursų žymos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Course Title"
msgstr "Kurso antraštė"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_finish
msgid "Course finished"
msgstr "Kursas pabaigtas"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Course not published yet"
msgstr "Mokymosi programa nepaskelbta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_rank
msgid "Course ranked"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_type
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_type
msgid "Course type"
msgstr "Kurso tipas"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Course: %s"
msgstr "Kursas: %s"

#. module: website_slides
#: code:addons/website_slides/models/website.py:0
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_courses
#: model:website.menu,name:website_slides.website_menu_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#, python-format
msgid "Courses"
msgstr "Kursai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Create"
msgstr "Sukurti"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Create a community and let the members help each others"
msgstr ""

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid "Create a course"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Create new %s '%s'"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Create new Tag '%s'"
msgstr ""

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "Create new content for your eLearning"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
msgid "Creation Date"
msgstr "Sukūrimo data"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__html_content
msgid "Custom HTML content for slides of type 'Web Page'."
msgstr ""

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_6_furn3
msgid "DIY Furniture"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__null_value
msgid "Default Value"
msgstr "Numatytoji reikšmė"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Nustatykite iššūkio matomumą per meniu"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#, python-format
msgid "Delete"
msgstr "Trinti"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
#, python-format
msgid "Delete Category"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Delete Question"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__promote_strategy
msgid ""
"Depending the promote strategy, a slide will appear on the top of the course's page :\n"
" * Latest Published : the slide created last.\n"
" * Most Voted : the slide which has to most votes.\n"
" * Most Viewed ; the slide which has been viewed the most.\n"
" * Specific : You choose the slide to appear.\n"
" * None : No slides will be shown.\n"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Description"
msgstr "Aprašymas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_html
msgid "Detailed Description"
msgstr ""

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_1
msgid "Did you read the whole article ?"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Discard"
msgstr "Atmesti"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Discover more"
msgstr "Atrasti daugiau"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Dislikes"
msgstr "Neigiami įvertinimai"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Display"
msgstr "Vaizdavimas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_question__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__sequence
msgid "Display order"
msgstr "Atvaizdavimo tvarka"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_0
msgid "Do you make beams out of lemon trees ?"
msgstr ""

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_1
msgid "Do you make lemons out of beams ?"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Do you really want to leave the course?"
msgstr "Tikrai norite išeiti iš kurso?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_0
msgid "Do you think Harry Potted has a good name ?"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Do you want to install the \"%s\" app?"
msgstr ""

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_5_3_question_0
msgid "Do you want to reply correctly ?"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Do you want to request access to this course ?"
msgstr ""

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__document
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Document"
msgstr "Dokumentas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_id
msgid "Document ID"
msgstr "Dokumento ID"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "Document URL"
msgstr "Dokumento nuoroda"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__documentation
msgid "Documentation"
msgstr "Dokumentacija"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Documentation Layout"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_document
#: model:slide.slide,name:website_slides.slide_category_demo_4_0
msgid "Documents"
msgstr "Dokumentai"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_2
msgid "Dog Friendly"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Don't have an account ?"
msgstr "Neturite paskyros?"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Done"
msgstr "Atlikta"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Done !"
msgstr "Atlikta!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__done_count
msgid "Done Count"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "Atsisiųsti"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download Content"
msgstr "Atsisiųsti turinį."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_10
msgid "Drawing 1"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_11
msgid "Drawing 2"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Dropdown menu"
msgstr "Išsiskleidžiantis meniu"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_time
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__completion_time
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#, python-format
msgid "Duration"
msgstr "Trukmė"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Edit"
msgstr "Redaguoti"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Edit in backend"
msgstr "Redaguoti programinį kodą"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_email
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Email"
msgstr "El. paštas"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid "Email attendees once a new content is published"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__completed_template_id
msgid "Email attendees once they've finished the course"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_template_id
msgid "Email template used when sharing a slide"
msgstr "Laiško šablonas, naudojamas dalijantis skaidrėmis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr "Įterptas kodas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embedcount_ids
msgid "Embed Count"
msgstr "Įterpinių skaičius"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in your website"
msgstr "Įterpkite į savo svetainę"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "Įterptų skaidrių peržiūrų skaičius"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "End course"
msgstr "Baigti kursą"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_2
msgid "Energy Efficiency Facts"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Enjoy this exclusive content !"
msgstr "Mėgaukitės šiuo išskirtiniu turiniu!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_msg
msgid "Enroll Message"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_enroll
msgid "Enroll Policy"
msgstr "Prisijungimo prie kurso politika"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Enrolled On"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter at least two possible <b>Answers</b>."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter your <b>Question</b>. Be clear and concise."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Enter your answer"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Enter your question"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Estimated slide completion time"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Evaluate and certify your students."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Evaluate your students and certify them"
msgstr ""

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_exercises
msgid "Exercises"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "External Links"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_link
msgid "External URL for a particular slide"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__link_ids
msgid "External URL for this slide"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "External sources"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Failed to install \"%s\"."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__model_object_field
msgid "Field"
msgstr "Laukas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/controllers/main.py:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "File is too big. File size cannot exceed 25MB"
msgstr "Failas per didelis. Failo dydis negali viršyti 25MB."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Filter &amp; order"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr "Galutinė vietos ženklo išraiška, kopijuojama į norimą šablono lauką."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Finish Course"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "First attempt"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"First, create your lesson, then edit it with the website builder. You'll be "
"able to drop building blocks on your page and edit them."
msgstr ""
"Pirmiausia sukurkite mokymus, tada redaguokite juos naudodami svetainių "
"kūrimo įrankius. Galėsite naudoti e-puslapio konstravimo blokus ir juos "
"redaguoti."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "First, let's add a <b>Presentation</b>. It can be a .pdf or an image."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "First, upload your videos on YouTube and mark them as"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
#, python-format
msgid "Followed Courses"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "Sekėjai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekėjai (partneriai)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome piktograma, pvz., fa-tasks"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_0
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Foreword"
msgstr "Pratarmė"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_0
msgid "Foreword for this documentation: how to use it, main attention points"
msgstr "Įvadas į šį dokumentą: kaip naudotis, į ką atkreipti dėmesį"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_forum
msgid "Forum"
msgstr "Forumas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Fourth and more attempt"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_2
msgid "From a piece of wood to a fully functional furniture, step by step."
msgstr ""

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_furniture
msgid "Furniture Designer"
msgstr "Baldų dizaineris"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_4_furn1
msgid "Furniture Technical Specifications"
msgstr "Baldų techninės specifikacijos"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_12
msgid "GLork"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Sužaidybinimo iššūkis"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_gardener
msgid "Gardener"
msgstr "Sodininkas"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_0
msgid "Gardening: The Know-How"
msgstr "Sodininkystė: Žinoti kaip"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Generate revenues thanks to your courses"
msgstr ""

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_certification_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_certification
msgid "Get a certification"
msgstr "Gauti sertifikatą"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_register
#: model:gamification.challenge.line,name:website_slides.badge_data_register_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_register_goal
msgid "Get started"
msgstr "Pradėkite"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course a helpful <b>Description</b>."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course an engaging <b>Title</b>."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr "Google Doc raktas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr "Google Drive API raktas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/components/activity/activity.xml:0
#, python-format
msgid "Grant Access"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_graph
msgid "Graph of Contents"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_id
msgid "Group"
msgstr "Grupė"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "Grupuoti pagal"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__name
msgid "Group Name"
msgstr "Grupės pavadinimas"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid "Group of users allowed to publish contents on a documentation course."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_sequence
msgid "Group sequence"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__html_content
msgid "HTML Content"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP nukreipimas"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_1
msgid "Hand on !"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_message
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__has_message
msgid "Has Message"
msgstr "Turi žinutę"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_6
msgid "Here is How to get the Sweetest Strawberries you ever tasted!"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Home"
msgstr "Pradžia"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_1
msgid "Home Gardening"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_2
msgid "How To Build a HIGH QUALITY Dining Table with LIMITED TOOLS"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_3
msgid "How to Grow and Harvest The Best Strawberries | Basics"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_6
msgid ""
"How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to create a Lesson as a Web Page?"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_1
msgid "How to find quality wood"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_3
msgid "How to plant a potted tree"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your PowerPoint Presentations or Word Documents?"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your videos ?"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_2
msgid "How to wall decorating by tree planting in hanging plastic bottles."
msgstr ""

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_howto
msgid "HowTo"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_question__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "ID"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon"
msgstr "Piktograma"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Išimties veiklą žyminti piktograma."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__allow_comment
#: model:ir.model.fields,help:website_slides.field_slide_slide__channel_allow_comment
msgid ""
"If checked it allows members to either:\n"
" * like content and post comments on documentation course;\n"
" * post comment and review on training course;"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeigu pažymėta, naujiems pranešimams reikės jūsų dėmesio."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jei pažymėta, yra žinučių, turinčių pristatymo klaidų."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_4_furn1
msgid ""
"If you are looking for technical specifications, have a look at this "
"documentation."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"If you want to be sure that attendees have understood and memorized the "
"content, you can add a Quiz on the lesson. Click on <b>Add Quiz</b>."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1920
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1920
msgid "Image"
msgstr "Paveikslėlis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1024
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1024
msgid "Image 1024"
msgstr "Paveikslėlis "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_128
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_128
msgid "Image 128"
msgstr "Paveikslėlis 128"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_256
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_256
msgid "Image 256"
msgstr "Paveikslėlis 256"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_512
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_512
msgid "Image 512"
msgstr "Paveikslėlis 512"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_0
msgid "Incorrect !"
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_1
msgid "Incorrect ! A strawberry is not a vegetable."
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_2
msgid "Incorrect ! A table is a piece of furniture."
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_1
msgid "Incorrect ! Good luck digging a hole with a spoon..."
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_2
msgid "Incorrect ! Seriously ?"
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_1
msgid "Incorrect ! You better think twice..."
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_1
msgid "Incorrect ! You really should read it."
msgstr ""

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_2
msgid "Incorrect ! of course not ..."
msgstr ""

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__infographic
msgid "Infographic"
msgstr "Infografikas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographic
msgid "Infographics"
msgstr "Inforgrafikai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Install"
msgstr "Diegti"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Installing \"%s\"."
msgstr "Įdiegiama \"%s\"."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_0
msgid "Interesting Facts"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_1
msgid "Interesting Tree Facts"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_1
msgid "Interesting information about home gardening. Keep it close !"
msgstr ""

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_intermediate
msgid "Intermediate"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"Vidinė serverio klaida. Bandykite dar kartą arba susisiekite su administratoriumi.\n"
"Klaidos pranešimas: %s"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Invalid file type. Please select pdf or image file"
msgstr "Netinkamas failo tipas. Pasirinkite PDF arba paveikslėlio tipo failą."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Invite"
msgstr "Kviesti"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed
msgid "Is Completed"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__is_mail_template_editor
msgid "Is Editor"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr "Yra sekėjas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member
msgid "Is Member"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_new_slide
msgid "Is New Slide"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is Published"
msgstr "Paskelbta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_category
msgid "Is a category"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__is_correct
msgid "Is correct answer"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_3
msgid ""
"Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and "
"Landscaping. Narrated by Leif Knecht, owner."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Join & Submit"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
#, python-format
msgid "Join Course"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Join the Course"
msgstr "Prisijungti prie kurso"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Join the course to take the quiz and verify your answers!"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_2
msgid "Just some basics Energy Efficiency Facts."
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_1
msgid "Just some basics Interesting Tree Facts."
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_0
msgid "Just some basics Tree Infographic."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Karma"
msgstr "Karma"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_comment
msgid "Karma needed to add a comment on a slide of this course"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_review
msgid "Karma needed to add a review on the course"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_vote
msgid "Karma needed to like/dislike a slide of this course."
msgstr ""

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_profile
#: model:gamification.challenge.line,name:website_slides.badge_data_profile_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_profile_goal
msgid "Know yourself"
msgstr ""

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_3_furn0
msgid ""
"Knowing which kind of wood to use depending on your application is important. In this course you\n"
"will learn the basics of wood characteristics."
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_3
msgid ""
"Knowing wood characteristics is a requirement in order to know which kind of"
" wood to use in a given situation."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__lang
msgid "Language"
msgstr "Kalba"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Action On"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_embed____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_question____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_tag____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_last_update
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Last Update"
msgstr "Paskutinis atnaujinimas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__latest
msgid "Latest Published"
msgstr "Paskutinis paskelbtas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "Latest achievements"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "Leaderboard"
msgstr "Lyderių sąrašas"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_1_gard1
msgid ""
"Learn how to take care of your favorite trees. Learn when to plant, how to "
"manage potted trees, ..."
msgstr ""

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_0_gard_0
msgid "Learn the basics of gardening !"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_1
msgid "Learn to identify quality wood in order to create solid furnitures."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Leave the course"
msgstr "Išeiti iš kurso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Lesson"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Lesson Nav"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_slide_vote
msgid "Lesson voted"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Lessons"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz_finish.js:0
#, python-format
msgid "Level up!"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Likes"
msgstr "Teigiami vertinimai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__link
msgid "Link"
msgstr "Nuoroda"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__template_id
msgid "Mail Template"
msgstr "Laiško šablonas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_mass_mailing_slides
msgid "Mailing"
msgstr "Laiškų siuntimas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pagrindinis prisegtukas"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_0
msgid "Main Trees Categories"
msgstr ""

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_manager
msgid "Manager"
msgstr "Vadovas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Mark the correct answer by checking the <b>correct</b> mark."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Member"
msgstr "Narys"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_ids
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Members"
msgstr "Nariai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_ids
msgid "Members Information"
msgstr ""

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__members
msgid "Members Only"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Members Views"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_group_ids
msgid ""
"Members of those groups are automatically added as members of the channel."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Menu Entry"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr "Žinutės pristatymo klaida"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_msg
msgid "Message explaining the enroll process"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "Žinutės"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_1
msgid "Methods"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_2
msgid "Mighty Carrots"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_1
msgid ""
"Mighty forest just don't appear in a few weeks. Learn how time made our "
"forests mighty and mysterious."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__mime_type
msgid "Mime-type"
msgstr "MIME tipas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Minutes"
msgstr "Minutės"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Missing \"Tag Group\" for creating a new \"Tag\"."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Mobile sub-nav"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "More info"
msgstr "Daugiau informacijos"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_viewed
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Most Viewed"
msgstr "Dažniausiai žiūrėta"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_voted
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Most Voted"
msgstr "Daugiausia balsuota"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Most popular courses"
msgstr "Populiariausi kursai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Veiklos paskutinis terminas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "My Courses"
msgstr "Mano kursai"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "My courses"
msgstr "Mano kursai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
msgid "Name"
msgstr "Pavadinimas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Nav"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "New"
msgstr "Naujas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "New Certification"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "New Content Email"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "New Course"
msgstr "Naujas kursas"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid "New {{ object.slide_type }} published on {{ object.channel_id.name }}"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Newest"
msgstr "Naujausia"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Newest courses"
msgstr "Naujausi kursai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Next"
msgstr "Kitas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kitas veiklos kalendoriaus įvykis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Kito veiksmo terminas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_summary
msgid "Next Activity Summary"
msgstr "Kito veiksmo santrauka"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_id
msgid "Next Activity Type"
msgstr "Kito veiksmo tipas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Next rank:"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_1
#, python-format
msgid "No"
msgstr "Ne"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "No Course created yet."
msgstr "Kol kas nesukurtas nei vienas kursas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No completed courses yet!"
msgstr "Dar nėra pabaigtų kursų!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "No content was found using your search"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search"
msgstr "Nerasta nė vieno kurso, atitinkančio jūsų paiešką"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search."
msgstr "Nerasta nė vieno kurso, atitinkančio jūsų paiešką."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "No data yet!"
msgstr "Jokių duomenų!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No followed courses yet!"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "No leaderboard currently :("
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "No presentation available."
msgstr "Nėra pasiekiamų prezentacijų."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No results found for '"
msgstr ""

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__none
msgid "None"
msgstr "Nieko"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Not enough karma to comment"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Not enough karma to review"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Notifications"
msgstr "Pranešimai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr "Veiksmų skaičius"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_document
msgid "Number of Documents"
msgstr "Dokumentų skaičius"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_infographic
msgid "Number of Infographics"
msgstr "Infografikų skaičius"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_presentation
msgid "Number of Presentations"
msgstr "Prezentacijų skaičius"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_quiz
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_quiz
msgid "Number of Quizs"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_video
msgid "Number of Videos"
msgstr "VIdeo skaičius"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_webpage
msgid "Number of Webpages"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__comments_count
msgid "Number of comments"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of errors"
msgstr "Klaidų kiekis"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Žinučių, kurioms reikia jūsų veiksmo, skaičius"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Žinučių su pristatymo klaida skaičius"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread_counter
msgid "Number of unread messages"
msgstr "Neperskaitytų žinučių skaičius"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__questions_count
msgid "Numbers of Questions"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Odoo"
msgstr "Odoo"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Odoo • Image and Text"
msgstr "Odoo • Paveikslėlis ir tekstas"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_officer
msgid "Officer"
msgstr "Vadovas"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__invite
msgid "On Invitation"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Once you're done, don't forget to <b>Publish</b> your course."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Only JPG, PNG, PDF, files types are supported"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Papildoma reikšmė nustatymui, jei nurodytas laukas yra tuščias"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Options"
msgstr "Pasirinkimai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__partner_id
msgid "Partner"
msgstr "Partneris"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_has_new_content
msgid "Partner Has New Content"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"People already took this quiz. To keep course progression it should not be "
"deleted."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__copyvalue
msgid "Placeholder Expression"
msgstr "Vietaženklio išraiška"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Please"
msgstr "Prašome"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to join this course"
msgstr ""
"Prašome<a href=\"/web/login?redirect=%s\">prisijungti prie paskyros</a>, "
"norėdami dalyvauti kursuose"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to vote this lesson"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to join "
"this course"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to vote "
"this lesson"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Please enter valid Youtube or Google Doc URL"
msgstr "Prašome įvesti galiojančią Youtube ar Google Doc nuorodą"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Please enter valid youtube or google doc url"
msgstr "Prašome įvesti galiojančią Youtube ar Google Doc nuorodą"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz_question_form.js:0
#, python-format
msgid "Please fill in the question"
msgstr "Prašome atsakyti į klausimą"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "Prašome užpildyti šį lauką"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Please select at least one recipient."
msgstr ""

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_course
#: model:gamification.challenge.line,name:website_slides.badge_data_course_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_course_goal
msgid "Power User"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Powered by"
msgstr "Technologija"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__presentation
#: model:slide.slide,name:website_slides.slide_slide_demo_4_12
#, python-format
msgid "Presentation"
msgstr "Prezentacija"

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "Prezentacija publikuota"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_presentation
msgid "Presentations"
msgstr "Prezentacijos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Preview"
msgstr "Peržiūra"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Private Course"
msgstr "Privatus kursas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Progress"
msgstr "Eiga"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Promoted Content"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promoted_slide_id
msgid "Promoted Slide"
msgstr ""

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__public
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__public
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Public"
msgstr "Viešas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Public Views"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Publication Date"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr "Publikavimo data"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "Paskelbtas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Published Date"
msgstr "Paskelbimo data"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Publishing is restricted to the responsible of training courses or members "
"of the publisher group for documentation courses"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__question_id
msgid "Question"
msgstr "Klausimas"

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer and at least 1 incorrect answer"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__question
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Question Name"
msgstr "Klausimo pavadinimas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__question_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Questions"
msgstr "Klausimai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__quiz
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Quiz"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Quiz Demo Data"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__quiz_attempts_count
msgid "Quiz attempts count"
msgstr ""

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_question_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_quizzes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree_report
msgid "Quizzes"
msgstr ""

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_ids
msgid "Rating"
msgstr "Įvertinimas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_graph_slide_channel
msgid "Rating Average"
msgstr "Įvertinimo vidurkis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_stars
msgid "Rating Average (Stars)"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Paskutinio įvertinimo atsiliepimas "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Paskutinio įvertinimo paveikslėlis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Paskutinio įvertinimo reikšmė"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_count
msgid "Rating count"
msgstr "Įvertinimų kiekis"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Rating of %s"
msgstr ""

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_karma_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_karma
msgid "Reach 2000 XP"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Reach new heights"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "Įvertinimo priežastis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__partner_ids
msgid "Recipients"
msgstr "Gavėjai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/components/activity/activity.xml:0
#, python-format
msgid "Refuse Access"
msgstr ""

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_register_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_register
msgid "Register to the platform"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Related"
msgstr "Susijęs"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Remove"
msgstr "Pašalinti"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove the answer comment"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove this answer"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__render_model
msgid "Rendering Model"
msgstr ""

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report
msgid "Reporting"
msgstr "Ataskaitos"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request Access."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request sent !"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Reset"
msgstr "Atstatyti"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__data
msgid "Resource"
msgstr "Išteklius"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Resources"
msgstr "Ištekliai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__user_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_user_id
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Responsible"
msgstr "Atsakingas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_user_id
msgid "Responsible User"
msgstr "Atsakingas vartotojas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Responsible already contacted."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict publishing to this website."
msgstr "Apriboti skelbimą šioje svetainėje."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Retry"
msgstr "Pakartoti"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.private_profile
msgid "Return to the course."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Review"
msgstr "Peržiūrėti"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Review Course"
msgstr "Kurso apžvalga"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_reviews
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_reviews
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Reviews"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/portal_chatter.js:0
#, python-format
msgid "Reviews (%d)"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_fourth_attempt_reward
msgid "Reward: every attempt after the third try"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_first_attempt_reward
msgid "Reward: first attempt"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_second_attempt_reward
msgid "Reward: second attempt"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_third_attempt_reward
msgid "Reward: third attempt"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Rewards"
msgstr "Atlygis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimizuotas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS pristatymo klaida"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Save"
msgstr "Išsaugoti"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Save & Publish"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Save your presentations or documents as PDF files and upload them."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search"
msgstr "Paieška"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Contents"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search courses"
msgstr "Ieškoti kursų"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search in content"
msgstr "Ieškoti pagal turinį"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Second attempt"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
#, python-format
msgid "Section"
msgstr "Sekcija"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Section Subtitle"
msgstr "Sekcijos poraštė"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Section name"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_token
msgid "Security Token"
msgstr "Apsaugos prieigos raktas"

#. module: website_slides
#: code:addons/website_slides/models/res_users.py:0
#, python-format
msgid "See our eLearning"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Select <b>Course</b> to create it and manage it."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Select page to start with"
msgstr "Pasirinkite puslapį, nuo kurio pradėsite"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Pasirinkite tikslinį lauką iš susijusio dokumento modelio.\n"
"Jei tai yra santykio laukas, galėsite pasirinkti tikslinį lauką santykio tiksle."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Select the correct answer below :"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_sale_slides
msgid "Sell on eCommerce"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Send"
msgstr "Siųsti"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Send Email"
msgstr "Siųsti el. laišką"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__seo_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__seo_name
msgid "Seo name"
msgstr "SEO pavadinimas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_question__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__sequence
msgid "Sequence"
msgstr "Seka"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Set Done"
msgstr ""

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.website_slides_action_settings
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_settings
msgid "Settings"
msgstr "Nustatymai"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Share"
msgstr "Dalintis"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share Channel"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
#, python-format
msgid "Share Link"
msgstr "Dalintis nuoroda"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_template_id
msgid "Share Template"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Share by mail"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Facebook"
msgstr "Pasidalinti \"Facebook\""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on LinkedIn"
msgstr "Pasidalinti \"LinkedIn\""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_body
#, python-format
msgid "Share on Social Networks"
msgstr "Dalintis socialiniuose tinkluose"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Twitter"
msgstr "Pasidalinti \"Twitter\""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_short
msgid "Short Description"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_4
msgid "Show your newly mastered knowledge !"
msgstr "Pademonstruokite Jūsų naujai įgytas žinias!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign Up !"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Sign in"
msgstr "Prisijungti"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign in and join the course to verify your answers!"
msgstr ""
"Prisijunkite prie paskyros ir kurso, kad patikrintumėte savo atsakymus!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Size"
msgstr "Dydis"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Skill up and have an impact! Your business career starts here.<br/>Time to "
"start a course."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__slide_id
msgid "Slide"
msgstr "Skaidrė"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr ""

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_published
msgid "Slide Published"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_answer
msgid "Slide Question's Answer"
msgstr ""

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_shared
msgid "Slide Shared"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "Skaidrės žyma"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_partner_ids
msgid "Slide User Data"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_id
msgid "Slide channel"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#, python-format
msgid "Slide image"
msgstr "Skaidrės paveikslėlis"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Slide with questions must be marked as done when submitting all good answers"
" "
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_content_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_ids
msgid "Slides"
msgstr "Skaidrės"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
msgid "Slides and categories"
msgstr ""

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_6_furn3
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_6_furn3
msgid "So much amazing certification."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Sort by"
msgstr "Rūšiuoti pagal"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__specific
msgid "Specific"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Start Course"
msgstr "Pradėti kursą"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Start Course Channel"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Start with the customer – find out what they want and give it to them."
msgstr ""
"Pradėkite nuo kliento – sužinokite, ko jie nori, ir duokite jiems tai."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Start your online course today!"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Statistics"
msgstr "Statistika"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Būsena, paremta veiklomis\n"
"Vėluojantis: Termino data jau praėjo\n"
"Šiandien: Veikla turi būti baigta šiandien\n"
"Suplanuotas: Ateities veiklos."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__sub_model_object_field
msgid "Sub-field"
msgstr "Polaukis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__sub_object
msgid "Sub-model"
msgstr "Submodelis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__subject
msgid "Subject"
msgstr "Tema"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Subject..."
msgstr "Tema..."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Subscribe"
msgstr "Prenumeruoti"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information"
msgstr "Sekėjų informacija"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information for the current logged in user"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__partner_ids
msgid "Subscribers"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_partner_ids
msgid "Subscribers information"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Succeed and gain karma"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
#, python-format
msgid "Tag"
msgstr "Žyma"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#, python-format
msgid "Tag Group"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Tag Group (required for new tags)"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Tag Name"
msgstr "Žymos pavadinimas"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag__color
msgid ""
"Tag color used in both backend and website. No color means no display in "
"kanban or front-end, to distinguish internal tags from public categorization"
" tags"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_data_other
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
#, python-format
msgid "Tags"
msgstr "Žymos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "Žymos..."

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_1_gard1
msgid "Taking care of Trees"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_4_1
msgid "Technical Drawings"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_10
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_11
msgid "Technical drawing"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_2
msgid "Test Yourself"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_4
msgid "Test your knowledge"
msgstr "Patikrinkite savo žinias"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_3
msgid "Test your knowledge !"
msgstr "Patikrinkite savo žinias!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Test your students with small Quizzes"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Duration</b> of the lesson is based on the number of pages of your "
"document. You can change this number if your attendees will need more time "
"to assimilate the content."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Title</b> of your lesson is autocompleted but you can change it if "
"you want.</br>A <b>Preview</b> of your file is available on the right side "
"of the screen."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description_short
msgid "The description that is displayed on the course card"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description
msgid ""
"The description that is displayed on top of the course page, just below the "
"title"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"The document type will be set automatically based on the document URL and "
"properties (e.g. height and width for presentation and document)."
msgstr ""
"Dokumento tipas bus nustatytas automatiškai, remiantis jo nuoroda ir "
"savybėms (pvz., aukštis ir plotis prezentacijoms ar dokumentams)."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__completion_time
msgid "The estimated completion time for this slide"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external sign up in configuration."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr "Pilna nuoroda dokumento pasiekimui per svetainę."

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_theory
msgid "Theory"
msgstr ""

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "There are no quizzes"
msgstr ""

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel_report
msgid "There are no ratings for these courses at the moment"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr "Trečiųjų šalių svetainės nuoroda"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Third attempt"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_answer__comment
msgid "This comment will be displayed to the user if he selects this answer"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "This course is private."
msgstr "Šis kursas yra privatus."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer, congratulations"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr ""

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This video already exists in this channel on the following slide: %s"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__name
#, python-format
msgid "Title"
msgstr "Pavadinimas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Toggle navigation"
msgstr "Perjungti navigaciją"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_tools
msgid "Tools"
msgstr "Įrankiai"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_0
msgid "Tools and Methods"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_0
msgid "Tools you will need to complete this course."
msgstr "Įrankiai, kurių prireiks norint baigti šį kursą."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Total"
msgstr "Suma"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_slides
msgid "Total Slides"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Total Views"
msgstr ""

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__training
msgid "Training"
msgstr "Mokymai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Training Layout"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_0
msgid "Tree Infographic"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_2
msgid "Tree planting in hanging bottles on wall"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_0
msgid "Trees"
msgstr ""

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_2_gard2
msgid "Trees, Wood and Gardens"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "Tipas"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Įrašytos išimties veiklos tipas."

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "Nepavyko paskelbti žinutės, nustatykite siuntėjo pašto adresą."

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#, python-format
msgid "Uncategorized"
msgstr "Nekategorizuotas"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_0
msgid "Unforgettable Tools"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Unknown document"
msgstr "Nežinomas dokumentas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Unknown error"
msgstr "Nežinoma klaida"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Unknown error, try again."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Unpublished"
msgstr "Nepaskelbtas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread
msgid "Unread Messages"
msgstr "Neperskaitytos žinutės"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Neperskaitytų žinučių skaičiavimas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Update"
msgstr "Atnaujinti"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr "Įkelti grupes"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "Upload Presentation"
msgstr "Įkelti prezentaciją"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Upload a document"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_id
msgid "Uploaded by"
msgstr "Įkėlė"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Uploading document ..."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__tag_ids
msgid "Used to categorize and filter displayed channels/courses"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__color
msgid "Used to decorate kanban view"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_vote
msgid "User vote"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_users
msgid "Users"
msgstr "Vartotojai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__video
#, python-format
msgid "Video"
msgstr "Video"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_video
msgid "Videos"
msgstr "Video"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "View"
msgstr "Peržiūrėti"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "View all"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "View course"
msgstr "Rodyti kursą"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Views"
msgstr "Peržiūros"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_card
msgid "Views •"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_visibility
msgid "Visibility"
msgstr "Matomumas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr "Matomas dabartinėje svetainėje"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Visits"
msgstr "Apsilankymai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_vote
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__vote
msgid "Vote"
msgstr "Balsuoti"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_votes
msgid "Votes"
msgstr "Balsai"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Votes and comments are disabled for this course"
msgstr "Šio kurso komentuoti ar už jį balsuoti neleidžiama"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "Laukia patvirtinimo"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_1
msgid "Watching the master(s) at work"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_4
msgid ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say !"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__webpage
#, python-format
msgid "Web Page"
msgstr "Svetainės puslapis"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_webpage
msgid "Webpages"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
msgid "Website"
msgstr "Svetainė"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__gamification_challenge__challenge_category__slides
msgid "Website / Slides"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr "Interneto svetainės žinutės"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr "Svetainės nuoroda"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr "Svetainės komunikacijos istorija"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr "Svetainės meta aprašymas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr "Svetainės meta raktažodžiai"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr "Svetainės meta pavadinimas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr "Svetainės Opengraph paveikslėlis"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Welcome on your course's home page. It's still empty for now. Click on "
"\"<b>New</b>\" to write your first course."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "What does"
msgstr ""

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_0
msgid "What is a strawberry ?"
msgstr ""

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_1
msgid "What is the best tool to dig a hole for your plants ?"
msgstr ""

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_2
msgid "What was the question again ?"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Kai santykio laukas yra  pasirinktas kaip pirmasis laukas, šis laukas "
"leidžia jums pasirinkti tikslinį lauką tikslo dokumento modelyje ar "
"submodelyje."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Kai sąryšio laukas yra pasirinktas kaip pirmas laukas, šis laukas nurodo to "
"sąryšio dokumento modelį."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_1
msgid ""
"Which wood type is best for my solid wood furniture? That's the question we "
"help you answer in this video !"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"With Quizzes you can keep your students focused and motivated by answering "
"some questions and gaining some karma points"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_1
msgid "Wood"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_1
msgid "Wood Bending With Steam Box"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_3
msgid "Wood Characteristics"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_1
msgid "Wood Types"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_3_0
msgid "Working with Wood"
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product or services. <br>To be "
"successful your content needs to be useful to your readers."
msgstr ""

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br> To be successful your content needs to be useful to your "
"readers."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "XP"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_0
#, python-format
msgid "Yes"
msgstr "Taip"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid ""
"You are not allowed to add members to this course. Please contact the course"
" responsible or an administrator."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"You can add <b>comments</b> on answers. This will be visible with the "
"results if the user select this answer."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "You can not upload password protected file."
msgstr "Negalite įkelti failo, kuris yra apsaugotas slaptažodžiu."

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot add tags to this course."
msgstr "Negalite pridėti žymų prie šio kurso."

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as completed if you are not among its members."
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as viewed if you are not among its members."
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"You cannot mark a slide quiz as completed if you are not among its members."
msgstr ""

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot upload on this channel."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have access to this lesson"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have enough karma to vote"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "You gained"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "You have already joined this channel"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You have already voted for this lesson"
msgstr ""

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_invite
msgid "You have been invited to join {{ object.channel_id.name }}"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "You have to sign in before"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "You may now participate in our eLearning."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "You must be logged to submit the quiz."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You must be member of this course to vote"
msgstr "Norėdami balsuoti, turite būti šio kurso narys"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_2
msgid "You won't believe those facts about carrots."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "You're enrolled"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Your"
msgstr "Jūsų"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Your Google API key is invalid, please update it in your settings.\n"
"Settings > Website > Features > API Key"
msgstr ""

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_level
msgid "Your Level"
msgstr ""

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_role
msgid "Your Role"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Your first section is created, now it's time to add lessons to your course. "
"Click on <b>Add Content</b> to upload a document, create a web page or link "
"a video."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Link"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Video URL"
msgstr "Youtube video nuoroda"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_id
msgid "Youtube or Google Document ID"
msgstr "Youtube arba Google Document ID"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "Youtube or Google Document URL"
msgstr "Youtube arba Google Document nuoroda"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.achievement_card
msgid "achieved"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "breadcrumb"
msgstr "kelias"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "by email."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "e.g. Computer Science for kids"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. How to grow your business with Odoo?"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid ""
"e.g. In this video, we'll give you the keys on how Odoo can help you to grow"
" your business. At the end, we'll propose you a quiz to test your knowledge."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "e.g. Introduction"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "e.g. Your Level"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. https://www.odoo.com"
msgstr "pvz. https://www.odoo.com"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_root
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "eLearning"
msgstr "eMokymas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_ids
msgid "eLearning Courses"
msgstr "El.mokymai Kursai"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_overview
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "eLearning Overview"
msgstr "eMokymų apžvalga"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "login"
msgstr "prisijungimas"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"means? The YouTube \"unlisted\" means it is a video which can be viewed only"
" by the users with the link to it. Your video will never come up in the "
"search results nor on your channel."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "or Leave the course"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "reviews"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#, python-format
msgid "sign in"
msgstr "prisijungti"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid "steps"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "to contact responsible."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "to download resources"
msgstr "Parsisiųsti resursus"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "to enroll."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "to share this"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "unlisted"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "xp"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "{{ user.name }} shared a {{ object.slide_type }} with you!"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "└<span class=\"ml-1\">Uncategorized</span>"
msgstr ""
