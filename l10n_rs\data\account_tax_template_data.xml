<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="rs_sale_vat_20" model="account.tax.template">
        <field name="sequence">10</field>
        <field name="description">VAT 20%</field>
        <field name="name">20%</field>
        <field name="price_include" eval="0"/>
        <field name="amount">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_003')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_470'),
                'plus_report_line_ids': [ref('tax_report_line_103')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_470'),
                'minus_report_line_ids': [ref('tax_report_line_103')],
            }),
        ]"/>
    </record>

    <record id="rs_sale_vat_10" model="account.tax.template">
        <field name="sequence">20</field>
        <field name="description">VAT 10%</field>
        <field name="name">10%</field>
        <field name="price_include" eval="0"/>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_004')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_471'),
                'plus_report_line_ids': [ref('tax_report_line_104')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_471'),
                'minus_report_line_ids': [ref('tax_report_line_104')],
            }),
        ]"/>
    </record>

    <record id="rs_sale_vat_0" model="account.tax.template">
        <field name="sequence">30</field>
        <field name="description">VAT 0%</field>
        <field name="name">0%</field>
        <field name="price_include" eval="0"/>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_471'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_471'),
            }),
        ]"/>
    </record>

    <record id="rs_sale_vat_0_deduct_previous_tax" model="account.tax.template">
        <field name="sequence">40</field>
        <field name="description">VAT 0% with the right to deduct previous tax</field>
        <field name="name">0% - prev. tax deductible</field>
        <field name="price_include" eval="0"/>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_001')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rs_sale_vat_0_no_deduct_previous_tax" model="account.tax.template">
        <field name="sequence">50</field>
        <field name="description">VAT 0% without the right to deduct previous tax</field>
        <field name="name">0% - prev. tax not deductible</field>
        <field name="price_include" eval="0"/>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_002')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rs_purchase_vat_20" model="account.tax.template">
        <field name="sequence">60</field>
        <field name="description">VAT 20%</field>
        <field name="name">20%</field>
        <field name="price_include" eval="0"/>
        <field name="amount">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_008')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_270'),
                'plus_report_line_ids': [ref('tax_report_line_108')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_270'),
                'minus_report_line_ids': [ref('tax_report_line_108')],
            }),
        ]"/>
    </record>

    <record id="rs_purchase_vat_10" model="account.tax.template">
        <field name="sequence">70</field>
        <field name="description">VAT 10%</field>
        <field name="name">10%</field>
        <field name="price_include" eval="0"/>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_008')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_271'),
                'plus_report_line_ids': [ref('tax_report_line_108')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_271'),
                'minus_report_line_ids': [ref('tax_report_line_108')],
            }),
        ]"/>
    </record>

    <record id="rs_purchase_vat_0" model="account.tax.template">
        <field name="sequence">80</field>
        <field name="description">VAT 0%</field>
        <field name="name">0%</field>
        <field name="price_include" eval="0"/>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rs_purchase_import_vat_0" model="account.tax.template">
        <field name="sequence">90</field>
        <field name="description">VAT 0% - import VAT exempt</field>
        <field name="name">0% - import</field>
        <field name="price_include" eval="0"/>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_006')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="rs_purchase_import_vat_20" model="account.tax.template">
        <field name="sequence">100</field>
        <field name="description">VAT 20% - import general rate</field>
        <field name="name">20% - import</field>
        <field name="price_include" eval="0"/>
        <field name="amount">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_006')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_274'),
                'plus_report_line_ids': [ref('tax_report_line_106')],

            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_274'),
                'minus_report_line_ids': [ref('tax_report_line_106')],
            }),
        ]"/>
    </record>

    <record id="rs_purchase_import_vat_10" model="account.tax.template">
        <field name="sequence">110</field>
        <field name="description">VAT 10% - import specific rate</field>
        <field name="name">10% - import</field>
        <field name="price_include" eval="0"/>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_006')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_275'),
                'plus_report_line_ids': [ref('tax_report_line_106')],

            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_275'),
                'minus_report_line_ids': [ref('tax_report_line_106')],
            }),
        ]"/>
    </record>

    <record id="rs_purchase_farmer_deductible_vat_8" model="account.tax.template">
        <field name="sequence">120</field>
        <field name="description">VAT 8% - deductible farmers compensation</field>
        <field name="name">8% - farmers deductible</field>
        <field name="price_include" eval="0"/>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_compensation"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_007')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_278'),
                'plus_report_line_ids': [ref('tax_report_line_107')],

            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('rs_278'),
                'minus_report_line_ids': [ref('tax_report_line_107')],
            }),
        ]"/>
    </record>

    <record id="rs_purchase_farmer_non_deductible_vat_8" model="account.tax.template">
        <field name="sequence">130</field>
        <field name="description">VAT 8% - non-deductible farmers compensation</field>
        <field name="name">8% - farmers non-deductible</field>
        <field name="price_include" eval="0"/>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="True"/>
        <field name="chart_template_id" ref="l10n_rs_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_compensation"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_007')],
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0, 0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

</odoo>
