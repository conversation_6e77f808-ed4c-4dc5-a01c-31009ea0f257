# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-27 15:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Connect Stripe"
msgstr "Підключіть Stripe"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Не вдалося встановити з’єднання з API."

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Generate your webhook"
msgstr "Створіть ваш webhook"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Get your Secret and Publishable keys"
msgstr "Отримайте ваш секретний та публічний ключ"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid ""
"If a webhook is enabled on your Stripe account, this signing secret must be "
"set to authenticate the messages sent from Stripe to Odoo."
msgstr ""
"Якщо вебхук увімкнено у вашому обліковому записі Stripe, цей секрет підпису "
"має бути встановлений для автентифікації повідомлень, надісланих від Stripe "
"до Odoo."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Не знайдено жодної транзакції, що відповідає референсу %s."

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Платіжний еквайєр"

#. module: payment_stripe
#: model:ir.actions.act_window,name:payment_stripe.action_payment_acquirer_onboarding
msgid "Payment Acquirers"
msgstr "Платіжні еквайєри"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_account_payment_method
msgid "Payment Methods"
msgstr "Способи оплати"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "Токен оплати"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "Платіжна операція"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__provider
msgid "Provider"
msgstr "Провайдер"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "Publishable Key"
msgstr "Публічний ключ"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid intent status: %s"
msgstr "Отримані дані з недійсним статусом наміру: %s"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing intent status."
msgstr "Отримані дані із відсутнім статусом наміру."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr "Отримані дані з відсутнім посиланням на мерчант"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_secret_key
msgid "Secret Key"
msgstr "Секретний ключ"

#. module: payment_stripe
#: model:account.payment.method,name:payment_stripe.payment_method_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_acquirer__provider__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent
msgid "Stripe Payment Intent ID"
msgstr "ID призначення платежу Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Stripe Payment Method ID"
msgstr "ID способу оплати Stripe"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy error: %(error)s"
msgstr "Помилка Stripe Proxy: %(error)s"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: An error occurred when communicating with the proxy."
msgstr "Проксі Stripe: Виникла помилка під час з'єднання з проксі."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: Could not establish the connection."
msgstr "Проксі Stripe: Не вдалося встановити з’єднання."

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "Постачальник платіжних послуг для використання з цим еквайєром"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Stripe gave us the following info about the problem:\n"
"'%s'"
msgstr ""
"З'єднання з API не вдалося.\n"
"Stripe надав нам наступні інформацію про проблему:\n"
"'%s'"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "The key solely used to identify the account with Stripe"
msgstr ""
"Ключ, який використовується виключно для ідентифікації облікового запису за "
"допомогою Stripe"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "Транзакція не зв'язана з токеном."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_token.py:0
#, python-format
msgid "Unable to convert payment token to new API."
msgstr "Неможливо конвертувати платіжний токен на новий API."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid "Webhook Signing Secret"
msgstr "Секретний ключ підписання вебхуку"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "You Stripe Webhook was successfully set up!"
msgstr "Ваш вебхук Stripe було успішно встановлено!"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot create a Stripe Webhook if your Stripe Secret Key is not set."
msgstr ""
"Ви не можете створити вебхук Stripe якщо ваш секретний клю Stripe не "
"встановлено."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer state to Enabled until your onboarding to Stripe"
" is completed."
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer to Test Mode while it is linked with your Stripe"
" account."
msgstr ""
"Ви не можете встановити еквайєр у тестовий режим у той час, коли він "
"пов'язаний з обліковим записом Stripe."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Your Stripe Webhook is already set up."
msgstr "Ваш вебхук Stripe вже встановлено."
