# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_picking_batch
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-08-24 09:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Lot/Serial Number</strong>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Package</strong>"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Responsible:</strong>"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "Action Needed"
msgstr "Þarfnast aðgerðar"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add pickings to batch"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_to_batch_action
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_to_batch_action_stock_picking
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add to Batch"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Barcode"
msgstr "Strikamerki"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:64
#: model:ir.actions.report,name:stock_picking_batch.action_report_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__batch_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
#, python-format
msgid "Batch Picking"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_to_batch
msgid "Batch Picking Lines"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__name
msgid "Batch Picking Name"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_batch_action
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_batch_menu
msgid "Batch Pickings"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Batch Pickings not finished"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking__batch_id
msgid "Batch associated to this picking"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Cancel"
msgstr "Hætta við"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Cancel picking"
msgstr ""

#. module: stock_picking_batch
#: selection:stock.picking.batch,state:0
msgid "Cancelled"
msgstr "Afpöntuð"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Commitment Date"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Confirm"
msgstr "Staðfesta"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Confirm picking"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid "Create a new batch picking"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Destination"
msgstr "Destination"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: selection:stock.picking.batch,state:0
msgid "Done"
msgstr "Lokið"

#. module: stock_picking_batch
#: selection:stock.picking.batch,state:0
msgid "Draft"
msgstr "Tillaga"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_follower_ids
msgid "Followers"
msgstr "Fylgjendur"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_channel_ids
msgid "Followers (Channels)"
msgstr "Followers (Channels)"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_partner_ids
msgid "Followers (Partners)"
msgstr "Fylgjendur (viðskiptafélagar)"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Group By"
msgstr "Hópa eftir"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__id
msgid "ID"
msgstr "Auðkenni"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_unread
msgid "If checked new messages require your attention."
msgstr "Ef merkt við þá eru ný skilaboð"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "If checked, new messages require your attention."
msgstr "If checked, new messages require your attention."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_immediate_transfer
msgid "Immediate Transfer"
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:92
#, python-format
msgid "Immediate Transfer?"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_is_follower
msgid "Is Follower"
msgstr "Is Follower"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch____last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__picking_ids
msgid "List of picking associated to this batch"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_ids
msgid "Messages"
msgstr "Skilaboð"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__name
msgid "Name of the batch picking"
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:52
#, python-format
msgid "Nothing to print."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of Actions"
msgstr "Fjöldi aðgerða"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Fjöldi skilaboð sem bíða afgreiðslu"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_unread_counter
msgid "Number of unread messages"
msgstr "Number of unread messages"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__user_id
msgid "Person responsible for this batch picking"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_immediate_transfer__pick_to_backorder_ids
msgid "Pick To Backorder"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Picking"
msgstr "Tiltekt"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Picking Reference"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_immediate_transfer__pick_to_backorder_ids
msgid "Picking to backorder"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_ids
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Pickings"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print"
msgstr "Prenta"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Product"
msgstr "Vara"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Quantity"
msgstr "Magn"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__user_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Responsible"
msgstr "Ábyrgðaraðili"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
#: selection:stock.picking.batch,state:0
msgid "Running"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Scheduled Date"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Search Batch Picking"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Select a batch"
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:59
#, python-format
msgid ""
"Some pickings are still waiting for goods. Please check or force their "
"availability before setting this batch to done."
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:77
#, python-format
msgid "Some products require lots/serial numbers."
msgstr ""

#. module: stock_picking_batch
#: model:mail.message.subtype,description:stock_picking_batch.mt_batch_state
#: model:mail.message.subtype,name:stock_picking_batch.mt_batch_state
msgid "Stage Changed"
msgstr "Stage Changed"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__state
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "State"
msgstr "Fylki"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Status"
msgstr "Staða"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_tree
msgid "Stock Batch Picking"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Summary:"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid ""
"The goal of the batch picking is to group operations that may\n"
"            (needs to) be done together in order to increase their efficiency.\n"
"            It may also be useful to assign jobs (one person = one batch) or\n"
"            help the timing management of operations (tasks to be done at 1pm)."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "To take from:"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:63
#, python-format
msgid "Transferred by"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_unread
msgid "Unread Messages"
msgstr "Ólesin skilaboð"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Teljari fyrir ólesin skilaboð"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website Messages"
msgstr "Skilaboð frá vef"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website communication history"
msgstr "Website communication history"
