<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="hr_expense.food_expense_product" model="product.product">
            <field name="expense_policy">sales_price</field>
        </record>

        <record id="hr_expense.mileage_expense_product" model="product.product">
            <field name="invoice_policy">delivery</field>
            <field name="expense_policy">sales_price</field>
        </record>

        <record id="hr_expense.accomodation_expense_product" model="product.product">
            <field name="invoice_policy">delivery</field>
            <field name="expense_policy">cost</field>
        </record>

        <record id="hr_expense.allowance_expense_product" model="product.product">
            <field name="expense_policy">cost</field>
        </record>

    </data>
</odoo>
