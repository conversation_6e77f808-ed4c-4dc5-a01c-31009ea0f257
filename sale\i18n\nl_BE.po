# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale
#
# Translators:
# <PERSON> <eric<PERSON><PERSON>@yahoo.com>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-03-10 09:16+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Dutch (Belgium) (http://www.transifex.com/odoo/odoo-9/"
"language/nl_BE/)\n"
"Language: nl_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"\n"
"% set access_action = object.get_access_action()\n"
"% set doc_name = 'quotation' if object.state in ('draft', 'sent') else "
"'order confirmation'\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions."
"act_url'\n"
"% set access_name = is_online and object.template_id and 'Accept and pay %s "
"online' % doc_name or 'View %s' % doc_name\n"
"% set access_url = is_online and access_action['url'] or object."
"get_signup_url()\n"
"\n"
"<p>Dear\n"
"% if object.partner_id.is_company and object.child_ids:\n"
"    ${object.partner_id.child_ids[0].name}\n"
"% else :\n"
"    ${object.partner_id.name}\n"
"% endif\n"
",</p>\n"
"<p>Thank you for your inquiry.<br />\n"
"Here is your ${doc_name} <strong>${object.name}</strong>\n"
"% if object.origin:\n"
"(with reference: ${object.origin} )\n"
"% endif\n"
"amounting <strong>${object.amount_total} ${object.pricelist_id.currency_id."
"name}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"<p style=\"margin-left: 30px; margin-top: 10 px; margin-bottom: 10px;\">\n"
"    <a href=\"${access_url}\" style=\"padding: 5px 10px; font-size: 12px; "
"line-height: 18px; color: #FFFFFF; border-color:#a24689; text-decoration: "
"none; display: inline-block; margin-bottom: 0px; font-weight: 400; text-"
"align: center; vertical-align: middle; cursor: pointer; white-space: nowrap; "
"background-image: none; background-color: #a24689; border: 1px solid "
"#a24689; border-radius:3px\" class=\"o_default_snippet_text\">${access_name}"
"</a>\n"
"</p>\n"
"<p>If you have any question, do not hesitate to contact us.</p>\n"
"<p>Best regards,</p>\n"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_sales_count
#: model:ir.model.fields,field_description:sale.field_product_template_sales_count
msgid "# Sales"
msgstr "# Verkopen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_count
msgid "# of Invoices"
msgstr "# Facturen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_nbr
msgid "# of Lines"
msgstr "# Regels"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_count
msgid "# of Orders"
msgstr "# Orders"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_product_uom_qty
msgid "# of Qty"
msgstr "# Aantal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_order_count
msgid "# of Sales Order"
msgstr "# Verkooporders"

#. module: sale
#: model:mail.template,report_name:sale.email_template_edi_sale
msgid ""
"${(object.name or '').replace('/','_')}_${object.state == 'draft' and "
"'draft' or ''}"
msgstr ""
"${(object.name or '').replace('/','_')}_${object.state == 'draft' and "
"'draft' or ''}"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
#, fuzzy
msgid ""
"${object.company_id.name} ${object.state in ('draft', 'sent') and "
"'Quotation' or 'Order'} (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name|safe} ${object.state in ('draft', 'sent') and "
"'Offerte' or 'Verkooporder'} (Referentie ${object.name or 'n/a' })"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "(update)"
msgstr "(bijwerken)"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"<i>Example: pre-paid service offers for which the customer have\n"
"                to buy an extra pack of hours, because he used all his "
"support\n"
"                hours.</i>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Opmerking voor fiscale positie:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Payment Term:</strong>"
msgstr "<strong>Betalingstermijn:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Verkoper:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Verzendadres:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Total Without Taxes</strong>"
msgstr "<strong>Totaal zonder BTW</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Total</strong>"
msgstr "<strong>Totaal</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr "<strong>Uw referentie:</strong>"

#. module: sale
#: selection:sale.config.settings,sale_pricelist_setting:0
msgid "A single sale price per product"
msgstr "Eén enkele verkoopprijs per product"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_deposit_account_id
msgid "Account used for deposits"
msgstr "Rekening gebruikt voor aanbetalingen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_sale_delivery_address
msgid "Addresses"
msgstr "Adressen"

#. module: sale
#: model:res.groups,name:sale.group_delivery_invoice_address
msgid "Addresses in Sales Orders"
msgstr "Adressen in verkooporders"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:139
#, python-format
msgid "Advance: %s"
msgstr "Vooruitbetaling: %s"

#. module: sale
#: selection:sale.config.settings,sale_pricelist_setting:0
msgid "Advanced pricing based on formula"
msgstr "Geavanceerde prijsstelling gebaseerd op formule"

#. module: sale
#: selection:sale.config.settings,group_discount_per_so_line:0
msgid "Allow discounts on sales order lines"
msgstr "Sta kortingen toe op verkooporderregels"

#. module: sale
#: selection:sale.config.settings,auto_done_setting:0
msgid ""
"Allow to edit sales order from the 'Sales Order' menu (not from the "
"Quotation menu)"
msgstr ""
"Sta het wijzigen van verkooporders toe vanuit het 'Verkooporder' menu (niet "
"van het Offerte menu)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                    Example: 10% for retailers, promotion of 5 EUR on this "
"product, etc."
msgstr ""
"Geeft u de mogelijkheid om verschillende prijzen te beheren op basis van "
"regels per klantcategorie.\n"
"                Bijvoorbeeld: 10% voor kleinhandel, korting van 5 EUR op dit "
"product, enz."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_uom
msgid ""
"Allows you to select and maintain different units of measure for products."
msgstr ""
"Hiermee kunt u verschillende maateenheden voor producten selecteren en "
"onderhouden."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_sales_to_invoice_amount
msgid "Amount of sales to invoice"
msgstr "Aantal verkopen te factureren"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_project_id
#: model:ir.model.fields,field_description:sale.field_sale_report_analytic_account_id
msgid "Analytic Account"
msgstr "Kostenplaatsen"

#. module: sale
#: model:res.groups,name:sale.group_analytic_accounting
msgid "Analytic Accounting for Sales"
msgstr "Kostenplaatsen voor verkopen"

#. module: sale
#: model:ir.filters,name:sale.filter_isale_report_product
msgid "By Product"
msgstr "Per product"

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_salespersons
msgid "By Salespersons"
msgstr "Per Verkoper"

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_salesteam
msgid "By Salesteam"
msgstr "Per verkoopteam"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_delivered_updateable
msgid "Can Edit Delivered"
msgstr "Kan leveringen wijzigen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Annuleren"

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_use_invoices
msgid "Check this box to manage invoices in this sales team."
msgstr "Vink dit aan om de facturen in dit verkoopteam te beheren."

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_use_quotations
msgid "Check this box to manage quotations in this sales team."
msgstr "Klik dit aan om in dit verkoopteam offertes te beheren."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Click to define a team target"
msgstr "Klik om een nieuw doel voor verkoopteam te bepalen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_commercial_partner_id
msgid "Commercial Entity"
msgstr "Commerciële entiteit"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_company_id
#: model:ir.model.fields,field_description:sale.field_sale_report_company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Bedrijf"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm Sale"
msgstr "Bevestig verkoop"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Create Invoice"
msgstr "Maak factuur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Invoices"
msgstr "Maak facturen"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid "Create a Quotation, the first step of a new sale."
msgstr "Maak een offerte aan, de eerste stap in een nieuwe verkoop."

#. module: sale
#: selection:product.template,track_service:0
msgid "Create a task and track hours"
msgstr "Maak een taak aan en volg de uren ervan op"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create and View Invoices"
msgstr "Maak en bekijk facturen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line_create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line_create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_create_date
msgid "Creation Date"
msgstr "Aanmaakdatum"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Klant"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_client_order_ref
msgid "Customer Reference"
msgstr "Referentie klant"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_deposit_taxes_id
msgid "Customer Taxes"
msgstr "Verkoop belastingen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Customer portal"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_date
msgid "Date Order"
msgstr "Orderdatum"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Date Ordered:"
msgstr "Besteldatum:"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_create_date
msgid "Date on which sales order is created."
msgstr "Datum waarop de verkooporder is gemaakt."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_default_invoice_policy
msgid "Default Invoicing"
msgstr "Standaard facturatie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company_sale_note
msgid "Default Terms and Conditions"
msgstr "Standaard Algemene Voorwaarden"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_deposit_product_id_setting
msgid "Default product used for payment advances"
msgstr "Standaardproduct dat gebruikt wordt bij vooruitbetalingen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_company_inherit_form2
msgid "Default terms & conditions..."
msgstr "Standaard Algemene Voorwaarden..."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_delivered
msgid "Delivered"
msgstr "Geleverd"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered Quantity"
msgstr "Geleverde hoeveelheid"

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Delivered quantities"
msgstr "Geleverde hoeveelheden"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_shipping_id
msgid "Delivery Address"
msgstr "Afleveradres"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_customer_lead
msgid "Delivery Lead Time"
msgstr "Leveringstermijn"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_partner_shipping_id
msgid "Delivery address for current sales order."
msgstr "Afleveradres voor de huidige verkooporder"

#. module: sale
#: model:product.product,name:sale.advance_product_0
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Aanbetaling"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_deposit_product_id_setting
msgid "Deposit Product"
msgstr "Product aanbetaling"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Description"
msgstr "Omschrijving"

#. module: sale
#: selection:sale.config.settings,sale_pricelist_setting:0
msgid "Different prices per customer segment"
msgstr "Verschillende prijzen per klantsegment"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#, fuzzy
msgid "Disc.(%)"
msgstr "Krt. (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_discount_per_so_line
msgid "Discount"
msgstr "Korting"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_discount
msgid "Discount (%)"
msgstr "Krt. (%)"

#. module: sale
#: model:res.groups,name:sale.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Korting op regels"

#. module: sale
#: selection:sale.config.settings,group_sale_delivery_address:0
msgid ""
"Display 3 fields on sales orders: customer, invoice address, delivery address"
msgstr "Toon 3 velden op verkooporders: klant, factuuradres en afleveradres"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line_display_name
#: model:ir.model.fields,field_description:sale.field_sale_report_display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: sale
#: model:res.groups,name:sale.group_display_incoterm
msgid "Display incoterms on Sales Order and related invoices"
msgstr ""
"Toon de leveringscondities op het verkooporder en de bijhorende facturen"

#. module: sale
#: selection:sale.config.settings,module_sale_margin:0
msgid "Display margins on quotations and sales orders"
msgstr "Toon de marges voor offertes en verkooporders"

#. module: sale
#: selection:sale.config.settings,module_sale_layout:0
#, fuzzy
msgid "Do not personalize sale orders and invoice reports"
msgstr "Toon leveringscondities op verkooporders en facturen"

#. module: sale
#: selection:sale.order,state:0
msgid "Done"
msgstr "Voltooid"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:83
#, python-format
msgid "Down Payment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_amount
msgid "Down Payment Amount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_product_id
msgid "Down Payment Product"
msgstr ""

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (fixed amount)"
msgstr ""

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (percentage)"
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:80
#, python-format
msgid "Down payment of %s%%"
msgstr ""

#. module: sale
#: selection:sale.report,state:0
msgid "Draft Quotation"
msgstr "Concept offerte"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "E-mail samenstellen wizard"

#. module: sale
#: model:ir.model,name:sale.model_survey_mail_compose_message
#, fuzzy
msgid "Email composition wizard for Survey"
msgstr "E-mail samenstellen wizard"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_website_portal
msgid "Enable customer portal to track orders, delivery and invoices"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_validity_date
msgid "Expiration Date"
msgstr "Vervaldatum"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Uitgebreide filters"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_fiscal_position_id
msgid "Fiscal Position"
msgstr "Fiscale positie"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_sale_pricelist_setting
msgid ""
"Fix Price: all price manage from products sale price.\n"
"Different prices per Customer: you can assign price on buying of minimum "
"quantity in products sale tab.\n"
"Advanced pricing based on formula: You can have all the rights on pricelist"
msgstr ""
"Vaste prijs: alle prijzen worden beheerd vanuit de verkoopprijs van het "
"product.\n"
"Verschillende prijzen per klant: prijzen bepaald door de aankoop van "
"minimale hoeveelheden per  product.\n"
"Geavanceerde prijszetting via formules: u hebt alle rechten bij het bepalen "
"van de prijszetting."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Deze rapportage geeft een overzicht van de uitstaande bedragen gefactureerd "
"aan uw klanten. De zoekopties geven de mogelijkheid om de analyses aan te "
"passen."

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Fully Invoiced"
msgstr "Volledig gefactureerd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_weight
msgid "Gross Weight"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Groeperen op"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_id
#: model:ir.model.fields,field_description:sale.field_sale_order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_sale_report_id
msgid "ID"
msgstr "ID"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If a sale order is done, you cannot modify it manually anymore. However, you "
"will still be able to invoice or deliver. This is used to freeze the sale "
"order."
msgstr ""
"Als een verkooporder gereed is, kan u het niet meer handmatig wijzigen. U "
"kunt echter nog steeds factureren of afleveren. Dit wordt gebruikt om het "
"verkooporder te bevriezen."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_deposit_account_id
msgid "Income Account"
msgstr "Omzetrekening"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_display_incoterm
msgid "Incoterms"
msgstr "Leveringscondities"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice
msgid "Invoice"
msgstr "Factuur"

#. module: sale
#: code:addons/sale/sale.py:840
#, python-format
msgid "Invoice %s paid"
msgstr "Factuur %s betaald"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_invoice_id
msgid "Invoice Address"
msgstr "Factuuradres"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Factuur bevestigd"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Factuur aangemaakt"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_line
msgid "Invoice Line"
msgstr "Factuurregel"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_invoice_lines
msgid "Invoice Lines"
msgstr "Factuurregels"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Invoice Order"
msgstr "Factureer order"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Factureer verkooporders"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line_invoice_status
msgid "Invoice Status"
msgstr "Factuurstatus"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_invoiced_target
msgid "Invoice Target"
msgstr "Facturatie doelstelling"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_partner_invoice_id
msgid "Invoice address for current sales order."
msgstr "Factuuradres van actuele verkooporder."

#. module: sale
#: selection:sale.config.settings,default_invoice_policy:0
msgid "Invoice based on costs (time and material, expenses)"
msgstr "Factuur gebaseerd op kost (tijd en materiaal, declaraties)"

#. module: sale
#: selection:sale.config.settings,default_invoice_policy:0
msgid "Invoice delivered quantities"
msgstr "Geleverde hoeveelheden factureren"

#. module: sale
#: selection:sale.config.settings,default_invoice_policy:0
msgid "Invoice ordered quantities"
msgstr "Bestelde hoeveelheden factureren"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales team has "
"invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"Factuuromzet voor deze maand. Dit is het bedrag dat het verkoopteam deze "
"maand heeft gefactureerd. Het wordt in de kanban weergave gebruikt om de "
"voortgangsratio te berekenen van de huidige omzet tegenover de doelstelling."

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines"
msgstr "Te factureren lijnen"

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines (deduct down payments)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_invoiced
msgid "Invoiced"
msgstr "Gefactureerd"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced Quantity"
msgstr "Hoeveelheid gefactureerd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_invoiced
msgid "Invoiced This Month"
msgstr "Deze maand gefactureerd"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_crm_team_use_invoices
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Facturen"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Factuur analyse"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Factuur analyses"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"Invoices will be created in draft so that you can update\n"
"                        them before validation."
msgstr ""
"Facturen worden aangemaakt als concept, zodat u ze nog kan\n"
"bijwerken voor de definitieve validatie. "

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Facturatie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template_invoice_policy
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_invoice_policy
msgid "Invoicing Policy"
msgstr "Facturatiebeleid"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing address:"
msgstr "Factuuradres:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing and shipping address:"
msgstr "Factuur- en verzendadres:"

#. module: sale
#: selection:sale.config.settings,group_sale_delivery_address:0
msgid ""
"Invoicing and shipping addresses are always the same (Example: services "
"companies)"
msgstr ""
"Facturatie- en afleveradressen zijn steeds hetzelfde (Bijv. voor een "
"dienstenbedrijf)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "Invoicing/Progression Ratio"
msgstr "Voortgang factuurratio"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv___last_update
#: model:ir.model.fields,field_description:sale.field_sale_order___last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_line___last_update
#: model:ir.model.fields,field_description:sale.field_sale_report___last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line_write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line_write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_sale_contract
msgid "Manage subscriptions and recurring invoicing"
msgstr ""

#. module: sale
#: selection:product.template,track_service:0
msgid "Manually set quantities on order"
msgstr "Bepaal handmatig de hoeveelheden voor het order"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product_track_service
#: model:ir.model.fields,help:sale.field_product_template_track_service
msgid ""
"Manually set quantities on order: Invoice based on the manually entered "
"quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related "
"timesheet.\n"
"Create a task and track hours: Create a task on the sale order validation "
"and track the work hours."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_validity_date
msgid ""
"Manually set the expiration date of your quotation (offer), or it will set "
"the date automatically based on the template if online quotation is "
"installed."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_sale_margin
msgid "Margins"
msgstr "Marges"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Mijn orders"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Mijn verkooporderregels"

#. module: sale
#: selection:sale.config.settings,auto_done_setting:0
msgid "Never allow to modify a confirmed sale order"
msgstr "Sta nooit het wijzigen van een bevestigde verkooporder toe"

#. module: sale
#: code:addons/sale/sale.py:101
#, python-format
msgid "New"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "Nieuwe offerte"

#. module: sale
#: selection:sale.config.settings,group_discount_per_so_line:0
msgid "No discount on sales order lines, global discount only"
msgstr "Geen korting op verkooporderlijnen, enkel algemene korting "

#. module: sale
#: selection:sale.config.settings,group_display_incoterm:0
msgid "No incoterm on reports"
msgstr "Geen leveringscondities op rapport"

#. module: sale
#: selection:sale.config.settings,group_product_variant:0
msgid "No variants on products"
msgstr "Geen productvarianten"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Note that once a Quotation becomes a Sale Order, it will be moved \n"
"                from the Quotations list to the Sales Order list."
msgstr ""
"Merk op dat eenmaal een offerte verandert in een verkooporder, het niet\n"
"meer in de offertelijst voorkomt, maar zichtbaar wordt in de "
"verkooporderlijst."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
msgid ""
"Note that once a Quotation becomes a Sale Order, it will be moved from the "
"Quotations list to the Sales Order list."
msgstr ""
"Merk op dat eenmaal een offerte verandert in een verkooporder, het niet meer "
"in de offertelijst voorkomt, maar zichtbaar wordt in de verkooporderlijst."

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Nothing to Invoice"
msgstr "Niets te factureren"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line_customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Aantal dagen tussen het bevestigen van de order en het werkelijk leveren van "
"de producten aan de klant."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.\n"
"                    You'll be able to invoice it and collect payments.\n"
"                    From the <i>Sales Orders</i> menu, you can track "
"delivery\n"
"                    orders or services."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_website_quote
msgid "Online Quotations"
msgstr "Online offertes"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:26
#, python-format
msgid "Only Integer Value should be valid."
msgstr "Alleen gehele getallen zijn geldig"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Order"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_date_order
msgid "Order Date"
msgstr "Orderdatum"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Orderregels"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Order Month"
msgstr "Bestel maand"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
msgid "Order Number"
msgstr "Order Nummer"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_name
msgid "Order Reference"
msgstr "Order referentie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_state
msgid "Order Status"
msgstr "Order status"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Ordered Qty"
msgstr "Bestelde hvh."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Ordered Quantity"
msgstr "Bestelde hoeveelheid"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product_invoice_policy
#: model:ir.model.fields,help:sale.field_product_template_invoice_policy
msgid ""
"Ordered Quantity: Invoice based on the quantity the customer ordered.\n"
"Delivered Quantity: Invoiced based on the quantity the vendor delivered.\n"
"Reinvoice Costs: Invoice with some additional charges (product transfer, "
"labour charges,...)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Ordered date of the sales order"
msgstr "Besteldatum van de verkooporders"

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Ordered quantities"
msgstr "Bestelde hoeveelheden"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Meerverkoop orders"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"Orders to upsell are orders having products with an invoicing\n"
"                policy based on <i>ordered quantities</i> for which you "
"have\n"
"                delivered more than what have been ordered."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Information"
msgstr "Overige informatie"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model:ir.model.fields,field_description:sale.field_sale_report_partner_id
msgid "Partner"
msgstr "Relatie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_country_id
msgid "Partner Country"
msgstr "Land relatie"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Partner's Country"
msgstr "Relatie zijn land"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_payment_term_id
msgid "Payment Term"
msgstr "Betalingsconditie"

#. module: sale
#: selection:sale.config.settings,module_sale_layout:0
msgid ""
"Personnalize the sale orders and invoice report with separators, page-breaks "
"or subtotals"
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:246
#, python-format
msgid "Please define an accounting sale journal for this company."
msgstr "U dient een verkoop dagboek aan te maken voor dit bedrijf."

#. module: sale
#: code:addons/sale/sale.py:703
#, python-format
msgid ""
"Please define income account for this product: \"%s\" (id:%d) - or for its "
"category: \"%s\"."
msgstr ""
"U dient een inkomstenrekening te bepalen voor product: \"%s\" (id:%d) - of "
"voor de categorie waartoe het behoort: \"%s\"."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Price"
msgstr "Bedrag"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_reduce
msgid "Price Reduce"
msgstr "Gereduceerde prijs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report_pricelist_id
msgid "Pricelist"
msgstr "Prijslijst"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_pricelist_id
msgid "Pricelist for current sales order."
msgstr "Prijslijst voor actuele verkooporder"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Print"
msgstr "Afdrukken"

#. module: sale
#: selection:sale.config.settings,module_website_quote:0
msgid "Print quotes or send by email"
msgstr "Offertes afdrukken of per email versturen"

#. module: sale
#: model:ir.model,name:sale.model_procurement_order
msgid "Procurement"
msgstr "Verwerving"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_procurement_group_id
msgid "Procurement Group"
msgstr "Verwervingsgroep"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_procurement_ids
msgid "Procurements"
msgstr "Verwervingen"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_product_id
#: model:ir.model.fields,field_description:sale.field_sale_report_product_id
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Product"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Productcategorie"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_sale_report_product_tmpl_id
msgid "Product Template"
msgstr "Productsjabloon"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_product_variant
msgid "Product Variants"
msgstr "Product varianten"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Products"
msgstr "Producten"

#. module: sale
#: selection:sale.config.settings,group_product_variant:0
msgid ""
"Products can have several attributes, defining variants (Example: size, "
"color,...)"
msgstr ""
"Producten kunnen meerdere attributen hebben die als varianten worden "
"gedefinieerd (Voorbeelden zijn: maat, kleur,...)"

#. module: sale
#: selection:sale.config.settings,group_uom:0
msgid "Products have only one unit of measure (easier)"
msgstr "Producten hebben slechts één maateenheid (eenvoudiger)"

#. module: sale
#: model:res.groups,name:sale.group_mrp_properties
msgid "Properties on lines"
msgstr "Eigenschappen op regels"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Hvhd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_delivered
msgid "Qty Delivered"
msgstr "Aantal geleverd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_invoiced
msgid "Qty Invoiced"
msgstr "Hvh gefactureerd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_to_invoice
msgid "Qty To Invoice"
msgstr "Hvh te factureren"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quantity"
msgstr "Hoeveelheid"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
#: selection:sale.order,state:0
msgid "Quotation"
msgstr "Offerte"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr ""

#. module: sale
#: model:ir.actions.report.xml,name:sale.report_sale_order
msgid "Quotation / Order"
msgstr "Offerte / Order"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation Date:"
msgstr "Offertedatum:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotation Number"
msgstr "Offerte nummer"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation Send"
msgstr "Offerte verstuurd"

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Quotation Sent"
msgstr "Offerte verstuurd"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Offerte bevestigd"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
msgid "Quotation sent"
msgstr "Offerte verzonden"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.model.fields,field_description:sale.field_crm_team_use_quotations
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Quotations"
msgstr "Offertes"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Quotations & Sales"
msgstr "Offertes & Verkopen"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Offerte analyse"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#, fuzzy
msgid "Quotations Sent"
msgstr "Offerte verstuurd"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Offertes en verkooporders"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_origin
msgid "Reference of the document that generated this sales order request."
msgstr "Referentie van het document dat deze verkooporder aanvraag genereerde."

#. module: sale
#: selection:product.template,invoice_policy:0
#, fuzzy
msgid "Reinvoice Costs"
msgstr "Factuurstatus"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Reporting"
msgstr "Rapportages"

#. module: sale
#: selection:sale.order,state:0
msgid "Sale Order"
msgstr "Verkooporder"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line_so_line
#: model:ir.model.fields,field_description:sale.field_procurement_order_sale_line_id
msgid "Sale Order Line"
msgstr "Verkooporderregel"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_product_sale_list
#: model:ir.model.fields,field_description:sale.field_account_invoice_line_sale_line_ids
msgid "Sale Order Lines"
msgstr "Verkooporderregels"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_auto_done_setting
msgid "Sale Order Modification"
msgstr "Verkooporder wijziging"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Sale Price"
msgstr "Verkoopprijs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_sale_layout
msgid "Sale Reports Layout"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_report_product_all
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_company_inherit_form2
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales"
msgstr "Verkopen"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Verkoop vooruibetaling factuur"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr "Verkoop Analyse"

#. module: sale
#: selection:sale.report,state:0
msgid "Sales Done"
msgstr "Gerealiseerde verkopen"

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_sales_funnel
msgid "Sales Funnel"
msgstr "Verkoop pijplijn"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Information"
msgstr "Verkoop informatie"

#. module: sale
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_order_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model:res.request.link,name:sale.req_link_sale_order
#: selection:sale.report,state:0
msgid "Sales Order"
msgstr "Verkoop orders"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Verkooporder bevestigd"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Verkooporderregels"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Verkooporderregels gereed om te factureren"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Verkooporderregels gerelateerd aan een verkooporder van mijzelf"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Sales Order that haven't yet been confirmed"
msgstr "Verkooporders welke nog niet zijn bevestigd"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "Verkooporders"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Orders Statistics"
msgstr "Verkooporder analyses"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_invoice_report_team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_team_id
#: model:ir.model.fields,field_description:sale.field_sale_order_team_id
#: model:ir.model.fields,field_description:sale.field_sale_report_team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Verkoopteam"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Sales to Invoice"
msgstr "Verkopen te factureren"

#. module: sale
#: selection:sale.config.settings,module_sale_margin:0
msgid "Salespeople do not need to view margins when quoting"
msgstr ""
"Verkopers hebben geen zicht op de marges bij het opmaken van een offerte"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_order_user_id
#: model:ir.model.fields,field_description:sale.field_sale_report_user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Verkoper"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Verkooporder zoeken"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_website_sale_digital
msgid ""
"Sell digital products - provide downloadable content on your customer portal"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Verzenden via e-mail"

#. module: sale
#: selection:sale.config.settings,module_website_quote:0
msgid "Send online quotations based on templates (advanced)"
msgstr "Stuur online offertes gebaseerd op voorbeelden (geavanceerd)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_sequence
msgid "Sequence"
msgstr "Reeks"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:19
#, python-format
msgid "Set an invoicing target: "
msgstr "Bepaal een facturatiedoel:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Done"
msgstr "Zet naar voltooid"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Zet naar offerte"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Setup default terms and conditions in your company settings."
msgstr "Stel de algemene verkoopvoorwaarden in bij uw bedrijfsinstellingen."

#. module: sale
#: selection:sale.config.settings,group_display_incoterm:0
msgid "Show incoterms on sale orders and invoices"
msgstr "Toon leveringscondities op verkooporders en facturen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_product_pricelist
msgid "Show pricelists On Products"
msgstr "Toon prijslijsten op producten"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_pricelist_item
msgid "Show pricelists to customers"
msgstr "Toon prijslijsten aan klanten"

#. module: sale
#: selection:sale.config.settings,group_uom:0
#, fuzzy
msgid ""
"Some products may be sold/purchased in different units of measure (advanced)"
msgstr ""
"Sommige producten kunnen verkocht/aangekocht worden met verschillende "
"maateenheden (geavanceerd)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_origin
msgid "Source Document"
msgstr "Bron document"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_state
#: model:ir.model.fields,field_description:sale.field_sale_report_state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Status"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Subscriptions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_subtotal
msgid "Subtotal"
msgstr "Subtotaal"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_invoiced_target
msgid ""
"Target of invoice revenue for the current month. This is the amount the "
"sales team estimates to be able to invoice this month."
msgstr ""
"Doelomzet uit facturatie voor de huidige maand. Dit is het bedrag dat het "
"verkoopteam deze maand denkt te kunnen factureren."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line_tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "Belastingen"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_deposit_taxes_id
msgid "Taxes used for deposits"
msgstr "Belastingen gebruikt voor aanbetalingen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_note
msgid "Terms and conditions"
msgstr "Voorwaarden"

#. module: sale
#: code:addons/sale/sale_analytic.py:56
#, python-format
msgid ""
"The Sale Order %s linked to the Analytic Account must be validated before "
"registering expenses."
msgstr ""
"De verkooporder \"%s\" gelinkt aan de analytische rekening moet gevalideerd "
"worden voordat u uitgaven registreert."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_amount
msgid "The amount to be invoiced in advance, taxes excluded."
msgstr "Het bedrag dat vooruit gefactureerd wordt, belastingen uitgezonderd. "

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_project_id
msgid "The analytic account related to a sales order."
msgstr "De kostenplaatsrekening verbonden met een verkooporder."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_display_incoterm
msgid ""
"The printed reports will display the incoterms for the sale orders and the "
"related invoices"
msgstr ""
"Het afgedrukte rapport toont de leveringscondities voor de verkooporders en "
"de bijhorende facturen"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:137
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:135
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policy set "
"to \"Ordered quantities\". Please update your deposit product to be able to "
"create a deposit invoice."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:77
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:73
#, python-format
msgid ""
"There is no income account defined for this product: \"%s\". You may have to "
"install a chart of account from Accounting app, settings menu."
msgstr ""
"Er bestaat geen inkomstenrekening voor het product \"%s\". U moet eerst een "
"grootboek aanmaken in de boekhoudinstellingen."

#. module: sale
#: code:addons/sale/sale.py:330 code:addons/sale/sale.py:334
#, python-format
msgid "There is no invoicable line."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "This Year"
msgstr "Dit jaar"

#. module: sale
#: model:web.tip,description:sale.sale_tip_1
msgid ""
"This progress bar shows the stages your quotation will go through.\n"
"                Use buttons on the left to move forward to the next stages."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman, "
"partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"Dit overzicht voert een analyse uit op uw offertes en verkooporders. "
"Analyseer uw inkomsten en sorteer het op verschillende gropeer criteria "
"(verkoper, relatie, product, etc.) Gebruik dit overzicht op nog niet "
"gefactureerde verkopen. Als u uw omzet wilt analyseren, moet u de factuur "
"analyse gebruiken in de financiële applicatie."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Dit overzicht voert een analyse uit op uw offertes. Analyseer uw inkomsten "
"en sorteer het op verschillende groepeer criteria (verkoper, relatie, "
"product, etc.) Gebruik dit overzicht op nog niet gefactureerde verkopen. Als "
"u uw omzet wilt analyseren, moet u de factuur analyse gebruiken in de "
"financiële applicatie."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Dit overzicht voert een analyse uit op uw verkooporders. Analyseer uw "
"inkomsten en sorteer het op verschillende gropeer criteria (verkoper, "
"relatie, product, etc.) Gebruik dit overzicht op nog niet gefactureerde "
"verkopen. Als u uw omzet wilt analyseren, moet u de factuur analyse "
"gebruiken in de financiële applicatie."

#. module: sale
#: selection:product.template,track_service:0
#, fuzzy
msgid "Timesheets on project"
msgstr "Urenstaten op contract"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_to_invoice
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "To Invoice"
msgstr "Te factureren"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_total
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Totaal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_price_total
msgid "Total Price"
msgstr "Totaalprijs"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Included"
msgstr "Totaal incl. BTW"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_track_service
#: model:ir.model.fields,field_description:sale.field_product_template_track_service
msgid "Track Service"
msgstr "Dienst opvolgen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Unit Price"
msgstr "Eenheidsprijs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report_product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Maateenheid"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_uom
#, fuzzy
msgid "Units of Measure"
msgstr "Maateenheid"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Unread Messages"
msgstr "Ongelezen berichten"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_untaxed
msgid "Untaxed Amount"
msgstr "Netto bedrag"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_price_subtotal
msgid "Untaxed Total Price"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "Upselling"
msgstr "Meer verkopen"

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Upselling Opportunity"
msgstr "Opportuniteit voor meerverkoop"

#. module: sale
#: model:web.tip,description:sale.sale_tip_2
msgid ""
"Use pivot and graph views to analyze your sales pipeline.\n"
"                Select measures, filter and group dimensions to get the "
"perfect report according to your needs."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_sale_pricelist
msgid "Use pricelists to adapt your price per customers"
msgstr "Gebruik prijslijsten voor klantspecifieke prijzen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "VAT:"
msgstr "BTW:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_volume
msgid "Volume"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_advance_payment_method
msgid "What do you want to invoice?"
msgstr "Wat wilt u factureren?"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""
"Werken met productvarianten biedt u de mogelijkheid om varianten voor "
"eenzelfde product te definiëren, en vergemakkelijkt het productbeheer voor "
"bijv. e-Commerce"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:26
#, python-format
msgid "Wrong value entered!"
msgstr "Verkeerde waarde ingevoerd!"

#. module: sale
#: code:addons/sale/sale.py:163
#, python-format
msgid ""
"You can not delete a sent quotation or a sales order! Try to cancel it "
"before."
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:797
#, python-format
msgid ""
"You can not remove a sale order line.\n"
"Discard changes and try setting the quantity to 0."
msgstr ""
"U kan een verkooplijn niet verwijderen.\n"
"Negeer de wijzigingen en probeer de hoeveelheid op 0 te zetten."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch, or check\n"
"                every order and invoice them one by one."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
#, fuzzy
msgid "You will find here all orders that are ready to be invoiced."
msgstr "Verkooporderregels gereed om te factureren"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Your next actions should flow efficiently: confirm the Quotation \n"
"                to a Sale Order, then create the Invoice and collect the "
"Payment."
msgstr ""
"Uw volgende acties moeten efficiënt verlopen: verander de offerte in een \n"
"verkooporder, maak daarna de factuur aan en verwerk de betaling ervan."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
msgid ""
"Your next actions should flow efficiently: confirm the Quotation to a Sale "
"Order, then create the Invoice and collect the Payment."
msgstr ""
"Uw volgende acties moeten efficiënt verlopen: verander de offerte in een "
"verkooporder, maak daarna de factuur aan en verwerk de betaling ervan."

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
#, fuzzy
msgid "account analytic line"
msgstr "Kostenplaatsboeking"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "dagen"

#. module: sale
#: model:ir.model,name:sale.model_sale_config_settings
msgid "sale.config.settings"
msgstr "sale.config.settings"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_sale_pricelist_setting
msgid "unknown"
msgstr "onbekend"

#~ msgid "<span groups=\"sale.group_discount_per_so_line\">Disc.(%)</span>"
#~ msgstr "<span groups=\"sale.group_discount_per_so_line\">Kort.(%)</span>"

#~ msgid "Action Needed"
#~ msgstr "Vereist actie"

#~ msgid "Allows you to specify an analytic account on sales orders."
#~ msgstr ""
#~ "Geeft u de mogelijkheid om kostenplaatsen te koppelen aan verkooporders"

#~ msgid "Analytic accounting for sales"
#~ msgstr "Kostenplaatsen voor verkopen"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Datum van het laatste bericht verstuurt op deze regel."

#~ msgid "Followers"
#~ msgstr "Volgers"

#~ msgid "Followers (Channels)"
#~ msgstr "Volgers (Kanalen)"

#~ msgid "Followers (Partners)"
#~ msgstr "Volgers (Partners)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Indien aangevinkt zullen nieuwe berichten uw aandacht vragen."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Indien aangevinkt vragen nieuwe berichten uw aandacht."

#~ msgid "Invoice based on time and material"
#~ msgstr "Factuur gebaseerd op tijd en materiaal"

#~ msgid "Is Follower"
#~ msgstr "Is een volger"

#~ msgid "Last Message Date"
#~ msgstr "Laatste bericht datum"

#~ msgid "Messages"
#~ msgstr "Berichten"

#~ msgid "Messages and communication history"
#~ msgstr "Berichten en communicatie historie"

#~ msgid "Number of Actions"
#~ msgstr "Aantal acties"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Aantal berichten die actie vereisen"

#~ msgid "Number of unread messages"
#~ msgstr "Aantal ongelezen berichten"

#~ msgid "Open Sale Menu"
#~ msgstr "Open verkoop menu"

#~ msgid "Unit of Measures"
#~ msgstr "Maateenheden"

#~ msgid "Unread Messages Counter"
#~ msgstr "Teller ongelezen berichten"

#~ msgid "Website Messages"
#~ msgstr "Website berichten"

#~ msgid "Website communication history"
#~ msgstr "Website communicatie geschiedenis"

#~ msgid "You can only delete draft quotations!"
#~ msgstr "U kan enkel concept offertes verwijderen! "

#~ msgid "account.config.settings"
#~ msgstr "account.config.settings"
