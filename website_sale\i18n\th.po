# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>hwu<PERSON><PERSON>aengsawang <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON>hipreecha, 2022
# <PERSON>, 2022
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-26 11:51+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "ดึงข้อมูล"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "\" หมวดหมู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_categories_recursive
msgid "#{'Unfold' if c.id in category.parents_and_self.ids else 'Fold'}"
msgstr "#{'Unfold' if c.id in category.parents_and_self.ids else 'Fold'}"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s รีวิว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "%s รีวิว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "&amp; Shipping"
msgstr "และการจัดส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "กำลังแสดงผลลัพธ์สำหรับ "

#. module: website_sale
#: model:product.template.attribute.value,name:website_sale.product_1_attribute_3_value_1
msgid "1 year"
msgstr "1 ปี"

#. module: website_sale
#: model:product.template.attribute.value,name:website_sale.product_1_attribute_3_value_2
msgid "2 year"
msgstr "2 ปี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>การสื่อสาร: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<b>Shipping: </b>"
msgstr "<b>การจัดส่ง: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "<b>Your order: </b>"
msgstr "<b>คำสั่งของคุณ: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"                รับประกันคืนเงินภายใน 30 วัน<br/>\n"
"               การจัดส่งสินค้า: 2-3 วันทำการ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<i class=\"fa fa-arrow-right\"/> Add payment acquirers"
msgstr "<i class=\"fa fa-arrow-right\"/>เพิ่มผู้ให้บริการรับชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid "<i class=\"fa fa-bolt mr-2\"/>BUY NOW"
msgstr "<i class=\"fa fa-bolt mr-2\"/>ซื้อเลย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "<i class=\"fa fa-check\"/> Ship to this address"
msgstr "<i class=\"fa fa-check\"/> จัดส่งไปยังที่อยู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                            <span>Back</span>"
msgstr ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                            <span>กลับ</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                    <span>Return to Cart</span>"
msgstr ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                    <span>กลับไปที่รถเข็น</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_footer
msgid "<i class=\"fa fa-chevron-left\"/> Return to Cart"
msgstr "<i class=\"fa fa-chevron-left\"/>กลับไปที่รถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-edit\"/> Edit"
msgstr "<i class=\"fa fa-edit\"/> แก้ไข"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "<i class=\"fa fa-eraser mr-1\"/><u>Clear filters</u>"
msgstr "<i class=\"fa fa-eraser mr-1\"/><u>ล้างตัวกรอง</u>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-plus-square\"/>\n"
"                                                        <span>Add an address</span>"
msgstr ""
"<i class=\"fa fa-plus-square\"/>\n"
"                                                        <span>เพิ่มที่อยู่</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> พิมพ์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "<i class=\"fa fa-shopping-cart mr-2\"/>ADD TO CART"
msgstr "<i class=\"fa fa-shopping-cart mr-2\"/>เพิ่มในรถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">ประเทศ...</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/crm_team.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        คุณสามารถค้นหารถเข็นที่ถูกละทิ้งทั้งหมดได้ที่นี่ เช่น รถเข็นที่สร้างขึ้นโดยผู้เยี่ยมชมเว็บไซต์ของคุณเมื่อหนึ่งชั่วโมงที่แล้วที่ยังไม่ได้รับการยืนยัน</p>\n"
"                        <p>คุณควรส่งอีเมลถึงลูกค้าเพื่อเชิญชวนพวกเขา!</p>\n"
"                    "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. Please "
"contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-"
"muted\">ไม่อนุญาตให้เปลี่ยนชื่อบริษัทหรือหมายเลข VAT "
"เมื่อออกเอกสารสำหรับบัญชีของคุณแล้ว "
"โปรดติดต่อเราโดยตรงสำหรับการดำเนินการนี้</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                        <span class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span class=\"\">ดำเนินการชำระเงิน</span>\n"
"                                        <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                    <span class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span class=\"\">ดำเนินการชำระเงิน</span>\n"
"                                    <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid ""
"<span class=\"d-none d-lg-inline font-weight-bold text-muted\">Sort "
"By:</span>"
msgstr ""
"<span class=\"d-none d-lg-inline font-weight-bold text-"
"muted\">เรียงตาม:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid ""
"<span class=\"fa fa-chevron-down fa-border float-right\" role=\"img\" aria-"
"label=\"Details\" title=\"Details\"/>"
msgstr ""
"<span class=\"fa fa-chevron-down fa-border float-right\" role=\"img\" aria-"
"label=\"รายละเอียด\" title=\"Details\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"fa fa-chevron-left fa-2x\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"
msgstr ""
"<span class=\"fa fa-chevron-left fa-2x\" role=\"img\" aria-"
"label=\"ก่อนหน้า\" title=\"Previous\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                        <span class=\"\">Continue Shopping</span>"
msgstr ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                        <span class=\"\">ช้อปปิ้งต่อ</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                    Continue<span class=\"d-none d-md-inline\"> Shopping</span>"
msgstr ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                    <span class=\"d-none d-md-inline\">ช้อปปิ้ง</span>ต่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"fa fa-chevron-left\"/> Previous"
msgstr "<span class=\"fa fa-chevron-left\"/> ก่อนหน้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"fa fa-chevron-right fa-2x\" role=\"img\" aria-label=\"Next\" "
"title=\"Next\"/>"
msgstr ""
"<span class=\"fa fa-chevron-right fa-2x\" role=\"img\" aria-label=\"ต่อไป\" "
"title=\"Next\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" "
"title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะเว็บไซต์\" "
"groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Abandoned Carts</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">รถเข็นที่ถูกละทิ้ง</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะเว็บไซต์\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Assignment</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">การมอบหมาย</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะเว็บไซต์\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Confirmation Email</span>"
msgstr "<span class=\"o_form_label\">การยืนยันอีเมล์</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Invoicing Policy</span>"
msgstr "<span class=\"o_form_label\">นโยบายการออกใบแจ้งหนี้</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr ""
"<span "
"class=\"s_website_form_label_content\">ให้ข้อเสนอแนะของคุณกับเรา</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr "<span class=\"s_website_form_label_content\">อัปโหลดเอกสาร</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">ข้อมูลอ้างอิงของคุณ</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('product_variant_count', "
"'&lt;=', 1)]}\">Based on variants</span>"
msgstr ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('product_variant_count', "
"'&lt;=', 1)]}\">ขึ้นอยู่กับรุ่นต่าง ๆ </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<span>Confirm</span>\n"
"                                    <i class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span>ยืนยัน</span>\n"
"                                    <i class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Confirmed</span>"
msgstr "<span>ยืนยันแล้ว</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<span>Next</span>\n"
"                                            <i class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span>ต่อไป</span>\n"
"                                            <i class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>คำสั่ง</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "<span>Process Checkout</span>"
msgstr "<span>ดำเนินการชำระเงิน</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>ขออภัย ไม่สามารถยืนยันคำสั่งซื้อของคุณได้ เนื่องจากจำนวนเงินที่คุณชำระไม่ตรงกับจำนวนในรถเข็น\n"
"                        โปรดติดต่อผู้รับผิดชอบของร้านค้าเพื่อขอข้อมูลเพิ่มเติม</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>ดูตัวอย่างวิดีโอ</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<span>Your payment has been authorized.</span>"
msgstr "<span>การชำระเงินของคุณได้รับการอนุมัติแล้ว</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form_web_terms
msgid "<strong class=\"align-top\">URL: </strong>"
msgstr "<strong class=\"align-top\">URL: </strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid ""
"<strong class=\"o_categories_collapse_title text-"
"uppercase\">Categories</strong>"
msgstr ""
"<strong class=\"o_categories_collapse_title text-"
"uppercase\">หมวดหมู่</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "<strong>Add to Cart</strong>"
msgstr "<strong>Add to Cart1"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                    If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>ไม่พบตัวเลือกการชำระเงินที่เหมาะสม</strong><br/>\n"
"                                   หากคุณเชื่อว่าเป็นข้อผิดพลาด โปรดติดต่อผู้ดูแลเว็บไซต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Payment Information:</strong>"
msgstr "<strong>ข้อมูลการชำระเงิน:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total:</strong>"
msgstr "<strong>ทั้งหมด:</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Cart</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">S00060</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br/><br/>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr/>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\" t-out=\"company.name or ''\">My Company (San Francisco)</td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"company.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"company.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- ส่วนหัว -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">รถเข็นของคุณ</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">S00060</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- เนื้อหา -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">มีของในรถเข็นของคุณ</h1>\n"
"                    คุณต้องการซื้อให้เสร็จสิ้นหรือไม่?<br/><br/>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] ชุดโต๊ะ</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Desk ชุดโต๊ะรวมกัน น้ำตาลดำ: เก้าอี้ + โต๊ะ + ลิ้นชัก.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">ยูนิต</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr/>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                           ดำเนินการสั่งซื้อต่อ\n"
"                        </a>\n"
"                    </div>\n"
"                    <div style=\"text-align: center;\"><strong>ขอบคุณสำหรับการช้อปปิ้งกับ <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- ส่วนท้าย -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\" t-out=\"company.name or ''\">My Company (San Francisco)</td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"company.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"company.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- สนับสนุนโดย -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        สนับสนุนโดย<a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__terms_url
msgid "A preview will be available at this URL."
msgstr "ตัวอย่างจะสามารถใช้ได้ที่ URL นี้"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""
"สินค้าสามารถเป็นได้ทั้งสินค้าที่จับต้องได้หรือบริการที่คุณขายให้กับลูกค้าของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "A short description that will also appear on documents."
msgstr "คำอธิบายสั้น ๆ ที่จะปรากฏบนเอกสารด้วย"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "AT A GLANCE"
msgstr "ได้อย่างรวดเร็ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "ละทิ้ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "รถเข็นที่ถูกละทิ้ง"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/models/crm_team.py:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#, python-format
msgid "Abandoned Carts"
msgstr "รถเข็นที่ถูกละทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "กู้คืนรถเข็นที่ถูกละทิ้ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "ความล่าช้าที่ถูกละทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Abandoned carts are all carts left unconfirmed by website visitors. You can "
"find them in *Website > Orders > Abandoned Carts*. From there you can send "
"recovery emails to visitors who entered their contact details."
msgstr ""
"รถเข็นที่ถูกละทิ้งคือรถเข็นทั้งหมดที่ไม่ได้รับการยืนยันจากผู้เยี่ยมชมเว็บไซต์"
" คุณสามารถค้นหาได้ใน *เว็บไซต์ > คำสั่งซื้อ > รถเข็นที่ถูกละทิ้ง* จากที่นั่น"
" "
"คุณสามารถส่งอีเมลการกู้คืนไปยังผู้เยี่ยมชมที่ป้อนรายละเอียดการติดต่อของพวกเขา"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "เกี่ยวกับการขายสินค้าไขว้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "ขนาดไฟล์ที่รองรับ"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""
"สินค้าเสริมจะปรากฏขึ้นเมื่อลูกค้าตรวจสอบรถเข็นก่อนชำระเงิน "
"(กลยุทธ์การขายไขว้)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "สินค้าเสริม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "หมายเลขบัญชี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add a Media"
msgstr "เพิ่มสื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Add one"
msgstr "เพิ่มหนึ่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add price per base unit of measure on your Products"
msgstr "เพิ่มราคาต่อฐานหน่วยวัดของสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
msgid "Add to Cart"
msgstr "เพิ่มในรถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
msgid "Add to Cart <i class=\"fa fa-fw fa-shopping-cart\"/>"
msgstr "เพิ่มในรถเข็น <i class=\"fa fa-fw fa-shopping-cart\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Address"
msgstr "ที่อยู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "สินค้าทั้งหมด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "รายการราคาทั้งหมด"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_utils.xml:0
#, python-format
msgid "All results"
msgstr "ผลลัพธ์ทั้งหมด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr "อนุญาตให้ผู้ซื้อเปรียบเทียบสินค้าตามคุณลักษณะของสินค้า"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "อนุญาตให้ผู้ใช้เลือกรายการราคานี้"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
msgid "Alternative Products"
msgstr "สินค้าทางเลือก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.recommended_products
msgid "Alternative Products:"
msgstr "สินค้าทางเลือก:"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "จำนวนรถเข็นที่ถูกทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "ใช้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"ใช้ส่วนลดด้วยตนเองในรายการใบสั่งขายหรือแสดงส่วนลดที่คำนวณจากรายการราคา "
"(ตัวเลือกเพื่อเปิดใช้งานในการตั้งค่าการกำหนดค่ารายการราคา)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Apply specific prices per country, discounts, etc."
msgstr "ใช้ราคาเฉพาะสำหรับแต่ละประเทศ ส่วนลด และอื่น ๆ "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "มอบหมายคำสั่งออนไลน์"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Attributes"
msgstr "คุณลักษณะ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Average Order"
msgstr "คำสั่งโดยเฉลี่ย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "ชื่อธนาคาร"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "จำนวนหน่วยฐาน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "ชื่อหน่วยฐาน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "ราคาต่อหน่วยฐาน"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
#: model:ir.ui.menu,name:website_sale.website_base_unit_menu
msgid "Base Units"
msgstr "หน่วยฐาน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "ระวัง!"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "BeNeLux"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "Benelux"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Best Sellers"
msgstr "ขายดีที่สุด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Billing"
msgstr "ทำรายการบิล"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing Address"
msgstr "ที่อยู่เรียกเก็บเงิน"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Bin"
msgstr "ถังขยะ"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "ถังขยะ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""
"เพิ่มยอดขายของคุณด้วยโปรแกรมส่วนลดสองประเภท: โปรโมชั่นและโค้ดคูปอง "
"และสามารถกำหนดเงื่อนไขเฉพาะได้ (สินค้า ลูกค้า ยอดซื้อขั้นต่ำ ระยะเวลา) "
"รางวัลอาจเป็นส่วนลด (% หรือจำนวน) หรือผลิตภัณฑ์ฟรี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "ด้านล่าง"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Box"
msgstr "กล่อง"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "กล่อง"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "แบรนด์"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "ตู้เก็บของ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Campaigns"
msgstr "แคมเปญ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "สามารถขยายภาพ 1024 ได้หรือไม่"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "สามารถเผยแพร่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Can be used as payment toward future orders."
msgstr "สามารถใช้เป็นการชำระเงินสำหรับการสั่งซื้อในอนาคต"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Capture order payments when the delivery is completed."
msgstr "บันทึกการชำระเงินตามคำสั่งซื้อเมื่อการจัดส่งสร็จสิ้น"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "ปริมาณรถเข็น"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "อีเมลกู้คืนรถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Cart is abandoned after"
msgstr "รถเข็นถูกละทิ้งหลังจาก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "ส่งอีเมลกู้คืนรถเข็นแล้ว"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Carts"
msgstr "รถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "รถเข็นจะถูกตั้งสถานะว่าละทิ้งหลังจากเกิดความล่าช้านี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Categories"
msgstr "หมวดหมู่"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""
"หมวดหมู่ที่ใช้เพื่อเรียกดูผลิตภัณฑ์ของคุณผ่านทาง\n"
"          อินเตอร์เฟซหน้าจอสัมผัส"

#. module: website_sale
#: code:addons/website_sale/models/product_template.py:0
#, python-format
msgid "Categories:"
msgstr "หมวดหมู่:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "หมวดหมู่"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "คำอธิบายหมวดหมู่"

#. module: website_sale
#: code:addons/website_sale/models/product_template.py:0
#, python-format
msgid "Category:"
msgstr "หมวดหมู่:"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Chair"
msgstr "เก้าอี้"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "เก้าอี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"ไม่อนุญาตให้เปลี่ยนหมายเลข VAT เมื่อออกเอกสารสำหรับบัญชีของคุณแล้ว "
"โปรดติดต่อเราโดยตรงสำหรับการดำเนินการนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"ไม่อนุญาตให้เปลี่ยนชื่อบริษัทเมื่อมีการออกเอกสารสำหรับบัญชีของคุณแล้ว "
"โปรดติดต่อเราโดยตรงสำหรับการดำเนินการนี้"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once documents have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "หมวดหมู่เด็ก"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_open_website_sale_onboarding_payment_acquirer_wizard
msgid "Choose a payment method"
msgstr "เลือกวิธีการชำระเงิน"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "คริสต์มาส"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "เมือง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr "คลิก <i>'ใหม่'</i> ที่มุมบนขวาเพื่อสร้างสินค้าชิ้นแรกของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "คลิกที่นี้"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on <em>Continue</em> to create the product."
msgstr "คลิกที่ <em>ต่อ</em> เพื่อสร้างสินค้า"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on this button so your customers can see it."
msgstr "คลิกที่ปุ่มเพื่อให้ลูกค้าของคุณเห็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Comma-separated list of parts of product names"
msgstr "รายการที่คั่นด้วยเครื่องหมายจุลภาคของชื่อสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
#, python-format
msgid "Company Name"
msgstr "ชื่อบริษัท"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "ชิ้นส่วน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "คำนวณค่าขนส่งและจัดส่งด้วย Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ DHL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ UPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "คำนวณค่าจัดส่งตามคำสั่งซื้อ"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Confirm Order"
msgstr "ยืนยันการสั่งซื้อ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Confirm orders when you get paid."
msgstr "ยืนยันคำสั่งซื้อเมื่อคุณได้รับการชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "ยืนยันแล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "ยืนยันการสั่งซื้อ"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Conversion"
msgstr "การแปลง"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "โซฟา"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "ประเทศ"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "สร้างสินค้าใหม่"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "วันที่สร้าง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "หมวดหมุ่ปัจจุบันหรือทั้งหมด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "หน่วยวัดแบบกำหนดเอง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "ลูกค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "ประเทศของลูกค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "รีวิวของลูกค้า"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
msgid "Customers"
msgstr "ลูกค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "ตัวเชื่อม DHL Express"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "สกุลเงินเริ่มต้น"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist"
msgstr "รายการราคาเริ่มต้น"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr "กำหนดหน่วยแบบกำหนดเองเพื่อแสดงราคาต่อหน่วยของฟิลด์หน่วยวัด"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "กำหนดหมวดหมู่ใหม่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete"
msgstr "ลบ"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__internal
msgid ""
"Delivery methods are only used internally: the customer doesn't pay for "
"shipping costs"
msgstr "วิธีการจัดส่งจะใช้ภายในเท่านั้น: ลูกค้าไม่ต้องจ่ายค่าขนส่ง"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__website
msgid ""
"Delivery methods are selectable on the website: the customer pays for "
"shipping costs"
msgstr "สามารถเลือกวิธีการจัดส่งได้บนเว็บไซต์: ลูกค้าต้องชำระค่าขนส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "รายละเอียด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "รายละเอียดสำหรับเว็บไซต์"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "โต๊ะ"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "กำหนดลำดับการแสดงผลในเว็บไซต์อีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "ไดเจสต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_digital
msgid "Digital Content"
msgstr "เนื้อหาดิจิทัล"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display a prompt with optional products when adding to cart"
msgstr "แสดงข้อความหระตุ้นพร้อมสินค้าเสริมอื่น ๆ เมื่อหยิบใส่รถเข็น"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr ""
"แสดงราคาต่อหน่วยฐานในหน้าอีคอมเมิร์ซของคุณ ตั้งค่าเป็น 0 "
"เพื่อซ่อนสำหรับผลิตภัณฑ์นี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "แสดงอยู่ด้านล่างของหน้าสินค้า"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr ""
"แสดงหน่วยที่กำหนดเองสำหรับผลิตภัณฑ์หากมีการกำหนดไว้หรือหน่วยวัดที่เลือกเป็นอย่างอื่น"

#. module: website_sale
#: code:addons/website_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "ไม่มีสิทธิ์เข้าถึง ข้ามข้อมูลนี้สำหรับอีเมลไดเจสต์ของผู้ใช้"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__done
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Double click here to set an image describing your product."
msgstr "ดับเบิลคลิกที่นี้เพื่อกำหนดรูปภาพที่อธิบายผลิตภัณฑ์ของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Drag building blocks here to customize the header for \""
msgstr "ลากบล็อกสำเร็จรูปมาที่นี้เพื่อปรับแต่งส่วนหัวสำหรับ \""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr "ลากบล็อกเว็บไซต์นี้แล้ววางลงในหน้าเว็บของคุณ"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Drawer"
msgstr "ลิ้นชัก"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "ลิ้นชัก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "ฟิลด์เสริมอีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr "ข้อมูลเสริมอีคอมเมิร์ซแสดงบนหน้าสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "โค้ดโปรโมชั่นอีคอมเมิร์ซ"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "EUR"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Edit"
msgstr "แก้ไข"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form_web_terms
msgid "Edit in Website Builder"
msgstr "แก้ไขตัวสร้างเว็บไซต์"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr "แก้ไขราคาสินค้าโดยคลิกที่จำนวนเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "แก้ไขที่อยู่"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_email_account
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "อีเมล"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email Template"
msgstr "เทมเพลตอีเมล"

#. module: website_sale
#: model:ir.model,name:website_sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "ตัวช่วยการเขียนอีเมล"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email sent to the customer after the checkout"
msgstr "อีเมลที่ส่งถึงลูกค้าหลังการชำระเงิน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "ฝังโค้ด"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Enter a name for your new product"
msgstr "ใส่ชื่อสำหรับสินค้าใหม่ของคุณ"

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "ข้อผิดพลาด ! คุณไม่สามารถสร้างหมวดหมู่แบบเรียกซ้ำได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
msgid "Extra Info"
msgstr "ข้อมูลเพิ่มเติม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Extra Product Media"
msgstr "สื่อสินค้าพิเศษ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "รูปภาพหลากหลายแบบพิเศษ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "สื่อตัวแปรพิเศษ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Featured"
msgstr "ฟีเจอร์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "FedEx"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "ฟิลด์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "ป้ายฟิลด์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "ชื่อฟิลด์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "จากเว็บไซต์"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "เฟอร์นิเจอร์"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Generate an invoice from orders ready for invoicing."
msgstr "สร้างใบแจ้งหนี้จากคำสั่งซื้อที่พร้อมสำหรับการออกใบแจ้งหนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "สร้างใบแจ้งหนี้โดยอัตโนมัติเมื่อยืนยันการชำระเงินออนไลน์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_gift_card
msgid "Gift Card"
msgstr "บัตรของขวัญ"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "จัดลำดับการเรียงเมื่อแสดงรายการประเภทผลิตภัณฑ์"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to cart"
msgstr "ไปที่รถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "มอบส่วนลดในไลน์ใบสั่งขาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
msgid "Grid"
msgstr "ตาราง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "จัดกลุ่มตาม"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
msgid "Hidden"
msgstr "ซ่อน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr "ขนาดไฟล์ใหญ่ รูปภาพควรได้รับการปรับให้เหมาะสม/ลดขนาด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "I agree to the"
msgstr "ฉันเห็นด้วยกับ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "I have a promo code"
msgstr "ฉันมีรหัสโปรโมชั่น"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__id
msgid "ID"
msgstr "ไอดี"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "รูปภาพ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "รูปภาพ 1024"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "รูปภาพ 128"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "รูปภาพ 256"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "รูปภาพ 512"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "ชื่อรูปภาพ"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""
"หากคุณกำลังสั่งซื้อสำหรับบุคคลภายนอก โปรดสั่งซื้อผ่านทางแบ็กเอนด์ "
"หากคุณต้องการเปลี่ยนชื่อหรือที่อยู่อีเมลของคุณ "
"โปรดดำเนินการในการตั้งค่าบัญชีหรือติดต่อผู้ดูแลระบบของคุณ"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "อีเมลไม่ถูกต้อง! กรุณาใส่อีเมล์ที่ถูกต้อง."

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_invoices_ecommerce
msgid "Invoices"
msgstr "ใบแจ้งหนี้"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "การออกใบแจ้งหนี้"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "เผยแพร่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "ออกใบแจ้งหนี้ให้กับลูกค้า"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr "ห้ามแก้ไขใบสั่งขายที่ไม่อยู่ในสถานะร่าง"

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "รายการบันทึก"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__just_done
msgid "Just done"
msgstr "เพิ่งเสร็จ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "ยอดขายรวมเว็บไซต์ Kpi "

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lamp"
msgstr "โคมไฟ"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "โคมไฟ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_public_category____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "เดือนที่แล้ว"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "คำสั่งขายออนไลน์ล่าสุด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "สัปดาห์ที่แล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "ปีที่แล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "ซ้าย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let returning shoppers save products in a wishlist"
msgstr "ให้นักช้อปกลับมาบันทึกสินค้าในรายการสิ่งที่อยากได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr "ให้ลูกค้ากรอกที่อยู่สำหรับจัดส่ง"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Let's create your first product."
msgstr "เริ่มสร้างสินค้าแรกของคุณ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Let's now take a look at your administration dashboard to get your eCommerce"
" website ready in no time."
msgstr ""
"มาดูแดชบอร์ดการดูแลระบบของคุณเพื่อเตรียมเว็บไซต์อีคอมเมิร์ซของคุณให้พร้อมในเวลาไม่นาน"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lightbulb sold separately"
msgstr "หลอดไฟแยก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "เชื่อมโยงไลน์คำสั่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
msgid "List"
msgstr "รายการ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Log In"
msgstr "ลงชื่อเข้าใช้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "จัดการโปรโมชั่นและโปรแกรมคูปอง"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Medium"
msgstr "สื่อกลาง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "ไอดีบัญชีการค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "วิธีการ"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "มัลติมีเดีย"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
#, python-format
msgid "My Cart"
msgstr "รถเข็นของฉัน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Name"
msgstr "ชื่อ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "ชื่อสั้น"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/controllers/main.py:0
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "New Product"
msgstr "สินค้าใหม่"

#. module: website_sale
#: model:product.ribbon,html:website_sale.new_ribbon
msgid "New!"
msgstr "ใหม่!"

#. module: website_sale
#: model:ir.filters,name:website_sale.dynamic_snippet_newest_products_filter
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "สินค้าใหม่ล่าสุด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Newest arrivals"
msgstr "มาใหม่ล่าสุด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Next <span class=\"fa fa-chevron-right\"/>"
msgstr "ต่อไป <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "ไม่พบรถเข็นที่ถูกทอดทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "ไม่ได้กำหนดสินค้าไว้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in category \""
msgstr "ไม่มีการกำหนดสินค้าในหมวดหมู่ \""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "ยังไม่มีการดูสินค้าสำหรับผู้เยี่ยมชมรายนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "No redirect when the user adds a product to cart."
msgstr "ไม่มีการเปลี่ยนเส้นทางเมื่อผู้ใช้เพิ่มสินค้าลงในรถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "ไม่มีผลลัพธ์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr "ไม่พบผลลัพธ์สำหรับ \" "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "ไม่พบผลลัพธ์สำหรับ '"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_utils.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "ไม่พบผลลัพธ์ โปรดลองค้นหาอีกครั้ง"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__none
msgid "No shipping management on website"
msgstr "ไม่มีการจัดการการจัดส่งบนเว็บไซต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "ไม่มี"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__not_done
msgid "Not done"
msgstr "ยังไม่เสร็จ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "จำนวนรถเข็นที่ถูกละทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Number of Columns"
msgstr "จำนวนคอลัมน์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "จำนวนคอลัมน์ตารางในร้านค้า"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__cart_abandoned_delay
msgid "Number of hours after which the cart is considered abandoned."
msgstr "จำนวนชั่วโมงหลังจากที่รถเข็นถูกละทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Number of products"
msgstr "จำนวนสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "จำนวนสินค้าในตารางของร้าน"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "On wheels"
msgstr "บนล้อ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr "เมื่อคุณคลิกที่ <b>บันทึก</b> สินค้าของคุณจะได้รับการอัปเดต"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "การขายออนไลน์"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "วิเคราะห์การขายออนไลน์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "เฉาะบริการ"

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"อนุญาตเฉพาะเว็บไซต์ของบริษัทเท่านั้น\n"
"ปล่อยให้ช่องบริษัทว่างหรือเลือกเว็บไซต์จากบริษัทนั้น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "โอเพ่นซอร์สอีคอมเมิร์ซ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Open your website app here."
msgstr "เปิดเว็บไซต์ของคุณที่นี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr ""
"จำเป็นต้องเพิ่มประสิทธิภาพ! ลดขนาดภาพหรือเพิ่มการตั้งค่าการบีบอัดของคุณ"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Option for: %s"
msgstr "ตัวเลือกสำหรับ: %s"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Option: %s"
msgstr "ตัวเลือก: %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Optional Products"
msgstr "สินค้าทางเลือก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__option_line_ids
msgid "Options Linked"
msgstr "ตัวเลือกลิงก์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "หรือสแกนฉันด้วยแอปธนาคารของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "วันที่สั่งซื้อ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "ไลน์การสั่งซื้อที่แสดงบนเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_order_line
msgid ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."
msgstr "ไลน์คำสั่งที่จะแสดงบนเว็บไซต์ ไม่ควรใช้เพื่อวัตถุประสงค์ในการคำนวณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "Order Total"
msgstr "คำสั่งทั้งหมด"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
#, python-format
msgid "Orders"
msgstr "รายการสั่งซื้อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Orders Followup"
msgstr "ติดตามคำสั่งซื้อ"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "คำสั่งซื้อเป็นใบแจ้งหนี้"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Orders to Invoice"
msgstr "คำสั่งที่จะออกใบแจ้งหนี้"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Orders/Day"
msgstr "คำสั่ง/วันที่"

#. module: website_sale
#: model:product.ribbon,html:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "สินค้าหมด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "โทเคน PDT Identity"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_page
msgid "Page"
msgstr "เพจ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "หมวดหมู่หลัก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "เส้นทางหลัก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr "หลักและของตัวเอง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now"
msgstr "ชำระเดี๋ยวนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay with"
msgstr "ชำระด้วย"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_acquirers
msgid "Payment Acquirers"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_icons
msgid "Payment Icons"
msgstr "ไอคอนการชำระเงิน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "คำแนะนำการชำระเงิน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "วิธีการชำระเงิน"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "โทเคนการชำระเงิน"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "ธุรกรรมการชำระเงิน"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Payments to Capture"
msgstr "บันทึกการชำระเงิน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "ประเภทผู้ใช้ Paypal"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Pedal-based opening system"
msgstr "ระบบเปิดแบบแป้นเหยียบ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "โทรศัพท์"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "หมายเลขโทรศัพท์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "โปรดป้อน URL ของวิดีโอที่ถูกต้อง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "กรุณาดำเนินการรถเข็นปัจจุบันของคุณ"

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Month"
msgstr "เดือนที่แล้ว"

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Week"
msgstr "สัปดาห์ที่แล้ว"

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Year"
msgstr "ปีที่แล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price"
msgstr "ราคา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price (high to low)"
msgstr "ราคา(มากไปน้อย)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price (low to high)"
msgstr "ราคา(น้อยไปมาก)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Price - High to Low"
msgstr "ราคา - มากไปน้อย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Price - Low to High"
msgstr "ราคา - น้อยไปมาก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "ราคาต่อหนึ่งหน่วย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "รายการราคาสำหรับอีคอมเมิร์ซ/เว็บไซต์นี้"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "รายการราคา"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_product_pricelist3
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "รายการราคา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "ราคา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "พิมพ์"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "ดำเนินการสั่งซื้อเมื่อได้รับการชำระเงิน"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#, python-format
msgid "Product"
msgstr "สินค้า"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "อุปกรณ์เสริมสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "คุณลักษณสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "หมวดหมู่สินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
msgid "Product Comparison Tool"
msgstr "เครื่องมือเปรียบเทียบสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "รูปภาพสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "รูปภาพสินค้า"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.product
#, python-format
msgid "Product Name"
msgstr "ชื่อสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr "ฟิลด์พิเศษหน้าผลิตภัณฑ์ "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "ราคาสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "หมวดหมู่สินค้าสาธารณะ"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "ไลน์คุณลักษณะเทมเพลตสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "เทมเพลตสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "ความหลากหลายของสินค้า"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_variants
msgid "Product Variants"
msgstr "ความหลากหลายของสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "ดูสินค้า"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "ประวัตืการดูสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product names"
msgstr "ชื่อสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product prices displaying in web catalog"
msgstr "ราคาสินค้าที่แสดงในเว็บแค็ตตาล็อก"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "สินค้า ribbon"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_settings
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "สินค้า"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "สินค้าที่เพิ่งขายด้วย"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "ดูสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Provide customers with product-specific links or downloadable content in the"
" confirmation page of the checkout process if the payment gets through. To "
"do so, attach some files to a product using the new Files button and publish"
" them."
msgstr ""
"ให้ลิงก์เฉพาะผลิตภัณฑ์หรือเนื้อหาที่ดาวน์โหลดได้แก่ลูกค้าในหน้ายืนยันของการเช็คเอาต์"
" หากการชำระเงินผ่าน ในการดำเนินการดังกล่าว "
"ให้แนบไฟล์บางไฟล์กับผลิตภัณฑ์โดยใช้ปุ่ม \"ไฟล์\" ใหม่และเผยแพร่"

#. module: website_sale
#: code:addons/website_sale/models/product_image.py:0
#, python-format
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr ""
"URL วิดีโอที่ระบุสำหรับ '%s' ไม่ถูกต้อง โปรดป้อน URL ของวิดีโอที่ถูกต้อง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
msgid "Published"
msgstr "เผยแพร่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "ดันลง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "ดันลงไปด้านล่าง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "ดันขึ้นไปด้านบน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "ดันขึ้น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Qty:"
msgstr "ปริมาณ:"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#, python-format
msgid "Quantity"
msgstr "ปริมาณ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "REVENUE BY"
msgstr "รายได้โดย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Rating"
msgstr "การให้คะแนน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Rating Average"
msgstr "คะแนนเฉลี่ย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "ให้คะแนนความคิดเห็นล่าสุด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "ให้คะแนนภาพล่าสุด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "ให้คะแนนค่าสุดท้าย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "จำนวนการให้คะแนน"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__rating_last_feedback
msgid "Reason of the rating"
msgstr "เหตุผลของการให้คะแนน"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "สินค้าที่ขายล่าสุด"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "สินค้าที่ดูล่าสุด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "อีเมลกู้คืนถูกส่งแล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "อีเมลกู้คืนที่จะส่ง"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Reinforced for heavy loads"
msgstr "เสริมแรงสำหรับการบรรทุกหนัก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "นำออกจากรถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Remove one"
msgstr "นำออกหนึ่ง"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_reporting
msgid "Reporting"
msgstr "การรายงาน"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict publishing to this website."
msgstr "จำกัดการเผยแพร่ในเว็บไซต์นี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Review Order"
msgstr "รีวิวคำสั่งซื้อ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Ribbon"
msgstr "Ribbon"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Ribbon background color"
msgstr "สีพื้นหลัง Ribbon "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html_class
msgid "Ribbon class"
msgstr "คลาส Ribbon"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html
msgid "Ribbon html"
msgstr "Ribbon html"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Ribbon text color"
msgstr "สีข้อความ Ribbon"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "ขวา"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "ปรับ SEO ให้เหมาะสม"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sale_ribbon
msgid "Sale"
msgstr "การขาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "วิเคราะห์การขาย"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#, python-format
msgid "Sales"
msgstr "ขาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "วิเคราะห์การขาย"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "รายงานการวิเคราะห์การขาย"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "ใบสั่งขาย"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "ไลน์คำสั่งขาย"

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Sales Order: Cart Recovery Email"
msgstr "คำสั่งขาย: อีเมลกู้คืนรถเข็น"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Month"
msgstr "ขายตั้งแต่เดือนที่แล้ว"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Week"
msgstr "ขายตั้งแต่สัปดาห์ที่แล้ว"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Year"
msgstr "ขายตั้งแต่ปีที่แล้ว"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "ทีมขาย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "พนักงานขาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "ตัวอย่าง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "ค้นหาใบสั่งขายที่ถูกละทิ้ง"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""
"เลือก <b>สินค้าใหม่</b>เพื่อสร้างและจัดการคุณสมบัติเพื่อเพิ่มยอดขายของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Select this address"
msgstr "เลือกที่อยู่นี้"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "สามารถเลือกได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell content to download or URL links"
msgstr "ขายเนื้อหาเพื่อดาวน์โหลดหรือลิงก์ URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr "ขายสินค้าที่มีคัวเลือกหลากหลาย โดยใช้คุณลักษณะ (ขนาด สี ต่าง ๆ )"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr "ส่งอีเมลการกู้คืนรถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "ส่งอีเมลการกู้คืน"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Send a recovery email to visitors who haven't completed their order."
msgstr "ส่งอีเมลการกู้คืนไปยังผู้เข้าชมที่ยังไม่ได้ดำเนินการสั่งซื้อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send a recovery email when a cart is abandoned"
msgstr "ส่งอีเมลสำรองเมื่อรถเข็นถูกละทิ้ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "ชื่อ Seo"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Sequence"
msgstr "ลำดับ"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "บริการ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Ship to the same address\n"
"                                                    <span class=\"ship_to_other text-muted\" style=\"display: none\">&amp;nbsp;(<i>Your shipping address will be requested later) </i></span>"
msgstr ""
"จัดส่งไปยังที่อยู่เดียวกัน\n"
"                                                    <span class=\"ship_to_other text-muted\" style=\"display: none\">&amp;nbsp;(<i>ที่อยู่จัดส่งของคุณจะถูกร้องขอในภายหลัง) </i></span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping"
msgstr "การจัดส่งสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping Address"
msgstr "ที่อยู่สำหรับจัดส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "ค่าใช้จ่ายการจัดส่ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__sale_delivery_settings
msgid "Shipping Management"
msgstr "การจัดการจัดส่งสินค้า"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Shop"
msgstr "ร้านค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "ร้านค้า - ชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "ร้านค้า - ยืนยันแล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Acquirer"
msgstr "ร้านค้า - เลือกผู้รับการชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "รถเข็นสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty Cart"
msgstr "แสดงรถเข็นว่างเปล่า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid "Show categories"
msgstr "แสดงหมวดหมู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "Show options"
msgstr "แสดงตัวเลือก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "ลงชื่อ"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Sit comfortably"
msgstr "นั่งสบาย ๆ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "ไซส์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "ไซส์ X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "ไซส์ Y"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Slanted"
msgstr "เอียง"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sold"
msgstr "ขาย"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "ขายหมดแล้ว"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "ช่องที่ต้องกรอกบางช่องว่างเปล่า"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sources"
msgstr "แหล่งที่มา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "รัฐ / จังหวัด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_company__website_sale_onboarding_payment_acquirer_state
msgid "State of the website sale onboarding payment acquirer step"
msgstr "สถานะของการขายบนเว็บไซต์อยู่ในขั้นตอนการรับการชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "สถานะ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_add_on_page
#: model:ir.model.fields,field_description:website_sale.field_website__cart_add_on_page
msgid "Stay on page after adding to cart"
msgstr "อยู่ในเพจหลังจากหยิบสินค้าลงรถเข็น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street 2"
msgstr "ถนน 2"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street <span class=\"d-none d-md-inline\"> and Number</span>"
msgstr "ถนน <span class=\"d-none d-md-inline\"> และเลขที่</span>"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "คีย์ของ Stripe ที่เผยแพร่ได้"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Stripe Secret Key"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal:"
msgstr "ยอดรวมย่อย:"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""
"เสนอทางเลือกอื่นให้กับลูกค้าของคุณ (กลยุทธ์การขายต่อยอด) "
"ผลิตภัณฑ์เหล่านั้นจะปรากฏบนหน้าสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested Accessories:"
msgstr "แนะนำอุปกรณ์เสริม:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "อุปกรณ์เสริมที่แนะนำในรถเข็นสินค้าอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid "VAT"
msgstr "ภาษีมูลค่าเพิ่ม (VAT)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tag"
msgstr "แท็ก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes:"
msgstr "ภาษี :"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website__all_pricelist_ids
msgid "Technical: Used to recompute pricelist_ids"
msgstr "เทคนิค: ใช้เพื่อคำนวณ pricelist_ids"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "Terms and Conditions"
msgstr "เงื่อนไขและข้อกำหนด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "ขอบคุณสำหรับการสั่งซื้อของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "อันดับ #1"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The access token is invalid."
msgstr "โทเค็นการเข้าถึงไม่ถูกต้อง"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has been updated. Please refresh the page."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr "URL แบบเต็มเพื่อเข้าถึงเอกสารผ่านเว็บไซต์"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr "ไม่มีำการรวมกันที่ระบุ จึงไม่สามารถเพิ่มลงในรถเข็นได้"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr "ไม่มีสินค้าที่ระบุ จึงไม่สามารถหยิบใส่ตะกร้าได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""
"โหมดที่เลือกที่นี่้จะนำไปใช้เป็นนโยบายการออกใบแจ้งหนี้สำหรับสินค้าใหม่ใด ๆ "
"ที่สร้างขึ้นแต่ไม่ใช่ของสินค้าที่มีอยู่แล้ว"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The order has been canceled."
msgstr "คำสั่งซื้อถูกยกเลิกแล้ว"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Customize and enable 'eCommerce categories' to view all eCommerce "
"categories."
msgstr ""
"สินค้าจะมีจำหน่ายในแต่ละหมวดอีคอมเมิร์ซที่กล่าวถึง ไปที่ร้านค้า > ปรับแต่ง "
"และเปิดใช้งาน 'หมวดหมู่อีคอมเมิร์ซ' เพื่อดูหมวดหมู่อีคอมเมิร์ซทั้งหมด"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr ""
"เวลาสำหรับการทำเครื่องหมายรถเข็นว่าละทิ้งสามารถเปลี่ยนแปลงได้ในการตั้งค่า"

#. module: website_sale
#: code:addons/website_sale/models/product_product.py:0
#, python-format
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr ""
"ค่าของจำนวนหน่ายฐาน ต้องมากกว่า 0 ใช้ 0 เพื่อซ่อนราคาต่อหน่วยสำหรับสินค้านี้"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "ไม่มีการยืนยันคำสั่งซื้อจากเว็บไซต์"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "There is no recent confirmed order."
msgstr "ไม่มีคำสั่งซื้อที่ได้รับการยืนยันล่าสุด"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "ยังไม่มีการสั่งซื้อที่ค้างชำระจากเว็บไซต์"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "There isn't any UTM tag detected in orders"
msgstr "ไม่พบแท็ก UTM ใด ๆ ในคำสั่งซื้อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "ไม่มีการรวมนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"This email template is suggested by default when you send a recovery email."
msgstr "เทมเพลตอีเมลนี้แนะนำโดยค่าเริ่มต้นเมื่อคุณส่งอีเมลกู้คืน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "นี่คือรถเข็นปัจจุบันของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "สินค้านี้ไม่มีการรวมกันที่ถูกต้อง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "สินค้านี้ไม่มีจำหน่ายแล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "สินค้านี้ไม่ได้รับการเผยแพร่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "ไม่มีรหัสโปรโมชั่นนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails Position"
msgstr "ตำแหน่งรูปภาพภาพตัวอย่าง"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "จำนวนการรับชมสินค้าทั้งหมด"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "จำนวนเข้าชมสินค้าทั้งหมด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "True"
msgstr "จริง"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr ""
"จริงเฉพาะตัวกรองผลิตภัณฑ์ที่ต้องการ product_id "
"เนื่องจากเกี่ยวข้องกับการขายไขว้"

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr "การแสดงราคา UOM สำหรับอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "UPS"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__terms_url
msgid "URL"
msgstr "URL"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "URL ของวิดีโอสำหรับแสดงสินค้าของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "USPS"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr "หน่วยวัดราคาต่อหน่วยของสินค้าอีคอมเมิร์ซ"

#. module: website_sale
#: model:product.product,uom_name:website_sale.product_product_1
#: model:product.product,uom_name:website_sale.product_product_1b
#: model:product.template,uom_name:website_sale.product_product_1_product_template
msgid "Units"
msgstr "หน่วย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "ยังไม่ได้ชำระ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
#, python-format
msgid "Unpaid Orders"
msgstr "คำสั่งซื้อที่ยังไม่ได้ชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "ไม่ได้เผยแพร่"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Untaxed Total Sold"
msgstr "ยอดขายทั้งหมดไม่รวมภาษี"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Upload a file from your local library."
msgstr "อัปโหลดไฟล์จากห้องสมุดท้องถิ่นของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Validate"
msgstr "ตรวจสอบ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "วิดีโอ URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "View Cart ("
msgstr "ดูรถเข็น ("

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "ดูสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "การมองเห็น"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "สามารถมองเห็น"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "ปรากฏบนเว็บไซต์ปัจจุบัน"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "หน้าที่เข้าชม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "สินค้าที่ได้รับการเยี่ยมชม"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "การเข้าชมสินค้าของผู้เยี่ยมชม"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "ประวัติการเข้าชมสินค้าของผู้เยี่ยมชม"

#. module: website_sale
#: model:product.product,name:website_sale.product_product_1
#: model:product.product,name:website_sale.product_product_1b
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "การรับประกัน"

#. module: website_sale
#: model:product.product,description_sale:website_sale.product_product_1
#: model:product.product,description_sale:website_sale.product_product_1b
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""
"การรับประกัน ซึ่งออกให้แก่ผู้ซื้อโดยตรงจากผู้ผลิต "
"โดยสัญญาว่าจะซ่อมแซมหรือเปลี่ยนใหม่หากจำเป็นภายในระยะเวลาที่กำหนด"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_validate.js:0
#, python-format
msgid "We are waiting for confirmation from the bank or the payment provider"
msgstr "เรากำลังรอการยืนยันจากธนาคารหรือผู้ให้บริการชำระเงิน"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_payment__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Website"
msgstr "เว็บไซต์"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_payment_acquirer_onboarding_wizard
msgid "Website Payment acquire onboarding wizard"
msgstr "ตัวช่วยเตรียมความพร้อมการชำระเงินผ่านเว็บไซต์"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "หมวดหมู่สินค้าเว็บไซต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "หมวดหมู่สาธารณะเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "ลำดับเว็บไซต์"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "เว็บไซต์ร้านค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "ตัวกรอง Snippet ของเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "URL เว็บไซต์"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "ผู้เยี่ยมชมเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "คำอธิบายเนื้อหาเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "คำสำคัญในคำอธิบายเนื้อหาเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "ชื่อข้อมูลเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "รูป Opengraph บนเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
#: model:ir.model.fields,help:website_sale.field_account_payment__website_id
msgid "Website through which this invoice was created."
msgstr "เว็บไซต์ที่ใช้สร้างใบแจ้งหนี้นี้"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed."
msgstr "เว็บไซต์ที่ทำการสั่งซื้อนี้"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "เว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_crm_team__website_ids
msgid "Websites using this Sales Team"
msgstr "เว็บไซต์ที่ใช้ทีมขายนี้"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Whiteboard"
msgstr "ไวท์บอร์ด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "รายการที่อยากได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""
"ด้วยโหมดแรก คุณสามารถกำหนดราคาได้หลายแบบในแบบฟอร์มการกำหนดค่าผลิตภัณฑ์ "
"(จากแท็บการขาย) ในอันที่สอง คุณจะตั้งราคาและกฎการคำนวณจากรายการราคา"

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid "With this action, '%s' website would not have any pricelist available."
msgstr "ด้วยการกระทำนี้ '%s' เว็บไซต์จะไม่มีรายการราคาใด ๆ"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "With three feet"
msgstr "ด้วยสามขา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>billing and shipping</b> addresses at the same time!<br/>\n"
"                                            If you want to modify your shipping address, create a"
msgstr ""
"คุณกำลังแก้ไขที่อยู่ <b>สำหรับการเรียกเก็บเงินและที่อยู่สำหรับจัดส่ง</b> ในเวลาเดียวกัน!<br/>\n"
"                                            หากคุณต้องการแก้ไขที่อยู่สำหรับจัดส่ง ให้สร้าง"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "คุณไม่มีคำสั่งซื้อใด ๆ จากเว็บไซต์"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "คุณไม่มีคำสั่งซื้อใด ๆ ที่จะออกใบแจ้งหนี้จากเว็บไซต์"

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "คุณทิ้งสินค้าไว้ในรถเข็นของคุณ!"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"คุณจะพบรถเข็นทั้งหมดที่ผู้เยี่ยมชมของคุณละทิ้งที่นี้\n"
"               หากพวกเขากรอกที่อยู่เรียบร้อยแล้ว คุณควรส่งอีเมลกู้คืนให้พวกเขา!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Your Address"
msgstr "ที่อยู่ของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Your Address\n"
"                                        <small> or </small>"
msgstr ""
"ที่อยู่ของคุณ\n"
"                                        <small> หรือ </small>"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "อีเมลของคุณ"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "ชื่อของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "Your cart is empty!"
msgstr "รถเข็นของคุณว่างเปล่า!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "รถเข็นก่อนหน้าของคุณเสร็จสมบูรณ์แล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "รหัสไปรษณีย์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "code..."
msgstr "โค้ด..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "เช่น โคมไฟ ถังขยะ"

#. module: website_sale
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
#, python-format
msgid "eCommerce"
msgstr "eCommerce"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
msgid "eCommerce Categories"
msgstr "หมวดหมู่อีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter Visibility"
msgstr "การมองเห็นตัวกรองอีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "การขายอีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_delivery
msgid "eCommerce Shipping Costs"
msgstr "ค่าขนส่งอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Shop"
msgstr "ร้านค้าอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.search_count_box
msgid "found)"
msgstr "พบ)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "hours."
msgstr "ชั่วโมง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr "หากคุณต้องการรวมข็นสินค้าก่อนหน้าของคุณเข้ากับรถเข็นสินค้าปัจจุบัน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""
"หากคุณต้องการคืนค่ารถเข็นก่อนหน้าของคุณ "
"รถเข็นปัจจุบันของคุณจะถูกแทนที่ด้วยรถเข็นก่อนหน้าของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "ในหมวดหมู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "item(s))"
msgstr "รายการ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "ที่อยู่ใหม่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "terms &amp; conditions"
msgstr "ข้อกำหนดและเงื่อนไข"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr "เพื่อปฏิบัติตามคำสั่งของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Background"
msgstr "⌙ พื้นหลัง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Mode"
msgstr "⌙ โหมด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Position"
msgstr "⌙ ตำแหน่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Text"
msgstr "⌙ ข้อความ"
