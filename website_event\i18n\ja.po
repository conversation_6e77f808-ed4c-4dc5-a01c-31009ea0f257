# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-27 13:05+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "登録数"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr "'. 右記の結果を表示しています'"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "(Ref:"
msgstr "(参照:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "(only"
msgstr "(以下のみ:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid ", .oe_country_events, .s_speaker_bio"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "<b>Drag and Drop</b> this snippet below the event title."
msgstr "イベントタイトル下にあるこのスニペットを<b>ドラッグ＆ドロップ</b> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr "<b>終了</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr "<b>開始</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<b>View all</b>"
msgstr "<b>すべてを見る</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid ""
"<em>Write here a quote from one of your attendees. It gives confidence in "
"your events.</em>"
msgstr "<em>参加者の言葉をここに書きましょう。イベントに自信を与えてくれます。</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid ""
"<font style=\"font-size: 62px;\" "
"class=\"o_default_snippet_text\">Introduction</font>"
msgstr "<font style=\"font-size: 62px;\" class=\"o_default_snippet_text\">紹介</font>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<i class=\"fa fa-ban mr-2\"/>Sold Out"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban mr-2\"/>未公開"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check mr-2\"/>Registered"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"
msgstr ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<i class=\"fa fa-flag mr-2\"/>Events:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> グーグルカレンダーに追加"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> iCal/Outlook に追加"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
#: model_terms:ir.ui.view,arch_db:website_event.s_country_events
msgid "<i class=\"fa fa-globe mr-2\"/>Upcoming Events"
msgstr "<i class=\"fa fa-globe mr-2\"/>今後のイベント予定"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid ""
"<i class=\"fa fa-long-arrow-left text-primary mr-2\"/>\n"
"                            <span>All Events</span>"
msgstr ""
"<i class=\"fa fa-long-arrow-left text-primary mr-2\"/>\n"
"                            <span>全てのイベント</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"<span class=\"badge badge-secondary text-uppercase "
"o_wevent_badge\">Speaker</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "<span class=\"navbar-brand h4 my-0 mr-auto\">Events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_days pr-1\">0</span><span "
"class=\"o_countdown_metric pr-1\">days</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"py-2 o_wevent_registration_title text-left\">Tickets</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"text-dark font-weight-bold align-middle px-2\">Qty</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                    <i class=\"fa fa-ban mr-2\"/>Sold Out\n"
"                                </span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>オンラインイベント</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span>Tickets</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"
msgstr ""
"<span>チケット</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr "<strong>空席以上のチケットを注文されました</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr "過去のイベント"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "会社概要"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Add to Calendar"
msgstr "カレンダーに追加"

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "すべての国"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr "全ての国"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr "ウェブサイト上でイベント固有のメニューを表示・管理できます。"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Apply"
msgstr "適用"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Ask questions to attendees when registering online"
msgstr "オンラインでの参加登録時に質問する"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"わずか13歳にして、ジョン・ドーはすでに顧客向けの最初のビジネス・アプリケーションを開発し始めていました。土木工学を学んだ後、TinyERPを設立。これは、後に世界で最もインストールされるオープンソースのビジネスソフトウェアであるOdooとなるOpenERPの初期段階でした。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "出席者"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "著作者"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "公開可能"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "閉じる"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
#, python-format
msgid "Community"
msgstr "コミュニティ"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr "コミュニティメニュー"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "会社"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Continue"
msgstr "次へ進む"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "表紙のプロパティ"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Create"
msgstr "作成"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Create \"%s\""
msgstr " \"%s\"を作成する"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "作成者"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "作成日"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Custom Range"
msgstr "カスタム幅"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr "日時"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "日付（新→旧）"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "日付（旧→新）"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "説明"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Discard"
msgstr "破棄"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "表示名"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr "ウェブサイトに専用メニューを表示"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr "ウェブサイトでコミュニティタブを表示"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Don't forget to click <b>save</b> when you're done."
msgstr "完了したら <b>保存</b>を忘れずにクリックして下さい。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Email"
msgstr "Eメール"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr "終了 -"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "イベント"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr "イベント コミュニティメニュー"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr "イベント日"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "イベントの場所"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr "イベントメニュー"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Event Name"
msgstr "イベント名"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "イベント登録"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr "イベント登録"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/customize_options.xml:0
#, python-format
msgid "Event Specific"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/customize_options.xml:0
#, python-format
msgid "Event Sub-menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Subtitle"
msgstr "イベント副題"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr "イベントタグカテゴリ"

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr "イベントテンプレート"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Title"
msgstr "イベントタイトル"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "イベントが見つかりません！"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "イベントの公開"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "イベントの非公開化"

#. module: website_event
#: code:addons/website_event/models/website.py:0
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#, python-format
msgid "Events"
msgstr "イベント"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Expired"
msgstr "契約期間終了済"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_register_cta
#: model:ir.model.fields,field_description:website_event.field_event_type__menu_register_cta
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Extra Register Button"
msgstr "追加登録ボタン"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event, and join the "
"conversation."
msgstr "このイベントに関する人々の意見や感想を聞いて、会話に参加しましょう。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "Follow Us"
msgstr "もしよかったらフォローお願いします"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr "以下のコンテンツは全てのイベントに表示されます。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Get the direction"
msgstr "方向性をつかむ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr "Google"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "ID"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
#, python-format
msgid "Introduction"
msgstr "入門"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr "紹介メニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr "紹介メニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr "完了"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "現在進行中か"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr "参加予定"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "公開済"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr "ジョン・ドー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: website_event
#. openerp-web
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#, python-format
msgid "Location"
msgstr "ロケーション"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr "ロケーションメニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr "ロケーションメニュー"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Looking great! Let's now <b>publish</b> this page so that it becomes "
"<b>visible</b> on your website!"
msgstr "素晴らしい！では、このページを<b>公開</b>し、ウェブサイトで<b>閲覧可能</b>にしましょう！"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_search
msgid "Main Contact"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_visitor__parent_id
msgid "Main identity"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "メニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "メニュータイプ"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "メニュー"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "N/A"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Name"
msgstr "名称"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "New Event"
msgstr "新しいイベント"

#. module: website_event
#: code:addons/website_event/models/website.py:0
#, python-format
msgid "Next Events"
msgstr "次のイベント"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr "ウェブサイトメニュー項目がまだありません！"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events found."
msgstr ""

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr "この訪問者にリンクする登録がありません"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr "次の検索に結果が見つかりませんでした:「"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "On Site"
msgstr "オンサイト"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Online"
msgstr "オンライン"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "オーガナイザ"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr "当社のトレーニング"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__parent_id
msgid "Parent"
msgstr "親"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Past Events"
msgstr "過去のイベント"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Phone <small>(Optional)</small>"
msgstr "電話番号 <small>(任意)</small>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Photos"
msgstr "写真"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "このフィールドに入力してください"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.js:0
#, python-format
msgid "Please select at least one ticket."
msgstr "少なくとも1つのチケットを選択して下さい。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Questions"
msgstr "質問"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Ref:"
msgstr "参照:"

#. module: website_event
#. openerp-web
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
#: model_terms:ir.ui.view,arch_db:website_event.layout
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Register"
msgstr "登録"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Register Button"
msgstr "登録ボタン"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr "登録メニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr "メニュー登録"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr "イベント登録"

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Registration"
msgstr "登録"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr "登録が確認されました！"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr "参加者"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations Closed"
msgstr "登録を締め切りました"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations are <b>closed</b>"
msgstr "登録を <b>締切ました</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations not yet open"
msgstr "登録をまだ受け付けていません"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr "開始前の残り時間"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr "開始前の残り時間(分)"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
msgid "Restrict publishing to this website."
msgstr "このWebサイトへの公開を制限します。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "イベントリストに戻る。"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO最適化"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "SHARE"
msgstr "共有"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales end on"
msgstr "販売終了日"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales start on"
msgstr "販売開始"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr "イベントを検索..."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all events from"
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Select Venue"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "ソ名"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr "ウェブサイトに掲載"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "完売"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "申し訳ありません。このイベントはもう利用できません。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr "開始 -"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr "本日開始"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Start → End"
msgstr "開始 → 終了"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr "開始<span/>"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr "ウェブサイトを介してドキュメントにアクセスするために完全なURLです。"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "This month"
msgstr "今月"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This shortcut will bring you right back to the event form."
msgstr "このショートカットを使うと、イベントフォームにすぐに戻ることができます。"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr "この技術メニューには、すべてのイベントサブ・メニュー項目が表示されます。"

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "This ticket is not available for sale for this event"
msgstr "このチケットはこのイベントでは販売していません。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr "チケット数"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Sales starting on"
msgstr "チケット販売開始:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets for this Event are <b>Sold Out</b>"
msgstr "このイベントのチケットは <b>完売しました</b>"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Today"
msgstr "今日"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Toggle navigation"
msgstr "ナビゲーションを切り替える"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "Unpublished"
msgstr "未公開"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#, python-format
msgid "Upcoming Events"
msgstr "今後のイベント予定"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Use the top button '<b>+ New</b>' to create an event."
msgstr ""

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr "この<b>ショートカット</b>を使ってイベントウェブページに簡単にアクセスできます。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragrah to write a short text about your events or company."
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr "URLベースのメニューでない場合に使用されます。"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Venue"
msgstr "会場"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "ビュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "現在のウェブサイトで見える状態"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "訪問者"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Want to change your event configuration? Let's go back to the event form."
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
msgid "Website"
msgstr "ウェブサイト"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr "ウェブサイトイベントメニュー"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr "ウェブサイトイベントメニュー"

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "ウェブサイトホーム"

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "ウェブサイトメニュー"

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr "ウェブサイトメニュー"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr "ウェブサイト サブメニュー"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "ウェブサイトURL"

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "ウェブサイト訪問者"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "ウェブサイトメタディスクリプション"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "ウェブサイトメタキーワード"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "ウェブサイトメタタイトル"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "ウェブサイトopengraph画像"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "イベントが開始したか"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_done
msgid "Whether event is finished"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr "現在進行中でない場合、イベントが本日開始か"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"With the Edit button, you can <b>customize</b> the web page visitors will "
"see when registering."
msgstr "編集ボタンを使用すると、訪問者が登録時に表示されるウェブページを<b>カスタマイズ</b>することができます。"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "available)"
msgstr "利用可能)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr "iCal/Outlook"
