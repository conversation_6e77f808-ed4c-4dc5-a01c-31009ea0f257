<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="event_event_view_form" model="ir.ui.view">
        <field name="name">event.event.view.form.inherit.website</field>
        <field name="model">event.event</field>
        <field name="priority" eval="5"/>
        <field name="inherit_id" ref="event.view_event_form"/>
        <field name="arch" type="xml">
            <field name="company_id" position="after">
                <field name="website_id" options="{'no_create': True}" domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]" groups="website.group_multi_website"/>
            </field>
            <div name="button_box" position="inside">
                <field name="website_url" invisible="1"/>
                <field name="is_published" widget="website_redirect_button"/>
            </div>
            <xpath expr="//div[hasclass('oe_title')]" position="after">
                <div name="event_menu_configuration" groups="base.group_no_one">
                    <label for="website_menu" string="Website Submenu"/>
                    <field name="website_menu"/>
                    <!-- hidden sub-menus, they are triggered all at once based on "website_menu" -->
                    <field name="introduction_menu" invisible="1"/>
                    <field name="location_menu" invisible="1"/>
                    <field name="register_menu" invisible="1"/>
                    <label for="menu_register_cta" string="Extra Register Button"/>
                    <field name="menu_register_cta"/>
                    <label for="community_menu" string="Community" invisible="1"/>
                    <field name="community_menu" invisible="1"/>
                </div>
            </xpath>
        </field>
    </record>

    <record id="event_event_view_list" model="ir.ui.view">
        <field name="name">event.event.view.list.inherit.website</field>
        <field name="model">event.event</field>
        <field name="inherit_id" ref="event.view_event_tree"/>
        <field name="arch" type="xml">
            <field name="company_id" position="after">
                <field name="website_id" groups="website.group_multi_website" domain="['|', ('company_id', '=', company_id), ('company_id', '=', False)]" optional="show"/>
            </field>
        </field>
    </record>

</odoo>
