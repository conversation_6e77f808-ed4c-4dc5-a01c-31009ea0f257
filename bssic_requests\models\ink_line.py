from odoo import models, fields, api, _
from odoo.exceptions import UserError


class PrinterInkLine(models.Model):
    """Printer Ink Request Line Model"""
    _name = 'bssic.printer.ink.line'
    _description = 'Printer Ink Request Line'

    request_id = fields.Many2one('bssic.ink.request', string='Request', required=True, ondelete='cascade')
    printer_number = fields.Char('رقم الطابعة', required=True)
    quantity = fields.Float('الكمية', default=1.0, required=True)
    ink_number = fields.Char('رقم الحبر', required=True)
    stock_quantity = fields.Float('Stock Quantity', default=0.0)
    approved_quantity = fields.Float('Approved Quantity', default=0.0)
    can_edit_approved_quantity = fields.Boolean(compute='_compute_can_edit_approved_quantity')
    can_edit_stock_quantity = fields.Boolean(compute='_compute_can_edit_stock_quantity')
    notes = fields.Char('Notes')

    @api.depends('request_id.state')
    def _compute_can_edit_approved_quantity(self):
        """Determine if approved quantity can be edited"""
        for line in self:
            # HR managers can edit approved quantity during HR approval
            is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
            line.can_edit_approved_quantity = (
                is_hr_manager and 
                line.request_id.state == 'hr_approval'
            )

    @api.depends('request_id.state')
    def _compute_can_edit_stock_quantity(self):
        """Determine if stock quantity can be edited"""
        for line in self:
            # Warehouse managers can edit stock quantity during warehouse approval
            is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
            line.can_edit_stock_quantity = (
                is_warehouse_manager and 
                line.request_id.state == 'warehouse_approval'
            )

    @api.constrains('quantity')
    def _check_quantity(self):
        """Validate quantity is positive"""
        for line in self:
            if line.quantity <= 0:
                raise UserError(_('Quantity must be greater than zero.'))

    @api.constrains('stock_quantity')
    def _check_stock_quantity(self):
        """Validate stock quantity is not negative"""
        for line in self:
            if line.stock_quantity < 0:
                raise UserError(_('Stock quantity cannot be negative.'))

    @api.constrains('approved_quantity')
    def _check_approved_quantity(self):
        """Validate approved quantity is not negative and not more than requested"""
        for line in self:
            if line.approved_quantity < 0:
                raise UserError(_('Approved quantity cannot be negative.'))
            if line.approved_quantity > line.quantity:
                raise UserError(_('Approved quantity cannot be more than requested quantity.'))

    def write(self, vals):
        """Override write to control field editing permissions"""
        if 'approved_quantity' in vals:
            # Check if user is HR manager and request is in HR approval state
            is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
            if not is_hr_manager:
                # Remove approved_quantity from vals if user is not HR manager
                vals.pop('approved_quantity')
            elif self.request_id.state != 'hr_approval':
                # Remove approved_quantity from vals if request is not in HR approval state
                vals.pop('approved_quantity')

        if 'stock_quantity' in vals:
            # Check if user is Warehouse manager and request is in Warehouse approval state
            is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
            if not is_warehouse_manager:
                # Remove stock_quantity from vals if user is not Warehouse manager
                vals.pop('stock_quantity')
            elif self.request_id.state != 'warehouse_approval':
                # Remove stock_quantity from vals if request is not in Warehouse approval state
                vals.pop('stock_quantity')

        return super(PrinterInkLine, self).write(vals)


class CopierInkLine(models.Model):
    """Copier Ink Request Line Model"""
    _name = 'bssic.copier.ink.line'
    _description = 'Copier Ink Request Line'

    request_id = fields.Many2one('bssic.ink.request', string='Request', required=True, ondelete='cascade')
    copier_number = fields.Char('رقم آلة التصوير', required=True)
    quantity = fields.Float('الكمية', default=1.0, required=True)
    ink_number = fields.Char('رقم الحبر', required=True)
    stock_quantity = fields.Float('Stock Quantity', default=0.0)
    approved_quantity = fields.Float('Approved Quantity', default=0.0)
    can_edit_approved_quantity = fields.Boolean(compute='_compute_can_edit_approved_quantity')
    can_edit_stock_quantity = fields.Boolean(compute='_compute_can_edit_stock_quantity')
    notes = fields.Char('Notes')

    @api.depends('request_id.state')
    def _compute_can_edit_approved_quantity(self):
        """Determine if approved quantity can be edited"""
        for line in self:
            # HR managers can edit approved quantity during HR approval
            is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
            line.can_edit_approved_quantity = (
                is_hr_manager and 
                line.request_id.state == 'hr_approval'
            )

    @api.depends('request_id.state')
    def _compute_can_edit_stock_quantity(self):
        """Determine if stock quantity can be edited"""
        for line in self:
            # Warehouse managers can edit stock quantity during warehouse approval
            is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
            line.can_edit_stock_quantity = (
                is_warehouse_manager and 
                line.request_id.state == 'warehouse_approval'
            )

    @api.constrains('quantity')
    def _check_quantity(self):
        """Validate quantity is positive"""
        for line in self:
            if line.quantity <= 0:
                raise UserError(_('Quantity must be greater than zero.'))

    @api.constrains('stock_quantity')
    def _check_stock_quantity(self):
        """Validate stock quantity is not negative"""
        for line in self:
            if line.stock_quantity < 0:
                raise UserError(_('Stock quantity cannot be negative.'))

    @api.constrains('approved_quantity')
    def _check_approved_quantity(self):
        """Validate approved quantity is not negative and not more than requested"""
        for line in self:
            if line.approved_quantity < 0:
                raise UserError(_('Approved quantity cannot be negative.'))
            if line.approved_quantity > line.quantity:
                raise UserError(_('Approved quantity cannot be more than requested quantity.'))

    def write(self, vals):
        """Override write to control field editing permissions"""
        if 'approved_quantity' in vals:
            # Check if user is HR manager and request is in HR approval state
            is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
            if not is_hr_manager:
                # Remove approved_quantity from vals if user is not HR manager
                vals.pop('approved_quantity')
            elif self.request_id.state != 'hr_approval':
                # Remove approved_quantity from vals if request is not in HR approval state
                vals.pop('approved_quantity')

        if 'stock_quantity' in vals:
            # Check if user is Warehouse manager and request is in Warehouse approval state
            is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
            if not is_warehouse_manager:
                # Remove stock_quantity from vals if user is not Warehouse manager
                vals.pop('stock_quantity')
            elif self.request_id.state != 'warehouse_approval':
                # Remove stock_quantity from vals if request is not in Warehouse approval state
                vals.pop('stock_quantity')

        return super(CopierInkLine, self).write(vals)
