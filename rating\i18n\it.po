# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rating
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_count
msgid "# Ratings"
msgstr "# Valutazioni"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__access_token
msgid "Access token to set the rating of the value"
msgstr "Token di accesso per impostare la valutazione del valore"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__message_id
msgid ""
"Associated message when posting a review. Mainly used in website addons."
msgstr ""
"Messaggio associato alla pubblicazione di una recensione. Usato "
"principalmente in componenti aggiuntivi per siti web."

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__partner_id
msgid "Author of the rating"
msgstr "Autore della valutazione"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__feedback
msgid "Comment"
msgstr "Commento"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Customer"
msgstr "Cliente"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Date"
msgstr "Data"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ko
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Dissatisfied"
msgstr "Insoddisfatto"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Document"
msgstr "Documento"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model
msgid "Document Model"
msgstr "Modello documento"

#. module: rating
#: model:ir.model,name:rating.model_mail_thread
msgid "Email Thread"
msgstr "Discussione e-mail"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__consumed
msgid "Enabled if the rating has been filled."
msgstr "Abilitata se è stata effettuata la valutazione."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Feel free to write a feedback on your experience:"
msgstr "Siete invitati a scrivere un commento sulla vostra esperienza:"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__consumed
msgid "Filled Rating"
msgstr "Valutazione effettuata"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Go back to the Homepage"
msgstr "Ritorna alla pagina iniziale"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"Nascosto agli utenti pubblici / portale, indipendentemente dalla "
"configurazione del sottotipo."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__id
msgid "ID"
msgstr "ID"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_id
msgid "Identifier of the rated object"
msgstr "Identificativo dell'oggetto valutato"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image
msgid "Image"
msgstr "Immagine"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 30 days"
msgstr "Ultimi 30 giorni"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 7 days"
msgstr "Ultimi 7 giorni"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: rating
#: model:ir.model,name:rating.model_mail_message
#: model:ir.model.fields,field_description:rating.field_rating_rating__message_id
msgid "Message"
msgstr "Messaggio"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_model_id
msgid "Model of the followed resource"
msgstr "Modello della risorsa seguita"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "My Ratings"
msgstr "Le mie valutazioni"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_name
msgid "Name"
msgstr "Nome"

#. module: rating
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__none
msgid "No Rating yet"
msgstr "Ancora nessuna valutazione"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "No rating yet"
msgstr "Ancora nessuna valutazione"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ok
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Okay"
msgstr "OK"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rated_partner_id
msgid "Owner of the rated resource"
msgstr "Proprietario della risorsa valutata"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_id
msgid "Parent Document"
msgstr "Documento principale"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model
msgid "Parent Document Model"
msgstr "Modello documento principale"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_name
msgid "Parent Document Name"
msgstr "Nome documento principale"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Parent Holder"
msgstr "Contenitore principale"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_ref
msgid "Parent Ref"
msgstr "Rif. principale"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model_id
msgid "Parent Related Document Model"
msgstr "Modello documento collegato al principale"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Percentuale di valutazioni positive"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rated Operator"
msgstr "Operatore valutato"

#. module: rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rating"
msgstr "Valutazione"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg
msgid "Rating Average"
msgstr "Media valutazioni"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Ultimo riscontro valutazione"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_image
msgid "Rating Last Image"
msgstr "Immagine ultima valutazione"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_value
msgid "Rating Last Value"
msgstr "Valore ultima valutazione"

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr "Mixin valutazione"

#. module: rating
#: model:ir.model,name:rating.model_rating_parent_mixin
msgid "Rating Parent Mixin"
msgstr "Mixin principale valutazione"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Valutazione soddisfazione"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_value
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_value
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating
msgid "Rating Value"
msgstr "Valore valutazione"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
msgid "Rating Value (/5)"
msgstr "Livello valutazione (/5)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_count
msgid "Rating count"
msgstr "Numero valutazioni"

#. module: rating
#: model:ir.model.constraint,message:rating.constraint_rating_rating_rating_range
msgid "Rating should be between 0 and 5"
msgstr "La valutazione deve essere tra 0 e 5"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rating
msgid "Rating value: 0=Unhappy, 5=Happy"
msgstr "Valore valutazione: 0=Non soddisfatto, 5=Soddisfatto"

#. module: rating
#: model:ir.actions.act_window,name:rating.rating_rating_view
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_ids
#: model:ir.ui.menu,name:rating.rating_rating_menu_technical
#: model_terms:ir.ui.view,arch_db:rating.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_tree
msgid "Ratings"
msgstr "Valutazioni"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__rating_last_feedback
#: model:ir.model.fields,help:rating.field_rating_rating__feedback
msgid "Reason of the rating"
msgstr "Motivo della valutazione"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model_id
msgid "Related Document Model"
msgstr "Modello documento correlato"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_ids
msgid "Related ratings"
msgstr "Valutazioni correlate"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Resource"
msgstr "Risorsa"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__resource_ref
msgid "Resource Ref"
msgstr "Rif. risorsa"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_name
msgid "Resource name"
msgstr "Nome risorsa"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__top
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Satisfied"
msgstr "Soddisfatto"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__access_token
msgid "Security Token"
msgstr "Token di sicurezza"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Send Feedback"
msgstr "Invia commento"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_date
msgid "Submitted on"
msgstr "Inviata il"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Thank you for rating our services!"
msgstr "Grazie per aver valutato i nostri servizi!"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thank you, we appreciate your feedback!"
msgstr "Grazie, apprezziamo la sua opinione!"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_name
msgid "The name of the rated resource."
msgstr "Il nome della risorsa valutata."

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "There is no rating for this object at the moment."
msgstr "Ancora nessuna valutazione per questo oggetto."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Today"
msgstr "Oggi"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__is_internal
msgid "Visible Internally Only"
msgstr "Visibile solo internamente"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "by"
msgstr "di"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "for"
msgstr "per"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "on"
msgstr "il"
