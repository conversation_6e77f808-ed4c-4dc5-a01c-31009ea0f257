<?xml version="1.0" encoding="UTF-8"?>

<templates>

    <t t-name="foldable">
        <span t-att-class="'o_stock_reports_foldable ' + lineId + ' o_stock_reports_caret_icon'"><i class="fa fa-fw fa-caret-down" role="img" aria-label="Fold" title="Fold"></i></span>
    </t>

    <t t-name="unfoldable">
        <span t-att-class="'o_stock_reports_unfoldable ' + lineId + ' o_stock_reports_caret_icon'"><i class="fa fa-fw fa-caret-right" role="img" aria-label="Unfold" title="Unfold"></i></span>
    </t>

    <t t-name="report_mrp_line">
        <t t-set="trclass" t-value="'o_stock_reports_default_style'"/>
        <t t-if="l.model == 'stock.move.line'"><t t-set="trclass" t-value="'o_stock_reports_level0'"/></t>
        <t t-set="space_td" t-value="'margin-left: '+ l.level + 'px;'"/>
        <t t-set="domainClass" t-value="'o_stock_reports_domain_line_0'"/>
        <t t-if="l.unfoldable == false">
            <t t-set="spanclass" t-value="'o_stock_reports_nofoldable'" />
            <t t-set="domainClass" t-value="'o_stock_reports_domain_line_1'"/>
        </t>

        <tr t-att-data-unfold="l.unfoldable" t-att-data-parent_id="l.parent_id" t-att-data-id="l.id"  t-att-data-model_id="l.model_id" t-att-data-model="l.model" t-att-class="trclass" t-att-data-lot_name="l.lot_name" t-att-data-lot_id="l.lot_id">
            <t t-if="l.unfoldable == true"><t t-set="tdclass" t-value="'o_stock_reports_unfoldable'" /></t>
            <t t-set="column" t-value="0" />
            <t t-foreach="l.columns" t-as="c">
                <t t-set="column" t-value="column + 1" />
                <td style="white-space: nowrap;" t-att-data-id="l.id" t-att-data-model="l.model" t-att-data-model_id="l.model_id" t-att-class="tdclass" t-att-data-level="l.level" t-att-data-lot_name="l.lot_name">
                    <t t-if="column == 1">
                        <span t-att-style="space_td" t-att-class="domainClass"></span>
                        <t t-if="l.unfoldable">
                            <span class="o_stock_reports_unfoldable o_stock_reports_caret_icon"><i class="fa fa-fw fa-caret-right" role="img" aria-label="Unfold" title="Unfold"></i></span>
                        </t>
                    </t>
                    <t t-if="l.reference == c">
                        <span t-if="c" t-att-class="spanclass">
                            <a t-att-data-active-id="l.res_id" t-att-data-res-model="l.res_model" class="o_stock_reports_web_action" href="#"><t t-esc="c"/></a>
                        </span>
                    </t><t t-elif="l.lot_name == c and l.lot_name != false">
                        <span>
                            <a class="o_stock_report_lot_action" href="#"><t t-esc="c"/></a>
                        </span>
                    </t>
                    <t t-if="l.reference != c and l.lot_name != c">
                        <t t-if="typeof c == 'string' || typeof c == 'number'">
                            <t t-esc="c"/>
                        </t>
                        <t t-if="typeof c != 'string' &amp; typeof c != 'number'"><span t-att-style="c[1]">
                            <t t-esc="c[0]"/>
                        </span></t>
                    </t>
                </td>
            </t>
        </tr>
    </t>

</templates>
