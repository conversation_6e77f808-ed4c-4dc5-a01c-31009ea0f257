# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid "Cashdrawer"
msgstr "Kassa"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""
"Controleer in de printerconfiguratie de instelling 'ID Apparaat'. Deze moet "
"zijn ingesteld op:"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Verbinding met de printer mislukt"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "Epson Printer IP"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "Epson ticketprinter IP adres"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s"
msgstr ""
"Als je zich op een beveiligde server (HTTPS) bevindt, zorg er dan voor dat "
"je het certificaat handmatig hebt geaccepteerd door toegang te krijgen tot "
"%s"

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Lokaal IP adres van een Epson ticket printer."

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "No paper was detected by the printer"
msgstr "De printer heeft geen papier gedetecteerd"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer has enough paper and is ready to print."
msgstr ""
"Controleer of de printer voldoende papier heeft en klaar is om af te "
"drukken."

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr "Controleer of de printer nog steeds verbonden is."

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Kassa instellingen"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Printing failed"
msgstr "Afdrukken mislukt"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""
"De Epson ticket printer wordt gebruikt in plaats van de ticket printer "
"gekoppeld aan de IoT box."

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The following error code was given by the printer:"
msgstr "De printer gaf de volgende foutcode:"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr "De printer werd met succes bereikt, maar kon niet afdrukken."

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "To find more details on the error reason, please search online for:"
msgstr ""
"Voor meer details over de reden van de fout kan je online zoeken naar:"
