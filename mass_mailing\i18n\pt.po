# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# 425fe09b3064b9f906f637fff94056ae_a00ea56 <0fa3588fa89906bfcb3a354600956e0e_308047>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <marcelo.per<PERSON>@arxi.pt>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-01 17:39+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Manuela Silva <<EMAIL>>, 2023\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "$18"
msgstr "$18"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "$20"
msgstr "$20"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "% of recipients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "% of recipients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "% receive one of the"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "%Click (Total)"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (cópia)"

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_contact_to_list.py:0
#, python-format
msgid "%s Mailing Contacts have been added. "
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-compass fa-2x text-o-color-4\" role=\"img\" aria-label=\"Choose\" title=\"Choose\"/>\n"
"                                        &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-credit-card fa-2x text-o-color-4\" role=\"img\" aria-label=\"Order\" title=\"Order\"/>\n"
"                                        &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-smile-o fa-2x text-o-color-4\" role=\"img\" aria-label=\"Enjoy\" title=\"Enjoy\"/>\n"
"                                        &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "&amp;nbsp;&amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ", and"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "-20%"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "100%"
msgstr "100%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "21 Jul"
msgstr "21 de julho"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "25%"
msgstr "25%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "400px"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "50%"
msgstr "50%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "75%"
msgstr "75%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "800px"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert
msgid ""
"<b>Explain the benefits you offer</b>\n"
"            <br/>Don't write about products or services here, write about solutions."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "<b>GET $20 OFF</b>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_title
msgid "<font style=\"font-size: 42px;\">Your Title</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"<font style=\"font-size: 48px;\" class=\"o_default_snippet_text\">A punchy "
"Headline</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_subscription_view_form
msgid ""
"<i class=\"fa fa-ban text-danger\" role=\"img\" title=\"This email is "
"blacklisted for mass mailings\" aria-label=\"Blacklisted\" "
"attrs=\"{'invisible': [('is_blacklisted', '=', False)]}\" "
"groups=\"base.group_user\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-bar-chart\"/> Compare Version"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-copy\"/> Create an Alternative"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<i class=\"fa fa-envelope\"/> <span name=\"ab_test_manual\" attrs=\"{'invisible': [('ab_testing_winner_selection', '!=', 'manual')]}\">\n"
"                                                    Send this version to remaining recipients\n"
"                                                </span> <span name=\"ab_test_auto\" attrs=\"{'invisible': [('ab_testing_winner_selection', '=', 'manual')]}\">\n"
"                                                    Send Winner Now\n"
"                                                </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-envelope\"/> Send this as winner"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"Warning\" "
"title=\"Warning\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh mr-1\"/> Replace Icon"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid ""
"<i>Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services.</i>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<small class=\"o_default_snippet_text\">user / month (billed "
"annually)</small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<small class=\"oe_edit_only text-muted mb-2\" style=\"font-size:74%\" attrs=\"{'invisible': ['|', ('reply_to_mode', '=', 'update'), ('mailing_model_name', 'in', ['mailing.contact', 'res.partner', 'mailing.list'])],}\">\n"
"                                                    To track replies, this address must belong to this database.\n"
"                                                </small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid ""
"<small>\n"
"                <strong>Michael Fletcher</strong><br/>\n"
"                <small>Community Manager</small>\n"
"            </small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "<small>Step 1:</small>"
msgstr "<small>Passo 1:</small>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "<small>Step 2:</small>"
msgstr "<small>Passo 2:</small>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "<small>Step 3:</small>"
msgstr "<small>Passo 3:</small>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<small>user / month (billed annually)</small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span attrs=\"{'invisible': "
"[('mailing_model_name','!=','mailing.list')]}\">Mailing Contact</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-calendar-check-o mr-2 small my-auto\" aria-label=\"Sent "
"date\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
msgid ""
"<span class=\"fa fa-copyright\" role=\"img\" aria-label=\"Copyright\" "
"title=\"Copyright\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-half mr-2 small my-auto\" aria-"
"label=\"Scheduled date\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-o mr-2 small my-auto\" aria-label=\"Scheduled date\"/>\n"
"                                            <span class=\"align-self-baseline\">Next Batch</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span class=\"o_default_snippet_text\" style=\"font-size:18px; font-weight: "
"500;\">DEFAULT</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span class=\"o_default_snippet_text\" style=\"font-size:18px; font-weight: "
"500;\">PRO</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "<span class=\"o_stat_text\">Blacklist</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "<span class=\"o_stat_text\">Bounce</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "<span class=\"o_stat_text\">Opt-out</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "<span class=\"o_stat_value\">%</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span class=\"s_number display-4 o_default_snippet_text\">12</span><br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span class=\"s_number display-4 o_default_snippet_text\">45</span><br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span class=\"s_number display-4 o_default_snippet_text\">8</span><br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Blacklist</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Bounce</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Mailings</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Opt-out</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"canceled_text\">emails have been canceled and will not be "
"sent.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "<span name=\"failed_text\">email(s) not sent.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"failed_text\">emails could not be sent.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"next_departure_text\">This mailing is scheduled for </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "<span name=\"scheduled_text\">email(s) scheduled for </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"scheduled_text\">emails are in queue and will be sent "
"soon.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"sent\">emails have been sent.</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid ""
"<span style=\"line-height: 30px;\"><small>CODE: </small></span><strong "
"class=\"o_code h3 oe_unremovable\">45A9E77DGW8455</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "<span widget=\"statinfo\">Open Recipient</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "<span>%</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Contacts</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "<span>Select a template</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Valid Email Recipients</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<strong class=\"o_default_snippet_text\">24/7 Support</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<strong class=\"o_default_snippet_text\">Advanced</strong>\n"
"                                features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<strong class=\"o_default_snippet_text\">Fully customizable</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<strong class=\"o_default_snippet_text\">Total</strong>\n"
"                                management"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>24/7 Support</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>Advanced</strong> features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>Fully customizable</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>Total</strong> management"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A campaign should be set when A/B test is enabled"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "A color block"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr ""

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_contact_list_rel_unique_contact_list
msgid ""
"A mailing contact cannot subscribe to the same mailing list multiple times."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "A sample of"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
msgid "A short description"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "A short description of this great feature."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr ""
"Uma pequena explicação desta funcionalidade <br/>fantástica, em palavras "
"inequívocas."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "A unique value"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "A/B Test"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_mailings_count
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_mailings_count
msgid "A/B Test Mailings #"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_ab_testing_open_winner_mailing
msgid "A/B Test Winner"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A/B Test: %s"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_completed
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_completed
msgid "A/B Testing Campaign Finished"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_description
msgid "A/B Testing Description"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid "A/B Testing percentage"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#, python-format
msgid "A/B Tests"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "A/B Tests to review"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A/B test option has not been enabled"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "ALL DAY"
msgstr "TODO O DIA"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction
msgid "Action Needed"
msgstr "Ação Necessária"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__active
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__active
msgid "Active"
msgstr "Ativo"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_ids
msgid "Activities"
msgstr "Atividades"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Marcador de Exceções de Atividade"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_state
msgid "Activity State"
msgstr "Estado da Atividade"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícone de Tipo de Atividade"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
msgid "Add"
msgstr "Adicionar"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_to_list
msgid "Add Contacts to Mailing List"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_contact_to_list_action
msgid "Add Selected Contacts to a Mailing List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid "Add a caption to enhance the meaning of this image."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Add a great slogan."
msgstr "Adicionar um slogan fantástico."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
msgid "Add and Send Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Add to List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Advanced"
msgstr "Avançado"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Bottom"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Middle"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Top"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Alignment"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
msgid "All Rights Reserved"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid "Allow A/B Testing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Allow recipients to blacklist themselves"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid ""
"Allow the recipient to manage himself his state in the blacklist via the "
"unsubscription page."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Allow the recipient to manage himself his state in the blacklist via the "
"unsubscription page.                                 If the option is "
"active, the 'Blacklist Me' button is hidden on the unsubscription page."
"                                   The 'come Back' button will always be "
"visible in any case to allow leads and partners to re-subscribe."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Image Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Amazing pages"
msgstr "Páginas extraordinárias"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "An error occurred. Please try again later or contact us."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "An error occurred. Your changes have not been saved, try again later."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "Another color block"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Another feature"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_tag_line
msgid "Apps That Help You Grow Your Business"
msgstr "Aplicações que ajudam a crescer o seu negócio"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_sub
msgid "Apps That Help You Grow Your Business!"
msgstr "Aplicações que ajudam a crescer o seu negócio!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Archive"
msgstr "Arquivar"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__archive_src_lists
msgid "Archive source mailing lists"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Archived"
msgstr "Arquivados"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid ""
"At least one of the mailing list you are trying to archive is used in an "
"ongoing mailing campaign."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Attach a file"
msgstr "Anexar um ficheiro"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_attachment_count
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_attachment_count
msgid "Attachment Count"
msgstr "Número de Anexos"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__attachment_ids
msgid "Attachments"
msgstr "Anexos"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Auto"
msgstr "Automático"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Background Color"
msgstr "Cor de fundo"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "Basic features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "Basic management"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/mass_mailing_snippets.js:0
#, python-format
msgid "Be aware that this option may not work on many mail clients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Beautiful snippets"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__is_blacklisted
msgid "Blacklist"
msgstr "Lista negra"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Blacklist (%s)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "Blacklist Me"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid "Blacklist Option when Unsubscribing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Blacklisted"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mail_blacklist_mm_menu
msgid "Blacklisted Email Addresses"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_arch
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Body"
msgstr "Corpo"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Body Width"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_html
msgid "Body converted to be sent by mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Books"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Border"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__message_bounce
msgid "Bounce"
msgstr "Email Rejeitado"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Bounce (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__bounced
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__bounce
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Bounced"
msgstr "Emails Rejeitados"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Bounced (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Business Benefits on %(expected)i %(mailing_type)s Sent"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "Button Label"
msgstr "Legenda do Botão"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "By using the <b>Breadcrumb</b>, you can navigate back to the overview."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__calendar_date
msgid "Calendar Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Campaign"
msgstr "Campanha"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_view_mass_mailing_stages
msgid "Campaign Stages"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_tag_menu
msgid "Campaign Tags"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_utm_campaigns
#: model:ir.ui.menu,name:mass_mailing.menu_email_campaigns
msgid "Campaigns"
msgstr "Campanhas"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid ""
"Campaigns are the perfect tool to track results across multiple mailings."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Cancel"
msgstr "Cancelar"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__canceled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__canceled
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__cancel
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Canceled"
msgstr "Cancelado"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing_test__email_to
msgid "Carriage-return-separated list of email addresses."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__preview
msgid ""
"Catchy preview sentence that encourages recipients to open this email.\n"
"In most inboxes, this is displayed next to the subject.\n"
"Keep it empty if you prefer the first characters of your email content to appear instead."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Center"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Change Icons"
msgstr "Alterar Ícones"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Chat"
msgstr "Conversar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our books"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our clothes"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our furniture"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Check the email address and click send."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_sub
msgid "Check this out!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "Choose"
msgstr "Escolher"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Choose this <b>theme</b>."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "Choose your mailing subscriptions"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Click on this paragraph to edit it."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicked
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__clicked
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Clicked"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Clicked (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Clicked On"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Clicks"
msgstr "Cliques"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Clothes"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Color"
msgstr "Cor"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__color
msgid "Color Index"
msgstr "Índice de Cor"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Colors"
msgstr "Cores"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
msgid "Column Title"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid "Column title"
msgstr "Título da coluna"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Columns"
msgstr "Colunas"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "Come Back"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__company_name
msgid "Company Name"
msgstr "Nome da Empresa"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Company name"
msgstr "Nome da empresa"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_configuration
msgid "Configuration"
msgstr "Configuração"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Configure Email Server"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Congratulations, I love your first mailing. :)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_partner
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__contact_id
msgid "Contact"
msgstr "Contacto"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "Contact List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "Contact Name"
msgstr "Nome do Contacto"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Contact us"
msgstr "Contacte-nos"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__contact_ids
msgid "Contacts"
msgstr "Contactos"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Copyright &amp;copy;"
msgstr "Direitos de Autor &amp;copy;"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact_subscription__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Contador do número de emails rejeitados para este contacto"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__country_id
msgid "Country"
msgstr "País"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_cover
msgid "Cover image"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified_footer
msgid "Create"
msgstr "Criar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Create a"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Create a Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.open_create_mass_mailing_list
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid "Create a Mailing List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid "Create a mailing campaign"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid "Create a mailing contact"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid "Create a new mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Create an Alternative Version"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_date
msgid "Created on"
msgstr "Criado em"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Creation Date"
msgstr "Data de Criação"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
msgid "Creation Period"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Custom"
msgstr "Personalizado"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid ""
"Cyber-threats continue to increase.\n"
"                        <br/>The discussion will examine how to develop new norms and integrate them into EU"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "Cybersecurity"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "DEFAULT"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Danger"
msgstr "Perigo"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dashed"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Date"
msgstr "Data"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__calendar_date
msgid "Date at which the mailing was or will be sent."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid ""
"Date that will be used to know when to determine and send the winner mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
msgid "Dedicated Server"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default"
msgstr "Predefinição"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default Reversed"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__null_value
msgid "Default Value"
msgstr "Valor Predefinido"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Delete Blocks"
msgstr "Eliminar Blocos"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__delivered
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__delivered
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Delivered"
msgstr "Entregue"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Delivered (%)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delivered to"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "Demo Signature"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Design a striking email, define recipients and track its results."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__dest_list_id
msgid "Destination Mailing List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Discard"
msgstr "Descartar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Discover"
msgstr "Descobrir"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Discover all the features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Display Inline"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__display_name
msgid "Display Name"
msgstr "Nome"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__res_id
msgid "Document ID"
msgstr "ID do Documento"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__model
msgid "Document model"
msgstr "Modelo de Documento"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_domain
msgid "Domain"
msgstr "Domínio"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Don't forget to send your prefered version"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Don't worry, the mailing contact we created is an internal user."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dotted"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Double"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__draft
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__draft
msgid "Draft"
msgstr "Rascunho"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Dropdown menu"
msgstr "Menu dropdown"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Duplicate"
msgstr "Duplicar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_dup
msgid "Duplicated Email"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Dynamic Placeholder Generator"
msgstr "Gereador dinâmico de Conteúdo"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "ENDOFSUMMER20"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Edit Styles"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__email
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__mailing_type__mail
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_type__mail
msgid "Email"
msgstr "Email"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Email Blacklisted"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Email Content"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/res_users.py:0
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
#, python-format
msgid "Email Marketing"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_thread
msgid "Email Thread"
msgstr "Email Thread"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistente de composição de e-mail"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Emails"
msgstr "Emails"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_trace_ids
msgid "Emails Statistics"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Engagement on %(expected)i %(mailing_type)s Sent"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "Enjoy!"
msgstr "Desfrute!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "Enjoy,"
msgstr "Desfrute,"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__error
msgid "Error"
msgstr "Erro"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Event heading"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__error
msgid "Exception"
msgstr "Exceção"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Blacklisted Emails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Opt Out"
msgstr "Excluir Opt Out"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__expected
msgid "Expected"
msgstr "Esperados"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Extended Filters..."
msgstr "Filtros Avançados..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Facebook"
msgstr "Facebook"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__failed
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Failed"
msgstr "Falhou"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__failure_type
msgid "Failure type"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature One"
msgstr "Funcionalidade Um"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Three"
msgstr "Funcionalidade Três"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Two"
msgstr "Funcionalidade Dois"

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Feedback from %(email)s: %(feedback)s"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__model_object_field
msgid "Field"
msgstr "Campo"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Expressão espaço reservado final, a cópia colada no campo do template "
"desejado."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "First Feature"
msgstr "Primeira Funcionalidade"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "First feature"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "First list of Features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Fit content"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_follower_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_partner_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Parceiros)"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ícone do Font awesome ex. fa-tasks"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footers"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                    to keep his hands full by participating in the development of the software,\n"
"                                    marketing, and customer experience strategies."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__email_from
msgid "From"
msgstr "De"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Full"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Fullscreen"
msgstr "Fullscreen"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Furniture"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
msgid ""
"Get your inside sales (CRM) fully integrated with online sales (eCommerce), "
"in-store sales (Point of Sale) and marketplaces like eBay and Amazon."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Great Value"
msgstr "Grande Valor"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Group By"
msgstr "Agrupar por"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Group By..."
msgstr "Agrupar Por..."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__has_message
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__has_message
msgid "Has Message"
msgstr "Há Mensagem"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Headers"
msgstr "Cabeçalhos"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Height"
msgstr "Altura"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "Here's your coupon code - but hurry! Ends 9/28"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__opened_ratio
msgid "Highest Open Rate"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__replied_ratio
msgid "Highest Reply Rate"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__id
msgid "ID"
msgstr "ID"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid ""
"ID of the related mail_mail. This field is an integer field because the "
"related mail_mail can be deleted separately from its statistics. However the"
" ID is needed for several action and controllers."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_icon
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Icon"
msgstr "Ícone"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ícone para indicar uma exceção na atividade."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_unread
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_unread
msgid "If checked, new messages require your attention."
msgstr "Se selecionado, há novas mensagens que requerem a sua atenção."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid ""
"If checked, recipients will be mailed only once for the whole campaign. This"
" lets you send different mailings to randomly selected recipients and test "
"the effectiveness of the mailings, without causing duplicate messages."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se estiver marcado, algumas mensagens têm um erro de entrega."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact_subscription__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Ignored"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Image Text Image"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Images"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__in_queue
msgid "In Queue"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Info"
msgstr "Informação"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Inner Content"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Instagram"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__is_body_empty
msgid "Is Body Empty"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_is_follower
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_is_follower
msgid "Is Follower"
msgstr "É Seguidor"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__is_public
msgid "Is Public"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Join us and make your company a better place."
msgstr "Junte-se a nós e torne a sua empresa em um lugar melhor."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__kpi_mail_required
msgid "KPI mail required"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__keep_archives
msgid "Keep Archives"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "LOGIN"
msgstr "INICIAR SESSÃO"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__lang
msgid "Language"
msgstr "Idioma"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Large"
msgstr "Grande"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Last Feature"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report____last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Last State Update"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Left"
msgstr "Esquerda"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Let's try the Email Marketing app."
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker
#: model:ir.ui.menu,name:mass_mailing.link_tracker_menu_mass_mailing
msgid "Link Tracker"
msgstr "Rastrear Ligações"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker_click
msgid "Link Tracker Click"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_ids
msgid "Links click"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__mailing_type__mail
msgid "Mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Mail Body"
msgstr "Conteúdo do e-mail"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Mail Debug"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Mail ID"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid "Mail ID (tech)"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_ab_testing_ir_actions_server
#: model:ir.cron,cron_name:mass_mailing.ir_cron_mass_mailing_ab_testing
#: model:ir.cron,name:mass_mailing.ir_cron_mass_mailing_ab_testing
msgid "Mail Marketing: A/B Testing"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_queue_ir_actions_server
#: model:ir.cron,cron_name:mass_mailing.ir_cron_mass_mailing_queue
#: model:ir.cron,name:mass_mailing.ir_cron_mass_mailing_queue
msgid "Mail Marketing: Process queue"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_mail_server_id
msgid "Mail Server"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_available
msgid "Mail Server Available"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mailing_trace_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_graph
msgid "Mail Statistics"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Mail Traces"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mass_mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__campaign
msgid "Mailing Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid "Mailing Campaigns"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact
msgid "Mailing Contact"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_ir_model__is_mailing_enabled
msgid "Mailing Enabled"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list
#: model:ir.model.fields,field_description:mass_mailing.field_account_invoice_send__mailing_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mailing_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__list_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__mailing_list_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__name
msgid "Mailing List"
msgstr "Lista de Endereços"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_contacts
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_contacts
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Mailing List Contacts"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_subscription_view_form
msgid "Mailing List Subscription"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_subscription_view_tree
msgid "Mailing List Subscriptions"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_lists
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__src_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__contact_list_ids
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_mailing_list_menu
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_lists
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Mailing Lists"
msgstr "Listas de Endereços"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace
msgid "Mailing Statistics"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "Mailing Subscriptions"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mail_mass_mailing_test
msgid "Mailing Test"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_action
#: model:ir.ui.menu,name:mass_mailing.menu_email_statistics
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
msgid "Mailing Traces"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type_description
msgid "Mailing Type Description"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Mailing addresses incorrect: %s"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"business contact directory."
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_create_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_action_mail
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Mailings"
msgstr "Mailings"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailings that are assigned to me"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_main_attachment_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_main_attachment_id
msgid "Main Attachment"
msgstr "Anexo Principal"

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_campaign
msgid "Manage Mass Mailing Campaigns"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Manage campaigns and A/B test your mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__manual
msgid "Manual"
msgstr "Manual"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Marketing"
msgstr "Marketing"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Marketing Content"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__name
msgid "Mass Mail"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_account_invoice_send__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__mass_mailing_id
#: model:ir.ui.menu,name:mass_mailing.mailing_mailing_menu_technical
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Mass Mailing"
msgstr "Emails em Massa"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_report_action_mail
msgid "Mass Mailing Analysis"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_account_invoice_send__campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Mass Mailing Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_account_invoice_send__mass_mailing_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_name
msgid "Mass Mailing Name"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace_report
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Mass Mailing Statistics"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_trace_report_action_mail
msgid ""
"Mass Mailing Statistics allows you to check different mailing related information\n"
"    like number of bounced mails, opened mails, replied mails. You can sort out\n"
"    your analysis by different groups to get accurate grained analysis."
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_subscription
msgid "Mass Mailing Subscription Information"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_ids
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_ids
msgid "Mass Mailings"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Media heading"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__medium_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Medium"
msgstr "Médio"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_list_merge_action
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge"
msgstr "Fundir"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list_merge
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge Mass Mailing List"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__merge_options
msgid "Merge Option"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__new
msgid "Merge into a new mailing list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__existing
msgid "Merge into an existing mailing list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error
msgid "Message Delivery error"
msgstr "Erro de Envio de Mensagem"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__message_id
msgid "Message-ID"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_missing
msgid "Missing email address"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_model
msgid "Models"
msgstr "Modelos"

#. module: mass_mailing
#: model:mailing.mailing,sms_subject:mass_mailing.mass_mail_1
msgid "Monthly Newsletter"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "More"
msgstr "Mais"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "More Details"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "More Info"
msgstr "Mais informação"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Mosaic"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_tag_line
msgid "My Account"
msgstr "A Minha Conta"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Prazo das Minhas Atividades"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_text_social
msgid "My Company"
msgstr "A Minha Empresa"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "My Mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__name
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Name"
msgstr "Nome"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Name / Email"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__new_list_name
msgid "New Mailing List Name"
msgstr ""

#. module: mass_mailing
#: model:utm.campaign,name:mass_mailing.mass_mail_campaign_1
msgid "Newsletter"
msgstr "Boletim Informativo"

#. module: mass_mailing
#: model:mailing.mailing,name:mass_mailing.mass_mail_1
#: model:utm.source,name:mass_mailing.utm_source_0
msgid "Newsletter 1"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nova Atividade de Calendário"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Prazo da Próxima Atividade"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_summary
msgid "Next Activity Summary"
msgstr "Resumo da Próxima Atividade"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo da Atividade Seguinte "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "No customization"
msgstr "Sem personalização"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mail_mail_statistics_mailing
msgid "No data yet!"
msgstr "Sem dados!"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No mailing campaign has been found"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"No mailing for this A/B testing campaign has been sent yet! Send one first "
"and try again later."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid ""
"No need to import mailing lists, you can send mailings to contacts saved in "
"other Odoo apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "No support"
msgstr "Sem suporte"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "No template picked yet."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "None"
msgstr "Nenhum(a)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email_normalized
msgid "Normalized Email"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__email
msgid "Normalized email address"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Ações"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_blacklisted
msgid "Number of Blacklisted"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicks_ratio
msgid "Number of Clicks"
msgstr "Número de Cliques"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count
msgid "Number of Contacts"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_email
msgid "Number of Emails"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_count
msgid "Number of Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_count
msgid "Number of Mass Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_opt_out
msgid "Number of Opted-out"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Number of Recipients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid "Number of bounced email."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of errors"
msgstr "Número de erros"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensagens que requerem uma ação"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com um erro de entrega"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_unread_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensagens não lidas"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "OFF YOUR NEXT ORDER!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "ON YOUR NEXT ORDER!"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "OPENED (%i)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Odoo"
msgstr "Odoo"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
msgid "Omnichannel sales"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Once the best version is identified, we will send the best one to the "
"remaining recipients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Open Date"
msgstr "Data Abertura"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Open Recipient"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__opened
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__open
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Opened"
msgstr "Aberto"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Opened (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__open_datetime
msgid "Opened On"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__opened_ratio
msgid "Opened Ratio"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__opt_out
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__opt_out
msgid "Opt Out"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__opt_out
msgid ""
"Opt out flag for a specific mailing list.This field should not be used in a "
"view without a unique and active mailing list context."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Opt-out (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_optout
msgid "Opted Out"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Opted-out"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Valor opcional para usar se o campo do destino estiver vazio"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "Order"
msgstr "Ordem"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "Our References"
msgstr "As Nossas Referências"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__outgoing
msgid "Outgoing"
msgstr "Entrega"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mail
msgid "Outgoing Mails"
msgstr "Mensagens a Enviar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "PRO"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_blacklisted
msgid "Percentage of Blacklisted"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_bounce
msgid "Percentage of Bouncing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_opt_out
msgid "Percentage of Opted-out"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid ""
"Percentage of the contacts that will be mailed. Recipients will be chosen "
"randomly."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Pick the <b>email subject</b>."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__copyvalue
msgid "Placeholder Expression"
msgstr "Expressão de Conteúdo"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Post heading"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to
msgid "Preferred Reply-To Address"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__preview
msgid "Preview"
msgstr "Pré-visualizar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Preview Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Primary"
msgstr "Primário"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating
msgid "Quality"
msgstr "Qualidade"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "RECEIVED (%i)"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "REPLIED (%i)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "Read More"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid "Read More..."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Ready for take-off!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Received"
msgstr "Recebido"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__received_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__received_ratio
msgid "Received Ratio"
msgstr "Proporção Recebida"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
msgid "Recipient"
msgstr "Destinatário"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Recipient Address"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__update
msgid "Recipient Followers"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__email_to
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Recipients"
msgstr "Destinatários"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_name
msgid "Recipients Model Name"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_real
msgid "Recipients Real Model"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "Redeem Discount!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "Register"
msgstr "Registar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Regular"
msgstr "Normal"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__render_model
msgid "Rendering Model"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__replied
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__reply
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Replied"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Replied (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__reply_datetime
msgid "Replied On"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__replied_ratio
msgid "Replied Ratio"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Reply Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to
msgid "Reply To"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to_mode
msgid "Reply-To Mode"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_report
msgid "Reporting"
msgstr "Relatórios"

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Requested blacklisting via unsubscribe link."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Requested blacklisting via unsubscription page."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Requested de-blacklisting via unsubscription page."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__user_id
msgid "Responsible"
msgstr "Responsável"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_user_id
msgid "Responsible User"
msgstr "Utilizador Responsável"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Restore"
msgstr "Restaurar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Retry"
msgstr "Tentar Novamente"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Right"
msgstr "Direita"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Round Corners"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erro de Envio de SMS"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Sample Icons"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_test
msgid "Sample Mail Wizard"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_type
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Schedule"
msgstr "Planear"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__scheduled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Scheduled"
msgstr "Agendado"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled_date
msgid "Scheduled Date"
msgstr "Data Agendada"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Scheduled On"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Scheduled Period"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__next_departure
msgid "Scheduled date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__schedule_date
msgid "Scheduled for"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.next_departure.value}"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.schedule_date.value}"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Score"
msgstr "Pontuação"

#. module: mass_mailing
#: code:addons/mass_mailing/models/ir_model.py:0
#, python-format
msgid ""
"Searching Mailing Enabled models supports only direct search using '='' or "
"'!='."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Second Feature"
msgstr "Segunda Funcionalidade"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Second feature"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Second list of Features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Secondary"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists..."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists:"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Selecione o campo de destino a partir do modelo do documento relacionado.\n"
"Se for um campo de relação será capaz de selecionar um campo de destino no destino do relacionamento."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Selection to determine the winner mailing that will be sent."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send"
msgstr "Enviar"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid "Send Final On"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__email_from
msgid "Send From"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a Sample Mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a sample mailing for testing purpose to the address below."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Send new Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__now
msgid "Send now"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Send on"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__sending
msgid "Sending"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__sent
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__done
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__sent
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__done
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Sent"
msgstr "Enviado"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent By"
msgstr "Enviado por"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent_date
msgid "Sent Date"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Sent Mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__sent_datetime
msgid "Sent On"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent Period"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Sent on #{record.sent_date.value}"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mass_mailing_configuration
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_global_settings
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Settings"
msgstr "Definições"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Since the date and time for this test has not been scheduled, don't forget "
"to manually send your preferred version."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Size"
msgstr "Tamanho"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Small"
msgstr "Pequeno"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Solid"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__source_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__source_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Source"
msgstr "Origem"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__name
msgid "Source Name"
msgstr "Nome da Origem"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__new
msgid "Specified Email Address"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Start by creating your first <b>Mailing</b>."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Start editing your mailing to design something awesome."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "State"
msgstr "Estado"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_trace_ids
msgid "Statistics"
msgstr "Estatísticas"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__state
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_status
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__state
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Status"
msgstr "Estado"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estados baseados nas atividades\n"
"Vencida: Ultrapassada a data planeada\n"
"Hoje: Data da atividade é a de hoje\n"
"Planeada: Atividades futuras."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Stores last click datetime in case of multi clicks."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Stretch to Equal Height"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sub_model_object_field
msgid "Sub-field"
msgstr "SubCampo"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sub_object
msgid "Sub-model"
msgstr "Submodelo"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__subject
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Subject"
msgstr "Assunto"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__subject
msgid "Subject of your Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__subscription_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__subscription_ids
msgid "Subscription Information"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Success"
msgstr "Sucesso"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__tag_ids
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "Tags"
msgstr "Etiquetas"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Take Future Schedule Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__message_id
msgid "Technical field for the email Message-ID (RFC 2392)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__is_body_empty
msgid "Technical field used to determine if the mail body is empty"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_available
msgid ""
"Technical field used to know if the user has activated the outgoing mail "
"server option in the settings"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Template"
msgstr "Modelo"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Test"
msgstr "Teste"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Test Mailing"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Test mailing could not be sent to %s:<br>%s"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Test mailing successfully sent to %s"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Test this mailing by sending a copy to yourself."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__test
msgid "Tested"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Text Highlight"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Text Image Text"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "Thank you for joining us!"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "Thank you! Your feedback has been sent successfully!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid "That way, Odoo evolves much faster than any other solution."
msgstr ""

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_mailing_percentage_valid
msgid "The A/B Testing Percentage needs to be between 0 and 100%"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "The actual"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact_subscription__opt_out
msgid "The contact has chosen not to receive mails anymore from this list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_list__is_public
msgid ""
"The mailing list can be accessible by recipient in the unsubscription page "
"to allows him to update his subscription preferences."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of developers and\n"
"                                business experts to build hundreds of apps in just a few years."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of developers and\n"
"                    business experts to build hundreds of apps in just a few years."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of "
"developers and business experts to build hundreds of apps in just a few "
"years."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "The recipient <strong>subscribed to %s</strong> mailing list(s)"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "The recipient <strong>unsubscribed from %s</strong> mailing list(s)"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/utm.py:0
#, python-format
msgid ""
"The total percentage for an A/B testing campaign should be less than 100%"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"The winner has already been sent. Use <b>Compare Version</b> to get an "
"overview of this A/B testing campaign."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Then on"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "There are no recipients selected."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Third Feature"
msgstr "Terceira Funcionalidade"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "This"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"This email from can not be used with this mail server.\n"
"Your emails might be marked as spam on the mail clients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__source_id
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__source_id
msgid ""
"This is the link source, e.g. Search Engine, another domain, or name of "
"email list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid ""
"This is useful if your marketing campaigns are composed of several emails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"This is useful if your marketing campaigns are composed of several emails."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"This tool is advised if your marketing campaign is composed of several "
"emails."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"This will send the email to all recipients. Do you still want to proceed ?"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to_mode
msgid ""
"Thread: replies go to target document. Email: replies are routed to a given "
"email."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__title_id
msgid "Title"
msgstr "Título"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To send the winner mailing the campaign should not have been completed."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To send the winner mailing the same campaign should be used by the mailings"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__total
msgid "Total"
msgstr "Total"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_total_pc
msgid "Total A/B test percentage"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Total Bounces"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Tracking"
msgstr "Rastrear"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Try different variations in the campaign to compare their"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Twitter"
msgstr "Twitter"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_type
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__mailing_type
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Type"
msgstr "Tipo"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de atividade de exceção registada."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_campaign
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__campaign_id
msgid "UTM Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__medium_id
msgid "UTM Medium: delivery method (email, sms, ...)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__unknown
msgid "Unknown error"
msgstr "Erro desconhecido"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_unread
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_unread
msgid "Unread Messages"
msgstr "Mensagens Não Lidas"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_unread_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contador de Mensagens Não Lidas"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_basic_template
msgid "Unsubscribe"
msgstr "Cancelar Subscrição"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "Unsubscribed"
msgstr "Subscrição cancelada"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__unsubscription_date
msgid "Unsubscription Date"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Unsupported mass mailing model %s"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "Update my subscriptions"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid ""
"Usability improvements made on Odoo will automatically apply to all\n"
"                                of our fully integrated apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"Usability improvements made on Odoo will automatically apply to all\n"
"                    of our fully integrated apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "Use This Promo Code BEFORE 1st of August"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Use a dedicated server for mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Use a specific mail server in priority. Otherwise Odoo relies on the first "
"outgoing mail server available (based on their sequencing) as it does for "
"normal mails."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "Use now"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Useful options"
msgstr ""

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_user
msgid "User"
msgstr "Utilizador"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_users
msgid "Users"
msgstr "Utilizadores"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Valid Email Recipients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vert. Alignment"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vertical Alignment"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_view
msgid "View Online"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Warning"
msgstr "Aviso"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning Message"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning message displayed in the mailing form view"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid ""
"We are continuing to grow and we miss seeing you be a part of it! We've "
"increased store hours and have lot's of new brands available. To welcome you"
" back please accept this 20% discount on you next purchase by clicking the "
"button."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "We are in good company."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid ""
"We want to take this opportunity to welcome you to our ever-growing "
"community!<br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid ""
"We would appreciate if you provide feedback about why you updated<br/>your "
"subscriptions"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website Messages"
msgstr "Mensagens do Website"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website communication history"
msgstr "Histórico de comunicação do Website"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Quando um campo de relacionamento é selecionado como primeiro campo, este "
"campo permite que selecione o campo de destino dentro do modelo de documento"
" de destino (sub-modelo)."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Quando um campo de relacionamento é selecionado como primeiro campo, este "
"campo mostra o modelo de documento e a relação."

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_schedule_date_action
msgid "When do you want to send your mailing?"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_ir_model__is_mailing_enabled
msgid ""
"Whether this model supports marketing mailing capabilities (notably email "
"and SMS)."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Width"
msgstr "Largura"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Winner Selection"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid ""
"With strong technical foundations, Odoo's framework is unique.\n"
"                                It provides top notch usability that scales across all apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"With strong technical foundations, Odoo's framework is unique.\n"
"                    It provides top notch usability that scales across all apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"With strong technical foundations, Odoo's framework is unique. It provides "
"<strong>top notch usability that scales across all apps</strong>."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid ""
"Write one paragraph describing your product,\n"
"                                    services or a specific feature. To be successful\n"
"                                    your content needs to be useful to your readers."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr ""
"Escreva o que o cliente gostaria de saber <br/>não o que você gostaria de "
"mostrar."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "You are not authorized to do this!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "You are not subscribed to any of our mailing list."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_list_merge.py:0
#, python-format
msgid "You can only apply this action from Mailing Lists."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid ""
"You don't need to import your mailing lists, you can easily\n"
"                send emails<br> to any contact saved in other Odoo apps."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "You have been <strong>successfully unsubscribed from %s</strong>."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "You have been <strong>successfully unsubscribed</strong>."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid ""
"You have been successfully <strong>added to our blacklist</strong>. You will"
" not be contacted anymore by our services."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid ""
"You have been successfully <strong>removed from our blacklist</strong>. You "
"are now able to be contacted by our services."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "You have been successfully <strong>unsubscribed</strong>!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid ""
"You were still subscribed to those newsletters. You will not receive any "
"news from them anymore:"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_social
msgid "Your Logo"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "Your changes have been saved."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid ""
"Your platform is ready for work. It will help you reduce the costs of "
"digital signage, attract new customers and increase sales."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "and save $20 on your next order!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. Check it out before it's too late!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "e.g. Consumer Newsletter"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "e.g. John Smith"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. New Sale on all T-shirts"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid ""
"<EMAIL>\n"
"<EMAIL>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "free website"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "from the same campaign."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "having the"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"is the winner of the A/B testing campaign and has been sent to all remaining"
" recipients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "on"
msgstr "no(a)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "other versions"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_schedule_date
msgid "schedule a mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "the"
msgstr "o/a"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "to the remaining"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will be sent"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will be sent to the remaining recipients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will receive this"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "with"
msgstr "com"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Active"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Inactive"
msgstr ""
