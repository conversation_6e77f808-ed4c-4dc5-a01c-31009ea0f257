.accounts-dashboard-wrap svg.ct-chart-bar,
.accounts-dashboard-wrap svg.ct-chart-line {
    overflow: visible;
}

.accounts-dashboard-wrap .ct-label.ct-vertical.ct-start {
    color: black !important;
}

.accounts-dashboard-wrap .ct-label.ct-label.ct-horizontal.ct-end {
    position: relative;
    justify-content: flex-end;
    text-align: right;
    transform-origin: 100%;
    color: black;
    transform: translate(-100%) rotate(-45deg);
    white-space: nowrap;
}




.accounts-dashboard-wrap .ct-series-e .ct-slice-pie {
    fill: #7b2138 !important;
}

.accounts-dashboard-wrap .ct-series-a .ct-bar,
.accounts-dashboard-wrap .ct-series-a .ct-line,
.accounts-dashboard-wrap .ct-series-a .ct-point,
.accounts-dashboard-wrap .ct-series-a .ct-slice-donut,
.accounts-dashboard-wrap .ct-series-a .ct-slice-pie {
    stroke: #009f9d !important;
}


.accounts-dashboard-wrap h4 {
    padding-left: 20px !important;
    padding-top: 10px !important;
}




.accounts-dashboard-wrap .users-list>li img {
    border-radius: 50%;
    height: auto;
    max-width: 100%;
}

.accounts-dashboard-wrap .badge-danger {
    width: 50px;
    height: 20px;
    color: #fff;
    background-color: #dc3545;
}

.accounts-dashboard-wrap .card-header>.card-tools {
    float: right;
    margin-right: -.625rem;
}


.accounts-dashboard-wrap .card {
    box-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2);
    margin-bottom: 1rem;
}

.accounts-dashboard-wrap .card-title {
    float: left;
    font-size: 1.1rem;
    font-weight: 400;
    margin: 0;
    text-transform: uppercase;
}

.accounts-dashboard-wrap .col,
.accounts-dashboard-wrap .col-1,
.accounts-dashboard-wrap .col-10,
.accounts-dashboard-wrap .col-11,
.accounts-dashboard-wrap .col-12,
.accounts-dashboard-wrap .col-2,
.accounts-dashboard-wrap .col-3,
.accounts-dashboard-wrap .col-4,
.accounts-dashboard-wrap .col-5,
.accounts-dashboard-wrap .col-6,
.accounts-dashboard-wrap .col-7,
.accounts-dashboard-wrap .col-8,
.accounts-dashboard-wrap .col-9,
.accounts-dashboard-wrap .col-auto,
.accounts-dashboard-wrap .col-lg,
.accounts-dashboard-wrap .col-lg-1,
.accounts-dashboard-wrap .col-lg-10,
.accounts-dashboard-wrap .col-lg-11,
.accounts-dashboard-wrap .col-lg-12,
.accounts-dashboard-wrap .col-lg-2,
.accounts-dashboard-wrap .col-lg-3,
.accounts-dashboard-wrap .col-lg-4,
.accounts-dashboard-wrap .col-lg-5,
.accounts-dashboard-wrap .col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-auto,
.col-md,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-auto,
.col-sm,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-auto,
.col-xl,
.col-xl-1,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-auto {
    position: relative;
    width: 100%;
    padding-right: 7.5px;
    padding-left: 7.5px;
}


.accounts-dashboard-wrap .card-header {
    background-color:
        transparent;
    border-bottom: 1px solid rgba(0, 0, 0, .125);
    padding: .75rem 1.25rem;
    position: relative;
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
}


.accounts-dashboard-wrap .fa:hover {
    -ms-transform: scale(1.5);
    /* IE 9 */
    -webkit-transform: scale(1.5);
    /* Safari 3-8 */
    transform: scale(1.5);
}

.accounts-dashboard-wrap .card-header>.card-tools {
    float: right;
    margin-right: -.625rem;
}

.accounts-dashboard-wrap .right {
    float: left;
}

.accounts-dashboard-wrap .tooltip:hover .tooltiptext {
    visibility: visible;
}


.accounts-dashboard-wrap .col-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
}

.accounts-dashboard-wrap .fa-cog {
    content: "\f013"
}

.accounts-dashboard-wrap .fa,
.fas {
    font-weight: 900;
}

.accounts-dashboard-wrap .fa,
.accounts-dashboard-wrap .fab,
.accounts-dashboard-wrap .fad,
.accounts-dashboard-wrap .fal,
.accounts-dashboard-wrap .far,
.accounts-dashboard-wrap .fas {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

.accounts-dashboard-wrap .info-box .info-box-icon {

    border-radius: .25rem;
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    font-size: 1.875rem;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
    width: 70px;
}

.accounts-dashboard-wrap .info-box {


    box-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2);
    border-radius: .25rem;
    background: #fff;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 1rem;
    min-height: 80px;
    padding: .5rem;
    position: relative;
}

.accounts-dashboard-wrap .o_datepicker .o_datepicker_input {
    width: 100%;
    cursor: pointer;
}

.accounts-dashboard-wrap #overdue {
    width: 100%;
    cursor: pointer;
}

.accounts-dashboard-wrap .o_input {
    border: 1px solid #cfcfcf;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;
}


.accounts-dashboard-wrap .in_graph {
    padding-left: 90px;
    height: auto;
    padding-bottom: 65px;
    text-align: center !important;
}


.accounts-dashboard-wrap .oh_dashboards {
    padding-top: 15px;
    background-color: #f8faff !important;
}

.accounts-dashboard-wrap .container-fluid.o_in_dashboard {
    padding: 0px !important;
}

.accounts-dashboard-wrap .o_action_manager {
    overflow-y: scroll !important;
    max-width: 100%;
}

// new tile

body {
    background-color: #ececec;
}

.accounts-dashboard-wrap .container {
    margin: 50px 0 0 100px;
}

.accounts-dashboard-wrap .o_dashboards {
    color: #2a2a2a;
    background-color: #f2f2f2 !important;
}

.accounts-dashboard-wrap .dash-header {

    margin: 15px 0px 12px 0 !important;
    display: block;
    padding: 7px 25px 7px 0;
    color: #0e1319;
    font-size: 2rem;
    font-weight: 400;
    background-color:
        rgba(255, 255, 255, 0.9) !important;
    color: #212529;
    padding: 1.5rem;
    border-radius: 3px;
    box-shadow: 0 0px 10px 0px rgba(0, 0, 0, 0.05) !important;
    display: flex;
    justify-content: space-between;
    align-items: center;

}

.accounts-dashboard-wrap .dashboard-h1 {

    display: block;
    padding: 7px 25px 7px 0;
    color: #0e1319;
    font-size: 2rem;
    font-weight: 400;
    color:

        #212529;
    float: left;
    margin-bottom: 0;

}

.accounts-dashboard-wrap .card {
    position: relative !important;
    border-top: 0 !important;
    margin-bottom: 30px !important;
    width: 100% !important;
    background-color: #ffffff !important;
    border-radius: 0.25rem !important;
    padding: 0px !important;
    -webkit-transition: .5s !important;
    transition: .5s !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
    box-shadow: 0 0px 10px 0px rgba(0, 0, 0, 0.05) !important;
    border-radius: 0.25rem;
}

.accounts-dashboard-wrap .card-header {
    border: 0;
    padding: 0;
}

.accounts-dashboard-wrap .card-header>.card-tools {
    float: right;
    margin-right: 0.375rem;
    margin-top: 5px;
    margin-bottom: 10px;
}

.accounts-dashboard-wrap .card-header i.fa {
    font-size: 1.3rem;
    display: inline-block;
    padding: 0 0px;
    margin: 0 0px;
    color: #57769c;
    opacity: .8;
    -webkit-transition: 0.3s linear;
    transition: 0.3s linear;
}

.accounts-dashboard-wrap .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
    color: #7891af;
}

.accounts-dashboard-wrap .account-details {
    display: flex;
    justify-content: space-evenly;
}

.main-title {
    color: #a3a3a3;
    display: block;
    margin-bottom: 5px;
    font-size: 20px;
    font-weight: 400;
}

.accounts-dashboard-wrap .main-title {
    display: block;
    margin-bottom: 5px;
    font-size: 13px;
    font-weight: 600;
    color: #fff !important;
    text-transform: uppercase;
    padding: 1rem;
    border-radius: 5px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.accounts-dashboard-wrap .card-body {
    background-color: rgba(255, 255, 255, 0.9) !important;
    color: #212529;
    padding-top: 0;
}

.accounts-dashboard-wrap .tile.wide.invoice {
    margin-bottom: 27px;
    -webkit-box-shadow: 1px 5px 24px 0 rgba(68, 102, 242, 0.05);
    box-shadow: 1px 5px 24px 0 rgba(68, 102, 242, 0);
    background-color: #ffffff;
    border-radius: 5px;
    position: relative;
    width: 100%;
    padding: 0rem 0rem;
    border: 1px solid rgba(0, 0, 0, 0.07);
}

.accounts-dashboard-wrap .box-1 .main-title {
    background: #67b7dc;
    color: #fff;
}

.accounts-dashboard-wrap .box-2 .main-title {
    background: #6794dc !important;
    color: #fff;
}

.accounts-dashboard-wrap .box-3 .main-title {
    background: #8067dc;
    color: #fff;
}

.accounts-dashboard-wrap .box-4 .main-title {
    background: #c767dc;
    color: #fff;
}

.accounts-dashboard-wrap .count {
    margin-bottom: 1rem;
}

.accounts-dashboard-wrap span#total_invoices_ span,
.accounts-dashboard-wrap span#total_invoices_last span,
.accounts-dashboard-wrap span#total_incomes_ span,
.accounts-dashboard-wrap span#total_incomes_last span,
.accounts-dashboard-wrap span#total_expenses_ span,
.accounts-dashboard-wrap span#total_expense_last span,
.accounts-dashboard-wrap span#unreconciled_items_ span,
.accounts-dashboard-wrap span#unreconciled_items_last span,
.accounts-dashboard-wrap span#unreconciled_counts_last_year span,
.accounts-dashboard-wrap span#unreconciled_counts_this_year span,
.accounts-dashboard-wrap span#total_expense_last_year span,
.accounts-dashboard-wrap span#total_expense_this_year span,
.accounts-dashboard-wrap span#total_incomes_last_year span,
.accounts-dashboard-wrap span#total_incomes_this_year span,
.accounts-dashboard-wrap span#total_invoices_last_year span,
.accounts-dashboard-wrap span#total_invoices_this_year span,
.accounts-dashboard-wrap span#net_profit_current_months span,
.accounts-dashboard-wrap span#net_profit_current_year span {
    padding-right: 8px;
    font-size: 16px;
    font-weight: 600;
}

.accounts-dashboard-wrap span#total_invoices_,
.accounts-dashboard-wrap span#total_invoices_last,
.accounts-dashboard-wrap span#total_incomes_,
.accounts-dashboard-wrap span#total_incomes_last,
.accounts-dashboard-wrap span#total_expenses_,
.accounts-dashboard-wrap span#total_expense_last,
.accounts-dashboard-wrap span#unreconciled_items_,
.accounts-dashboard-wrap span#unreconciled_items_last,
.accounts-dashboard-wrap span#unreconciled_counts_last_year,
.accounts-dashboard-wrap span#unreconciled_counts_this_year,
.accounts-dashboard-wrap span#total_expense_last_year,
.accounts-dashboard-wrap span#total_expense_this_year,
.accounts-dashboard-wrap span#total_incomes_last_year,
.accounts-dashboard-wrap span#total_incomes_this_year,
.accounts-dashboard-wrap span#total_invoices_last_year,
.accounts-dashboard-wrap span#total_invoices_this_year,
.accounts-dashboard-wrap span#net_profit_current_months,
.accounts-dashboard-wrap span#net_profit_current_year {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: column;
    color: #455e7b !important;
}

.accounts-dashboard-wrap .main-title~div {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding: 1rem;
    background: #fff;
}

.accounts-dashboard-wrap .card-header {
    color: #0e1319 !important;
    display: block !important;
    padding: 1.5rem 1.5rem !important;
    position: relative !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07) !important;
    border-top-left-radius: 0.25rem !important;
    border-top-right-radius: 0.25rem !important;
}

.accounts-dashboard-wrap .card-header i.fa {
    font-size: 1rem;
    display: inline-block;
    padding: 0 0px;
    margin: 0 0px;
    color: #57769c;
    opacity: .8;
    -webkit-transition: 0.3s linear;
    transition: 0.3s linear;
}

.accounts-dashboard-wrap .card-header>.card-tools {
    float: right;
    margin-right: 0;
    margin-top: 0px !important;
    margin-bottom: 0;
}

.accounts-dashboard-wrap .card-tools .btn {
    padding: 2px 10px;
    margin: 0;
    line-height: normal !important;
}

.accounts-dashboard-wrap .ct-series-a .ct-bar,
.accounts-dashboard-wrap .ct-series-a .ct-line,
.accounts-dashboard-wrap .ct-series-a .ct-point,
.accounts-dashboard-wrap .ct-series-a .ct-slice-donut,
.accounts-dashboard-wrap .ct-series-a .ct-slice-pie {
    stroke: rgb(132, 60, 247) !important;
}

.accounts-dashboard-wrap canvas#salesChart,
.accounts-dashboard-wrap canvas#exChart {
    display: none;
}

.accounts-dashboard-wrap ul#overdues li,
.accounts-dashboard-wrap ul#latebills li {
    list-style: none;
    padding-top: 6px;
    padding-bottom: 6px;
    font-size: 13px;
    color: #455e7b !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07) !important;
}

.accounts-dashboard-wrap ul#overdues,
.accounts-dashboard-wrap ul#latebills {
    padding: 0;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.accounts-dashboard-wrap ul#overdues li a,
.accounts-dashboard-wrap ul#latebills li a {
    color: #455e7b !important;
}

.accounts-dashboard-wrap .badge-danger {
    width: auto;
    height: 20px;
    color: #fff;
    background-color: #843cf7 !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

.accounts-dashboard-wrap .ct-label {
    fill: rgba(0, 0, 0, .4);
    color: rgba(0, 0, 0, .4);
    font-size: 1.1rem !important;
    line-height: 1;
    font-weight: 600;
}

.accounts-dashboard-wrap .ct-label {
    fill: rgb(255, 255, 255) !important;
    color: rgb(255, 255, 255) !important;
}

.accounts-dashboard-wrap .chart {
    .ct-legend {
        position: relative;
        z-index: 10;

        li {
            position: relative;
            padding-left: 23px;
            margin-bottom: 3px;
        }

        li:before {
            width: 12px;
            height: 12px;
            position: absolute;
            left: 0;
            content: '';
            border: 3px solid transparent;
            border-radius: 2px;
        }

        li.inactive:before {
            background: transparent;
        }

        &.ct-legend-inside {
            position: absolute;
            top: 0;
            right: 0;
        }

        //     @for $i from 0 to length($ct-series) {
        //         .ct-series-#{$i}:before {
        //             background-color: nth($ct-series, $i + 1);
        //             border-color: nth($ct-series, $i + 1);
        //         }
        //     }
    }
}

.accounts-dashboard-wrap #chartdiv {
    width: 100%;
    height: 400px;
}

.accounts-dashboard-wrap #chartdiv_ex {
    width: 100%;
    height: 500px;
}

.accounts-dashboard-wrap #barChart {
    display: block;
    width: 595px;
    height: 396px;
    // pointer-events: none;

}

.accounts-dashboard-wrap .canvas-con {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 365px;
    position: relative;
}

/*p {
  position: relative;
  left: 194px;
  margin-top: 64px;
}*/

.accounts-dashboard-wrap .canvas-con-inner {
    height: 100%;
}

.accounts-dashboard-wrap .canvas-con-inner,
.legend-con {
    display: inline-block;
}

.accounts-dashboard-wrap .legend-con {
    font-family: Roboto;
    display: inline-block;

    ul {
        list-style: none;
    }

    li {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        span {
            display: inline-block;
        }

        span.chart-legend {
            width: 25px;
            height: 25px;
            margin-right: 10px;
        }
    }
}

html,
body {
    margin: 0;
}

.accounts-dashboard-wrap #canvas {
    height: 277px !important;
    width: 100% !important;
}

.accounts-dashboard-wrap #net_profit_this_year .title {
    float: left;
}

.accounts-dashboard-wrap #net_profit_this_year {
    display: flex;
    justify-content: center;
    align-content: center;
    padding: .3rem;
    background: #843cf7;
    color: #fff;
    border-radius: 10px;
    width: auto !important;
    font-weight: 600;
    margin-bottom: 2rem;
    margin-top: 1rem;
    display: none !important;
}

.accounts-dashboard-wrap #net_profit_last_year .title {
    float: left;
}

.accounts-dashboard-wrap #net_profit_last_year {
    display: flex;
    justify-content: center;
    align-content: center;
    padding: .3rem;
    background:
        #843cf7;
    color:
        #fff;
    border-radius: 10px;
    width: auto !important;
    font-weight: 600;
    margin-bottom: 2rem;
    margin-top: 1rem;
}

.accounts-dashboard-wrap #net_profit_last_month .title {
    float: left;
}

.accounts-dashboard-wrap #net_profit_last_month {
    display: flex;
    justify-content: center;
    align-content: center;
    padding: .3rem;
    background:
        #843cf7;
    color:
        #fff;
    border-radius: 10px;
    width: auto !important;
    font-weight: 600;
    margin-bottom: 2rem;
    margin-top: 1rem;
}


.accounts-dashboard-wrap #net_profit_this_months .title {
    float: left;
}

.accounts-dashboard-wrap #net_profit_this_months {
    display: flex;
    justify-content: center;
    align-content: center;
    padding: .3rem;
    background:
        #843cf7;
    color:
        #fff;
    border-radius: 10px;
    width: auto !important;
    font-weight: 600;
    margin-bottom: 2rem;
    margin-top: 1rem;
}



.accounts-dashboard-wrap #col-graph .card {
    height: 366px;
}

.accounts-dashboard-wrap #top_10_customers {
    padding: 0;
}

.accounts-dashboard-wrap #top_10_customers li {
    list-style: none;
    padding-top: 6px;
    padding-bottom: 6px;
    font-size: 13px;
    color: #455e7b !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07) !important;
    padding-left: 2rem;
    display: flex;
    justify-content: space-between;
    padding-right: 2rem;
}

.accounts-dashboard-wrap #top_10_customers li a {
    color:
        #455e7b !important;
}

.accounts-dashboard-wrap #top_10_customers_this_month {
    padding: 0;
}

.accounts-dashboard-wrap #top_10_customers_this_month li {
    list-style: none;
    padding-top: 6px;
    padding-bottom: 6px;
    font-size: 13px;
    color: #455e7b !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07) !important;
    padding-left: 2rem;
    display: flex;
    justify-content: space-between;
    padding-right: 2rem;
}

.accounts-dashboard-wrap #top_10_customers_this_month li a {
    color:
        #455e7b !important;
}

.accounts-dashboard-wrap #top_10_customers_last_month {
    padding: 0;
}

.accounts-dashboard-wrap #top_10_customers_last_month li {
    list-style: none;
    padding-top: 6px;
    padding-bottom: 6px;
    font-size: 13px;
    color: #455e7b !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07) !important;
    padding-left: 2rem;
    display: flex;
    justify-content: space-between;
    padding-right: 2rem;
}

.accounts-dashboard-wrap #top_10_customers_last_month li a {
    color:
        #455e7b !important;
}

.accounts-dashboard-wrap #current_bank_balance {
    padding: 0;
}

.accounts-dashboard-wrap #current_bank_balance li {

    list-style: none;
    padding-top: 6px;
    padding-bottom: 6px;
    font-size: 13px;
    color: #455e7b !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07) !important;
    padding-left: 2rem;
    display: flex;
    justify-content: space-between;
    padding-right: 2rem;

}

.accounts-dashboard-wrap #current_bank_balance li a {
    color:
        #455e7b !important;
}

.accounts-dashboard-wrap #current_cash_balance {
    padding: 0;
}

.accounts-dashboard-wrap #current_cash_balance li {
    list-style: none;
    padding-top: 6px;
    padding-bottom: 6px;
    font-size: 13px;
    color: #455e7b !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07) !important;
    padding-left: 2rem;
    display: flex;
    justify-content: space-between;
    padding-right: 2rem;
}

.accounts-dashboard-wrap #current_cash_balance li a {
    color: #455e7b !important;

}


.accounts-dashboard-wrap .custom-h1 {
    font-size: 1em;
    margin-bottom: 0rem;
}

.accounts-dashboard-wrap .custom-h3 {
    font-size: 1em;
    margin: 0;
}

// Progress Bars
.accounts-dashboard-wrap progress,
.accounts-dashboard-wrap progress[role] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    background-size: auto;
    height: 20px;
    width: 100%;
    background-color: #8067dc;
}

// The unordered list
.accounts-dashboard-wrap .skill-list {
    list-style: none;
    margin: 0;
    padding: 1em;
}

// The list item
.accounts-dashboard-wrap .skill {
    margin-bottom: 1em;
    position: relative;

    h3 {
        color: #000;
        left: 1em;
        line-height: 1;
        position: absolute;
        top: 1em;
    }

    ::-webkit-progress-value {
        -webkit-animation: bar-fill 2s;
        width: 0px;
    }
}

// Style the bar colors
.accounts-dashboard-wrap .skill-1::-webkit-progress-value {
    background: #c767dc;
}

.accounts-dashboard-wrap .skill-1::-moz-progress-bar {
    background: #c767dc;
}

// Animation Keyframes
@-webkit-keyframes bar-fill {
    0% {
        width: 0;
    }
}

@keyframes bar-fill {
    0% {
        width: 0;
    }
}

.accounts-dashboard-wrap #total_supplier_invoice {
    color: #696969;
}

.accounts-dashboard-wrap #total_customer_invoice_names {
    color: #696969;
}

.accounts-dashboard-wrap #total_customer_invoice {
    color: #696969;
}

.accounts-dashboard-wrap #total_invoice_difference,
.accounts-dashboard-wrap #total_supplier_difference {
    color: #4ecdc4;
}

progress {
    border: 0;
    border-radius: 20px;
}

progress::-webkit-progress-bar {
    border: 0;
    border-radius: 20px;
}

progress::-webkit-progress-value {
    border: 0;
    border-radius: 20px;
}

progress::-moz-progress-bar {
    border: 0;
    border-radius: 20px;
}

.accounts-dashboard-wrap #total_customer_invoice_paid .logo,
.accounts-dashboard-wrap #total_customer_invoice .logo,
.accounts-dashboard-wrap #total_supplier_invoice_paid .logo,
.accounts-dashboard-wrap #total_supplier_invoice .logo,
.accounts-dashboard-wrap #total_customer_invoice_paid_current_year .logo,
.accounts-dashboard-wrap #total_customer_invoice_current_year .logo,
.accounts-dashboard-wrap #total_supplier_invoice_paid_current_year .logo,
.accounts-dashboard-wrap #total_supplier_invoice_current_year .logo,
.accounts-dashboard-wrap #total_customer_invoice_paid_current_month .logo,
.accounts-dashboard-wrap #total_customer_invoice_current_month .logo,
.accounts-dashboard-wrap #total_supplier_invoice_paid_current_month .logo,
.accounts-dashboard-wrap #total_supplier_invoice_current_month .logo {

    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    justify-content: left;
    flex-direction: column-reverse;
    color: #455e7b !important;

}

.accounts-dashboard-wrap #total_customer_invoice_paid .logo span:nth-child(2),
.accounts-dashboard-wrap #total_customer_invoice .logo span:nth-child(2),
.accounts-dashboard-wrap #total_supplier_invoice_paid .logo span:nth-child(2),
.accounts-dashboard-wrap #total_supplier_invoice .logo span:nth-child(2),
.accounts-dashboard-wrap #total_customer_invoice_paid_current_year .logo span:nth-child(2),
.accounts-dashboard-wrap #total_customer_invoice_current_year .logo span:nth-child(2),
.accounts-dashboard-wrap #total_supplier_invoice_paid_current_year .logo span:nth-child(2),
.accounts-dashboard-wrap #total_supplier_invoice_current_year .logo span:nth-child(2),
.accounts-dashboard-wrap #total_customer_invoice_paid_current_month .logo span:nth-child(2),
.accounts-dashboard-wrap #total_customer_invoice_current_month .logo span:nth-child(2),
.accounts-dashboard-wrap #total_supplier_invoice_paid_current_month .logo span:nth-child(2),
.accounts-dashboard-wrap #total_supplier_invoice_current_month .logo span:nth-child(2) {

    font-weight: 600;

}

.accounts-dashboard-wrap .switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.accounts-dashboard-wrap .switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.accounts-dashboard-wrap .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.accounts-dashboard-wrap .slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

.accounts-dashboard-wrap input:checked+.slider {
    background-color: #2196F3;
}

.accounts-dashboard-wrap input:focus+.slider {
    box-shadow: 0 0 1px #2196F3;
}

.accounts-dashboard-wrap input:checked+.slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

/* Rounded sliders */
.accounts-dashboard-wrap .slider.round {
    border-radius: 34px;
}

.accounts-dashboard-wrap .slider.round:before {
    border-radius: 50%;
}

.accounts-dashboard-wrap .btn-primary {

    color: #FFFFFF;
    background-color: #7C7BAD;
    border-color: #7C7BAD;

}

.accounts-dashboard-wrap .toggle-on.btn {

    padding-right: 18px !important;
    right: 50%;


}

.accounts-dashboard-wrap .toggle.btn.btn-default.off {

    border: 1px solid #aaa;

    background: #fff;
    font-weight: 600 !important;

}

.accounts-dashboard-wrap .toggle-off.btn {

    padding-left: 9px !important;

}

.accounts-dashboard-wrap .toggle {

    width: 160px !important;
    height: auto !important;

}

html .o_web_client>.o_action_manager {
    overflow: auto !important;
}