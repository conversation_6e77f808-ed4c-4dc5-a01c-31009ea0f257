<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="TicketScreen" owl="1">
        <div class="ticket-screen screen" t-att-class="{ oe_hidden: !props.isShown }">
            <div class="screen-full-width">
                <div class="rightpane pane-border">
                    <div class="controls">
                        <t t-if="!env.isMobile || !state.showSearchBar">
                            <div class="buttons">
                                <button class="discard" t-on-click="trigger('close-screen')">
                                    <span class="search-icon">
                                        <i class="fa fa-angle-double-left"/>
                                    </span>
                                    <t t-if="!env.isMobile">
                                        Back
                                    </t>
                                </button>
                                <button t-if="shouldShowNewOrderButton()" class="highlight" t-on-click="trigger('create-new-order')">New Order</button>
                            </div>
                        </t>
                        <t t-if="env.isMobile">
                            <t t-if="state.showSearchBar">
                                <button class="arrow-left" t-on-click="state.showSearchBar = !state.showSearchBar;">
                                    <span class="search-icon">
                                        <i class="fa fa-angle-double-left"/>
                                    </span>
                                </button>
                            </t>
                            <t t-else="">
                                <button class="search" t-on-click="state.showSearchBar = !state.showSearchBar;">
                                    <span class="search-icon">
                                        <i class="fa fa-search"/>
                                    </span>
                                </button>
                            </t>
                        </t>
                        <t t-set="_placeholder">Search Orders...</t>
                        <SearchBar t-if="state.showSearchBar" config="getSearchBarConfig()" placeholder="_placeholder" />
                        <div t-if="shouldShowPageControls()" class="item">
                            <div class="page-controls">
                                <div class="previous" t-on-click="trigger('prev-page')">
                                    <i class="fa fa-fw fa-caret-left" role="img" aria-label="Previous Order List" title="Previous Order List"></i>
                                </div>
                                <div class="next" t-on-click="trigger('next-page')">
                                    <i class="fa fa-fw fa-caret-right" role="img" aria-label="Next Order List" title="Next Order List"></i>
                                </div>
                            </div>
                            <div class="page">
                                <span><t t-esc="getPageNumber()" /></span>
                            </div>
                        </div>
                    </div>
                    <div class="orders">
                        <t t-set="_filteredOrderList" t-value="getFilteredOrderList()" />
                        <t t-if="_filteredOrderList.length !== 0">
                            <div class="header-row" t-att-class="{ oe_hidden: env.isMobile }">
                                <div class="col wide">Date</div>
                                <div class="col wide">Receipt Number</div>
                                <div class="col">Customer</div>
                                <div class="col wide" t-if="showCardholderName()">Cardholder Name</div>
                                <div class="col">Employee</div>
                                <div class="col end">Total</div>
                                <div class="col narrow">Status</div>
                                <div class="col very-narrow" name="delete"></div>
                            </div>
                            <t t-foreach="_filteredOrderList" t-as="order" t-key="order.cid">
                                <div class="order-row" t-att-class="{ highlight: isHighlighted(order) }" t-on-click="trigger('click-order', order)">
                                    <div class="col wide">
                                        <div t-if="env.isMobile">Date</div>
                                        <div><t t-esc="getDate(order)"></t></div>
                                    </div>
                                    <div class="col wide">
                                        <div t-if="env.isMobile">Receipt Number</div>
                                        <div><t t-esc="order.name"></t></div>
                                    </div>
                                    <div class="col">
                                        <div t-if="env.isMobile">Customer</div>
                                        <div><t t-esc="getCustomer(order)"></t></div>
                                    </div>
                                    <div t-if="showCardholderName()" class="col">
                                        <div t-if="env.isMobile">Cardholder Name</div>
                                        <div><t t-esc="getCardholderName(order)"></t></div>
                                    </div>
                                    <div class="col">
                                        <div t-if="env.isMobile">Employee</div>
                                        <div><t t-esc="getEmployee(order)"></t></div>
                                    </div>
                                    <div class="col end">
                                        <div t-if="env.isMobile">Total</div>
                                        <div><t t-esc="getTotal(order)"></t></div>
                                    </div>
                                    <div class="col narrow">
                                        <div t-if="env.isMobile">Status</div>
                                        <div><t t-esc="getStatus(order)"></t></div>
                                    </div>
                                    <div t-if="!shouldHideDeleteButton(order)" class="col very-narrow delete-button" name="delete" t-on-click.stop="trigger('delete-order', order)">
                                        <i class="fa fa-trash" aria-hidden="true"/><t t-if="env.isMobile"> Delete</t>
                                    </div>
                                    <div t-else="" class="col very-narrow"></div>
                                </div>
                            </t>
                        </t>
                        <t t-else="">
                            <div class="empty-order-list">
                                <i role="img" aria-label="Shopping cart" title="Shopping cart" class="fa fa-shopping-cart"></i>
                                <h1>No orders found</h1>
                            </div>
                        </t>
                    </div>
                </div>
                <div class="leftpane">
                    <t t-set="_selectedSyncedOrder" t-value="getSelectedSyncedOrder()" />
                    <t t-set="_selectedOrderlineId" t-value="getSelectedOrderlineId()" />
                    <OrderDetails order="_selectedSyncedOrder" selectedOrderlineId="_selectedOrderlineId" highlightHeaderNote="_state.ui.highlightHeaderNote" />
                    <div class="pads">
                        <div class="control-buttons">
                            <InvoiceButton t-if="env.pos.config.module_account" order="_selectedSyncedOrder" />
                            <ReprintReceiptButton order="_selectedSyncedOrder" />
                        </div>
                        <div class="subpads">
                            <!-- We set so that the term 'Refund' is translated. -->
                            <t t-set="_actionName">Refund</t>
                            <ActionpadWidget client="getSelectedClient()" actionName="_actionName" actionToTrigger="'do-refund'" isActionButtonHighlighted="getHasItemsToRefund()" />
                            <NumpadWidget disabledModes="['price', 'discount']" activeMode="_selectedOrderlineId and 'quantity'" disableSign="true" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
