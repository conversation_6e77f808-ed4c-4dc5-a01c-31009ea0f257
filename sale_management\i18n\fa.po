# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_management
# 
# Translators:
# <PERSON><PERSON><PERSON> <iam<PERSON><EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Hanna Kheradroosta, 2022\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: sale_management
#: model:sale.order.template.line,name:sale_management.sale_order_template_line_1
msgid "4 Person Desk"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
msgid "<span>Options</span>"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__active
msgid "Active"
msgstr "فعال"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a note"
msgstr "افزودن یادداشت"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a product"
msgstr "افزودن یک محصول"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a section"
msgstr "افزودن یک بخش"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add one"
msgstr "افزودن یکی"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add to cart"
msgstr "افزودن به سبد"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Add to order lines"
msgstr "اضافه کردن به سطرهای سفارش"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total
msgid "All Sales"
msgstr "همه فروش‌ها"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Archived"
msgstr "بایگانی شده"

#. module: sale_management
#: model:ir.ui.menu,name:sale_management.menu_product_attribute_action
msgid "Attributes"
msgstr "مشخصه‌ها"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_uom_category_id
msgid "Category"
msgstr "دسته‌بندی"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__company_id
msgid "Company"
msgstr "شرکت"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Confirmation"
msgstr "تاییدیه"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__mail_template_id
msgid "Confirmation Mail"
msgstr "ایمیل تایید"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_option__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"تبدیل بین واحدهای اندازه‌گیری تنها در صورتی می تواند رخ دهد که به یک دسته "
"تعلق داشته باشد. تبدیل بر اساس نسبت‌ها انجام خواهد شد."

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Create standardized offers with default products"
msgstr "ایجاد پیشنهادات استاندارد شده با محصولات پیش‌فرض"

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid "Create your quotation template"
msgstr "قالب پیش‌فاکتورخود را ایجاد کنید"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_date
msgid "Created on"
msgstr "ایجاد شده در"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_company__sale_order_template_id
msgid "Default Sale Template"
msgstr "الگو پیش‌فرض فروش"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__company_so_template_id
msgid "Default Template"
msgstr "قالب پیشفرض"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__name
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Description"
msgstr "توصیف"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid ""
"Design your quotation templates using building blocks<br/>\n"
"                            <em attrs=\"{'invisible': [('module_sale_quotation_builder','=',False)]}\">Warning: this option will install the Website app.</em>"
msgstr ""

#. module: sale_management
#: model:ir.model,name:sale_management.model_digest_digest
msgid "Digest"
msgstr "خلاصه"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Disc.%"
msgstr "تخفیف.%"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__discount
msgid "Discount (%)"
msgstr "تخفیف(٪)"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_type
msgid "Display Type"
msgstr "نوع نمایش"

#. module: sale_management
#: code:addons/sale_management/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "دسترسی ندارید ، این اطلاعات را برای ایمیل خلاصه/کلی کاربر رد کنید"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Documentation"
msgstr "مستندات"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_non_accountable_fields_null
msgid ""
"Forbidden product, unit price, quantity, and UoM on non-accountable sale "
"quote line"
msgstr ""
"کالای غیرمجاز، قیمت واحد، کمیت، و UoM در سطر پیش‌فاکتور فروش غیرقابل‌شمارش"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__sequence
msgid "Gives the sequence order when displaying a list of optional products."
msgstr "ترتیب دنباله را هنگام نمایش فهرستی از محصولات اختیاری ارایه می‌دهد."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__sequence
msgid "Gives the sequence order when displaying a list of sale quote lines."
msgstr "ترتیب دنباله را هنگام نمایش فهرستی از سطرهای پیش‌فاکتور ارایه می‌دهد."

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__id
msgid "ID"
msgstr "شناسه"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__active
msgid ""
"If unchecked, it will allow you to hide the quotation template without "
"removing it."
msgstr ""
"اگر علامت آن را بردارید، به شما امکان می دهد الگوی پیش‌فاکتور را بدون حذف آن"
" پنهان کنید."

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total_value
msgid "Kpi All Sale Total Value"
msgstr "شاخص‌های کلیدی عملکرد (KPI) ارزش کل همه فروش"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_uid
msgid "Last Updated by"
msgstr "آخرین تغییر توسط"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_date
msgid "Last Updated on"
msgstr "آخرین به روز رسانی در"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__line_id
msgid "Line"
msgstr "سطر"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_line_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Lines"
msgstr "سطرها"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_accountable_product_id_required
msgid "Missing required product and UoM on accountable sale quote line."
msgstr "محصول مورد نیاز و UoM در سطر پیش‌فاکتور فروش پاسخگو وجود ندارد."

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Note"
msgstr "یادداشت"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__number_of_days
msgid "Number of days for the validity date computation of the quotation"
msgstr "تعداد روز برای محاسبه تاریخ اعتبار پیش‌فاکتور"

#. module: sale_management
#: model:sale.order.template.option,name:sale_management.sale_order_template_option_1
msgid "Office Chair"
msgstr "صندلی اداری"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_payment
msgid "Online Payment"
msgstr "پرداخت آنلاین"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_signature
msgid "Online Signature"
msgstr "امضای آنلاین"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_option_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Optional Products"
msgstr "محصولات اختیاری"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_option_ids
#: model:ir.model.fields,field_description:sale_management.field_sale_order_line__sale_order_option_ids
msgid "Optional Products Lines"
msgstr "سطرهای محصولات اختیاری"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Options"
msgstr "گزینه ها"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__is_present
msgid "Present on Quotation"
msgstr "حاضر در پیش‌فاکتور"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_id
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Product"
msgstr "محصول"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__quantity
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_qty
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__quantity
msgid "Quantity"
msgstr "تعداد"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Quantity:"
msgstr "تعداد:"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__module_sale_quotation_builder
msgid "Quotation Builder"
msgstr "سازنده پیش‌فاکتور"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__number_of_days
msgid "Quotation Duration"
msgstr "مدت زمان پیش‌فاکتور"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__name
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_tree
msgid "Quotation Template"
msgstr "قالب پیش‌فاکتور"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "سطر قالب پیش‌فاکتور"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation Template Lines"
msgstr "سطرهای قالب پیش‌فاکتور"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "گزینه قالب پیش‌فاکتور"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__sale_order_template_id
msgid "Quotation Template Reference"
msgstr "مرجع قالب پیش‌فاکتور"

#. module: sale_management
#: model:ir.actions.act_window,name:sale_management.sale_order_template_action
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__group_sale_order_template
#: model:ir.ui.menu,name:sale_management.sale_order_template_menu
#: model:res.groups,name:sale_management.group_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Quotation Templates"
msgstr "قالب‌های پیش‌فاکتور"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation expires after"
msgstr "انقضای پیش‌فاکتور پس از"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove"
msgstr "حذف"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove one"
msgstr "حذف یکی"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr "برای تایید سفارشات به صورت خودکار از مشتری درخواست امضای آنلاین کنید."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr ""
"برای تایید سفارشات به صورت خودکار از مشتری درخواست پرداخت آنلاین کنید."

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_option
msgid "Sale Options"
msgstr "گزینه‌های فروش"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.digest_digest_view_form
msgid "Sales"
msgstr "فروش"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order
msgid "Sales Order"
msgstr "سفارش فروش"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_line
msgid "Sales Order Line"
msgstr "سطر سفارش‌فروش"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__order_id
msgid "Sales Order Reference"
msgstr "مرجع سفارش فروش"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Sales Quotation Template Lines"
msgstr "سطرهای قالب پیش‌فاکتور فروش"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Search Quotation Template"
msgstr "جستجوی قالب پیش‌فاکتور"

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_section
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Section"
msgstr "بخش"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid ""
"Selling the same product in different sizes or colors? Try the product grid "
"and populate your orders with multiple quantities of each variant. This "
"feature also exists in the Purchase application."
msgstr ""
"فروش یک محصول در اندازه ها یا رنگ های مختلف؟ گرید محصول را امتحان کنید و "
"سفارشات خود را با مقادیر متعدد از هر نوع پر کنید. این ویژگی در اپلیکیشن خرید"
" نیز وجود دارد."

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__sequence
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid ""
"Struggling with a complex product catalog? Try out the Product Configurator "
"to help sales configure a product with different options: colors, size, "
"capacity, etc. Make sale orders encoding easier and error-proof."
msgstr ""
"آیا با یک کاتالوگ محصول پیچیده مشکل دارید؟ پیکربندی محصول را امتحان کنید تا "
"به فروشندگان کمک کند تا یک محصول را با گزینه های مختلف پیکربندی کنند: رنگ، "
"اندازه، ظرفیت، و غیره. رمزگذاری سفارشات فروش را آسان تر و بدون خطا کنید."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__display_type
msgid "Technical field for UX purpose."
msgstr "فیلد فنی برای منظور UX."

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__note
msgid "Terms and conditions"
msgstr "شرایط و ضوابط"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid ""
"The Administrator can set default Terms & Conditions in Sales Settings. "
"Terms set here will show up instead if you select this quotation template."
msgstr ""
"راهبر سیستم می‌تواند شرایط و ضوابط پیش‌فرض را در تنظیمات فروش تنظیم کند. اگر"
" شما این قالب پیش‌فاکتور را انتخاب کنید، شرایط تنظیم‌شده در اینجا به جای آن "
"نشان‌داده می‌شود."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__mail_template_id
msgid ""
"This e-mail template will be sent on confirmation. Leave empty to send "
"nothing."
msgstr ""
"این قالب ایمیل درصورت تایید ارسال خواهد شد. خالی بگذارید تا چیزی نفرستد."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__is_present
msgid ""
"This field will be checked if the option line's product is already present "
"in the quotation."
msgstr ""
"این فیلد تیک خواهد خورد اگر گزینه سطر محصول در حال حاضر در پیش‌فاکتور وجود "
"دارد."

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale1_management_0
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid "Tip: Odoo supports configurable products"
msgstr "نکته: اودو از محصولات قابل تنظیم پشتیبانی می کند"

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale_management_1
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid "Tip: Sell or buy products in bulk with matrixes"
msgstr "نکته: فروش یا خرید محصولات به صورت عمده با ماتریس"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__price_unit
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Unit Price"
msgstr "قیمت واحد"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Unit Price:"
msgstr "قیمت واحد:"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_id
msgid "Unit of Measure"
msgstr "واحد اندازه گیری"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__uom_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__uom_id
msgid "Unit of Measure "
msgstr "واحد اندازه گیری "

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "UoM"
msgstr "واحد"

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid ""
"Use templates to create polished, professional quotes in minutes.\n"
"                Send these quotes by email and let your customers sign online.\n"
"                Use cross-selling and discounts to push and boost your sales."
msgstr ""
"از قالب‌ها برای ایجاد پیش‌فاکتورهای تمیز و حرفه‌ای، در طی چند دقیقه استفاده کنید.\n"
"                این پیش‌فاکتورها را از طریق ایمیل ارسال کنید و به مشتریان خود اجازه دهید به صورت آنلاین امضا کنند.\n"
"                از فروش و تخفیف متقابل برای ترعیب و افزایش فروش خود استفاده کنید."

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid "You cannot add options to a confirmed order."
msgstr "شما نمی‌توانید گزینه‌هایی را به یک سفارش تاییدشده اضافه کنید."

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"You cannot change the type of a sale quote line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"شما نمی‌توانید نوع یک سطر سفارش فروش را تغییر دهید. بجای آن شما باید سطر "
"جاری را حذف و یک سطر جدید از نوع مناسب ایجاد کنید."

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"استعلام قیمت شما حاوی محصولات شرکت %(product_company)s است، در حالی که استعلام قیمت شما متعلق به شرکت %(quote_company)s است.\n"
"  لطفا شرکت استعلام قیمت خود را تغییر دهید یا محصولات را از سایر شرکت ها (%(bad_products)s) حذف کنید."

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid "Your template cannot contain products from multiple companies."
msgstr "قالب‌ شما نمی‌تواند شامل محصولاتی از شرکت‌های متعدد باشد."

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"Your template contains products from company %(product_company)s whereas your template belongs to company %(template_company)s. \n"
" Please change the company of your template or remove the products from other companies."
msgstr ""
"الگوی شما شامل محصولات شرکت %(product_company)s است در حالی که الگوی شما متعلق به شرکت %(template_company)s است.\n"
"  لطفاً شرکت قالب خود را تغییر دهید یا محصولات را از سایر شرکت ها حذف کنید."

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "days"
msgstr "روز"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "e.g. Standard Consultancy Package"
msgstr ""
