# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <PERSON>, 2021
# krn<PERSON><PERSON>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"- Adó csoport: Az adó részadók halmaza.\n"
"- Rögzített: Az adó összege ugyan az marad minden ár esetén.\n"
"- Ár százaléka: Az adó összege az ár megadott százalékából:\n"
"    pl.: 100 * (1 + 10%) = 110 (Az ár nem tartalmazza)\n"
"    pl.: 110 / ( 1 + 10%) = 100 (Az ár tartalmazza)\n"
"- Az ár százalékosan tartalmazza az adó-t: Az adó összege az ár hányada:\n"
"    pl.: 180 / (1 - 10%) = 200 (Az ár nem tartalmazza)\n"
"    pl.: 200 * (1 - 10%) = 180 (Az ár tartalmazza)"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_applicable
msgid "Applicable Code"
msgstr "Alkalmazható kód"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Adó összegének kiszámítása az 'eredmény' változó beállításával.\n"
"\n"
":param base_amount: lebegőpontos, aktuális összeg melyre az adót  alkalmaztuk\n"
":param price_unit: lebegőpontos\n"
":param quantity: lebegőpontos\n"
":param company: res.company recordset egyetlen\n"
":param product: product.product recordset egyetlen vagy semmi\n"
":param partner: res.partner recordset egyetlen vagy semmi"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Adó összegének kiszámítása az 'eredmény' változó beállításával.\n"
"\n"
":param base_amount: lebegőpontos, aktuális összeg melyre az adót  alkalmaztuk\n"
":param price_unit: lebegőpontos\n"
":param quantity: lebegőpontos\n"
":param product: product.product recordset egyetlen vagy SEMMI\n"
":param partner: res.partner recordset egyetlen or SEMMI"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Adó alkalmazásának meghatározása az 'eredmény' változó Igaz vagy Hamis beállításától függően.\n"
"\n"
":param price_unit: lebegőpontos\n"
":param quantity: lebegőpontos\n"
":param company: res.company recordset egyetlen\n"
":param product: product.product recordset egyetlen vagy Semmi\n"
":param partner: res.partner recordset egyetlen vagy Semmi"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Adó alkalmazásának meghatározása az 'eredmény' változó Igaz vagy Hamis beállításától függően.\n"
"\n"
":param price_unit: lebegőpontos\n"
":param quantity: lebegőpontos\n"
":param product: product.product recordset egyetlen vagy Semmi\n"
":param partner: res.partner recordset egyetlen vagy Semmi"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax_template__amount_type__code
msgid "Python Code"
msgstr "Python kód"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Adó"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Adó kiszámítás"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Sablonok adókhoz"

#. module: account_tax_python
#: code:addons/account_tax_python/models/account_tax.py:0
#: code:addons/account_tax_python/models/account_tax.py:0
#, python-format
msgid ""
"You entered invalid code %r in %r taxes\n"
"\n"
"Error : %s"
msgstr ""
