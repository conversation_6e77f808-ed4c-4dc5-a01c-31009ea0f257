<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="tax_group_icms_0" model="account.tax.group">
            <field name="name">ICMS 0%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_icms_7" model="account.tax.group">
            <field name="name">ICMS 7%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_icms_12" model="account.tax.group">
            <field name="name">ICMS 12%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_icms_19" model="account.tax.group">
            <field name="name">ICMS 19%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_icms_26" model="account.tax.group">
            <field name="name">ICMS 26%</field>
            <field name="country_id" ref="base.br"/>
        </record>

        <record id="tax_group_irpj_0" model="account.tax.group">
            <field name="name">IRPJ 0%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_pis_0" model="account.tax.group">
            <field name="name">PIS 0%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_pis_065" model="account.tax.group">
            <field name="name">PIS 0.65%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_cofins_0" model="account.tax.group">
            <field name="name">COFINS 0%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_cofins_3" model="account.tax.group">
            <field name="name">COFINS 3%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ir_0" model="account.tax.group">
            <field name="name">IR 0%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_issqn_0" model="account.tax.group">
            <field name="name">ISSQN 0%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_issqn_1" model="account.tax.group">
            <field name="name">ISSQN 1%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_issqn_2" model="account.tax.group">
            <field name="name">ISSQN 2%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_issqn_3" model="account.tax.group">
            <field name="name">ISSQN 3%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_issqn_4" model="account.tax.group">
            <field name="name">ISSQN 4%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_issqn_5" model="account.tax.group">
            <field name="name">ISSQN 5%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_csll_0" model="account.tax.group">
            <field name="name">CSLL 0%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_0" model="account.tax.group">
            <field name="name">IPI 0%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_2" model="account.tax.group">
            <field name="name">IPI 2%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_3" model="account.tax.group">
            <field name="name">IPI 3%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_4" model="account.tax.group">
            <field name="name">IPI 4%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_5" model="account.tax.group">
            <field name="name">IPI 5%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_7" model="account.tax.group">
            <field name="name">IPI 7%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_8" model="account.tax.group">
            <field name="name">IPI 8%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_10" model="account.tax.group">
            <field name="name">IPI 10%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_12" model="account.tax.group">
            <field name="name">IPI 12%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_13" model="account.tax.group">
            <field name="name">IPI 13%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_15" model="account.tax.group">
            <field name="name">IPI 15%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_16" model="account.tax.group">
            <field name="name">IPI 16%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_18" model="account.tax.group">
            <field name="name">IPI 18%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_20" model="account.tax.group">
            <field name="name">IPI 20%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_22" model="account.tax.group">
            <field name="name">IPI 22%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_24" model="account.tax.group">
            <field name="name">IPI 24%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_25" model="account.tax.group">
            <field name="name">IPI 25%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_27" model="account.tax.group">
            <field name="name">IPI 27%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_30" model="account.tax.group">
            <field name="name">IPI 30%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_35" model="account.tax.group">
            <field name="name">IPI 35%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_40" model="account.tax.group">
            <field name="name">IPI 40%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_42" model="account.tax.group">
            <field name="name">IPI 42%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_45" model="account.tax.group">
            <field name="name">IPI 45%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_50" model="account.tax.group">
            <field name="name">IPI 50%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_60" model="account.tax.group">
            <field name="name">IPI 60%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_300" model="account.tax.group">
            <field name="name">IPI 300%</field>
            <field name="country_id" ref="base.br"/>
        </record>
        <record id="tax_group_ipi_330" model="account.tax.group">
            <field name="name">IPI 330%</field>
            <field name="country_id" ref="base.br"/>
        </record>
    </data>
</odoo>
