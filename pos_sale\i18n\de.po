# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>esselbosch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "(left:"
msgstr "(verbleibend:"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
#, python-format
msgid "(tax incl.)"
msgstr "(Steuer inkl.)"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "<span class=\"o_form_label\">Down Payment Product</span>"
msgstr "<span class=\"o_form_label\">Anzahlung Produkt</span>"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "<span class=\"o_form_label\">Sales Team</span>"
msgstr "<span class=\"o_form_label\">Verkaufsteam</span>"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
msgid "<span style=\"margin: 0px 5px;\">:</span>"
msgstr "<span style=\"margin: 0px 5px;\">:</span>"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Apply a down payment"
msgstr "Eine Anzahlung anwenden"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "Back"
msgstr "Zurück"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/MobileSaleOrderManagementScreen.xml:0
#, python-format
msgid "Back to list"
msgstr "Zurück zur Liste"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Cancelled"
msgstr "Abgebrochen"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/SetSaleOrderButton.js:0
#, python-format
msgid "Cannot access order management screen if offline."
msgstr ""
"Der Bildschirm für die Auftragsverwaltung kann offline nicht aufgerufen "
"werden."

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Wechselkurs"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Customer"
msgstr "Kunde"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Customer loading error"
msgstr "Fehler beim Laden des Kunden"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Date"
msgstr "Datum"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.message_body
msgid "Delivered from"
msgstr "Geliefert aus"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Do you want to load the SN/Lots linked to the Sales Order?"
msgstr ""
"Möchten Sie die mit dem Verkaufsauftrag verbundenen Seriennummern/Lose "
"laden?"

#. module: pos_sale
#: model:product.product,name:pos_sale.default_downpayment_product
#: model:product.template,name:pos_sale.default_downpayment_product_product_template
msgid "Down Payment (POS)"
msgstr "Anzahlung (Kassensystem)"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__down_payment_details
msgid "Down Payment Details"
msgstr "Details zur Anzahlung"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__down_payment_product_id
msgid "Down Payment Product"
msgstr "Anzahlungsprodukt"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "E.g. customer: Steward, date: 2020-05-09"
msgstr "z. B. Kunde: Steward, Datum: 2022-05-09"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/ReceiptScreen/OrderReceipt.xml:0
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
#, python-format
msgid "From"
msgstr "Von"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__invoiced
msgid "Invoiced"
msgstr "Abgerechnet"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid ""
"It seems that you didn't configure a down payment product in your point of sale.\n"
"                        You can go to your point of sale configuration to choose one."
msgstr ""
"Es scheint, dass Sie in Ihrem Kassensystem kein Anzahlungsprodukt konfiguriert haben.\n"
"Sie können in der Konfiguration Ihres Kassensystems ein solches Produkt auswählen."

#. module: pos_sale
#: code:addons/pos_sale/models/sale_order.py:0
#, python-format
msgid "Linked POS Orders"
msgstr "Verknüpfte Kassenaufträge"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_origin_id
msgid "Linked Sale Order"
msgstr "Verknüpfter Verkaufsauftrag"

#. module: pos_sale
#: code:addons/pos_sale/models/pos_order.py:0
#, python-format
msgid "Linked Sale Orders"
msgstr "Verknüpfte Verkaufsaufträge"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Locked"
msgstr "Gesperrt"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderFetcher.js:0
#: code:addons/pos_sale/static/src/js/SetSaleOrderButton.js:0
#, python-format
msgid "Network Error"
msgstr "Netzwerkfehler"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__pos_draft
msgid "New"
msgstr "Neu"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "Next Order List"
msgstr "Liste nächster Aufträge"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "No"
msgstr "Nein"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "No down payment product"
msgstr "Kein Anzahlungsprodukt"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_sessions_open_count
msgid "Open POS Sessions"
msgstr "Kassensitzungen öffnen"

#. module: pos_sale
#: model:ir.actions.act_window,name:pos_sale.pos_session_action_from_crm_team
msgid "Open Sessions"
msgstr "Offene Sitzungen"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Order"
msgstr "Auftrag"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_line_ids
#: model:ir.model.fields,field_description:pos_sale.field_sale_order_line__pos_order_line_ids
msgid "Order lines Transfered to Point of Sale"
msgstr "An das Kassensystem übertragene Auftragspositionen"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__paid
msgid "Paid"
msgstr "Bezahlt"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Percentage of %s"
msgstr "Anteil von %s"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Kassensystem-Konfiguration"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Kassenauftragszeilen"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order
msgid "Point of Sale Orders"
msgstr "Kassenverkäufe"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_session
msgid "Point of Sale Session"
msgstr "Kassensitzung"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_config_ids
msgid "Point of Sales"
msgstr "Kassensystem"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_count
msgid "Pos Order Count"
msgstr "Anzahl Kassenaufträge"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__pos_done
msgid "Posted"
msgstr "Gebucht"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "Previous Order List"
msgstr "Vorherige Auftragsliste"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Products not available in POS"
msgstr "Produkte im Kassensystem nicht verfügbar"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Quotation"
msgstr "Angebot"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Quotation Sent"
msgstr "Angebot gesendet"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
#, python-format
msgid "Quotation/Order"
msgstr "Angebot/Auftrag"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "SN/Lots Loading"
msgstr "SN/Lose werden geladen"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
#, python-format
msgid "SO"
msgstr "Verkaufsauftrag"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__sale_order_count
msgid "Sale Order Count"
msgstr "Anzahl Verkaufsaufträge"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "Sales"
msgstr "Verkauf"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Verkaufsanalysebericht"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#: model:ir.model,name:pos_sale.model_sale_order
#, python-format
msgid "Sales Order"
msgstr "Verkaufsauftrag"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkaufsauftragszeile"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_crm_team
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_session__crm_team_id
msgid "Sales Team"
msgstr "Verkaufsteam"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "Sales are reported to the following sales team"
msgstr "Die Verkäufe werden an das folgende Verkaufsteam gemeldet"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Salesman"
msgstr "Verkäufer"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#, python-format
msgid "Salesperson"
msgstr "Verkäufer"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Session Running"
msgstr "Sitzung läuft"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_order_amount_total
msgid "Session Sale Amount"
msgstr "Anzahl Sitzungsverkäufe"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Sessions Running"
msgstr "Laufende Sitzungen"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
#, python-format
msgid "Set Sale Order"
msgstr "Verkaufsauftrag setzen"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Settle the order"
msgstr "Auftrag abwickeln"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid ""
"Some of the products in your Sale Order are not available in POS, do you "
"want to import them?"
msgstr ""
"Einige der Produkte in Ihrem Verkaufsauftrag sind nicht im Kassensystem "
"verfügbar. Möchten Sie diese importieren?"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_line_id
msgid "Source Sale Order Line"
msgstr "Ursprung der Verkaufsauftragszeile"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "State"
msgstr "Bundesland"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_report__state
msgid "Status"
msgstr "Status"

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr ""
"Der zum Zeitpunkt des Auftrags geltende Wechselkurs zur Währung des Kurses"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "There was a problem in loading the %s customer."
msgstr "Es gab ein Problem beim Laden des Kunden %s."

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,help:pos_sale.field_pos_session__crm_team_id
msgid "This Point of sale's sales will be related to this Sales Team."
msgstr ""
"Die Verkäufe dieses Kassensystems sind mit diesem Verkaufsteam verknüpft."

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "This product will be applied when down payment is made"
msgstr "Dieses Produkt wird bei Anzahlung angewendet"

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__down_payment_product_id
msgid "This product will be used as down payment on a sale order."
msgstr ""
"Dieses Produkt wird als Anzahlung für einen Verkaufsauftrag verwendet."

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Total"
msgstr "Gesamt"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_pos_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                from Sale"
msgstr ""
"Übertragen<br/>\n"
"                                aus Verkauf"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                to POS"
msgstr ""
"Übertragen<br/>\n"
"                               an Kassensystem"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderFetcher.js:0
#, python-format
msgid "Unable to fetch orders if offline."
msgstr "Bestellungen können offline nicht angenommen werden."

#. module: pos_sale
#: model:product.product,uom_name:pos_sale.default_downpayment_product
#: model:product.template,uom_name:pos_sale.default_downpayment_product_product_template
msgid "Units"
msgstr "Artikel"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "What do you want to do?"
msgstr "Was möchten Sie tun?"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Yes"
msgstr "Ja"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid ""
"You have tried to charge a down payment of %s but only %s remains to be "
"paid, %s will be applied to the purchase order line."
msgstr ""
"Sie haben versucht, eine Anzahlung von %s zu berechnen, aber nur %s sind "
"noch zu zahlen, %s wird auf die Auftragszeile angewendet."
