# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * project
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-05-19 06:07+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project
#: model:mail.template,body_html:project.mail_template_data_module_install_project
msgid ""
"\n"
"            % set last_created_project = user.env['project.project']."
"search([], order=\"create_date desc\")[0]\n"
"\n"
"            <div style=\"margin: 10px auto;\">\n"
"            <table cellspacing=\"0\" cellpadding=\"0\" style=\"width:100%;"
"\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td style=\"padding:2px;width:30%;\">\n"
"                            <img src=\"web/static/src/img/logo.png\"/>\n"
"                        </td>\n"
"                        <td style=\"vertical-align: top; padding: 8px 10px;"
"text-align: left;font-size: 14px;\">\n"
"                            <a href=\"web/login\" style=\"float:right ;"
"margin:15px auto;background: #a24689;border-radius: 5px;color: #ffffff;font-"
"size: 16px;padding: 10px 20px 10px 20px;text-decoration: none;\">Auto Login</"
"a>\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"            <table style=\"width:100%;text-align:justify;margin:0 auto;"
"border-collapse:collapse;border-top:1px solid lightgray\"\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td style=\"padding:15px 10px;font-size:20px\">\n"
"                            <p style=\"color:#a24689;margin:0\" >Hooray!</"
"p><br>\n"
"                            <p dir=\"ltr\" style=\"font-size:15px;margin-"
"top:0pt;margin-bottom:0pt;\">\n"
"                                <span>Your Odoo Project application is up "
"and running</span></p><br>\n"
"                            <p dir=\"ltr\" style=\"margin-top:0pt;margin-"
"bottom:8pt;\">\n"
"                                <span style=\"font-size:13px;font-weight:"
"bold;\">What’s next?</span></p>\n"
"                            <ul style=\"margin-top:0pt;margin-bottom:0pt;"
"font-size:13px;list-style-type:disc;\">\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Try creating a task by sending "
"an email to </span>\n"
"                                        <a href=\"mailto:"
"${last_created_project.alias_id.name_get()[0][1] if last_created_project."
"alias_id.alias_domain else user.company_id.email}\">\n"
"                                            <span style=\"font-weight:bold; "
"text-decoration:underline;\">${last_created_project.alias_id.name_get()[0]"
"[1] if last_created_project.alias_id.alias_domain else user.company_id.email}"
"</span>\n"
"                                        </a>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:8pt;\">\n"
"                                        <span><a href=\"/"
"web#view_type=list&model=res.users&action=base.action_res_users\">\n"
"                                            <span style=\"font-weight:bold; "
"text-decoration:underline;\">Invite new users</span></a></span>\n"
"                                        <span>to collaborate</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                            </ul> <br>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;margin-"
"top:0pt;margin-bottom:8pt;\">\n"
"                                <span style=\"font-weight:bold;\">Discover "
"the </span>\n"
"                                <span><a href=\"/"
"web#view_type=kanban&model=project.project&action=project."
"open_view_project_all\">\n"
"                                    <span style=\"font-weight:bold; text-"
"decoration:underline;\">project planner</span></a></span>\n"
"                                <span> to activate extra features</span>\n"
"                                <span style=\"color:#a24689;margin:0;font-"
"weight:bold\">(${user.env['web.planner']."
"get_planner_progress('planner_project')}% done)</span>\n"
"                            </p>\n"
"                            <ul style=\"margin-top:0pt;margin-bottom:0pt;"
"font-size:13px;list-style-type:disc;\">\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Track hours with timesheets,</"
"span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Plan tasks and resources with "
"forecasts,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Get smart reporting and "
"accurate dashboards,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Bill time on tasks or issues,</"
"span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:8pt;\">\n"
"                                        <span>And much more...</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                            </ul>\n"
"                            <br>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;line-"
"height:1.3;margin-top:0pt;margin-bottom:8pt;\">\n"
"                                <span style=\"font-weight:bold;\">Need Help?"
"</span>\n"
"                                <span style=\"font-style:italic;\">You’re "
"not alone</span>\n"
"                            </p>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;margin-"
"top:0pt;margin-bottom:8pt;\">\n"
"                                <span>We would be delighted to assist you "
"along the way. Contact us at \n"
"                                <a href=\"mailto:<EMAIL>\"><span style="
"\"text-decoration:underline;\">\n"
"                                <EMAIL></span></a> if you have any "
"question. You can also discover \n"
"                                how to get the best out of Odoo Project with "
"our </span>\n"
"                                <a href=\"https://www.odoo.com/documentation/"
"user/9.0/project.html\">\n"
"                                <span style=\"text-decoration:underline;"
"\">User Documentation</span></a>\n"
"                                </span><span> or with our </span>\n"
"                                <a href=\"https://www.odoo.com/"
"documentation/9.0/\">\n"
"                                <span style=\"text-decoration:underline;"
"\">API Documentation</span></a>\n"
"                            </p>\n"
"                            <br>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;margin-"
"top:0pt;margin-bottom:8pt;\"><span>Enjoy your Odoo experience,</span></p>\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"            <div dir=\"ltr\" style=\"font-size:13px;margin-top:0pt;margin-"
"bottom:8pt;color:grey\">\n"
"                <span><br/>-- <br/>The Odoo Team<br/>PS: People love Odoo, "
"check </span><a href=\"https://twitter.com/odoo/favorites\"><span style="
"\"text-decoration:underline;\">what they say about it.</span></a></span>\n"
"            </div>\n"
"        </div>"
msgstr ""

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"\n"
"<p>Dear ${object.partner_id.name or 'customer'},</p>\n"
"<p>Thank you for your enquiry.<br /></p>\n"
"<p>If you have any questions, please let us know.</p>\n"
"<p>Best regards,</p>"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner_task_count
msgid "# Tasks"
msgstr "N° Tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user_no_of_days
msgid "# of Days"
msgstr "Nº de Días"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_nbr_tasks
#: model:ir.model.fields,field_description:project.field_report_project_task_user_nbr
msgid "# of Tasks"
msgstr "N° de Tareas"

#. module: project
#: code:addons/project/project.py:273 code:addons/project/project.py:294
#: code:addons/project/project.py:446
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"(Un)archiving a project automatically (un)archives its tasks and issues. Do "
"you want to proceed?"
msgstr ""
"(Des)archivar un proyecto de forma automática (des)archiva sus tareas e "
"incidencias. ¿Quiere proceder?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "- The Odoo Team"
msgstr "- El Equipo de Odoo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "1. Learn about Tasks, Issues and Timesheets."
msgstr "1. Aprenda acerca de las Tareas, Incidencias y Partes de Horas."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"2. Now, take some time to list <strong>the Projects you'll need:</strong>"
msgstr ""
"2. Ahora , tome algún tiempo para registrar<strong>los proyectos que "
"necesitará:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<i class=\"fa fa-user\"/> Person Responsible"
msgstr "<i class=\"fa fa-user\"/> Persona Responsable"

#. module: project
#: code:addons/project/project.py:151
#, python-format
msgid ""
"<p class=\"oe_view_nocontent_create\">\n"
"                        Documents are attached to the tasks and issues of "
"your project.</p><p>\n"
"                        Send messages or log internal notes with attachments "
"to link\n"
"                        documents to your project.\n"
"                    </p>"
msgstr ""
"<p class=\"oe_view_nocontent_create\">\n"
"                        Los documentos son adjuntados a las tareas e "
"incidencias de su proyecto.</p><p>\n"
"                        Envíe mensajes o registre notas internas con "
"adjuntos para enlazar\n"
"                        documentos a su proyecto.\n"
"                    </p>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span attrs=\"{'invisible':[('use_tasks', '=', False)]}\">as </span>"
msgstr "<span attrs=\"{'invisible':[('use_tasks', '=', False)]}\">como </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install now"
msgstr "<span class=\"fa fa-arrow-circle-o-down\"/> Instalar ahora"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<span class=\"fa fa-comment-o\"/> Website Live Chat"
msgstr "<span class=\"fa fa-comment-o\"/> Chat del Sitio Web"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<span class=\"fa fa-comment-o\"/> Website Live Chat on"
msgstr "<span class=\"fa fa-comment-o\"/> En el Chat del Sitio Web"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<span class=\"fa fa-envelope-o\"/> Email Our Project Expert"
msgstr ""
"<span class=\"fa fa-envelope-o\"/> Nuestro correo electrónico Proyecto de "
"Expertos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"fa fa-thumbs-o-down\"/> The <strong> Wrong Way</strong> to use "
"projects:"
msgstr ""
"<span class=\"fa fa-thumbs-o-down\"/> La <strong> Manera Incorrecta</strong> "
"de usar proyectos:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"fa fa-thumbs-o-up\"/> The <strong>Right Way</strong> to use "
"projects:"
msgstr ""
"<span class=\"fa fa-thumbs-o-up\"/> La <strong>Manera Correcta</strong> de "
"usar proyectos:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"o_label\">Documents</span>"
msgstr "<span class=\"o_label\">Documentos</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-laptop\"/>\n"
"                                            <strong> Screen Customization</"
"strong>\n"
"                                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-laptop\"/>\n"
"                                            <strong> Personalización de la "
"Pantalla</strong>\n"
"                                        </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-mobile\"/>\n"
"                                            <strong> From your Mobile phone</"
"strong>\n"
"                                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-mobile\"/>\n"
"                                            <strong> Desde su teléfono "
"Móvil</strong>\n"
"                                        </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-pencil-"
"square-o\"/>\n"
"                                            <strong> Create Custom Reports</"
"strong>\n"
"                                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-pencil-"
"square-o\"/>\n"
"                                            <strong> Crear Informes "
"Personalizados</strong>\n"
"                                        </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-puzzle-piece"
"\"/>\n"
"                                            <strong> Via Chrome extension</"
"strong>\n"
"                                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-puzzle-piece"
"\"/>\n"
"                                            <strong> Vía extensión de "
"Chrome</strong>\n"
"                                        </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-sitemap\"/>\n"
"                                            <strong> Workflow Customization</"
"strong>\n"
"                                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-sitemap\"/>\n"
"                                            <strong> Personalización del "
"Workflow</strong>\n"
"                                        </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-tasks\"/>\n"
"                                            <strong> Directly in Odoo</"
"strong>\n"
"                                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-tasks\"/>\n"
"                                            <strong> Directamente en Odoo</"
"strong>\n"
"                                        </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-thumb-tack\"/"
">\n"
"                                            <strong> Exercise 1</strong><br/"
">\n"
"                                            <span class=\"small\">Check "
"Workload</span>\n"
"                                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-thumb-tack\"/"
">\n"
"                                            <strong> Ejercicio 1</strong><br/"
">\n"
"                                            <span class=\"small\">Comprobar "
"Carga de Trabajo</span>\n"
"                                        </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-thumb-tack\"/"
">\n"
"                                            <strong> Exercise 2</strong><br/"
">\n"
"                                            <span class=\"small\">Delay to "
"close an Issue</span>\n"
"                                        </span>"
msgstr ""
"<strong> Ejercicio 2</strong><br/>\n"
"<span class=\"small\">Demora para cerrar un problema</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-check-square-o\"/"
">\n"
"                                        <strong> Tasks</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-check-square-o\"/"
">\n"
"                                        <strong> Tareas</strong>\n"
"                                    </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-clock-o\"/>\n"
"                                        <strong> Timesheets</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-clock-o\"/>\n"
"                                        <strong> Partes de Horas</strong>\n"
"                                    </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-exclamation-"
"circle\"/>\n"
"                                        <strong> Issues</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-exclamation-"
"circle\"/>\n"
"                                        <strong> Incidencias</strong>\n"
"                                    </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span><strong>Contact us to customize your application:</strong><br/>\n"
"                                    We have special options for unlimited "
"number of customizations !\n"
"                                    </span>"
msgstr ""
"<span><strong>Contáctenos para personalizar su aplicación:</strong><br/>\n"
"                                    Tenemos opciones especiales para un "
"número ilimitado de personalizaciones!\n"
"                                    </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<span><strong>We are here to help you:</strong> if you don't succeed in "
"achieving your favorite KPIs, contact us and we can help you create your "
"custom reports.\n"
"                                    </span>"
msgstr ""
"<span><strong>Estamos aquí para ayudarlo:</strong> si no tiene éxito "
"alcanzando sus KPIs, contáctenosy le ayudaremos a crear sus reportes "
"personalizados.\n"
"                                    </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong> Adjourn (5 min)</strong>\n"
"                                Conclude the meeting with upbeat statements "
"and positive input on project accomplishments. Always reinforce teamwork and "
"encourage all member to watch out for each other to ensure the project is "
"successful."
msgstr ""
"<strong> Aplazar (5 min)</strong>\n"
"                                La conclusión de la reunión con afirmaciones "
"optimistas y entrada positiva de los logros del proyecto . Siempre reforzar "
"el trabajo en equipo y animar a todos los miembros a tenerse en cuenta entre "
"sí para asegurar el éxito del proyecto."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong> Critical items (10 min)</strong>\n"
"                                Upon completing the project status review "
"session, summarize the critical items."
msgstr ""
"<strong> Comentarios criticos (10 min)</strong>\n"
"                                Al término de la sesión de revisión del "
"estado del proyecto, un resumen de los elementos críticos."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong> New business issues (5 min)</strong>\n"
"                                You may have learned something during the "
"week that affects the project.  Share the news early in the meeting to help "
"team members consider the  issue as you walk through the project status "
"session. Not every  issue warrants spending time here, so keep discussions "
"to a minimum."
msgstr ""
"<strong> Nuevos Temas de Negocios (5 min)</strong>\n"
"                                Es posible que haya aprendido algo durante "
"la semana que afecta el proyecto . Comparte la noticia a principios de la "
"reunión para ayudar a los miembros del equipo consideran que el tema a "
"medida que camina a través de la sesión de estado del proyecto. No todos los "
"números garantiza que pasan el tiempo aquí , a fin de mantener discusiones a "
"un mínimo."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong> Prepare an agenda (and keep to it)</strong>"
msgstr "<strong> Preparar una agenda (y mantenerse a la misma)</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong> Project plan status review (20 min) </strong>\n"
"                                Walk through your project plan and allow "
"each team member to provide a brief status of assignments due this week and "
"tasks planned for the next two  weeks. You want to know whether tasks are on "
"track and if any will miss  their projected deadline. You also want to allow "
"the team member to share  any special considerations that might affect other "
"tasks or members of  the project. Carefully manage this part because some "
"team members will  want to pontificate and spend more time than is really "
"needed. Remember, you’re the project manager."
msgstr ""
"<strong> Revisión del estado del Plan del Proyecto (20 min) </strong>\n"
"                                Avanzar a través de su plan de proyecto y "
"permitir que cada miembro del equipo pueda proporcionar un breve estado de  "
"los entregables esperados por semana y las tareas previstas para las "
"próximas dos semanas . ¿Quieres saber si las tareas están en camino y si "
"alguna  perderá su plazo proyectado ?. También desea permitir que el miembro "
"del equipo pueda compartir algunas consideraciones especiales que podrían "
"afectar a otras tareas o los miembros del proyecto, maneje con cuidado esta "
"parte debido a que algunos miembros del equipo van a querer asegurar y "
"utilizar más tiempo de lo que realmente se necesita. Recuerde, usted es el "
"director del proyecto ."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong> Q&amp;A and discussion time (10 min)</strong>\n"
"                                Always give your team time to ask questions "
"on issues that were not discussed. This gives you another opportunity to "
"reinforce key points that you've picked up during the week or discovered "
"during the meeting."
msgstr ""
"<strong> Q&amp;A y tiempo de discusión (10 min)</strong>\n"
"                                Siempre darle tiempo a su equipo para hacer "
"preguntas sobre temas que no se discuten . Esto le da otra oportunidad para "
"reforzar los puntos claves que ha recogido durante la semana o descubiertos "
"durante la reunión."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong> Roll call (5 min)</strong>\n"
"                                Let everyone know who is in attendance "
"before starting. This is important  for remote team members who have dialled "
"in and will save you time later."
msgstr ""
"<strong>  Llamado de lista (5 min)</strong>\n"
"                                Que cada uno sabe que está en la asistencia "
"antes de comenzar. Esto es importante para los miembros del equipo antiguo "
"que han marcados y le ahorrará tiempo más tarde."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong> Summary and follow-up items (5 min)</strong>\n"
"                                Always wrap up with a project status summary "
"and a list of action items  dentified in the meeting to help move the "
"project along."
msgstr ""
"<strong> Resumen y objetos de seguimiento (5 min)</strong>\n"
"                                Siempre concluir con un resumen del estado "
"del proyecto y una lista de elementos de acción identificados en la reunión "
"para ayudar a mover el proyecto a lo largo."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>A great feature in Odoo is the integration of a Collaborative "
"Notepad called Etherpad.</strong><br/>\n"
"                            It replaces the standard Description area in "
"Tasks and Issues and is extremely useful for several cases."
msgstr ""
"<strong>\n"
"Una gran característica de Odoo es la integración de una Libreta "
"Colaborativa llamada Etherpad.</strong><br/>\n"
"Esta sustituye la Descripción estándar en las Tareas e Incidencias y es "
"extremadamente útil para varios casos."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>A problem or request from a customer</strong> that needs to be "
"identified, solved and followed up asap."
msgstr ""
"<strong>Un problema o una petición de un cliente</strong> que necesita ser "
"identificado, resuelto y seguido lo antes posible."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>About Employees:</strong>"
msgstr "<strong>Sobre Empleados:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Add a Deadline</strong><br/>\n"
"                                    The deadline will help you determine if "
"a task or issue is progressing as expected  and to anticipate its next "
"update."
msgstr ""
"<strong>\n"
"Añadir una fecha de vencimiento</strong><br/>\n"
"                                    El plazo le ayudará a determinar si una "
"tarea o asunto está progresando como se esperaba y para anticipar en su "
"próxima actualización."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>An internal activity</strong> that should be done within a defined "
"period of time."
msgstr ""
"<strong>Una actividad interna</strong> que debe hacerse dentro de un período "
"de tiempo definido."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Analyze reports</strong> (every a year)"
msgstr "<strong>Analizar los informes</strong> ( Cada año )"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Approve your Timesheets</strong> (every week)"
msgstr "<strong>Apruebe sus Partes de Horas</strong> (cada semana)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Ask participants to prepare</strong><br/>\n"
"                                To run an effective meeting, prepare "
"participants beforehand. Inform them of how they are expected to contribute."
msgstr ""
"<strong>Pida a los participantes prepararse</strong><br/>\n"
"                                \n"
"Para ejecutar una reunión eficaz, preparar a los participantes de antemano. "
"Informarles de la forma en que se espera que contribuyan."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Be careful about the messages you send</strong><br/>\n"
"                                    Sending a message through Odoo will "
"automatically send an email containing your message to all the followers "
"including internal employees, external users or customers."
msgstr ""
"<strong>Tenga cuidado con los mensajes que envíe</strong><br/>\n"
"El envío de un mensaje a través de Odoo enviará automáticamente un correo "
"electrónico a todos los seguidores incluidos los empleados internos, y "
"usuarios o clientes externos."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Billing</strong>"
msgstr "<strong>Facturación</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Choose the right day</strong><br/>\n"
"                                    Avoid Monday mornings as your regular "
"meeting day; choosing to meet later in the week gives participants time to "
"get ready for the meeting and to work toward specific objectives in the days "
"that follow."
msgstr ""
"<strong>Elija el día correcto</strong><br/>\n"
"                                    Evitar el lunes por la mañana como su "
"día de reunión ordinaria, elija para compensar tiempo más adelante en la "
"semana con los participantes para prepararse para  la próxima reunión y "
"trabajar hacia objetivos específicos en los días que siguen."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Click on 'Reporting' on the main menu</strong> and generate "
"statistics relevent to each profiles:"
msgstr ""
"<strong>Haga clic en 'Informes' en el menú principal </strong>y genere las "
"estadísticas relevantes a cada perfil:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Contact us now:</strong><br/>"
msgstr "<strong>Contáctenos ahora:</strong><br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Create tasks and issues by email</strong><br/>\n"
"                                In Odoo, every project has an email alias. "
"If you send an email to this alias, it will automatically create a task or "
"issue in the first stage of the project, with all the email recipients as "
"its default followers."
msgstr ""
"<strong>Crear tareas e incidencias por correo</strong><br/>\n"
"                                En Odoo, cada proyecto tiene un alias de "
"correo. Si usted envía un correo a este alias, creará una tarea o incidencia "
"automáticamente en la primera etapa del proyecto, con todos los "
"destinatarios como seguidores predeterminados."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Define a Naming Convention</strong><br/>\n"
"                                    Add keywords in the 'Task title' field, "
"for example [Customer name] or [Website]. This will help you navigate and "
"search through the dozens of tasks in your project."
msgstr ""
"<strong>Definir una convención de nomenclatura</strong><br/>\n"
"                                    Añadir palabras clave en el campo \" "
"título de la tarea ' , por ejemplo [ Nombre del cliente ] o [Sitio ] . Esto "
"le ayudará a navegar y buscar a través de las docenas de tareas en su "
"proyecto."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Enter your activities</strong> (every day)"
msgstr "<strong>Introduzca sus actividades</strong> (cada día)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Example: </strong>"
msgstr "<strong>Ejemplo: </strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Exercise:</strong> Try to create a graph with the monthly evolution "
"of the 'Average delay to close'  of Issues."
msgstr ""
"<strong>Ejercicio:</strong> Trate de crear una gráfica de la evolución "
"mensual de el ' Tiempo medio para cerrar ' de incidencias."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Exercise:</strong> Try to get a view of the workload for this week "
"for all your employees (planned hours)."
msgstr ""
"<strong>Ejercicio:</strong> Trate de obtener una vista de la carga de "
"trabajo para esta semana para todos sus empleados (horas planificadas )."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Follow every meeting with a recap email</strong>\n"
"                                    As soon as the meeting is over, publish "
"meeting minutes and distribute them along with the updated project schedule, "
"issues/action item matrix, and  any other appropriate documents. Try to use "
"the same template throughout meetings and improve it continuously."
msgstr ""
"<strong>\n"
"Siga todas las reuniones con un resumen de correo electrónico</strong>\n"
"                                    Tan pronto como se termine la reunión , "
"publicará las actas de reuniones y distribuirlos junto con el calendario "
"actualizado de proyectos, incidencias,matriz de punto de acción  y "
"cualesquier otro documento apropiado. Trate de usar la misma plantilla a lo "
"largo de las reuniones y mejorarla continuamente."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
#, fuzzy
msgid ""
"<strong>Follow only what you need</strong><br/>\n"
"                                    The simplest way to use notifications is "
"to follow a whole project: you will receive notifications for all the new "
"and existing tasks or issues of a project."
msgstr ""
"<strong>Siga sólo lo que usted necesita</strong><br/>\n"
"                                    La manera más simple de usar las "
"notificaciones es seguir un Proyecto completo: usted recibirá todas las "
"notificaciones seleccionadas para todas las Tareas e Incidencias nuevas y "
"existentes del Proyecto."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>For issues:</strong>"
msgstr "<strong>Para incidencias:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>For tasks:</strong>"
msgstr "<strong>Para tareas:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Getting reports</strong> on what your employees are working on"
msgstr ""
"<strong>Preparación de Informes</strong> en lo que sus empleados están "
"trabajando en"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Getting statistics</strong> on how much time a task takes to be "
"completed"
msgstr ""
"<strong>Entrega de Estadísticas</strong> que tanto tiempo tarda una tarea "
"para ser completada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Have Clear Responsibilities</strong><br/>\n"
"                                    The person assigned to a task is "
"responsible for its progress and their avatar is displayed in Kanban view "
"for quick reference. Of course, the responsibility for a task can change "
"depending on its stage."
msgstr ""
"<strong>Tener Responsabilidades Claras</strong><br/>\n"
"                                    La persona asignada a la tarea es "
"responsable de su progreso y su avatar es mostrado en la vista Kanban para "
"una referencia rápida. Desde luego, la responsabilidad por una tarea puede "
"cambiar dependiendo de la etapa."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>In 'Pull' mode</strong>, tasks ready to progress to the next stage "
"are just marked  as 'Ready for next stage (using the status icon) by the "
"person responsible for the current stage. Then, the person responsible for "
"the next stage  takes the task and moves it to the next stage. This is the "
"best way to work if you have diluted responsibilities for your Kanban stages."
msgstr ""
"<strong>En Modo 'Pull'</strong>, tareas listas para pasar a la siguiente "
"etapa se acaban de marcar como \" Listo para la siguiente etapa ( usando el "
"icono de estado ) por la persona responsable de la etapa actual. Entonces , "
"la persona responsable de la próxima etapa tiene la tarea y lo mueve a la "
"siguiente etapa . Esta es la mejor manera de trabajar si se ha dividido "
"responsabilidades para sus etapas Kanban."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>In 'Push' mode</strong>, tasks are pushed into the next stage (once "
"they satisfy all requirements) by the person responsible for the current "
"stage. This is a simple way to work but only functions well if you work "
"alone."
msgstr ""
"<strong>En Modo Push' </strong>, tareas que se van pasando a la siguiente "
"etapa ( cuando hayan satisfecho todos los requisitos ) por la persona "
"responsable de la etapa actual. Esta es una forma sencilla de trabajar, pero "
"sólo funciona bien si trabaja solo."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Invoice your customers</strong> (every month)"
msgstr "<strong>Facturar a sus clientes</strong> (cada mes)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Invoicing your customers</strong> on Time &amp; Material projects"
msgstr ""
"<strong>Facturando a sus clientes</strong> en proyectos de Tiempo y Material"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Need help to structure your projects?</strong><br/>"
msgstr "<strong>¿Necesita ayuda para estructurar sus proyectos?</strong><br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Need help with Timesheets management? Contact us now:</strong><br/>"
msgstr ""
"<strong>Need help with Timesheets management? Contáctanos ahora:</strong><br/"
">"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Need help with defining your Projects? Contact us now.</strong><br/>"
msgstr ""
"<strong>¿Necesita ayuda con la definición de sus proyectos ? Contáctanos "
"ahora.</strong><br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Notes</strong>"
msgstr "<strong>Notas</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Prepare yourself as well</strong><br/>\n"
"                                As project manager, you also need to be "
"fully prepared. There should be no surprises during the meeting. Surprises "
"can undermine your ability to manage the project and cause team members to "
"lose confidence in you."
msgstr ""
"<strong>Prepárese así</strong><br/>\n"
"                                Como director del proyecto , también tiene "
"que estar preparado totalmente . No debe haber sorpresas durante la "
"reunión . Las sorpresas pueden debilitar su capacidad para gestionar el "
"proyecto y hacer que los miembros del equipo a perder la confianza en ti."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Recommended actions:</strong>"
msgstr "<strong>Acciones recomendadas:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Rely on the chatter</strong><br/>\n"
"                                    Below every Task and Issue (or more "
"generally, below every Document in Odoo) is an area called Chatter."
msgstr ""
"<strong>Use el área de charla</strong><br/>\n"
"Debajo de cada Tarea e Incidencia (o más generalmente, debajo de cada "
"Documento en Odoo) hay un area de Charla."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Set Priorities</strong><br/>\n"
"                                    The <i class=\"fa fa-star\"/> is used to "
"indicate priority: in Kanban views, high priority Tasks or Issues will be "
"displayed on top. This is particulary useful if you use a Scrum methodology "
"to indicate the tasks for the week. Similarly to Status, you can change the "
"meaning of the Star indicator from the Project Stages tab."
msgstr ""
"<strong>Establecer Prioridades</strong><br/>\n"
"                                    El <i class=\"fa fa-star\"/> se utiliza "
"para indicar la prioridad : en las vistas de Kanban , tareas de alta "
"prioridad o cuestiones se mostrarán en la parte superior . Esto es "
"particularmente útil si se utiliza una metodología Scrum para indicar las "
"tareas para la semana . De manera similar a la de estado , puede cambiar el "
"significado del indicador de la estrella de la pestaña Proyecto etapas ."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>Stages and Requirements</strong> for next stage:"
msgstr "<strong>Etapas y Requerimientos</strong> para la próxima etapa:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Start with an answer.</strong><br/>\n"
"                                The worst question to ask is, \"What did you "
"do this week?\" It invariably  generates unnecessary, time-consuming "
"dialogue from team members. Plus, you should already know what everyone on "
"the team did during the week."
msgstr ""
"<strong>Comience con una respuesta.</strong><br/>\n"
"                                La peor pregunta es , \" ¿Qué has hecho esta "
"semana ? \" Se genera invariablemente , el diálogo innecesario tiempo de los "
"miembros del equipo . Además, usted ya debe saber lo que todos en el equipo "
"hizo durante la semana."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>There are two ways of managing your Kanban stages:</strong>"
msgstr "<strong>Hay dos formas de gestionar sus etapas Kanban : </strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>To increase the efficiency of your Projects</strong>, you should "
"have a look at some of our other apps:"
msgstr ""
"<strong>Para aumentar la eficiencia de sus proyectos</strong>,usted debe "
"echar un vistazo a algunas de nuestras otras aplicaciones:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>To use Collaborative Notepads</strong>, simply activate the "
"corresponding option in your"
msgstr ""
"<strong>Para utilizar las libretas de colaboración</strong>,\n"
"basta con activar la opción correspondiente "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Use Status Indicator</strong><br/>\n"
"                                    The Status indicator helps you manage "
"Tasks and Issues by giving them one of 3 different colour: Grey, Green or "
"Red. The meaning of these Statuses can be freely configured, for example:"
msgstr ""
"<strong>Indicador de estado de Uso</strong><br/>\n"
"                                    El indicador de estado le ayuda a "
"gestionar las tareas y problemas , dándoles uno de los 3 colores "
"diferentes : gris , verde o rojo. El significado de estos estados se puede "
"configurar libremente, por ejemplo :"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>Use Tags</strong><br/>\n"
"                                    Tags  are complementary to your project "
"stages: they can work as a second  level categorization which is very useful "
"if you have a lot of Tasks or Issues to manage. Also, it becomes very easy "
"to find Tasks or Issues by typing  the Tag into the main Search bar."
msgstr ""
"<strong>Use Etiquetas</strong><br/>\n"
"                                    Las etiquetas son complementarias a las "
"etapas de su proyecto: ellas pueden funcionar como un segundo nivel de "
"categorización que es muy útil si usted tiene muchas Tareas o Incidencias "
"por manejar. También, facilitan la búsqueda por nombre de etiqueta desde la "
"barra superior."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>What Activities</strong> would you like to manage?"
msgstr "<strong>Qué Actividades</strong> le gustaría administrar?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "<strong>What do you expect</strong> from using Odoo Project?"
msgstr "<strong>Qué espera</strong> al usar Odoo?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>You have different users writing Tasks descriptions</strong><br/>\n"
"                            It can quickly become messy if everyone uses his "
"own layout. Etherpad will allow you to create a basic template with a few "
"titles and bullet points, making it much easier for everyone."
msgstr ""
"<strong>Tienes diferentes usuarios de las tareas de escritura descripciones</"
"strong><br/>\n"
"                            Se puede convertir rápidamente en un desastre si "
"cada uno utiliza su propio diseño . Etherpad le permitirá crear una "
"plantilla básica con algunos títulos y viñetas , lo que es mucho más fácil "
"para todos."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>You have to manage versions and track changes</strong><br/>\n"
"                            Etherpad auto-saves the document at regular "
"short intervals and users can permanently save specific versions at any "
"time. Plus, a \"time slider\" feature also allows anyone to explore the "
"history of the pad."
msgstr ""
"<strong>Hay que gestionar versiones y control de cambios</strong><br/>\n"
"                            Etherpad auto-guarda el documento en cortos "
"intervalos regulares y los usuarios pueden guardar de forma permanente las "
"versiones específicas en cualquier momento. Además, una función de \" "
"control deslizante de tiempo \" también permite que cualquiera pueda "
"explorar la historia de la almohadilla."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"<strong>You organize distant meetings</strong><br/>\n"
"                            Etherpad allows users to simultaneously edit a "
"text document, see all of the edits in real-time and with the ability to "
"display each author's text in their own color."
msgstr ""
"<strong>A organizar reuniones distantes</strong><br/>\n"
"                            Etherpad permite a los usuarios editar "
"simultáneamente un documento de texto , ver todos los cambios en tiempo real "
"y con la capacidad de mostrar el texto de cada autor en su propio color."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Un diccionario Python que será evaluado para proveer valores predeterminados "
"cuando se creen nuevos registros para este alias."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"A customer sends a <NAME_EMAIL>: an Issue is "
"automatically created into a 'Support level 1' project based on the original "
"email from the customer."
msgstr ""
"Un cliente envía una <NAME_EMAIL> : un problema se crea "
"automáticamente en el proyecto ' Nivel de soporte 1 ' basado en el correo "
"electrónico original del cliente."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"A project usually involves many stakeholders, be it the project's sponsor, "
"resources, customers or external contractors. But the most important person "
"in a project is usually the Project Manager.<br/>\n"
"                    In Odoo, the Project Managers have the responsibility of "
"managing the Kanban view: they ensure smooth progression of the projects, "
"minimal downtime between stages and optimal work distribution between "
"resources."
msgstr ""
"Un proyecto generalmente implica a muchos interesados ​​, ya sea de los "
"patrocinadores , los recursos, los clientes del proyecto o contratistas "
"externos . Pero la persona más importante en un proyecto suele ser el "
"director de proyecto.<br/>\n"
"                   En Odoo, los jefes de proyecto tienen la responsabilidad "
"de gestionar la vista Kanban: aseguran un avance  manejable de los "
"proyectos, el tiempo de inactividad mínimo entre etapas y la distribución "
"del trabajo óptima entre los recursos. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_kanban_state
msgid ""
"A task's kanban state indicates special situations affecting it:\n"
" * Normal is the default situation\n"
" * Blocked indicates something is preventing the progress of this task\n"
" * Ready for next stage indicates the task is ready to be pulled to the next "
"stage"
msgstr ""
"Un estado de kanban de una tarea indica situaciones especiales que la "
"afectan:\n"
" * 'Normal' es la situación por defecto.\n"
" * 'Bloqueada' indica que algo está impidiendo el progreso de la tarea.\n"
" * 'Lista para la siguiente etapa' indica que la tarea puede ser llevada a "
"la siguiente etapa."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "Aceptar Correos Desde"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Account Preferences"
msgstr "Preferencias de la cuenta."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:38
#, python-format
msgid "Action has a clear description"
msgstr "Descripción clara de una acción."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Activate 'Allow invoicing based on timesheets' from the <i>Human Resources "
"Settings</i>."
msgstr ""
"Activar 'Permitir facturar basado en partes de horas' en los <i>Ajustes de "
"Recursos Humanos</i>."

#. module: project
#: selection:project.config.settings,module_project_issue_sheet:0
msgid "Activate timesheets on issues"
msgstr "Activar partes de horas en las incidencias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_active
#: model:ir.model.fields,field_description:project.field_project_task_active
msgid "Active"
msgstr "Activo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Add a description..."
msgstr "Añadir una descripción..."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:55
#, python-format
msgid "Added in current sprint"
msgstr "Añadido en el sprint actual"

#. module: project
#: model:project.task.type,name:project.project_stage_data_2
msgid "Advanced"
msgstr "Avanzada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_id
msgid "Alias"
msgstr "Alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_contact
msgid "Alias Contact Security"
msgstr "Seguridad al Contactar el Alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_model
msgid "Alias Model"
msgstr "Alias del Modelo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_name
msgid "Alias Name"
msgstr "Nombre del Alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_domain
msgid "Alias domain"
msgstr "Alias del dominio"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_model_id
msgid "Aliased Model"
msgstr "Modelo con Alias"

#. module: project
#: code:addons/project/project.py:133
#, python-format
msgid "All Employees Project: all employees can access"
msgstr "Todos los empleados del proyecto: todos los empleados pueden acceder"

#. module: project
#: selection:project.config.settings,module_rating_project:0
msgid "Allow activating customer rating on projects, at issue completion"
msgstr ""
"Activar la calificación del cliente en los proyectos, al completar "
"incidencias"

#. module: project
#: model:ir.model.fields,help:project.field_project_config_settings_group_time_work_estimation_tasks
msgid "Allows you to compute Time Estimation on tasks."
msgstr "Permite calcular estimación de tiempos en las tareas."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Also, lead by example. When your team members see how prepared you are it "
"will  reinforce the need for each of them to be prepared for status meetings."
msgstr ""
"También, lidere con el ejemplo. Cuando los miembros de su equipo ven que tan "
"preparado está se reforzará la necesidad en cada uno de ellos de estar "
"preparados para reuniones de estado."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Alternatively, Timesheets can be added directly from Tasks by activating "
"<strong>'Log work activities on tasks'</strong> in the"
msgstr ""
"Alternativamente, partes de horas se pueden añadir directamente a partir de "
"Tareas activas <strong>\n"
"\"Registrar las actividades de trabajo en tareas '</strong> en el"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"An internal message will not send any email notification, but your message "
"will still be displayed to every user that has access to the page."
msgstr ""
"Un mensaje interno no enviará ninguna notificación por correo electrónico , "
"pero su mensaje se seguirá mostrando a cada usuario que tiene acceso a la "
"página."

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project_name
msgid "Analytic Account"
msgstr "Cuenta analítica"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_line_ids
msgid "Analytic Lines"
msgstr "Líneas analíticas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Another good way to limit the number of notifications you receive is to only "
"follow your Project's 'Task Assigned' events.  Then you'll be notified when "
"a Task or Issue is created, and can  manually decide if you want to be "
"notified for its other events too."
msgstr ""
"Otra buena manera de limitar el número de notificaciones que usted recibe es "
"sólo seguir los eventos de sus 'Tareas Asignadas' del Proyecto. Entonces "
"será notificado cuando una Tarea o Incidencia es creada, y puede decidir "
"manualmente si quiere ser notificado de sus otros eventos también."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_config_settings
msgid "Apply"
msgstr "Aplicar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Archived"
msgstr "Archivado"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user_date_start
msgid "Assignation Date"
msgstr "Fecha Asignación"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Assignation Month"
msgstr "Mes Asignado"

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr "Tareas Asignadas"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user_user_id
msgid "Assigned To"
msgstr "Asignado A"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_user_id
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Assigned to"
msgstr "Asignado(a) a"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_date_assign
msgid "Assigning Date"
msgstr "Fecha de Asignación"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"At each stage employees can block or make task/issue ready for next stage.\n"
"                            You can define here labels that will be "
"displayed for the state instead\n"
"                            of the default labels."
msgstr ""
"En cada etapa los empleados pueden bloquear o hacer tarea / emitir listo "
"para la próxima etapa.\n"
"                            Aquí se puede determinar etiquetas que se "
"mostrarán para el estado en vez\n"
"                            de las etiquetas predeterminadas."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"At the end of the week, each employee should review\n"
"                            their entries for the week and makes sure the\n"
"                            entries are correctly encoded. This can be done\n"
"                            from the"
msgstr ""
"Al final de la semana, cada empleado debe revisar\n"
"                           sus entradas para la semana y se asegura la\n"
"                           las entradas están codificados correctamente.\n"
"                            Esto se puede hacer desde el"

#. module: project
#: code:addons/project/project.py:144
#: model:ir.model.fields,field_description:project.field_project_task_attachment_ids
#, python-format
msgid "Attachments"
msgstr "Adjuntos"

#. module: project
#: selection:project.config.settings,generate_project_alias:0
msgid "Automatically generate an email alias at the project creation"
msgstr "Generar un alias de correo automáticamente en la creación del proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Available on the Apple Store"
msgstr "Disponible en la Tienda de Apple"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Back at the office, the manager splits the customer's requests into several "
"tasks and delegates them to several employees."
msgstr ""
"De vuelta en la oficina, el gerente divide las solicitudes del cliente en "
"varias tareas y delegados a varios empleados."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:31
#: code:addons/project/static/src/js/web_planner_project.js:47
#: code:addons/project/static/src/js/web_planner_project.js:63
#, python-format
msgid "Backlog"
msgstr "Backlog"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_balance
msgid "Balance"
msgstr "Saldo"

#. module: project
#: model:project.task.type,name:project.project_stage_data_1
msgid "Basic"
msgstr "Básico"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Basic Management"
msgstr "Administración Básica"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Be aware of the team’s productivity and time: try to keep meetings to one "
"hour or less."
msgstr ""
"Esté al tanto de la productividad y la hora del equipo: tratar de mantener "
"reuniones de una hora o menos."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
#: selection:project.task,kanban_state:0
#: selection:project.task.history,kanban_state:0
#: selection:project.task.history.cumulative,kanban_state:0
#: selection:report.project.task.user,state:0
msgid "Blocked"
msgstr "Bloqueado(a)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_crossovered_budget_line
msgid "Budget Lines"
msgstr "Líneas de Presupuesto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"But because change is never easy, we've created this Planner to guide you."
"<br/>\n"
"                        For example, you'll understand why you shouldn’t use "
"Odoo to plan but instead to\n"
"                        collaborate, or why organizing your projects by role "
"is wrong."
msgstr ""
"Ya que el cambio nunca es fácil, hemos creado este Planificador para guiarlo."
"<br/>\n"
"Por ejemplo, usted entenderá por qué usted no debería usar Odoo para "
"planear\n"
"sino para colaborar, o por qué organizar sus proyectos por roles es algo "
"equivocado."

#. module: project
#: model:project.task.type,legend_done:project.project_stage_1
msgid "Buzz or set as done"
msgstr "Zumbido o establecer como realizado"

#. module: project
#: model:ir.filters,name:project.filter_task_report_responsible
msgid "By Responsible"
msgstr "Por Responsable"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_config_settings
msgid "Cancel"
msgstr "Cancelar"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:69
#: code:addons/project/static/src/js/web_planner_project.js:84
#: code:addons/project/static/src/js/web_planner_project.js:98
#: selection:project.project,state:0
#: model:project.task.type,name:project.project_stage_3
#, python-format
msgid "Cancelled"
msgstr "Cancelado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Change their Stages in the Project Stages tab"
msgstr "Stages tab Cambiar sus etapas en la pestaña de Etapas del Proyecto"

#. module: project
#: model:ir.model.fields,help:project.field_account_analytic_account_use_tasks
#: model:ir.model.fields,help:project.field_project_project_use_tasks
msgid "Check this box to manage internal activities through this project"
msgstr "Marque esta casilla para manejar actividades internas en este proyecto"

#. module: project
#: code:addons/project/project.py:648
#, python-format
msgid ""
"Child task still open.\n"
"Please cancel or complete child task first."
msgstr ""
"La tarea hija está abierta aún.<br>\n"
"Por favor cancele o complete la tarea hija primero."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:54
#, python-format
msgid "Clear description and purpose"
msgstr "Descripción y propósito claros"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Click to add a new tag."
msgstr "Clic para añadir una nueva etiqueta."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Click to add a stage in the task pipeline."
msgstr "Clic para añadir una etapa en el flujo de tareas."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Click to add/remove from favorite"
msgstr ""

#. module: project
#: model:web.tip,description:project.project_tip_1
msgid "Click to view all the tasks related to this project."
msgstr ""

#. module: project
#: selection:project.project,state:0
msgid "Closed"
msgstr "Cerrado"

#. module: project
#: selection:project.config.settings,module_pad:0
msgid "Collaborative rich text on task description"
msgstr "Texto colaborativo enriquecido en la descripción de la tarea"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_color
#: model:ir.model.fields,field_description:project.field_project_tags_color
#: model:ir.model.fields,field_description:project.field_project_task_color
msgid "Color Index"
msgstr "Índice de colores"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Communication campaign"
msgstr "Campaña de comunicación"

#. module: project
#: model:ir.model,name:project.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_company_id
#: model:ir.model.fields,field_description:project.field_project_task_company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user_company_id
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Company"
msgstr "Compañía"

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account_company_uom_id
#: model:ir.model.fields,field_description:project.field_project_project_company_uom_id
msgid "Company UOM"
msgstr "UdM de Compañía"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Configuration"
msgstr "Configuración"

#. module: project
#: model:ir.actions.act_window,name:project.action_config_settings
msgid "Configure Project"
msgstr "Configurar Proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Congratulations on choosing Odoo Project to help running your company more "
"efficiently!"
msgstr ""
"¡Felicidades por escojer Odoo Proyectos para ayudar a la gestión de su "
"empresa más eficientemente!"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Congratulations, you're done !"
msgstr "¡Enhorabuena, ha Terminado!"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Consulting mission"
msgstr "Misión de Consultoría"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user_partner_id
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Contact"
msgstr "Contacto"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_analytic_account_id
msgid "Contract/Analytic"
msgstr "Contrato/Analítica"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Control projects quality and satisfaction"
msgstr "Control de Proyectos de Calidad y la Satisfacción"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:33
#, python-format
msgid "Copywriting / Design"
msgstr "Redacción / Diseño"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_create_date
msgid "Create Date"
msgstr "Fecha de creación"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Create a Gantt chart with your projects tasks and deadlines"
msgstr "Crear un diagrama Gantt con sus tareas del proyecto y fechas límite"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid "Create a new project."
msgstr "Crear un proyecto nuevo."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Create a task by sending an email to a project alias with one of your "
"colleagues in copy"
msgstr ""
"Crear una tarea enviando un correo al alias del proyecto con uno de sus "
"colegas en copia"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Create at least 3 tasks"
msgstr "Crear al menos 3 tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Create bills automatically based on Time &amp; Material."
msgstr "Crear facturas automáticamente basadas en Tiempo y Material."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Create the Projects"
msgstr "Crear el Proyecto"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_create_uid
#: model:ir.model.fields,field_description:project.field_project_project_create_uid
#: model:ir.model.fields,field_description:project.field_project_tags_create_uid
#: model:ir.model.fields,field_description:project.field_project_task_create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_create_date
#: model:ir.model.fields,field_description:project.field_project_project_create_date
#: model:ir.model.fields,field_description:project.field_project_tags_create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_create_date
msgid "Created on"
msgstr "Creado en"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Creating Tasks and/or Issues is the next step in managing your Projects.<br/"
">\n"
"                        In Odoo, it is pretty straightforward, but here are "
"some explanations you may find useful."
msgstr ""
"Creación de tareas y / o salidas es el siguiente paso en la gestión de sus "
"proyectos.<br/>\n"
"                        En Openerp, es bastante sencillo, pero aquí hay "
"algunas explicaciones que pueden ser útiles."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Creation Date"
msgstr "Fecha creación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_credit
msgid "Credit"
msgstr "Haber"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_history_cumulative
#: model:ir.actions.act_window,name:project.action_view_task_history_cumulative_filter
#: model:ir.ui.menu,name:project.menu_action_view_task_history_cumulative
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Cumulative Flow"
msgstr "Flujo Acumulado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_currency_id
msgid "Currency"
msgstr "Moneda"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Current Timesheet"
msgstr "Parte de horas actual"

#. module: project
#: code:addons/project/project.py:868
#: model:ir.model.fields,field_description:project.field_project_project_partner_id
#: model:ir.model.fields,field_description:project.field_project_task_partner_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: project
#: code:addons/project/project.py:868
#, python-format
msgid "Customer Email"
msgstr "Correo del Cliente"

#. module: project
#: code:addons/project/project.py:132
#, python-format
msgid "Customer Project: visible in portal if the customer is a follower"
msgstr "Proyecto de Cliente: visible en el portal si el cliente es un seguidor"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Customer Service"
msgstr "Servicio al cliente"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:73
#, python-format
msgid "Customer feedback has been requested"
msgstr "Comentarios del cliente han sido solicitados"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:91
#, python-format
msgid "Customer has cancelled repair"
msgstr "El ciente ha cancelado la reparación"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:71
#, python-format
msgid "Customer has reported new issue"
msgstr "Cliente ha informado de nueva emisión"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:70
#, python-format
msgid "Customer service has found new issue"
msgstr "El servicio al cliente ha encontrado un nuevo tema"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Customer support tickets"
msgstr "Tickets de soporte al cliente"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Customization"
msgstr "Personalización"

#. module: project
#: model:ir.ui.menu,name:project.menu_projects
msgid "Dashboard"
msgstr "Tablero"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_date
#: model:ir.model.fields,field_description:project.field_project_task_history_date
msgid "Date"
msgstr "Fecha"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user_opening_days
msgid "Days to Assign"
msgstr "Dias a Asignar"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user_closing_days
msgid "Days to Close"
msgstr "Días para el Cierre"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user_date_deadline
msgid "Deadline"
msgstr "Fecha límite"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_debit
msgid "Debit"
msgstr "Debe"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_defaults
msgid "Default Values"
msgstr "Valores Predeterminados"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or "
"issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"Defina los pasos que empleara en el proyecto desde la\n"
"creación de la tarea, hasta cerrar la tarea o el problema.\n"
"Puedes crear estas etapas con el fin de seguir el progreso \n"
"de la resolución de tareas."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Depending on what you need and how you want to operate, there are several "
"ways to work with Odoo. First, decide if you want to think in terms of tasks "
"or issues. Then, activate the Timesheets app if you need it."
msgstr ""
"Dependiendo en lo que usted necesita y cómo quiere operar, hay muchas "
"maneras de trabajar con Odoo. Primero, decida si quiere pensar en términos "
"de tareas o incidencias. Luego, active la app de Partes de Horas si la "
"necesita."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Deploy"
msgstr "Desplegar"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:19
#, python-format
msgid "Deployment"
msgstr "Despliegue"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_description
#: model:ir.model.fields,field_description:project.field_project_task_type_description
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "Descripción"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:17
#, python-format
msgid "Development"
msgstr "Desarrollo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Development Process"
msgstr "Proceso de Desarrollo"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project.js:50
#, python-format
msgid "Discard"
msgstr "Descartado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_display_name
#: model:ir.model.fields,field_description:project.field_project_project_display_name
#: model:ir.model.fields,field_description:project.field_project_tags_display_name
#: model:ir.model.fields,field_description:project.field_project_task_display_name
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_display_name
#: model:ir.model.fields,field_description:project.field_project_task_history_display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_displayed_image_id
msgid "Displayed Image"
msgstr "Imagen Mostrada"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:34
#, python-format
msgid "Distribute"
msgstr "Distribuir"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:42
#, python-format
msgid "Distribution is completed"
msgstr "Distribución completada"

#. module: project
#: selection:project.config.settings,generate_project_alias:0
msgid "Do not create an email alias automatically"
msgstr "No crear un alias de correo automáticamente"

#. module: project
#: selection:project.config.settings,group_time_work_estimation_tasks:0
msgid "Do not estimate working time on tasks"
msgstr "No estimar el tiempo de trabajo en las tareas"

#. module: project
#: selection:project.config.settings,module_project_issue_sheet:0
msgid "Do not track working hours on issues"
msgstr "No hacer seguimiento a las horas de trabajo en incidencias"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:50
#, python-format
msgid "Documentation"
msgstr "Documentación"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Documents"
msgstr "Documentos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Don't create a Project for different locations (this could isolate teams "
"that work at different locations)."
msgstr ""
"No cree un Proyecto para diferentes ubicaciones (esto puede aislar a los "
"equipos que trabajan en diferentes lugares)."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Don't create a Project for each of your customers - this will be too "
"complicated to manage properly."
msgstr ""
"No cree un proyecto para cada uno de sus clientes - sería demasiado "
"complicado de gestionar adecuadamente"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Don't hesitate to"
msgstr "No dude en"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Don't hesitate to select only the events you are interested in!"
msgstr "¡No dude en seleccionar solo los eventos que le interesan!"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:35
#: code:addons/project/static/src/js/web_planner_project.js:68
#: code:addons/project/static/src/js/web_planner_project.js:83
#: code:addons/project/static/src/js/web_planner_project.js:97
#: model:project.task.type,name:project.project_stage_2
#, python-format
msgid "Done"
msgstr "Realizado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"During a meeting, a customer asks a manager for a few modifications to a "
"project."
msgstr ""
"Durante una reunión, un cliente pide un gerente de algunas modificaciones a "
"un proyecto."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Each employee will have his own task, while the manager will be able to "
"follow the global progress in the Kanban view of the project."
msgstr ""
"Cada empleado tiene su propia tarea, mientras que el gerente será capaz de "
"seguir el progreso global en la vista Kanban del proyecto."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Edit Task"
msgstr "Editar Tarea"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Email Alias"
msgstr "Alias de Correo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_mail_template_id
#, fuzzy
msgid "Email Template"
msgstr "Alias de Correo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Emails"
msgstr "Correo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "End"
msgstr "Fin"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_end_date
#: model:ir.model.fields,field_description:project.field_project_task_history_end_date
msgid "End Date"
msgstr "Fecha final"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user_date_end
msgid "Ending Date"
msgstr "Fecha final"

#. module: project
#: constraint:project.task:0
msgid "Error ! Task starting date must be lower than its ending date."
msgstr ""
"¡Error! La fecha de inicio de la tarea debe ser previa a la de finalización."

#. module: project
#: constraint:project.task:0
msgid "Error ! You cannot create recursive tasks."
msgstr "Error! No puede crear tareas recursivas."

#. module: project
#: constraint:project.project:0
msgid "Error! project start-date must be lower than project end-date."
msgstr ""
"¡Error! La fecha de inicio del proyecto debe ser anterior a la fecha final "
"del proyecto"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_planned_hours
msgid ""
"Estimated time to do the task, usually set by the project manager when the "
"task is in draft state."
msgstr ""
"Tiempo estimado para realizar la tarea, usualmente fijado por el gerente del "
"proyecto cuando la tarea está en estado borrador"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Even if there is no specific field in Odoo, it's important to define a "
"person responsible for each stage of your Project.<br/>\n"
"                        This person will have the responsibility for "
"validating each stage and ensuring that the requirements to move to the next "
"stage are met."
msgstr ""
"Incluso si no hay un campo específico en Openerp, es importante definir a "
"una persona responsable de cada etapa de su proyecto<br/>\n"
"                        Esta persona tendrá la responsabilidad de validar "
"cada etapa y asegurar que los requisitos pasen a la siguiente etapa y se "
"cumplan."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Every business is different.<br/>\n"
"                    Odoo allows you to customize every application and it's "
"usually a good idea to customize screens to fit your project needs."
msgstr ""
"Cada negocio es diferente.<br/>\n"
"                    Openerp le permite personalizar cada aplicación y por lo "
"general es una buena idea para personalizar las pantallas para adaptarlos a "
"las necesidades del proyecto."

#. module: project
#: model:web.tip,description:project.project_tip_3
msgid ""
"Every event on a task is logged in this section. Send a new message to "
"notify followers or log an internal note."
msgstr ""
"Cada evento en una tarea se registra en esta sección. Envíe un nuevo mensaje "
"para notificar a los seguidores o ingrese una nota interna."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Examples"
msgstr "Ejemplos"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:74
#, python-format
msgid "Expert advice has been requested"
msgstr "Solicitar Asesoramiento de Expertos."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_date
msgid "Expiration Date"
msgstr "Fecha de Caducidad"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type_legend_priority
msgid ""
"Explanation text to help users using the star and priority mechanism on "
"stages or issues that are in this stage."
msgstr ""
"Texto explicativo para ayudar a los usuarios usando la estrella y el "
"mecanismo de prioridades en etapas o incidencias que están en esta etapa."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Extended Filters"
msgstr "Filtros Avanzados"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "Información extra"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_config_settings
msgid "Extra features"
msgstr "Características extra"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Extra useful for when you're with a customer or in a meeting."
msgstr "Muy útil para cuando estás con un cliente o en una reunión."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Favorite"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:88
#, python-format
msgid "Feedback from customer requested"
msgstr "Comentarios del usuario solicitados"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:26
#, python-format
msgid "Finally task is deployed"
msgstr "Las tareas finales están desarrolladas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_fold
msgid "Folded in Tasks Pipeline"
msgstr "Plegado en el Canal de Tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr ""
"Seguir este proyecto para rastrear automáticamente los eventos asociados a "
"las tareas y a las incidencias del mismo."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Followed by Me"
msgstr "Seguido por mí"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "For employees, the"
msgstr "Para los empleados, la"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"For example, risk and issue owners should come prepared to share the status "
"of their item and, ideally, a path to resolution."
msgstr ""
"Por ejemplo, los propietarios del riesgo y emisión deben venir preparados "
"para compartir el estado de su elemento y  de ser posible, un camino hacia "
"una solución."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""
"De parte del equipo Odoo,<br/>\n"
"Fabien Pinckaers, Fundador"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"For the same reason, don't create a Project based on weeks or time (example: "
"Scrum)."
msgstr ""
"Por el mismo motivo, no cree proyectos en base a semanas o tiempo (p.e., "
"Scrum)."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_config_settings
msgid "Forecasts"
msgstr "Proyecciones"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_module_project_forecast
msgid "Forecasts, planning and Gantt charts"
msgstr "Proyecciones, planeación y diagramas de Gantt"

#. module: project
#: model:ir.ui.menu,name:project.menu_tasks_config
msgid "GTD"
msgstr "GTD"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Generate a timesheet report to attach to your customer invoices"
msgstr ""
"Generar un informe de parte de horas para adjuntar a las facturas al cliente"

#. module: project
#: selection:project.config.settings,module_sale_service:0
msgid "Generate tasks from sale orders"
msgstr "Generar tareas desde las órdenes de venta"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Get full synchronization with Odoo"
msgstr "Obtener la sincronización completa con Odoo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Get it on Google Play"
msgstr "Consigalo en Google Play"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Get more apps"
msgstr "Obtener más aplicaciones"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_label_tasks
msgid "Gives label to tasks on project's kanban view."
msgstr "De etiqueta para tareas en vista Kanban del proyecto."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_sequence
msgid "Gives the sequence order when displaying a list of Projects."
msgstr "Indica el orden de secuencia cuando se muestra una lista de Proyectos."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_sequence
msgid "Gives the sequence order when displaying a list of tasks."
msgstr "Indica el orden de las tareas al mostrarlas en la lista."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Good luck!"
msgstr "Buena suerte!"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Green: the Task is ready for next stage (the job for this stage is complete)"
msgstr ""
"Verde: la tarea está lista para la siguiente etapa (el trabajo en esta etapa "
"se ha completado)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Grey: the Task is in progress (someone is working on it)"
msgstr "Gris: la tarea está en curso (hay alguien trabajando en ella)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Group By"
msgstr "Agrupar por"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_config_settings
msgid "Helpdesk & Support"
msgstr "Mesa de ayuda y Soporte"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Here are some of the <strong>available customizations</strong>"
msgstr "Éstos son algunos de los <strong> personalizaciones disponibles"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
msgid "Here, you can create new tasks"
msgstr "Aquí, puedes crear nuevas tareas"

#. module: project
#: selection:project.task,priority:0
#: selection:report.project.task.user,priority:0
msgid "High"
msgstr "Alta"

#. module: project
#: model:ir.model,name:project.model_project_task_history
msgid "History of Tasks"
msgstr "Histórico de Tareas"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_privacy_visibility
msgid ""
"Holds visibility of the tasks or issues that belong to the current project:\n"
"- Portal : employees see everything;\n"
"   if portal is activated, portal users see the tasks or issues followed by\n"
"   them or by someone of their company\n"
"- Employees Only: employees see all tasks or issues\n"
"- Followers Only: employees see only the followed tasks or issues; if "
"portal\n"
"   is activated, portal users see the followed tasks or issues."
msgstr ""
"Mantiene la visibilidad de las tareas o cuestiones que pertenecen al "
"proyecto actual:\n"
"- Portal: empleados ven todo;\n"
" Si se activa el portal , los usuarios del portal ver las tareas o temas "
"seguidos de   \n"
"ellos o por alguien de su empresa\n"
"-Sólo los empleados : los empleados ven todas las tareas o cuestiones\n"
"-Sólo los seguidores : los empleados ven sólo las tareas seguido o "
"problemas ; Si portal\n"
"   está activada, los usuarios del portal ver las tareas o temas seguidos ."

#. module: project
#: code:addons/project/project.py:799
#, python-format
msgid "I take it"
msgstr "Lo cojo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_id
#: model:ir.model.fields,field_description:project.field_project_project_id
#: model:ir.model.fields,field_description:project.field_project_tags_id
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_id
#: model:ir.model.fields,field_description:project.field_project_task_history_id
#: model:ir.model.fields,field_description:project.field_project_task_id
#: model:ir.model.fields,field_description:project.field_project_task_type_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user_id
msgid "ID"
msgstr "ID"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task "
"creation alias)"
msgstr ""
"ID del registro padre que tiene el alias. (ejemplo: el proyecto que guarda "
"el alias para la creación de tareas)"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:103
#, python-format
msgid "Idea has been transformed into concrete actions"
msgstr "La Idea ha sido transformada en acciones concretas"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:102
#, python-format
msgid "Idea is fully explained"
msgstr "La idea ha sido explicada completamente"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Ideally, a person should only be responsible for one project."
msgstr "Idealmente, una persona solo debe ser responsable de un proyecto."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:95
#, python-format
msgid "Ideas"
msgstr "Ideas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Identify problems and blocking points more easily"
msgstr "Identificar los problemas y los puntos de bloqueo con más facilidad"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type_mail_template_id
msgid ""
"If set an email will be sent to the customer when the task or issue reaches "
"this step."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project_active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr "Si el proyecto no está activo, permanecerá oculto sin ser eliminado."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"If you don't want to receive email notifications, you can uncheck the option "
"in your"
msgstr ""
"Si no quiere recibir correos de notificación, puede desmarcar la opción en su"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"If you want to limit access for certain users or customers, simply use the "
"Privacy / Visibility settings in the Project Settings."
msgstr ""
"Si desea limitar el acceso a determinados usuarios o clientes , basta con "
"utilizar la configuración de privacidad / visibilidad en la configuración "
"del proyecto ."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"If you work on a Time &amp; Material project, you'll probably want to "
"extract a Timesheet of the tasks and issues to invoice directly to the "
"customer. To do that:"
msgstr ""
"Si usted trabaja en un proyecto de Tiempo y Material, probablemente quiera "
"extraer un Parte de Horas de las tareas e incidencias a facturar "
"directamente al usuario. Para hacer eso:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Implement"
msgstr "Implementar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Improve"
msgstr "Mejorar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Improve collaboration with customers"
msgstr "Mejorar la colaboración con los clientes"

#. module: project
#: selection:project.project,state:0 selection:project.task,kanban_state:0
#: model:project.task.type,name:project.project_stage_1
#: selection:report.project.task.user,state:0
msgid "In Progress"
msgstr "En proceso"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:32
#: code:addons/project/static/src/js/web_planner_project.js:65
#: code:addons/project/static/src/js/web_planner_project.js:80
#, python-format
msgid "In progress"
msgstr "En proceso"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:79
#, python-format
msgid "Incoming"
msgstr "Entrada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Incoming Emails create"
msgstr "Crear correos electrónicos de entrada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_planned_hours
msgid "Initially Planned Hours"
msgstr "Horas planeadas inicialmente"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"Correo electrónico interno asociado con este proyecto. Los correos "
"electrónicos entrantes se sincronizan automáticamente con las tareas (u "
"opcionalmente con los problemas si se ha instalado el módulo de control de "
"incidencias ) ."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Internal notes are messages that will appear in the Chatter but will not be "
"notified in Odoo's Inbox."
msgstr ""
"Las notas internas son mensajes que aparecerán en el Chatter pero no será "
"notificado en el Inbox de Odoo."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Issue Tracking app."
msgstr "Seguimiento de asuntos de incidencias."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "Versión"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:72
#, python-format
msgid "Issue is being worked on"
msgstr "Incidencia en la que se está trabajando"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:75
#, python-format
msgid "Issue is resolved"
msgstr "Incidencia resuelta"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Issues"
msgstr "Incidencias"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Issues analysis"
msgstr "Análisis de incidencias"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"It is better to start with a \"project answer\", such as: \"We are two weeks "
"late\", \"We are at planned budget\" or \"We are 50% complete with the "
"process model\". Also if you can, start the meeting on a positive note, such "
"as milestones that have been met or are ahead of schedule. This will make "
"participants feel motivated to engage in the conversation."
msgstr ""
"Es mejor empezar con una \" respuesta proyecto\", tales como: \" Estamos a "
"dos semanas de retraso \", \"Estamos en presupuesto previsto \" o \" Somos "
"el 50 % completo con el modelo de proceso \" . Además, si se puede , iniciar "
"la sesión con una nota positiva, tales como hitos que se han alcanzado o "
"están por delante de lo previsto. Esto hará que los participantes se sientan "
"motivados para participar en la conversación ."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"It is time to think about how you will transform your activities into real "
"projects in Odoo.<br/>\n"
"                        For that, the most important part is defining the "
"stages of your projects. Stages are the different steps a task or an issue "
"can go through, from its creation to its ending. They will appear in what we "
"call the 'Kanban' view of your projects."
msgstr ""
"Es tiempo de pensar en cómo va a transformar sus actividades en proyectos "
"reales en Openerp.<br/>\n"
"                       \n"
"Para ello, la parte más importante es la definición de las etapas de sus "
"proyectos. Las etapas son las diferentes etapas de una tarea o un problema "
"puede ir desde su creación hasta su final. Aparecerán en lo que llamamos la "
"vista ' Kanban ' de sus proyectos "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"It's essential to be clear about why you want to use Odoo Project and what "
"your goals are.\n"
"                        Indeed, there are many ways to manage a project, to "
"find the best one for you, you need to know exactly what you want to "
"achieve. And later on, we will hopefully transform your objectives into real "
"improvements for your company."
msgstr ""
"Es esencial ser claro por qué desea utilizar Odoo y cuáles son sus "
"objetivos.\n"
"De hecho, hay muchas maneras de gestionar un proyecto, para encontrar el "
"mejor para usted, usted necesita saber exactamente lo que quiere lograr. Y "
"más adelante , vamos a transformar con suerte sus objetivos en mejoras "
"reales para su empresa."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"It's for logging every change, event or message related to the Document."
msgstr ""
"Es para registrar cada cambio, evento o mensaje relacionado al Documento."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"It's usually a good idea to take time to analyze your tasks and issues once "
"a year. Here are some KPIs you should take a look at. Ask yourself 'How can "
"they be improved?'"
msgstr ""
"Por lo general es una buena idea tomar tiempo para analizar sus tareas y los "
"problemas una vez al año . Aquí están algunos indicadores clave de "
"rendimiento que debe tomar un vistazo a. Pregúntese \" ¿Cómo pueden "
"mejorarse? '"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_legend_blocked
#: model:ir.model.fields,field_description:project.field_project_task_type_legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Explicación bloqueada Kanban"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_legend_normal
#: model:ir.model.fields,field_description:project.field_project_task_type_legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Explicación en marcha Kanban"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Kanban Stage"
msgstr "Etapa Kanban"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Kanban Stages"
msgstr "Etapas Kanban"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_kanban_state
#: model:ir.model.fields,field_description:project.field_project_task_history_kanban_state
#: model:ir.model.fields,field_description:project.field_project_task_kanban_state
msgid "Kanban State"
msgstr "Estado Kanban"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_legend_done
#: model:ir.model.fields,field_description:project.field_project_task_type_legend_done
msgid "Kanban Valid Explanation"
msgstr "Explicación válida Kanban"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Keep track of messages and conversations"
msgstr "Realizar un seguimiento de mensajes y conversaciones"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Know what my employees are working on"
msgstr "Saben en que mis empleados están trabajando"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Last Message"
msgstr "Último Mensaje"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_write_date
msgid "Last Modification Date"
msgstr "Última fecha de modificación"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings___last_update
#: model:ir.model.fields,field_description:project.field_project_project___last_update
#: model:ir.model.fields,field_description:project.field_project_tags___last_update
#: model:ir.model.fields,field_description:project.field_project_task___last_update
#: model:ir.model.fields,field_description:project.field_project_task_history___last_update
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative___last_update
#: model:ir.model.fields,field_description:project.field_project_task_type___last_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user_date_last_stage_update
msgid "Last Stage Update"
msgstr "Última Actualización de la Etapa"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_write_uid
#: model:ir.model.fields,field_description:project.field_project_project_write_uid
#: model:ir.model.fields,field_description:project.field_project_tags_write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_write_uid
#: model:ir.model.fields,field_description:project.field_project_task_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_write_date
#: model:ir.model.fields,field_description:project.field_project_project_write_date
#: model:ir.model.fields,field_description:project.field_project_tags_write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: project
#: model:web.planner,tooltip_planner:project.planner_project
msgid ""
"Learn how to better organize your company using Projects, Tasks, Issues and "
"Timesheets."
msgstr ""
"Aprender a organizar mejor su empresa mediante proyectos, tareas, novedades "
"y hojas de registro de tiempo. 333"

#. module: project
#: model:ir.model.fields,help:project.field_project_config_settings_module_pad
msgid ""
"Lets the company customize which Pad installation should be used to link to "
"new pads (for example: http://ietherpad.com/).\n"
"-This installs the module pad."
msgstr ""
"Permite personalizar a nivel de compañía que instalación de Pad debe usarse "
"para gestionar los nuevos pads (por ejemplo: https://ietherpad.com/). \n"
"Esto instala el módulo pad."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_analytic_account_id
msgid ""
"Link this project to an analytic account if you need financial management on "
"projects. It enables you to connect projects with budgets, planning, cost "
"and revenue analysis, timesheets on projects, etc."
msgstr ""
"Enlace este proyecto a una cuenta analítica si necesita la gestión "
"financiera de los proyectos. Le permite conectar los proyectos con "
"presupuestos, planificación, análisis de costos e ingresos, tiempo dedicado "
"en los proyectos, etc."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "List, plan and track things to do"
msgstr "Listar, planificar y ejecutar el seguimiento de cosas pendientes"

#. module: project
#: selection:report.project.task.user,priority:0
msgid "Low"
msgstr "Baja"

#. module: project
#: selection:project.config.settings,group_time_work_estimation_tasks:0
msgid "Manage time estimation on tasks"
msgstr "Gestionar estimación de tiempo en las tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model:res.groups,name:project.group_project_manager
msgid "Manager"
msgstr "Director"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Managing a group of people, a team or a department (example: R&amp;D team, "
"HR Department, etc.)"
msgstr ""
"Administrando un grupo de personas, un equipo o un departamento (ej. Equipo I"
"+D, Departamento RR.HH., etc.)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Managing long projects that span over many months and/or need Timesheets."
msgstr ""
"La gestión de proyectos largos que se extienden a lo largo de muchos meses "
"y / o necesitan partes de horas."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Managing notifications is essential: too few and you risk missing critical "
"information, too many and you will be  overloaded with unnecessary "
"information. The trick is to find the right balance between the projects, "
"stages and tasks you want to be informed about. Fortunately, Odoo Project "
"has many levels of notifications and messages you can choose from."
msgstr ""
"Administrar las notificaciones es esencial: muy pocas y usted corre el "
"riesgo de no enterarse de información crítica, y con muchas será "
"sobrecargado de información innecesaria. El truco es encontrar el equilibrio "
"perfecto entre los proyectos, etapas y tareas de las que quiere ser "
"informado. Afortunadamente, los Proyectos de Odoo tiene muchos niveles de "
"notificaciones y mensajes de donde usted puede escoger."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Marketing Department"
msgstr "Departamento de Mercadeo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_favorite_user_ids
msgid "Members"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
msgid "Month"
msgstr "Mes"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "More <i class=\"fa fa-caret-down\"/>"
msgstr "Más <i class=\"fa fa-caret-down\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "More efficient communication between employees"
msgstr "Una comunicación más eficiente entre los empleados"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
msgid "My Projects"
msgstr "Mis Proyectos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "My Tasks"
msgstr "Mis Tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags_name
msgid "Name"
msgstr "Nombre"

#. module: project
#: model:project.task.type,legend_blocked:project.project_stage_1
msgid "Need functional or technical help"
msgstr "Necesita ayuda funcional o técnica"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:64
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: selection:project.project,state:0
#: model:project.task.type,name:project.project_stage_data_0
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: project
#: code:addons/project/project.py:801
#, python-format
msgid "New Task"
msgstr "Nueva Tarea"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:86
#, python-format
msgid "New repair added"
msgstr "Adicionar Nueva reparación"

#. module: project
#: selection:project.config.settings,module_sale_service:0
msgid "No automatic task creation"
msgstr "Sin creación automática de tareas"

#. module: project
#: selection:project.config.settings,module_rating_project:0
msgid "No customer rating"
msgstr "Sin calificación del cliente"

#. module: project
#: selection:project.task,priority:0
#: selection:project.task.history,kanban_state:0
#: selection:project.task.history.cumulative,kanban_state:0
#: selection:report.project.task.user,priority:0
msgid "Normal"
msgstr "Normal"

#. module: project
#: model:project.task.type,legend_blocked:project.project_stage_0
msgid "Not validated"
msgstr "No validada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_notes
msgid "Notes"
msgstr "Notas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Notifications"
msgstr "Notificaciones"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user_opening_days
msgid "Number of Days to Open the task"
msgstr "Número de Días para Abrir la tarea"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user_closing_days
msgid "Number of Days to close the task"
msgstr "Número de días para cerrar la tarea."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_doc_count
msgid "Number of documents attached"
msgstr "Número de documentos adjuntos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Odoo Project is a super fast and easy way to make your activities and tasks "
"visible to\n"
"                        everyone in your company. Follow how things "
"progress, see when things are stuck, know\n"
"                        who's in charge, all in one place."
msgstr ""
"Odoo es una manera muy rápida y fácil de hacer sus actividades y tareas "
"visible para\n"
"todos en su compañía . Hacer seguimiento de cómo progresan las cosas, ver "
"cuando las\n"
"cosas están atascados, saber quien está a cargo, todo en un solo lugar."

#. module: project
#: model:ir.model.fields,help:project.field_project_config_settings_generate_project_alias
msgid ""
"Odoo will generate an email alias at the project creation from project name."
msgstr ""
"Odoo generará un alias de correo en la creación del proyecto basado en el "
"nombre."

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_task
msgid ""
"Odoo's project management allows you to manage the pipeline of your tasks "
"efficiently. You can track progress, discuss on tasks, attach documents, etc."
msgstr ""
"La administración de proyectos de Odoo le permite manejar el canal de "
"trabajo de sus tareas eficientemente. Usted puede hacer seguimiento del "
"progreso, comentarios, documentos adjuntos, etc."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Once a Timesheet is confirmed by an employee, it needs to be Approved by a "
"manager in the"
msgstr ""
"Una vez un Parte de Horas es confirmado por un empleado, este necesita ser "
"Aprobado por un administrador en el"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Open"
msgstr "Abierto"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID opcional de un hilo (registro) al cual se le adjuntarán todos los "
"mensajes entrantes, incluso si no fueron respuestas del mismo. Si se fija, "
"esto deshabilitará completamente la creación de nuevos registros."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Or, if you are using Issues, by activating 'Activate timesheets on issues', "
"also in the"
msgstr ""
"O, si usted está usando Incidencias, habilitando 'Activar partes de horas en "
"incidencias', también en el"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Organize meetings"
msgstr "Organizar reuniones"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Organize your activities (plan tasks, track issues, invoice timesheets) for "
"internal, personal or customer projects."
msgstr ""
"Organice sus actividades (planee tareas, rastree incidencias, facture partes "
"de horas) para proyectos internos, personales o de clientes."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Organize your company, from personal tasks to collaborative meeting minutes."
msgstr ""
"Organizar su empresa, desde tareas personales para colaboración actas de las "
"reuniones."

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user_delay_endings_days
msgid "Overpassed Deadline"
msgstr "Fecha Límite Excedida"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "Tareas Sobrepasadas"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type_legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection, when the task or issue is in that stage."
msgstr ""
"Sustituir el valor predeterminado que se muestra para el estado de bloqueo "
"para la selección de Kanban, cuando la tarea o asunto está en esa etapa ."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type_legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection, when the task or issue is in that stage."
msgstr ""
"Sustituir el valor predeterminado que se muestra el estado de hecho para la "
"selección de Kanban, cuando la tarea o asunto está en esa etapa ."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type_legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection, when the task or issue is in that stage."
msgstr ""
"Sustituir el valor predeterminado que se muestra para el estado normal para "
"la selección de Kanban, cuando la tarea o asunto está en esa etapa."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_user_id
msgid "Owner"
msgstr "Propietario"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_module_pad
msgid "Pads"
msgstr "Pads"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_parent_model_id
msgid "Parent Model"
msgstr "Modelo Padre"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID del Hilo del Registro Padre"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_parent_ids
msgid "Parent Tasks"
msgstr "Tareas Hijas"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not "
"necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"El modelo padre que tiene el alias. El modelo que guarda la referencia al "
"alias no es necesariamente el modelo dado por alias_model_id (ejemplo: "
"proyecto (modelo padre) y tarea (modelo))"

#. module: project
#: model:ir.model,name:project.model_res_partner
msgid "Partner"
msgstr "Empresa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: selection:project.project,state:0
msgid "Pending"
msgstr "Pendiente"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Plan your activities for the day"
msgstr "Planificar sus actividades para el día"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_planned_hours
#: model:ir.model.fields,field_description:project.field_project_task_history_planned_hours
msgid "Planned Time"
msgstr "Tiempo Planeado"

#. module: project
#: model:ir.model,name:project.model_web_planner
msgid "Planner"
msgstr "Planificador"

#. module: project
#: code:addons/project/project.py:961
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""
"Por favor, elimine las tareas existentes en el proyecto relacionado con las "
"cuentas que desea eliminar."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_contact
#, fuzzy
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following "
"channels\n"
msgstr ""
"Política para enviar un mensaje en el documento usando la pasarela de "
"correo.\n"
"- todos: todos pueden enviar\n"
"- contactos: sólo contactos autenticados\n"
"- seguidores: sólo los seguidores del documento relacionado\n"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Prepare"
msgstr "Preparar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user_priority
msgid "Priority"
msgstr "Prioridad"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_legend_priority
msgid "Priority Management Explanation"
msgstr "Explicación de la Administración de Prioridades"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_privacy_visibility
msgid "Privacy / Visibility"
msgstr "Privacidad / Visibilidad"

#. module: project
#: code:addons/project/project.py:134
#, python-format
msgid "Private Project: followers only"
msgstr "Proyecto Privado: sólo seguidores"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Product or software version"
msgstr "Producto o versión de software"

#. module: project
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_project_id
#: model:ir.model.fields,field_description:project.field_project_task_project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user_project_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model:res.request.link,name:project.req_link_project
msgid "Project"
msgstr "Proyecto"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_generate_project_alias
msgid "Project Alias"
msgstr "Alias del Proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_config_settings
msgid "Project Management"
msgstr "Gestión de proyectos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_user_id
#: model:ir.model.fields,field_description:project.field_project_task_manager_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "Gerente de Proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Name"
msgstr "Nombre del Proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Project Settings"
msgstr "Configuración del Proyecto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Project Settings."
msgstr "Configuración del Proyecto."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_history_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_history_pivot
msgid "Project Tasks"
msgstr "Tareas del Proyecto"

#. module: project
#: model:res.request.link,name:project.req_link_task
msgid "Project task"
msgstr "Tarea del proyecto"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "Tareas del proyecto"

#. module: project
#: code:addons/project/project.py:309
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.model.fields,field_description:project.field_account_analytic_account_project_ids
#: model:ir.model.fields,field_description:project.field_project_project_project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_project_ids
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.portal_services_projects
#: model_terms:ir.ui.view,arch_db:project.analytic_account_inherited_form
#: model_terms:ir.ui.view,arch_db:project.task_company
#: model_terms:ir.ui.view,arch_db:project.view_project
#, python-format
msgid "Projects"
msgstr "Proyectos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Projects using this stage"
msgstr "Proyectos usando esta etapa"

#. module: project
#: model:ir.model.fields,help:project.field_project_config_settings_module_project_issue_sheet
msgid ""
"Provides timesheet support for the issues/bugs management in project.\n"
"-This installs the module project_issue_sheet."
msgstr ""
"Soporta partes de horas para la gestión de incidencias/errores en los "
"proyectos.\n"
"-Esto instala el módulo project_issue_sheet."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_module_rating_project
msgid "Rating"
msgstr "Calificación"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
msgid "Ready"
msgstr "Preparado"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:40
#, python-format
msgid "Ready for layout / copywriting"
msgstr "Listo para su diseño / redacción"

#. module: project
#: selection:project.task,kanban_state:0
#: selection:project.task.history,kanban_state:0
#: selection:project.task.history.cumulative,kanban_state:0
#: selection:report.project.task.user,state:0
msgid "Ready for next stage"
msgstr "Lista para la siguiente fase/etapa"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:58
#, python-format
msgid "Ready for release"
msgstr "Listo para lanzamiento"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:56
#, python-format
msgid "Ready for testing"
msgstr "Listo para pruebas"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:41
#, python-format
msgid "Ready to be displayed, published or sent"
msgstr "Listo para ser mostrado, publicado o enviado"

#. module: project
#: model:project.task.type,legend_done:project.project_stage_3
msgid "Ready to reopen"
msgstr "Listo para ser reabiertos"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:76
#: code:addons/project/static/src/js/web_planner_project.js:105
#, python-format
msgid "Reason for cancellation has been documented"
msgstr "Las razones para la cancelación han sido documentadas"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of ${object.name}"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID del Hilo del Registro"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Red: the Task is blocked (there's a problem)"
msgstr "Rojo: la tarea está bloqueada (existe un problema)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_code
msgid "Reference"
msgstr "Referencia"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:51
#, python-format
msgid "Release"
msgstr "Lanzamiento"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_remaining_hours
msgid "Remaining Hours"
msgstr "Tiempo Restante"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_remaining_hours
#: model:ir.model.fields,field_description:project.field_project_task_history_remaining_hours
msgid "Remaining Time"
msgstr "Tiempo Restante"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project.js:48
#, python-format
msgid "Remove Cover Image"
msgstr "Eliminar Imagen de Portada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Repair Workshop"
msgstr "Taller de reparación"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:87
#, python-format
msgid "Repair has started"
msgstr "Reparación ha iniciado"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:90
#, python-format
msgid "Repair is completed"
msgstr "La reparación está completa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Reporting"
msgstr "Informes"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:89
#, python-format
msgid "Request for parts has been sent"
msgstr "Requerimiento de piezas ha sido enviado."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Responsibilities"
msgstr "Responsabilidades"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Responsibility"
msgstr "Responsabilidad"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_user_id
#: model:ir.model.fields,field_description:project.field_project_task_history_user_id
msgid "Responsible"
msgstr "Responsable"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Runs outside Odoo, always available"
msgstr "Se ejecuta fuera de Odoo, siempre disponible"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_module_sale_service
msgid "Sale Service"
msgstr "Servicio de venta"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Scrum Methodology"
msgstr "Metodología Scrum"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_management
msgid "Search"
msgstr "Búsqueda"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "Buscar Proyecto"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project.js:46
#, python-format
msgid "Select"
msgstr "Seleccionar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Send an alert when a task is stuck in red for more than a few days"
msgstr ""
"Enviar una alerta cuando una tarea se ha quedado atascado en rojo durante "
"algunos días"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Send an automatic confirmation to all issue emails sent to your customer "
"support"
msgstr ""
"Enviar una confirmación automática a todos los correos de incidencia "
"enviados a su soporte de cliente"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_sequence
#: model:ir.model.fields,field_description:project.field_project_task_sequence
#: model:ir.model.fields,field_description:project.field_project_task_type_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Service Level Agreement (SLA)"
msgstr "Acuerdo de nivel de servicio (Service Level Agreement - SLA)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "Fijar Imagen de Portada"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project.js:45
#, python-format
msgid "Set a Cover Image"
msgstr "Fijar una Imagen de Portada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "Configuración"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Severity"
msgstr "Severidad"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Share files and manage versions"
msgstr "Compartir archivos y administrar versiones"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_is_favorite
msgid "Show Project on dashboard"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"So if you're looking for the history of a Task, or the latest message on an "
"Issue, simply go to the corresponding Document and you'll find it!"
msgstr ""
"Así que si usted está buscando la historia de un grupo, o el último mensaje "
"en un problema, sólo tiene que ir con el documento correspondiente y lo "
"encontrará!"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Software development"
msgstr "Desarrollo de software"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:15
#, python-format
msgid "Specification"
msgstr "Especificación"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:23
#, python-format
msgid "Specification is validated"
msgstr "Especificaciones son validadas"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:22
#, python-format
msgid "Specification of task is written"
msgstr "Especificación de tarea fue escrita"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:48
#, python-format
msgid "Sprint"
msgstr "Sprint"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_type_id
#: model:ir.model.fields,field_description:project.field_project_task_history_type_id
#: model:ir.model.fields,field_description:project.field_project_task_stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user_stage_id
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Stage"
msgstr "Fase"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "Etapa Cambiada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Stage Description and Tooltips"
msgstr "Descripción y Ayudas Contextales de la Etapa"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_name
msgid "Stage Name"
msgstr "Nombre de fase"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "Etapa cambiada"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
msgid "Stages"
msgstr "Etapas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Start / Stop a timer in one click"
msgstr "Iniciar / Detener un temporizador en un clic"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_date_start
msgid "Start Date"
msgstr "Fecha inicial"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_date_start
msgid "Starting Date"
msgstr "Fecha inicial"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_account_type
msgid "State"
msgstr "Estado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_state
#: model:ir.model.fields,field_description:project.field_report_project_task_user_state
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
msgid "Status"
msgstr "Estado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_child_ids
#, fuzzy
msgid "Sub-tasks"
msgstr "tareas"

#. module: project
#: sql_constraint:project.tags:0
msgid "Tag name already exists !"
msgstr "Ese nombre de etiqueta ya existe!"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project_tag_ids
#: model:ir.model.fields,field_description:project.field_project_task_tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
msgid "Tags"
msgstr "Etiquetas"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Tags of project's tasks, issues..."
msgstr "Etiquetas de las tareas del proyecto, incidencias..."

#. module: project
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_history_cumulative_task_id
#: model:ir.model.fields,field_description:project.field_project_task_history_task_id
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Task"
msgstr "Tarea"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_tasks
msgid "Task Activities"
msgstr "Actividades de Tareas"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_blocked
#: model:mail.message.subtype,name:project.mt_task_blocked
msgid "Task Blocked"
msgstr "Tarea Bloqueada"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Opened"
msgstr "Tarea Abierta"

#. module: project
#: model:ir.filters,name:project.filter_task_report_task_pipe
msgid "Task Pipe"
msgstr "Canal de Tareas"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_ready
#: model:mail.message.subtype,name:project.mt_task_ready
msgid "Task Ready"
msgstr "Tarea lista"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "Fase/Etapa de la Tarea"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "Etapa de Tarea Cambiada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user_name
msgid "Task Title"
msgstr "Título de la Tarea"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "Título de la Tarea..."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_blocked
msgid "Task blocked"
msgstr "Tarea bloqueada"

#. module: project
#: selection:project.config.settings,module_pad:0
msgid "Task description is a plain text"
msgstr "La descripción de la tarea es texto plano"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:24
#, python-format
msgid "Task is Developed"
msgstr "Tarea se encuentra en desarrollo"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:104
#, python-format
msgid "Task is completed"
msgstr "La tarea está completa"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:25
#, python-format
msgid "Task is tested"
msgstr "Tarea fue probada"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
msgid "Task opened"
msgstr "Tarea abierta"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_ready
msgid "Task ready for Next Stage"
msgstr "Tarea lista para la siguiente etapa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
msgid "Task's Analysis"
msgstr "Análisis de Tareas"

#. module: project
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.model.fields,field_description:project.field_account_analytic_account_use_tasks
#: model:ir.model.fields,field_description:project.field_project_project_task_count
#: model:ir.model.fields,field_description:project.field_project_project_task_needaction_count
#: model:ir.model.fields,field_description:project.field_project_project_use_tasks
#: model:ir.model.fields,field_description:project.field_res_partner_task_ids
#: model:ir.ui.menu,name:project.menu_action_view_task
#: model:ir.ui.menu,name:project.menu_project_task_user_tree
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_planner
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
msgid "Tasks"
msgstr "Tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Tasks &amp; Issues"
msgstr "Tareas e Incidencias"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.actions.act_window,name:project.action_project_task_user_tree_filtered
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "Análisis de Tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "Fases/Etapas de Tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Tasks analysis"
msgstr "Análisis de tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Tasks are the main mechanism in Odoo and are activated by default."
msgstr ""
"Las tareas son el mecanismo principal en Odoo y estpan activadas por defecto."

#. module: project
#: model:ir.model,name:project.model_report_project_task_user
msgid "Tasks by user and project"
msgstr "Tareas por usuario y proyecto"

#. module: project
#: model:ir.actions.act_window,name:project.open_view_template_project
msgid "Templates of Projects"
msgstr "Plantillas de Proyectos"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:49
#, python-format
msgid "Test"
msgstr "Prueba"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:57
#, python-format
msgid "Test is OK, need to document"
msgstr "El Test es correcto, tiene que documentarlo"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:18
#, python-format
msgid "Testing"
msgstr "Pruebas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"The Odoo Project app can be used to manage many activities, from the "
"development of a new product to the daily operations of a customer support. "
"With some creativity, it can even be used to manage your marketing "
"communications or personal projects. But just because it can be done doesn't "
"mean it's always a good idea: let's start by helping you understand what can "
"be a good project."
msgstr ""
"La aplicación Odoo Proyectos se puede utilizar para gestionar muchas "
"actividades, desde el desarrollo de un nuevo producto hasta las operaciones "
"diarias de soporte al cliente. Con un poco de creatividad, que incluso se "
"puede utilizar para gestionar sus comunicaciones de marketing o proyectos "
"personales. Pero sólo porque se puede hacer no quiere decir que siempre es "
"una buena idea: vamos a empezar por ayudar a entender lo que puede ser un "
"buen proyecto."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_model
msgid ""
"The kind of document created when an email is received on this project's "
"email alias"
msgstr ""
"El tipo de documento creado cuando se recibe un correo electrónico en el "
"alias de correo del proyecto"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming "
"email that does not reply to an existing record will cause the creation of a "
"new record of this model (e.g. a Project Task)"
msgstr ""
"El modelo (Tipo de Documento de Odoo) al que corresponde este alias. "
"Cualquier correo entrante que no sea respuesta a un registro existente, "
"causará la creación de un nuevo registro de este modelo (ej. una Tarea de "
"Proyecto)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El nombre del alias de correo, ej. 'trabajos' si quiere capturar los correos "
"para <<EMAIL>>"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"El propietario de los registros creados al recibir correos en este alias. Si "
"este campo no está marcado, el sistema tratará de encontrar el propietario "
"adecuado basado en el correo del remotente (De), o usará la cuenta de "
"administrador si no se encuentra un usuario para esa dirección."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "The same features as the Chrome extension, but on your mobile phone!"
msgstr ""
"Las mismas características que la extensión de Chrome, pero en su teléfono "
"móvil."

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project.xml:7
#, python-format
msgid ""
"There is no available image to be set as cover. Send a message on the task "
"with an attached image."
msgstr ""
"No hay imágenes disponibles para usarlas como portada. Envíe un mensaje en "
"la tarea con una imágen adjunta."

#. module: project
#: model:ir.model.fields,help:project.field_project_config_settings_module_rating_project
msgid "This allows customers to give rating on provided services"
msgstr ""
"Esto permite a los clientes dar calificación de los servicios prestados"

#. module: project
#: model:ir.model.fields,help:project.field_project_config_settings_module_sale_service
msgid ""
"This feature automatically creates project tasks from service products in "
"sale orders. In order to make it work,  the product has to be a service and "
"'Create Task Automatically' has to be flagged on the procurement tab in the "
"product form.\n"
"-This installs the module sale_service."
msgstr ""
"Esta catacterística crea automáticamente tareas de proyectos desde servicios "
"en órdenes de venta. Para hacerla funcionar, el producto tiene que ser un "
"servicio y la opción 'Crear Tarea Automáticamente' tiene que estar marcada "
"en la pestaña de abastecimiento, en el formulario del producto.\n"
"-Esto instala el módulo sale_service"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"This is particularly useful to manage help and support: all incoming email  "
"from customers will be transformed into an issue that you'll be able to "
"track easily!"
msgstr ""
"Esto es particularmente útil para gestionar la ayuda y el apoyo: todo el "
"correo entrante de los clientes será transformado en un tema que usted será "
"capaz de rastrear fácilmente!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree_filtered
msgid ""
"This report allows you to analyse the performance of your projects and "
"users. You can analyse the quantities of tasks, the hours spent compared to "
"the planned hours, the average number of days to open or close a task, etc."
msgstr ""
"Este informe le permite analizar el desempeño de sus proyectos y usuarios. "
"Puede analizar la cantidad de tareas, las horas invertidas en comparación "
"con las horas planeadas, el número promedio de días para abrir o cerrar una "
"tarea, etc."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type_fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Esta etapa se pliega en la vista kanban cuando no hay registros para mostrar."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"This whole process might take you a few hours, but don't worry, you can take "
"a break and\n"
"                        return to it at any time: your progress is "
"automatically saved."
msgstr ""
"Todo este proceso que puede tardar un par de horas , pero no se preocupe , "
"puede tomar un descanso y\n"
"                        volver a ella en cualquier momento: su progreso se "
"guarda automáticamente."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"This will add an Invoice Tasks menu in the Project module, that can be used "
"to select the Timesheet to invoice."
msgstr ""
"Esto añadirá un menú Tareas de factura en el módulo de proyectos, que se "
"puede utilizar para seleccionar la parte de horas a facturar."

#. module: project
#: model:ir.model.fields,help:project.field_res_company_project_time_mode_id
#: model:ir.model.fields,help:project.project_time_mode_id_duplicate_xmlid
#, fuzzy
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right "
"unit of measure in your employees."
msgstr ""
"Permite fijar la unidad de medida utilizada en proyectos y tareas.\n"
"Si utiliza las hojas de tiempo relacionadas con proyectos (módulo "
"project_timesheet), no olvide configurar la unidad de medida correcta en sus "
"empleados."

#. module: project
#: model:res.groups,name:project.group_time_work_estimation_tasks
msgid "Time Estimation on Tasks"
msgstr "Estimación de Tiempo en Tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Time Scheduling"
msgstr "Programación de Tiempo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_group_time_work_estimation_tasks
msgid "Time on Tasks"
msgstr "Tiempo en las Tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_company_project_time_mode_id
#: model:ir.model.fields,field_description:project.project_time_mode_id_duplicate_xmlid
msgid "Timesheet UoM"
msgstr "Unidad de medida de parte de horas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_module_project_timesheet_synchro
msgid "Timesheet app for Chrome/Android/iOS"
msgstr "App de parte de horas para Chrome/Android/iOS"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_config_settings
msgid "Timesheets"
msgstr "Partes de Horas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_config_settings_module_project_issue_sheet
msgid "Timesheets Invoicing"
msgstr "Facturación de Partes de Horas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Timesheets are often essential for running a company.<br/>\n"
"                    They are also prone to human error, repetitive, "
"annoying, and sometimes stressful to employees.<br/>\n"
"                    Fortunately, Odoo has several solutions to make them as "
"efficient and painless as possible!<br/>"
msgstr ""
"Tabla de tiempos a menudo son esenciales para el funcionamiento de una "
"empresa.<br/>\n"
"                    También son propensos a errores humanos, repetitivo, "
"molesto, y a veces estresante para los empleados.<br/>\n"
"                   Afortunadamente, Odoo tiene varias soluciones para que "
"sean lo más eficientes y menos incomodo posible!<br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Timesheets can be used for several purposes:"
msgstr "Los partes de horas se pueden usar por varios motivos:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Timesheets to Approve"
msgstr "Partes de Horas por Aprobar"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_resource_calendar_id
msgid "Timetable working hours to adjust the gantt diagram report"
msgstr "Horario de trabajo para ajustar el informe del diagrama de Gantt"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:96
#: model:project.task.type,name:project.project_stage_0
#, python-format
msgid "To Do"
msgstr "Por hacer"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"To configure these Kanban Statuses, go to the 'Project Stages' tab of a "
"Project."
msgstr ""
"Para configurar estos estados Kanban, vaya a la pestaña ' etapas del "
"proyecto."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "To use Issues, install the"
msgstr "Para usar Incidencias, instale el"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "To use Timesheets, go to your"
msgstr "Para usar Hojas de Registro, vaya a su"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_remaining_hours
msgid ""
"Total remaining time, can be re-estimated periodically by the assignee of "
"the task."
msgstr ""
"Tiempo restante total, puede ser re-estimado periódicamente por quien se le "
"ha asignado la tarea."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unassigned"
msgstr "No Asignado"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_history_search
msgid "Unassigned Tasks"
msgstr "Tareas no Asignadas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Use Tasks"
msgstr "Usar Tareas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_label_tasks
msgid "Use Tasks as"
msgstr "Usar Tareas como"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Use Timesheets"
msgstr "Usar Partes de Horas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Use separate meetings to solve big issues or issues that aren’t important "
"for the entire team."
msgstr ""
"Utilice reuniones por separado para resolver grandes problemas o cuestiones "
"que no son importantes para todo el equipo."

#. module: project
#: model:project.task.type,legend_priority:project.project_stage_0
msgid "Use the priority for tasks related to gold customers"
msgstr ""
"Utilice la prioridad de las tareas relacionadas con los clientes de oro"

#. module: project
#: model:res.groups,name:project.group_project_user
msgid "User"
msgstr "Usuario"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_user_email
msgid "User Email"
msgstr "Email del Usuario"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"Usually, a project's team members are managed through weekly (or monthly) "
"status meetings.<br/>\n"
"                        Sometimes, these meetings can last hours and expose "
"participants to an overly detailed review of the project.<br/>\n"
"                        Your team members will probably try to avoid those "
"kind of meetings, or have to rush afterwards to meet their deadlines...<br/"
"><br/>\n"
"                        So how can you, as project manager, structure a "
"weekly status meeting where team members are engaged, informed and willing "
"to contribute to the project's next steps? Here are some tips."
msgstr ""
"Por lo general, los miembros del equipo de un proyecto se organizan a través "
"de reuniones de estado semanales ( o mensuales ) reuniones de estado.<br/>\n"
"                        Los miembros de su equipo probablemente tratarán de "
"evitar ese tipo de reuniones, o tienen que correr después de cumplir sus "
"plazos ...<br/>\n"
"                                    Entonces, ¿cómo puede usted, como "
"director del proyecto, estructura de una reunión semanal sobre el estado en "
"que los miembros del equipo están comprometidos, informados y dispuestos a "
"contribuir a los próximos pasos del proyecto? Estos son algunos consejos."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:16
#, python-format
msgid "Validation"
msgstr "Validación"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "View statistics (time spent, efficiency, etc.)"
msgstr "Ver estadísticas (tiempo usado, eficiencia, etc.)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "View statistics for the week"
msgstr "Ver estadísticas para la semana"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:66
#: code:addons/project/static/src/js/web_planner_project.js:81
#, python-format
msgid "Wait. Customer"
msgstr "Espera. Cliente"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:67
#: code:addons/project/static/src/js/web_planner_project.js:82
#, python-format
msgid "Wait. Expert"
msgstr "Espera. Experto"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "We can add fields related to your business on any screen, for example:"
msgstr ""
"Podemos añadir campos relacionados con su negocio en cada pantalla, por "
"ejemplo:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "We can automate steps in your workflow, for example:"
msgstr "Podemos automatizar pasos en su workflow, por ejemplo:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"We can implement custom reports based on your Word or GoogleDocs templates, "
"for example:"
msgstr ""
"Nosotros podemos implementar informes personalizados basados en su plantilla "
"Word o GoogleDocs, por ejemplo:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"We hope this process helped you implement our project management application."
msgstr ""
"Esperamos que este proceso ayude a implementar nuestra aplicación de gestión "
"de proyectos ."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"We've developed a super simple and efficient Chrome extension to enter your "
"timesheets:<br/><br/>"
msgstr ""
"Hemos desarrollado una super sencilla y eficaz extensión de Chrome para "
"entrar en sus hojas de tiempo:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Welcome"
msgstr "Bienvenido/a"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"What is the average number of working hours necessary to close an issue?"
msgstr ""
"¿Cuál es el número promedio de horas de trabajo necesarias para cerrar un "
"tema ?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "What is the average time before an issue is assigned / closed?"
msgstr ""
"¿Cuál es el promedio de tiempo antes de que se le asigna una emisión / "
"cerrado ?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"What is the difference between initial time estimation and final time spent?"
msgstr ""
"Cual es la diferencia entre el tiempo inicial estimado y el tiempo final "
"gastado?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "What is the number of missed deadlines?"
msgstr "¿Cuál es el número de incumplimiento en los plazos ?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "What is their average number of tasks or issues worked on / closed?"
msgstr ""
"¿Cuál es su número medio de las tareas o problemas resueltos / cerrados ?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "What is their average number of working hours over the year?"
msgstr "¿Cuál es su número medio de horas de trabajo durante el año ?"

#. module: project
#: model:ir.model.fields,help:project.field_project_project_is_favorite
msgid "Whether this project should be displayed on the dashboard or not"
msgstr ""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/web_planner_project.js:39
#, python-format
msgid "Work has started"
msgstr "El trabajo ha comenzado"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_resource_calendar_id
msgid "Working Time"
msgstr "Horario de Trabajo"

#. module: project
#: model:ir.filters,name:project.filter_task_report_workload
msgid "Workload"
msgstr "Carga de Trabajo"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"You can also add a description to help your coworkers understand the meaning "
"and purpose of the stage."
msgstr ""
"También puede agregar una descripción para ayudar a sus compañeros de "
"trabajo a entender el significado y propósito de la etapa."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"You can also give a tooltip about the use of the stars available in the "
"kanban and form views."
msgstr ""
"Usted también puede poner un tooltip sobre el uso de las estrellas "
"disponibles en la vista kanban y el formulario."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "You can even include any report in your dashboard for permanent access!"
msgstr ""
"¡Incluso puede incluir cualquier informe en el tablero de control para el "
"acceso permanente!"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"You can learn more about Timesheets in the 'Use Timesheets' section of this "
"planner."
msgstr ""
"Usted puede aprender más acerca de Partes de Horas en la sección 'Usando "
"Partes de Horas' de este planificador."

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
msgid ""
"You can now manage your tasks in order to get things done efficiently. Track "
"progress, discuss, attach documents, etc."
msgstr ""
"Ahora usted puede gestionar sus tareas para hacer las cosas eficientemente. "
"Rastree el progreso, discuta, adjunte documentos, etc."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"You can reply directly to a message from you email software; the message and "
"its attachments will be added to the Chatter."
msgstr ""
"Puede responder directamente a un mensaje desde su software de correo "
"electrónico; el mensaje y sus anexos se añadirán a la Charla."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
#, fuzzy
msgid "You can save your reports to easily reuse it later"
msgstr "Usted puede guardar sus reportes para reusarlos fácilmente luego"

#. module: project
#: code:addons/project/project.py:94
#, python-format
msgid ""
"You cannot delete a project containing tasks. You can either delete all the "
"project's tasks and then delete the project or simply deactivate the project."
msgstr ""
"No puede eliminar un proyecto que contiene tareas. Puede eliminar todas las "
"tareas y entonces eliminar el proyecto, o simplemente desactivarlo."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Your Activities"
msgstr "Sus Actividades"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Your Objectives"
msgstr "Sus Objetivos"

#. module: project
#: model:mail.template,subject:project.mail_template_data_module_install_project
msgid "Your Odoo Project application is up and running"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Your Projects"
msgstr "Sus Proyectos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "Your Projects:"
msgstr "Sus Proyectos:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "and activate:"
msgstr "y active:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "etc.."
msgstr "etc.."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "for Issues: 'Activate timesheets on issues'"
msgstr "para las Incidencias: 'Active partes de horas en incidencias'"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "for Tasks: 'Log work activities on tasks'"
msgstr "para las tareas:\" Registrar las actividades de trabajo en tareas \""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "oe_kanban_text_red"
msgstr "oe_kanban_text_red"

#. module: project
#: model:ir.model,name:project.model_project_config_settings
msgid "project.config.settings"
msgstr "project.config.settings"

#. module: project
#: model:ir.model,name:project.model_project_task_history_cumulative
msgid "project.task.history.cumulative"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "send us an email"
msgstr "envíenos un correo"

#. module: project
#: code:addons/project/project.py:605
#, python-format
msgid "tasks"
msgstr "tareas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "to describe<br/> your experience or to suggest improvements !"
msgstr "para describir<br/> su experiencia o sugerir mejoras!"

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account_project_count
#: model:ir.model.fields,field_description:project.field_project_project_project_count
#: model:ir.model.fields,field_description:project.field_project_project_task_ids
msgid "unknown"
msgstr "desconocido"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "using the above recommendations"
msgstr "Utilizando las recomendaciones anteriores"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid ""
"view of the HR module is the main tool to check, modify and confirm "
"Timesheets."
msgstr ""
"Vista del módulo de recursos humanos es la principal herramienta para "
"comprobar, modificar y confirmar partes de horas ."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "view of the Human Resources module."
msgstr "Vista del módulo de Recursos Humanos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "view."
msgstr "Ver"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "with Timesheet"
msgstr "con Parte de horas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_planner
msgid "you listed on the previous step"
msgstr "Usted enumeró en el paso anterior"

#~ msgid "&times;"
#~ msgstr "&times;"

#~ msgid "Action Needed"
#~ msgstr "Acción Requerida"

#~ msgid "Check this field if this project manages timesheets"
#~ msgstr "Marque esta casilla si este proyecto administra partes de horas"

#~ msgid "Close"
#~ msgstr "Cerrar"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Delegated Tasks"
#~ msgstr "Tareas Delegadas"

#~ msgid "Do not record timesheets on tasks"
#~ msgstr "No registrar partes de horas en las tareas"

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Contactos)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, los nuevos mensajes requerirán su atención."

#~ msgid "Is Follower"
#~ msgstr "Es Seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes e historial de comunicación"

#~ msgid "Number of Actions"
#~ msgstr "Número de Acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Open Project Menu"
#~ msgstr "Abrir Menú Proyecto"

#~ msgid "Project Time Unit"
#~ msgstr "Unidad de tiempo del proyecto"

#~ msgid "Record timesheet lines per tasks"
#~ msgstr "Grabar registro de tiempo para las tareas"

#~ msgid "Subscriptions"
#~ msgstr "Suscripciones"

#~ msgid "Task's Work on Tasks"
#~ msgstr "Trabajo en las Tareas"

#~ msgid ""
#~ "This allows you to transfer the entries under tasks defined for Project "
#~ "Management to the timesheet line entries for particular date and user, "
#~ "with the effect of creating, editing and deleting either ways.\n"
#~ "-This installs the module project_timesheet."
#~ msgstr ""
#~ "Esto permite transferir las entradas de las tareas de la gestión de "
#~ "proyectos a las entradas del parte de horas para una fecha y usuario en "
#~ "concreto, con el efecto de crear, editar y borrar en ambos sentidos\n"
#~ "Esto instala el módulo project_timesheet."

#~ msgid "Timesheets on Project"
#~ msgstr "Hojas de registro en Proyectos"

#~ msgid "Type of Account"
#~ msgstr "Tipo de Cuenta"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de Mensajes no Leídos"

#~ msgid "Website Messages"
#~ msgstr "Mensajes del Sitio Web"

#~ msgid "Website communication history"
#~ msgstr "Historial de comunicación del sitio Web"
