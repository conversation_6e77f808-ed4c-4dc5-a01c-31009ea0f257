# -*- coding:utf-8 -*-
from odoo import api, fields, models, _
from datetime import datetime, timedelta
from pytz import timezone
from odoo.exceptions import ValidationError


class ManagerEmployeeCarAllaRepo(models.TransientModel):
    _name = "hr.masarat.car.wizard"

    employee_id = fields.Many2one('hr.employee', string="الموظف")
    all_employee = fields.Boolean(string="كل الموظفين")
    date_start = fields.Date(string='تاريخ البدأ')
    date_end = fields.Date(string='تاريخ الانتهاء')

    def get_report_action(self):
        if self.all_employee:
            ee = False
        else:
            ee = self.employee_id.id

        data = {'model': self._name, 'employee_id': ee,'all_employee':self.all_employee, 'date_start':str(self.date_start), 'date_end':str(self.date_end)}
        return self.sudo().env.ref('hr_approvales_masarat.car_allawance_report_x1').report_action(self, data=data)


class ManagerEmployeeCarAlla(models.AbstractModel):
    _name = "report.hr_approvales_masarat.car_allawance_report_id"
    _description = 'Car Allawance Report'

    def _get_report_values_all(self,date_from, date_to):
        car_moves = self.env['hr.masarat.car.line'].search([('request_date','>=',date_from),('request_date','<=',date_to),('state','in',('hr_approval','manager_approval'))])
        vals = {}
        for elem in car_moves:
            vals.setdefault(str(elem.employee_id.id),{'name':str(elem.employee_id.name), 'total_hours':0, 'total_count':0})
            vals[str(elem.employee_id.id)]['total_hours']+=elem.allowance_hours
            vals[str(elem.employee_id.id)]['total_count'] += 1

        for line in vals:
            vals[line]['total_hours'] = '{0:02.0f}:{1:02.0f}'.format(*divmod(vals[line]['total_hours']* 60, 60))

        return {'all_employee': True, 'employees_dict': vals, 'start_date': date_from, 'end_date': date_to}

    def _get_by_employee(self,date_from, date_to, employee_id):
        car_moves = self.env['hr.masarat.car.line'].search([('employee_id','=',employee_id),('request_date', '>=', date_from), ('request_date', '<=', date_to),('state', 'in', ('hr_approval', 'manager_approval'))])
        employee_id = self.env['hr.employee'].search([('id','=',employee_id)])
        vals = []
        for line in car_moves:
            vals.append({'located_by':line.located_by.name,
                         'request_date':str(line.request_date),
                         'allowance_hours':str(line.allowance_hours),
                         'mission_type':str(line.mission_type)})
        vals = sorted(vals,key=lambda x:x['request_date'])
        return {'all_employee': False, 'employee_name':employee_id.name, 'employees_dict': vals, 'start_date': date_from, 'end_date': date_to}

    def _get_report_values(self, docids, data=None):
        if data['all_employee']:
            all_employee = self._get_report_values_all(data['date_start'],data['date_end'])
            return all_employee
        else:
            one_employee = self._get_by_employee(data['date_start'],data['date_end'], data['employee_id'])
            return one_employee


