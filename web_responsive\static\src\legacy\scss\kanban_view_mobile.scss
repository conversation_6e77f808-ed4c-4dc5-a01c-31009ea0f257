/* Copyright 2019 Odoo S.A.
 * License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl). */

@include media-breakpoint-down(sm) {
    .o_kanban_view {
        width: 100%;
    }
    .o_kanban_view.o_kanban_grouped {
        display: block;
        position: relative;
        overflow-x: hidden;
        &.o_renderer_with_searchpanel {
            width: 100%;
        }

        .o_kanban_mobile_tabs_container {
            position: sticky;
            display: flex;
            justify-content: space-between;
            width: 100%;
            top: 0;
            z-index: 1;
            background-color: #5e5e5e;

            .o_kanban_mobile_add_column {
                height: $o-kanban-mobile-tabs-height;
                padding: 10px;
                border-left: grey 1px solid;
                color: white;
                font-size: 14px;
            }

            .o_kanban_mobile_tabs {
                position: relative;
                display: flex;
                width: 100%;
                height: $o-kanban-mobile-tabs-height;
                overflow-x: auto;
                -ms-overflow-style: none;
                scrollbar-width: none;
                &::-webkit-scrollbar {
                    display: none;
                }

                .o_kanban_mobile_tab {
                    height: $o-kanban-mobile-tabs-height;
                    padding: 10px 20px;
                    font-size: 14px;
                    color: white;

                    &.o_current {
                        font-weight: bold;
                        border-bottom: 3px solid $o-brand-primary;
                        background-color: gray("600");
                        font-weight: bold;
                    }

                    .o_column_title {
                        white-space: nowrap;
                        text-transform: uppercase;
                    }
                }
            }
        }
        .o_kanban_columns_content {
            position: relative;
        }
        // [class] to get same specificity as elsewhere (kanban_view.less)
        &[class] .o_kanban_group:not(.o_column_folded) {
            @include o-position-absolute(
                $top: $o-kanban-mobile-tabs-height,
                $left: 0,
                $bottom: 0
            );
            width: 100%;
            padding: 0;
            margin-left: 0; // override the margin-left: -1px of the desktop mode
            border: none;

            &.o_current {
                position: inherit;
                top: 0;

                &.o_kanban_no_records {
                    // set a default height for clarity when embedded in another view
                    min-height: $o-kanban-mobile-empty-height;
                }
            }

            .o_kanban_header {
                display: none;
            }
            .o_kanban_record,
            .o_kanban_quick_create {
                border: none;
                border-bottom: 1px solid lightgray;
                padding: 10px 16px;
                margin: 0;
            }
        }
    }
    .modal {
        z-index: 1052;
    }
    .o_kanban_view .o_column_quick_create {
        .o_quick_create_folded {
            display: none !important;
        }
        .o_quick_create_unfolded {
            width: 100%;
        }
    }
}
