# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from pytz import timezone
from odoo import models, fields, api, _

class SalaryTable(models.Model):
    _name = "bsic.salary.table"

    name = fields.Char(compute="get_name")
    class_bsic = fields.Char(string="الدرجة", required=True)
    class_bsic_number = fields.Char(string="المربوط", required=True)
    amount = fields.Float(string="القيمة المالية", required=True)

    def get_name(self):
        for elem in self:
            elem.name = '/'
            if elem.class_bsic and elem.class_bsic_number:
                elem.name = elem.class_bsic+'-'+elem.class_bsic_number


