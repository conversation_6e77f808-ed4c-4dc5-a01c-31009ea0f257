<?xml version="1.0"?>
<odoo>

    <record id="hr_manager_attendance_report_form" model="ir.ui.view">
        <field name="name">hr.manager.attendance.report.form</field>
        <field name="model">hr.manager.attendance.report</field>
        <field name="arch" type="xml">
            <form string="Manager Attendance Report">
                <div>
                    <h4>
                        My Employees
                    </h4>
                </div>
                <group>
                    <group>
                        <field name="employee_selection" required="1"/>
                    </group>
                    <group>
                        <field name="date_start" required="1"/>
                        <field name="date_end" required="1"/>
                    </group>
                </group>
                <footer>
                    <button name="get_employee_attendancy_report_action" type="object" string="Create"
                            class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>

            </form>
        </field>
    </record>


    <record id="action_report_manager_attendance_report" model="ir.actions.act_window">
        <field name="name">Manager Attendance Report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.manager.attendance.report</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_manager_attendance_report_form"/>
        <field name="target">new</field>
    </record>


    <!---->
    <record id="view_employee_form_inherited_x2" model="ir.ui.view">
        <field name="name">hr.employee.form.x1</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="%(action_report_manager_attendance_report)d"
                        type="action"
                        class="oe_stat_button"
                        icon="fa-sign-in"
                        style="color: black; background-color:#69FF82; font-size: 12px;"
                        string="Manager Attendance report">
                </button>
            </xpath>
        </field>
    </record>

    <record id="hr.open_view_employee_list_my" model="ir.actions.act_window">
        <field name="name">Employees</field>
        <field name="res_model">hr.employee</field>
        <field name="view_mode">kanban,tree,form,activity</field>
        <field name="domain">[('user_id.id', '=', uid)]</field>
        <field name="context">{'chat_icon': True}</field>
        <field name="view_id" eval="False"/>
        <field name="search_view_id" ref="hr.view_employee_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new employee
            </p>
            <p>
                With just a quick glance on the Odoo employee screen, you
                can easily find all the information you need for each person;
                contact data, job position, availability, etc.
            </p>
        </field>
    </record>

    <record id="open_view_employee_list_my_records" model="ir.actions.act_window">
        <field name="name">Employees</field>
        <field name="res_model">hr.employee</field>
        <field name="view_mode">kanban,tree,form,activity</field>
        <field name="domain">[]</field>
        <field name="context">{'chat_icon': True}</field>
        <field name="view_id" eval="False"/>
        <field name="search_view_id" ref="hr.view_employee_filter"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new employee
            </p>
            <p>
                With just a quick glance on the Odoo employee screen, you
                can easily find all the information you need for each person;
                contact data, job position, availability, etc.
            </p>
        </field>
    </record>

    <record id="view_employee_form_inherited_x3" model="ir.ui.view">
        <field name="name">hr.employee.public.form.x2</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='department_id']" position="replace">
                <field name="department_id" options='{"no_open": True}'/>
            </xpath>
            <xpath expr="//field[@name='parent_id']" position="replace">
                <field name="parent_id" options='{"no_open": True}'/>
            </xpath>
            <xpath expr="//field[@name='coach_id']" position="replace">
                <field name="coach_id" options='{"no_open": True}'/>
            </xpath>
        </field>
    </record>

    <record id="view_employee_form_inherited_x4" model="ir.ui.view">
        <field name="name">hr.employee.public.form.x3</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr_holidays.hr_employee_public_form_view_inherit"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='leave_manager_id']" position="replace">
                <field name="leave_manager_id" options='{"no_open": True}'/>
            </xpath>
        </field>
    </record>



    <menuitem
            id="menu_hr_employee_user_my_records"
            name="All Employees"
            action="open_view_employee_list_my_records"
            parent="hr.menu_hr_employee_payroll"
            groups="hr_approvales_masarat.group_hr_approvales_masarat"
            sequence="2"/>

    <menuitem
            id="hr.menu_hr_employee"
            name="Employee Directory"
            action="hr.hr_employee_public_action"
            parent="hr.menu_hr_root"
            groups="hr_approvales_masarat.group_hr_approvales_masarat"
            sequence="4"/>

</odoo>

