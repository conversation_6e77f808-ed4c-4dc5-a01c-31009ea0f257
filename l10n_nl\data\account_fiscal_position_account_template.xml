<?xml version="1.0" encoding="utf-8"?>
<openerp>
    <data>

        <!-- Fiscal Position Account Templates -->

        <!-- NON-EU Countries -->
        <record
            id="fiscal_position_account_non_eu_1"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_non_eu" />
            <field name="account_src_id"  ref="7001" />
            <field name="account_dest_id" ref="7003" />
        </record>
        <record
            id="fiscal_position_account_non_eu_2"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_non_eu" />
            <field name="account_src_id"  ref="7005" />
            <field name="account_dest_id" ref="7007" />
        </record>
        <record
            id="fiscal_position_account_non_eu_3"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_non_eu" />
            <field name="account_src_id"  ref="7009" />
            <field name="account_dest_id" ref="7011" />
        </record>
        <record
            id="fiscal_position_account_non_eu_4"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_non_eu" />
            <field name="account_src_id"  ref="8001" />
            <field name="account_dest_id" ref="8003" />
        </record>
        <record
            id="fiscal_position_account_non_eu_5"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_non_eu" />
            <field name="account_src_id"  ref="8011" />
            <field name="account_dest_id" ref="8013" />
        </record>
        <record
            id="fiscal_position_account_non_eu_6"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_non_eu" />
            <field name="account_src_id"  ref="8021" />
            <field name="account_dest_id" ref="8023" />
        </record>

        <!-- EU Countries -->
        <record
            id="fiscal_position_account_eu_1"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_eu" />
            <field name="account_src_id"  ref="7001" />
            <field name="account_dest_id" ref="7002" />
        </record>
        <record
            id="fiscal_position_account_eu_2"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_eu" />
            <field name="account_src_id"  ref="7005" />
            <field name="account_dest_id" ref="7006" />
        </record>
        <record
            id="fiscal_position_account_eu_3"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_eu" />
            <field name="account_src_id"  ref="7009" />
            <field name="account_dest_id" ref="7010" />
        </record>
        <record
            id="fiscal_position_account_eu_4"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_eu" />
            <field name="account_src_id"  ref="8001" />
            <field name="account_dest_id" ref="8002" />
        </record>
        <record
            id="fiscal_position_account_eu_5"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_eu" />
            <field name="account_src_id"  ref="8011" />
            <field name="account_dest_id" ref="8012" />
        </record>
        <record
            id="fiscal_position_account_eu_6"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_eu" />
            <field name="account_src_id"  ref="8021" />
            <field name="account_dest_id" ref="8022" />
        </record>

        <!-- Installatie / afstandsverkopen -->
        <record
            id="fiscal_position_account_installatie_afstand_1"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_eu_no_taxes_report" />
            <field name="account_src_id"  ref="8001" />
            <field name="account_dest_id" ref="8110" />
        </record>

        <record
            id="fiscal_position_account_installatie_afstand_2"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_eu_no_taxes_report" />
            <field name="account_src_id"  ref="8011" />
            <field name="account_dest_id" ref="8120" />
        </record>

        <record
            id="fiscal_position_account_installatie_afstand_3"
            model="account.fiscal.position.account.template">
            <field name="position_id"     ref="fiscal_position_template_eu_no_taxes_report" />
            <field name="account_src_id"  ref="8021" />
            <field name="account_dest_id" ref="8130" />
        </record>

    </data>
</openerp>
