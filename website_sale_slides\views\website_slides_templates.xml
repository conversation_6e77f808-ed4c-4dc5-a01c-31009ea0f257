<?xml version="1.0" ?>
<odoo><data>

<!-- LESSON -->
<template id="lesson_content_quiz" inherit_id="website_slides.lesson_content_quiz">
    <xpath expr="//div[hasclass('o_wslides_js_lesson_quiz')]" position="attributes">
        <attribute name="t-att-data-price">product_info['price'] if product_info else None</attribute>
        <attribute name="t-att-data-currency-name">product_info['currency_id'].name if product_info else None</attribute>
        <attribute name="t-att-data-currency-symbol">product_info['currency_id'].symbol if product_info else None</attribute>
        <attribute name="t-att-data-has-discounted-price">product_info['has_discounted_price'] if product_info else None</attribute>
        <attribute name="t-att-data-product-id">slide.channel_id.product_id.id if slide.channel_id.product_id else None</attribute>
        <attribute name="t-att-data-list-price">product_info['list_price'] if product_info else None</attribute>
    </xpath>
</template>

<template name="Buy Course To Download Resource" id="slide_aside_training_category_buy_course" inherit_id="website_slides.slide_aside_training_category">
    <xpath expr="//div[hasclass('o_wslides_js_course_join')]" position="inside">
        <li t-elif="aside_slide.channel_id.enroll == 'payment'" class="text-decoration-none small">
            <i class="fa fa-download mr-1"/>
            <t t-call="website_sale_slides.course_buy_course_link">
                <t t-set="slide" t-value="aside_slide"/>
            </t>
        </li>
    </xpath>
</template>

<template name="Buy Course To Download Resource Slide Detail" id="slide_content_detailed_buy_course" inherit_id="website_slides.slide_content_detailed">
    <xpath expr="//div[hasclass('o_wslides_js_course_join')]" position="inside">
        <span t-elif="slide.channel_id.enroll == 'payment'" class="text-muted mr-auto border-left pl-3">
            <t t-call="website_sale_slides.course_buy_course_link"/>
        </span>
    </xpath>
</template>

<!-- Tweak "preview" badge: display Free Preview if payment-based course -->
<template id="course_slides_list_slide"
    name="Slide template for a training channel (Sale)"
    inherit_id="website_slides.course_slides_list_slide">
    <xpath expr="//a[@t-if='channel.can_upload']/span/span" position="replace">
        <span t-if="channel.enroll == 'payment'">Free Preview</span>
        <span t-else="">Preview</span>
    </xpath>
    <xpath expr="//t[@t-elif='slide.is_preview and not channel.is_member']/span/span" position="replace">
        <span t-if="channel.enroll == 'payment'">Free Preview</span>
        <span t-else="">Preview</span>
    </xpath>
</template>

<!-- FULLSCREEN -->
<template id="slide_fullscreen" inherit_id="website_slides.slide_fullscreen">
    <xpath expr="//div[hasclass('o_wslides_fs_main')]" position="attributes">
        <attribute name="t-att-data-price">product_info['price'] if product_info else None</attribute>
        <attribute name="t-att-data-currency-name">product_info['currency_id'].name if product_info else None</attribute>
        <attribute name="t-att-data-currency-symbol">product_info['currency_id'].symbol if product_info else None</attribute>
        <attribute name="t-att-data-has-discounted-price">product_info['has_discounted_price'] if product_info else None</attribute>
        <attribute name="t-att-data-product-id">slide.channel_id.product_id.id if product_info else None</attribute>
        <attribute name="t-att-data-list-price">product_info['list_price'] if product_info else None</attribute>
    </xpath>
</template>

<template name="Buy Course To Download Resource Fullscreen" id="slide_fullscreen_sidebar_category_buy_course" inherit_id="website_slides.slide_fullscreen_sidebar_category">
    <xpath expr="//div[hasclass('o_wslides_js_course_join')]" position="inside">
        <li t-elif="slide.channel_id.enroll == 'payment'" class="o_wslides_fs_slide_link mb-1">
            <i class="fa fa-download mr-1"/>
            <t t-call="website_sale_slides.course_buy_course_link"/>
        </li>
    </xpath>
</template>

<!-- COURSE -->
<template name="Course Sidebar (infos, CTA)" id='course_sidebar' inherit_id="website_slides.course_sidebar">
    <!-- Channel main template: override button to join channel -->
    <xpath expr="//div[hasclass('o_wslides_js_course_join')]" position="inside">
        <t t-if="(not channel.is_member or channel.can_publish) and channel.enroll == 'payment'">
            <t t-call="website_sale_slides.course_buy_course_button" />
        </t>
    </xpath>
</template>

<!-- TOOLS -->
<template name="Buy Course Link" id="course_buy_course_link">
    <a class="post_link" t-att-href="'/shop/cart/update?product_id=%s' % slide.channel_id.product_id.id">
        Buy Course</a> to download resources
</template>

<template name="Buy Course Button" id="course_buy_course_button">
    <t t-if="channel.product_id.website_published">
        <div t-attf-class="text-center d-flex align-items-center text-center pb-1 #{'justify-content-between' if product_info['has_discounted_price'] else 'justify-content-around'}">
            <div class="css_editable_mode_hidden">
                <!-- real price -->
                <div class="oe_price font-weight-bold text-nowrap h2 my-2"
                     t-esc="product_info['price']"
                     t-options="{'widget': 'monetary', 'display_currency': product_info['currency_id']}"/>
                <span itemprop="price" style="display:none;" t-esc="product_info['price']"/>
                <span itemprop="priceCurrency" style="display:none;" t-esc="product_info['currency_id'].name"/>
                <!-- original discounted price, if any -->
                <del t-att-class="'text-600 text-nowrap oe_default_price %s' % ('' if product_info['has_discounted_price'] else 'd-none')"
                     t-esc="product_info['list_price']"
                     t-options="{'widget': 'monetary', 'display_currency': product_info['currency_id']}"/>
            </div>
            <div class="css_non_editable_mode_hidden decimal_precision oe_price font-weight-bold text-nowrap h2 my-2"
                 t-att-data-precision="str(product_info['currency_id'].decimal_places)">
                <span t-field="channel.product_id.list_price" t-options="{'widget': 'monetary', 'display_currency': product_info['currency_id']}"/>
            </div>
        </div>
        <div class="oe_website_sale">
            <div class="add_to_cart_button">
                <form action="/shop/cart/update" method="POST">
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()" />
                    <input type="hidden" class="product_id" name="product_id" t-att-value="channel.product_id.id"/>
                    <input type="hidden" class="product_template_id" name="product_template_id" t-att-value="channel.product_id.id"/>
                    <a id="add_to_cart" role="button" href="#"
                       class="btn btn-primary btn-block js_check_product o_js_add_to_cart a-submit"
                       data-animation-selector=".o_wslides_course_pict">
                        <i class="fa fa-shopping-cart"></i> Add to Cart
                    </a>
                    <div id="product_option_block"/>
                </form>
            </div>
            <div class="buy_now_button"/>
        </div>
    </t>
    <t t-else="">
        <div class="alert my-0 bg-200 text-center">
            Course Not Buyable
        </div>
    </t>
</template>

<template name="Allow Buy Now" id="course_option_buy_course_now" inherit_id="website_sale_slides.course_buy_course_button" active="False" customize_show="True">
    <xpath expr="//div[hasclass('buy_now_button')]" position="inside">
        <div style="margin-top:5px;">
            <a role="button" class="btn btn-outline-primary btn-block post_link" t-att-href="'/shop/cart/update?product_id=%s&amp;express=1' % channel.product_id.id">
                <i class="fa fa-bolt"></i> Buy Now
            </a>
        </div>
    </xpath>
</template>

</data></odoo>
