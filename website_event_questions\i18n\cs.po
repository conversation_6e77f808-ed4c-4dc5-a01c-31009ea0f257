# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_questions
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <jan.ho<PERSON><PERSON><PERSON>@centrum.cz>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2021\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_2
msgid "A friend"
msgstr "Od přátel"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_1
msgid "Allergies"
msgstr "Alergie"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__name
msgid "Answer"
msgstr "Odpovědět"

#. module: website_event_questions
#: model:ir.actions.act_window,name:website_event_questions.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_tree
msgid "Answer Breakdown"
msgstr "Serie odpovědí"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_question_view_form
msgid "Answers"
msgstr "Odpovědi"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__once_per_order
msgid "Ask only once per order"
msgstr "Zeptejte se pouze jednou na každou objednávku"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr "Odpovědi účastníka"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr "Rezervováno"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_1
msgid "Commercials"
msgstr "Inzeráty"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_0
msgid "Consumers"
msgstr "Spotřebitelé"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_event
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__event_id
msgid "Event"
msgstr "Událost"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question
msgid "Event Question"
msgstr "Otázka události"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question_answer
msgid "Event Question Answer"
msgstr "Odpověď na otázku události"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration
msgid "Event Registration"
msgstr "Registrace události"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration_answer
msgid "Event Registration Answer"
msgstr "Odpověď na registraci události"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_type
msgid "Event Template"
msgstr "Šablona události"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_type_id
msgid "Event Type"
msgstr "Typ události"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__general_question_ids
msgid "General Questions"
msgstr "Všeobecné otázky"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_7_question_1
msgid "How did you hear about us ?"
msgstr "Jak jsi se o nás dozvěděl ?"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_2
msgid "How did you learn about this event?"
msgstr "Jak jste se o této události dozvěděli?"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__id
msgid "ID"
msgstr "ID"

#. module: website_event_questions
#: model:ir.model.fields,help:website_event_questions.field_event_question__once_per_order
msgid ""
"If True, this question will be asked only once and its value will be "
"propagated to every attendees.If not it will be asked for every attendee of "
"a reservation."
msgstr ""
"Pokud je to pravda, tato otázka bude položena pouze jednou a její hodnota "
"bude přenesena na všechny účastníky. Pokud ne, bude požádán o odpověď každý "
"účastník rezervace."

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_0
msgid "Meal Type"
msgstr "Typ jídla"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_0
msgid "Mixed"
msgstr "Smíšený"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_type_data_conference_question_0_answer_1
msgid "No"
msgstr "Ne"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_0
msgid "Our website"
msgstr "Naše webové stránky"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_type_data_conference_question_0
msgid "Participate in Social Event"
msgstr "Zúčastněte se společenské akce"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr "Pastafarián"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_question_view_form
msgid "Question"
msgstr "Otázka"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__question_type
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__question_type
msgid "Question Type"
msgstr "Typ otázky"

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid "Question cannot belong to both the event category and itself."
msgstr "Otázka nemůže patřit jak do kategorie události, tak do vlastní."

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event_questions.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_view_form_inherit_question
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_type_view_form_inherit_question
msgid "Questions"
msgstr "Otázky"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__registration_id
msgid "Registration"
msgstr "Registrace"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_2
msgid "Research"
msgstr "Průzkum"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_1
msgid "Sales"
msgstr "Prodej"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_tree
msgid "Selected answer"
msgstr "Vybraná odpověď"

#. module: website_event_questions
#: model:ir.model.fields.selection,name:website_event_questions.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr "Výběr"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__sequence
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr "konkrétní otázky"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr "Navrhovaná odpověď"

#. module: website_event_questions
#: model:ir.model.fields.selection,name:website_event_questions.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr "Zadání textu"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr "Textová odpověď"

#. module: website_event_questions
#: model:ir.model.constraint,message:website_event_questions.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr "Musí existovat doporučená hodnota nebo textová hodnota."

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__title
msgid "Title"
msgstr "Název"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_view_form_inherit_question
msgid "Type"
msgstr "Typ"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr "Vegetariánský"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_7_question_0
msgid "Which field are you working in"
msgstr "Ve kterém oboru pracujete"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_type_data_conference_question_0_answer_0
msgid "Yes"
msgstr "Ano"

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid ""
"You cannot change the question type of a question that already has answers!"
msgstr "Typ otázky, která již má odpovědi, nelze změnit!"
