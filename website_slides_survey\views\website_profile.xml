<?xml version="1.0" encoding="utf-8"?>
<odoo><data>
    <template id="user_profile_content" inherit_id="website_profile.user_profile_content">
        <xpath expr="//div[@id='profile_about_badge']" position="before">
            <t t-if="channel">
                <div class="mb32">
                    <h5 class="border-bottom pb-1">Certifications</h5>
                    <t t-call="website_slides_survey.display_certificate"/>
                </div>
            </t>
        </xpath>
    </template>

     <template id="display_certificate">
        <t t-if="certificates">
            <div class="row">
               <div class="col-12 col-lg-6" t-foreach="certificates" t-as="certificate">
                    <div class="card mb-2">
                        <div class="card-body o_wprofile_slides_course_card_body p-0 d-flex" t-attf-onclick="location.href='/slides/#{slug(certificate.slide_id.channel_id)}';">
                            <div class="pl-5 pr-4 rounded-left" t-attf-style="background-image: url(#{website.image_url(certificate.slide_id, 'image_128')}); background-position: center"/>
                            <div class="p-2 w-100">
                                <h5 class="mt-0 mb-1" t-esc="certificate.survey_id.title"/>
                                <div t-if="user.id == uid">
                                    <small class="font-weight-bold">Score : <span t-esc="certificate.scoring_percentage"/> %</small>
                                    <div class="float-right">
                                        <a role="button" class="float-right" t-att-href="'/survey/%s/get_certification' % certificate.survey_id.id">
                                            <i class="fa fa-download" aria-label="Download certification" title="Download Certification"/>
                                        </a>
                                        <a role="button" class="mr-2" href="#"
                                             t-attf-onclick="event.stopPropagation(); $('#slideChannelShareModal_#{certificate.slide_id.channel_id.id}').modal('show');">
                                            <i class="fa fa-share-alt" aria-label="Share" title="Share"/>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <t t-call="website_slides.slide_share_modal">
                        <t t-set="record" t-value="certificate.slide_id.channel_id"/>
                    </t>
                </div>
            </div>
        </t>
        <t t-else="">
            <div class="text-muted d-inline-block">No certifications yet!</div>
        </t>
        <div class="text-right d-inline-block pull-right">
            <a href="/slides/all?slide_type=certification" class="btn btn-link btn-sm"><i class="fa fa-arrow-right mr-1"/>All Certifications</a>
        </div>
    </template>

    <template id="top3_user_card" inherit_id="website_profile.top3_user_card">
        <xpath expr="//div[hasclass('o_wprofile_top3_card_footer')]//div[last()]" position="after">
            <div class="col py-3"><b t-esc="user['certification_count']"/> <span class="text-muted">Certifications</span></div>
        </xpath>
    </template>

    <template id="all_user_card" inherit_id="website_profile.all_user_card">
        <xpath expr="//td[hasclass('all_user_badge_count')]" position="after">
            <td class="align-middle text-right pr-3 text-nowrap all_user_certification_count">
                <b t-esc="user['certification_count']"/> <span class="text-muted small font-weight-bold">Certifications</span>
            </td>
        </xpath>
    </template>

    <template id="badge_content" inherit_id="website_profile.badge_content">
        <xpath expr="//div[@id='website_profile_badges']" position="after">
            <t t-if="certification_badges">
                <div class="row">
                    <div class="col-12">
                        <h1 class="mt-4 mt-lg-2">Certification Badges</h1>
                        <p class="lead">
                            You can gain badges by passing certifications. Here is a list of all available certification badges.
                            <br />Follow the links to reach new heights and skill up!
                        </p>
                        <div class="row col-12 align-items-center p-0" t-foreach="certification_badges" t-as="badge">
                            <div class="col-3">
                                <t t-call="website_profile.badge_header">
                                    <t t-if="badge.id in certification_badge_urls" t-set="badge_url" t-value="certification_badge_urls[badge.id]"/>
                                </t>
                            </div>
                            <div class="col-6">
                                <span t-field="badge.description"/>
                            </div>
                            <div class="col-3 text-right">
                                <b t-esc="badge.granted_users_count"/>
                                <i class="text-muted"> awarded users</i>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </xpath>
    </template>
</data></odoo>
