<?xml version="1.0" encoding="utf-8"?>
<odoo><data>

    <record id="crm_team_member_view_tree" model="ir.ui.view">
        <field name="name">crm.team.member.view.tree</field>
        <field name="model">crm.team.member</field>
        <field name="inherit_id" ref="sales_team.crm_team_member_view_tree"/>
        <field name="arch" type="xml">
            <field name="user_id" position="after">
                <field name="assignment_enabled" invisible="1"/>
                <field name="assignment_optout"/>
                <field name="assignment_max"/>
                <field name="lead_month_count"/>
            </field>
        </field>
    </record>

    <record id="crm_team_member_view_kanban" model="ir.ui.view">
        <field name="name">crm.team.member.view.kanban.inherit.crm</field>
        <field name="model">crm.team.member</field>
        <field name="inherit_id" ref="sales_team.crm_team_member_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_kanban_details')]" position="after">
                <field name="assignment_enabled" invisible="1"/>
                <field name="assignment_optout" invisible="1"/>
                <div class="o_member_assignment"
                        attrs="{'invisible': ['|', ('assignment_enabled', '=', False), ('assignment_optout', '=', True)]}">
                    <field name="assignment_max" invisible="1"/>
                    <field name="lead_month_count" widget="gauge"
                        options="{'max_field': 'assignment_max'}"
                        attrs="{'invisible': [('assignment_max', '=', 0)]}"/>
                </div>
            </xpath>
        </field>
    </record>

    <record id="crm_team_member_view_form" model="ir.ui.view">
        <field name="name">crm.team.member.view.form.inherit.crm</field>
        <field name="model">crm.team.member</field>
        <field name="inherit_id" ref="sales_team.crm_team_member_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='member_partner_info']" position="after">
                <group name="group_assign" attrs="{'invisible': [('assignment_enabled', '=', False)]}">
                    <field name="assignment_enabled" invisible="1"/>
                    <field name="assignment_optout"/>
                    <label for="lead_month_count" attrs="{'invisible': [('assignment_optout', '=', True)]}"/>
                    <div attrs="{'invisible': [('assignment_optout', '=', True)]}">
                        <field name="lead_month_count" class="oe_inline"/>
                        <span class="oe_inline"> / </span>
                        <field name="assignment_max" class="oe_inline"/>
                        <span class="oe_inline"> (max) </span>
                    </div>
                    <field name="assignment_domain" string="Domain" widget="domain"
                        options="{'model': 'crm.lead'}"
                        attrs="{'invisible': ['|', ('assignment_max', '=', 0), ('assignment_optout', '=', True)]}"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="sales_team.crm_team_member_action" model="ir.actions.act_window">
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new salesman
            </p><p>
                Link salespersons to sales teams. Set their monthly lead capacity
                and configure automatic lead assignment.
            </p>
        </field>
    </record>

</data></odoo>
