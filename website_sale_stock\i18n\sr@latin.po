# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_stock
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.portal_order_page_shipping
msgid ""
"<span class=\"label label-danger label-text-align\"><i class=\"fa fa-fw fa-"
"times\"/> Cancelled</span>"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.portal_order_page_shipping
msgid ""
"<span class=\"label label-info label-text-align\"><i class=\"fa fa-fw fa-"
"clock-o\"/> Preparation</span>"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.portal_order_page_shipping
msgid ""
"<span class=\"label label-success label-text-align\"><i class=\"fa fa-fw fa-"
"truck\"/> Shipped</span>"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.portal_order_page_shipping
msgid ""
"<span class=\"label label-warning label-text-align\"><i class=\"fa fa-fw fa-"
"clock-o\"/> Partially Available</span>"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.portal_order_page_shipping
msgid "<strong>Delivery Orders</strong>"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_payment
msgid "<strong>Warning!</strong>"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_product_product_inventory_availability
#: model:ir.model.fields,help:website_sale_stock.field_product_template_inventory_availability
msgid "Adds an inventory availability status on the web product page."
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product_available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template_available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings_available_threshold
msgid "Availability Threshold"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product_cart_qty
msgid "Cart Qty"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product_custom_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template_custom_message
msgid "Custom Message"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Default Availability Mode"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created stockable products. This can "
"be changed at the product level."
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Default threshold"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:16
#, python-format
msgid "In stock"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings_inventory_availability
msgid "Inventory"
msgstr "Skladište"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product_inventory_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template_inventory_availability
msgid "Inventory Availability"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Manage the inventory of your products and display their availability status "
"on the website."
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product"
msgstr "Proizvod"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Quotation"
msgstr "Ponuda"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Stavka naloga za prodaju"

#. module: website_sale_stock
#: selection:product.template,inventory_availability:0
#: selection:res.config.settings,inventory_availability:0
msgid "Sell regardless of inventory"
msgstr ""

#. module: website_sale_stock
#: selection:product.template,inventory_availability:0
#: selection:res.config.settings,inventory_availability:0
msgid "Show inventory below a threshold and prevent sales if not enough stock"
msgstr ""

#. module: website_sale_stock
#: selection:product.template,inventory_availability:0
#: selection:res.config.settings,inventory_availability:0
msgid "Show inventory on website and prevent sales if not enough stock"
msgstr ""

#. module: website_sale_stock
#: selection:product.template,inventory_availability:0
#: selection:res.config.settings,inventory_availability:0
msgid "Show product-specific notifications"
msgstr ""

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:28
#, python-format
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry"
" for the inconvenience."
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:22
#, python-format
msgid "Temporarily out of stock"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order_line_warning_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order_warning_stock
msgid "Warning"
msgstr "Upozorenje"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
msgid "Website"
msgstr "Internet stranica"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:19
#, python-format
msgid "You already added"
msgstr ""

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:26
#, python-format
msgid "You ask for %s products but only %s is available"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:20
#, python-format
msgid "all"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:9
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:14
#, python-format
msgid "available"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:20
#, python-format
msgid "in your cart."
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "res.config.settings"
msgstr ""
