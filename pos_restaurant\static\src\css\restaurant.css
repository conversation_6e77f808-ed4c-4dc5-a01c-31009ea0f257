/* --- Restaurant Specific CSS --- */

.screen .screen-content-flexbox {
    margin:     0px auto;
    text-align: left;
    height:     100%;
    overflow:   hidden;
    position:   relative;
    display:    -webkit-flex;
    -webkit-flex-flow:  column nowrap;
    flex-flow:  column nowrap;
}

/* ------ FLOOR SELECTOR ------- */

.floor-selector {
    line-height: 48px;
    font-size: 18px;
    display: -webkit-flex;
    display: flex;
    text-align: center;
    width: 100%;
}
.floor-selector .button {
    cursor: pointer;
    border-left: dashed 1px rgb(196,196,196);
    -webkit-flex: 1;
    flex: 1;
}
.floor-selector .button:first-child {
    border-left: none;
}
.floor-selector .button.active {
    background: #6EC89B;
    color: white;
}

/* ------ FLOOR MAP ------- */

.floor-map {
    -webkit-flex: 1;
    flex: 1;
    position: relative;
    width: auto;
    height: 100%;
    box-shadow: 0px 6px 0px -3px rgba(0,0,0,0.07) inset;
    background: #D8D7D7;
    background-repeat: no-repeat;
    overflow: auto;
    background-size: cover;
    transition: all 300ms ease-in-out;
}

.floor-map .tables {
    position: relative;
    --scale: 1;
    transform: scale(var(--scale));
}
@media screen and (min-width: 1024px) {
    .floor-map .tables {
        max-width: 1024px;
        margin: auto;
        max-height: 540px;
        border-radius: 0px 0px 6px 6px;
        border: dashed 2px rgba(0,0,0,0.1);
        border-top: none;
        height: 100%;
    }
}

.floor-map .table{
    position: absolute;
    text-align: center;
    font-size: 18px;
    color: white;
    background: rgb(53, 211, 116);
    border-radius: 3px;
    cursor: pointer;
    box-shadow: 0px 3px rgba(0,0,0,0.07);
    transition: background, background-color 300ms ease-in-out;
    overflow: hidden;
}
.floor-map .table .table-cover {
    display: block;
    position: absolute;
    left: 0; right: 0; bottom: 0;
    border-radius: 0px 0px 3px 3px;
    background: rgba(0,0,0,0.2);
}
.floor-map .table .table-cover.full {
    border-radius: 3px 3px 3px 3px;
}
.floor-map .table .table-seats {
    position: absolute;
    display: inline-block;
    bottom: 0;
    left: 50%;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
    border-radius: 5px;
    margin-left: -16px;
    margin-bottom: 4px;
    background: black;
    color: white;
    opacity: 0.2;
    z-index: 3;
    padding: 0 4px;
}
.floor-map .table .label {
    display: block;
    max-height: 100%;
    overflow: hidden;
    position: relative;
    bottom: 5px;
}
.floor-map .table.selected {
    outline: solid rgba(255,255,255,0.3);
    cursor: move;
    z-index: 50;
}
.floor-map .table.selected .table-seats {
    margin-left: -12px;
}
.floor-map .edit-button.editing {
    position: fixed;
    top: 0;
    right: 0;
    font-size: 20px;
    margin: 8px;
    line-height: 32px;
    width: 32px;
    text-align: center;
    border-radius: 5px;
    cursor: pointer;
    border: solid 1px rgba(0,0,0,0.2);
}
.floor-map .edit-button.editing.active {
    background: #444;
    border-color: transparent;
    color: white;
}
.floor-map .edit-bar {
    position: fixed;
    top: 0;
    right: 40px;
    margin: 8px;
    line-height: 34px;
    text-align: center;
    border-radius: 5px;
    cursor: pointer;
    font-size: 20px;
    background: rgba(255,255,255,0.5);
    z-index: 100;
}
.floor-map .edit-bar .edit-button {
    position: relative;
    width: 32px;
    display: inline-block;
    cursor: pointer;
    margin-right: -4px;
    border-right: solid 1px rgba(0,0,0,0.2);
    transition: all 150ms linear;
}
.floor-map .edit-bar .edit-button.disabled {
    cursor: default;
}
.floor-map .edit-bar .edit-button.disabled > * {
    opacity: 0.5;
}
.floor-map .edit-bar .color-picker {
    position: absolute;
    left: 36px;
    top: 40px;
    width: 180px;
    height: 180px;
    border-radius: 3px;
    z-index: 100;
}
.floor-map .edit-bar .color-picker .color {
    display: block;
    float: left;
    cursor: pointer;
    width: 60px;
    height: 60px;
    background-color: gray;
}
.floor-map .edit-bar .color-picker .color.tl {  border-top-left-radius: 3px; }
.floor-map .edit-bar .color-picker .color.tr {  border-top-right-radius: 3px; }
.floor-map .edit-bar .color-picker .color.bl {  border-bottom-left-radius: 3px; }
.floor-map .edit-bar .color-picker .color.br {  border-bottom-right-radius: 3px; }

.floor-map .edit-bar .close-picker {
    position: absolute;
    bottom: 0;
    left: 50%;
    margin-left: -16px;
    margin-bottom: -16px;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-size: 20px;
    border-radius: 16px;
    background: black;
    color: white;
    cursor: pointer;
}

.floor-map .edit-bar .edit-button:last-child {
    margin-right: 0;
    border-right: none;
}

.floor-map .table.selected .table-handle {
    padding: 0px;
    position: absolute;
    width: 48px;
    height: 48px;
    left: 50%;
    top: 50%;
    border-radius: 24px;
    background: white;
    box-shadow: 0px 2px 3px rgba(0,0,0,0.2);
    /* See o-grab-cursor mixin */
    cursor: url(/web/static/src/img/openhand.cur), grab;
    transition: all 150ms linear;
    z-index: 100;
    transform: translate(-50%, -50%);
}
.floor-map .table.selected .table-handle:hover {
    width: 60px;
    height: 60px;
    border-radius: 30px;
}
.floor-map .table .table-handle.top {      top: 0;         }
.floor-map .table .table-handle.bottom {   top: 100%;      }
.floor-map .table .table-handle.left {     left: 0;        }
.floor-map .table .table-handle.right {    left: 100%;     }

.floor-map .table .order-count {
    position: absolute;
    top: 0;
    left: 50%;
    background: black;
    width: 20px;
    margin-top: 1px;
    margin-left: -10px;
    height: 20px;
    line-height: 20px;
    border-radius: 10px;
    font-size: 16px;
    z-index: 10;
}
.floor-map .table .order-count.notify-printing {
    background: red;
}
.floor-map .table .order-count.notify-skipped {
    background: blue;
}

.floor-map .empty-floor {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 400px;
    height: 40px;
    font-size: 18px;
    text-align: center;
    opacity: 0.6;
}
.floor-map .empty-floor i {
    display: inline-block;
    padding: 6px 7px 3px;
    margin: 0px 3px;
    background: rgba(255,255,255,0.5);
    border-radius: 3px;
}


/* ------ FLOOR BUTTON IN THE ORDER SELECTOR ------- */

.pos .order-button.floor-button {
    display: flex;
    background: #6EC89B;
    font-weight: bold;
    font-size: 16px;
    padding-left: 16px;
    padding-right: 16px;
    overflow-wrap: anywhere;
    text-overflow: hidden;
}
.pos .order-button.floor-button .table-name {
    font-weight: normal;
}
.pos .order-button.floor-button .fa{
    font-size: 24px;
    line-height: 42px;
}
/* ------ ORDER LINE STATUS ------- */

.pos .order .orderline.dirty {
    border-left: solid 6px #6EC89B;
    color: #6EC89B;
    padding-left: 9px;
}
.pos .order .orderline.skip {
    border-left: solid 6px #7F82AC;
    color: #7F82AC;
    padding-left: 9px;
}

/* ------ SPLIT BILL SCREEN ------- */

.splitbill-screen.screen .contents {
    display: flex;
    flex-flow: column nowrap;
    margin: 0px auto;
    max-width: 1024px;
    border-left: dashed 1px rgb(215,215,215);
    border-right: dashed 1px rgb(215,215,215);
    height: 100%;
}
.splitbill-screen.screen .main {
    display: flex;
    flex-flow: row nowrap;
    /* take the remaining vertical space */
    flex: 1;
    /* do not capture overflow in this element */
    overflow: hidden;
}
.splitbill-screen.screen .main .lines {
    display: flex;
    flex: 1;
    justify-content: center;
    /* show scrollbar inside this element if its content overflows */
    overflow-y: auto;
}
.splitbill-screen.screen .main .controls {
    display: flex;
    flex-flow: column nowrap;
}
.splitbill-screen.screen .pay-button {
    margin: 16px;
}
.splitbill-screen.screen .pay-button .button {
    background: #e2e2e2;
    line-height: 74px;
    font-size: 16px;
    border: solid 1px rgb(202, 202, 202);
    border-top-width: 0px;
    cursor: pointer;
    width: 100%;
    text-align: center;
}
.splitbill-screen.screen .pay-button .button:first-child {
    border-top-width: 1px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}
.splitbill-screen.screen .pay-button .button:last-child {
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
}
.splitbill-screen.screen .pay-button .button:active {
    background: black;
    border-color: black;
    color: white;
}

.splitbill-screen.screen .main .lines {
    border-right: dashed 1px rgb(215,215,215);
}

.splitbill-screen.screen .main .controls {
    flex: 1;
}

/* ------ NARROWER SCREEN ------ */
@media screen and (max-width: 768px) {
    .splitbill-screen.screen .main {
        flex-flow: column nowrap;
    }
    .splitbill-screen.screen .main .controls {
        border-top: dashed 1px rgb(215,215,215);
        flex: none;
    }
}

.tip-screen .tip-options .total-amount {
    text-align: center;
    font-size: x-large;
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}

.tip-screen .tip-amount-options {
    background: white;
    border-radius: 3px;
    margin-left: 4rem;
    margin-right: 4rem;
}

.tip-screen .percentage-amounts {
    display: flex;
}

.tip-screen .tip-options .button {
    background: #FAFAFA;
    border: solid 1px rgb(209, 209, 209);
    border-radius: 3px;
    flex: 1;
    margin: 1rem;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    cursor: pointer;
}

.tip-screen .tip-options .button .percentage {
    text-align: center;
    font-size: xx-large;
    color: #2196F3;
    padding-top: 4rem;
    padding-bottom: 0.75rem;
}

.tip-screen .tip-options .button .amount {
    text-align: center;
    font-size: large;
    color: #757575;
    padding-bottom: 4rem;
}

.tip-screen .custom-amount-form {
    display: flex;
}

.tip-screen .custom-amount-form .item {
    font-size: x-large;
    color: #2196F3;
    margin: 1rem;
    flex: 1
}

.tip-screen .custom-amount-form .label {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    text-align: center;
}

.tip-screen .custom-amount-form .input {
    background: #FAFAFA;
    border: solid 1px rgb(209, 209, 209);
    border-radius: 3px;
    margin: 1rem;
    text-align: right;
    overflow: hidden;
    position: relative;
}

.tip-screen .custom-amount-form .input .currency {
    position: absolute;
    display: flex;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3rem;
    justify-content: center;
    align-items: center;
}

.tip-screen .custom-amount-form .input input {
    border: none;
    padding: 0;
    outline: none;
    font-size: x-large;
    text-align: right;
    margin-left: 1rem;
    margin-right: 3rem;
    height: 100%;
    width: calc(100% - 4rem);
    background: #FAFAFA;
    color: #2196F3;
}

.tip-screen .custom-amount-form .input input:focus {
    border: none;
    padding: 0;
    outline: none;
}

.tip-screen .custom-amount-form .add {
    text-align: center;
}

.tip-screen .no-tip {
    display: flex;
    color: #2196F3;
    font-size: x-large;
}

.tip-screen .no-tip .button {
    text-align: center;
    flex: 1;
    padding: 1.5rem;
}

.tip-screen .pos-receipt-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    margin: 0;
}

@media print {
    .tip-screen .pos-receipt-container {
        display: block;
    }
    .tip-screen .pos-receipt-container * {
        visibility: visible;
    }
}

.pos-receipt .tip-form {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.pos-receipt .tip-form .title {
    text-align: center;
}

.pos-receipt .tip-form > div {
    margin-top: 1rem;
}

.pos-receipt .tip-form .option {
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
    flex: 1;
    justify-content: center;
}

.pos-receipt .tip-form .percentage-options {
    display: flex;
    flex-flow: row nowrap;
}

.ticket-screen .tip-cell {
    height: 100%;
    width: 100%;
    text-align: right;
    white-space: nowrap;
}

.ticket-screen .tip-cell input {
    width: 100%;
    text-align: right;
    font-size: medium;
    color: #555555;
    padding-bottom: 5px;
    border: none;
    outline: none;
}

.ticket-screen .tip-cell input:focus {
    border: none;
    outline: none;
    border-bottom: solid 1px #555555;
}

.multiprint-flex {
    display: flex;
    justify-content: space-between;
}
