# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    module_event_sale = fields.<PERSON><PERSON><PERSON>("Tickets")
    module_website_event_meet = fields.<PERSON><PERSON><PERSON>("Discussion Rooms")
    module_website_event_track = fields.<PERSON><PERSON><PERSON>("Tracks and Agenda")
    module_website_event_track_live = fields.<PERSON><PERSON><PERSON>("Live Mode")
    module_website_event_track_quiz = fields.<PERSON><PERSON><PERSON>("Quiz on Tracks")
    module_website_event_exhibitor = fields.<PERSON><PERSON><PERSON>("Advanced Sponsors")
    module_website_event_questions = fields.<PERSON><PERSON><PERSON>("Registration Survey")
    module_event_barcode = fields.<PERSON><PERSON><PERSON>("Barcode")
    module_website_event_sale = fields.<PERSON><PERSON>an("Online Ticketing")
    module_event_booth = fields.<PERSON><PERSON><PERSON>("Booth Management")

    @api.onchange('module_website_event_track')
    def _onchange_module_website_event_track(self):
        """ Reset sub-modules, otherwise you may have track to False but still
        have track_live or track_quiz to True, meaning track will come back due
        to dependencies of modules. """
        for config in self:
            if not config.module_website_event_track:
                config.module_website_event_track_live = False
                config.module_website_event_track_quiz = False
