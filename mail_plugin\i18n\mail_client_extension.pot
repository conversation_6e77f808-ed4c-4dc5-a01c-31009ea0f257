# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_client_extension
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-29 13:45+0000\n"
"PO-Revision-Date: 2020-09-29 13:45+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: mail_client_extension
#: model_terms:ir.ui.view,arch_db:mail_client_extension.app_auth
msgid "Allow"
msgstr ""

#. module: mail_client_extension
#: model:ir.model,name:mail_client_extension.model_res_partner
msgid "Contact"
msgstr ""

#. module: mail_client_extension
#: model_terms:ir.ui.view,arch_db:mail_client_extension.app_auth
msgid "Deny"
msgstr ""

#. module: mail_client_extension
#: model:ir.model.fields,field_description:mail_client_extension.field_crm_lead__display_name
#: model:ir.model.fields,field_description:mail_client_extension.field_ir_http__display_name
#: model:ir.model.fields,field_description:mail_client_extension.field_res_partner__display_name
msgid "Display Name"
msgstr ""

#. module: mail_client_extension
#: model:ir.model,name:mail_client_extension.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: mail_client_extension
#: model:ir.model.fields,field_description:mail_client_extension.field_res_partner__iap_enrich_info
#: model:ir.model.fields,field_description:mail_client_extension.field_res_users__iap_enrich_info
msgid "IAP Enrich Info"
msgstr ""

#. module: mail_client_extension
#: model:ir.model.fields,field_description:mail_client_extension.field_crm_lead__id
#: model:ir.model.fields,field_description:mail_client_extension.field_ir_http__id
#: model:ir.model.fields,field_description:mail_client_extension.field_res_partner__id
msgid "ID"
msgstr ""

#. module: mail_client_extension
#: model:ir.model.fields,field_description:mail_client_extension.field_crm_lead____last_update
#: model:ir.model.fields,field_description:mail_client_extension.field_ir_http____last_update
#: model:ir.model.fields,field_description:mail_client_extension.field_res_partner____last_update
msgid "Last Modified on"
msgstr ""

#. module: mail_client_extension
#: model:ir.model,name:mail_client_extension.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: mail_client_extension
#: model_terms:ir.ui.view,arch_db:mail_client_extension.app_auth
msgid "Let"
msgstr ""

#. module: mail_client_extension
#: model:ir.actions.server,name:mail_client_extension.lead_creation_prefilled_action
msgid "Redirection to the lead creation form with prefilled info"
msgstr ""

#. module: mail_client_extension
#: model:ir.model.fields,help:mail_client_extension.field_res_partner__iap_enrich_info
#: model:ir.model.fields,help:mail_client_extension.field_res_users__iap_enrich_info
msgid "Stores additional info retrieved from IAP in JSON"
msgstr ""

#. module: mail_client_extension
#: model_terms:ir.ui.view,arch_db:mail_client_extension.app_auth
msgid "access your Odoo database?"
msgstr ""
