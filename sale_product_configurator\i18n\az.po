# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_configurator
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2023\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "<i class=\"fa fa-shopping-cart add-optionnal-item\"/> Add to cart"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Price</span>"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Product</span>"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Quantity</span>"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<strong>Total:</strong>"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Add"
msgstr "Əlavə edin"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Add one"
msgstr "Birini əlavə et"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "Available Options:"
msgstr ""

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Back"
msgstr "Geri"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Cancel"
msgstr "Ləğv edin"

#. module: sale_product_configurator
#: model:product.template,name:sale_product_configurator.product_product_1_product_template
msgid "Chair floor protection"
msgstr ""

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Configure"
msgstr "Konfiqurasiya edin"

#. module: sale_product_configurator
#: model:ir.actions.act_window,name:sale_product_configurator.sale_product_configurator_action
msgid "Configure a product"
msgstr ""

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Confirm"
msgstr "Təsdiq edin"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr ""

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__display_name
msgid "Display Name"
msgstr "Ekran Adı"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr ""

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__id
msgid "ID"
msgstr "ID"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr ""

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator____last_update
msgid "Last Modified on"
msgstr "Son Dəyişdirilmə tarixi"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: sale_product_configurator
#: model:product.template,description_sale:sale_product_configurator.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Option not available"
msgstr ""

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "İtəyə bağlı Məhsullar"

#. module: sale_product_configurator
#: model:ir.model.fields,help:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale_product_configurator.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr ""

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__pricelist_id
msgid "Pricelist"
msgstr "Qiymət siyahısı"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_template_id
msgid "Product"
msgstr "Məhsul"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Product Image"
msgstr ""

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_product_template
msgid "Product Template"
msgstr "Məhsul Şablonu"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_order_view_form
msgid "Product Variant"
msgstr "Məhsul Çeşidi"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__quantity
msgid "Quantity"
msgstr "Miqdar"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Remove one"
msgstr "Birini Silin"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_product_configurator
msgid "Sale Product Configurator"
msgstr "Satış Məhsul Konfiquratoru"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_order_line
msgid "Sales Order Line"
msgstr "Satış Sifarişi Sətri"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Save"
msgstr "Yadda Saxla"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This combination does not exist."
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This product has no valid combination."
msgstr ""

#. module: sale_product_configurator
#: model:product.template,uom_name:sale_product_configurator.product_product_1_product_template
msgid "Units"
msgstr "Vahidlər"
