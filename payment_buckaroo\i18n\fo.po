# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_buckaroo
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Faroese (https://www.transifex.com/odoo/teams/41243/fo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:141
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:139
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:148
#, python-format
msgid "<PERSON>aroo: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:137
#, python-format
msgid "Buckaroo: received data for reference %s"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:131
#, python-format
msgid ""
"Buckaroo: received data with missing reference (%s) or pay_id (%s) or "
"shasign (%s)"
msgstr ""

#. module: payment_buckaroo
#: model_terms:ir.ui.view,arch_db:payment_buckaroo.acquirer_form_buckaroo
msgid "How to configure your Buckaroo account?"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer_brq_secretkey
msgid "SecretKey"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer_brq_websitekey
msgid "WebsiteKey"
msgstr ""
