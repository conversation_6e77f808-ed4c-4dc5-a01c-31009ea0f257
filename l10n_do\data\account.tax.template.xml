<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Chart template for Taxes -->
        <record id="tax_0_sale" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">4</field>
            <field name="name">Exento ITBIS Ventas</field>
            <field name="description">Exento</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_2A3_ingrs_local_vnts')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_2A3_ingrs_local_vnts')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax_0_purch" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">10</field>
            <field name="name">Exento ITBIS Compras</field>
            <field name="description">Exento</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax_18_sale" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">1</field>
            <field name="name">18% ITBIS Ventas</field>
            <field name="description">18% ITBIS</field>
            <field name="amount">18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_2b6_grvd')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030102'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_18_casilla')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_2b6_grvd')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030102'),
                    'minus_report_line_ids': [ref('account_tax_report_itbs_18_casilla')],
                }),
            ]"/>
        </record>

        <record id="tax_18_sale_incl" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">2</field>
            <field name="name">18% ITBIS Incl. Ventas</field>
            <field name="description">18% ITBIS</field>
            <field name="amount">18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field eval="1" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_2b6_grvd')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030102'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_18_casilla')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_2b6_grvd')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030102'),
                    'minus_report_line_ids': [ref('account_tax_report_itbs_18_casilla')],
                }),
            ]"/>
        </record>

        <record id="tax_tip_sale" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">3</field>
            <field name="name">10% Propina Legal</field>
            <field name="description">10% Legal</field>
            <field name="amount">10</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="tax_group_tip"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030503'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030503'),
                }),
            ]"/>
        </record>

        <record id="tax_18_purch" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">18% ITBIS Compras</field>
            <field name="sequence">11</field>
            <field name="description">18% ITBIS (B)</field>
            <field name="amount">18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                    'minus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
        </record>

        <record id="tax_18_purch_incl" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">12</field>
            <field name="name">18% ITBIS Incl. Compras</field>
            <field name="description">18% ITBIS (Incl B)</field>
            <field name="amount">18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="1" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                    'minus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
        </record>

        <record id="tax_16_purch" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">16% ITBIS Compras</field>
            <field name="sequence">13</field>
            <field name="description">16% ITBIS (B)</field>
            <field name="amount">16</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
        </record>

        <record id="tax_16_purch_incl" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">16% ITBIS Incl. Compras</field>
            <field name="sequence">14</field>
            <field name="description">16% ITBIS (Incl B)</field>
            <field name="amount">16</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="1" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
        </record>

        <record id="tax_9_purch" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">9% ITBIS Compras</field>
            <field name="sequence">15</field>
            <field name="description">9% ITBIS (L690-16)</field>
            <field name="amount">9</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
        </record>

        <record id="tax_9_purch_incl" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">9% ITBIS Incl. Compras</field>
            <field name="sequence">16</field>
            <field name="description">9% ITBIS (L690-16)</field>
            <field name="amount">9</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="1" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
        </record>

        <record id="tax_8_purch" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">8% ITBIS Compras</field>
            <field name="sequence">17</field>
            <field name="description">8% ITBIS (L690-16)</field>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
        </record>

        <record id="tax_8_purch_incl" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">8% ITBIS Incl. Compras</field>
            <field name="sequence">18</field>
            <field name="description">8% ITBIS (L690-16)</field>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="1" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080101'),
                }),
            ]"/>
        </record>

        <record id="tax_tip_purch" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">19</field>
            <field name="name">10% Propina Legal</field>
            <field name="description">10% Legal</field>
            <field name="amount">10</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="tax_group_tip"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_52080900'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_52080900'),
                }),
            ]"/>
        </record>

        <record id="tax_18_purch_serv" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">20</field>
            <field name="name">18% ITBIS Compras - Servicios</field>
            <field name="description">18% ITBIS (S)</field>
            <field name="amount">18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080102'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080102'),
                    'minus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
        </record>

        <record id="tax_18_purch_serv_incl" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">20</field>
            <field name="name">18% ITBIS Incl. Compras - Servicios</field>
            <field name="description">18% ITBIS (Incl S)</field>
            <field name="amount">18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="1" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080102'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080102'),
                    'minus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
        </record>

        <record id="tax_18_importation" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">20</field>
            <field name="name">18% ITBIS - Importaciones</field>
            <field name="description">18% ITBIS (IMP)</field>
            <field name="amount">18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080103'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_pgdo_imptcn')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080103'),
                    'minus_report_line_ids': [ref('account_tax_report_itbs_pgdo_imptcn')],
                }),
            ]"/>
        </record>

        <record id="tax_18_of_10" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">20</field>
            <field name="name">18% ITBIS sobre el 10% del Monto Total</field>
            <field name="description">18% del 10%</field>
            <field name="amount">1.8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030102'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030102'),
                }),
            ]"/>
        </record>

        <record id="tax_0015_bank" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">30</field>
            <field name="name">0.15% Transferencia Bancaria</field>
            <field name="description">0.15% Trans</field>
            <field name="amount">0.0015</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">none</field>
            <field eval="1" name="price_include"/>
            <field name="tax_group_id" ref="group_tax"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_52070200'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_52070200'),
                }),
            ]"/>
        </record>

        <record id="tax_10_telco" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">30</field>
            <field name="name">10% ISC Telecomunicaciones</field>
            <field name="description">10% ISC</field>
            <field name="amount">10</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="tax_group_isc"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_52020200'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_52020200'),
                }),
            ]"/>
        </record>

        <record id="tax_2_telco" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">30</field>
            <field name="name">2% CDT Telecomunicaciones</field>
            <field name="description">2% CDT</field>
            <field name="amount">2</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_tax"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_52020200'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_52020200'),
                }),
            ]"/>
        </record>

        <record id="tax_group_telco" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">30</field>
            <field name="name">Impuestos a las Telecomunicaciones</field>
            <field eval="18" name="amount"/>
            <field name="amount_type">group</field>
            <field name="type_tax_use">purchase</field>
            <field eval="[(6, 0, [ref('tax_18_purch'), ref('tax_10_telco'), ref('tax_2_telco')])]" name="children_tax_ids"/>
            <field name="tax_group_id" ref="group_tax"/>
        </record>

        <record id="ret_100_tax_security" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">40</field>
            <field name="name">Retención 100% ITBIS Servicios de Seguridad (N07-09)</field>
            <field name="description">-100% ITBIS (N07-09)</field>
            <field name="amount">-18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030201'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030201'),
                }),
            ]"/>
        </record>

        <record id="ret_100_tax_nonprofit" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">41</field>
            <field name="name">Retención 100% ITBIS Servicios No Lucrativas (N01-11)</field>
            <field name="description">-100% ITBIS (N01-11)</field>
            <field name="amount">-18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_retcn_prsn')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030203'),
                    'minus_report_line_ids': [ref('account_tax_report_itbs_retcn_prsn')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_retcn_prsn')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030203'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_retcn_prsn')],
                }),
            ]"/>
        </record>

        <record id="ret_100_tax_person" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">42</field>
            <field name="name">Retención 100% ITBIS Servicios a Físicas (R293-11)</field>
            <field name="description">-100% ITBIS (R293-11)</field>
            <field name="amount">-18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_retcn_prsn')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030202'),
                    'minus_report_line_ids': [ref('account_tax_report_itbs_retcn_prsn')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_retcn_prsn')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030202'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_retcn_prsn')],
                }),
            ]"/>
        </record>

        <record id="ret_30_tax_moral" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">43</field>
            <field name="name">Retención 30% ITBIS Servicios a Jurídicas (N02-05)</field>
            <field name="description">-30% ITBIS (N02-05)</field>
            <field name="amount">-5.4</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030201'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030201'),
                }),
            ]"/>
        </record>

        <record id="ret_30_tax_freelance" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="active">False</field>
            <field name="sequence">43</field>
            <field name="name">Retención 30% ITBIS Servicios Profesionales (N02-05)</field>
            <field name="description">-30% ITBIS (N02-05)</field>
            <field name="amount">-5.4</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">none</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030201'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030201'),
                }),
            ]"/>
        </record>

        <record id="ret_75_tax_nonformal" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">44</field>
            <field name="name">Retención 75% ITBIS Bienes a Informales (N08-10)</field>
            <field name="description">-75% ITBIS (N08-10)</field>
            <field name="amount">-13.5</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030205'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030205'),
                }),
            ]"/>
        </record>

        <record id="ret_10_income_person" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">40</field>
            <field name="name">Retención 10% ISR Honorarios a Físicas</field>
            <field name="description">-10% ISR</field>
            <field name="amount">-10</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_isr"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030301'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030301'),
                }),
            ]"/>
        </record>

        <record id="ret_10_income_rent" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">50</field>
            <field name="name">Retención 10% ISR Alquileres a Físicas</field>
            <field name="description">-10% ISR</field>
            <field name="amount">-10</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_isr"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030302'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030302'),
                }),
            ]"/>
        </record>

        <record id="ret_10_income_dividend" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">51</field>
            <field name="name">Retención 10% ISR por Dividendos (L253-12)</field>
            <field name="description">-10% ISR</field>
            <field name="amount">-10</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_isr"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030303'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030303'),
                }),
            ]"/>
        </record>

        <record id="ret_2_income_person" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">52</field>
            <field name="name">Retención 2% ISR a Física (con Materiales)</field>
            <field name="description">-2% ISR (N07-07)</field>
            <field name="amount">-2</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_isr"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030308'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030308'),
                }),
            ]"/>
        </record>

        <record id="ret_2_income_transfer" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">53</field>
            <field name="name">Retención 2% ISR por Transferencia de Títulos</field>
            <field name="description">-2% ISR</field>
            <field name="amount">-2</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_isr"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030306'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030306'),
                }),
            ]"/>
        </record>

        <record id="ret_27_income_remittance" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">49</field>
            <field name="name">Retención 27% ISR por Remesas al Exterior (L253-12)</field>
            <field name="description">-27% ISR</field>
            <field name="amount">-27</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_isr"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030307'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_21030307'),
                }),
            ]"/>
        </record>

        <record id="ret_5_income_gov" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">50</field>
            <field name="name">Retención 5% ISR Gubernamentales</field>
            <field name="description">-5% ISR</field>
            <field name="amount">-5</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_isr"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080302'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080302'),
                }),
            ]"/>
        </record>

        <record id="tax_group_nonformal" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">60</field>
            <field name="name">Retención a Proveedores Informales de Bienes (75%)</field>
            <field name="amount_type">group</field>
            <field eval="18" name="amount"/>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="group_ret"/>
            <field eval="[(6, 0, [ref('tax_18_purch'), ref('ret_75_tax_nonformal')])]" name="children_tax_ids"/>
        </record>

        <record id="tax_group_person_construction" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">61</field>
            <field name="name">Retención a Físicas por Servicios con Materiales (2%)</field>
            <field name="amount_type">group</field>
            <field eval="18" name="amount"/>
            <field name="type_tax_use">purchase</field>
            <field eval="[(6, 0, [ref('tax_18_purch'), ref('ret_100_tax_person'), ref('ret_2_income_person')])]" name="children_tax_ids"/>
            <field name="tax_group_id" ref="group_ret"/>
        </record>

        <record id="tax_group_person_services" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">58</field>
            <field name="name">Retención a Físicas por Honorarios por Servicios (10%)</field>
            <field name="amount_type">group</field>
            <field eval="18" name="amount"/>
            <field name="type_tax_use">purchase</field>
            <field eval="[(6, 0, [ref('tax_18_purch'), ref('ret_100_tax_person'), ref('ret_10_income_person')])]" name="children_tax_ids"/>
            <field name="tax_group_id" ref="group_ret"/>
        </record>

        <record id="tax_group_moral_services" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">58</field>
            <field name="name">Retención a Jurídicas por Servicios Profesionales (30%)</field>
            <field name="amount_type">group</field>
            <field eval="18" name="amount"/>
            <field name="type_tax_use">purchase</field>
            <field eval="[(6, 0, [ref('tax_18_purch'), ref('ret_30_tax_moral')])]" name="children_tax_ids"/>
            <field name="tax_group_id" ref="group_ret"/>
        </record>

        <record id="tax_group_restaurant_sale" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">64</field>
            <field name="name">Ventas del Restaurante</field>
            <field name="amount_type">group</field>
            <field eval="18" name="amount"/>
            <field name="type_tax_use">sale</field>
            <field eval="[(6, 0, [ref('tax_18_sale'), ref('tax_tip_sale')])]" name="children_tax_ids"/>
            <field name="tax_group_id" ref="group_ret"/>
        </record>

        <record id="tax_group_restaurant_purch" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="sequence">65</field>
            <field name="name">Compras a Restaurantes</field>
            <field name="amount_type">group</field>
            <field eval="18" name="amount"/>
            <field name="type_tax_use">purchase</field>
            <field eval="[(6, 0, [ref('tax_18_purch'), ref('tax_tip_purch')])]" name="children_tax_ids"/>
            <field name="tax_group_id" ref="group_ret"/>
        </record>

        <record id="tax_18_10_total_mount" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">18% ITBIS sobre el 10% del Monto Total</field>
            <field name="description">18% del 10%</field>
            <field name="amount">1.8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080102'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_11080102'),
                }),
            ]"/>
        </record>

        <record id="tax_18_property_cost" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">18% ITBIS llevado al Costo Bienes</field>
            <field name="description">18%</field>
            <field name="amount">18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_51010500'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_51010500'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
        </record>

        <record id="tax_18_serv_cost" model="account.tax.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">18% ITBIS llevado al Costo Servicios</field>
            <field name="description">18%</field>
            <field name="amount">18</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field eval="0" name="price_include"/>
            <field name="tax_group_id" ref="group_itbis"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_51010500'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('do_niif_51010500'),
                    'plus_report_line_ids': [ref('account_tax_report_itbs_pgdo_locales')],
                }),
            ]"/>
        </record>

    </data>
</odoo>
