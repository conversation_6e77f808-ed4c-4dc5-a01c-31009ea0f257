<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <!-- Account Tax Template -->
        <record id="account_tax_template_IV_0" model="account.tax.template">
            <field name="name">Sales Tax</field>
            <field name="description">IV</field>
            <field name="sequence">10</field>
            <field name="amount">13</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="account_chart_template_0"/>
            <field name="tax_group_id" ref="tax_group_13"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_account_template_0_212101'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_account_template_0_212101'),
                }),
            ]"/>
        </record>
        <record id="account_tax_template_IV_1" model="account.tax.template">
            <field name="name">Purchase Tax</field>
            <field name="description">IV</field>
            <field name="sequence">10</field>
            <field name="amount">13</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="account_chart_template_0"/>
            <field name="tax_group_id" ref="tax_group_13"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_account_template_0_212101'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_account_template_0_212101'),
                }),
            ]"/>
        </record>

        <!-- Default Tax -->
        <record id="account_account_template_0_410001" model="account.account.template">
            <field name="tax_ids" eval="[(6,0,[ref('account_tax_template_IV_0')])]"/>
        </record>
</odoo>
