# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * board
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-07 16:41+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Hindi (http://www.transifex.com/odoo/odoo-9/language/hi/)\n"
"Language: hi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:24
#, python-format
msgid "&nbsp;"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/dashboard.js:407
#, python-format
msgid "'%s' added to dashboard"
msgstr ""

#. module: board
#: model_terms:ir.actions.act_window,help:board.open_board_my_dash_action
msgid "<b>Your personal dashboard is empty.</b>"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:70
#, python-format
msgid "Add"
msgstr "जोड़ना"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:64
#, python-format
msgid "Add to my Dashboard"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/dashboard.js:139
#, python-format
msgid "Are you sure you want to remove this item ?"
msgstr ""

#. module: board
#: model:ir.model,name:board.model_board_board
msgid "Board"
msgstr ""

#. module: board
#: model:ir.model,name:board.model_board_create
msgid "Board Creation"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_create_name
msgid "Board Name"
msgstr ""

#. module: board
#: model_terms:ir.ui.view,arch_db:board.view_board_create
msgid "Cancel"
msgstr "रद्द"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:6
#, python-format
msgid "Change Layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:4
#, python-format
msgid "Change Layout.."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:36
#, python-format
msgid "Choose dashboard layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/dashboard.js:409
#, python-format
msgid "Could not add filter to dashboard"
msgstr ""

#. module: board
#: model_terms:ir.ui.view,arch_db:board.view_board_create
msgid "Create"
msgstr "बनाएँ"

#. module: board
#: model:ir.actions.act_window,name:board.action_board_create
#: model:ir.ui.menu,name:board.menu_board_create
msgid "Create Board"
msgstr ""

#. module: board
#: model_terms:ir.ui.view,arch_db:board.view_board_create
msgid "Create New Dashboard"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_create_create_uid
msgid "Created by"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_create_create_date
msgid "Created on"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board_display_name
#: model:ir.model.fields,field_description:board.field_board_create_display_name
msgid "Display Name"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/dashboard.js:94
#, python-format
msgid "Edit Layout"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board_id
#: model:ir.model.fields,field_description:board.field_board_create_id
msgid "ID"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board___last_update
#: model:ir.model.fields,field_description:board.field_board_create___last_update
msgid "Last Modified on"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_create_write_uid
msgid "Last Updated by"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_create_write_date
msgid "Last Updated on"
msgstr ""

#. module: board
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
msgid "My Dashboard"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_create_menu_parent_id
msgid "Parent Menu"
msgstr ""

#. module: board
#: model_terms:ir.actions.act_window,help:board.open_board_my_dash_action
msgid ""
"To add your first report into this dashboard, go to any\n"
"                    menu, switch to list or graph view, and click <i>'Add "
"to\n"
"                    Dashboard'</i> in the extended search options."
msgstr ""

#. module: board
#: model_terms:ir.actions.act_window,help:board.open_board_my_dash_action
msgid ""
"You can filter and group data before inserting into the\n"
"                    dashboard using the search options."
msgstr ""
