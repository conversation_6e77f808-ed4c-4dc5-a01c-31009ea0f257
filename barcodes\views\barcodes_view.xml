<?xml version="1.0"?>
<odoo>
        <!--     BARCODE NOMENCLATURES     -->
        <record model="ir.ui.view" id="view_barcode_nomenclature_form">
            <field name="name">Barcode Nomenclatures</field>
            <field name="model">barcode.nomenclature</field>
            <field name="arch" type="xml">
                <form string="Barcode Nomenclature">
                    <sheet>
                        <group name="general_attributes" col="4">
                            <field name="name" />
                            <field name="upc_ean_conv"/>
                        </group>
                        <div>
                            <p>
                                <i>Barcodes Nomenclatures</i> define how barcodes are recognized and categorized.
                                When a barcode is scanned it is associated to the <i>first</i> rule with a matching
                                pattern. The pattern syntax is that of regular expression, and a barcode is matched
                                if the regular expression matches a prefix of the barcode. 
                            </p><p>
                                Patterns can also define how numerical values, such as weight or price, can be
                                encoded into the barcode. They are indicated by <code>{NNN}</code> where the N's
                                define where the number's digits are encoded. Floats are also supported with the 
                                decimals indicated with D's, such as <code>{NNNDD}</code>. In these cases, 
                                the barcode field on the associated records <i>must</i> show these digits as 
                                zeroes. 
                            </p>
                        </div>
                        <field name="rule_ids">
                            <tree string='Tables'>
                                <field name="sequence" widget="handle"/>
                                <field name="name" />
                                <field name="type" />
                                <field name="encoding" />
                                <field name="pattern" />
                            </tree>
                        </field>
                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="view_barcode_nomenclature_tree">
            <field name="name">Barcode Nomenclatures</field>
            <field name="model">barcode.nomenclature</field>
            <field name="arch" type="xml">
                <tree string="Barcode Nomenclatures">
                    <field name="name" />
                </tree>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_barcode_nomenclature_form">
            <field name="name">Barcode Nomenclatures</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">barcode.nomenclature</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Add a new barcode nomenclature
              </p><p>
                A barcode nomenclature defines how the point of sale identify and interprets barcodes
              </p>
            </field>
        </record>

        <record model="ir.ui.view" id="view_barcode_rule_form">
            <field name="name">Barcode Rule</field>
            <field name="model">barcode.rule</field>
            <field name="arch" type="xml">
                <form string="Barcode Rule">
                    <group col="4">
                        <field name="name" />
                        <field name="sequence" />
                        <field name="type"/>  
                        <field name="encoding" attrs="{'invisible': [('type','=', 'alias')]}"/> 
                        <field name="pattern" />
                        <field name="alias" attrs="{'invisible': [('type','!=', 'alias')]}"/>   
                    </group>
                </form>
            </field>
        </record>
</odoo>
