<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <template id="label_product_product_view">
		<t t-foreach="quantity.items()" t-as="barcode_and_qty_by_product">
                <t t-set="product" t-value="barcode_and_qty_by_product[0]"/>
                <t t-foreach="barcode_and_qty_by_product[1]" t-as="barcode_and_qty">
                    <t t-set="barcode" t-value="barcode_and_qty[0]"/>
                    <t t-foreach="range(barcode_and_qty[1])" t-as="qty">
                        <t t-translation="off">
^XA
^FT100,80^A0N,40,30^FD<t t-esc="product.display_name"/>^FS
<t t-if="product.default_code and len(product.default_code) &gt; 15">
^FT100,115^A0N,30,24^FD<t t-esc="product.default_code[:15]"/>^FS
^FT100,150^A0N,30,24^FD<t t-esc="product.default_code[15:30]"/>^FS
</t>
<t t-else="">
^FT100,150^A0N,30,24^FD<t t-esc="product.default_code"/>^FS
</t>
<t t-if="price_included">
^FO600,100,1
^CI28
<t t-if="product.currency_id.position == 'after'">
^A0N,66,48^FH^FD<t t-esc="product.lst_price if 'lst_price' in product else product.list_price" t-options='{"widget": "float", "precision": 2}'/><t t-esc="product.currency_id.symbol"/>^FS
</t>
<t t-if="product.currency_id.position == 'before'">
^A0N,66,48^FH^FD<t t-esc="product.currency_id.symbol"/><t t-esc="product.lst_price if 'lst_price' in product else product.list_price" t-options='{"widget": "float", "precision": 2}'/>^FS
</t>
</t>
<t t-if="barcode">
^FO100,160^BY3
^BCN,100,Y,N,N
^FD<t t-esc="barcode"/>^FS
</t>
^XZ
                        </t>
                    </t>
                </t>
            </t>
        </template>
        <template id="label_lot_template_view">
            <t t-foreach="docs" t-as="lot">
                <t t-translation="off">
^XA
^FO100,50
^A0N,44,33^FD<t t-esc="lot.product_id.display_name"/>^FS
^FO100,100
^A0N,44,33^FDLN/SN: <t t-esc="lot.name"/>^FS
^FO100,150^BY3
^BCN,100,Y,N,N
^FD<t t-esc="lot.name"/>^FS
^XZ
                </t>
            </t>
        </template>
    </data>
</odoo>
