# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_no
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-09 14:29+0000\n"
"PO-Revision-Date: 2023-02-09 14:29+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_no
#: model:ir.model.fields,field_description:l10n_no.field_account_bank_statement_import_journal_creation__invoice_reference_model
#: model:ir.model.fields,field_description:l10n_no.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr "Kommunikasjonsstandard"

#. module: l10n_no
#: model:ir.model,name:l10n_no.model_res_company
msgid "Companies"
msgstr "Bedrifter"

#. module: l10n_no
#: model:ir.model,name:l10n_no.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: l10n_no
#: model:ir.model,name:l10n_no.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_no
#: model:ir.model,name:l10n_no.model_account_move
msgid "Journal Entry"
msgstr "Journalpost"

#. module: l10n_no
#: model:account.tax.group,name:l10n_no.tax_group_0
msgid "MVA 0%"
msgstr ""

#. module: l10n_no
#: model:account.tax.group,name:l10n_no.tax_group_10
msgid "MVA 10%"
msgstr ""

#. module: l10n_no
#: model:account.tax.group,name:l10n_no.tax_group_12
msgid "MVA 12%"
msgstr ""

#. module: l10n_no
#: model:account.tax.group,name:l10n_no.tax_group_15
msgid "MVA 15%"
msgstr ""

#. module: l10n_no
#: model:account.tax.group,name:l10n_no.tax_group_25
msgid "MVA 25%"
msgstr ""

#. module: l10n_no
#: model:ir.model.fields.selection,name:l10n_no.selection__account_journal__invoice_reference_model__no
msgid "Norway"
msgstr "Norge"

#. module: l10n_no
#: model:ir.ui.menu,name:l10n_no.account_reports_no_statements_menu
msgid "Norwegian Statements"
msgstr "Norske uttalelser"

#. module: l10n_no
#: model:ir.model.fields,field_description:l10n_no.field_res_company__l10n_no_bronnoysund_number
#: model:ir.model.fields,field_description:l10n_no.field_res_partner__l10n_no_bronnoysund_number
#: model:ir.model.fields,field_description:l10n_no.field_res_users__l10n_no_bronnoysund_number
msgid "Register of Legal Entities (Brønnøysund Register Center)"
msgstr "Brønnøysundregistrene"

#. module: l10n_no
#: model:ir.model.fields,help:l10n_no.field_account_bank_statement_import_journal_creation__invoice_reference_model
#: model:ir.model.fields,help:l10n_no.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""
