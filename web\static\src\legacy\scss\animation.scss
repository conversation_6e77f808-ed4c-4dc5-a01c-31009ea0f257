/************** DEFINITION of ANIMATIONS **************/
// 'marked' effects
@keyframes markAnim {
    0% {
        opacity: 0;
        transform: scaleX(0.50) scaleY(0.50);
    }
    30% {
        opacity: 1;
        transform: scaleX(1.00) scaleY(1.00);
    }
    100% {
        opacity: 0;
        transform: scaleX(1.00) scaleY(1.00);
    }
}

@-moz-keyframes markAnim {
    0% {
        opacity: 0;
        -moz-transform: scaleX(0.50) scaleY(0.50);
    }
    30% {
        opacity: 1;
        -moz-transform: scaleX(1.00) scaleY(1.00);
    }
    100% {
        opacity: 0;
        -moz-transform: scaleX(1.00) scaleY(1.00);
    }
}

@-webkit-keyframes markAnim {
    0% {
        opacity: 0;
        -webkit-transform: scaleX(0.50) scaleY(0.50);
    }
    30% {
        opacity: 1;
        -webkit-transform: scaleX(1.00) scaleY(1.00);
    }
    100% {
        opacity: 0;
        -webkit-transform: scaleX(1.00) scaleY(1.00);
    }
}

@-o-keyframes markAnim {
    0% {
        opacity: 0;
        -o-transform: scaleX(0.50) scaleY(0.50);
    }
    30% {
        opacity: 1;
        -o-transform: scaleX(1.00) scaleY(1.00);
    }
    100% {
        opacity: 0;
        -o-transform: scaleX(1.00) scaleY(1.00);
    }
}

@-ms-keyframes markAnim {
    0% {
        opacity: 0;
        -ms-transform: scaleX(0.50) scaleY(0.50);
    }
    30% {
        opacity: 1;
        -ms-transform: scaleX(1.00) scaleY(1.00);
    }
    100% {
        opacity: 0;
        -ms-transform: scaleX(1.00) scaleY(1.00);
    }
}

// 'bounce' effect
@-webkit-keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, 100% {
    -webkit-transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
            transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }

  0% {
    opacity: 0;
    -webkit-transform: scale3d(.3, .3, .3);
            transform: scale3d(.3, .3, .3);
  }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
            transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    -webkit-transform: scale3d(.9, .9, .9);
            transform: scale3d(.9, .9, .9);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
            transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    -webkit-transform: scale3d(.97, .97, .97);
            transform: scale3d(.97, .97, .97);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
            transform: scale3d(1, 1, 1);
  }
}

@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, 100% {
    -webkit-transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
            transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(.3, .3, .3);
            transform: scale3d(.3, .3, .3);
  }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
            transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    -webkit-transform: scale3d(.9, .9, .9);
            transform: scale3d(.9, .9, .9);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
            transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    -webkit-transform: scale3d(.97, .97, .97);
            transform: scale3d(.97, .97, .97);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
            transform: scale3d(1, 1, 1);
  }
}
