<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="pos_payment_method_view_form" model="ir.ui.view">
        <field name="name">pos.payment.method.form</field>
        <field name="model">pos.payment.method</field>
        <field name="arch" type="xml">
            <form string="Payment Methods">
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <field name="active" invisible="1"/>
                    <field name="type" invisible="1" />
                    <div class="oe_title">
                        <label for="name"/>
                        <h1><field name="name" placeholder="e.g. Cash" class="oe_inline"/></h1>
                    </div>
                    <group name="Payment methods">
                        <field name="hide_use_payment_terminal" invisible="1"/>
                        <group>
                            <field name="split_transactions"/>
                            <field name="journal_id" attrs="{'required': [('split_transactions', '=', False)]}" placeholder="Leave empty to use the receivable account of customer" />
                            <field name="outstanding_account_id" groups="account.group_account_readonly" attrs="{'invisible': [('type', '!=', 'bank')]}" placeholder="Leave empty to use the default account from the company setting" />
                            <field name="receivable_account_id" groups="account.group_account_readonly" attrs="{'invisible': [('split_transactions', '=', True)]}" placeholder="Leave empty to use the default account from the company setting" />
                            <field name="company_id" readonly="1" groups="base.group_multi_company" />
                        </group>
                        <group attrs="{'invisible': ['|', ('hide_use_payment_terminal', '=', False), ('type', 'in', ['cash', 'pay_later'])]}">
                            <div>
                                <h2>Tips:</h2>
                                <p>
                                    Go to <a href="#" name="%(action_pos_configuration)d" type="action" class="btn-link o_form_uri" role="button">Configurations > Settings</a>
                                    <strong> > Payment Terminals</strong>
                                    in order to install a Payment Terminal and make a fully integrated payment method.
                                </p>
                            </div>
                        </group>
                        <group attrs="{'invisible': [('hide_use_payment_terminal', '=', True)]}">
                            <field name="use_payment_terminal" />
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="pos_payment_method_view_tree" model="ir.ui.view">
        <field name="name">pos.payment.method.tree</field>
        <field name="model">pos.payment.method</field>
        <field name="arch" type="xml">
            <tree string="Payment Methods" create="1" delete="1">
                <field name="type" invisible="1"/>
                <field name="name" />
                <field name="split_transactions" optional="hide" />
                <field name="journal_id" attrs="{'required': [('split_transactions', '=', False)]}" />
                <field name="outstanding_account_id" groups="account.group_account_readonly" optional="hide" attrs="{'invisible': [('type', '!=', 'bank')]}" />
                <field name="receivable_account_id" groups="account.group_account_readonly" optional="hide" attrs="{'invisible': [('split_transactions', '=', True)]}" />
                <field name="company_id" groups="base.group_multi_company" />
            </tree>
        </field>
    </record>

    <record id="pos_payment_method_view_search" model="ir.ui.view">
        <field name="name">pos.payment.search.view</field>
        <field name="model">pos.payment.method</field>
        <field name="arch" type="xml">
            <search string="Payment Methods">
                <field name="name"/>
                <field name="receivable_account_id" groups="account.group_account_readonly" />
                <group expand="1" string="Group By">
                    <filter name="group_by_receivable_account" string="Account" domain="[]"  context="{'group_by':'receivable_account_id'}"/>
                </group>
                <filter string="Archived" name="active" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="action_pos_payment_method_form" model="ir.actions.act_window">
        <field name="name">Payment Methods</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">pos.payment.method</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="view_id" eval="False"/>
        <field name="domain">[]</field>
        <field name="context">{'search_default_group_by_account': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new payment method
            </p>
            <p>
                Installing chart of accounts from the General Settings of
                Invocing/Accounting app will create Bank and Cash payment
                methods automatically.
            </p>
        </field>
    </record>

    <menuitem id="menu_pos_payment_method" parent="menu_point_config_product" action="action_pos_payment_method_form" sequence="3" groups="group_pos_manager,group_pos_user"/>

    <record id="action_payment_methods_tree" model="ir.actions.act_window">
        <field name="context">{}</field>
        <field name="name">Payments Methods</field>
        <field name="res_model">pos.payment.method</field>
        <field name="view_id" ref="pos_payment_method_view_tree"/>
        <field name="view_mode">tree,form,kanban</field>
    </record>
</odoo>
