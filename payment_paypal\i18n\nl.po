# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_paypal
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-31 17:26+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"<br/><br/>\n"
"                Thanks,<br/>\n"
"                <b>The Odoo Team</b>"
msgstr ""
"<br/><br/>\n"
"                Bedankt,<br/>\n"
"                <b>Het Odoo Team</b>"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_acquirer.py:0
#, python-format
msgid "Add your PayPal account to Odoo"
msgstr "Voeg je PayPal-account toe aan Odoo"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_email_account
msgid "Email"
msgstr "E-mail"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"Hello,\n"
"                <br/><br/>\n"
"                You have received a payment through PayPal.<br/>\n"
"                Kindly follow the instructions given by PayPal to create your account.<br/>\n"
"                Then, help us complete your Paypal credentials in Odoo.<br/><br/>"
msgstr ""
"Hallo,\n"
"<br/><br/>\n"
"Je hebt een betaling ontvangen via PayPal. <br/>\n"
"Volg de instructies van PayPal op om je account aan te maken. <br/>\n"
"Help ons vervolgens met het invullen van je Paypal-gegevens in Odoo.<br/><br/>"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_acquirer_form
msgid "How to configure your paypal account?"
msgstr "Hoe je Paypal account te configureren?"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_seller_account
msgid "Merchant Account ID"
msgstr "Handelaarsaccount ID"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Missing value for txn_id (%(txn_id)s) or txn_type (%(txn_type)s)."
msgstr ""
"Ontbrekende waarde voor txn_id (%(txn_id)s) of txn_type (%(txn_type)s)."

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Geen transactie gevonden die overeenkomt met referentie %s."

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid "Notification data were not acknowledged."
msgstr "Meldingsgegevens werden niet bevestigd."

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDT identiteitstoken"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_type
msgid "PayPal Transaction Type"
msgstr "PayPal-transactietype"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Betaalprovider"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_account_payment_method
msgid "Payment Methods"
msgstr "Betaalwijzes"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransactie"

#. module: payment_paypal
#: model:account.payment.method,name:payment_paypal.payment_method_paypal
#: model:ir.model.fields.selection,name:payment_paypal.selection__payment_acquirer__provider__paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Paypal Instant Payment Notification"
msgstr "Directe betaling notificatie Paypal"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__provider
msgid "Provider"
msgstr "Provider"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "Gegevens ontvangen met ongeldige betalingsstatus: %s"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"Received unrecognized authentication check response code: received %s, "
"expected VERIFIED or INVALID."
msgstr ""
"Niet-herkende responscode voor authenticatiecontrole ontvangen: ontvangen "
"%s, verwacht VERIFIED of INVALID."

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid "Set Paypal credentials"
msgstr "Stel Paypal inloggegevens in"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"De betalingsdienstaanbieder die bij deze profider moet worden gebruikt"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_email_account
msgid ""
"The public business email solely used to identify the account with PayPal"
msgstr ""
"Het openbare zakelijke e-mailadres dat uitsluitend wordt gebruikt om het "
"account bij PayPal te identificeren"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"The status of transaction with reference %(ref)s was not synchronized "
"because the 'Payment data transfer' option is not enabled on the PayPal "
"dashboard."
msgstr ""
"De status van de transactie met referentie %(ref)s werd niet "
"gesynchroniseerd omdat de optie 'Betalingsgegevens overdracht' niet "
"ingeschakeld is op het PayPal dashboard."

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"The status of transaction with reference %(ref)s was not synchronized "
"because the PDT Identify Token is not configured on the acquirer "
"%(acq_link)s."
msgstr ""
"De status van de transactie met referentie %(ref)s werd niet "
"gesynchroniseerd omdat de PDT identiteitstoken niet geconfigureerd is op de "
"provider %(acq_link)s."

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_transaction__paypal_type
msgid "This has no use in Odoo except for debugging."
msgstr "Dit heeft geen nut in Odoo, behalve voor debuggen."

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Use IPN"
msgstr "Gebruik IPN"
