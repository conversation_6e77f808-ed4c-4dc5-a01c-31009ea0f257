<?xml version="1.0"?>
<odoo>

        <!-- ******  Basic Restaurant Setup ***** -->

        <!-- Kitchen Printer -->

        <record id="kitchen_printer" model="restaurant.printer">
            <field name="name">Kitchen Printer</field>
            <field name="proxy_ip">localhost</field>
            <field name="product_categories_ids" eval="[(6, 0, [ref('point_of_sale.pos_category_miscellaneous')])]" />
        </record>

        <record id="drinks" model="pos.category">
              <field name="name">Drinks</field>
        </record>

        <record id="product_category_pos_food" model="product.category">
            <field name="parent_id" ref="point_of_sale.product_category_pos"/>
            <field name="name">Food</field>
        </record>

        <record id="food" model="pos.category">
              <field name="name">Food</field>
        </record>

        <!-- Food -->
        <record id="pos_food_margherita" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.0</field>
            <field name="name">Margherita</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pizza.png"/>
        </record>
        <record id="pos_food_funghi" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.0</field>
            <field name="name">Funghi</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pizza.png"/>
        </record>
        <record id="pos_food_vege" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.0</field>
            <field name="name">Vegetarian</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pizza.png"/>
        </record>
        <record id="pos_food_bolo" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">4.5</field>
            <field name="name">Pasta Bolognese</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pasta.jpg"/>
        </record>
        <record id="pos_food_4formaggi" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">5.5</field>
            <field name="name">Pasta 4 formaggi </field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pasta-4f.jpg"/>
        </record>
        <record id="pos_food_bacon" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.5</field>
            <field name="name">Bacon Burger</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-burger.jpg"/>
        </record>
        <record id="pos_food_cheeseburger" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.0</field>
            <field name="name">Cheese Burger</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-cheeseburger.jpg"/>
        </record>
        <record id="pos_food_chicken" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.0</field>
            <field name="name">Chicken Curry Sandwich</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-sandwich.jpg"/>
        </record>
        <record id="pos_food_tuna" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.0</field>
            <field name="name">Spicy Tuna Sandwich</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-tuna.jpg"/>
        </record>
        <record id="pos_food_mozza" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.9</field>
            <field name="name">Mozzarella Sandwich</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-mozza.jpg"/>
        </record>
        <record id="pos_food_club" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.4</field>
            <field name="name">Club Sandwich</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-club.jpg"/>
        </record>
        <record id="pos_food_maki" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">12.0</field>
            <field name="name">Lunch Maki 18pc</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-maki.jpg"/>
        </record>
        <record id="pos_food_salmon" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">13.80</field>
            <field name="name">Lunch Salmon 20pc</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-salmon.jpg"/>
        </record>
        <record id="pos_food_temaki" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">14.0</field>
            <field name="name">Lunch Temaki mix 3pc</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-temaki.jpg"/>
        </record>
        <record id="pos_food_chirashi" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">9.25</field>
            <field name="name">Salmon and Avocado</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="food"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-salmon-avocado.jpg"/>
        </record>

        <!-- Drinks -->
        <record id="coke" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.20</field>
            <field name="name">Coca-Cola</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="drinks"/>
            <field name="categ_id" ref="point_of_sale.product_category_pos"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-coke.jpg"/>
        </record>

        <record id="water" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.20</field>
            <field name="name">Water</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="drinks"/>
            <field name="categ_id" ref="point_of_sale.product_category_pos"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-water.jpg"/>
        </record>

        <record id="minute_maid" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.20</field>
            <field name="name">Minute Maid</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_id" ref="drinks"/>
            <field name="categ_id" ref="point_of_sale.product_category_pos"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-minute_maid.jpg"/>
        </record>

        <!-- Pos Config -->
        <record model="pos.config" id="pos_config_restaurant">
            <field name="name">Bar</field>
            <field name="barcode_nomenclature_id" ref="barcodes.default_barcode_nomenclature"/>
            <field name="module_pos_restaurant">True</field>
            <field name="is_table_management">True</field>
            <field name="iface_splitbill">True</field>
            <field name="iface_printbill">True</field>
            <field name="iface_orderline_notes">True</field>
            <field name="printer_ids" eval="[(6, 0, [ref('pos_restaurant.kitchen_printer')])]" />
            <field name="iface_start_categ_id" ref="drinks"/>
            <field name="start_category">True</field>
        </record>

        <!-- Floors: Main Floor -->

        <record id="floor_main" model="restaurant.floor">
            <field name="name">Main Floor</field>
            <field name="background_color">rgb(136,137,242)</field>
            <field name="pos_config_id" ref="pos_restaurant.pos_config_restaurant"/>
        </record>

        <record id="table_01" model="restaurant.table">
            <field name="name">T1</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">50</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_02" model="restaurant.table">
            <field name="name">T2</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">212</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_03" model="restaurant.table">
            <field name="name">T3</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">374</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_04" model="restaurant.table">
            <field name="name">T4</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">536</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_05" model="restaurant.table">
            <field name="name">T5</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">698</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_06" model="restaurant.table">
            <field name="name">T6</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">860</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_07" model="restaurant.table">
            <field name="name">T7</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(235,109,109)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">50</field>
            <field name="position_v">280</field>
        </record>

        <record id="table_08" model="restaurant.table">
            <field name="name">T8</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(235,109,109)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">212</field>
            <field name="position_v">280</field>
        </record>

        <record id="table_09" model="restaurant.table">
            <field name="name">T9</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(235,109,109)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">698</field>
            <field name="position_v">280</field>
        </record>

        <record id="table_10" model="restaurant.table">
            <field name="name">T10</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(235,109,109)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">860</field>
            <field name="position_v">280</field>
        </record>

        <record id="table_11" model="restaurant.table">
            <field name="name">T11</field>
            <field name="floor_id" ref="pos_restaurant.floor_main"/>
            <field name="seats">4</field>
            <field name="color">rgb(78,210,190)</field>
            <field name="shape">round</field>
            <field name="width">210</field>
            <field name="height">210</field>
            <field name="position_h">400</field>
            <field name="position_v">230</field>
        </record>

        <!-- Restaurant Floor: Patio -->

        <record id="floor_patio" model="restaurant.floor">
            <field name="name">Patio</field>
            <field name="background_color">rgb(130, 233, 171)</field>
            <field name="pos_config_id" ref="pos_restaurant.pos_config_restaurant"/>
        </record>

        <!-- Patio: Left table row -->

        <record id="table_21" model="restaurant.table">
            <field name="name">T1</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">75</field>
            <field name="position_h">100</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_22" model="restaurant.table">
            <field name="name">T2</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">75</field>
            <field name="position_h">100</field>
            <field name="position_v">166</field>
        </record>

        <record id="table_23" model="restaurant.table">
            <field name="name">T3</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">75</field>
            <field name="position_h">100</field>
            <field name="position_v">283</field>
        </record>

        <record id="table_24" model="restaurant.table">
            <field name="name">T4</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">75</field>
            <field name="position_h">100</field>
            <field name="position_v">400</field>
        </record>

        <!-- Patio: Right table row -->

        <record id="table_25" model="restaurant.table">
            <field name="name">T5</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">75</field>
            <field name="position_h">800</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_26" model="restaurant.table">
            <field name="name">T6</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">75</field>
            <field name="position_h">800</field>
            <field name="position_v">166</field>
        </record>

        <record id="table_27" model="restaurant.table">
            <field name="name">T7</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">75</field>
            <field name="position_h">800</field>
            <field name="position_v">283</field>
        </record>

        <record id="table_28" model="restaurant.table">
            <field name="name">T8</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">75</field>
            <field name="position_h">800</field>
            <field name="position_v">400</field>
        </record>

        <!-- Patio: Center table block -->

        <record id="table_29" model="restaurant.table">
            <field name="name">T9</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">330</field>
            <field name="position_v">100</field>
        </record>

        <record id="table_29" model="restaurant.table">
            <field name="name">T9</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">330</field>
            <field name="position_v">100</field>
        </record>

        <record id="table_30" model="restaurant.table">
            <field name="name">T10</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">560</field>
            <field name="position_v">100</field>
        </record>

        <record id="table_31" model="restaurant.table">
            <field name="name">T11</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">330</field>
            <field name="position_v">315</field>
        </record>

        <record id="table_32" model="restaurant.table">
            <field name="name">T12</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio"/>
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">560</field>
            <field name="position_v">315</field>
        </record>

        <function model="pos.config" name="add_cash_payment_method" />
</odoo>
