# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "ABA Routing Number"
msgstr "ABA Routing-Nummer"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_client_key
msgid "API Client Key"
msgstr "AP-Client-Schlüssel"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_login
msgid "API Login ID"
msgstr "API Login ID"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_signature_key
msgid "API Signature Key"
msgstr "API Signature Key"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_transaction_key
msgid "API Transaction Key"
msgstr "API Transaction Key"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Account Number"
msgstr "Kontonummer"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Allow Payments From"
msgstr "Zahlungen erlauben von"

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "An error occurred when displayed this payment form."
msgstr "Bei der Anzeige dieses Zahlungsformulars ist ein Fehler aufgetreten."

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_currency_id
msgid "Authorize Currency"
msgstr "Währung autorisieren"

#. module: payment_authorize
#: model:account.payment.method,name:payment_authorize.payment_method_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__provider__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "Authorize.Net Payment Type"
msgstr "Authorize.Net Zahlungstyp"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.Net Profile ID"
msgstr "Authorize.Net Profil ID"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Bank (powered by Authorize)"
msgstr "Bank (powered by Authorize)"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__bank_account
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__bank_account
msgid "Bank Account (USA Only)"
msgstr "Bankkonto (nur USA)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Account Type"
msgstr "Bankkonto Typ"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Name"
msgstr "Bankname"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Business Checking"
msgstr "Geschäftliches Girokonto"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Code"
msgstr "Kartencode"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Number"
msgstr "Kartennummer"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Could not fetch merchant details:\n"
"%s"
msgstr ""
"Es konnten keine Händlerdetails abgerufen werden:\n"
"%s"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__credit_card
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__credit_card
msgid "Credit Card"
msgstr "Kreditkarte"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Credit Card (powered by Authorize)"
msgstr "Kreditkarte (powered by Authorize)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Currency"
msgstr "Währung"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Determines with what payment method the customer can pay."
msgstr "Legt fest, welche Zahlungsmethoden der Kunde nutzen kann."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Expiration"
msgstr "Ablauf"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Failed to authenticate.\n"
"%s"
msgstr ""
"Authentifizierung fehlgeschlagen.\n"
"%s"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Generate Client Key"
msgstr "Client-Schlüssel generieren"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "How to get paid with Authorize.Net"
msgstr "Wie Sie mit Authorize.Net bezahlt werden"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Name On Account"
msgstr "Name auf Konto"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Keine Transaktion gefunden, die der Referenz %s entspricht."

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Zahlungsanbieter"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_account_payment_method
msgid "Payment Methods"
msgstr "Zahlungsmethoden"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "Zahlungstoken"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Zahlungstransaktion"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Checking"
msgstr "Persönliches Girokonto"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Savings"
msgstr "Persönliche Ersparnisse"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__provider
msgid "Provider"
msgstr "Anbieter"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "Received data with status code \"%(status)s\" and error code \"%(error)s\""
msgstr "Daten empfangen mit Statuscode \"%(status)s\" und Fehlercode \"%(error)s\""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr "Es wurden manipulierte Zahlungsanforderungsdaten empfangen."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_token.py:0
#, python-format
msgid "Saved payment methods cannot be restored once they have been deleted."
msgstr ""
"Gespeicherte Zahlungsmethoden können nicht wieder hergestellt werden, wenn "
"sie einmal gelöscht wurden."

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Server fehler"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Set Account Currency"
msgstr "Kontowährung bestimmen"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_login
msgid "The ID solely used to identify the account with Authorize.Net"
msgstr ""
"Die ID, die ausschließlich zur Identifizierung des Kontos bei Authorize.Net "
"verwendet wird"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "Der mit diesem Zahlungsanbieter zu verwendende Dienstleister"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_client_key
msgid ""
"The public client key. To generate directly from Odoo or from Authorize.Net "
"backend."
msgstr ""
"Der öffentliche Client-Schlüssel. Direkt aus Odoo oder aus dem Authorize.Net"
" Backend zu generieren."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "Die Transaktion ist nicht mit einem Token verknüpft."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "The type of payment method this token is linked to."
msgstr "Die Art der Zahlungsmethode, mit der dieses Token verknüpft ist."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"The unique reference for the partner/token combination in the Authorize.net "
"backend."
msgstr ""
"Die eindeutige Referenz für die Partner/Token-Kombination im Authorize.net "
"Backend."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"There are active tokens linked to this acquirer. To change the payment "
"method type, please disable the acquirer and duplicate it. Then, change the "
"payment method type on the duplicated acquirer."
msgstr ""
"Es sind aktive Token mit diesem Anbieter verknüpft. Um die Art der "
"Zahlungsmethode zu ändern, deaktivieren Sie bitte den Anbieter und "
"duplizieren Sie ihn. Ändern Sie dann die Art der Zahlungsmethode für den "
"duplizierten Anbieter."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "This action cannot be performed while the acquirer is disabled."
msgstr ""
"Diese Aktion kann nicht durchgeführt werden, wenn der Anbieter deaktiviert "
"ist."

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr "Ihre Zahlung kann nicht verarbeitet werden."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "YY"
msgstr "YY"
