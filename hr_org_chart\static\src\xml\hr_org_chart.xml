<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

<t t-name="hr_org_chart_employee">
    <div t-attf-class="o_org_chart_entry o_org_chart_entry_#{employee_type} media">
        <t t-set="is_self" t-value="employee.id == view_employee_id"/>

        <div class="o_media_left">
            <!-- NOTE: Since by the default on not squared images odoo add white borders,
                use bg-images to get a clean and centred images -->
            <a t-if="! is_self"
                class="o_media_object rounded-circle o_employee_redirect"
                t-att-style="'background-image:url(\'/web/image/hr.employee.public/' + employee.id + '/avatar_1024/\')'"
                t-att-alt="employee.name"
                t-att-data-employee-id="employee.id"
                t-att-href="employee.link"/>
            <div t-if="is_self"
                class="o_media_object rounded-circle"
                t-att-style="'background-image:url(\'/web/image/hr.employee.public/' + employee.id + '/avatar_1024/\')'"/>
        </div>

        <div class="media-body">
            <span
                    t-if="employee.indirect_sub_count &gt; 0"
                    class="badge badge-pill"
                    tabindex="0"
                    data-trigger="focus"
                    t-att-data-emp-name="employee.name"
                    t-att-data-emp-id="employee.id"
                    t-att-data-emp-dir-subs="employee.direct_sub_count"
                    t-att-data-emp-ind-subs="employee.indirect_sub_count"
                    data-toggle="popover">
                <t t-esc="employee.indirect_sub_count"/>
            </span>

            <t t-if="!is_self">
                <a t-att-href="employee.link" class="o_employee_redirect" t-att-data-employee-id="employee.id">
                    <h5 class="o_media_heading"><b><t t-esc="employee.name"/></b></h5>
                    <strong><t t-esc="employee.job_title"/></strong>
                </a>
            </t>
            <t t-if="is_self">
                <h5 class="o_media_heading"><b><t t-esc="employee.name"/></b></h5>
                <strong><t t-esc="employee.job_title"/></strong>
            </t>
        </div>
    </div>
</t>

<t t-name="hr_org_chart">
    <!-- NOTE: Desidered behaviour:
            The maximun number of people is always 7 (including 'self'). Managers have priority over suburdinates
            Eg. 1 Manager + 1 self = show just 5 subordinates (if availables)
            Eg. 0 Manager + 1 self = show 6 subordinates (if available)

        -->
    <t t-set="emp_count" t-value="0"/>

    <div t-if='managers.length &gt; 0' class="o_org_chart_group_up">
        <t t-if='managers_more'>
            <div class="o_org_chart_entry o_org_chart_more media">
                <div class="o_media_left">
                    <a class="text-center o_employee_more_managers"
                            t-att-data-employee-id="managers[0].id">
                        <i t-attf-class="fa fa-angle-double-up" role="img" aria-label="More managers" title="More managers"/>
                    </a>
                </div>
            </div>
        </t>

        <t t-foreach="managers" t-as="employee">
            <t t-set="emp_count" t-value="emp_count + 1"/>
            <t t-call="hr_org_chart_employee">
                <t t-set="employee_type" t-value="'manager'"/>
            </t>
        </t>
    </div>

    <t t-if="children.length || managers.length" t-call="hr_org_chart_employee">
        <t t-set="employee_type" t-value="'self'"/>
        <t t-set="employee" t-value="self"/>
    </t>

    <t t-if="!children.length &amp;&amp; !managers.length">
        <div class="alert alert-info" role="alert">
            <p><b>No hierarchy position.</b></p>
            <p>This employee has no manager or subordinate.</p>
            <p>In order to get an organigram, set a manager and save the record.</p>
        </div>
    </t>

    <div t-if="children.length" class="o_org_chart_group_down">
        <t t-foreach="children" t-as="employee">
            <t t-set="emp_count" t-value="emp_count + 1"/>
            <t t-if="emp_count &lt; 20">
                <t t-call="hr_org_chart_employee">
                    <t t-set="employee_type" t-value="'sub'"/>
                </t>
            </t>
        </t>

        <t t-if="(children.length + managers.length) &gt; 19">
            <div class="o_org_chart_entry o_org_chart_more media">
                <div class="o_media_left">
                    <a href="#"
                        t-att-data-employee-id="self.id"
                        t-att-data-employee-name="self.name"
                        class="o_org_chart_show_more text-center o_employee_sub_redirect">See All</a>
                </div>
            </div>
        </t>
    </div>
</t>

<t t-name="hr_orgchart_emp_popover">
    <div class="popover o_org_chart_popup" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>
</t>

<t t-name="hr_orgchart_emp_popover_content">
    <table class="table table-sm">
        <thead>
            <td class="text-right"><t t-esc="employee.direct_sub_count"/></td>
            <td>
                <a href="#" class="o_employee_sub_redirect" data-type='direct'
                        t-att-data-employee-name="employee.name" t-att-data-employee-id="employee.id">
                    <b>Direct subordinates</b></a>
            </td>
        </thead>
        <tbody>
            <tr>
                <td class="text-right">
                    <t t-esc="employee.indirect_sub_count - employee.direct_sub_count"/>
                </td>
                <td>
                    <a href="#" class="o_employee_sub_redirect" data-type='indirect'
                            t-att-data-employee-name="employee.name" t-att-data-employee-id="employee.id">
                        Indirect subordinates</a>
                </td>
            </tr>
            <tr>
                <td class="text-right"><t t-esc="employee.indirect_sub_count"/></td>
                <td>
                    <a href="#" class="o_employee_sub_redirect" data-type='total'
                            t-att-data-employee-name="employee.name" t-att-data-employee-id="employee.id">
                        Total</a>
                </td>
            </tr>
        </tbody>
    </table>
</t>

<t t-name="hr_orgchart_emp_popover_title">
    <div>
        <span t-att-style='"background-image:url(\"/web/image/hr.employee.public/" + employee.id + "/avatar_1024/\")"'/>
        <a href="#" class="float-right o_employee_redirect" t-att-data-employee-id="employee.id"><i class="fa fa-external-link" role="img" aria-label='Redirect' title="Redirect"></i></a>
        <b><t t-esc="employee.name"/></b>
    </div>
</t>

</templates>
