# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_eg_edi_eta
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-17 10:23+0000\n"
"PO-Revision-Date: 2022-05-17 10:23+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">ETA API Integration</span>\n"
"                            <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__access_token
msgid "Access Token"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6920
msgid "Accounting, auditing, bookkeeping and tax advice activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0162
msgid "Activities in support of animal production"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8550
msgid "Activities in support of education"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8291
msgid "Activities of collection agencies and lending offices"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6420
msgid "Activities of holding companies"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6622
msgid "Activities of insurance and brokerage agents"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9900
msgid "Activities of non-regional organizations and bodies"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9499
msgid "Activities of other membership organizations not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9492
msgid "Activities of political organizations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6391
msgid "Activities of press agencies"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9810
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9820
msgid ""
"Activities of producing unearthed products and services for home appliances "
"for personal use"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9412
msgid "Activities of professional membership organizations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7810
msgid "Activities of recruitment and appointment agencies"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9491
msgid "Activities of religious organizations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7820
msgid "Activities of temporary employment agencies"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7912
msgid "Activities of tour guides"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8620
msgid "Activities related to medicine and dentistry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_C62
msgid "Activity unit ( AU )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,help:l10n_eg_edi_eta.field_account_journal__l10n_eg_branch_id
msgid ""
"Address of the subdivision of the company.  You can just put the company "
"partner if this is used for the main branch."
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7310
msgid "Advertising"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2821
msgid "Agricultural and forestry equipment industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3030
msgid "Air and spacecraft industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5120
msgid "Air freight transport"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5110
msgid "Air transport of passengers"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "An error occured in created the ETA invoice, please retry signing"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid "An unexpected error has occurred"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7110
msgid ""
"Architectural and engineering activities and related technical consulting"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2520
msgid "Arms and ammunition industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0230
msgid "Assembling non-wood forest products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6619
msgid "Auxiliary activities for financial services"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_BG
msgid "Bag ( Bag )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_BBL
msgid "Barrel (oil 42 gal.)"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2011
msgid "Basic chemicals"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9200
msgid "Betting activities and gambling"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9103
msgid "Botanical and zoological gardens and natural wildlife activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_BO
msgid "Bottle ( Bt. )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_BOX
msgid "Box"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_journal__l10n_eg_branch_id
msgid "Branch"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0145
msgid "Breeding of Pig"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0143
msgid "Breeding of camels"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0141
msgid "Breeding of cattle and buffalo"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0142
msgid "Breeding of horses and mare"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0149
msgid "Breeding other animals"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0144
msgid "Breeding sheep and goats"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6010
msgid "Broadcasting over radio stations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_partner__l10n_eg_building_no
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_users__l10n_eg_building_no
msgid "Building No."
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.eg_partner_address_form
msgid "Building Number..."
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8129
msgid "Building cleaning activities and other industrial facilities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4100
msgid "Building constructions"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3011
msgid "Building ship hulls and rafts"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5520
msgid "Campgrounds, parking lots, and locomotives"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_CA
msgid "Canister ( Can )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5224
msgid "Cargo handling"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1393
msgid "Carpet and blanket industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_CT
msgid "Carton ( Car )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_CS
msgid "Case ( Case )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2394
msgid "Cement, lime and plaster industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_CTL
msgid "Centiliter ( Cl )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_CMT
msgid "Centimeter ( cm )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6411
msgid "Central banks"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.res_config_settings_view_form
msgid ""
"Check to start sending invoices to your e-invoicing production environment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0891
msgid "Chemical minerals and fertilizer extraction"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1820
msgid "Clone recorded media"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_activity_type__code
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_uom_code__code
msgid "Code"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1910
msgid "Coke oven products industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3812
msgid "Collection of hazardous waste"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3811
msgid "Collection of non-hazardous waste"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9512
msgid "Communication equipment repair"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2630
msgid "Communications equipment industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__company_id
msgid "Company"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4330
msgid "Completion and finishing of buildings"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8430
msgid "Compulsory social insurance activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5820
msgid "Computer Software Publishing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6202
msgid ""
"Computer consultancy experience and facilities management activities related"
" to computer fields"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6201
msgid "Computer program preparation activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9511
msgid "Computer repair and accessories"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4290
msgid "Construction for other civil engineering projects"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4220
msgid "Construction for projects of public benefit"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.eg_partner_address_form
msgid "Country"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_activity_type__create_uid
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__create_uid
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_uom_code__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_activity_type__create_date
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__create_date
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_uom_code__create_date
msgid "Created on"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9000
msgid "Creative and recreational art activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6430
msgid ""
"Credit activities, provision of credits, and similar financial entities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0130
msgid "Crop breeding"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_CMQ
msgid "Cubic centimeter ( cm3 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_FTQ
msgid "Cubic foot ( ft3 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_MTQ
msgid "Cubic meter ( m3 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_MMQ
msgid "Cubic millimeter ( mm3 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_YDQ
msgid "Cubic yard ( yd3 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0123
msgid "Cultivation of citrus fruits"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0124
msgid "Cultivation of fruit with Date kernel and from palm trees"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0111
msgid ""
"Cultivation of grains and crops (except for rice), legumes and oilseeds"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0119
msgid "Cultivation of other non-perennial crops"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0129
msgid "Cultivation of other perennial crops"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0112
msgid "Cultivation of rice"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0128
msgid ""
"Cultivation of spice crops, aromatics, medicine and pharmaceutical drugs"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0114
msgid "Cultivation of sugar cane"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0127
msgid "Cultivation of the crops from which drinks are extracted"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8542
msgid "Cultural education"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_res_currency_rate
msgid "Currency Rate"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2396
msgid "Cutting, forming and completing the stone processing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1050
msgid "Dairy products manufacturing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6311
msgid "Data processing, hosting and related activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_DAY
msgid "Days ( d )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_DMT
msgid "Decimeter ( dm )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8422
msgid "Defense activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_activity_type__display_name
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__display_name
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_uom_code__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Document Canceled"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_bank_statement_line__l10n_eg_uuid
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_move__l10n_eg_uuid
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_payment__l10n_eg_uuid
msgid "Document UUID"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.ui.menu,name:l10n_eg_edi_eta.account_eta_menu
msgid "ETA"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_journal__l10n_eg_activity_type_id
msgid "ETA Activity Code"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_journal__l10n_eg_branch_identifier
msgid "ETA Branch ID"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__certificate
msgid "ETA Certificate"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_company__l10n_eg_client_identifier
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_config_settings__l10n_eg_client_identifier
msgid "ETA Client ID"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_product_product__l10n_eg_eta_code
msgid "ETA Code"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.view_move_form_inherit
msgid "ETA E-Invoice"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.res_config_settings_view_form
msgid "ETA E-Invoicing Settings"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.product_normal_form_view_inherit_l10n_eg_eta_edi
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.product_template_only_form_view_inherit_l10n_eg_eta_edi
msgid ""
"ETA Field for GS1/EGS product codes. Please use the barcode field to store "
"GS1/EGS ETA code if possible"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_product_template__l10n_eg_eta_code
msgid "ETA Item code"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_company__l10n_eg_client_secret
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_config_settings__l10n_eg_client_secret
msgid "ETA Secret"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__pin
msgid "ETA USB Pin"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_uom_uom__l10n_eg_unit_code_id
msgid "ETA Unit Code"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_l10n_eg_edi_activity_type
msgid "ETA code for activity type"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_l10n_eg_edi_uom_code
msgid "ETA code for the unit of measures"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_move.py:0
#, python-format
msgid "ETA invoice has been received"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_move.py:0
#, python-format
msgid "ETA_INVOICE_DOC_%s"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0411
msgid "Earn a job"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.view_account_journal_form_inherit_l10n_eg_edi
msgid "Egyptian ETA settings"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.product_normal_form_view_inherit_l10n_eg_eta_edi
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.product_template_only_form_view_inherit_l10n_eg_eta_edi
msgid "Egyptian Electronic Invoicing"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_move.py:0
#, python-format
msgid "Egyptian Tax authority JSON invoice generated for %s."
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3510
msgid "Electric generators, transformers and power distributors"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4321
msgid "Electrical installations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2740
msgid "Electrical lighting devices industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2610
msgid "Electronic components and panels industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2640
msgid "Electronic devices industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3314
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9521
msgid "Electronic devices repair"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6312
msgid "Electronic portals"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5223
msgid "Emergency service activities related to air transport"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5222
msgid "Emergency service activities related to maritime transport"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.res_config_settings_view_form
msgid "Enter your API credentials to enable ETA E-Invoicing."
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0451
msgid "Errand stamp"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid "Error trying to connect to Odoo. Check your internet connection"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid "Error trying to connect to the middleware. Is the middleware running?"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5621
msgid "Event catering"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7220
msgid ""
"Experimental research and development in the field of social and human "
"sciences"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0620
msgid "Extract natural gas"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0610
msgid "Extract the crude oil"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_FAR
msgid "Farad ( F )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5914
msgid "Film screening activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5911
msgid "Film, video and television program production activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6630
msgid "Financial credit management activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6491
msgid "Financial leasing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6611
msgid "Financial markets management"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0311
msgid "Fishing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_FOT
msgid "Foot ( Foot )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8421
msgid "Foreign affairs"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0210
msgid "Forest care and forest-related activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0240
msgid "Forest support services"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2591
msgid ""
"Forming metals by hammering, pressing, casting, rolling, and treatment of "
"metal powders"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0472
msgid "Free Zones revenue"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0471
msgid "Free market revenue"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9603
msgid "Funeral and related activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1420
msgid "Fur accessories industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3100
msgid "Furniture Industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_GPT
msgid "Gallon per thousand"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8130
msgid "Gardening services and maintenance activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2814
msgid "Gears, carriers and driving devices industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8121
msgid "General cleaning of buildings"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8521
msgid "General secondary education"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.view_move_form_inherit
msgid "Get ETA Invoice PDF"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.view_l10n_eg_edi_thumb_drive_tree
msgid "Get certificate"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2310
msgid "Glass and its products industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_GRM
msgid "Gram ( g )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_A93
msgid "Gram/Cubic meter ( g/m3 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_A94
msgid "Gram/cubic centimeter ( g/cm3 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_GL
msgid "Gram/liter ( g/l )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_GM
msgid "Gram/square meter ( g/m2 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0116
msgid "Growing fiber crops"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0126
msgid "Growing oil fruits"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0122
msgid "Growing tropical and subtropical fruits"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0113
msgid "Growing vegetables, melons, roots and tubers"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9602
msgid "Hair styling and other cosmetics"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0510
msgid "Hard coal mining"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_HLT
msgid "Hectoliter ( hl )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8530
msgid "Higher Education"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2750
msgid "Home appliances industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9700
msgid "Home employment activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8610
msgid "Hospital activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_HUR
msgid "Hours ( hrs )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0170
msgid "Hunting, erection, and related service activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_HHP
msgid "Hydraulic Horse Power"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_activity_type__id
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__id
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_uom_code__id
msgid "ID"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_company__l10n_eg_production_env
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_config_settings__l10n_eg_production_env
msgid "In Production Environment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_INH
msgid "Inch ( “” )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0414
msgid "Income and salaries from the private sector"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0413
msgid "Income and salaries from the public business sector"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0415
msgid "Income and salary from non-subject entities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0463
msgid "Income earned from abroad"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0412
msgid "Income from government agencies salaries"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0441
msgid "Income of agricultural lands"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0444
msgid "Income of real estate activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2731
msgid "Industrial fiber cable industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2030
msgid "Industrial fiber industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3091
msgid "Industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2512
msgid "Industry of tanks and metal containers"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8220
msgid "Information center services"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4911
msgid "Inland passenger transportation"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5021
msgid "Inland passenger water transport"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5022
msgid "Inland water transport of goods"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0416
msgid "Inspection and sting"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3320
msgid "Installation of industrial equipment and devices"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_IVL
msgid "Interval"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8030
msgid "Investigation activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_company__l10n_eg_invoicing_threshold
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_res_config_settings__l10n_eg_invoicing_threshold
msgid "Invoicing Threshold"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2431
msgid "Iron and steel casting"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0710
msgid "Iron ore mining"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_JOB
msgid "JOB"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8211
msgid "Joint office support services activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5210
msgid "Keep and store"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_KGM
msgid "Kilogram ( KG )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_KSM
msgid "Kilogram/Square meter ( kg/m2 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_KMQ
msgid "Kilogram/cubic meter ( kg/m3 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_KHZ
msgid "Kilohertz ( kHz )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_KMT
msgid "Kilometer ( km )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_KMH
msgid "Kilometer/hour ( km/h )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_KVT
msgid "Kilovolt ( kV )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_KWT
msgid "Kilowatt ( KW )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_bank_statement_line__l10n_eg_eta_json_doc_id
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_move__l10n_eg_eta_json_doc_id
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_payment__l10n_eg_eta_json_doc_id
msgid "L10N Eg Eta Json Doc"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_bank_statement_line__l10n_eg_is_signed
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_move__l10n_eg_is_signed
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_payment__l10n_eg_is_signed
msgid "L10N Eg Is Signed"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4923
msgid "Land transportation of goods by bus"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_activity_type____last_update
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive____last_update
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_uom_code____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_activity_type__write_uid
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__write_uid
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_uom_code__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_activity_type__write_date
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__write_date
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_uom_code__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6910
msgid "Legal activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_LVL
msgid "Level"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9101
msgid "Library and archive activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5630
msgid "Light beverage service activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0520
msgid "Lignite mining"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2812
msgid "Liquid power devices industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_LTR
msgid "Liter ( l )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1512
msgid ""
"Luggage, handbags and similar industries, along with saddles and horse sets"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3312
msgid "Machinery repair"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5310
msgid "Mail activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4520
msgid "Maintenance and repair of motor vehicles"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3240
msgid "Make games and play"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_MAN
msgid "Man"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7020
msgid "Management consultancy activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1020
msgid "Manufacture and preservation of fish, crustaceans and mollusks"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2392
msgid "Manufacture of Shale products for Building"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2930
msgid "Manufacture of accessories and spare parts for motor vehicles"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1622
msgid ""
"Manufacture of carpentry accessories intended for buildings and "
"installations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1073
msgid "Manufacture of cocoa, chocolate and sugar confectionery"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2395
msgid "Manufacture of concrete products, cement and plaster"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1702
msgid ""
"Manufacture of corrugated paper and paperboard and boxes made of paper and "
"paperboard"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2593
msgid "Manufacture of cutting tools, hand tools and general metal tools"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2720
msgid "Manufacture of dry and stored batteries"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2822
msgid "Manufacture of equipment and machinery for forming metals"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2012
msgid "Manufacture of fertilizers and nitrogen compounds"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2825
msgid "Manufacture of food, beverage and tobacco industries equipment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2815
msgid "Manufacture of furnaces, furnaces and their incinerators"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2391
msgid "Manufacture of fusion products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1410
msgid "Manufacture of garment with the exception of fur"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2811
msgid ""
"Manufacture of generators and engines, with the exception of aircraft, "
"vehicles and motorcycles"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1061
msgid "Manufacture of grain mill products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3212
msgid "Manufacture of imitation jewelry and related items"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3211
msgid "Manufacture of jewelry and related items"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1391
msgid "Manufacture of knitted and crocheted fabrics"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2818
msgid "Manufacture of manual power steering equipment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3040
msgid "Manufacture of military military vehicles"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2920
msgid ""
"Manufacture of motor vehicle bodies and the manufacture of trailers and "
"semi-trailers"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2910
msgid "Manufacture of motor vehicles"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2710
msgid ""
"Manufacture of motors, generators, electrical transformers, devices and "
"control panels for electricity distribution"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2399
msgid "Manufacture of non-metallic minerals products not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2817
msgid ""
"Manufacture of office equipment and equipment (excluding electronic "
"computers and their accessories)"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3092
msgid "Manufacture of ordinary bicycles and infirm vehicles"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2393
msgid "Manufacture of other Porcelain and ceramic products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1709
msgid "Manufacture of other articles of paper and paperboard"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2029
msgid "Manufacture of other chemical products not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2599
msgid ""
"Manufacture of other fabricated metal products not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1079
msgid "Manufacture of other products not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2219
msgid "Manufacture of other rubber products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2829
msgid "Manufacture of other special-purpose equipment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2022
msgid ""
"Manufacture of paints, varnishes, and similar coatings, printing inks and "
"molds"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2100
msgid "Manufacture of pharmaceutical, chemical, and plant products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3012
msgid "Manufacture of pleasure boats and sport boats"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2420
msgid "Manufacture of precious and non-ferrous basic metals"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2813
msgid "Manufacture of pumps, compressors, tapes and other valves"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2826
msgid "Manufacture of ready-made clothes, accessories, and leather production"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1392
msgid "Manufacture of ready-made textile accessories, except garment wear"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1394
msgid "Manufacture of ropes, thick and double ropes and nets"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2211
msgid ""
"Manufacture of rubber tires and tubes, renewing and rebuilding the outer "
"surfaces of rubber tires"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2023
msgid ""
"Manufacture of soap, disinfectants, cleaning and polishing preparations, "
"perfumes and cosmetics"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1062
msgid "Manufacture of starch and starch products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3520
msgid ""
"Manufacture of sulfur gas and distribution of gaseous fuels by means of main"
" pipes"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1200
msgid "Manufacture of tobacco products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1040
msgid "Manufacture of vegetable and animal oils and fats"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1629
msgid ""
"Manufacture of wood, wood products and cork, except furniture, and "
"manufacture of articles produced from straw and sheets"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1030
msgid "Manufacturing and preserving fruits and vegetables"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1071
msgid "Manufacturing bakery products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2651
msgid "Manufacturing measuring, testing, navigation and control devices"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3250
msgid "Manufacturing of dental and medical equipment and tools"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1074
msgid "Manufacturing pasta, strips, couscous and similar starchy products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1104
msgid "Manufacturing soft drinks and producing mineral water"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5012
msgid "Marine and coastal cargo transportation"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0321
msgid "Marine farms"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7320
msgid "Market studies and public opinion polls"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3830
msgid "Material handling"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1075
msgid "Meals and ready-made food industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1010
msgid "Meat processing and preservation"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2823
msgid "Metal equipment industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2592
msgid "Metal processing and coating"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_M
msgid "Meter ( m )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_X03
msgid "Meter/Hour ( m/h )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_G42
msgid "Microsiemens per centimeter ( microS/cm )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_SMI
msgid "Mile ( mile )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_MGM
msgid "Milligram ( mg )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_H63
msgid "Milligram/Square centimeter ( mg/cm2 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_MMT
msgid "Millimeter ( mm )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2824
msgid "Mining and quarrying and building equipment industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0729
msgid "Mining other non-ferrous metals"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_MIN
msgid "Minute ( min )"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid ""
"Missing Dependency - If you are using Windows, make sure eps2003csp11.dll is"
" correctly installed. You can download it here: "
"https://www.egypttrust.com/en/downloads/other-drivers. If you are using "
"Linux or macOS, please install OpenSC"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid ""
"Missing library - Please make sure that PyKCS11 is correctly installed on "
"the local proxy server"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0150
msgid "Mixed education"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_MON
msgid "Months ( Months )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5913
msgid "Motion picture, video and television program distribution activities"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid ""
"Multiple drive detected - Only one secure thumb drive can be inserted at the"
" same time"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9102
msgid "Museum activities and restoration of historic sites and buildings"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3220
msgid "Musical instrument industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_activity_type__name
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_uom_code__name
msgid "Name"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_C45
msgid "Nanometer ( nm )"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid "No drive found - Make sure the thumb drive is correctly inserted"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2432
msgid "Non-ferrous metal casting"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6512
msgid "Non-life insurance"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4690
msgid "Non-specialized wholesale trade"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_IE
msgid "Number of Persons ( PRS )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8720
msgid ""
"Nursing care facilities for special needs clinics, mental illnesses and "
"physical abuse"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8710
msgid "Nursing facilities for sanatoriums"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2680
msgid "Optical and magnetic conveyor industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2670
msgid "Optical equipment and imaging equipment industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8413
msgid "Organize and contribute to effective business operations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8412
msgid ""
"Organizing activities to provide health care, education, educational "
"services and other social services, with the exception of social security"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8230
msgid "Organizing trade conferences and exhibitions"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6629
msgid "Other activities auxiliary to insurance and provision for pensions"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6399
msgid ""
"Other activities for information services that are not classified in other "
"locations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5229
msgid "Other activities in support of the transfer"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8690
msgid "Other activities related to human health"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6209
msgid ""
"Other activities related to information technology and computer services"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0464
msgid "Other categories / miscellaneous other income"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5629
msgid "Other catering services activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2732
msgid "Other electrical and electronic wires and cables"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2790
msgid "Other electrical appliances industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2819
msgid "Other equipment industry of various purposes"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6419
msgid "Other financial intermediaries"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6499
msgid ""
"Other financial services activities, with the exception of insurance and "
"credit provision activities for pensions not classified in other locations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6492
msgid "Other forms of loans granted"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3290
msgid "Other industries not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9329
msgid "Other leisure and entertainment activities not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0899
msgid "Other mining and quarrying activities are not elsewhere classified"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9609
msgid "Other personal services activities not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5819
msgid "Other publishing activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4719
msgid "Other retail types in non-specialized stores"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8890
msgid "Other social business activities that take place without residence"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8790
msgid "Other spa care facilities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4390
msgid "Other specialized construction activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7490
msgid ""
"Other specialized, scientific and artistic activities not classified "
"elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9319
msgid "Other sports activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4329
msgid "Other structural installations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8299
msgid ""
"Other support services activities that are not classified in other locations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6190
msgid "Other telecommunications activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1399
msgid "Other textile industry not elsewhere classified"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3099
msgid "Other transportation equipment industry not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8549
msgid "Other types of education not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4922
msgid "Other types of passenger transport by land"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5590
msgid "Other types of placement"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7990
msgid "Other types of reservations and related activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4799
msgid ""
"Other types of retail sales that do not take place in stores, kiosks or "
"markets"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_ONZ
msgid "Ounce ( oz )"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "PDF Document is not available"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_PK
msgid "Pack ( PAK )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8292
msgid "Packaging activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_PF
msgid "Pallet ( PAL )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1701
msgid "Paper and carvatard pulp industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5320
msgid "Parcel delivery activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_PAL
msgid "Pascal ( Pa )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0892
msgid "Peat extraction"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2021
msgid "Pesticide industry and other agricultural chemical products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8219
msgid ""
"Photocopying, document processing and other specialized office support "
"services activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7420
msgid "Photographic activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4930
msgid "Pipeline transportation"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0125
msgid "Plant fruit trees and shrubs and other nuts"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2220
msgid "Plastics industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2013
msgid "Plastics industry in its primary forms and synthetic rubber"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Please add all the required fields in the branch details"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Please add all the required fields in the customer details"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Please configure the API domain from the system parameters"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Please configure the token domain from the system parameters"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/eta_thumb_drive.py:0
#, python-format
msgid "Please define the host of sign tool."
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/res_currency_rate.py:0
#, python-format
msgid ""
"Please make sure that the EGP per unit is within 5 decimal accuracy.\n"
"Higher decimal accuracy might lead to inconsistency with the ETA invoicing portal!"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Please make sure the EGS/GS1 Barcode is set correctly on all products"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Please make sure the invoice is signed"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Please make sure the invoice lines UoM codes are all set up correctly"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid ""
"Please make sure the invoice lines taxes all have the correct ETA tax code"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_move.py:0
#, python-format
msgid "Please only sign invoices from one company at a time"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Please set the all the ETA information on the invoice's journal"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_move.py:0
#, python-format
msgid "Please setup a personal drive for company %s"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_move.py:0
#, python-format
msgid "Please setup the certificate on the thumb drive menu"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4322
msgid "Plumbing, heating and air-conditioning installations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0163
msgid "Post-harvest activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0146
msgid "Poultry farming"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1080
msgid "Prepared animal food industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0164
msgid "Preparing grains for reproduction"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4312
msgid "Preparing sites"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8510
msgid "Primary and pre-primary education"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1812
msgid "Printing service activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8010
msgid "Private security activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1311
msgid "Processing and spinning of textile fibers"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_product_product
msgid "Product"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_product_template
msgid "Product Template"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5920
msgid "Production and publishing of sound and music recordings"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6530
msgid "Providing credits for pensions"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7830
msgid "Providing other human resources"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9311
msgid "Providing sports facilities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8411
msgid "Public administration activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5812
msgid "Publish the directory and address lists"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5811
msgid "Publishing books"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5813
msgid "Publishing newspapers, magazines and periodicals"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0810
msgid "Quarrying to extract stones, sand and shale"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2660
msgid "Radiation, medical and therapeutic electronic devices industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3020
msgid "Railroad locomotives and rolling stock industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6820
msgid "Real estate activities on the basis of a contract or a fee"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6810
msgid "Real estate activities with own or leased property"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9321
msgid "Recreational activities and performances in parks"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3900
msgid "Recycling activities and services and the disposal of other waste"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1920
msgid "Refined petroleum products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4311
msgid "Remove the installations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7740
msgid ""
"Rent forms of intellectual property and similar products, except for "
"copyright works"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7722
msgid "Rental of video tapes and CDs"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7730
msgid "Renting and leasing of other physical devices and equipment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7729
msgid "Renting and renting other personal and household products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7721
msgid "Renting and renting sports and leisure products and tools"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7710
msgid "Renting motor vehicles"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3313
msgid "Repair of electronic and optical devices"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9524
msgid "Repair of furniture and household items"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3311
msgid "Repair of manufactured metal products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9529
msgid "Repair of other household and personal products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9522
msgid "Repair of tools, household appliances, and garden care equipment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3315
msgid "Repair of transport devices, except for motor vehicles"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3319
msgid "Repair other devices"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7210
msgid ""
"Research and experimental development in the field of natural and "
"engineering sciences"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5610
msgid "Restaurant service and food delivery activities by mobile means"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4751
msgid "Retail sale in clothing stores"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4711
msgid "Retail sale in non-specialized stores of food, beverages or tobacco"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4721
msgid "Retail sale in specialized food stores"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4764
msgid "Retail sale in specialized games and toys stores"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4722
msgid "Retail sale in specialized stores for drinks"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4761
msgid "Retail sale in specialized stores of books, newspapers, and stationery"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4753
msgid ""
"Retail sale in specialized stores of carpets, blankets, wall and floor "
"coverings"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4752
msgid "Retail sale in specialized stores of hardware, paint and glass"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4759
msgid ""
"Retail sale in specialized stores of household electrical appliances, "
"furniture, lighting equipment and other household appliances"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4762
msgid "Retail sale in specialized stores of music and video recordings"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4773
msgid "Retail sale in specialized stores of other new products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4772
msgid ""
"Retail sale in specialized stores of pharmaceutical, medical and "
"pharmaceutical products, ornamental and cosmetic products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4771
msgid ""
"Retail sale in specialized stores of shoes, clothing and leather products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4763
msgid "Retail sale in specialized stores of sports equipment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4723
msgid "Retail sale in specialized stores of tobacco products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4742
msgid "Retail sale in stores specialized in audio-visual equipment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4741
msgid ""
"Retail sale in stores specialized in computer hardware, accessories, "
"computer software, and communications equipment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4730
msgid "Retail sale of specialized vehicles for fuel"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4774
msgid "Retail sale of used products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4782
msgid "Retail sale through kiosks and markets of clothes, fabrics and shoes"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4781
msgid ""
"Retail sale through kiosks and markets of food, soft drinks and tobacco "
"products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4791
msgid "Retail sale via mail requests or through the Internet"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4789
msgid "Retail sale via stalls of other products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0442
msgid "Revenue from constructed real estate"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0461
msgid "Revenue from non-funders"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0462
msgid "Revenue of transferred capital"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6621
msgid "Risk and damage assessment"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0322
msgid "River farms"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0312
msgid "River fishing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4210
msgid "Road and railway constructions"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_SK
msgid "Sack"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4530
msgid "Sale of motor vehicle parts and accessories"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4510
msgid "Sale of motor vehicles"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4540
msgid ""
"Sale, maintenance and repair of motorcycles, parts and accessories thereof"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0893
msgid "Salt extraction"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6130
msgid "Satellite communication activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1610
msgid "Sawing wood and abrasion"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6612
msgid "Security and commodity contracts brokerage"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8423
msgid "Security and public order activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8020
msgid "Security systems services activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0910
msgid "Service activities in support of oil and natural gas extraction"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0990
msgid "Service activities in support of other mining and quarrying activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5221
msgid "Service activities related to road transport"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.res_config_settings_view_form
msgid ""
"Set the threshold amount for invoices that won't require the VAT ID of "
"individuals when invoicing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3700
msgid "Sewer"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1621
msgid "Sheets made of wood veneer and wood-based panels"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4912
msgid "Shipping by rail"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9523
msgid "Shoe and leather products repair"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1520
msgid "Shoe manufacturing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5510
msgid "Short-term placement activities (rental - housing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_SH
msgid "Shrink ( Shrink )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_D10
msgid "Siemens per meter ( S/m )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.view_move_form_inherit
msgid "Sign Invoice"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.actions.server,name:l10n_eg_edi_eta.action_sign_invoices
msgid "Sign invoices"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_bank_statement_line__l10n_eg_signing_time
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_move__l10n_eg_signing_time
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_payment__l10n_eg_signing_time
msgid "Signing Time"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8810
msgid ""
"Social work activities for the infirm and disabled that take place without "
"accommodation"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8730
msgid "Spa facilities for the elderly and disabled"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7410
msgid "Specialized design activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1101
msgid "Spirits distilled, refined and mixed"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8541
msgid "Sports and rehabilitation education"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9312
msgid "Sports club activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3230
msgid "Sports products industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_CMK
msgid "Square centimeter ( cm2 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_FTK
msgid "Square foot ( ft2 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_INK
msgid "Square inch ( Inch2 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_KMK
msgid "Square kilometer ( km2 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_MTK
msgid "Square meter ( m2 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_MMK
msgid "Square millimeter ( mm2 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.eg_partner_address_form
msgid "State..."
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3530
msgid "Steam supply and air conditioning"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.eg_partner_address_form
msgid "Street"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2511
msgid "Structural metal products industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_bank_statement_line__l10n_eg_submission_number
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_move__l10n_eg_submission_number
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_account_payment__l10n_eg_submission_number
msgid "Submission ID"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5912
msgid ""
"Subsequent activities for the production of movies, videos and television "
"programs"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1072
msgid "Sugar industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0161
msgid "Support activities for crop production"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8110
msgid "Support activities for joint facilities"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid "System not supported"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1511
msgid "Tanning and processing of leather, fillings and dyeing of fur"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_8522
msgid "Technical and vocational secondary education"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7120
msgid "Technical tests and analyzes"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6020
msgid "Television program preparation and broadcast activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1312
msgid "Textile weave"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9411
msgid ""
"The activities of commercial enterprises, employers and professional "
"membership organizations"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.res_config_settings_view_form
msgid "The client ID retrieved from the ETA e-invoicing portal"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2816
msgid "The elevators and equipment needed for it"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2410
msgid "The industry of basic iron and steel"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7010
msgid "The main office activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1103
msgid ""
"The manufacture of alcoholic drinks derived from the molten and the "
"manufacture of molten"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1430
msgid "The manufacture of clothing, knitted and crocheted"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2620
msgid "The manufacture of electronic computers and related devices"
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.res_config_settings_view_form
msgid "The secret key provided by the ETA. You can input client secret 1 or 2"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1313
msgid "The textile industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,help:l10n_eg_edi_eta.field_product_template__l10n_eg_eta_code
msgid ""
"This can be an EGS or GS1 product code, which is needed for the e-invoice.  "
"The best practice however is to use that code also as barcode and in that "
"case, you should put it in the Barcode field instead and leave this field "
"empty."
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,help:l10n_eg_edi_eta.field_product_product__l10n_eg_eta_code
msgid ""
"This can be an EGS or GS1 product code, which is needed for the e-invoice.  "
"The best practice however is to use that code also as barcode and in that "
"case, you should put it in the Barcode field instead and leave this field "
"empty. "
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid ""
"This invoice has been marked as invalid by the ETA. Please check the ETA "
"website for more information"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid ""
"This invoice has been sent to the ETA, but we are still awaiting validation"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,help:l10n_eg_edi_eta.field_account_journal__l10n_eg_activity_type_id
msgid ""
"This is the activity type of the branch according to Egyptian Tax Authority"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,help:l10n_eg_edi_eta.field_uom_uom__l10n_eg_unit_code_id
msgid "This is the type of unit according to egyptian tax authority"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,help:l10n_eg_edi_eta.field_account_journal__l10n_eg_branch_identifier
msgid ""
"This number can be found on the taxpayer profile on the eInvoicing portal. "
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,help:l10n_eg_edi_eta.field_res_company__l10n_eg_invoicing_threshold
#: model:ir.model.fields,help:l10n_eg_edi_eta.field_res_config_settings__l10n_eg_invoicing_threshold
msgid ""
"Threshold at which you are required to give the VAT number of the customer. "
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.actions.act_window,name:l10n_eg_edi_eta.action_eta_thumb_drive_tree
#: model:ir.ui.menu,name:l10n_eg_edi_eta.menu_action_eta_thumb_drive_tree
msgid "Thumb Drive"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model,name:l10n_eg_edi_eta.model_l10n_eg_edi_thumb_drive
msgid "Thumb drive used to sign invoices in Egypt"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0115
msgid "Tobacco cultivation"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_TON
msgid "Ton (metric)"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_ST
msgid "Ton (short,2000 lb)"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_D41
msgid "Ton/Cubic meter ( t/m3 )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_TNE
msgid "Tonne ( t )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7911
msgid "Tourism agency services"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9420
msgid "Trade union activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_5011
msgid "Transportation of marine and coastal passengers"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4921
msgid "Transporting land passengers outside and inside cities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3822
msgid "Treatment and disposal of hazardous waste"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3821
msgid "Treatment and disposal of non-hazardous waste"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid "Unauthorized"
msgstr ""

#. module: l10n_eg_edi_eta
#. openerp-web
#: code:addons/l10n_eg_edi_eta/static/src/js/sign_invoice.js:0
#, python-format
msgid "Unexpected error:"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "Unknown error"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0721
msgid "Uranium and raw thorium mining"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.fields,field_description:l10n_eg_edi_eta.field_l10n_eg_edi_thumb_drive__user_id
msgid "User"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_7500
msgid "Veterinary activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_VLT
msgid "Volt ( V )"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/res_currency_rate.py:0
#, python-format
msgid "Warning for %s"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_9601
msgid "Wash and clean textile and fur products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2652
msgid "Watch and alarm clock industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_3600
msgid "Water collection, treatment and supply"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2513
msgid "Water vapor generators except for central heating boilers in hot water"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_WTT
msgid "Watt ( W )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_WEE
msgid "Weeks ( Weeks )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4653
msgid "Wholesale trade for agricultural equipment, machinery and supplies"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4649
msgid "Wholesale trade for other household appliances"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4669
msgid ""
"Wholesale trade for waste, waste and other products not classified elsewhere"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4620
msgid "Wholesale trade in agricultural raw materials and live animals"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4662
msgid "Wholesale trade in precious metals and minerals"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4641
msgid "Wholesale trade of clothes, fabrics and shoes"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4651
msgid ""
"Wholesale trade of computer hardware, accessories and computer software"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4661
msgid "Wholesale trade of dry, liquid and gaseous fuels and related products"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4652
msgid ""
"Wholesale trade of electronic devices, communications devices and "
"accessories"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4659
msgid "Wholesale trade of equipment and other devices"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4630
msgid "Wholesale trade of food, beverages and tobacco"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4610
msgid "Wholesale trade on the basis of a contract or a fee"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_4663
msgid ""
"Wholesale trade, supplies and equipment for building materials, hardware, "
"plumbing and heating appliances"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1102
msgid "Winemaking"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_2733
msgid "Wire devices industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6110
msgid "Wired telecommunications activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6120
msgid "Wireless communication activities"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0220
msgid "Wood cutting"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1623
msgid "Wooden boxes industry"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_YRD
msgid "Yards ( yd )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_ANN
msgid "Years ( yr )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:ir.model.constraint,message:l10n_eg_edi_eta.constraint_l10n_eg_edi_thumb_drive_user_drive_uniq
msgid "You can only have one thumb drive per user per company!"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid ""
"You cannot issue an invoice to a partner with the same VAT number as the "
"branch."
msgstr ""

#. module: l10n_eg_edi_eta
#: model_terms:ir.ui.view,arch_db:l10n_eg_edi_eta.eg_partner_address_form
msgid "ZIP"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "an Unknown error has occured"
msgstr ""

#. module: l10n_eg_edi_eta
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#: code:addons/l10n_eg_edi_eta/models/account_edi_format.py:0
#, python-format
msgid "an Unknown error has occurred"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_BAR
msgid "bar ( bar )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_EA
msgid "each (ST) ( ST )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_GLL
msgid "gallon ( gal )"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6511
msgid "life insurance"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_LB
msgid "pounds"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_1811
msgid "printing"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_PMP
msgid "pump"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_6520
msgid "re Insurance"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.uom.code,name:l10n_eg_edi_eta.l10n_eg_edi_uom_code_RUN
msgid "run"
msgstr ""

#. module: l10n_eg_edi_eta
#: model:l10n_eg_edi.activity.type,name:l10n_eg_edi_eta.l10n_eg_activity_type_0121
msgid "the cultivation of grapevines."
msgstr ""
