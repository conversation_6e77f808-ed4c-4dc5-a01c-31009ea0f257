<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.form.auth_password_policy</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval ="20"/>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <!-- add before the Access Rights section -->
            <xpath expr="//div[@id='allow_import']" position="before">
                <div class="col-12 col-lg-6 o_setting_box">
                    <div class="o_setting_left_pane"/>
                    <div class="o_setting_right_pane">
                        <label for="minlength"/>
                        <field name="minlength"/>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
