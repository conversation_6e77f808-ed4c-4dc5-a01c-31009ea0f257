# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth_exhibitor
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: 山西清水欧度(QQ:54773801) <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_booked_template
msgid "<b>Sponsor</b>:"
msgstr "<b>赞助</b>:"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                        <span class=\"mandatory_mark\"> *</span>"
msgstr ""
"<span>Email</span>\n"
"                        <span class=\"mandatory_mark\"> *</span>"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                        <span class=\"mandatory_mark\"> *</span>"
msgstr ""
"<span>名称</span>\n"
"                        <span class=\"mandatory_mark\"> *</span>"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "<strong>Sponsor Details</strong>"
msgstr "<strong>赞助商详情</strong>"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,help:website_event_booth_exhibitor.field_event_booth__sponsor_subtitle
msgid "Catchy marketing sentence for promote"
msgstr "用于促销的朗朗上口的营销句子"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Contact me through a different email/phone."
msgstr "通过不同的电子邮件/电话与我联系。"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__use_sponsor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth_category__use_sponsor
msgid "Create Sponsor"
msgstr "创建赞助商"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Description"
msgstr "说明"

#. module: website_event_booth_exhibitor
#: model:ir.model,name:website_event_booth_exhibitor.model_event_booth
msgid "Event Booth"
msgstr "活动展位"

#. module: website_event_booth_exhibitor
#: model:ir.model,name:website_event_booth_exhibitor.model_event_booth_category
msgid "Event Booth Category"
msgstr "活动展位类别"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_search
msgid "Exhibitor type"
msgstr "参展商类型"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_search
msgid "Group By"
msgstr "分组"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,help:website_event_booth_exhibitor.field_event_booth__use_sponsor
#: model:ir.model.fields,help:website_event_booth_exhibitor.field_event_booth_category__use_sponsor
msgid "If set, when booking a booth a sponsor will be created for the user"
msgstr "如果设置，则在预订展位时将为用户创建赞助商"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Phone"
msgstr "电话"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Picture"
msgstr "图片"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Slogan"
msgstr "口号"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_id
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_view_search
msgid "Sponsor"
msgstr "赞助"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_website_description
msgid "Sponsor Description"
msgstr "赞助商说明"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_email
msgid "Sponsor Email"
msgstr "赞助商 Email"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_type_id
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth_category__sponsor_type_id
msgid "Sponsor Level"
msgstr "赞助商级别"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_image_512
msgid "Sponsor Logo"
msgstr "赞助商 Logo"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_mobile
msgid "Sponsor Mobile"
msgstr "赞助商手机"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_name
msgid "Sponsor Name"
msgstr "赞助商名称"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_phone
msgid "Sponsor Phone"
msgstr "赞助商电话"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_subtitle
msgid "Sponsor Slogan"
msgstr "赞助标语"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth_category__exhibitor_type
msgid "Sponsor Type"
msgstr "主办单位类型"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_search
msgid "Sponsor type"
msgstr "赞助商类型"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_form
msgid "Sponsorship"
msgstr "赞助"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid ""
"This booth type allows you to have visibility on the event website. Please "
"fill in this form"
msgstr "这种展位类型可让您在活动网站上获得可见性。请填写这张表格"
