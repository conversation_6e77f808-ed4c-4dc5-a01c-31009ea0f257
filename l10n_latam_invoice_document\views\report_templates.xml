<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="external_layout_standard" inherit_id="web.external_layout_standard" >
        <!-- support for custom header -->
        <div t-attf-class="header o_company_#{company.id}_layout" position="attributes">
            <attribute name="t-if">not custom_header</attribute>
        </div>
        <div t-attf-class="header o_company_#{company.id}_layout" position="after">
            <div t-attf-class="header o_company_#{company.id}_layout" t-if="custom_header">
                <t t-call="#{custom_header}"/>
            </div>
        </div>

        <!-- support for custom footer -->
        <div t-attf-class="footer o_standard_footer o_company_#{company.id}_layout" position="attributes">
            <attribute name="t-if">not custom_footer</attribute>
        </div>

        <div t-attf-class="footer o_standard_footer o_company_#{company.id}_layout" position="after">
            <div t-attf-class="footer o_standard_footer o_company_#{company.id}_layout" t-if="custom_footer">
                <t t-out="custom_footer"/>
            </div>
        </div>
    </template>

    <template id="external_layout_bold" inherit_id="web.external_layout_bold" >
        <!-- support for custom header -->
        <div t-attf-class="header o_company_#{company.id}_layout" position="attributes">
            <attribute name="t-if">not custom_header</attribute>
        </div>
        <div t-attf-class="header o_company_#{company.id}_layout" position="after">
            <div t-attf-class="header o_company_#{company.id}_layout" t-if="custom_header">
                <div class="o_clean_header">
                    <t t-call="#{custom_header}"/>
                </div>
            </div>
        </div>

        <!-- support for custom footer -->
        <div t-attf-class="footer o_clean_footer o_company_#{company.id}_layout" position="attributes">
            <attribute name="t-if">not custom_footer</attribute>
        </div>
        <div t-attf-class="footer o_clean_footer o_company_#{company.id}_layout" position="after">
            <div t-attf-class="footer o_clean_footer o_company_#{company.id}_layout" t-if="custom_footer">
                <t t-out="custom_footer"/>
            </div>
        </div>
    </template>

    <template id="external_layout_boxed" inherit_id="web.external_layout_boxed" >
        <!-- support for custom header -->
        <div t-attf-class="header o_company_#{company.id}_layout" position="attributes">
            <attribute name="t-if">not custom_header</attribute>
        </div>
        <div t-attf-class="header o_company_#{company.id}_layout" position="after">
            <div t-attf-class="header o_company_#{company.id}_layout" t-if="custom_header">
                <div class="o_boxed_header">
                    <t t-call="#{custom_header}"/>
                </div>
            </div>
        </div>

        <!-- support for custom footer -->
        <div t-attf-class="footer o_boxed_footer o_company_#{company.id}_layout" position="attributes">
            <attribute name="t-if">not custom_footer</attribute>
        </div>
        <div t-attf-class="footer o_boxed_footer o_company_#{company.id}_layout" position="after">
            <div t-attf-class="footer o_boxed_footer o_company_#{company.id}_layout" t-if="custom_footer">
                <t t-out="custom_footer"/>
            </div>
        </div>
    </template>

    <template id="external_layout_striped" inherit_id="web.external_layout_striped" >
        <!-- support for custom header -->
        <div t-attf-class="o_company_#{company.id}_layout header" position="attributes">
            <attribute name="t-if">not custom_header</attribute>
        </div>
        <div t-attf-class="o_company_#{company.id}_layout header" position="after">
            <div t-attf-class="o_company_#{company.id}_layout header" t-if="custom_header">
                <div class="o_background_header">
                    <t t-call="#{custom_header}"/>
                </div>
            </div>
        </div>

        <!-- support for custom footer -->
        <div t-attf-class="o_company_#{company.id}_layout footer o_background_footer" position="attributes">
            <attribute name="t-if">not custom_footer</attribute>
        </div>
        <div t-attf-class="o_company_#{company.id}_layout footer o_background_footer" position="after">
            <div t-attf-class="o_company_#{company.id}_layout footer o_background_footer" t-if="custom_footer">
                <t t-out="custom_footer"/>
            </div>
        </div>
    </template>

</odoo>
