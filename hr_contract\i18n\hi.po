# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_contract
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-06-03 04:50+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Hindi (http://www.transifex.com/odoo/odoo-9/language/hi/)\n"
"Language: hi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_base_action_rule
msgid "Action Rules"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_advantages
msgid "Advantages"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Advantages..."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_wage
msgid "Basic Salary of the employee"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_vehicle
msgid "Company Vehicle"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Reference"
msgstr ""

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_id_5678
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Contract Type"
msgstr ""

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract_type
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract_type
msgid "Contract Types"
msgstr ""

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_pending
#: model:mail.message.subtype,description:hr_contract.mt_department_contract_pending
msgid "Contract about to expire"
msgstr ""

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_close
msgid "Contract expired"
msgstr ""

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_department_contract_pending
msgid "Contract to Renew"
msgstr ""

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.act_hr_employee_2_hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_contract_ids
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_contracts_count
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Contracts"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_create_uid
msgid "Created by"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_create_date
msgid "Created on"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Current Contract"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Current Employee"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_department_id
msgid "Department"
msgstr "विभाग"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_display_name
msgid "Display Name"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Duration"
msgstr "अवधि"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employee"
msgstr "कर्मचारी"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract
#, fuzzy
msgid "Employee Contract"
msgstr "कर्मचारी"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_date_end
msgid "End Date"
msgstr "समाप्ति तिथि"

#. module: hr_contract
#: constraint:hr.contract:0
msgid "Error! Contract start-date must be less than contract end-date."
msgstr ""

#. module: hr_contract
#: selection:hr.contract,state:0
#: model:mail.message.subtype,name:hr_contract.mt_contract_close
msgid "Expired"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_type_sequence
msgid "Gives the sequence when displaying a list of Contract."
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Group By"
msgstr ""

#. module: hr_contract
#: model:ir.actions.server,name:hr_contract.contract_set_as_close
msgid "HR Contract: set as close"
msgstr ""

#. module: hr_contract
#: model:ir.actions.server,name:hr_contract.contract_set_as_pending
msgid "HR Contract: set as pending"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_vehicle_distance
msgid "Home-Work Dist."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_id
msgid "ID"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee_vehicle_distance
msgid "In kilometers"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Information"
msgstr "जानकारी"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_manager
msgid "Is a Manager"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Job"
msgstr "कार्य"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_job_id
msgid "Job Title"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract___last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type___last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee_contract_id
msgid "Latest contract of the employee"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Medical Exam"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_medic_exam
msgid "Medical Examination Date"
msgstr ""

#. module: hr_contract
#: selection:hr.contract,state:0
msgid "New"
msgstr "नया"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_notes
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Notes"
msgstr "टिप्पणियाँ"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_children
msgid "Number of Children"
msgstr ""

#. module: hr_contract
#: model:ir.filters,name:hr_contract.contract_open
msgid "Open Contracts"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_place_of_birth
msgid "Place of Birth"
msgstr "जन्म स्थान"

#. module: hr_contract
#: selection:hr.contract,state:0
msgid "Running"
msgstr "फैलना"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Salary and Advantages"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Search Contract"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_search
msgid "Search Contract Type"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_sequence
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_search
msgid "Sequence"
msgstr "अनुक्रम"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_date_start
msgid "Start Date"
msgstr "प्रारंभ दिनांक"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_state
msgid "Status"
msgstr "स्थिति"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_state
msgid "Status of the contract"
msgstr ""

#. module: hr_contract
#: selection:hr.contract,state:0
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model:mail.message.subtype,name:hr_contract.mt_contract_pending
msgid "To Renew"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_trial_date_end
msgid "Trial End Date"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Trial Period Duration"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_trial_date_start
msgid "Trial Start Date"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Unread Messages"
msgstr "अपठित संदेश"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_base_action_rule_trg_date_resource_field_id
msgid "Use employee work schedule"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_base_action_rule_trg_date_resource_field_id
msgid "Use the user's working schedule."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_visa_expire
msgid "Visa Expire Date"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_visa_no
msgid "Visa No"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_wage
msgid "Wage"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Work Permit"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_permit_no
msgid "Work Permit No"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_working_hours
msgid "Working Schedule"
msgstr ""

#~ msgid "Followers"
#~ msgstr "फ़ॉलोअर्स"

#~ msgid "If checked new messages require your attention."
#~ msgstr "sale"

#~ msgid "Messages"
#~ msgstr "संदेश"

#~ msgid "Messages and communication history"
#~ msgstr "संदेश और संचार इतिहास"
