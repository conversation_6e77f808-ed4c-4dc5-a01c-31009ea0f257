# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_paypal
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_api_access_token
msgid "Access Token"
msgstr "Pristupni token"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_api_access_token_validity
msgid "Access Token Validity"
msgstr ""

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr "Fiksne domaće provizije"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr "Fiksne internacionalne provizije"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.acquirer_form_paypal
msgid "How to configure your paypal account?"
msgstr ""

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr "Ručna konfiguracija"

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Sticaoc plaćanja"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_pdt_token
msgid ""
"Payment Data Transfer allows you to receive notification of successful "
"payments as they are made."
msgstr ""

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcija plaćanja"

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_email_account
msgid "Paypal Email ID"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Paypal Instant Payment Notification"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_seller_account
msgid "Paypal Merchant ID"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_pdt_token
msgid "Paypal PDT Token"
msgstr ""

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment.py:138
#, python-format
msgid "Paypal: received data with missing reference (%s) or txn_id (%s)"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__provider
msgid "Provider"
msgstr "Provajder"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_api_password
msgid "Rest API Password"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_api_username
msgid "Rest API Username"
msgstr ""

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr "Slipovi"

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_seller_account
msgid ""
"The Merchant ID is used to ensure communications coming from Paypal are "
"valid and secured."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_txn_type
msgid "Transaction type"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Use IPN"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_api_enabled
msgid "Use Rest API"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr "Variabilne domaće provizije (u procentima)"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr "Varijabilne internacionalne provizije (u procentima)"

#. module: payment_paypal
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr "Žičani prenos"
