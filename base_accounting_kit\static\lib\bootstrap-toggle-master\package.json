{"name": "bootstrap-toggle", "description": "Bootstrap Toggle is a highly flexible Bootstrap plugin that converts checkboxes into toggles", "version": "2.2.2", "main": "js/bootstrap-toggle.js", "keywords": ["bootstrap", "toggle", "bootstrap-toggle", "switch", "bootstrap-switch"], "homepage": "http://www.bootstraptoggle.com", "repository": {"type": "git", "url": "https://github.com/minhur/bootstrap-toggle.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/minhur/bootstrap-toggle/issues"}, "devDependencies": {"grunt-contrib-clean": "^0.6.0", "grunt-contrib-cssmin": "^0.10.0", "grunt-contrib-uglify": "^0.6.0"}}