# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * board
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-08-24 09:04+0000\n"
"PO-Revision-Date: 2018-08-24 09:04+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:53
#, python-format
msgid ""
"\"Add to\n"
"                Dashboard\""
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:25
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/favorite_menu.js:103
#, python-format
msgid "'%s' added to dashboard"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:83
#, python-format
msgid "Add"
msgstr "ઉમેરો"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:78
#, python-format
msgid "Add to my Dashboard"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:362
#, python-format
msgid "Are you sure you want to remove this item?"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:439
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr "બૉર્ડ"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:7
#, python-format
msgid "Change Layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:5
#, python-format
msgid "Change Layout.."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:36
#, python-format
msgid "Choose dashboard layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/favorite_menu.js:107
#, python-format
msgid "Could not add filter to dashboard"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:80
#, python-format
msgid "Edit Layout"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ઓળખ"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board____last_update
msgid "Last Modified on"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:41
#, python-format
msgid "Layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:43
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
#, python-format
msgid "My Dashboard"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/favorite_menu.js:104
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:51
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                menu, switch to list or graph view, and click"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:55
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                dashboard using the search options."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:49
#, python-format
msgid "Your personal dashboard is empty"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:53
#, python-format
msgid "in the extended search options."
msgstr ""
