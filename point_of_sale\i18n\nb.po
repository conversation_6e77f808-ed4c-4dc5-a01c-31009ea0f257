# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON> <ma<PERSON>@stedjan.com>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-05 14:42+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr "REFUNDERING"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order ?"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "& invoice"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "(Both will be sent by email)"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr "(GJENOPPRETTING AV %(session)s)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "(as of opening)"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "(update)"
msgstr "(oppdater)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid ". Please contact your manager to accept the closing difference."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to manage tax-included prices"
msgstr ""
"<i class=\"fa fa-fw fa-arrow-right\"/>Hvordan administrere priser inkludert "
"mva"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "<p>Dear %s,<br/>Here is your electronic ticket for the %s. </p>"
msgstr "<p>Hei %s,<br/>Her er din elektroniske kvittering for %s. </p>"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid ""
"<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first "
"session.</p>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid ""
"<span attrs=\"{'invisible': [('is_total_cost_computed','=', "
"True)]}\">TBD</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Verdier satt er er spesifikt "
"for firma.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Authorized Employees</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Barcodes</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Default Pricelist</span>"
msgstr "<span class=\"o_form_label\">Standard Prisliste</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Journal Entries</span>"
msgstr "<span class=\"o_form_label\">Bilag</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Order Reference</span>"
msgstr "<span class=\"o_form_label\">Ordrereferanse</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Payment Methods</span>"
msgstr "<span class=\"o_form_label\">Betalingsmetoder</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>Hopp over forhåndsvisning</b></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Cash Balance</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Date</span>"
msgstr "<span>Siste avslutningsdato</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Rapportering</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>Vis</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "? Ved å klikke \"Valider\" vil betalingen bli validert."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr "Kundenavn er påkrevd"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""
"En global identifikator for dette kassapunktet. Brukes til å unngå "
"konflikter i data generert fra klienten."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr ""
"Et sekvensnummer som inkrementeres hver gang en bruker gjennopptar "
"kassaøkten"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "Et sekvensnummer som inkrementeres for hver ordre"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr ""
"En økt er en tidsperiode. Vanligvis én dag, der du selger igjennom "
"kassasystemet."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "Et unikt sekvensummer for ordren"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr "En kort tekst som blir brukt som bunntekst på kvitteringer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr "En kort tekst som blir brukt som topptekst på kvitteringer."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr "Ta imot tips fra kunder eller konverter vekslepengene deres til tips"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Accept payments difference and post a profit/loss journal entry"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr "Ta imot betalinger med betalingsterminal fra Vantiv"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "Ta imot betalinger med betalingsterminal fra Adyen"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "Konto"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "Kontantavrunding"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontoplanmal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "Regnskap"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "Regnskapsjournal som blir brukt ved fakturering."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "Handling påkrevd"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "Aktiv"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorering for Aktivitetsunntak"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "Aktivitetsstatus"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon type Aktivitet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#, python-format
msgid "Add"
msgstr "Legg til"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.js:0
#, python-format
msgid "Add Customer Note"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Add Tip"
msgstr "Legg til tips"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add a custom message to header and footer"
msgstr "Legg til en tilpasset beskjed i topptekst og bunntekst"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Add a customer"
msgstr "Legg til kunde"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "Legg til en ny betalingsmetode"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add notes on order lines to be printed on receipt and invoice"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Address"
msgstr "Adresse"

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "Administrator"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
msgid "Advanced Cash Control"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Advanced Pricelists"
msgstr "Avanserte prislister"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "Adyen Betalingsterminal"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "All active orders"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"Alle tilgjengelige prislister må ha samme valuta som firmaet eller "
"salgsjournalen, som brukes for dette kassapunktet, hvis du bruker "
"regnskapsmodulen."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Alle betalingsmetoder må ha samme valuta som salgsjournalen, eventuelt samme"
" valuta som firmaet hvis valuta på salgsjournalen ikke er satt."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "Alle salgslinjer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow discounts per line"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow global discounts on orders"
msgstr "Tillat globale rabatter på ordrer"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Allow the use of coupon and promotion programs in PoS."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Allow the use of gift card"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid ""
"Allow to write notes for customer on Orderlines. This will be shown in the "
"receipt."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "Allowed Pricelists"
msgstr "Tilgjengelige prislister"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
msgid "Amount"
msgstr "Beløp"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "Maks tillatt kassadifferanse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "Totalbeløp"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr ""
"En feil oppstod ved innlasting av produktpriser. Sjekk at alle prislister er"
" tilgjengelig for kassesystemet."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "En intern identifikator for kassapunktet."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "En annen økt er allerede igangsatt for dette kassapunktet."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "Arkivert"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "Er du sikker på at kunden vil betale"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "Så snart som mulig"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (recommended)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "Antall vedlegg"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "Attributter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Authorized Difference"
msgstr "Tillatt differanse"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr "Autogenerert økt for ordrer uten tilknyttet økt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "Automatisk kvitteringsutskrift"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "Åpne kassaskuffen automatisk."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "Tilgjengelige produktkategorier for kasse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Available Pricelists"
msgstr "Tilgjengelige prislister"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "Tilgjengelig i kassaløsningen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "Gjennomsnittspris"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ControlButtonPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Back"
msgstr "Tilbake"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/NumberPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenNumpad.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Backspace"
msgstr "Backspace"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr "Bank"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement
msgid "Bank Statement"
msgstr "Kontoutskrift"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Barcode"
msgstr "Strekkode"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Nomenclature"
msgstr "Strekkode-nomenklatur"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "Strekkoderegel"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "Strekkodeleser"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Scanner/Card Reader"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "Grunnlag"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "Fakturaer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Bills &amp; Receipts"
msgstr "Regninger og Kvitteringer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Buffer:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_partners_loading
msgid ""
"By default, 100 partners are loaded.\n"
"When the session is open, we keep on loading all remaining partners in the background.\n"
"In the meantime, you can use the 'Load Customers' button to load partners from database."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "Hopp over nettleserutskrift og print via proxy."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "CASH"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "CHANGE"
msgstr "VEKSEL"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "CLOSING CONTROL"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Can't change customer"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Can't mix order with refund products with new products."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "Kanseller"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr "Avbryt betalingsforespørsel"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "Kansellert"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot Invoice"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#, python-format
msgid "Cannot access product information screen if offline."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Cannot close the session when offline."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot invoice order from closed session."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Cannot modify a tip"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "Kan ikke gi veksel uten en betalingsmetode av type kontant "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#, python-format
msgid "Cash"
msgstr "Kontanter"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_out
msgid "Cash Box Out"
msgstr "Kontantuttak"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash In"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/CashMoveButton.xml:0
#, python-format
msgid "Cash In/Out"
msgstr "Innskudd/uttak"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "Kontantjournal"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash Out"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Cash Register"
msgstr "Kontantbeholdning"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
msgid "Cash Rounding"
msgstr "Avrunding"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Avrundinger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_ids
msgid "Cash Statements"
msgstr "Kontantuttalelse"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register for %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
msgid "Cash rounding"
msgstr "Avrunding"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.account_cashbox_line_view_tree
msgid "Cashbox balance"
msgstr "Kontantbalanse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Cashdrawer"
msgstr "Kassaskuff"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Cashier"
msgstr "Ekspeditør"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"Kategorier brukes for å bla igjennom produkter\n"
"i brukergrensesnittet."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "Kategori"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "Kategorinavn"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Category Pictures"
msgstr "Kategoribilder"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr "Kategorier brukt i kassasystemet."

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "Stoler"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Change"
msgstr "Veksel"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Change Customer"
msgstr "Endre kunde"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Change Tip"
msgstr "Endre tips"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr "Huk av om produktet skal veies med integrert vekt."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "Huk av om dette produktet skal vises i kassasystemet."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr ""
"Huk av om du ønsker å gruppere produkter i denne kategorien på kassaordrer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "Tell opp kontantbeholdning ved åpning og lukking av økt."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Check the internet connection then try again."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "Underkategorier"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""
"Velg en spesifikk avgiftsregel på ordren, avhenging av type kunde "
"(avgiftsfri, spise inne, takeaway osv)."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Choose among fiscal positions when processing an order"
msgstr "Velg avgiftsregel ved behandling av ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "City"
msgstr "Sted"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Click here to close the session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "Kunde"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Connected"
msgstr "Kundeskjerm tilkoblet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Disconnected"
msgstr "Kundeskjerm frakoblet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Client Screen Unsupported. Please upgrade the IoT Box"
msgstr "Kundeskjerm støttes ikke. Oppgrader IoT-boksen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Warning"
msgstr "Kundeskjerm-advarsel"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/HeaderButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Close"
msgstr "Lukk"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "Lukket og postert"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Closing ..."
msgstr "Avslutter..."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "Avslutningskontroll"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "Avslutningsdato"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Closing session error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Coins/Bills"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "Firma"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
msgid "Company has chart of accounts"
msgstr "Firma har kontoplan"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Konfigurasjon"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Configuration for journal entries of PoS orders"
msgstr "Konfigurasjon av bilag for kassaordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Configure at least one Point of Sale."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#, python-format
msgid "Confirm"
msgstr "Bekreft"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#, python-format
msgid "Confirm ?"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices to your PoS directly without an IoT Box"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "Koble enheter til kassasystemet uten en IoT Box."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices using an IoT Box"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connected Devices"
msgstr "Tilkoblede enheter"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr "Tilkoblet, ingen eier"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr "Kobler til proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Connecting to the IoT Box"
msgstr "Kobler til IoT-boksen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Connection Error"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Connection error"
msgstr "Tilkoblingsfeil"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is aborted"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is lost"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "Tilkobling til IoT Box feilet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Tilkobling til skriveren feilet"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Continue Selling"
msgstr "Fortsett å selge"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Continue selling"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "Konverteringsfrekvens"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "Konverteringssats mellom firmavaluta og ordevaluta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Cost:"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Counted"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Country"
msgstr "Land"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Coupon and Promotion Programs"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Coupons & Promotions"
msgstr "Kuponger og kampanjer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Create"
msgstr "Opprett"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Create a new PoS"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "Opprett en ny produktvariant"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Valutakurs"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "Aktiv økt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "Ansvarlig for økten"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "Status for aktiv økt"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr "Tilpasset"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "Kunde"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr "Kundekonto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Customer Display"
msgstr "Kundeskjerm"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr "Kundeskjerm"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "Kundefaktura"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid "Customer Notes"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Customer Required"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Customer tips, cannot be modified directly"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "Kunder"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "Dashbord"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#, python-format
msgid "Date"
msgstr "Dato"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Days"
msgstr "Dager"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Debug Window"
msgstr "Debug-vindu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__account_id
msgid "Default Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "Standard avgiftsregel"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Intermediary Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
msgid "Default Pricelist"
msgstr "Standard prisliste"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Standard utgående avgift"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "Standard utgående avgift"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "Standard avgift for produkter"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Standardenhet for alle lageroperasjoner."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "Definer en ny kategori"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Define the smallest coinage of the currency used to pay"
msgstr "Definer den minste verdien for valutaen som brukes til betaling"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr ""
"Bruk den minste verdien for valutaen som brukes til betaling med kontanter"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__barcode_nomenclature_id
msgid ""
"Defines what kind of barcodes are available and how they are assigned to "
"products, customers and cashiers."
msgstr ""
"Definerer hva slags strekkoder som er tilgjengelig, og hvordan de er "
"tilknyttet produkter, kunder og ekspeditører."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "Utsett validering"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Delete"
msgstr "Slett"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr "Slett betalte ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Paid Orders ?"
msgstr "Slett betalte ordrer?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr "Slett ubetalte ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Unpaid Orders ?"
msgstr "Slett ubetalte ordrer?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Deselect Customer"
msgstr "Velg bort kunde"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_organizer
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "Oppbevaring til pult"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_pad
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr "Skrivematte"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "Arbeidspulter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_difference
#, python-format
msgid "Difference"
msgstr "Differanse"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr "Differanse mellom teoretisk og opptalt kassebeholdning."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "Digest"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Direct Devices"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Disc"
msgstr "Rab"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "Rab.%"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "Rab:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Discard"
msgstr "Forkast"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Disconnected"
msgstr "Frakoblet"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_consumable
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
msgid "Discount"
msgstr "Rabatt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "Rabatt (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "Rabattnotat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discount:"
msgstr "Rabatt:"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "Rabattert Produkt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discounts"
msgstr "Rabatter"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Dismiss"
msgstr "Avvis"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_display_categ_images
msgid "Display Category Pictures"
msgstr "Vis kategoribilder"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Display pictures of product categories"
msgstr "Vis bilder av produktkategorier"

#. module: point_of_sale
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Har ikke tilgang, hopp over denne dataen for brukerens digest e-post"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Do you want to print using the web printer?"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentasjon"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Don't show again"
msgstr "Ikke vis igjen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "Done by"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr "Last ned betalte ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr "Last ned ubetalte ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Download error traceback"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientLine.xml:0
#, python-format
msgid "EDIT"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Either the server is inaccessible or browser is not connected online."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Electronic Scale"
msgstr "Elektronisk vekt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Email"
msgstr "E-post"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "Email Receipt"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Email sent."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Employee"
msgstr "Ansatt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"Ansatte kan skanne kortet sitt, eller taste inn pin-kode for å logge seg inn"
" i en kassaøkt. Dette kan konfigureres under fanen for HR-innstillinger i "
"skjemaet for ansatte."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Empty Order"
msgstr "Tom ordre"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "Aktiverer integrasjon med elektronisk vekt."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,help:point_of_sale.field_pos_order__invoice_group
msgid "Enables invoice generation from the Point of Sale."
msgstr "Slår på generering av faktura fra kasse."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "Avslutningsdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "Sluttbalanse"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error"
msgstr "Feil"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "Feil! Du kan ikke opprette rekursive kategorier."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Error with Traceback"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Existing orderlines"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Exit Pos"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_expected
#, python-format
msgid "Expected"
msgstr "Forventet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr "Eksporter betalte ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr "Eksporter ubetalte ordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Tilleggsinformasjon"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Financials"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr "Ferdig med import av ordrer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position"
msgstr "Avgiftsregel"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Fiscal Position not found"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position per Order"
msgstr "Avgiftsregel pr ordre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Positions"
msgstr "Avgiftsregler"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Fiscal data module error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome-ikon, for eksempel fa-tasks"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Footer"
msgstr "Bunntekst"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "For upresise industrielle berøringsskjermer."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force Done"
msgstr "Tving ferdigstilt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force done"
msgstr "Tving ferdigstilt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Generation of your order references"
msgstr "Generering av ordrereferanser"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Gift Cards"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Gift card"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Give customer rewards, free samples, etc."
msgstr "Gi kunder belønninger, gratis vareprøver osv."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Setter rekkefølgen når en liste med produktkategorier vises."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
msgid "Global Discounts"
msgstr "Globale rabatter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "Gå til"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Grupper etter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "Grupper produkter i kassasystemet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr "Sikker tilkobling til IoT Box feilet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Events"
msgstr "Maskinvarehandlinger"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Status"
msgstr "Maskinvarestatus"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
msgid "Has Active Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "Har kontantkontroll"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "Har melding"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Header"
msgstr "Topptekst"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
msgid "Header & Footer"
msgstr "Topptekst og bunntekst"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "Gjem bruk betalingsterminal"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#, python-format
msgid "Home"
msgstr "Hjem"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "How would you like to receive your receipt"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "VIKTIG: Feilrapport fra Odoo Kassasystem"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
msgid "IP Address"
msgstr "IP-adresse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for å indikere aktivitetsunntak."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread
msgid "If checked, new messages require your attention."
msgstr "Hvis haket av, vil nye meldinger kreve din oppmerksomhet."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis haket av, har enkelte meldinger leveringsfeil."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
msgid "Image"
msgstr "Bilde"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Import Orders"
msgstr "Importer ordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "Forbedre navigasjonen for upresise industrielle berøringsskjermer "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "Pågår"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "For å slette en ordre, må den ha status som ny eller avbrutt."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#, python-format
msgid "Info"
msgstr "Info"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
msgid "Initial Category"
msgstr "Startkategori"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"Installasjon av kontoplan fra Generelle Innstillinger for\n"
"Fakturerings/Regnskaps-appen, vil automatisk opprette betalingsmetoder\n"
"for bank og kontant."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "Integrerte betalingsterminaler"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "Motpartskonto"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "Interne kategorier"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "Interne notater"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Invalid action"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Invalid amount"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Invalid email."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Invalid product lot"
msgstr "Ugyldig produktparti"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "Lager"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "Administrasjon av lagerbeholdning"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "Faktura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Invoice Journal"
msgstr "Fakturajournal"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Invoiced"
msgstr "Fakturert"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__invoice_group
msgid "Invoicing"
msgstr "Fakturering"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box"
msgstr "IoT-boks"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box IP Address"
msgstr "IoT Box IP-adresse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "Er fakturert"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "Bruker firmaets valuta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Er en bar/restaurant"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "Regnskapsmodul installert"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"Du kan ikke endre avgifter som er brukt i en upostert kassaordre. Du må "
"lukke kassaøkten før du kan endre avgiften."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr "JS-tester"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
msgid "Journal"
msgstr "Journal"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "Bilag"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "Journalpost"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Journal Items"
msgstr "Bilagslinjer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Keep Session Open"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "Kpi Kasse Total Verdi"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.led_lamp
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "LED-lampe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "Merkelapp"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Language"
msgstr "Språk"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "Store rullefelt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "Kontantbeholdning for siste økt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "Dato for avslutning av siste økt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.letter_tray
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "Brevkurv"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_amount
msgid "Limited Partners Amount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_loading
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Partners Loading"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_loading
msgid "Limited Product Loading"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_amount
msgid "Limited Products Amount"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Products Loading"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "Linjenr."

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "List of Cash Registers"
msgstr "Liste over kassapunkter"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Load Customers"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining partners in the background"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining products in the background"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Loading"
msgstr "Laster inn"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Loading Image Error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "Sekvensnummer for innlogging"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "Partinavn"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/OrderWidget.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "Parti-/serienummer påkrevd"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "Parti-/serienummer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_loyalty
msgid "Loyalty Program"
msgstr "Lojalitetsprogram"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Loyalty program to use for this point of sale."
msgstr "Lojalitetsprogram for bruk med dette kassapunktet."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.magnetic_board
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "Magnettavle"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hovedvedlegg"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Utfør betaling"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"Gjør flere prislister tilgjengelig for kassasystemet. Du kan også knytte en "
"prisliste til en spesifikk kunde i kundeskjemaet (i salgsfanen). For at "
"dette skal fungere må prislisten være i denne listen. Ellers vil standard "
"prisliste brukes."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr ""
"Forsikre deg om at du bruker IoT Box v18.12 eller høyere. Naviger til %s for"
" å akseptere sertifikatet til IoT-boksen."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "Administrer kampanje- og kupong-programmer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion and coupon programs."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
msgid "Manual Discounts"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "Margin"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Margin:"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Maximum value reached"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "Melding ved leveringsfeil"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "Meldinger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "Metode"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Miscellaneous"
msgstr "Diverse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
msgid "Module Pos Hr"
msgstr "Modul Pos Hr"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.monitor_stand
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "Skjermstativ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#, python-format
msgid "More..."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "MIn aktivitets tidsfrist"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "Mine økter"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Name"
msgstr "Navn"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Need customer to invoice"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Network Error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "Ny"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "New Order"
msgstr "Ny ordre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "Ny økt"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.newspaper_rack
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "Avisstativ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Neste kalender aktivitet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Frist for neste aktivitet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "Oppsummering av neste aktivitet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "Neste aktivitetstype"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Next Order List"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "No"
msgstr "Nei"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "No Taxes"
msgstr "Ingen avgifter"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr ""
"Ingen kontantuttalelse funnet for denne økten. Kan ikke registrere "
"returnerte kontanter."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "No customer found"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "Ingen data ennå"

#. module: point_of_sale
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr "Ingen tilknyttet faktura for %s."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
#, python-format
msgid "No orders found"
msgstr "Ingen ordre funnet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid "No product found"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "No results found for \""
msgstr "Ingen resultater for \""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "No sequence defined on the journal"
msgstr "Ingen sekvens er definert for journalen"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid "No sessions found"
msgstr "Ingen økter funnet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "None"
msgstr "Ingen"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "Ikke fakturert"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Notes"
msgstr "Notater"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "Antall handlinger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_opened_session
msgid "Number of Opened Session"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Partners Loaded"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "Antall utskrifter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Products Loaded"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "Antall feil"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antall meldinger som krever handling"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antall meldinger med leveringsfeil"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of partners loaded can not be 0"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of product loaded can not be 0"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread_counter
msgid "Number of unread messages"
msgstr "Antall uleste meldinger"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "OPENING CASH CONTROL"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Offline"
msgstr "Frakoblet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Offline Error"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Offline Orders"
msgstr "Offline ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Ongoing"
msgstr "Pågående"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "Bruk avrunding bare med kontanter"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load a limited number of customers at the opening of the PoS."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load most common products at the opening of the PoS."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only on cash methods"
msgstr "Kun på kontanter"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"Kun brukere med lederrettigheter for kassasystemet kan endre priser på "
"produkter."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr "Åpne kassaskuff"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Open POS Menu"
msgstr "Åpne kassameny"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "Åpne økter som bruker denne betalingsmetoden"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr "Åpne økt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Open session"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Opened Sessions"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "Åpnet av"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Opening"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "Åpningskontroll"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "Åpningsdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Opening cash"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.account_cashbox_line_action
msgid "Opening/Closing Values"
msgstr "Verdier ved åpning/lukking"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation Type"
msgstr "Operasjonstype"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Operation type used to record product pickings <br/>\n"
"                                    Products will be taken from the default source location of this operation type"
msgstr ""
"Operasjonstypen som brukes til å registrere plukk av produkter  <br/>\n"
"                                    Produkter vil tas fra standard kildelokasjon for denne operasjonstypen."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "Operasjonstyper vises i dashbordet for Lager."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "Ordre"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Order %s"
msgstr "Ordre %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr "Ordre %s er ikke ferdig betalt."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "Ordreantall"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "Ordredato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
msgid "Order IDs Sequence"
msgstr "Sekvens for Ordre-IDer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "Sekvens for ordrelinje-IDer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "Ordrelinjer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
msgid "Order Ref"
msgstr "Ordreref."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "Sekvensnummer for ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Order is empty"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Order is not synced. Check your internet connection"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "Ordrelinjer"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/TicketButton.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "Ordrer"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "Ordreanalyse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
msgid "Other Devices"
msgstr "Andre enheter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "Annen informasjon"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Others"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr "Kassaordre %s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "Kassaordrelinje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "Kassaordrelinjer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "Kasseordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "Kassaordrelinjer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "Kassasalg"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "POS error"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr "Kassaordrelinje %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "Betalt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "Overordnet kategori"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__partner_load_background
msgid "Partner Load Background"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#, python-format
msgid "Pay"
msgstr "Betal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "Betal ordre"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "Betaling"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "Betalingsdato"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr "Betalingsmetode"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
msgid "Payment Methods"
msgstr "Betalingsmetoder"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "Betalingsreferanse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment Successful"
msgstr "Betaling gjennomført"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr "Betalingsterminaler"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "Betalingstransaksjon ID"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Payment methods available"
msgstr "Tilgjengelige betalingsmetoder"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment request pending"
msgstr "Betalingsforespørsel avventer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment reversed"
msgstr "Betaling reversert"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "Betalinger"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "Betalingsmetoder"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Payments in"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "Payments:"
msgstr "Betalinger:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Pending Electronic Payments"
msgstr ""

#. module: point_of_sale
#: model:ir.filters,name:point_of_sale.filter_orders_per_session
msgid "Per session"
msgstr "Per økt"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Person som bruker kassen. Det kan være en deltidsansatt, student eller "
"medhjelper."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Pick which product categories are available"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "Plukk"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "Plukkantall"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr "Kassaplukk"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr "Plukkinger"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Picture"
msgstr "Bilde"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr "Bekreft stort beløp"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "Forsikre deg om at IoT-boksen er tilkoblet."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr "Forsikre deg om at printeren er tilkoblet."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Please check your internet connection and try again."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr ""
"Lukk alle kassaøkter i denne perioden før du lukker den. Åpne økter er: %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr "Konfigurer en betalingsmetode i kassasystemet."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr "Du må definere inntektskonto for dette produktet: \"%s\" (id: %d)."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr "Skriv ut fakturaen fra backend"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "Oppgi en partner for salget."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Please select a payment method."
msgstr "Velg en betalingsmetode."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please select the Customer"
msgstr "Velg kunde"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pos_categ_id
msgid "PoS Category"
msgstr "Kassakategori"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Interface"
msgstr "Kassagrensesnitt"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr "Kasseordrer"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Product Categories"
msgstr "Produktkategorier for kasse"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "Produktkategori for kasse"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "Kasse"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_pos
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model:ir.ui.menu,name:point_of_sale.menu_pos_config_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Point of Sale"
msgstr "Kasse"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "Kassaanalyse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_id
msgid "Point of Sale Category"
msgstr "Kassakategori"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "Kassapunkt"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "Kassapunkt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
msgid "Point of Sale Configurations"
msgstr "Kassapunkter"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "Kassadetaljer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "Detaljrapport for kasse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "Fakturarapport for kasse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
msgid "Point of Sale Journal"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "Kasse Opprett Betaling Veiviser"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "Gruppe for kasseledere"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "Kasse Operasjonstype"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Kassaordrelinjer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "Kassaordrer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Ordrerapport for kasse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "Betalingsmetoder for Kassasystem"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Kassabetalinger"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "Kasseøkt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "Brukergruppe for kasse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "Kassaoppsett"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "Kassaordre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "Antall kassaordre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "Kassaordrelinje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "Kategorier for kasse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "Kassaøkt-varighet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "Status for kassaøkt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "Brukernavn for kassaøkt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "Kassaøkter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
msgid "PosBox"
msgstr "PosBox"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Postcode"
msgstr "Postnummer"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "Postert"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Previous Order List"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Price"
msgstr "Pris"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Price Control"
msgstr "Priskontroll"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Price Unit"
msgstr "Enhetspris"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Price excl. VAT:"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#, python-format
msgid "Price list"
msgstr "Prisliste"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "Priset produkt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "Prisliste"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Prislister"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Priser"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "Skriv ut"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ControlButtons/ReprintReceiptButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#, python-format
msgid "Print Receipt"
msgstr "Skriv ut kvittering"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr "Skriv ut salgsrapport for gjeldende kassaøkt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print invoices on customer request"
msgstr "Skriv ut fakturaer på forespørsel fra kunde"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "Skriv ut kvitteringer automatisk når betaling er registrert"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Skriv ut via proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Printer"
msgstr "Skriver"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid ""
"Printing is not supported on some browsers due to no default printing "
"protocol is available. It is possible to print your tickets by making use of"
" an IoT Box."
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "Anskaffelsesgruppe"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "Produkt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Produktkategori"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_configurator
msgid "Product Configurator"
msgstr "Produktkonfigurator"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_load_background
msgid "Product Load Background"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product Prices"
msgstr "Produktpriser"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "Kategorier for Produktvariant"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "Produktantall"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "Produktmal"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Produktenhet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "Produkt-enhet"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "Kategorier for Produktenhet"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "Produktvarianter"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Product information"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid ""
"Product is not loaded. Tried loading the product from the server but there "
"is a network error."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product prices on receipts"
msgstr "Produktpriser på kvitteringer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
msgid "Product tips"
msgstr "Produkt for Tips"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Produkter"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Connected"
msgstr "Proxy tilkoblet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr "Proxy frakoblet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Warning"
msgstr "Proxy-advarsel"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Qty"
msgstr "Ant"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "Antall"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "REASON"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr "Les vekt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr "Er du klar for å sette igang <b>kassaløsningen</b>?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Reason"
msgstr "Årsak"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Receipt"
msgstr "Mottak"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr "Kvittering %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
msgid "Receipt Footer"
msgstr "Bunntekst for kvittering"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
msgid "Receipt Header"
msgstr "Topptekst for kvittering"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr "Kvitteringsnummer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Receipt Printer"
msgstr "Kvitteringsskriver"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "Registrer betalinger med en betalingsterminal i denne journalen."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "Gjennopprettingsøkt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Refresh Display"
msgstr "Oppdater skjerm"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Refund"
msgstr "Kreditnota"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#, python-format
msgid "Refunded"
msgstr "Refundert"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "Refunding %s in "
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "Tilbakebetaling."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Remaining"
msgstr "Gjenstående"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Remove"
msgstr "Fjern"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Replenishment"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "Rapportering"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Reprint Invoice"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Request sent"
msgstr "Forespørsel sendt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Reset"
msgstr "Tilbakestill"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "Ansvarlig"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruker"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "Gjør prisendringer mulig kun for ledere"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
msgid "Restrict Product Categories"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Restrict price modification to managers"
msgstr "Gjør prisendringer mulig kun for ledere"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Retry"
msgstr "Prøv igjen"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "Returner produkter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "Returnert"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Kreditering av %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr "Forespørsel om reversering er sendt til terminalen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse"
msgstr "Reverser"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "Reverser betaling"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Review"
msgstr "Gjennomgå"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Rounding"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Rounding Method"
msgstr "Avrundingsmetode"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Leveringsfeil"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "SN"
msgstr "SN"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "Antall ordrelinjer"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "Ordrelinje"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "Salgsdetaljer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sales Journal"
msgstr "Salgsjournal"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Save"
msgstr "Lagre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Lagre denne siden og kom tilbake hit for å sette opp funksjonen."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scale"
msgstr "Vekt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan"
msgstr "Scan"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr "Skan EAN-13"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Scan via proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scanner"
msgstr "Skanner"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Search Customers"
msgstr "Søk i kunder"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Search Orders..."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidgetControlPanel.xml:0
#, python-format
msgid "Search Products..."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "Søk i salgsordre"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#, python-format
msgid "Select"
msgstr "Velg"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr "Velg avgiftsregel"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select an order"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Select either Cash In or Cash Out before confirming."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Select product attributes"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/HomeCategoryBreadcrumb.js:0
#, python-format
msgid "Select the category"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#, python-format
msgid "Select the pricelist"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__selectable_categ_ids
msgid "Selectable Categ"
msgstr "Valgbar kategori"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sell products and deliver them later."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send"
msgstr "Send"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send Payment Request"
msgstr "Send betalingsforespørsel"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Send by email"
msgstr "Send med e-post"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "Sekvensnummer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr "Serie-/partinummer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "Ekspeditør: "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Server Error"
msgstr "Serverfeil"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr "Økt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "Økt-ID"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "Bilag for økt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Session ids:"
msgstr "Økt-IDer:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Session is closed"
msgstr ""

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "Økt har vært open i over 7 dager"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "Økter"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Set Customer"
msgstr "Velg kunde"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
msgid "Set Maximum Difference"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Set Weight"
msgstr "Angi vekt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#, python-format
msgid "Set fiscal position"
msgstr "Angi avgiftsregel"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "Angi flere priser per produkt, automatiserte rabatter, osv."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set of coins/bills that will be used in opening and closing control"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set shop-specific prices, seasonal discounts, etc."
msgstr "Angi butikkspesifikke priser, sesongrabatter, osv."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Set the new quantity"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Settings"
msgstr "Innstillinger"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Ship Later"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
msgid "Shipping Policy"
msgstr "Fraktpolicy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Shopping cart"
msgstr "Handlevogn"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "Vis handlekurven til kunder med en tilkoblet skjerm."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Show customers checkout in a pop-up window. Can be moved to a second screen."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid ""
"Show customers checkout in a pop-up window. Recommend to be moved to a "
"second screen visible to the client."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
msgid "Show employee login screen"
msgstr "Vis skjerm for innlogging av ansatte"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr ""

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr "Størrelse"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#, python-format
msgid "Skip"
msgstr "Hopp over"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "Hopp over forhåndsvisning"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryBreadcrumb.xml:0
#, python-format
msgid "Slash"
msgstr "Slash"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.small_shelf
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "Liten hylle"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Some Cash Registers are already posted. Please reset them to new in order to close the session.\n"
"Cash Registers: %r"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Enkelte ordre kunne ikke sendes til serveren på grunn av feilkonfigurasjon. "
"Du kan gå ut av kassaløsningen, men ikke lukk økten før feilen er rettet."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Enkelte ordre kunne ikke sendes til serveren pga problemer med "
"internettilkoblingen. Du kan gå ut av kassaløsningen, men ikke lukk økten "
"før feilen er rettet."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Specific route"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr "Spesifiser parti-/serienummer for produkt i kassaordrelinje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
msgid "Spefic route for products delivered later."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
msgid "Start Category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "Startdato"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Start selling from a default product category"
msgstr "Begynn å selge fra en standard produktkategori"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "Inngående kontantbeholdning"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "State"
msgstr "Modus"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "Status"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basert på aktiviteter\n"
"Utgått: Fristen er allerede passert\n"
"I dag: Aktiviteten skal gjøres i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "Lagerbevegelse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "Regel lagring"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Street"
msgstr "Gate"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
#, python-format
msgid "Subtotal"
msgstr "Delsum"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "Subtotal eks mva"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "Subtotal u/rabatt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Successfully imported"
msgstr "Importert"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Sum of opening balance and transactions."
msgstr "Sum av inngående beholdning og transaksjoner."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "Sum av subtotaler"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr "Synkronisering tilkoblet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr "Synkronisering kobler til"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr "Synkronisering frakoblet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr "Synkroniseringsfeil"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "TOTAL"
msgstr "TOTAL"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_out
msgid "Take Money In/Out"
msgstr "Kassainnskudd/-uttak"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "Avgift"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "Avgiftsbeløp"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
msgid "Tax Display"
msgstr "Visning av avgifter"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Tax ID"
msgstr "Org.nr"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime
msgid "Tax Regime"
msgstr "Avgiftsregel"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "Avgiftsregel Valg Verdi"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "Pris eks mva"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "Pris inkl mva"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "Avgifter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "Avgifter å legge til"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Taxes:"
msgstr "Avgifter:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid ""
"Technical field which is used to hide use_payment_terminal when no payment "
"interfaces are installed."
msgstr ""
"Teknisk felt brukt til å gjemme use_payment_terminal når ingen "
"betalingsterminaler er installert."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "Tlf:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#, python-format
msgid ""
"The Point of Sale could not find any product, client, employee or action "
"associated with the scanned barcode."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount of your payment lines must be rounded to validate the "
"transaction."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr "Standard prisliste må være inkludert i de tilgjengelige prislistene."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The fiscal data module encountered an error while receiving your order."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""
"Domenet eller ip-adressen til IoT-boksen. Vil automatisk bli funnet hvis "
"utelatt."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Fakturajournalen må være i samme valuta som salgsjournalen, eventuelt "
"firmavaluta om salgsjournal ikke er satt."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal of the point of sale %s must belong to the same company."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "The maximum difference allowed is"
msgstr ""

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique !"
msgstr "Navnet på kassaøkten må være unikt!"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "Antall kassaordrer relatert til denne kunden"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr "Ordren kunne ikke sendes til server grunnet en ukjent feil"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr ""
"Ordren er tidligere synkronisert. Opprett faktura for ordren fra backend."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr "Den valgte betalingsmetoden er ikke tillatt i kassaøkten."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,help:point_of_sale.field_pos_session__config_id
msgid "The physical point of sale you will use."
msgstr "Det fysiske kassapunktet du vil bruke."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr ""
"Kassasystemet vil vise denne produktkategorien som standard. Om ingen "
"kategori er valgt, vil alle produkter bli vist."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"Kassaløsningen vil kun vise produkter som er i en av de valgte kategoriene "
"(og dens underkategorier). Hvis ingen kategori er spesifisert, vil alle "
"tilgjengelige produkter vises."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured."
msgstr ""
"Prislisten som blir brukt om ingen kunde er valgt, eller om kunden ikke er "
"tilknyttet en prisliste."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_display_categ_images
msgid "The product categories will be displayed with pictures."
msgstr "Produktkategoriene vil vises med bilder."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "Valutakurs ved ordredato"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""
"Kvitteringsskjermen vil hoppes over hvis kvitteringen kan skrives ut "
"automatisk."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "Kvittering vil skrives ut automatisk ved avslutning av ordre."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the refundable quantity"
" of %s."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The sales journal of the point of sale %s must belong to its company."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr ""
"Den valgte prislisten må tilhøre samme firma som kassapunktet, eventuelt "
"ikke tilhøre et firma."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "Serveren støtte på en feil under mottak av ordren."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr "Økten har vært åpen i uvanlig lang tid. Vurder å lukke den."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""
"Transaksjonene blir behandlet av Aydyen. Angi påloggingsinformasjon på "
"relatert betalingsmetode."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr ""
"Transaksjonene blir behandlet av Vantiv. Angi påloggingsinformasjon på "
"relatert betalingsmetode."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "Teoretisk beholdning ved avslutning"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "There are"
msgstr "Det er"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "There are no products in this category."
msgstr "Det finnes ingen produkter i denne kategorien."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"Det er fremdeles ordre med status kladd i denne økten. Betal eller avbryt følgende ordre for å validere økten:\n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr "Det pågår allerede en betaling."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is at least one pending electronic payment.\n"
"Please finish the payment with the terminal or cancel it then remove the payment line."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr ""
"Ingen kontoplan er konfigurert for firmaet. Gå til innstillingene for "
"fakturering, og velg en kontoplan."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please add a cash payment method in the point of sale configuration."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"Det finnes ingen kontant betalingsmetode for denne kassen, som kan behandle veksel.\n"
"\n"
"Betal eksakt beløp, eller legg til en kontant betalingsmetode i innstillingene for kassapunktet."

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_box.py:0
#, python-format
msgid "There is no cash register for this PoS Session"
msgstr "Det finnes ingen kontantuttalelse for denne kasseøkten"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"This account is used as intermediary account when nothing is set in a "
"payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"Dette feltet definerer maks tillatt differanse mellom teoretisk "
"kassabeholdning og opptalt kassabeholdning, for brukere som ikke er ledere. "
"Hvis tillatt differanse overskrides, vil brukeren få en feilmelding med "
"beskjed om å kontakte sin leder."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr "Dette feltet sender id-en til kassaleder-gruppen til kassaklienten."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr "Dette feltet sender id-en for kassabruker-gruppen til kassaklienten."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"This invoice has been created from the point of sale session: <a href=# "
"data-oe-model=pos.order data-oe-id=%d>%s</a>"
msgstr ""
"Faktura opprettet fra kassaøkt: <a href=# data-oe-model=pos.order data-oe-"
"id=%d>%s</a>"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__customer_note
msgid "This is a note destined to the customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "This is a technical field used for the domain of pricelist_id."
msgstr "Dette er et teknisk felt brukt for domenet på pricelist_id."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Dette er nyttig for å sette riktig mva for restauranter som tilbyr både spis"
" her og ta med."

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""
"Denne operasjonen vil slette alle ubetalte order i nettleseren. Du vil miste"
" all data som ikke er lagret, og kassen vil avsluttes. Denne operasjonen kan"
" ikke angres."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"Denne operasjonen vil permanent slette alle betalte ordrer fra lokal "
"lagring. Du vil miste all data. Denne operasjonen kan ikke angres."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#, python-format
msgid "This order is empty"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "This product is used as reference on customer receipts."
msgstr "Dette produktet brukes som referanse på kundekvitteringer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""
"Denne sekvensen er automatisk opprettet av Odoo, men du kan endre den for å "
"tilpasse referansenummer for ordrelinjer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"Denne sekvensen er automatisk opprettet av Odoo, men du kan endre den for å "
"tilpasse referansenummer for ordrer."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr "Denne økten er allerede lukket."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr ""
"Denne avgiften legges til på alle nye produkter som opprettes i katalogen."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Tip"
msgstr "Tips"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tip Product"
msgstr "Tipsprodukt"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_tip
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tips"
msgstr "Tips"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "Til lukking"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "To Pay"
msgstr "Å betale"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "To Refund: %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "Veies med vekt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "Å fakturere"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "Start en ny økt for å registrere nye ordre."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "For å returnere et produkt, må du åpne en kassaøkt i kassapunktet %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_ship
msgid "To ship"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "Total"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "Total kontanttrasaksjon"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Cost:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "Total rabatt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Total Due"
msgstr "Totalt utestående"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Margin:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "Totalt Betalt (avrundet)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "Totalt betalingsbeløp"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "Totalbeløp"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Price excl. VAT:"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Total Taxes"
msgstr "Total avgifter"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "Totalt betalingsbeløp"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total of all paid sales orders"
msgstr "Total for alle betalte ordrer"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Total of closing cash control lines."
msgstr "Kontantbeholdning ved avslutning."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Total of opening cash control lines."
msgstr "Kontantbeholdning ved åpning."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "Totalt ant"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "Transaksjon"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr "Transaksjon avbrutt"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "Overføring"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
msgid "Type"
msgstr "Type"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "Type kort brukt"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type unntaks-aktivitet på posten."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"Kan ikke lukke og validere økten.\n"
"Du må angi samsvarende avgiftskonto i hver fordelingslinje på følgende avgifter:\n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to download invoice."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to invoice order."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Unable to save changes."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "Enhetspris"

#. module: point_of_sale
#: model:product.product,uom_name:point_of_sale.desk_organizer
#: model:product.product,uom_name:point_of_sale.desk_pad
#: model:product.product,uom_name:point_of_sale.led_lamp
#: model:product.product,uom_name:point_of_sale.letter_tray
#: model:product.product,uom_name:point_of_sale.magnetic_board
#: model:product.product,uom_name:point_of_sale.monitor_stand
#: model:product.product,uom_name:point_of_sale.newspaper_rack
#: model:product.product,uom_name:point_of_sale.product_product_consumable
#: model:product.product,uom_name:point_of_sale.product_product_tip
#: model:product.product,uom_name:point_of_sale.small_shelf
#: model:product.product,uom_name:point_of_sale.wall_shelf
#: model:product.product,uom_name:point_of_sale.whiteboard
#: model:product.product,uom_name:point_of_sale.whiteboard_pen
#: model:product.template,uom_name:point_of_sale.desk_organizer_product_template
#: model:product.template,uom_name:point_of_sale.desk_pad_product_template
#: model:product.template,uom_name:point_of_sale.led_lamp_product_template
#: model:product.template,uom_name:point_of_sale.letter_tray_product_template
#: model:product.template,uom_name:point_of_sale.magnetic_board_product_template
#: model:product.template,uom_name:point_of_sale.monitor_stand_product_template
#: model:product.template,uom_name:point_of_sale.newspaper_rack_product_template
#: model:product.template,uom_name:point_of_sale.product_product_consumable_product_template
#: model:product.template,uom_name:point_of_sale.product_product_tip_product_template
#: model:product.template,uom_name:point_of_sale.small_shelf_product_template
#: model:product.template,uom_name:point_of_sale.wall_shelf_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_pen_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_product_template
msgid "Units"
msgstr "Stk"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#, python-format
msgid "Unknown Barcode"
msgstr "Ukjent strekkode"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr "Ukjent feil"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread
msgid "Unread Messages"
msgstr "Uleste meldinger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Antall uleste meldinger"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Unsupported File Format"
msgstr "Filformatet støttes ikke"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "Enhet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "Bruk betalingsterminal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use a default specific tax regime"
msgstr "Bruk en standard avgiftsregel"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
msgid "Use a pricelist."
msgstr "Bruk prisliste."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Use employee credentials to log in to the PoS session and switch cashier"
msgstr ""
"Bruk ansattes legitimasjon for å logge inn i kassaøkten, og bytte kasserer "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "Bruker"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "Navneskilt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "MVA:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Valid product lot"
msgstr "Gyldig produktparti"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Validate"
msgstr "Valider"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr "Vantiv (USA og Canada)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr "Vantiv betalingsterminal"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Waiting for card"
msgstr "Venter på kort"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.wall_shelf
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "Hylle"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Warehouse"
msgstr "Lager"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "Meldinger fra nettsted"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr " Kommunikasjonshistorikk for nettsted"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Weighing"
msgstr "Veiing"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Produkt til veiing"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "Når alle produkter er klare"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"Når du lukker en økt, vil det for alle ordrer som ikke er fakturert, "
"genereres ett bilag i følgende regnskapsjournal. Fakturaer bilagsføres hver "
"for seg."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "Whiteboard"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard_pen
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "Whiteboard-penn"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "With a"
msgstr "Med"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Would you like to load demo data?"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr "a"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"Du kan ikke endre innstillinger for avrunding mens en kassaøkt er åpen."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid ""
"You can't validate a bank statement that is used in an opened Session of a "
"Point of Sale."
msgstr "Du kan ikke validere en bankuttalelse som brukes i en åpen kassaøkt."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr "Du kan ikke opprette en økt før låsedatoen for regnskap."

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid "You cannot delete a bank statement linked to Point of Sale session."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/product.py:0
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid ""
"You cannot delete a product saleable in point of sale while a session is "
"still opened."
msgstr ""
"Du kan ikke slette et produkt som kan selges i kassasystemet, så lenge en "
"økt er åpen."

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_partner.py:0
#, python-format
msgid ""
"You cannot delete contacts while there are active PoS sessions. Close the "
"session(s) %s first."
msgstr ""
"Du kan ikke slette kontakter når det finnes åpne kassaøkter. Lukk økten(e) "
"%s først."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "You do not have any products"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You don't have the access rights to set the point of sale cash box."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You have enabled the \"Identify Customer\" option for %s payment method,but "
"the order %s does not contain a customer."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid ""
"You have to define which payment method must be available in the point of "
"sale by reusing existing bank and cash through \"Accounting / Configuration "
"/ Journals / Journals\". Select a journal and check the field \"PoS Payment "
"Method\" from the \"Point of Sale\" tab. You can also create new payment "
"methods directly from menu \"PoS Backend / Configuration / Payment "
"Methods\"."
msgstr ""
"Du må definere hvilke betalingsmetoder som skal være tilgjengelig i "
"kassasystemet, ved å bruke eksisterende bank eller kontantjournaler under "
"\"Regnskap / Konfigurasjon / Journaler / Journaler\". Velg en journal og huk"
" av feltet \"Bruk i Kassaløsning\". Du kan også opprette nye "
"betalingsmetoder direkte fra menyen \"Kasse / Konfigurasjon / "
"Betalingsmetoder\"."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You have to select a pricelist in the sale form !\n"
"Please set one before choosing a product."
msgstr ""
"Du må angi en prisliste i salgsskjemaet!\n"
"Angi en før du velger produkt."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You have to select a pricelist in the sale form."
msgstr "Du må velge en prisliste fra salgskjemaet."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"Du må definere produkter for alt du skal selge\n"
"med kassasystemet."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "Du må angi et kassapunkt for økten."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "ZIP"
msgstr "Postnummer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "at"
msgstr "á"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "available,"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "belong to another session:"
msgstr "hører til en annen økt:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "discount"
msgstr "rabatt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Company Address, Website"
msgstr "for eksempel firmaadresse, nettsted"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "for eks: Returpolicy, eller: Takk for at du handler hos oss!"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "f.eks mineralvann"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "for an order of"
msgstr "for en ordre på"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "forecasted"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "in"
msgstr "i"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#, python-format
msgid "items"
msgstr ""

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "notat"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "open sessions"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "orders"
msgstr "ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "out"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "paid orders"
msgstr "betalte ordrer"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr "retur"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders"
msgstr "ubetalte ordrer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr "kunne ikke importere ubetalte ordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_products_loading
msgid ""
"we load all starred products (favorite), all services, recent inventory movements of products, and the most recently updated products.\n"
"When the session is open, we keep on loading all remaining products in the background.\n"
"In the meantime, you can click on the 'database icon' in the searchbar to load products from database."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr "var duplikater av eksisterende ordrer"
