# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_payumoney
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr "Ručna konfiguracija"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_key
msgid "Merchant Key"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_salt
msgid "Merchant Salt"
msgstr ""

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:108
#, python-format
msgid "PayUmoney: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:102
#, python-format
msgid "PayUmoney: received data for reference %s; multiple orders found"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:99
#, python-format
msgid "PayUmoney: received data for reference %s; no order found"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:94
#, python-format
msgid ""
"PayUmoney: received data with missing reference (%s) or pay_id (%s) or "
"shashign (%s)"
msgstr ""

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Sticaoc plaćanja"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcija plaćanja"

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__provider
msgid "Provider"
msgstr "Provajder"

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr "Slipovi"

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_payumoney
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr "Žičani prenos"
