# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# Neun Pro, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Kat<PERSON>, 2023\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "%(id)s and %(length)s following"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s Dni"

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/xml/base_calendar.xml:0
#, python-format
msgid "&nbsp;Google"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "(No title)"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr "Aktivno"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"Administrator mora nastaviti Google sinhronizacijo preden jo lahko "
"uporabljate!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Google APIs plateform or try to stop and restart your calendar"
" synchronisation."
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Res želite brisati ta zapis?"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Informacije o udeležencih koledarja"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Calendar Event"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
msgid "Calendar ID"
msgstr "ID koledarja"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr "Prekliči"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "Client ID"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Odjemalčeva tajna šifra"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "Client_id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Client_key"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Configuration"
msgstr "Nastavitve"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr "Potrdi"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Confirmation"
msgstr "Potrditev"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/xml/google_calendar_popover.xml:0
#, python-format
msgid "Delete"
msgstr "Izbriši"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Email"
msgstr "E-pošta"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Pravilo o ponovitvi dogodka"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/xml/base_calendar.xml:0
#, python-format
msgid "Google"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_cal_account_id
msgid "Google Cal Account"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr "Google koledar"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_credentials
msgid "Google Calendar Account Data"
msgstr ""

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr "ID dogodka Google koledarja"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr ""

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
#: model:ir.cron,cron_name:google_calendar.ir_cron_sync_all_cals
#: model:ir.cron,name:google_calendar.ir_cron_sync_all_cals
msgid "Google Calendar: synchronization"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "Google gave the following explanation: %s"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__id
msgid "ID"
msgstr "ID"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Če je aktivno polje označeno, bo dovoljeno skriti alarm dogodka ne da bi ga "
"bilo treba brisati."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"Zadnji ID koledarja, ki se je sinhroniziral. Če se spremeni, odstranimo vse "
"povezave med Google ID in Notranjim Odoo Google ID"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset____last_update
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials____last_update
msgid "Last Modified on"
msgstr "Zadnjič spremenjeno"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
msgid "Next Sync Token"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Notification"
msgstr "Obvestilo"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Redirection"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
msgid "Refresh Token"
msgstr "Osvežitev žetona"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Success"
msgstr "Uspešno"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"Google sinhronizacija mora biti nastavljena preden jo lahko uporabljate. Jo "
"želite opraviti zdaj?"

#. module: google_calendar
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid "The account for the Google Calendar service is not configured."
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid ""
"The following event could not be synced with Google Calendar. </br>It will "
"not be synced as long at it is not updated.</br>%(reason)s"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "The synchronization with Google calendar was successfully stopped."
msgstr ""

#. module: google_calendar
#: model:ir.model.constraint,message:google_calendar.constraint_res_users_google_token_uniq
msgid "The user has already a google account"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
msgid "Token Validity"
msgstr "Veljavnost žetona"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__user_ids
msgid "User"
msgstr "Uporabnik"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
msgid "User token"
msgstr "Uporabniški žeton"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
msgid "Users"
msgstr "Uporabniki"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"You are about to stop the synchronization of your calendar with Google. Are "
"you sure you want to continue?"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "You will be redirected to Google to authorize access to your calendar!"
msgstr ""
"Preusmerjeni boste na Google, da boste pooblastili dostop do svojega "
"koledarja!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "undefined time"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid ""
"you don't seem to have permission to modify this event on Google Calendar"
msgstr ""
