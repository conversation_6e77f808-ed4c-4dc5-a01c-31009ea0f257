# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>ran <PERSON>hi <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Dung <PERSON><PERSON>hi <<EMAIL>>, 2021
# <PERSON><PERSON>, 2022
# <PERSON>ran <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Thi Huong Nguyen, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.action_report_purchase_order
msgid ""
"\n"
"                (object.state in ('draft', 'sent') and 'Request for Quotation - %s' % (object.name) or\n"
"                'Purchase Order - %s' % (object.name))"
msgstr ""
"\n"
"                (object.state in ('draft', 'sent') and 'Yêu cầu báo giá - %s' % (object.name) or\n"
"                'Đơn mua hàng - %s' % (object.name))"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__supplier_invoice_count
#: model:ir.model.fields,field_description:purchase.field_res_users__supplier_invoice_count
msgid "# Vendor Bills"
msgstr "# Hóa đơn nhà cung cấp"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__nbr_lines
msgid "# of Lines"
msgstr "# Số lượng dòng"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%(product)s from %(original_receipt_date)s to %(new_receipt_date)s"
msgstr "%(product)s từ %(original_receipt_date)s đến %(new_receipt_date)s"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%s confirmed the receipt will take place on %s."
msgstr "%s được xác nhận biên nhận sẽ diễn ra trên %s."

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%s modified receipt dates for the following products:"
msgstr "%s đã thay đổi ngày nhận các sản phẩm sau:"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.report_purchase_quotation
msgid "'Request for Quotation - %s' % (object.name)"
msgstr "'Yêu cầu báo giá - %s' % (object.name)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "-&gt;"
msgstr "-&gt;"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "3-way matching"
msgstr "Đối chiếu 3 bước"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_account_3way_match
msgid "3-way matching: purchases, receptions and bills"
msgstr "Đối chiếu 3 bước: Mua hàng, nhận hàng và hoá đơn nhà cung cấp"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_reminder
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is a reminder that the delivery of the purchase order <strong t-out=\"object.name or ''\">P00015</strong>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            <strong>(<t t-out=\"object.partner_ref or ''\">REF_XXX</t>)</strong>\n"
"        </t>\n"
"        is expected for \n"
"        <t t-if=\"object.date_planned\">\n"
"            <strong t-out=\"format_date(object.date_planned) or ''\">05/05/2021</strong>.\n"
"        </t>\n"
"         <t t-else=\"\">\n"
"            <strong>undefined</strong>.\n"
"        </t>\n"
"        Could you please confirm it will be delivered on time?\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a purchase order <strong t-out=\"object.name or ''\">P00015</strong>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        amounting in <strong t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</strong>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>. \n"
"        <br/><br/>\n"
"        <t t-if=\"object.date_planned\">\n"
"            The receipt is expected for <strong t-out=\"format_date(object.date_planned) or ''\">05/05/2021</strong>.\n"
"            <br/><br/>\n"
"            Could you please acknowledge the receipt of this order?\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a request for quotation <strong t-out=\"object.name or ''\">P00015</strong>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <br/><br/>\n"
"        If you have any questions, please do not hesitate to contact us.\n"
"        <br/><br/>\n"
"        Best regards,\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-comment\"/> Send message"
msgstr "<i class=\"fa fa-comment\"/> Gửi tin nhắn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Tải xuống"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/><span class=\"d-none d-md-inline\"> Done</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Hoàn"
" tất\"/><span class=\"d-none d-md-inline\"> Hoàn tất</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Paid</b>"
msgstr "<i class=\"fa fa-fw fa-check\"/> <b>Đã trả</b>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Waiting Payment</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>Đang chờ thanh toán</b>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-credit-card\" role=\"img\" aria-label=\"Purchases\" "
"title=\"Purchases\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-credit-card\" role=\"img\" aria-label=\"Purchases\" "
"title=\"Mua hàng\"/>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Waiting for Bill\"/><span class=\"d-none d-md-inline\"> "
"Waiting for Bill</span>"
msgstr ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Chờ hoá đơn\"/><span class=\"d-none d-md-inline\"> Chờ hoá "
"đơn</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Cancelled\" "
"title=\"Cancelled\"/><span class=\"d-none d-md-inline\"> Cancelled</span>"
msgstr ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Cancelled\" "
"title=\"Đã huỷ\"/><span class=\"d-none d-md-inline\"> Đã huỷ</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> In"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Confirmation Date</span>\n"
"                          <span class=\"d-block d-md-none\">Confirmation</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Ngày xác nhận</span>\n"
"                          <span class=\"d-block d-md-none\">Xác nhận</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Purchase Order #</span>\n"
"                          <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Đơn mua hàng #</span>\n"
"                          <span class=\"d-block d-md-none\">Mã</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid ""
"<span class=\"d-none d-md-inline\">Request for Quotation #</span>\n"
"                        <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Yêu cầu báo giá #</span>\n"
"                        <span class=\"d-block d-md-none\">Mã</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('state','not in',('draft','sent'))]}\">Request for Quotation </span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': [('state','in',('draft','sent'))]}\">Purchase Order </span>"
msgstr ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('state','not in',('draft','sent'))]}\">Yêu cầu báo giá </span>\n"
"<span class=\"o_form_label\" attrs=\"{'invisible': [('state','in',('draft','sent'))]}\">Đơn mua hàng </span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "<span class=\"o_stat_text\">Purchased</span>"
msgstr "<span class=\"o_stat_text\">Đã mua</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reception_confirmed','=', False)]}\">(confirmed by vendor)</span>"
msgstr ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reception_confirmed','=', False)]}\">(được xác nhận bởi nhà cung "
"cấp)</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reminder_confirmed', '=', False)]}\">(confirmed by vendor)</span>"
msgstr ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reminder_confirmed', '=', False)]}\">(được xác nhận bởi nhà cung "
"cấp)</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                              <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Số tiền</span>\n"
"                              <span groups=\"account.group_show_line_subtotals_tax_included\">Tổng giá</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_partner_property_form
msgid "<span> day(s) before</span>"
msgstr "<span> ngày trước</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "<span>Ask confirmation</span>"
msgstr "<span>Yêu cầu xác nhận</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Thuế</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">From:</strong>"
msgstr "<strong class=\"d-block mb-1\">Từ:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">Hoá đơn</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Thành tiền</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong class=\"text-muted\">Purchase Representative</strong>"
msgstr "<strong class=\"text-muted\">Đại diện mua hàng</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Amount</strong>"
msgstr "<strong>Số tiền</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Confirmation Date:</strong>"
msgstr "<strong>Ngày xác nhận:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Date Req.</strong>"
msgstr "<strong>Ngày YC.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr "<strong>Mô tả</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Expected Date</strong>"
msgstr "<strong>Ngày mong đợi</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Ngày đặt hàng:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Purchase Representative:</strong>"
msgstr "<strong>Đại diện mua hàng:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr "<strong>SL</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Receipt Date:</strong>"
msgstr "<strong>Ngày biên nhận:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Request For Quotation Date:</strong>"
msgstr "<strong>Ngày lập yêu cầu báo giá:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Địa chỉ giao hàng</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr "<strong>Thuế</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "<strong>The ordered quantity has been updated.</strong>"
msgstr "<strong>Số lượng đặt hàng đã cập nhật.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
msgid "<strong>The received quantity has been updated.</strong>"
msgstr "<strong>Số lượng nhận được đã được cập nhật.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This purchase has been canceled.</strong>"
msgstr "<strong>Mua hàng này đã bị huỷ.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Đơn giá</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Update Here</strong>"
msgstr "<strong>Cập nhật ở đây</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference:</strong>"
msgstr "<strong>Đơn hàng của bạn:</strong>"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "A sample email has been sent to %s."
msgstr "Một email mẫu đã được gửi đến %s."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Sản phẩm lưu kho là sản phẩm bạn muốn quản lý lượng tồn kho của nó. Hệ thống sẽ cần cài thêm phân hệ Kho.\n"
"Sản phẩm tiêu dùng là sản phẩm bạn không cần quản lý lượng tồn kho.\n"
"Sản phẩm dịch vụ là sản phẩm bạn cung cấp."

#. module: purchase
#: model:res.groups,name:purchase.group_warning_purchase
msgid "A warning can be set on a product or a customer (Purchase)"
msgstr "Có thể lập cảnh báo đối với Sản phẩm hoặc Đối tác (Mua)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Ability to select a package type in purchase orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Khả năng chọn loại kiện hàng trong đơn mua hàng và buộc số lượng là bội số "
"của số lượng hàng trên mỗi kiện."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_warning
msgid "Access warning"
msgstr "Cảnh báo truy cập"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Theo cấu hình sản phẩm, số lượng nhận được có thể được tính toán tự động theo cơ chế:\n"
"  - Thủ công: số lượng được điền tay theo dòng\n"
"  - Di chuyển kho: số lượng đến từ các phiếu kho đã xác nhận\n"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_accrued_expense_entry
msgid "Accrued Expense Entry"
msgstr "Nhập chi phí dồn tích"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction
msgid "Action Needed"
msgstr "Hành động cần thiết"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_ids
msgid "Activities"
msgstr "Các hoạt động"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Hành động ngoại lệ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng kiểu hoạt động"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a note"
msgstr "Thêm ghi chú"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a product"
msgstr "Thêm sản phẩm"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a section"
msgstr "Thêm khu vực"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Add several variants to the purchase order from a grid"
msgstr "Thêm một số biến thể cho đơn đặt hàng từ lưới"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Add some products or services to your quotation."
msgstr "Thêm một số sản phẩm hoặc dịch vụ vào bảng báo giá của bạn."

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Administrator"
msgstr "Người quản trị"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Tất cả"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "All Draft RFQs"
msgstr "Tất cả YCBG nháp"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "All Late RFQs"
msgstr "Tất cả YCBG trễ"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "All RFQs"
msgstr "Tất cả YCBG"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "All Waiting RFQs"
msgstr "Tất cả YCBG đang chờ"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__group_send_reminder
msgid "Allow automatically send email to remind your vendor the receipt date"
msgstr "Cho phép tự động gửi email để nhắc nhà cung cấp về ngày nhận hàng"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__edit
msgid "Allow to edit purchase orders"
msgstr "Cho phép sửa đơn mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__amount
msgid "Amount"
msgstr "Tổng tiền"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay_pass
msgid ""
"Amount of time between date planned and order by date for each purchase "
"order line."
msgstr ""
"Khoảng thời gian giữa ngày lập kế hoạch và đặt hàng theo ngày cho mỗi dòng "
"đơn đặt hàng."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__avg_days_to_purchase
msgid ""
"Amount of time between purchase approval and document creation date. Due to "
"a hack needed to calculate this,               every record will show the "
"same average value, therefore only use this as an aggregated value with "
"group_operator=avg"
msgstr ""
"Khoảng thời gian từ khi phê duyệt mua hàng đến ngày tạo tài liệu. Do cần "
"hack để tính toán điều này,               mọi bản ghi sẽ hiển thị cùng một "
"giá trị trung bình, do đó, chỉ sử dụng giá trị này làm giá trị tổng hợp với "
"group_operator=avg"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay
msgid "Amount of time between purchase approval and order by date."
msgstr ""
"Khoảng thời gian từ khi phê duyệt mua hàng đến khi đặt hàng theo ngày."

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_account
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__account_analytic_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__account_analytic_id
msgid "Analytic Account"
msgstr "Tài khoản phân tích"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Từ khóa khoản mục"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Approve Order"
msgstr "Phê duyệt đơn hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tập tin đính kèm"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Attributes"
msgstr "Thuộc tính"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Auto-Complete"
msgstr "Tự động hoàn thành"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_vendor_bill_id
#: model:ir.model.fields,field_description:purchase.field_account_payment__purchase_vendor_bill_id
msgid "Auto-complete"
msgstr "Tự hoàn tất"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_vendor_bill_id
#: model:ir.model.fields,help:purchase.field_account_payment__purchase_vendor_bill_id
msgid "Auto-complete from a past bill / purchase order."
msgstr "Tự động hoàn thành từ một Hóa đơn/Đơn mua hàng."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_id
#: model:ir.model.fields,help:purchase.field_account_payment__purchase_id
msgid "Auto-complete from a past purchase order."
msgstr "Tự động hoàn thành từ một Đơn mua hàng."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically lock confirmed orders to prevent editing"
msgstr "Tự động khóa các đơn đặt hàng đã xác nhận để ngăn chỉnh sửa"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically remind the receipt date to your vendors"
msgstr "Tự động nhắc ngày nhận hàng cho nhà cung cấp của bạn"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__receipt_reminder_email
#: model:ir.model.fields,help:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,help:purchase.field_res_users__receipt_reminder_email
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"Automatically send a confirmation email to the vendor X days before the "
"expected receipt date, asking him to confirm the exact date."
msgstr ""
"Tự động gửi email xác nhận cho nhà cung cấp X ngày trước ngày nhận hàng dự "
"kiến, yêu cầu họ xác nhận ngày chính xác."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_average
msgid "Average Cost"
msgstr "Giá trung bình"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__avg_days_to_purchase
msgid "Average Days to Purchase"
msgstr "Số ngày trung bình để mua hàng"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Avg Order Value ("
msgstr "Giá trị đơn TB ("

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "Best regards,"
msgstr "Trân trọng,"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__default_purchase_method
msgid "Bill Control"
msgstr "Kiểm soát thanh toán"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_count
msgid "Bill Count"
msgstr "Đếm hóa đơn"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__invoice_lines
msgid "Bill Lines"
msgstr "Chi tiết Hoá đơn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed"
msgstr "Đã có hoá đơn"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_invoiced
msgid "Billed Qty"
msgstr "SL đã xuất hoá đơn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed Quantity"
msgstr "Số lượng Hoá đơn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Billed Quantity:"
msgstr "Số lượng đã thanh toán:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_status
msgid "Billing Status"
msgstr "Tình trạng hoá đơn"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_ids
msgid "Bills"
msgstr "Hoá đơn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Bills Received"
msgstr "Hoá đơn đã nhận"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__block
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__block
msgid "Blocking Message"
msgstr "Khóa tin nhắn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_calendar
msgid "Calendar View"
msgstr "Giao diện lịch"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Calls for tenders are used when you want to generate requests for quotations"
" to several vendors for a given set of products. You can configure per "
"product if you directly do a Request for Quotation to one vendor or if you "
"want a Call for Tenders to compare offers from several vendors."
msgstr ""
"Các cuộc gọi đấu thầu được sử dụng khi bạn muốn tạo các yêu cầu trích dẫn "
"cho một số nhà cung cấp cho một tập hợp các sản phẩm nhất định. Bạn có thể "
"định cấu hình cho mỗi sản phẩm nếu bạn trực tiếp thực hiện Yêu cầu báo giá "
"cho một nhà cung cấp hoặc nếu bạn muốn một cuộc gọi cho người đấu giá để so "
"sánh phiếu mua hàng từ một số nhà cung cấp."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Cancel"
msgstr "Hủy"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__cancel
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__cancel
#, python-format
msgid "Cancelled"
msgstr "Đã hủy"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Cancelled Purchase Order #"
msgstr "Đơn mua hàng đã hủy #"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Cannot delete a purchase order line which is in state '%s'."
msgstr "Không thể xóa một chi tiết đơn hàng đang trong trạng thái '%s'."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_category_id
msgid "Category"
msgstr "Danh mục"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__commercial_partner_id
msgid "Commercial Entity"
msgstr "Đối tác thương mại"

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Công ty"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__company_currency_id
msgid "Company Currency"
msgstr "Loại tiền của công ty"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Compose Email"
msgstr "Soạn email"

#. module: purchase
#: model:ir.model,name:purchase.model_res_config_settings
msgid "Config Settings"
msgstr "Thiết lập cấu hình"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Cấu hình"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "Confirm"
msgstr "Xác nhận"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr "Xác nhận đơn hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Receipt Date"
msgstr "Ngày xác nhận nhận hàng"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__one_step
msgid "Confirm purchase orders in one step"
msgstr "Xác nhận đơn hàng bằng một bước"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Confirm your purchase."
msgstr "Xác nhận mua hàng của bạn."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date"
msgstr "Ngày xác nhận"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date Last Year"
msgstr "Ngày xác nhận năm trước"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__lock
msgid "Confirmed purchase orders are not editable"
msgstr "Đơn mua hàng đã xác nhận thì không cho phép sửa"

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Contact"
msgstr "Liên hệ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_method
msgid "Control Policy"
msgstr "Chính sách kiểm soát"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Việc chuyển đổi qua lại giữa các đơn vị đo lường chỉ có thể xảy ra nếu chúng"
" thuộc cùng một nhóm. Việc chuyển đổi sẽ dựa trên tỉ lệ."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Create Bill"
msgstr "Tạo hóa đơn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Create Bills"
msgstr "Tạo hóa đơn"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_batch_bills
msgid "Create Vendor Bills"
msgstr "Tạo hóa đơn nhà cung cấp"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid "Create a new product variant"
msgstr "Tạo biến thể sản phẩm mới"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_rate
msgid "Currency Rate"
msgstr "Tỷ giá"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__access_url
msgid "Customer Portal URL"
msgstr "Đường link cổng thông tin khách hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__date
msgid "Date"
msgstr "Ngày"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_calendar_start
msgid "Date Calendar Start"
msgstr ""
"More about this source textSource text required for additional translation "
"information"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Date Updated"
msgstr "Ngày được cập nhật"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Date:"
msgstr "Ngày:"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Days"
msgstr "Ngày"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_users__reminder_date_before_receipt
msgid "Days Before Receipt"
msgstr "Ngày trước khi nhận"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay
msgid "Days to Confirm"
msgstr "Ngày để xác nhận"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay_pass
msgid "Days to Receive"
msgstr "Ngày nhận"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Define your terms and conditions ..."
msgstr "Xác định điều khoản và điều kiện của bạn ..."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_planned
msgid "Delivery Date"
msgstr "Ngày Giao"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_planned
msgid ""
"Delivery date expected from vendor. This date respectively defaults to "
"vendor pricelist lead time then today's date."
msgstr ""
"Ngày giao hàng dự kiến từ nhà cung cấp. Ngày này tương ứng được mặc định cho"
" thời gian chờ trong bảng giá của nhà cung cấp rồi đến ngày hôm nay."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_planned
msgid ""
"Delivery date promised by vendor. This date is used to determine expected "
"arrival of products."
msgstr ""
"Ngày giao hàng do nhà cung cấp hứa hẹn. Ngày này được sử dụng để xác định "
"thời gian hàng về của sản phẩm."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__date_order
msgid ""
"Depicts the date when the Quotation should be validated and converted into a"
" purchase order."
msgstr ""
"Mô tả ngày mà Báo giá sẽ được xác thực và chuyển đổi thành đơn đặt hàng."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_order
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_order
msgid ""
"Depicts the date within which the Quotation should be confirmed and "
"converted into a purchase order."
msgstr ""
"Xác định ngày mà Báo giá cần được xác nhận và chuyển đổi thành đơn mua hàng."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__name
msgid "Description"
msgstr "Mô tả"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_report__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_type
msgid "Display Type"
msgstr "Loại hiển thị"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Documentation"
msgstr "Tài liệu"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__done
msgid "Done"
msgstr "Hoàn thành"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation_amount
msgid "Double validation amount"
msgstr "Giá trị thẩm định hai lần"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Download"
msgstr "Tải xuống"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__draft
msgid "Draft RFQ"
msgstr "Yêu cầu báo giá nháp"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Draft RFQs"
msgstr "YCBG nháp"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__dest_address_id
msgid "Dropship Address"
msgstr "Địa chỉ bán hàng trung gian"

#. module: purchase
#: model:ir.model,name:purchase.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Đồ thuật soạn thảo email"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Extended Filters"
msgstr "Bộ lọc mở rộng"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Extra line with %s "
msgstr "Dòng bổ sung với %s "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__fiscal_position_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__fiscal_position_id
msgid "Fiscal Position"
msgstr "Vị thế tài chính"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font biểu tượng ví dụ: fa-tasks"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable purchase order line"
msgstr "Các giá trị bị cấm trên dòng đơn đặt hàng không chịu trách nhiệm"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__invoiced
msgid "Fully Billed"
msgstr "Hoá đơn đầy đủ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Future Activities"
msgstr "Hoạt động tương lai"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__two_step
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr "Duyệt 2 lần để xác nhận đơn mua hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Get warnings in orders for products or vendors"
msgstr "Nhận cảnh báo trong đơn đặt hàng cho các sản phẩm hoặc nhà cung cấp"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__weight
msgid "Gross Weight"
msgstr "Tổng trọng lượng (cả bao bì)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr "Ẩn các dòng đã hủy"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "History"
msgstr "Lịch sử"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__id
msgid "ID"
msgstr "ID"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng để chỉ ra một hoạt động ngoại lệ."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction
#: model:ir.model.fields,help:purchase.field_purchase_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, các tin nhắn mới yêu cầu sự có mặt của bạn."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu đánh dấu thì một số thông điệp có lỗi."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If enabled, activates 3-way matching on vendor bills : the items must be "
"received in order to pay the invoice."
msgstr ""
"Nếu được bật, kích hoạt đối chiếu 3 bước trên hóa đơn của nhà cung cấp: các "
"mặt hàng phải được nhận để thanh toán hóa đơn."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If installed, the product variants will be added to purchase orders through "
"a grid entry."
msgstr ""
"Nếu được cài đặt, biến thể sản phẩm sẽ được thêm và Đơn mua hàng thông qua "
"một sổ lưới."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_packaging__purchase
msgid "If true, the packaging can be used for purchase orders"
msgstr "Nếu đúng, đóng gói có thể được sử dụng cho các đơn mua hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "If you have any questions, please do not hesitate to contact us."
msgstr "Nếu bạn có bất kỳ câu hỏi nào, xin vui lòng liên hệ với chúng tôi."

#. module: purchase
#: code:addons/purchase/models/product.py:0
#, python-format
msgid "Import Template for Products"
msgstr "Nhập sản phẩm gốc"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "In order to delete a purchase order, you must cancel it first."
msgstr "Để xóa một đơn hàng, bạn phải hủy nó trước."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "Điều khoản thương mại quốc tế"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Indicate the product quantity you want to order."
msgstr "Cho biết số lượng sản phẩm bạn muốn đặt hàng."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Bộ điều khoản thương mại Quốc tế (Incoterms) là một chuỗi các điều khoản "
"thương mại được định nghĩa trước để sử dụng trong các giao dịch quốc tế."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices and Incoming Shipments"
msgstr "Hoá đơn và Lô hàng sắp về"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Invoicing"
msgstr "Lên hóa đơn"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_is_follower
msgid "Is Follower"
msgstr "Trở thành người theo dõi"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move
msgid "Journal Entry"
msgstr "Bút toán sổ nhật ký"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move_line
msgid "Journal Item"
msgstr "Bút toán"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union____last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order____last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line____last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_report____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Late"
msgstr "Trễ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late Activities"
msgstr "Hoạt động trễ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late RFQs"
msgstr "YCBG trễ"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Lead Time to Purchase"
msgstr "Thời gian mua hàng để đảm bảo an toàn"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Let's create your first request for quotation."
msgstr "Hãy tạo yêu cầu báo giá đầu tiên của bạn."

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid ""
"Let's try the Purchase app to manage the flow from purchase to reception and"
" invoice control."
msgstr ""
"Hãy thử ứng dụng Mua hàng để quản lý quy trình từ mua hàng đến tiếp nhận và "
"kiểm soát hóa đơn."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation
msgid "Levels of Approvals"
msgstr "Các mức phê duyệt"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation
msgid "Levels of Approvals *"
msgstr "Các cấp độ Duyệt *"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Lock"
msgstr "Khoá"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__lock_confirmed_po
msgid "Lock Confirmed Orders"
msgstr "Khoá đơn hàng đã xác nhận"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__done
#, python-format
msgid "Locked"
msgstr "Đã khoá"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Tệp đính kèm chính"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Make sure you only pay bills for which you received the goods you ordered"
msgstr ""
"Đảm bảo bạn chỉ thanh toán hóa đơn theo những gì bạn đã nhận được khi bạn "
"đặt hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Manage your purchase agreements (call for tenders, blanket orders)"
msgstr "Quản lý các thỏa thuận mua hàng của bạn (gọi thầu, đơn đặt hàng)"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__qty_received_method__manual
msgid "Manual"
msgstr "Thủ công"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Manual Invoices"
msgstr "Hoá đơn thủ công"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_manual
msgid "Manual Received Qty"
msgstr "Số lượng đã nhận thủ công"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lead
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for procuring products, they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Biên độ sai lệch đối với thời gian mua hàng. Khi hệ thống tự tạo các đơn "
"hàng mua để cung ứng sản phẩm, nó sẽ ấn định thêm số ngày này vào thời gian "
"phải mua hàng để ngăn ngừa sự trễ hẹn không mong muốn từ phía nhà cung cấp."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__use_po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Biên độ lỗi cho thời gian chờ của nhà cung cấp. Khi hệ thống tạo đơn đặt "
"hàng để cung ứng lại các sản phẩm, chúng sẽ được lên lịch trước đó nhiều "
"ngày để đối phó với sự chậm trễ của nhà cung cấp không mong muốn."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error
msgid "Message Delivery error"
msgstr "Thông báo gửi đi gặp lỗi"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn_msg
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn_msg
msgid "Message for Purchase Order"
msgstr "Thông điệp cho đơn mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn_msg
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr "Thông điệp cho chi tiết trong đơn mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_ids
msgid "Messages"
msgstr "Thông báo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum Amount"
msgstr "Giá tối thiểu"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation_amount
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum amount for which a double validation is required"
msgstr "Giá trị tối thiểu để yêu cầu phải có thẩm định hai lần"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_accountable_required_fields
msgid "Missing required fields on accountable purchase order line."
msgstr "Thiếu các trường chịu trách nhiệm bắt buộc trên dòng đơn đặt hàng."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Hạn chót hoạt động"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "My Draft RFQs"
msgstr "YCBG nháp của tôi"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "My Late RFQs"
msgstr "YCBG bị trễ của tôi"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "My Orders"
msgstr "Đơn hàng của tôi"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "My Purchases"
msgstr "Mua hàng của tôi"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "My RFQs"
msgstr "YCBG của tôi"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "My Waiting RFQs"
msgstr "YCBG đang chờ của tôi"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "Tên"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Name, TIN, Email, or Reference"
msgstr "Tên, MST, Email, hoặc Mã"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Mới nhất"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện lịch hoạt động tiếp theo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Hạn chót cho hành động kế tiếp"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_id
msgid "Next Activity Type"
msgstr "Kiểu hoạt động kế tiếp"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__no-message
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__no-message
msgid "No Message"
msgstr "Không tin nhắn"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_action_dashboard_kanban
#: model_terms:ir.actions.act_window,help:purchase.purchase_action_dashboard_list
msgid "No RFQs to display"
msgstr "Không có YCBG để hiển thị"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid "No product found. Let's create one!"
msgstr "Không tìm thấy sản phẩm. Hãy tạo một cái!"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid "No purchase order found. Let's create one!"
msgstr "Không tìm thấy đơn mua hàng. Hãy tạo mới!"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "No request for quotation found. Let's create one!"
msgstr "Không tìm thấy yêu cầu báo giá nào. Hãy tạo mới!"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "No, Update Dates"
msgstr "Không, Cập nhật ngày"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__0
msgid "Normal"
msgstr "Thông thường"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Not Acknowledged"
msgstr "Không được công nhận"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Note"
msgstr "Ghi chú"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Ghi chú"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__no
msgid "Nothing to Bill"
msgstr "Không có gì để xuất hoá đơn"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng hành động"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__reminder_date_before_receipt
#: model:ir.model.fields,help:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,help:purchase.field_res_users__reminder_date_before_receipt
msgid "Number of days to send reminder email before the promised receipt date"
msgstr "Số ngày gửi email nhắc nhở trước ngày nhận đã thỏa thuận"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error_counter
msgid "Number of errors"
msgstr "Số lỗi"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Số thông báo cần xử lý"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số lượng tin gửi đi bị lỗi"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Số tin chưa đọc"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__purchase
msgid "On ordered quantities"
msgstr "Theo số lượng mua"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_method
#: model:ir.model.fields,help:purchase.field_product_template__purchase_method
msgid ""
"On ordered quantities: Control bills based on ordered quantities.\n"
"On received quantities: Control bills based on received quantities."
msgstr ""
"Theo số lượng mua: kiểm soát hóa dựa trên số lượng mua.\n"
"Theo số lượng nhận: kiểm soát hóa đơn dựa trên số lượng nhận (theo lô hàng nhận)."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__receive
msgid "On received quantities"
msgstr "Theo số lượng nhận"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid ""
"Once you get the price from the vendor, you can complete the purchase order "
"with the right price."
msgstr ""
"Sau khi nhận được giá từ nhà cung cấp, bạn có thể hoàn thành đơn hàng mua "
"với giá phù hợp."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Once you ordered your products to your supplier, confirm your request for "
"quotation and it will turn into a purchase order."
msgstr ""
"Sau khi bạn đặt hàng các sản phẩm của mình cho nhà cung cấp, hãy xác nhận "
"yêu cầu báo giá của bạn và nó sẽ chuyển thành đơn đặt hàng."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Order"
msgstr "Lệnh"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order Date"
msgstr "Ngày đặt hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_order
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Order Deadline"
msgstr "Hạn chốt đặt"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__order_line
msgid "Order Lines"
msgstr "Chi tiết đơn hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Reference"
msgstr "Mã đơn hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Ordered Quantity:"
msgstr "Số lượng đặt hàng:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__purchase
msgid "Ordered quantities"
msgstr "Số lượng đặt hàng"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Orders"
msgstr "Đơn hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Other Information"
msgstr "Thông tin khác"

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase_done
#: model:mail.template,report_name:purchase.email_template_edi_purchase_reminder
msgid "PO_{{ (object.name or '').replace('/','_') }}"
msgstr "PO_{{ (object.name or '').replace('/','_') }}"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_id
msgid "Packaging"
msgstr "Đóng gói"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Số lượng đóng gói"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__partner_id
msgid "Partner"
msgstr "Đối tác"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__country_id
msgid "Partner Country"
msgstr "Quốc gia của đối tác"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__payment_term_id
msgid "Payment Terms"
msgstr "Điều khoản thanh toán"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Payment terms"
msgstr "Các điều khoản thanh toán"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Please define an accounting purchase journal for the company %s (%s)."
msgstr "Vui lòng xác định 1 nhật kí mua hàng cho công ty %s (%s)."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_url
msgid "Portal Access URL"
msgstr "URL truy cập cổng thông tin"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Preview the reminder email by sending it to yourself."
msgstr "Xem trước email nhắc nhở bằng cách gửi cho chính bạn."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_category__property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase.field_product_template__property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "Tài khoản chênh lệch giá"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Pricing"
msgstr "Chi tiết giá"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Print"
msgstr "In"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr "In y/c báo giá"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__priority
msgid "Priority"
msgstr "Độ ưu tiên"

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
#: model:ir.model.fields,field_description:purchase.field_purchase_order__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product"
msgstr "Sản phẩm"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_attribute_action
msgid "Product Attributes"
msgstr "Thuộc tính sản phẩm"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_category_config_purchase
msgid "Product Categories"
msgstr "Nhóm sản phẩm"

#. module: purchase
#: model:ir.model,name:purchase.model_product_category
#: model:ir.model.fields,field_description:purchase.field_purchase_report__category_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product Category"
msgstr "Nhóm sản phẩm"

#. module: purchase
#: model:ir.model,name:purchase.model_product_packaging
msgid "Product Packaging"
msgstr "Đóng gói sản phẩm"

#. module: purchase
#: model:ir.model,name:purchase.model_product_template
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_tmpl_id
msgid "Product Template"
msgstr "Mẫu sản phẩm"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_type
msgid "Product Type"
msgstr "Loại sản phẩm"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_product_action
#: model:ir.ui.menu,name:purchase.product_product_menu
msgid "Product Variants"
msgstr "Biến thể Sản phẩm"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_products
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Products"
msgstr "Sản phẩm"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr "Cung cấp cơ chế thẩm định hai lần khi mua"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_packaging__purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_root
#: model:ir.ui.menu,name:purchase.purchase_report
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase"
msgstr "Mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_requisition
msgid "Purchase Agreements"
msgstr "Thoả thuận mua"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_order_report_all
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Analysis"
msgstr "Phân tích Mua sắm"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid ""
"Purchase Analysis allows you to easily check and analyse your company "
"purchase history and performance. From this menu you can track your "
"negotiation performance, the delivery performance of your vendors, etc."
msgstr ""
"Phân tích đơn mua hàng cho phép bạn dễ dàng kiểm tra và phân tích lịch sử và"
" hiệu suất mua sắm của công ty bạn. Từ mục này, bạn có thể truy xuất hiệu "
"suất đàm phán, hiệu suất bàn giao của các nhà cung cấp của bạn, v.v."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase Description"
msgstr "Mô tả mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_product_matrix
msgid "Purchase Grid Entry"
msgstr "Lưới biến thể mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lead
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lead
msgid "Purchase Lead Time"
msgstr "Thời gian mua hàng"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#: model:ir.actions.report,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_account_payment__purchase_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_activity
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
#, python-format
msgid "Purchase Order"
msgstr "Đơn mua hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order #"
msgstr "Đơn mua hàng #"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_order_approval
msgid "Purchase Order Approval"
msgstr "Đơn đã xác nhận"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_account__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_order_count
msgid "Purchase Order Count"
msgstr "Đếm đơn mua hàng"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_line_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Purchase Order Line"
msgstr "Chi tiết mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn
msgid "Purchase Order Line Warning"
msgstr "Cảnh báo trên đơn mua hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
msgid "Purchase Order Lines"
msgstr "Chi tiết mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lock
msgid "Purchase Order Modification"
msgstr "Điều chỉnh đơn mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lock
msgid "Purchase Order Modification *"
msgstr "Điều chỉnh đơn mua hàng *"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lock
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lock
msgid ""
"Purchase Order Modification used when you want to purchase order editable "
"after confirm"
msgstr ""
"Điều chỉnh đơn mua hàng được sử dụng khi bạn muốn sửa Đơn mua hàng sau khi "
"đã xác nhận"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_done
msgid "Purchase Order: Send PO"
msgstr ""

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase
msgid "Purchase Order: Send RFQ"
msgstr ""

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_reminder
msgid "Purchase Order: Vendor Reminder"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/analytic_account.py:0
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.account_analytic_account_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
#, python-format
msgid "Purchase Orders"
msgstr "Đơn mua hàng"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_report
msgid "Purchase Report"
msgstr "Báo cáo mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__user_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__user_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Representative"
msgstr "Đại diện mua hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_warning_purchase
msgid "Purchase Warnings"
msgstr "Cảnh báo mua hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that have been invoiced."
msgstr "Các đơn mua hàng mà đã có hoá đơn nhà cung cấp xuất."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that include lines not invoiced."
msgstr "Đơn hàng có chứa các hạng mục chưa lên hóa đơn."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase products by multiple of unit # per package"
msgstr "Mua sản phẩm theo bội số của số lượng hàng trên mỗi kiện"

#. module: purchase
#: model:ir.actions.server,name:purchase.purchase_send_reminder_mail_ir_actions_server
#: model:ir.cron,cron_name:purchase.purchase_send_reminder_mail
#: model:ir.cron,name:purchase.purchase_send_reminder_mail
msgid "Purchase reminder"
msgstr "Nhắc nhở mua hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase variants of a product using attributes (size, color, etc.)"
msgstr "Bán biến thể sản phẩm theo thuộc tính (kích cỡ, màu sắc, vv.)"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchased_product_qty
#: model:ir.model.fields,field_description:purchase.field_product_template__purchased_product_qty
msgid "Purchased"
msgstr "Đã mua"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Purchased Last 7 Days ("
msgstr "Đã mua trong 7 ngày qua ("

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "Purchased in the last 365 days"
msgstr "Đã mua trong 365 ngày gần đây"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Purchases"
msgstr "Mua hàng"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_bill_union
msgid "Purchases & Bills Union"
msgstr "Mua hàng & hóa đơn"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"Nhập một địa chỉ nếu bạn muốn giao hàng trực tiếp từ nhà cung cấp đến khách "
"hàng. Ngược lại, hãy để trống để giao hàng về công ty bạn."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_billed
msgid "Qty Billed"
msgstr "Số lượng có hoá đơn"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_ordered
msgid "Qty Ordered"
msgstr "SL Đặt hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_received
msgid "Qty Received"
msgstr "Số lượng đã nhận"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_to_be_billed
msgid "Qty to be Billed"
msgstr "Số lượng chờ hoá đơn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Quantities billed by vendors"
msgstr "Số lượng thanh toán theo nhà cung cấp"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Quantity"
msgstr "Số lượng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Quantity:"
msgstr "Số lượng:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__draft
msgid "RFQ"
msgstr "Y/c báo giá"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_approved
msgid "RFQ Approved"
msgstr "Y/c báo giá được duyệt"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_confirmed
msgid "RFQ Confirmed"
msgstr "Y/c báo giá được xác nhận"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_done
msgid "RFQ Done"
msgstr "Y/c báo giá Hoàn thành"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__sent
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__sent
msgid "RFQ Sent"
msgstr "Y/c báo giá đã được gửi"

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase
msgid "RFQ_{{ (object.name or '').replace('/','_') }}"
msgstr "RFQ_{{ (object.name or '').replace('/','_') }}"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "RFQs"
msgstr "Y/c Báo giá"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "RFQs Sent Last 7 Days"
msgstr "YCBG được gửi trong 7 ngày qua"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr "Y/c báo giá và Đơn hàng"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__currency_rate
msgid "Ratio between the purchase order currency and the company currency"
msgstr "Tỷ lệ tiền tệ của Đơn mua hàng và tiền tệ Công ty"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Send by Email"
msgstr "Gửi lại qua Email"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_planned
msgid "Receipt Date"
msgstr "Ngày nhận"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_send_reminder
#: model:ir.model.fields,field_description:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,field_description:purchase.field_res_users__receipt_reminder_email
msgid "Receipt Reminder"
msgstr "Nhắc nhở nhận hàng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__receipt_reminder_email
msgid "Receipt Reminder Email"
msgstr "Email Nhắc nhở nhận hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received"
msgstr "Đã nhận"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received
msgid "Received Qty"
msgstr "SL đã nhận"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "Cách thức số lượng đã nhận"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received Quantity"
msgstr "Số lượng đã nhận"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Received Quantity:"
msgstr "SL đã nhận:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__receive
msgid "Received quantities"
msgstr "Số lượng đã nhận"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reception_confirmed
msgid "Reception Confirmed"
msgstr "Đã xác nhận nhận hàng"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Record a new vendor bill"
msgstr "Tạo mới hóa đơn nhà cung cấp"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__name
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Reference"
msgstr "Mã phiếu"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_tree
msgid "Reference Document"
msgstr "Mã tài liệu"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_uom
msgid "Reference Unit of Measure"
msgstr "Mã đơn vị tính"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a"
" sales order)"
msgstr "Mã tài liệu đã tạo yêu cầu đơn đặt hàng này (ví dụ: đơn đặt hàng)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_ref
msgid ""
"Reference of the sales order or bid sent by the vendor. It's used to do the "
"matching when you receive the products as this reference is usually written "
"on the delivery order sent by your vendor."
msgstr ""
"Dẫn chiếu đến đơn bán hàng hoặc bản chào thầu gửi bởi nhà cung cấp. Nó được "
"sử dụng để thực hiện việc khớp lúc bạn nhận hàng khi dẫn chiếu này thường "
"được viết ở lệnh giao hàng gửi từ nhà cung cấp của bạn."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reminder_confirmed
msgid "Reminder Confirmed"
msgstr "Đã xác nhận lời nhắc"

#. module: purchase
#: model:ir.ui.menu,name:purchase.purchase_report_main
msgid "Reporting"
msgstr "Báo cáo"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: model:ir.actions.report,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
#, python-format
msgid "Request for Quotation"
msgstr "Yêu cầu báo giá"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr "Yêu cầu báo giá #"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Request managers to approve orders above a minimum amount"
msgstr "Yêu cầu quản lý phê duyệt Đơn hàng trên tổng tiền tối thiểu"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Requests For Quotation"
msgstr "Yêu cầu báo giá"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_rfq_form
#: model:ir.actions.act_window,name:purchase.purchase_action_dashboard_kanban
#: model:ir.actions.act_window,name:purchase.purchase_action_dashboard_list
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Requests for Quotation"
msgstr "Yêu cầu báo giá"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Requests for quotation are documents that will be sent to your suppliers to request prices for different products you consider buying.\n"
"                Once an agreement has been found with the supplier, they will be confirmed and turned into purchase orders."
msgstr ""
"Yêu cầu báo giá là tài liệu sẽ được gửi đến nhà cung cấp của bạn để yêu cầu giá cho các sản phẩm khác nhau mà bạn cân nhắc mua.\n"
"                Sau khi thỏa thuận được với nhà cung cấp, chúng sẽ được xác nhận và chuyển thành đơn mua hàng."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Scheduled Date"
msgstr "Ngày dự kiến"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr "Tìm kiếm đơn hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Search Reference Document"
msgstr "Tìm mã tài liệu"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Search a vendor name, or create one on the fly."
msgstr "Tìm kiếm tên nhà cung cấp hoặc tạo một nhà cung cấp nhanh chóng."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_section
msgid "Section"
msgstr "Mục"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Tên Mục (ví dụ: Sản phẩm, Dịch vụ)"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__use_po_lead
msgid "Security Lead Time for Purchase"
msgstr "Thời gian mua hàng để đảm bảo an toàn"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_token
msgid "Security Token"
msgstr "Mã bảo mật"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Select a product, or create a new one on the fly."
msgstr "Chọn một sản phẩm, hoặc tạo mới."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Select a purchase order or an old bill"
msgstr "Chọn đơn mua hàng hoặc một hóa đơn cũ"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_product_template__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,help:purchase.field_res_users__purchase_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Chọn \"Cảnh báo\" sẽ thông báo cho người qua một tin nhắn, Chọn \"Thông báo "
"chặn\" sẽ đưa ra một thông báo và khóa lại việc xử lý tiếp. Nội dung tin "
"nhắn thông báo bạn có thể điền ở trường kế tiếp."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr "Gửi đơn hàng qua email"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_send_reminder
msgid "Send Reminder"
msgstr "Gửi thông báo nhắc nhở"

#. module: purchase
#: model:res.groups,name:purchase.group_send_reminder
msgid "Send an automatic reminder email to confirm delivery"
msgstr "Gửi email nhắc nhở tự động để xác nhận giao hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send by Email"
msgstr "Gửi qua Email"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Send the request for quotation to your vendor."
msgstr "Gửi yêu cầu báo giá cho nhà cung cấp của bạn."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__sequence
msgid "Sequence"
msgstr "Quy tắc mã"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set to Draft"
msgstr "Thiết lập về nháp"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Thiết lập"

#. module: purchase
#: model:ir.actions.server,name:purchase.model_purchase_order_action_share
msgid "Share"
msgstr "Chia sẻ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Show all records which has next action date is before today"
msgstr "Hiển thị tất cả dữ liệu có ngày xử lý tiếp theo trước ngày hôm nay"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__reference
msgid "Source"
msgstr "Nguồn"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__origin
msgid "Source Document"
msgstr "Tài liệu gốc"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Starred"
msgstr "Được gắn sao"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__state
#: model:ir.model.fields,field_description:purchase.field_purchase_report__state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Trạng thái"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Ngày đến hạn phải được chuyển\n"
"Hôm nay: Hôm nay là ngày phải thực hiện\n"
"Kế hoạch: Các hoạt động trong tương lai."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_subtotal
msgid "Subtotal"
msgstr "Thành tiền"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Billed"
msgstr "Tổng của số lượng đã lập hoá đơn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Ordered"
msgstr "Tổng của số lượng đã đặt hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Received"
msgstr "Tổng của số lượng đã nhận"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Total"
msgstr "Tổng cộng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "Tổng cộng tổng chưa thuế"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_users__property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Tiền tệ nhà cung cấp"

#. module: purchase
#: model:ir.model,name:purchase.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Bảng giá nhà cung cấp"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_tax
msgid "Tax"
msgstr "Thuế"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_country_id
msgid "Tax Country"
msgstr "Quốc gia tính thuế"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_totals_json
msgid "Tax Totals Json"
msgstr "Tổng số thuế Json"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__taxes_id
msgid "Taxes"
msgstr "Thuế"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "Trường kỹ thuật cho mục đích UX."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__tax_country_id
msgid ""
"Technical field to filter the available taxes depending on the fiscal "
"country and fiscal position."
msgstr ""
"Trường kỹ thuật để lọc các loại thuế khả dụng dựa theo quốc gia tài chính và"
" vị trí tài chính. "

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Điều khoản &amp; Điều kiện"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__notes
msgid "Terms and Conditions"
msgstr "Điều khoản và Điều kiện"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "The order receipt has been acknowledged by %s."
msgstr "Biên nhận đơn hàng đã được xác nhận bởi %s."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid ""
"The request for quotation is the first step of the purchases flow. Once\n"
"                    converted into a purchase order, you will be able to control the receipt\n"
"                    of the products and the vendor bill."
msgstr ""
"Yêu cầu báo giá / Đơn mua hàng nháp là bước đầu tiên trong quy trình mua hàng. Khi\n"
"                    được chuyển thành đơn mua hàng, bạn có thể dựa vào nó để kiểm soát\n"
"                    các sản phẩm nhận được và hóa đơn từ nhà cung cấp."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid "There are currently no purchase orders for your account."
msgstr "Tài khoản của bạn hiện không có đơn mua hàng nào."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "There are currently no requests for quotation for your account."
msgstr "Tài khoản của bạn hiện không có yêu cầu báo giá nào."

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"There is no invoiceable line. If a product has a control policy based on "
"received quantity, please make sure that a quantity has been received."
msgstr ""
"Không có dòng có thể lập hóa đơn. Nếu một sản phẩm có chính sách kiểm soát "
"dựa trên số lượng đã nhận, vui lòng đảm bảo rằng số lượng đã được nhận."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase.field_product_template__property_account_creditor_price_difference
msgid ""
"This account is used in automated inventory valuation to record the price "
"difference between a purchase order and its related vendor bill when "
"validating this vendor bill."
msgstr ""
"Tài khoản này được dùng trong quá trình tính giá trị tồn kho tự động để ghi "
"nhận chênh lệch giá giữa việc mua hàng và hóa đơn nhà cung cấp khi bạn xác "
"nhận hóa đơn."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_category__property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr ""
"Tài khoản này được sử dụng để đánh giá sự chênh lệch giá giữa giá mua và giá"
" vốn theo kế toán."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,help:purchase.field_res_users__property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""
"Tiền tệ này sẽ được sử dụng để mua hàng từ nhà cung cấp hiện tại thay vì "
"tiền tệ mặc định"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__default_purchase_method
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Giá trị mặc định này được áp dụng cho bất kỳ sản phẩm mới nào được tạo ra. "
"Điều này có thể thay đổi trong sản phẩm mẫu."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "This note is added to purchase orders."
msgstr "Ghi chú này đã được thêm vào Đơn mua hàng."

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should "
"purchase %(quantity).2f %(unit)s."
msgstr ""
"Sản phẩm này được đóng gói bằng %(pack_size).2f %(pack_name)s. Bạn nên mua "
"%(quantity).2f %(unit)s."

#. module: purchase
#: code:addons/purchase/models/account_invoice.py:0
#, python-format
msgid "This vendor bill has been created from: %s"
msgstr "Hoá đơn nhà cung cấp này đã được tạo từ: %s"

#. module: purchase
#: code:addons/purchase/models/account_invoice.py:0
#, python-format
msgid "This vendor bill has been modified from: %s"
msgstr "Hoá đơn nhà cung cấp này đã được điều chỉnh từ: %s"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Create a new RfQ"
msgstr ""
"Nhà cung cấp này chưa có đơn mua hàng nào. Tạo một YCBG/đơn mua hàng nháp "
"mới"

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_0
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid "Tip: How to keep late receipts under control?"
msgstr "Lời khuyên: Cách kiểm soát phiếu nhập hàng trễ hạn?"

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_1
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid "Tip: Never miss a purchase order"
msgstr "Lời khuyên: Không bao giờ bỏ lỡ đơn mua hàng"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__to_approve
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__to_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "To Approve"
msgstr "Chờ Duyệt"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "Số lượng cần xuất hóa đơn"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "To Send"
msgstr "Chờ gửi"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Today Activities"
msgstr "Hoạt động hôm nay"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_total
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_total
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
#, python-format
msgid "Total"
msgstr "Tổng"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_qty
msgid "Total Quantity"
msgstr "Tổng số lượng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total Untaxed amount"
msgstr "Tổng trước thuế"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total amount"
msgstr "Tổng số tiền"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reception_confirmed
msgid "True if PO reception is confirmed by the vendor."
msgstr "Đúng nếu việc tiếp nhận PO được xác nhận bởi nhà cung cấp."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reminder_confirmed
msgid "True if the reminder email is confirmed by the vendor."
msgstr "Đúng nếu email nhắc nhở được xác nhận bởi nhà cung cấp."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Loại hoạt động ngoại lệ trên hồ sơ."

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"Unable to cancel this purchase order. You must first cancel the related "
"vendor bills."
msgstr ""
"Không thể hủy đơn mua hàng này. Bạn phải hủy hóa đơn liên quan của nó trước."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Unit Price"
msgstr "Đơn giá"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unit Price:"
msgstr "Đơn Giá:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom
msgid "Unit of Measure"
msgstr "Đơn vị tính"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_form_action
msgid "Units of Measure"
msgstr "Đơn vị tính"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Danh mục đơn vị"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_unit_of_measure_in_config_purchase
msgid "Units of Measures"
msgstr "Đơn vị tính"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unlock"
msgstr "Mở khoá"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_unread
msgid "Unread Messages"
msgstr "Tin chưa đọc"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Bộ đếm tin chưa đọc"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Untaxed"
msgstr "Trước thuế"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Tổng chưa thuế"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__untaxed_total
msgid "Untaxed Total"
msgstr "Giá trị trước thuế"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "UoM"
msgstr "Đơn vị"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__1
msgid "Urgent"
msgstr "Khẩn cấp"

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Người dùng"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__company_currency_id
msgid "Utility field to express amount currency"
msgstr "Trường ghi nhận số tiền theo loại tiền tệ"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Variant Grid Entry"
msgstr "Lưới biến thể"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr "Nhà cung cấp"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__vendor_bill_id
msgid "Vendor Bill"
msgstr "Hóa đơn Nhà cung cấp"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_supplier_invoices
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_account_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Vendor Bills"
msgstr "Hoá đơn Nhà cung cấp"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor Country"
msgstr "Quốc gia nhà cung cấp"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Vendor Pricelists"
msgstr "Bảng giá nhà cung cấp"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_ref
msgid "Vendor Reference"
msgstr "Mã nhà cung cấp"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management_supplier_name
msgid "Vendors"
msgstr "Nhà cung cấp"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid ""
"Vendors bills can be pre-generated based on purchase\n"
"                    orders or receipts. This allows you to control bills\n"
"                    you receive from your vendor according to the draft\n"
"                    document in Odoo."
msgstr ""
"Hoá đơn nhà cung cấp có thể được tạo trước dựa trên đơn mua hàng\n"
"                    hay phiếu mua hàng. Điều này cho phép bạn kiểm soát hoá\n"
"                    bạn nhận được từ nhà cung cấp."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__volume
msgid "Volume"
msgstr "Thể tích"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Waiting"
msgstr "Chờ"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__to_invoice
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Waiting Bills"
msgstr "Chờ hoá đơn"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Waiting RFQs"
msgstr "YCBG đang chờ"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__warning
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__warning
#, python-format
msgid "Warning"
msgstr "Cảnh báo"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Warning for %s"
msgstr "Cảnh báo cho %s"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Warning on the Purchase Order"
msgstr "Cảnh báo trên đơn mua hàng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Warning when Purchasing this Product"
msgstr "Cảnh báo khi mua sản phẩm này"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Warnings"
msgstr "Các cảnh báo"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__website_message_ids
msgid "Website Messages"
msgstr "Thông báo Website"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử thông tin liên lạc website"

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid ""
"When creating a purchase order, have a look at the vendor's <i>On Time "
"Delivery</i> rate: the percentage of products shipped on time. If it is too "
"low, activate the <i>automated reminders</i>. A few days before the due "
"shipment, Odoo will send the vendor an email to ask confirmation of shipment"
" dates and keep you informed in case of any delays. To get the vendor's "
"performance statistics, click on the OTD rate."
msgstr ""
"Khi tạo đơn đặt hàng, hãy xem thông số <i>Giao hàng đúng hạn</i> của nhà "
"cung cấp: tỷ lệ sản phẩm được vận chuyển đúng giờ. Nếu nó quá thấp, hãy kích"
" hoạt <i>lời nhắc tự động</i>. Một vài ngày trước khi đến hạn giao hàng, hệ "
"thống sẽ gửi cho nhà cung cấp một email để yêu cầu xác nhận ngày giao hàng "
"và thông báo cho bạn trong trường hợp có bất kỳ sự chậm trễ nào. Để nhận "
"thống kê hiệu suất của nhà cung cấp, hãy nhấp vào tỷ lệ OTD."

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid ""
"When sending a purchase order by email, Odoo asks the vendor to acknowledge "
"the reception of the order. When the vendor acknowledges the order by "
"clicking on a button in the email, the information is added on the purchase "
"order. Use filters to track orders that have not been acknowledged."
msgstr ""
"Khi gửi đơn đặt hàng qua email, BizApps yêu cầu nhà cung cấp xác nhận đã "
"nhận đơn đặt hàng. Khi nhà cung cấp xác nhận đơn đặt hàng bằng cách nhấp vào"
" một nút trong email, thông tin sẽ được thêm vào đơn đặt hàng. Sử dụng bộ "
"lọc để theo dõi các đơn đặt hàng chưa được xác nhận."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "Yes"
msgstr "Có"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,help:purchase.field_purchase_order_line__partner_id
msgid "You can find a vendor by its Name, TIN, Email or Internal Reference."
msgstr "Bạn có thể tìm nhà cung cấp theo Tên, MST, Email hoặc Mã nội bộ."

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"You cannot change the type of a purchase order line. Instead you should "
"delete the current line and create a new line of the proper type."
msgstr ""
"Bạn không thể thay đổi loại của một dòng mua\n"
"\n"
" hàng. Thay vào đó, bạn nên xóa dòng hiện tại và tạo một dòng mới có kiểu thích hợp."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Bạn nên chỉ định một sản phẩm mỗi khi muốn bán hoặc mua hàng.\n"
"khi nó là một sản phẩm tồn kho, một sản phẩm tiêu thụ hoặc một dịch vụ."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"You must define a product for everything you sell or purchase,\n"
"            whether it's a storable product, a consumable or a service."
msgstr ""
"Bạn nên chỉ định một sản phẩm mỗi khi muốn bán hoặc mua hàng.\n"
"khi nó là một sản phẩm tồn kho, một sản phẩm tiêu thụ hoặc một dịch vụ."

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Báo giá của bạn chứa các sản phẩm từ công ty %(product_company)s trong khi báo giá của bạn thuộc về công ty %(quote_company)s. \n"
" Vui lòng thay đổi công ty trong báo giá của bạn hoặc xóa sản phẩm từ các công ty khác (%(bad_products)s)."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "close"
msgstr "đóng"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "day(s) before"
msgstr "ngày trước"

#. module: purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase_done
#: model:mail.template,subject:purchase.email_template_edi_purchase_reminder
msgid "{{ object.company_id.name }} Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} Đơn hàng (Mã {{ object.name or 'không có' }})"
