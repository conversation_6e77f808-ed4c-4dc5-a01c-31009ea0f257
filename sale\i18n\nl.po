# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-10 09:48+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid " / Month"
msgstr "/ Maand"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Gegevens opgehaald"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sale_order_count
msgid "# Sale Orders"
msgstr "# Verkooporders"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "# Regels"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr "# Verkooporders"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr "'PRO-FORMA - %s' % (object.name)"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Offerte - %s' % (object.name)) or "
"'Order - %s' % (object.name)"

#. module: sale
#: model:product.product,description_sale:sale.product_product_4e
#: model:product.product,description_sale:sale.product_product_4f
msgid "160x80cm, with large legs."
msgstr "160x80cm, met grote poten"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"<b>Send the quote</b> to yourself and check what the customer will receive."
msgstr "<b>Stuur de offerte</b> naar jezelf en kijk wat de klant krijgt."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "<b>Set a price</b>."
msgstr "<b>Stel een prijs in</b>."

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br/><br/>\n"
"        <t t-set=\"transaction\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Your order <strong t-out=\"object.name or ''\">S00049</strong> amounting in <strong t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</strong>\n"
"        <t t-if=\"object.state == 'sale' or (transaction and transaction.state in ('done', 'authorized'))\">\n"
"            has been confirmed.<br/>\n"
"            Thank you for your trust!\n"
"        </t>\n"
"        <t t-elif=\"transaction and transaction.state == 'pending'\">\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            <t t-if=\"object.reference\">\n"
"                Your payment reference is <strong t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/><br/>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><strong>Products</strong></td>\n"
"                <td/>\n"
"                <td width=\"15%\" align=\"center\"><strong>Quantity</strong></td>\n"
"                <td width=\"20%\" align=\"right\"><strong>\n"
"                <t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\">\n"
"                    VAT Excl.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    VAT Incl.\n"
"                </t>\n"
"                </strong></td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and line.display_type in ['line_section', 'line_note']\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section'\">\n"
"                                <strong t-out=\"line.name or ''\">Taking care of Trees Course</strong>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><strong>\n"
"                        <t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </strong></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Delivery:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>SubTotal:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>SubTotal:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>Taxes:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Total:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <strong>Bill to:</strong>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Payment Method:</strong>\n"
"                    <t t-if=\"transaction.token_id\">\n"
"                        <t t-out=\"transaction.token_id.name or ''\"/>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"transaction.acquirer_id.sudo().name or ''\"/>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(transaction.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <strong>Ship to:</strong>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Shipping Method:</strong>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                    <t t-if=\"object.carrier_id.fixed_price == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.carrier_id.fixed_price, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hallo,\n"
"        <br/><br/>\n"
"        <t t-set=\"transaction\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Je bestelling  <strong t-out=\"object.name or ''\">S00049</strong> voor het bedrag van <strong t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</strong>\n"
"        <t t-if=\"object.state == 'sale' or (transaction and transaction.state in ('done', 'authorized'))\">\n"
"            is bevestigd.<br/>\n"
"            Bedankt voor je vertrouwen!\n"
"        </t>\n"
"        <t t-elif=\"transaction and transaction.state == 'pending'\">\n"
"            is in behandeling. Deze wordt bevestigd wanneer de betaling is ontvangen.\n"
"            <t t-if=\"object.reference\">\n"
"                Je betalingskenmerk is: <strong t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Aarzel niet om contact met ons op te nemen als je vragen hebt.\n"
"        <br/><br/>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><strong>Producten</strong></td>\n"
"                <td/>\n"
"                <td width=\"15%\" align=\"center\"><strong>Aantal</strong></td>\n"
"                <td width=\"20%\" align=\"right\"><strong>\n"
"                <t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\">\n"
"                    Excl. Btw\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Incl. Btw\n"
"                </t>\n"
"                </strong></td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and line.display_type in ['line_section', 'line_note']\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section'\">\n"
"                                <strong t-out=\"line.name or ''\">Taking care of Trees Course</strong>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><strong>\n"
"                        <t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </strong></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Levering:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>Subtotaal:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Subtotaal:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>Btw:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Totaal:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <strong>Factuur naar:</strong>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Betaalmethode:</strong>\n"
"                    <t t-if=\"transaction.token_id\">\n"
"                        <t t-out=\"transaction.token_id.name or ''\"/>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"transaction.acquirer_id.sudo().name or ''\"/>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(transaction.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <strong>Verzend naar:</strong>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Verzendwijze:</strong>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                    <t t-if=\"object.carrier_id.fixed_price == 0.0\">\n"
"                        (Gratis)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.carrier_id.fixed_price, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        Your\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            amounting in <strong t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 10.00</strong> is available.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\"/>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            amounting in <strong t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 10.00</strong> is ready for review.\n"
"        </t>\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'offerte' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Hallo,\n"
"        <br/><br/>\n"
"        Je\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Proforma-factuur voor <t t-out=\"doc_name or ''\">offerte</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"            <t t-if=\"object.origin\">\n"
"                (met referentie: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            voor het bedrag van <strong t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 10.00</strong> is beschikbaar.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">offerte</t> <strong t-out=\"object.name or ''\"/>\n"
"            <t t-if=\"object.origin\">\n"
"                (met referentie: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            voor het bedrag van <strong t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 10.00</strong> is klaar voor beoordeling.\n"
"        </t>\n"
"        <br/><br/>\n"
"        Aarzel niet om contact met ons op te nemen als je vragen hebt.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr ""
"<i class=\"fa fa-comment\"/> Neem contact met ons op voor een nieuwe "
"offerte."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> Feedback"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Send message"
msgstr "<i class=\"fa fa-comment\"/> Verstuur bericht"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Download"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Done\"/>Done"
msgstr ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Gereed\" "
"title=\"Gereed\"/>Gereed"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Paid</b>"
msgstr "<i class=\"fa fa-fw fa-check\"/> <b>Betaald</b>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Waiting Payment</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>Wachtend op betaling</b>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/>Vervallen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-remove\"/>Geannuleerd"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Verkooporders\" "
"title=\"Verkooporders\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Afdrukken"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> Weiger"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">This offer expires on</b></small>"
msgstr "<small><b class=\"text-muted\">Deze offerte vervalt op</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr "<small><b class=\"text-muted\">Jouw voordeel</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span attrs=\"{'invisible': [('advance_payment_method', '!=', "
"'percentage')]}\" class=\"oe_inline\">%</span>"
msgstr ""
"<span attrs=\"{'invisible': [('advance_payment_method', '!=', "
"'percentage')]}\" class=\"oe_inline\">%</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Verkooporder #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Waardes die hier ingesteld "
"staan zijn bedrijfsspecifiek.\" aria-label=\"Waardes die hier ingesteld "
"staan zijn bedrijfsspecifiek.\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"De waarden die hier worden "
"ingesteld, zijn bedrijfsspecifiek.\" groups=\"base.group_multi_company\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Confirmation Email</span>"
msgstr "<span class=\"o_form_label\">Bevestiging e-mail</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Down Payments</span>"
msgstr "<span class=\"o_form_label\">Aanbetalingen</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<span class=\"o_stat_text\">Customer</span>\n"
"                                <span class=\"o_stat_text\">Preview</span>"
msgstr ""
"<span class=\"o_stat_text\">Klant</span>\n"
"                                <span class=\"o_stat_text\">voorbeeld</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Verkocht</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "<span class=\"oe_read_only\">/ Month</span>"
msgstr "<span class=\"oe_read_only\">/ Maand</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Bedrag</span>\n"
"                                <span groups=\"account.group_show_line_subtotals_tax_included\">Totale prijs</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                            <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Bedrag</span>\n"
"                            <span groups=\"account.group_show_line_subtotals_tax_included\">Totale prijs</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>Accepted on the behalf of:</span>"
msgstr "<span>Geacccepteerd in opdracht van:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By paying this proposal, I agree to the following terms:</span>"
msgstr ""
"<span>Door deze offerte te betalen, ga je akkoord met de volgende "
"voorwaarden:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By signing this proposal, I agree to the following terms:</span>"
msgstr ""
"<span>Door deze offerte te ondertekenen, ga je akkoord met de volgende "
"voorwaarden:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr "<span>Kort.%</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>For an amount of:</span>"
msgstr "<span>Voor het bedrag van:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<span>Pro-Forma Invoice # </span>"
msgstr "<span>Pro-Forma factuur # </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>BTW </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>With payment terms:</span>"
msgstr "<span>Met betalingscondities:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">Facturen</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Shipping Address:</strong>"
msgstr "<strong class=\"d-block mb-1\">Afleveradres:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Subtotaal</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong class=\"text-muted\">Salesperson</strong>"
msgstr "<strong class=\"text-muted\">Verkoper</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Expiration Date:</strong>"
msgstr "<strong>Vervaldatum:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration:</strong>"
msgstr "<strong>Vervaldatum:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Opmerking voor fiscale positie:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                        If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Geen geschikte betalingsoptie gevonden.</strong><br/>\n"
"                                        Als je denkt dat het een fout is, neem dan contact op met de beheerder van de website."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Order datum:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Quotation Date:</strong>"
msgstr "<strong>Offertedatum:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Verkoper:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_document_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>Afleveradres:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Handtekening</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr "<strong>Dank je!</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr "<strong>Deze offerte is vervallen!</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been canceled.</strong>"
msgstr "<strong>Deze offerte is geannuleerd.</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr "<strong>Klantreferentie:</strong>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr "Een bevestigde verkooporder vereist een bevestigingsdatum"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid ""
"A standard invoice is issued with all the order lines ready for invoicing,"
"         according to their invoicing policy (based on ordered or delivered "
"quantity)."
msgstr ""
"Een standaard factuur wordt aangemaakt voor alle orderregels die gereed zijn"
" voor facturatie, gebaseerd op het facturatiebeleid (gebaseerd op bestelde "
"en geleverde hoeveelheid)."

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "Een waarschuwing kan worden ingesteld op een klant (verkoop)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Mogelijkheid om een verpakkingstype in verkooporders te selecteren en een "
"hoeveelheid te forceren die een veelvoud is van het aantal eenheden per "
"verpakking."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr "Accepteer &amp; Betaal"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr "Accepteer &amp; Teken"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr "Toegang waarschuwing"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Afhankelijk van de productinstellingen, kan de geleverde hoeveelheid automatisch worden berekend met behulp van het mechanisme:\n"
"- Handmatig: het aantal wordt handmatig op de regel ingesteld\n"
"- Kostenplaats van declaratie: de hoeveelheid is de som van de geboekte declaraties\n"
"- Urenstaat: de hoeveelheid is de som van de uren die zijn vastgelegd voor taken die aan deze verkoopregel zijn gekoppeld\n"
"- Voorraadverplaatsingen: de hoeveelheid komt van bevestigde pickings\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Rekeningnummer"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Account used for deposits"
msgstr "Rekening gebruikt voor aanbetalingen"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_accrued_revenue_entry
msgid "Accrued Revenue Entry"
msgstr "Toegerekende omzetboeking"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
msgid "Activities"
msgstr "Activiteiten"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activiteit uitzondering decoratie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "Activiteitsfase"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Activiteitensoort icoon"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "Activiteitssoorten"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr "Voeg een notitie toe"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr "Product toevoegen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr "Sectie toevoegen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr "Voeg meerdere varianten toe aan de verkooporder vanuit een matrix"

#. module: sale
#: model:res.groups,name:sale.group_delivery_invoice_address
msgid "Addresses in Sales Orders"
msgstr "Adressen in verkooporders"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr "Laat je toe proforma facturen te sturen naar je klanten."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "Staat je toe om facturen in pro-forma te versturen."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr "Amazon Sync"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "Totaal voor korting"

#. module: sale
#: code:addons/sale/models/payment_transaction.py:0
#, python-format
msgid "Amount Mismatch (%s)"
msgstr "Verschil bedrag (%s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr "Aantal offertes te factureren"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"An order is to upsell when delivered quantities are above initially\n"
"                ordered quantities, and the invoicing policy is based on ordered quantities."
msgstr ""
"Een order is om te verkopen wanneer de geleverde hoeveelheden aanvankelijk hoger zijn dan\n"
"bestelde hoeveelheden en het facturatiebeleid is gebaseerd op bestelde hoeveelheden."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_order__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_report__analytic_account_id
msgid "Analytic Account"
msgstr "Kostenplaats"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr "Kostenplaats van kosten"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Kostenplaatsregel"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Kostenplaatslabels"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Kostenplaatsregels"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"Handmatige kortingen toepassen op verkooporderregels of kortingen weergeven "
"die zijn berekend op basis van prijslijsten (optie om te activeren in de "
"prijslijstinstellingen)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Weet je zeker dat je de geautoriseerde transactie ongeldig wilt maken? Deze "
"actie is definitief."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"As an example, if you sell pre-paid hours of services, Odoo recommends you\n"
"                to sell extra hours when all ordered hours have been consumed."
msgstr ""
"Als je bijvoorbeeld vooraf betaalde uren aan diensten verkoopt, raadt Odoo je aan\n"
"                 om extra uren te verkopen als alle bestelde uren zijn verbruikt."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "Voor kostprijs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_value
msgid "Attribute Value"
msgstr "Kenmerkwaarde"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Kenmerken"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Geautoriseerde transacties"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "Automatische factuur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Automatic email sent after the customer has signed or paid online"
msgstr ""
"Automatische e-mail verzonden nadat de klant online heeft getekend of "
"betaald"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Naam bank"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_acquirer__so_reference_type__partner
msgid "Based on Customer ID"
msgstr "Gebaseerd op klant ID"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_acquirer__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr "Gebaseerd op documentreferentie"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "Blokkerend bericht"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""
"Verhoog je verkopen met twee soorten kortingsprogramma's: promoties en "
"coupon codes. Specifieke condities kunnen worden ingesteld (producten, "
"klanten, minimaal bestel bedrag, periode). Beloningen kunnen kortingen (% of"
" bedrag) of gratis producten zijn."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr "Campagne"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr "Mag producten wijzigen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Annuleren"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Cancel Sales Order"
msgstr "Annuleer verkooporder"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "Afvangen transactie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr "Categorie"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__use_quotations
msgid ""
"Check this box if you send quotations to your customers rather than "
"confirming orders straight away."
msgstr ""
"Vink dit aan als je offertes naar je klanten verzendt in plaats van orders "
"meteen te bevestigen."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Choose between electronic signatures or online payments."
msgstr "Kies tussen elektronische handtekeningen of online betalingen."

#. module: sale
#: model:ir.actions.act_window,name:sale.action_open_sale_onboarding_payment_acquirer_wizard
msgid "Choose how to confirm quotations"
msgstr "Kies hoe je offertes wilt bevestigen"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Click here to add some products or services to your quotation."
msgstr "Klik hier om producten of diensten toe te voegen aan je offerte."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Click to define an invoicing target"
msgstr "Klik om een facturatiedoel in te stellen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "Sluiten"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__closed
msgid "Closed"
msgstr "Gesloten"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_acquirer__so_reference_type
msgid "Communication"
msgstr "Communicatie"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Bedrijf"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Bereken verzendkosten en verstuur met DHL"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Bereken verzendkosten en verzend met Easypost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Bereken verzendkosten en verstuur met FedEx"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Bereken verzendkosten en verstuur met USP"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Bereken verzendkosten en verstuur met USPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Bereken verzendkosten en verstuur met bpost."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Bereken verzendkosten op orders"

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "Configuratie"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "Bevestigen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__confirmation_mail_template_id
msgid "Confirmation Email Template"
msgstr "Bevestigingsmailsjabloon"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr "Connectors"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Contact"
msgstr "Contact"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid "Contact Us"
msgstr "Contact"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Conversie tussen maateenheden kan alleen plaatsvinden als deze behoren tot "
"dezelfde categorie. De conversie wordt gemaakt op basis van ratio's."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_coupon
msgid "Coupons & Promotions"
msgstr "Kortingsbonnen & Promoties"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Create Date"
msgstr "Aanmaakdatum"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Invoice"
msgstr "Factuur maken"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create a customer invoice"
msgstr "Maak een klantfactuur"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "Maak een nieuw product aan"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
#: model_terms:ir.actions.act_window,help:sale.action_sale_order_form_view
msgid "Create a new quotation, the first step of a new sale!"
msgstr "Maak een nieuwe offerte, de eerste stap van een nieuwe verkoop!"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create and View Invoice"
msgstr "Maak en bekijk factuur"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoices"
msgstr "Facturen maken"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""
"Maak facturen, registreer betalingen en volg communicatie met je klanten op."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Creation Date"
msgstr "Aanmaakdatum"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""
"Aanmaakdatum van offertes/verstuurde offertes,\n"
"Bevestigingsdatum van bevestigde orders"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "Creditcard en betaalpas (via Stripe)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "Wisselkoers"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
#: model:product.template.attribute.value,name:sale.product_4_attribute_1_value_3
msgid "Custom"
msgstr "Aangepast"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "Aangepaste waardes"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Aangepaste betaalinstructies"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Klant"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "Klant account"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_sale_delivery_address
msgid "Customer Addresses"
msgstr "Klantadres"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr "Land klant"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr "Klant entiteit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr "Industrie klant"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr "Klantenportaal URL"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "Referentie klant"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr "Handtekening klant"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Customer Taxes"
msgstr "Verkoop BTW"

#. module: sale
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Klanten"

#. module: sale
#: model:product.product,name:sale.product_product_4e
#: model:product.product,name:sale.product_product_4f
msgid "Customizable Desk"
msgstr "Verstelbaar bureau"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize"
msgstr "Wijzigen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize the look of your quotations."
msgstr "Pas de lay-out van je offertes aan."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Customize your quotes and orders."
msgstr "Personaliseer je offertes en orders."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Express Connector"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "Datum"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__date_order
msgid "Date Order"
msgstr "Orderdatum"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signed_on
msgid "Date of the signature."
msgstr "Datum van de handtekening."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__create_date
msgid "Date on which sales order is created."
msgstr "Datum waarop de verkooporder is gemaakt."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "Datum:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr "Aanbetalingen aftrekken"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Default Limit:"
msgstr "Standaard geldigheid:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__use_quotation_validity_days
msgid "Default Quotation Validity"
msgstr "Standaard geldigheid offerte"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity (Days)"
msgstr "Standaard geldigheid offerte (dagen)"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__deposit_default_product_id
msgid "Default product used for payment advances"
msgstr "Standaardproduct dat gebruikt wordt bij vooruitbetalingen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr "Verstuur productinformatie via e-mail"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr "Geleverd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_manual
msgid "Delivered Manually"
msgstr "Handmatig geleverd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
msgid "Delivered Quantity"
msgstr "Geleverde aantal."

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Delivered Quantity: %s"
msgstr "Geleverde hoeveelheid: %s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "Geleverde hoeveelheden"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_account_move__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_account_payment__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "Afleveradres"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr "Leverdatum"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Verzendwijzes"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__partner_shipping_id
#: model:ir.model.fields,help:sale.field_account_move__partner_shipping_id
#: model:ir.model.fields,help:sale.field_account_payment__partner_shipping_id
msgid "Delivery address for current invoice."
msgstr "Afleveradres voor de huidige factuur."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Leveringsdatum die je aan de klant kunt beloven, berekend vanaf de minimale "
"levertijd van de orderregels in het geval van diensten-producten. In geval "
"van verzending wordt rekening gehouden met het verzendbeleid van de order om"
" de minimale of maximale levertijd van de orderregels te gebruiken."

#. module: sale
#: model:product.product,name:sale.advance_product_0
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Aanbetaling"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__deposit_default_product_id
msgid "Deposit Product"
msgstr "Product aanbetaling"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "Omschrijving"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr "Kort.%"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr "Korting %"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "Krt. (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr "Kortingsbedrag"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__display_name
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr "Soort weergave"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale_order_view.js:0
#, python-format
msgid "Do you want to apply this discount to all order lines?"
msgstr "Wil je deze korting toepassing op alle orderregels?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Documentation"
msgstr "Documentatie"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__done
msgid "Done"
msgstr "Gereed"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment"
msgstr "Aanbetaling"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment Amount"
msgstr "Bedrag aanbetaling"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount (Fixed)"
msgstr "Bedrag aanbetaling (vast)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__product_id
msgid "Down Payment Product"
msgstr "Product aanbetaling"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment: %s"
msgstr "Aanbetaling: %s"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Down Payments"
msgstr "Aanbetalingen"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment"
msgstr "Aanbetaling"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "Aanbetaling (vast bedrag)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "Aanbetaling (percentage)"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment of %s%%"
msgstr "Aanbetaling van %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""
"Aanbetalingen worden gemaakt wanneer facturen worden gemaakt vanuit een "
"verkooporder. Ze zijn niet gekopieerd wanneer je een verkooporder "
"dupliceert."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Download"
msgstr "Downloaden"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
msgid "Draft Quotation"
msgstr "Concept offerte"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Draft invoices for this order will be cancelled."
msgstr "Conceptfacturen voor deze order worden geannuleerd."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Easypost connector"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#, python-format
msgid "Edit Configuration"
msgstr "Wijzig configuratie"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__digital_signature
msgid "Electronic signature"
msgstr "Digitale handtekening"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr "E-mail"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Wizard e-mail opstellen"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__confirmation_mail_template_id
msgid "Email sent to the customer once the order is paid."
msgstr "E-mail verzonden naar de klant nadat de order betaald is."

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid "Ergonomic"
msgstr "Ergonomisch"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Datum verwacht"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr "Verwacht:"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Expenses and vendor bills can be re-invoiced to a customer.With this option,"
" a validated expense can be re-invoice to a customer at its cost or sales "
"price."
msgstr ""
"Declaraties en leveranciersfacturen kunnen worden door-gefactureerd aan de "
"klant. Met deze optie kan een bevestigde declaratie worden door-gefactureerd"
" aan de klant op basis van de kostprijs of de verkoopprijs."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr "Vervaldatum"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Uitgebreide filters"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#, python-format
msgid "External Link"
msgstr "Externe link"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "Extra waardes"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Extra line with %s"
msgstr "Extra regel met %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "FedEx connector"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Fiscale positie"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr ""
"Fiscale posities worden gebruikt om BTW en rekeningen voor bepaalde klanten "
"of verkooporders/facturen aan te passen. De standaardwaarde komt van de "
"klant."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Relaties)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icoon bijv. fa-tasks"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr "Ontbrekende verplichte velden op niet facturabele verkooporderregel."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Vrije registratie"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Deze rapportage geeft een overzicht van de uitstaande bedragen gefactureerd "
"aan je klanten. De zoekopties geven de mogelijkheid om de analyses aan te "
"passen."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
msgid "Fully Invoiced"
msgstr "Volledig gefactureerd"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "Toekomstige activiteiten"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Gegenereerde verkoop betaallink"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr "Genereer een betaallink"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "Maak de factuur automatisch aan als de online betaling is bevestigd"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr "Krijg waarschuwingen op orders voor producten of klanten"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Good job, let's continue."
msgstr "Goed werk, laten we verder gaan."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "Sta kortingen toe op verkooporderregels"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "Bruto gewicht"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Groeperen op"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_pricelist
msgid "Has Pricelist Changed"
msgstr "Heeft gewijzigde prijslijst"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr "Heeft aanbetalingen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "History"
msgstr "Geschiedenis"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__id
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "ID"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr "Icoon"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icoon om uitzondering op activiteit aan te geven."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
#: model:ir.model.fields,help:sale.field_sale_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "indien aangevinkt hebben sommige leveringen een fout."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""
"Als de verkooporder geblokkeerd is kun je deze niet meer aanpassen. Het is "
"wel mogelijk de order te factureren of te leveren."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_packaging__sales
msgid "If true, the packaging can be used for sales orders"
msgstr "Indien waar, kan de verpakking worden gebruikt voor verkooporders"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr ""
"Als je de prijslijst wijzigt, heeft dit alleen effect op nieuw toegevoegde "
"regels."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr "Importeren van Amazon orders en het synchroniseren van leveringen"

#. module: sale
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid "Import Template for Products"
msgstr "Import sjabloon voor producten"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr "Incl. BTW)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Income Account"
msgstr "Omzetrekening"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid order."
msgstr "Incorrecte order."

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr "Foutieve handtekening gegevens."

#. module: sale
#: code:addons/sale/models/account_move.py:0
#, python-format
msgid "Invoice %s paid"
msgstr "Factuur %s betaald"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "Factuuradres"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_invoice_alert
msgid "Invoice Alert"
msgstr "Factuurwaarschuwing"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Factuur bevestigd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr "Aantal facturen"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Factuur aangemaakt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "Factuur e-mail sjabloon"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "Factuurregels"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Factureer verkooporders"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
msgid "Invoice Status"
msgstr "Factuurstatus"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_sale_form_view
msgid "Invoice after delivery, based on quantities delivered, not ordered."
msgstr ""
"Factuur na levering, op basis van geleverde hoeveelheden, niet besteld."

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"Factuuromzet voor deze maand. Dit is het bedrag dat het verkoopteam deze "
"maand heeft gefactureerd. Het wordt in de kanban weergave gebruikt om de "
"voortgangsratio te berekenen van de huidige omzet tegenover de doelstelling."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr "Factureer wat is geleverd"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr "Factureer wat is besteld"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "Gefactureerd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "Gefactureerde aantal"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Invoiced Quantity: %s"
msgstr "Gefactureerde hoeveelheid: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "Deze maand gefactureerd"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Facturen"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Factuuranalyse"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Factuuranalyses"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"Invoices will be created in draft so that you can review\n"
"                        them before validation."
msgstr ""
"Facturen worden aangemaakt als concept zodat jij ze nog kan bijwerken\n"
"voor de definitieve validatie."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Facturatie"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address:"
msgstr "Factuuradres:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "Facturatiebeleid"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr "Facturatiedoel"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address:"
msgstr "Factuur- en afleveradres:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr "Is een aanbetaling"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr "Is declaratie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is expired"
msgstr "Is vervallen"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr ""
"Is waar als de verkooporderregel komt vanuit een declaratie of "
"leveranciersfactuur"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""
"Het is niet toegestaan om de volgende velden aan te passen in een geblokkeerde order:\n"
"%s"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "It is not allowed to confirm an order in the following states: %s"
msgstr ""
"Het is niet toegestaan een order te bevestigen, welke zich in de volgende "
"status bevinden: %s"

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__just_done
msgid "Just done"
msgstr "Net gedaan"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales____last_update
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:sale.field_sale_report____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "Te late activiteiten"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Levertijd"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Laat klanten inloggen voor toegang tot hun documenten"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Let's send the quote."
msgstr "Laten we de offerte versturen."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Lets keep electronic signature for now."
msgstr "Laten we de digitale handtekening voor nu houden."

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid "Locally handmade"
msgstr "Lokaal handgemaakt"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Lock"
msgstr "Blokkeer"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr "Bevestigde orders blokkeren"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__done
msgid "Locked"
msgstr "Geblokkeerd"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"Looking for a custom bamboo stain to match existing furniture? Contact us "
"for a quote."
msgstr ""
"Op zoek naar een bamboebeits op maat die past bij bestaande meubels? Neem "
"contact met ons op voor een offerte."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Looks good. Let's continue."
msgstr "Ziet er goed uit. Laten we verder gaan."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Looks great!"
msgstr "Ziet er goed uit!"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hoofdbijlage"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "Beheer promoties &amp; kortingsprogramma's"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "Handmatig"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr "Handmatige betaling"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "Bepaal handmatig de hoeveelheden voor de order"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Bepaal handmatig de hoeveelheden voor de order: Factuur op basis van de handmatig ingevoerde hoeveelheid, zonder het aanmaken van een kostenplaats.\n"
"Urenstaten op project: Factuur op basis van de geschreven uren op gerelateerde urenstaten.\n"
"Maak een taak aan en volg de uren ervan op: Een taak maken bij verkoop order bevestiging en volg werkuren via taken."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "Marges"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr "Markeer offerte als verzonden"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr "Marketing"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr "Medium"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "Handelaarsaccount ID"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "Bericht voor verkooporder"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Bericht bij een verkooporderregel"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Methode"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Methode om de geleverde hoeveelheid bij te werken"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr "Ontbrekende verplichte velden op facturabele verkooporderregel."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mijn activiteit deadline"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Mijn orders"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr "Mijn offertes"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Mijn verkooporderregels"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signed_by
msgid "Name of the person that signed the SO."
msgstr "Naam van de persoon die de SO ondertekend heeft."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "New"
msgstr "Nieuw"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "Nieuwe offerte"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Volgende activiteitenafspraak"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Volgende activiteit deadline"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "Omschrijving volgende actie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "Volgende activiteit type"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "Nee"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "Geen bericht"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr "Wijzig geen orders meer die al bevestigd zijn"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr "Geen orders om te factureren gevonden"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found."
msgstr "Geen upsell orders gevonden."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/variant_mixin.js:0
#, python-format
msgid "Not available with %s"
msgstr "Niet beschikbaar met %s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__not_done
msgid "Not done"
msgstr "Niet gedaan"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "Notitie"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
msgid "Nothing to Invoice"
msgstr "Niets te factureren"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Now, we'll create a sample quote."
msgstr "Laten we nu een voorbeeld offerte maken."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Number"
msgstr "Nummer"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Aantal dagen tussen het bevestigen van de order en het werkelijk leveren van"
" de producten aan de klant."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Aantal berichten die actie vereisen"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr "Aantal offertes te factureren"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr "Aantal verkopen te factureren"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Aantal ongelezen berichten"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "Op uitnodiging"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr ""
"Als de offerte wordt bevestigd door de klant wordt het een "
"verkooporder.<br>Je kunt er daarna een factuur van maken en de betaling "
"verwerken."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_sale_order_form_view
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"Als je de offerte bevestigd, dan wordt het een verkooporder.<br>Je kunt er "
"daarna een factuur van maken en de betaling verwerken."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online Payment"
msgstr "Online betaling"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online Signature"
msgstr "Online handtekening"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Only draft orders can be marked as sent directly."
msgstr ""
"Alleen offertes in concept kunnen als direct verzonden worden gemarkeerd."

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid ""
"Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr ""
"Per kenmerkwaarde per verkooporderregel is slechts één aangepaste waarde "
"toegestaan."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Open Sales app to send your first quotation in a few clicks."
msgstr ""
"Open de verkopen app om je eerste offerte te verzenden in een paar klikken."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Order"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__order_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Order #"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Order Confirmation"
msgstr "Orderbevestiging"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr "Aantal orders"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#, python-format
msgid "Order Date"
msgstr "Orderdatum"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Orderregels"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__name
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "Orderreferentie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "Order status"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr "Upsell order"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Order signed by %s"
msgstr "Order ondertekend door %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Order to Invoice"
msgstr "Orders om te factureren"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Ordered Quantity: %(old_qty)s -> %(new_qty)s"
msgstr "Bestelde hoeveelheid: %(old_qty)s->%(new_qty)s"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""
"Bestelde hoeveelheid: Factureer hoeveelheden besteld door de klant.\n"
"Geleverde hoeveelheden: Factureer hoeveelheden geleverd aan de klant."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "Bestelde hoeveelheden"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "Orders"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Orders to Invoice"
msgstr "Orders om te factureren"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Upsell orders"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr "Overige info"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr "Andere betaling verwervers"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDT identiteitstoken"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr "PRO-FORMA Factuur"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_id
msgid "Packaging"
msgstr "Verpakking"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Verpakkingshoeveelheid:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__partner_id
msgid "Partner"
msgstr "Relatie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__country_id
msgid "Partner Country"
msgstr "Land relatie"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay Now"
msgstr "Betaal nu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay with"
msgstr "Betaal met"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment acquirer"
msgstr "Betaal met een andere betaalprovider"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr "PayPal"

#. module: sale
#: model:ir.model,name:sale.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Betaalprovider"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Acquirers"
msgstr "Betaalproviders"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Betaalinstructies"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Betaalmethode"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr "Betaling Ref."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "Betalingscondities"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransactie"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr "Betalingstermijn"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "Paypal soort gebruiker"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Please define an accounting sales journal for the company %s (%s)."
msgstr "Maak een verkoopdagboek aan voor dit bedrijf %s (%s)."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid "Please enter an integer value"
msgstr "Geef een geheel getal in"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr "Portaal toegang URL"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"Press a button and watch your desk glide effortlessly from sitting to "
"standing height in seconds."
msgstr ""
"Druk op een knop en zie hoe het bureau in enkele seconden moeiteloos van "
"zit- naar stahoogte glijdt."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce
msgid "Price Reduce"
msgstr "Gereduceerde prijs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Gereduceerde prijs excl. BTW"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Gereduceerde prijs incl. BTW"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_subtotal
msgid "Price Subtotal"
msgstr "Prijs subtotaal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "Prijslijst"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Prijslijsten"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Pricing"
msgstr "Prijzen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Print"
msgstr "Afdrukken"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr "Proforma factuur"

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "Proforma facturen"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Product"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute
msgid "Product Attribute"
msgstr "Productkenmerk"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Product kenmerk aangepaste waarde"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr "Product catalogus"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__categ_id
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Productcategorie"

#. module: sale
#: model:ir.model,name:sale.model_product_packaging
msgid "Product Packaging"
msgstr "Productverpakkingen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_qty
msgid "Product Quantity"
msgstr "Producthoeveelheid"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_tmpl_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
msgid "Product Template"
msgstr "Productsjabloon"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_readonly
msgid "Product Uom Readonly"
msgstr "Productmaateenheid Alleen lezen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
msgid "Product Variant"
msgstr "Productvariant"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "Productvarianten"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Product prices have been recomputed according to pricelist <b>%s<b> "
msgstr "Productprijzen zijn opnieuw berekend volgens prijslijst <b>%s<b>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product used for down payments"
msgstr "Product gebruikt voor aanbetalingen"

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "Producten"

#. module: sale
#: model:ir.model,name:sale.model_report_sale_report_saleproforma
msgid "Proforma Report"
msgstr "Proforma rapport"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Aantal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "Aantal geleverd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "Gefactureerde aantal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr "Aantal besteld"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Aantal te leveren"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "Aantal te factureren"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "Aantal te factureren vanuit verkooporders"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Quantity"
msgstr "Aantal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_qty_configurator
#: model:ir.model.fields,field_description:sale.field_product_template__visible_qty_configurator
msgid "Quantity visible in configurator"
msgstr "Aantal zichtbaar in configurator"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr "Hoeveelheid:"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
#, python-format
msgid "Quotation"
msgstr "Offerte"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Offerte #"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "Quotation / Order"
msgstr "Offerte / Order"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr "Aantal offertes"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr "Offertedatum"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Quotation Layout"
msgstr "Offerte layout"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "Offerte verstuurd"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid "Quotation Validity is required and must be greater than 0."
msgstr "De offertegeldigheid moet grote zijn dan 0."

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Offerte bevestigd"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "Offerte verzonden"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Quotation viewed by customer %s"
msgstr "Offerte bekeken door klant %s"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.model.fields,field_description:sale.field_crm_team__use_quotations
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Quotations"
msgstr "Offertes"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations &amp; Orders"
msgstr "Offertes &amp; Orders"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Offerteanalyse"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Offertes en verkooporders"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Expenses"
msgstr "Her-factureer declaraties"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr "Her-facturatiebeleid zichtbaar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all prices based on this pricelist"
msgstr "Bereken alle prijzen opnieuw op basis van deze prijslijst."

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Reference"
msgstr "Referentie"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request."
msgstr ""
"Referentie van het document dat deze verkooporder aanvraag genereerde."

#. module: sale
#: model:ir.model,name:sale.model_account_payment_register
msgid "Register Payment"
msgstr "Betaling registreren"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr "Standaard factuur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr "Offerte afkeuren"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
msgid "Reporting"
msgstr "Rapportages"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr ""
"Vraag een online handtekening aan de klant in het order om automatisch de "
"orders te bevestigen."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online payment to confirm orders"
msgstr "Vraag een online betaling om de order te bevestigen"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr ""
"Vraag een online betaling aan de klant om automatisch de orders te "
"bevestigen."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online signature to confirm orders"
msgstr "Vraag een online handtekening om de order te bevestigen"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Requested date is too soon."
msgstr "Gevraagde datum is te snel."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "Verantwoordelijke gebruiker"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid ""
"Revenue target for the current month (untaxed total of confirmed invoices)."
msgstr ""
"Omzet doelstelling voor de huidige maand (onbelast totaal van bevestigde "
"facturen)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr "Omzet"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr "Omzet gegenereerd via deze campagne"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Review, Accept &amp; Pay Quotation"
msgstr "Beoordelen, accepteren &amp; offerte betalen:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Review, Accept &amp; Sign Quotation"
msgstr "Beoordelen, accepteren &amp; onderteken offerte"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Review, Sign &amp; Pay Quotation"
msgstr "Beoordeel, ondertekenen &amp; offerte betalen:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS fout bij versturen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__order_id
msgid "Sale Order"
msgstr "Verkooporder"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr "Totaal verkooporders"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr "Verkooporder waarschuwingen"

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_acquirer_onboarding_wizard
msgid "Sale Payment acquire onboarding wizard"
msgstr "Verkoopbetaalverwerker onboarding wizard"

#. module: sale
#: model:utm.source,name:sale.utm_source_sale_order_0
msgid "Sale Promotion 1"
msgstr "Verkooppromotie 1"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr "Verkoopwaarschuwingen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr "Verkoop onboarding geselecteerde betalinsgmethode"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_packaging__sales
#: model:ir.ui.menu,name:sale.menu_report_product_all
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "Verkoop"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Verkoop vooruibetaling factuur"

#. module: sale
#: code:addons/sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#, python-format
msgid "Sales Analysis"
msgstr "Verkoopanalyse"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Verkoopanalyserapport"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__done
msgid "Sales Done"
msgstr "Gerealiseerde verkopen"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: model:ir.actions.act_window,name:sale.action_sale_order_form_view
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Sales Order"
msgstr "Verkooporder"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Annuleer verkooporder"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Verkooporder bevestigd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr "Verkooporderregel"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Verkooporderregels"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Verkooporderregels gereed om te factureren"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Verkooporderregels gerelateerd aan een verkooporder van mijzelf"

#. module: sale
#: code:addons/sale/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
#, python-format
msgid "Sales Order(s)"
msgstr "Verkooporder(s)"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_confirmation
msgid "Sales Order: Confirmation Email"
msgstr "Verkooporder: bevestigingsmail"

#. module: sale
#: model:mail.template,name:sale.email_template_edi_sale
msgid "Sales Order: Send by email"
msgstr "Verkooporder: per e-mail verzenden"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "Verkooporders"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_account_payment__team_id
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Verkoopteam"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "Verkoopteams"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr "Verkoopwaarschuwingen"

#. module: sale
#: model:ir.model,name:sale.model_report_all_channels_sales
msgid "Sales by Channel (All in One)"
msgstr "Verkopen per kanaal (Alles in één)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr "Voor verkoopprijs"

#. module: sale
#: code:addons/sale/models/crm_team.py:0
#, python-format
msgid "Sales: Untaxed Total"
msgstr "Verkoop: Totaal excl. BTW"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Verkoper"

#. module: sale
#: code:addons/sale/models/res_company.py:0
#, python-format
msgid "Sample Order Line"
msgstr "Voorbeeld orderregel"

#. module: sale
#: code:addons/sale/models/res_company.py:0
#, python-format
msgid "Sample Product"
msgstr "Voorbeeld product"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Sample Quotation"
msgstr "Voorbeeld offerte"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Verkooporder zoeken"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "Sectie"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Sectienaam (bijv. Producten, Diensten)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "Veiligheidstoken"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Select a product, or create a new one on the fly."
msgstr "Selecteer een product, of maak direct een nieuw product aan."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Select product attributes and optional products from the sales order"
msgstr ""
"Selecteer product kenmerken en optionele producten vanuit het verkooporder"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Select specific invoice and delivery addresses"
msgstr "Selecteer specifieke factuur- en afleveradressen"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Door de 'Waarschuwingsoptie' te selecteren wordt het bericht naar de "
"gebruiker gestuurd. Door het 'Blokkerend bericht' te kiezen wordt een fout "
"gegenereerd met de boodschap en het proces wordt geblokkeerd. Het bericht "
"moet in het volgende veld worden ingevoerd."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Verkoop- en inkoopproducten in verschillende maateenheden"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr "Verkoop producten per meervoud van eenheid # per verpakking"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr ""
"Verkoop varianten van een product door middel van kenmerken (grootte, kleur,"
" enz.)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr "Stuur PRO-FORMA factuur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr ""
"Verzend een product specifieke e-mail wanneer de factuur is bevestigd."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send a quotation to test the customer portal."
msgstr "Verzend een offerte om het klantenportaal te testen."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Verzenden via e-mail"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send sample"
msgstr "Verzend voorbeeld"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Sales tab)."
msgstr ""
"Het verzenden van een e-mail is handig als je specifieke informatie of "
"inhoud over een product wilt delen (instructies, regels, links, media, "
"etc.). Maak en stel een e-mailsjabloon in op het productformulier (in het "
"tabblad Verkoop)."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set a default validity on your quotations"
msgstr "Stel een standaard geldigheid in op je offertes"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid "Set an invoicing target: "
msgstr "Bepaal een facturatiedoel:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "Stel meerdere prijzen per product in, automatische kortingen, enz."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Set payments"
msgstr "Stel betaling in"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Zet op offerte"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "Instellingen"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "Delen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Shipping"
msgstr "Afleveradres"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr "Toon alle records welke een actiedatum voor vandaag hebben"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr "Toon marge op verkooporders"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr "Teken &amp; Betaal"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr "Teken online"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "Handtekening"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr "Handtekening ontbreekt."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signature
msgid "Signature received through the portal."
msgstr "Handtekening ontvangen via het portaal."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr "Getekend door"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr "Getekend op"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr "Verkocht"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr "Verkocht in de laatste 365 dagen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__source_id
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr "Bron"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "Brondocument"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr "Specifiek e-mailadres"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Stage"
msgstr "Fase"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Start by checking your company's data."
msgstr "Begin met het controleren van de gegevens van je bedrijf."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_order_confirmation_state
msgid "State of the onboarding confirmation order step"
msgstr "Satus van de onboarding bevestigingsstap"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_sample_quotation_state
msgid "State of the onboarding sample quotation step"
msgstr "Status van de onboarding voorbeeldofferte stap"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_quotation_onboarding_state
msgid "State of the sale onboarding panel"
msgstr "Status van het verkoop onboarding paneel"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Status"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status gebaseerd op activiteiten\n"
"Te laat: Datum is al gepasseerd\n"
"Vandaag: Activiteit datum is vandaag\n"
"Gepland: Toekomstige activiteiten."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Stripe publiceerbare sleutel"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Stripe geheime sleutel"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "Subtotaal"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Total"
msgstr "Som van totaal"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "Som van onbelast totaal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tag_ids
#: model:ir.ui.menu,name:sale.menu_tag_config
msgid "Tags"
msgstr "Labels"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_country_id
msgid "Tax Country"
msgstr "Btw land"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Tax Total"
msgstr "Totaal BTW"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_totals_json
msgid "Tax Totals Json"
msgstr "Belastingtotalen Json"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "BTW"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Taxes used for deposits"
msgstr "BTW gebruikt voor aanbetalingen"

#. module: sale
#: code:addons/sale/models/crm_team.py:0
#, python-format
msgid ""
"Team %(team_name)s has %(sale_order_count)s active sale orders. Consider "
"canceling them or archiving the team instead."
msgstr ""
"Team %(team_name)s heeft %(sale_order_count)s actieve verkooporders. "
"Overweeg ze te annuleren of in plaats daarvan het team te archiveren."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__show_update_pricelist
msgid ""
"Technical Field, True if the pricelist was changed;\n"
" this will then display a recomputation button"
msgstr ""
"Technisch veld, 'waar' als de prijslijst is gewijzigd;\n"
"hierdoor wordt een herberekeningsknop weergegeven."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "Technisch veld voor UI doeleinden."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__tax_country_id
msgid ""
"Technical field to filter the available taxes depending on the fiscal "
"country and fiscal position."
msgstr ""
"Technisch veld om de beschikbare belastingen te filteren, afhankelijk van "
"het fiscale land en de fiscale positie."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr ""
"Vertel ons waarom je deze offerte afwijst, dit helpt ons bij het verbeteren "
"van onze dienstverlening."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__terms_type
msgid "Terms & Conditions format"
msgstr "Algemene voorwaarden formaat"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Terms & Conditions: %s"
msgstr "Algemene voorwaarden: %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Algemene voorwaarden"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions:"
msgstr "Algemene voorwaarden:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "Algemene voorwaarden"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr "Algemene verkoopvoorwaarden..."

#. module: sale
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is cancelled. You "
"cannot register an expense on a cancelled Sales Order."
msgstr ""
"De verkooporder %s gekoppeld aan kostenplaats  %s is geannuleerd. Het is "
"niet mogelijk een declaratie te boeken op een geannuleerde verkooporder."

#. module: sale
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is currently locked. "
"You cannot register an expense on a locked Sales Order. Please create a new "
"SO linked to this Analytic Account."
msgstr ""
"De verkooporder %s gekoppeld an kostenplaats %sis momenteel geblokkeerd. Het"
" is niet mogelijk een declaratie te boeken op een geblokkeerde verkooporder."
" Maak een nieuwe verkooporder gekoppeld aan deze kostenplaats."

#. module: sale
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s must be validated "
"before registering expenses."
msgstr ""
"De verkooporder %s gekoppeld aan kostenplaats %s moet zijn bevestigd voordat"
" een declaratie kan worden geboekt."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__analytic_account_id
msgid "The analytic account related to a sales order."
msgstr "De kostenplaatsrekening verbonden met een verkooporder."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"The delivery date is sooner than the expected date.You may be unable to "
"honor the delivery date."
msgstr ""
"De leverdatum is eerder dan de verwachte datum. Mogelijk kun je de "
"leverdatum niet nakomen."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance, taxes excluded."
msgstr "Het vooraf te factureren vaste bedrag, exclusief BTW."

#. module: sale
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"The following products cannot be restricted to the company %s because they have already been used in quotations or sales orders in another company:\n"
"%s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""
"Deze producten kunnen niet tot één bedrijf %s beperkt worden omdat die al gebruikt werden voor een offerte of order in een andere bedrijf:\n"
"%s\n"
"In plaats daarvan kun je deze producten archiveren en opnieuw aanmaken met een bedrijfsrestrictie, of ze als gemeenschappelijk product laten staan."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment acquirer.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment acquirer.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""
"De factuur wordt automatisch gegenereerd en is beschikbaar in het klantportaal wanneer de transactie wordt bevestigd door de betaalprovider.\n"
"De factuur is gemarkeerd als betaald en de betaling is geregistreerd in het betalingsdagboek dat is gedefinieerd in de configuratie van de betaalprovider.\n"
"Deze modus wordt aanbevolen als je de eindfactuur bij de order verstrekt en niet na de levering."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr ""
"De marge is berekend als de som van de product verkoopprijs minus de som van"
" de kostprijs zoals ingesteld op het product."

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"The minimum height is 65 cm, and for standing work the maximum height "
"position is 125 cm."
msgstr ""
"De minimale hoogte is 65 cm en voor staand werken is de maximale "
"hoogtepositie 125 cm."

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The order is not in a state requiring customer signature."
msgstr ""
"De order bevind zich niet in een status waarvoor een klanthandtekening nodig"
" is."

#. module: sale
#: code:addons/sale/models/payment_transaction.py:0
#, python-format
msgid ""
"The order was not confirmed despite response from the acquirer (%s): order "
"total is %r but acquirer replied with %r."
msgstr ""
"Het verkooporder is niet bevestigd, ondanks de response van de "
"betaalverwerker (%s): orderbedrag is %r maar betaalverwerker antwoordde met "
"%r."

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "The ordered quantity has been updated."
msgstr "De bestelde hoeveelheid is bijgewerkt."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr "De betalingscommunicatie voor deze verkooporder."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance, taxes excluded."
msgstr "Het percentage van het vooraf te factureren bedrag, exclusief BTW."

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr ""
"Het product dat gebruikt wordt om een aanbetaling op te doen moet van het "
"type 'Dienst' zijn. Graag een ander product gebruiken of dit product te "
"wijzigen."

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policy set"
" to \"Ordered quantities\". Please update your deposit product to be able to"
" create a deposit invoice."
msgstr ""
"Het product dat gebruikt wordt voor een aanbetaling moet een "
"facturatiebeleid hebben dat ingesteld is op \"bestelde hoeveelheden\".  "
"Graag je aanbetaling product bijwerken zodat je een aanbetalingsfactuur kunt"
" aanmaken."

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The provided parameters are invalid."
msgstr "De opgegeven parameters zijn ongeldig."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate 1 applicable at the date of"
" the order"
msgstr ""
"De koers van de valuta naar de valuta van tarief 1 van toepassing op de "
"datum van de order"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr "De waarde van de aanbetaling moet positief zijn."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no orders for your account."
msgstr "Er zijn momenteel geen orders voor je account."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr "Er zijn momenteel geen offertes voor je account."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"There is nothing to invoice!\n"
"\n"
"Reason(s) of this behavior could be:\n"
"- You should deliver your products before invoicing them.\n"
"- You should modify the invoicing policy of your product: Open the product, go to the \"Sales\" tab and modify invoicing policy from \"delivered quantities\" to \"ordered quantities\". For Services, you should modify the Service Invoicing Policy to 'Prepaid'."
msgstr ""
"Er valt niets te factureren!\n"
"\n"
"Reden(en) van dit gedrag kunnen zijn:\n"
"- Je moet je producten leveren voordat je ze factureert.\n"
"- Je moet het facturatiebeleid van je product wijzigen: Open het product, ga naar het tabblad \"Verkoop\" en wijzig het facturatiebeleid van \"geleverde hoeveelheden\" naar \"bestelde hoeveelheden\". Voor diensten moet je het facturatiebeleid van de dienst wijzigen naar 'Prepaid'."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Deze standaard waarde is toegepast op elk nieuw aangemaakt product. Dit kan "
"gewijzigd worden op het product detail formulier."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__campaign_id
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Dit is een naam die je helpt bij het volgen van je verschillende campagne "
"inspanningen, bijvoorbeeld Herfst_Rit, Kerstmis_Special"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""
"Dit is de levertijd welke is beloofd aan de klant. Indien ingesteld zal de "
"levering worden ingepland op deze datum in plaats van de leverdatum "
"ingesteld op het product."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__medium_id
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Dit een levermethode, bijv. ansichtkaart, e-mail of een banner advertentie"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__source_id
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Dit is de bron van de link, bijv, zoekmachine, een ander domein, of een naam"
" of e-mail lijst"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should sell "
"%(quantity).2f %(unit)s."
msgstr ""
"Dit product is verpakt door %(pack_size).2f %(pack_name)s. Je kunt beter "
"%(quantity).2f %(unit)s verkopen."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"Dit overzicht voert een analyse uit op je offertes en verkooporders. "
"Analyseer je omzet en sorteer het op verschillende groepeer criteria "
"(verkoper, relatie, product, etc.) Gebruik dit overzicht op nog niet "
"gefactureerde verkopen. Als je je omzet wilt analyseren, moet je de "
"factuuranalyse gebruiken in de financiële applicatie."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Dit overzicht voert een analyse uit op je offertes. Analyseer je omzet en "
"sorteer het op verschillende groepeer criteria (verkoper, relatie, product, "
"etc.) Gebruik dit overzicht op nog niet gefactureerde verkopen. Als je je "
"omzet wilt analyseren, moet je de factuuranalyse gebruiken in de financiële "
"applicatie."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Dit overzicht voert een analyse uit op je verkooporders. Analyseer je omzet "
"en sorteer het op verschillende groepeer criteria (verkoper, relatie, "
"product, etc.) Gebruik dit overzicht op nog niet gefactureerde verkopen. Als"
" je je omzet wilt analyseren, moet je de factuuranalyse gebruiken in de "
"financiële applicatie."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "This will update all unit prices based on the currently set pricelist."
msgstr ""
"Hiermee worden alle eenheidsprijzen bijgewerkt op basis van de momenteel "
"ingestelde prijslijst."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "Te factureren"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "Te factureren hoeveelheid"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr "Upsell mogelijkheid"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Om uitnodigingen in B2B modus te sturen, open je een contact of selecteert "
"je er meerdere in een lijstweergave. Klik dan op 'Portaal toegangsbeheer' "
"optie in het actiemenu."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"To speed up order confirmation, we can activate electronic signatures or "
"payments."
msgstr ""
"Om de orderbevestiging te versnellen, kunnen we elektronische handtekeningen"
" of betalingen activeren."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "Activiteiten van vandaag"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_total
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Totaal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr "Totaal BTW"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Excluded"
msgstr "Totaal exclusief BTW"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Included"
msgstr "Totaal incl. BTW"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "Dienst opvolgen"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tracking"
msgstr "Traceren"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "Transacties"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr "Type naam"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type van activiteit uitzondering op record."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "UPS connector"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "USPS connector"

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM Campagne"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "Prijs"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr "Prijs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Maateenheid"

#. module: sale
#: model:product.product,uom_name:sale.advance_product_0
#: model:product.product,uom_name:sale.product_product_4e
#: model:product.product,uom_name:sale.product_product_4f
#: model:product.template,uom_name:sale.advance_product_0_product_template
msgid "Units"
msgstr "Stuks"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Maateenheden"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Maateenheidcategorieën"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Unlock"
msgstr "Ontgrendel"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread
msgid "Unread Messages"
msgstr "Ongelezen berichten"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Aantal ongelezen berichten"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Excl. BTW"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "Totaal excl. BTW gefactureerd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "Totaal excl. BTW te factureren"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr "Totaal excl. BTW gefactureerd"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "Totaal excl. BTW"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr "Maateenheid"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Prices"
msgstr "Prijzen bijwerken"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Upsell %(order)s for customer %(customer)s"
msgstr "Upsell %(order)s voor klant %(customer)s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Upselling verkoopkans"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "Geldig tot"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr "Bevestig order"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr "Variantenmatrix ingave"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "View Quotation"
msgstr "Bekijk offerte"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr "Ongeldige transactie"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "Volume"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
#, python-format
msgid "Warning"
msgstr "Waarschuwing"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Warning for %s"
msgstr "Waarschuwing voor %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "Waarschuwing bij een verkooporder"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Warning when Selling this Product"
msgstr "Waarschuw bij verkopen van dit product"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"We pay special attention to detail, which is why our desks are of a superior"
" quality."
msgstr ""
"We besteden speciale aandacht aan details, daarom zijn onze bureaus van "
"superieure kwaliteit."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "Website communicatie geschiedenis"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Write a company name to create one, or see suggestions."
msgstr "Schrijf een bedrijfsnaam om er één te maken of bekijk suggesties."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_sale_form_view
msgid "You can invoice them before they are delivered."
msgstr "Je kunt ze factureren voordat ze geleverd worden."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr ""
"Het is niet mogelijk een verzonden offerte of een bevestigde offerte te "
"verwijderen. Annuleer deze eerst."

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"You can not remove an order line once the sales order is confirmed.\n"
"You should rather set the quantity to 0."
msgstr ""
"Je kunt geen orderregel verwijderen eenmaal de verkooporder bevestigd is.\n"
"Je moet de hoeveelheid naar 0 zetten."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"                or check every order and invoice them one by one."
msgstr ""
"Je kunt alle orders selecteren en ze facturen in een batch <br>\n"
"of vink elke order en factuur ze één voor één."

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_acquirer__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr ""
"Je kunt hier het communicatietype instellen welke zichtbaar is op de "
"verkooporder. Deze communicatie wordt gegeven aan de klant als deze "
"betalingsmethodewordt gekozen."

#. module: sale
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"You cannot change the product's type because it is already used in sales "
"orders."
msgstr ""
"Je kunt het type product niet meer wijzigen omdat het al in verkooporders "
"wordt gebruikt."

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"You cannot change the type of a sale order line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"Je kunt het type verkooporderregel niet wijzigen. In plaats daarvan moet je "
"de huidige regel verwijderen en een nieuwe regel van het juiste type maken."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                    whether it's a storable product, a consumable or a service."
msgstr ""
"Je moet een product definiëren voor alles dat je wilt verkopen of inkopen,\n"
"of het nu een fysiek product, verbruiksartikel of een dienst is."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr "Jouw feedback..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr "Je bestelling is bevestigd."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr ""
"Je bestelling is ondertekend, maar dient nog betaald te worden om te worden "
"bevestigd."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr "Je bestelling is getekend."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr "Je bestelling is niet in een fase om af te wijzen."

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Jouw offerte bevat producten van het bedrijf %(product_company)s terwijl je "
"offerte bij het bedrijf %(quote_company)s behoort. Wijzig het bedrijf van je"
" offerte of verwijder de producten van andere bedrijven (%(bad_products)s)."

#. module: sale
#: model:ir.actions.server,name:sale.send_invoice_cron_ir_actions_server
#: model:ir.cron,cron_name:sale.send_invoice_cron
#: model:ir.cron,name:sale.send_invoice_cron
msgid "automatic invoicing: send ready invoice"
msgstr "Automatische facturering: verzend factuur die gereed is"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bPost connector"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "sluiten"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "dagen"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "sale order"
msgstr "verkooporder"

#. module: sale
#: model:mail.template,report_name:sale.email_template_edi_sale
#: model:mail.template,report_name:sale.mail_template_sale_confirmation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr "{{ (object.name or '').replace('/','_') }}"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
msgid ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"
msgstr ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Order in behandeling' or 'Order' }} (Ref {{ object.name "
"or 'n/b' }})"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Offerte') or 'Order' }} (Ref {{ "
"object.name or 'n/b' }})"
