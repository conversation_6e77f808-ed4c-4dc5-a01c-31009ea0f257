<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- &#x642;&#x627;&#x639;&#x62F;&#x629; &#x639;&#x627;&#x645;&#x629; &#x644;&#x62C;&#x645;&#x64A;&#x639; &#x627;&#x644;&#x645;&#x633;&#x62A;&#x62E;&#x62F;&#x645;&#x64A;&#x646; - &#x62A;&#x642;&#x64A;&#x64A;&#x62F; &#x627;&#x644;&#x648;&#x635;&#x648;&#x644; &#x644;&#x644;&#x637;&#x644;&#x628;&#x627;&#x62A; -->
    <record id="rule_bssic_request_global" model="ir.rule">
        <field name="name">BSIC Request: Global Rule</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
        <field name="global" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- &#x642;&#x627;&#x639;&#x62F;&#x629; &#x644;&#x644;&#x645;&#x62F;&#x631;&#x627;&#x621; &#x627;&#x644;&#x645;&#x628;&#x627;&#x634;&#x631;&#x64A;&#x646; &#x644;&#x631;&#x624;&#x64A;&#x629; &#x637;&#x644;&#x628;&#x627;&#x62A; &#x641;&#x631;&#x64A;&#x642;&#x647;&#x645; -->
    <record id="rule_bssic_request_direct_manager" model="ir.rule">
        <field name="name">BSIC Request: Direct Managers see their team's requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), ('employee_id.parent_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('bssic_requests.group_bssic_direct_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- &#x642;&#x627;&#x639;&#x62F;&#x629; &#x644;&#x645;&#x62F;&#x631;&#x627;&#x621; &#x627;&#x644;&#x62A;&#x62F;&#x642;&#x64A;&#x642; &#x644;&#x631;&#x624;&#x64A;&#x629; &#x627;&#x644;&#x637;&#x644;&#x628;&#x627;&#x62A; &#x641;&#x64A; &#x645;&#x631;&#x62D;&#x644;&#x629; &#x645;&#x648;&#x627;&#x641;&#x642;&#x629; &#x645;&#x62F;&#x64A;&#x631; &#x627;&#x644;&#x62A;&#x62F;&#x642;&#x64A;&#x642; -->
    <record id="rule_bssic_request_audit_manager" model="ir.rule">
        <field name="name">BSIC Request: Audit Managers see requests in audit approval stage</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), ('state', '=', 'audit_manager')]</field>
        <field name="groups" eval="[(4, ref('bssic_requests.group_bssic_audit_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- &#x642;&#x627;&#x639;&#x62F;&#x629; &#x644;&#x645;&#x62F;&#x631;&#x627;&#x621; &#x62A;&#x643;&#x646;&#x648;&#x644;&#x648;&#x62C;&#x64A;&#x627; &#x627;&#x644;&#x645;&#x639;&#x644;&#x648;&#x645;&#x627;&#x62A; &#x644;&#x631;&#x624;&#x64A;&#x629; &#x627;&#x644;&#x637;&#x644;&#x628;&#x627;&#x62A; &#x641;&#x64A; &#x645;&#x631;&#x62D;&#x644;&#x629; &#x645;&#x648;&#x627;&#x641;&#x642;&#x629; &#x645;&#x62F;&#x64A;&#x631; &#x62A;&#x643;&#x646;&#x648;&#x644;&#x648;&#x62C;&#x64A;&#x627; &#x627;&#x644;&#x645;&#x639;&#x644;&#x648;&#x645;&#x627;&#x62A; -->
    <record id="rule_bssic_request_it_manager" model="ir.rule">
        <field name="name">BSIC Request: IT Managers see requests in IT approval stage</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), '|', ('state', '=', 'it_manager'), ('state', '=', 'assigned')]</field>
        <field name="groups" eval="[(4, ref('bssic_requests.group_bssic_it_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- &#x642;&#x627;&#x639;&#x62F;&#x629; &#x644;&#x645;&#x648;&#x638;&#x641;&#x64A; &#x62A;&#x643;&#x646;&#x648;&#x644;&#x648;&#x62C;&#x64A;&#x627; &#x627;&#x644;&#x645;&#x639;&#x644;&#x648;&#x645;&#x627;&#x62A; &#x644;&#x631;&#x624;&#x64A;&#x629; &#x627;&#x644;&#x637;&#x644;&#x628;&#x627;&#x62A; &#x627;&#x644;&#x645;&#x633;&#x646;&#x62F;&#x629; &#x625;&#x644;&#x64A;&#x647;&#x645; -->
    <record id="rule_bssic_request_it_staff" model="ir.rule">
        <field name="name">BSIC Request: IT Staff see requests assigned to them</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), ('assigned_to.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('bssic_requests.group_bssic_it_staff'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- &#x642;&#x627;&#x639;&#x62F;&#x629; &#x644;&#x644;&#x645;&#x62F;&#x631;&#x627;&#x621; &#x648;&#x627;&#x644;&#x645;&#x633;&#x62A;&#x62E;&#x62F;&#x645;&#x64A;&#x646; &#x630;&#x648;&#x64A; &#x627;&#x644;&#x635;&#x644;&#x627;&#x62D;&#x64A;&#x627;&#x62A; &#x627;&#x644;&#x62E;&#x627;&#x635;&#x629; &#x644;&#x631;&#x624;&#x64A;&#x629; &#x62C;&#x645;&#x64A;&#x639; &#x627;&#x644;&#x637;&#x644;&#x628;&#x627;&#x62A; -->
    <record id="rule_bssic_request_manager_all" model="ir.rule">
        <field name="name">BSSIC Request: Managers see all requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[
            (4, ref('bssic_requests.group_bssic_direct_manager')),
            (4, ref('bssic_requests.group_bssic_audit_manager')),
            (4, ref('bssic_requests.group_bssic_it_manager')),
            (4, ref('bssic_requests.group_bssic_it_staff'))
        ]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>
</odoo>