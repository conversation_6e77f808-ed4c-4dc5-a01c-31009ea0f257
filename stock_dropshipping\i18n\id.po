# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_dropshipping
# 
# Translators:
# <PERSON> <amunif<PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: stock_dropshipping
#: model:stock.location.route,name:stock_dropshipping.route_drop_shipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.purchase_order_form_inherit_stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_order_form_inherit_sale_stock
msgid "Dropship"
msgstr "Dropship"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_purchase_order__dropship_picking_count
#: model:ir.model.fields,field_description:stock_dropshipping.field_sale_order__dropship_picking_count
msgid "Dropship Count"
msgstr "Jumlah Dropship"

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking__is_dropship
msgid "Is a Dropship"
msgstr "Adalah Dropship"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_procurement_group
msgid "Procurement Group"
msgstr "Grup Pengadaan"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "Order Pembelian"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Baris Order Pembelian"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order
msgid "Sales Order"
msgstr "Order Penjualan"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order_line
msgid "Sales Order Line"
msgstr "Detail Order Penjualan"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "Peraturan Sto"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "Transfer"
