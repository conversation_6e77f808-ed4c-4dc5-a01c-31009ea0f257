.o_hidden {
    display: none!important;
}

.o_text_overflow {
    @include o-text-overflow;
}

.ui-autocomplete {
    z-index: $zindex-modal + 1;
    max-width: 600px;
    .ui-menu-item > a {
        display: block;
    }
}

.dropdown-toggle {
    white-space: nowrap;

    &.o-no-caret {
        &::before, &::after {
            content: normal;
        }
    }
}

// Allow to change data-toggle="collapse" text according to status
:not(.collapsed) > .o-collapsed-label,
.collapsed > .o-not-collapsed-label {
    display: none;
}

// This is rtl language specific fix
// It will fix the extra space in ui-autocomplete class
// and flip the next and previous symbols of jquery ui.
.o_rtl {
    .ui-autocomplete {
        direction: ltr;
        right: 0;
        left: auto;
    }
    .ui-datepicker-next, .ui-datepicker-prev {
        -webkit-transform: rotate(180deg);
         -o-transform: rotate(180deg);
        transform: rotate(180deg);
    }
}

.o_catch_attention {
    position: relative;
    z-index: 1;
    animation: catchAttention 200ms ease 0s infinite normal;
}

// bounce effect
@keyframes catchAttention {
    0%, 20%, 40%, 60%, 80%, 100% {
        transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }
    0% {
        transform: translateY(-30%);
    }
    20% {
        transform: translateY(-25%);
    }
    40% {
        transform: translateY(-20%);
    }
    60% {
        transform: translateY(-15%);
    }
    80% {
        transform: translateY(-10%);
    }
    100% {
        transform: translateY(-5%);
    }
}

// Declare a similar class as 'disabled' but with no visual effect
.o_debounce_disabled {
    pointer-events: none;
}

span.o_force_ltr {
    display: inline;
}
.o_force_ltr, .o_field_phone {
    unicode-bidi: embed; // ensure element has level of embedding for direction
    /*rtl:ignore*/
    direction: ltr;
}

// To fill the available space while keeping aspect ratio (crop).
// Assuming the important part of the image is its center.
.o_object_fit_cover {
    object-fit: cover;
}

.o_image_24_cover {
    width: 24px;
    height: 24px;
    object-fit: cover;
}

.o_image_40_cover {
    width: 40px;
    height: 40px;
    object-fit: cover;
}

.o_image_64_cover {
    width: 64px;
    height: 64px;
    object-fit: cover;
}

// Keep ratio but avoid cropping (so part of the background becomes visible).
.o_image_64_contain {
    width: 64px;
    height: 64px;
    object-fit: contain;
}

// When having a square is not necessary, the image will take less space if any
// of its width or height is smaller than 64px.
.o_image_64_max {
    max-width: 64px;
    max-height: 64px;
}

.o_image_128_max {
    max-width: 128px !important;
    max-height: 128px !important;
}

.o_width_128 {
    width: 128px;
}

.o_web_accesskey_overlay {
    @include o-position-absolute(0, 0);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .6);
    color: #FFFFFF;
    font-size: 1rem;

    // Needed as the parent button could be using font-awesome.
    // Can't be put to a BS variable here as we are in assets common (will be
    // overridden for backend but left with 'sans-serif' for frontend to not
    // have an useless frontend rule.
    font-family: sans-serif;
}
