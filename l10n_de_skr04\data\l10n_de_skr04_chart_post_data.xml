<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_chart_de_skr04" model="account.chart.template">
        <field name="code_digits">4</field>
        <field name="property_account_receivable_id" ref="l10n_de_skr04.chart_skr04_1205"/>
        <field name="property_account_payable_id" ref="l10n_de_skr04.chart_skr04_3301"/>
        <field name="property_account_expense_categ_id" ref="l10n_de_skr04.chart_skr04_5400"/>
        <field name="property_account_income_categ_id" ref="l10n_de_skr04.chart_skr04_4400"/>
        <field name="property_tax_payable_account_id" ref="l10n_de_skr04.chart_skr04_3860"/>
        <field name="property_tax_receivable_account_id" ref="l10n_de_skr04.chart_skr04_1421"/>
        <field name="property_advance_tax_payment_account_id" ref="l10n_de_skr04.chart_skr04_3820"/>
        <field name="income_currency_exchange_account_id" ref="l10n_de_skr04.chart_skr04_4840"/>
        <field name="expense_currency_exchange_account_id" ref="l10n_de_skr04.chart_skr04_6880"/>
        <field name="default_pos_receivable_account_id" ref="l10n_de_skr04.chart_skr04_1206" />
        <field name="default_cash_difference_income_account_id" ref="l10n_de_skr04.chart_skr04_9991"/>
        <field name="default_cash_difference_expense_account_id" ref="l10n_de_skr04.chart_skr04_9994"/>
    </record>
</odoo>
