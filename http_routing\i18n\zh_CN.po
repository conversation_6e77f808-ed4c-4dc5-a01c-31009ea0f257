# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* http_routing
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.403
msgid "403: Forbidden"
msgstr "403: 禁止访问"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid ""
"<b>Don't panic.</b> If you think it's our mistake, please send us a message "
"on"
msgstr "<b>莫慌.</b> 如若有闲,请您与我们联系"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.error_message
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "<strong>Error message:</strong>"
msgstr "<strong>错误消息:</strong>"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.500
msgid "Back"
msgstr "返回"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "Error"
msgstr "错误"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "Error 404"
msgstr "错误 404"

#. module: http_routing
#: model:ir.model,name:http_routing.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
#: model_terms:ir.ui.view,arch_db:http_routing.500
msgid "Home"
msgstr "首页"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "Maybe you were looking for one of these <b>popular pages?</b>"
msgstr "凤箫声动，玉壶光转， <b>一夜鱼龙舞。</b>"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.400
msgid "Oops! Something went wrong."
msgstr "糟糕！出问题了。"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "QWeb"
msgstr "QWeb"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.400
msgid "Take a look at the error message below."
msgstr "请查看下面的错误消息。"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "The error occurred while rendering the template"
msgstr "渲染模板时发生错误"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.403
msgid "The page you were looking for could not be authorized."
msgstr "您正在查找的网页可能未被授权。"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "Traceback"
msgstr "跟踪"

#. module: http_routing
#: model:ir.model,name:http_routing.model_ir_ui_view
msgid "View"
msgstr "视图"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "We couldn't find the page you're looking for!"
msgstr "找不到您要访问的页面！"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "and evaluating the following expression:"
msgstr "以及评估表达式："

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "this page"
msgstr "此页"
