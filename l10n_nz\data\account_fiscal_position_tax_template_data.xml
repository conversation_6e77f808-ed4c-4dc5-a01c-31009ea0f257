<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="fiscal_position_os_partner" model="account.fiscal.position.template">
            <field name="name">OS Partner</field>
            <field name="chart_template_id" ref="l10n_nz_chart_template"/>
        </record>

        <record id="fiscal_position_tax_template_os_partner_sale1" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_os_partner"/>
            <field name="tax_src_id" ref="nz_tax_sale_15"/>
            <field name="tax_dest_id" ref="nz_tax_sale_0"/>
        </record>

        <record id="fiscal_position_tax_template_os_partner_sale2" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_os_partner"/>
            <field name="tax_src_id" ref="nz_tax_sale_inc_15"/>
            <field name="tax_dest_id" ref="nz_tax_sale_0"/>
        </record>

        <record id="fiscal_position_tax_template_os_partner_purch1" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_os_partner"/>
            <field name="tax_src_id" ref="nz_tax_purchase_15"/>
            <field name="tax_dest_id" ref="nz_tax_purchase_0"/>
        </record>

        <record id="fiscal_position_tax_template_os_partner_purch2" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_os_partner"/>
            <field name="tax_src_id" ref="nz_tax_purchase_inc_15"/>
            <field name="tax_dest_id" ref="nz_tax_purchase_0"/>
        </record>
</odoo>
