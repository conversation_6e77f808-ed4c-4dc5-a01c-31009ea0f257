# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_customer
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>dev <<EMAIL>>, 2021
# <PERSON>ichanon Jamwutthipreecha, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2021\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/> ทั้งหมด"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"ลิงค์ภายนอก\"/>"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "ประเทศทั้งหมด"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Industries"
msgstr "อุตสาหกรรมทั้งหมด"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__classname
msgid "Bootstrap class to customize the color"
msgstr "Bootstrap class เพื่อปรับแต่งสี"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__can_publish
msgid "Can Publish"
msgstr "สามารถเผยแพร่ได้"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__name
msgid "Category Name"
msgstr "ชื่อหมวดหมู่"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__classname
msgid "Class"
msgstr "คลาส"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "Close"
msgstr "ปิด"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Create a new contact tag"
msgstr "สร้างแท็กผู้ติดต่อใหม่"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__id
msgid "ID"
msgstr "ไอดี"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "Implemented By"
msgstr "ดำเนินการโดย"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__is_published
msgid "Is Published"
msgstr "เผยแพร่แล้ว"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งสุดท้ายโดย"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งสุดท้ายเมื่อ"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage contact tags to better classify them for tracking and analysis "
"purposes."
msgstr ""
"จัดการแท็กผู้ติดต่อเพื่อจัดประเภทได้ดียิ่งขึ้นสำหรับการติดตามและการวิเคราะห์"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No result found"
msgstr "ไม่พบผลลัพธ์"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our References"
msgstr "ข้อมูลอ้างอิงของเรา"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "แท็กพาร์ทเนอร์"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ..."
msgstr ""
"แท็กพาร์ทเนอร์ - "
"แท็กเหล่านี้สามารถใช้บนเว็บไซต์เพื่อค้นหาลูกค้าตามภาคส่วนหรือ ..."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__partner_ids
msgid "Partners"
msgstr "พาร์ทเนอร์"

#. module: website_customer
#: code:addons/website_customer/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
#, python-format
msgid "References"
msgstr "อ้างอิง"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "References by Country"
msgstr "อ้างอิงตามประเทศ"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_industry_list
msgid "References by Industries"
msgstr "อ้างอิงตามอุตสาหกรรม"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "References by Tag"
msgstr "อ้างอิงตามแท็ก"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "ค้นหา"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Search Partner Tag"
msgstr "ค้าหาแท็กพาร์ทเนอร์"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__website_url
msgid "The full URL to access the document through the website."
msgstr "URL แบบเต็มเพื่อเข้าถึงเอกสารผ่านเว็บไซต์"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Trusted by millions worldwide"
msgstr "ได้รับความไว้วางใจจากผู้ใช้นับล้านทั่วโลก"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_published
msgid "Visible on current website"
msgstr "ปรากฏบนเว็บไซต์ปัจจุบัน"

#. module: website_customer
#: model:ir.model,name:website_customer.model_website
msgid "Website"
msgstr "เว็บไซต์"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "แท็กเว็บไซต์"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_url
msgid "Website URL"
msgstr "URL เว็บไซต์"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner__website_tag_ids
#: model:ir.model.fields,field_description:website_customer.field_res_users__website_tag_ids
msgid "Website tags"
msgstr "แท็กเว็บไซต์"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "แผนที่โลก"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "reference(s))"
msgstr "อ้างอิง"
