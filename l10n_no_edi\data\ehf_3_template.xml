<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="export_ehf_3_invoice_partner" inherit_id="account_edi_ubl_bis3.export_bis3_invoice_partner" primary="True">
            <xpath expr="//*[local-name()='PartyTaxScheme']" position="after">
                <cac:PartyTaxScheme xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                    xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
                    <cbc:CompanyID>Foretaksregisteret</cbc:CompanyID>
                    <cac:TaxScheme>
                        <cbc:ID>TAX</cbc:ID>
                    </cac:TaxScheme>
                </cac:PartyTaxScheme>
            </xpath>
        </template>

        <template id="export_ehf_3_invoice" inherit_id="account_edi_ubl_bis3.export_bis3_invoice" primary="True">
            <!-- Only for the supplier, append Foretaksregisteret, see rule NO-R-002 -->
            <xpath expr="//*[local-name()='AccountingSupplierParty']" position="replace">
                <cac:AccountingSupplierParty t-call="l10n_no_edi.export_ehf_3_invoice_partner"
                    xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                    xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2">
                    <t t-set="partner_vals" t-value="supplier_vals"/>
                </cac:AccountingSupplierParty>
            </xpath>
        </template>

    </data>
</odoo>
