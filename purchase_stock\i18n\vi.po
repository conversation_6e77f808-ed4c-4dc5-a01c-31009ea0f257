# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_stock
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Du<PERSON>en <PERSON>hi <<EMAIL>>, 2021
# <PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "% On-Time Delivery"
msgstr "% Giao hàng đúng hẹn"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#: code:addons/purchase_stock/models/stock_rule.py:0
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d ngày"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid ""
"<span attrs=\"{'invisible': [('on_time_rate', '&gt;=', 0)]}\">No On-time "
"Delivery Data</span>"
msgstr ""
"<span attrs=\"{'invisible': [('on_time_rate', '&gt;=', 0)]}\">Không có dữ "
"liệu giao hàng đúng hẹn</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Các giá trị được đặt ở đây là"
" dành riêng cho công ty.\" aria-label=\"Các giá trị được đặt ở đây là dành "
"riêng cho công ty.\" groups=\"base.group_multi_company\"/>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Các giá trị quy định cho công"
" ty này.\" groups=\"base.group_multi_company\"/>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_text\">On-time Rate</span>"
msgstr "<span class=\"o_stat_text\">Tỷ lệ đúng giờ</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">Mua hàng</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_value\">%</span>"
msgstr "<span class=\"o_stat_value\">%</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Điều khoản thương mại:</strong>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Địa chỉ giao hàng</strong>"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Theo cấu hình sản phẩm, số lượng nhận được có thể được tính toán tự động theo cơ chế:\n"
"  - Thủ công: số lượng được điền tay theo dòng\n"
"  - Di chuyển kho: số lượng đến từ các phiếu kho đã xác nhận\n"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_rule__action
msgid "Action"
msgstr "Thực hiện"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_report__avg_receipt_delay
msgid ""
"Amount of time between expected and effective receipt date. Due to a hack "
"needed to calculate this,               every record will show the same "
"average value, therefore only use this as an aggregated value with "
"group_operator=avg"
msgstr ""
"Khoảng thời gian từ ngày nhận dự kiến đến ngày có hiệu lực. Do cần hack để "
"tính toán điều này,               mọi bản ghi sẽ hiển thị cùng một giá trị "
"trung bình, do đó chỉ sử dụng giá trị này làm giá trị tổng hợp với "
"group_operator=avg"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__avg_receipt_delay
msgid "Average Receipt Delay"
msgstr "Độ trễ nhận hàng trung bình"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock.py:0
#: model:ir.model.fields.selection,name:purchase_stock.selection__stock_rule__action__buy
#: model:stock.location.route,name:purchase_stock.route_warehouse0_buy
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_stock_rule
#, python-format
msgid "Buy"
msgstr "Mua"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_pull_id
msgid "Buy rule"
msgstr "Quy tắc mua"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "Buy to Resupply"
msgstr "Mua để tái cung ứng"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__effective_date
msgid "Completion date of the first receipt order."
msgstr "Ngày hoàn thành đơn hàng giao hàng đầu tiên."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Thiết lập cấu hình"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_partner
msgid "Contact"
msgstr "Liên hệ"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "Corresponding receipt not found."
msgstr "Không tìm thấy biên lai tương ứng."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__created_purchase_line_id
msgid "Created Purchase Order Line"
msgstr "Chi tiết đơn hàng đã tạo"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__product_description_variants
msgid "Custom Description"
msgstr "Mô tả tùy chỉnh"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,help:purchase_stock.field_res_config_settings__days_to_purchase
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Days needed to confirm a PO, define when a PO should be validated"
msgstr ""
"Số ngày cần thiết để xác nhận một PO, xác định khi nào một PO cần được xác "
"thực"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__days_to_purchase
#, python-format
msgid "Days to Purchase"
msgstr "Ngày để mua"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_type_id
msgid "Deliver To"
msgstr "Giao hàng đến"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,help:purchase_stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"Phụ thuộc vào phân hệ đã được cài đặt, sẽ cho phép bạn xác định quy tắc cung"
" ứng của sản phẩm: nó sẽ được mua, sản xuất, cung ứng theo yêu cầu, vv."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Destination Location Type"
msgstr "Loại địa điểm đích"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Documentation"
msgstr "Tài liệu"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_dest_ids
msgid "Downstream Moves"
msgstr "Dịch chuyển đầu ra"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "Vận chuyển giao hàng"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__date
msgid "Effective Date"
msgstr "Ngày hiệu lực"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "Effective Date Last Year"
msgstr "Ngày có hiệu lực vào năm ngoái"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "(Các) ngoại lệ xảy ra trên (các) đơn đặt mua:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s):"
msgstr "Các ngoại lệ:"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"For the product %s, the warehouse of the operation type (%s) is inconsistent"
" with the location (%s) of the reordering rule (%s). Change the operation "
"type or cancel the request for quotation."
msgstr ""
"Đối với sản phẩm %s, nhà kho của loại hoạt động (%s) không tương thích với "
"vị trí (%s) của quy tắc tái đặt hàng (%s). Hãy thay đổi loại hoạt động hoặc "
"huỷ yêu cầu báo giá."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__forecasted_issue
msgid "Forecasted Issue"
msgstr "Vấn đề được dự báo"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Generate the draft vendor bill."
msgstr "Tạo hóa đơn nháp của nhà cung cấp."

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Go back to the purchase order to generate the vendor bill."
msgstr "Quay lại đơn mua hàng để tạo hóa đơn của nhà cung cấp."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__id
msgid "ID"
msgstr "ID"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoming_picking_count
msgid "Incoming Shipment count"
msgstr "Số lượng lô hàng đang về"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Incoming Shipments"
msgstr "Nhận hàng"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "Điều khoản thương mại quốc tế"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Bộ điều khoản thương mại Quốc tế (Incoterms) là một chuỗi các điều khoản "
"thương mại được định nghĩa trước để sử dụng trong các giao dịch quốc tế."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__is_shipped
msgid "Is Shipped"
msgstr "Đã giao"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__is_installed_sale
msgid "Is the Sale Module Installed"
msgstr "Phân hệ bán hàng đã cài đặt"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move
msgid "Journal Entry"
msgstr "Bút toán sổ nhật ký"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__last_purchase_date
msgid "Last Purchase"
msgstr "Mua hàng cuối cùng"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Logistics"
msgstr "Kho vận"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Lô/Số serial"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Manual actions may be needed."
msgstr "Có thể cần xử lý thủ công."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Biên độ lỗi cho thời gian chờ của nhà cung cấp. Khi hệ thống tạo đơn đặt "
"hàng để cung ứng lại các sản phẩm, chúng sẽ được lên lịch trước đó nhiều "
"ngày để đối phó với sự chậm trễ của nhà cung cấp không mong muốn."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Quy tắc tồn kho tối thiểu"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Move forward expected request creation date by"
msgstr "Tăng ngày tạo yêu cầu dự kiến thêm "

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Next transfer(s) impacted:"
msgstr "Các điều chuyển tiếp theo bị ảnh hưởng:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "No data yet"
msgstr "Chưa có dữ liệu nào"

#. module: purchase_stock
#: code:addons/purchase_stock/models/account_invoice.py:0
#, python-format
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""
"Hệ thống không thể tạo các bút toán anglo saxon. Tổng giá trị của %s là 0."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_view_graph
msgid "On-Time Delivery"
msgstr "Giao hàng đúng hẹn"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__on_time_rate
msgid "On-Time Delivery Rate"
msgstr "Tỷ lệ giao hàng đúng hẹn"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_on_time
msgid "On-Time Quantity"
msgstr "Số lượng đúng hẹn"

#. module: purchase_stock
#: model:ir.actions.act_window,name:purchase_stock.action_purchase_vendor_delay_report
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "On-time Delivery"
msgstr "Giao hàng đúng hẹn"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "On-time Rate"
msgstr "Tỷ lệ đúng hẹn"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__orderpoint_id
msgid "Orderpoint"
msgstr "Điểm đặt hàng"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_users__on_time_rate
msgid ""
"Over the past 12 months; the number of products received on time divided by "
"the number of ordered products."
msgstr ""
"Trong 12 tháng qua; số lượng sản phẩm nhận được đúng hạn chia cho số lượng "
"sản phẩm đã đặt hàng."

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Process all the receipt quantities."
msgstr "Xử lý số lượng khi nhận tất cả."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__group_id
msgid "Procurement Group"
msgstr "Nhóm cung ứng"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_product
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__product_id
msgid "Product"
msgstr "Sản phẩm"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__category_id
msgid "Product Category"
msgstr "Nhóm sản phẩm"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__supplier_id
msgid "Product Supplier"
msgstr "Nhà cung cấp sản phẩm"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_template
msgid "Product Template"
msgstr "Mẫu sản phẩm"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__propagate_cancel
msgid "Propagate cancellation"
msgstr "Huỷ loan báo"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__purchase_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__purchase_line_ids
msgid "Purchase Lines"
msgstr "Chi tiết mua hàng"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order
msgid "Purchase Order"
msgstr "Đơn mua hàng"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__purchase_order_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__purchase_line_id
msgid "Purchase Order Line"
msgstr "Chi tiết mua hàng"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__purchase_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_production_lot__purchase_order_ids
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "Purchase Orders"
msgstr "Đơn mua hàng"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_report
msgid "Purchase Report"
msgstr "Báo cáo mua hàng"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "Purchase Security Lead Time"
msgstr "Thời gian mua hàng để đảm bảo an toàn"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_production_lot__purchase_order_count
msgid "Purchase order count"
msgstr "Số đơn mua hàng"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receipt"
msgstr "Nhận hàng"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receive Products"
msgstr "Nhận sản phẩm"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Receive the ordered products."
msgstr "Nhận các sản phẩm đã đặt."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "Cách thức số lượng đã nhận"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_ids
msgid "Receptions"
msgstr "Nhận hàng"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Request your vendors to deliver to your customers"
msgstr "Yêu cầu nhà cung cấp của bạn phân phối tới khách hàng của bạn"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_product_product_replenishment
msgid "Requests for quotation"
msgstr "Yêu cầu báo giá"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_ids
msgid "Reservation"
msgstr "Lấy trước"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Trả hàng"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__route_ids
msgid "Routes"
msgstr "Tuyến cung ứng"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Schedule automatically generated request for quotations earlier to avoid "
"delays"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_supplierinfo_replenishment_tree_view
msgid "Set as Supplier"
msgstr "Đặt làm nhà cung cấp"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__show_set_supplier_button
msgid "Show Set Supplier Button"
msgstr "Hiển thị nút đặt nhà cung cấp"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__show_supplier
msgid "Show supplier column"
msgstr "Hiển thị cột nhà cung cấp"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_move
msgid "Stock Move"
msgstr "Hoạt động kho"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order_line__qty_received_method__stock_moves
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_line_view_form_inherit
msgid "Stock Moves"
msgstr "Dịch chuyển kho"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "Báo cáo bổ sung hàng tồn kho"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Tuyến cung ứng"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Báo cáo quy tác kho"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "Thông tin nhà cung cấp bổ sung tồn kho"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Bảng giá nhà cung cấp"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_ids
msgid "Supplierinfo"
msgstr "Thông tin nhà cung cấp"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr "Lĩnh vực kỹ thuật được sử dụng để hiển thị địa chỉ dropship"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__purchase_order_line_ids
msgid "Technical: used to compute quantities."
msgstr "Kỹ thuật: được sử dụng để tính toán số lượng."

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock.py:0
#, python-format
msgid "The following replenishment order has been generated"
msgstr "Đơn hàng bổ sung đã được tạo"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"The quantities on your purchase order indicate less than billed. You should "
"ask for a refund."
msgstr ""
"Số lượng trên đơn mua hàng của bạn ít hơn so với lập hóa đơn. Bạn nên yêu "
"cầu hoàn lại tiền."

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid ""
"There is no matching vendor price to generate the purchase order for product"
" %s (no vendor defined, minimum quantity not reached, dates not valid, ...)."
" Go on the product form and complete the list of vendors."
msgstr ""
"Không có giá nhà cung cấp phù hợp để tạo đơn đặt hàng cho sản phẩm %s (không"
" xác định nhà cung cấp, số lượng tối thiểu không đạt, ngày không hợp lệ, "
"...). Đi vào danh mục sản phẩm và bổ sung danh sách các nhà cung cấp."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"Điều này cho biết thêm một tuyến đường dropshipping để áp dụng trên các sản "
"phẩm để yêu cầu các nhà cung cấp của bạn cung cấp cho khách hàng của bạn. "
"Một sản phẩm để dropship sẽ tạo ra một yêu cầu mua hàng để báo giá khi đơn "
"hàng đã được xác nhận. Đây là một luồng theo yêu cầu. Địa chỉ giao hàng được"
" yêu cầu sẽ là địa chỉ giao hàng của khách hàng chứ không phải là nhà kho "
"của bạn."

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__picking_type_id
msgid "This will determine operation type of incoming shipment"
msgstr "Điều này sẽ xác định loại hoạt động của lô hàng đến"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"Those dates couldn’t be modified accordingly on the receipt %s which had "
"already been validated."
msgstr ""
"Không thể điều chỉnh những ngày đó theo biên nhận %s đã được xác nhận."

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "Those dates have been updated accordingly on the receipt %s."
msgstr "Đã cập nhật những ngày đó theo biên nhận %s."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_total
msgid "Total Quantity"
msgstr "Tổng số lượng"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_picking
msgid "Transfer"
msgstr "Điều chuyển"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"Unable to cancel purchase order %s as some receptions have already been "
"done."
msgstr ""
"Không thể huỷ đơn mua hàng %s khi mà đã nhận một phần hay toàn bộ hàng hoá "
"theo đơn hàng này."

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Validate the receipt of all ordered products."
msgstr "Xác thực việc nhận tất cả các sản phẩm đã đặt."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_warehouse_orderpoint_tree_editable_inherited_mrp
msgid "Vendor"
msgstr "Nhà cung cấp"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_vendor_delay_report
msgid "Vendor Delay Report"
msgstr "Báo cáo độ trễ của nhà cung cấp"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "Vendor Lead Time"
msgstr "Thời gian cung cấp an toàn"

#. module: purchase_stock
#: model_terms:ir.actions.act_window,help:purchase_stock.action_purchase_vendor_delay_report
msgid "Vendor On-time Delivery analysis"
msgstr "Phân tích giao hàng đúng giờ của nhà cung cấp"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
msgid "Vendor of this product"
msgstr "Nhà cung cấp của sản phẩm này"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__picking_type_id
msgid "Warehouse"
msgstr "Kho"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr "Khi sản phẩm được mua, chúng có thể được giao tới kho này"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> a request for quotation is "
"created to fulfill the need.<br/>Note: This rule will be used in combination"
" with the rules<br/>of the reception route(s)"
msgstr ""
"Khi cần sản phẩm trong <b>%s</b>, <br/> một yêu cầu báo giá sẽ được tạo để "
"đáp ứng nhu cầu. <br/>Lưu ý: Quy tắc này sẽ được sử dụng cùng với những quy "
"tắc <br/>về (các) tuyến biên nhận."

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"You cannot decrease the ordered quantity below the received quantity.\n"
"Create a return first."
msgstr ""
"Bạn không thể giảm số lượng đặt hàng nhỏ hơn số lượng nhận được.\n"
" Đầu tiên hãy tạo phiếu trả hàng."

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "You must set a Vendor Location for this partner %s"
msgstr "Bạn phải thiết lập địa điểm nhà cung cấp cho đối tác này %s"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "ngày"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "of"
msgstr "của"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "ordered instead of"
msgstr "đã đặt thay vì"
