<?xml version="1.0"?>
<odoo>
    <record id="view_crm_lead2opportunity_partner" model="ir.ui.view">
        <field name="name">crm.lead2opportunity.partner.form</field>
        <field name="model">crm.lead2opportunity.partner</field>
        <field name="arch" type="xml">
            <form string="Convert to Opportunity">
                <group name="name">
                    <field name="name" widget="radio"/>
                </group>
                <group string="Assign this opportunity to">
                    <field name="user_id" domain="[('share', '=', False)]"/>
                    <field name="team_id" options="{'no_open': True, 'no_create': True}" kanban_view_ref="%(sales_team.crm_team_view_kanban)s"/>
                </group>
                <group string="Opportunities" attrs="{'invisible': [('name', '!=', 'merge')]}">
                    <field name="lead_id" invisible="1"/>
                    <field name="duplicated_lead_ids" colspan="2" nolabel="1">
                        <tree>
                            <field name="create_date" widget="date"/>
                            <field name="name"/>
                            <field name="type"/>
                            <field name="contact_name"/>
                            <field name="country_id" invisible="context.get('invisible_country', True)" options="{'no_open': True, 'no_create': True}"/>
                            <field name="email_from"/>
                            <field name="stage_id"/>
                            <field name="user_id"/>
                            <field name="team_id" kanban_view_ref="%(sales_team.crm_team_view_kanban)s"/>
                        </tree>
                    </field>
                </group>
                <group name="action" attrs="{'invisible': [('name', '!=', 'convert')]}" string="Customer" col="1">
                    <field name="action" nolabel="1" widget="radio"/>
                    <group col="2">
                        <field name="partner_id" widget="res_partner_many2one" context="{'res_partner_search_mode': 'customer', 'show_vat': True}" attrs="{'required': [('action', '=', 'exist')], 'invisible':[('action','!=','exist')]}"/>
                    </group>
                </group>
                <footer>
                    <button name="action_apply" string="Create Opportunity" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_crm_lead2opportunity_partner" model="ir.actions.act_window">
        <field name="name">Convert to opportunity</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">crm.lead2opportunity.partner</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_crm_lead2opportunity_partner"/>
        <field name="target">new</field>
    </record>
</odoo>
