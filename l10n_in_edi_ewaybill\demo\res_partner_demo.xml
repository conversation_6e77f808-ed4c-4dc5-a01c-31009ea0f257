<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_partner_registered_customer_ewaybill" model="res.partner">
        <field name="name">Registered Customer (E-Waybill)</field>
        <field eval="[(6, 0, [ref('l10n_in.res_partner_category_registered')])]" name="category_id"/>
        <field name="is_company">1</field>
        <field name="l10n_in_gst_treatment">regular</field>
        <field name="street">19, Ground Floor</field>
        <field name="street2">Survey Road</field>
        <field name="city">Dehradun</field>
        <field name="zip">248001</field>
        <field name="state_id" ref="base.state_in_uk"/>
        <field name="country_id" ref="base.in"/>
        <field name="vat">05AAACH6605F1Z0</field>
    </record>

</odoo>
