# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_links
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-06-13 17:56+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Peru) (http://www.transifex.com/odoo/odoo-9/language/"
"es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.share_page_menu
msgid "<span title=\"Track this page to count clicks\">Share this Page</span>"
msgstr ""
"<span title=\"Track this page to count clicks\">Compartir esta Página</span>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Campaign</strong>"
msgstr "<strong>Campaña</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Medium</strong>"
msgstr "<strong>Medio</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Original URL</strong>"
msgstr "<strong>URL Original</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Redirected URL</strong>"
msgstr "<strong>URL Redireccionado</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Source</strong>"
msgstr "<strong>Fuente</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Tracked Link</strong>"
msgstr "<strong>Enlace de Seguimiento</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "All Time"
msgstr "Todo el Tiempo"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Campaign <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" title=\"Defines the context of your link. It might be an "
"event you want to promote or a special promotion.\"/>"
msgstr ""
"Campaña <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" title=\"Define el contexto de su enlace. Puede ser una "
"evento que quiere promover o una oferta especial.\"/>"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:37
#, python-format
msgid "Copy"
msgstr "Copiar"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:350
#, python-format
msgid "Generating link..."
msgstr "Generando enlace..."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Get tracked link"
msgstr "Obtener enlace de seguimiento"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Month"
msgstr "Último Mes"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Week"
msgstr "Última Semana"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Link Tracker"
msgstr "Seguidor de Enlace"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Medium <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-placement="
"\"top\" title=\"Defines the medium used to share your link. It might be an "
"email, or a Facebook Ads for instance.\"/>"
msgstr ""
"Medio <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-placement="
"\"top\" title=\"Define el medio usado para compartir su enlace. Puede ser un "
"email, on una Publicidad de Facebook por ejemplo.\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Most Clicked"
msgstr "Más Cliqueado"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Newest"
msgstr "Más Reciente"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Recently Used"
msgstr "Recientemente Usado"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Share this page with a <strong>short link</strong> that includes "
"<strong>analytics trackers</strong>."
msgstr ""
"Compartir esta página con un <strong>enlace corto</strong> que incluye "
"<strong>analíticas de seguimiento</strong>."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Source <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-placement="
"\"top\" title=\"Defines the source from which your traffic will come from, "
"Facebook or Twitter for instance.\"/>"
msgstr ""
"Fuente <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-placement="
"\"top\" title=\"Define la fuente desde la cual su tráfico va a provenir, "
"Facebook o Twitter por ejemplo.\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Statistics"
msgstr "Estadísticas"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:38
#, python-format
msgid "Stats"
msgstr "Estadísticas"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#, fuzzy
msgid ""
"Those trackers can be used in Google Analytics to track clicks and visitors, "
"or in Odoo reports to track opportunities and related revenues."
msgstr ""
"Estos seguimientos pueden ser usados por Google Analytics para seguir clicks "
"y visitantes, o en Odoo reports para seguir oportunidades y ganancias "
"relacionadas."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "URL"
msgstr "URL"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:216
#, python-format
msgid "Unable to get recent links"
msgstr "Imposible obtener enlaces recientes"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:239
#, python-format
msgid "You don't have any recent links."
msgstr "Usted no tiene ningún enlace reciente"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Your tracked links"
msgstr "Sus enlaces de seguimiento"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:23
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "cancel"
msgstr "cancelar"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:8
#, python-format
msgid "clicks"
msgstr "clicks"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "copy"
msgstr "copiar"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:259
#, python-format
msgid "e.g. Newsletter, Social Network, .."
msgstr "ejem. Hoja Informativa, Red Social, .."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:256
#, python-format
msgid "e.g. Promotion of June, Winter Newsletter, .."
msgstr "ejem. Promoción de Junio, Hoja Informativa de Invierno, .."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:262
#, python-format
msgid "e.g. Search Engine, Website page, .."
msgstr "ejem. Motor de Búsqueda, Página de Sitio Web, .."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "e.g. https://www.odoo.com/page/contactus"
msgstr "ejem. https://www.odoo.com/page/contactus"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:22
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "ok"
msgstr "ok"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:22
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "or"
msgstr "o"
