# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * event_sale
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:17+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Apply"
msgstr "Virkja"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_availability
msgid "Available Seat"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_available
msgid "Available Seats"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before confirming"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_event_ticket__seats_max
msgid ""
"Define the number of available tickets. If you have too much registrations "
"you will not be able to sell tickets anymore. Set 0 to ignore this rule set "
"as unlimited."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr "Tölvupóstur"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
msgid "Event"
msgstr "Event"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_type
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__event_type_id
msgid "Event Category"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_form_view
msgid "Event Name"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:product.product,name:event_sale.product_product_event
#: model:product.template,name:event_sale.product_product_event_product_template
msgid "Event Registration"
msgstr ""

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_event__event_ticket_ids
#: model:ir.model.fields,field_description:event_sale.field_event_registration__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
msgid "Event Ticket"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_form_view
msgid "Event's Ticket"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_product_template_form
#: model:product.category,name:event_sale.product_category_events
msgid "Events"
msgstr "Events"

#. module: event_sale
#: code:addons/event_sale/models/event.py:254
#, python-format
msgid "Free"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr "Auðkenni"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_template__event_ok
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ok
msgid ""
"If checked this product automatically creates an event registration at the "
"sales order confirmation."
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_account_invoice
msgid "Invoice"
msgstr "Reikningur"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__is_expired
msgid "Is Expired"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_template__event_ok
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ok
msgid "Is an Event Ticket"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: event_sale
#: selection:event.event.ticket,seats_availability:0
msgid "Limited"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_max
msgid "Maximum Available Seats"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:249
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
#, python-format
msgid "Name"
msgstr "Nafn"

#. module: event_sale
#: code:addons/event_sale/models/event.py:205
#, python-format
msgid "No more available seats for this ticket"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:152
#, python-format
msgid "No more available seats for this ticket type."
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:250
#, python-format
msgid "None"
msgstr "Ekkert"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_ticket_form
msgid "Origin"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:259
#, python-format
msgid "Paid"
msgstr "Greitt"

#. module: event_sale
#: code:addons/event_sale/models/event.py:260
#, python-format
msgid "Payment"
msgstr "Greiðsla"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr "Sími"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price
msgid "Price"
msgstr "Verð"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce
msgid "Price Reduce"
msgstr "Price Reduce"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Price Reduce Tax inc"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_product
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__product_id
msgid "Product"
msgstr "Vara"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
msgid "Product Template"
msgstr "Sniðmát vöru"

#. module: event_sale
#: code:addons/event_sale/models/event.py:20
#: code:addons/event_sale/models/event.py:33
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
#, python-format
msgid "Registration"
msgstr "Registration"

#. module: event_sale
#: code:addons/event_sale/models/event.py:34
#: code:addons/event_sale/models/event.py:51
#, python-format
msgid "Registration for %s"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__registration_ids
msgid "Registrations"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_reserved
msgid "Reserved Seats"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
msgid "Sale Order"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__deadline
msgid "Sales End"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
msgid "Sales Order"
msgstr "Sölupöntun"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr "Sales Order Line"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_used
msgid "Seats Used"
msgstr ""

#. module: event_sale
#: model:event.type,name:event_sale.event_type_data_sale
msgid "Sell Online"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Skip"
msgstr "Sleppa"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
msgid "Source Sales Order"
msgstr ""

#. module: event_sale
#: model:event.event.ticket,name:event_sale.event_0_ticket_1
#: model:event.event.ticket,name:event_sale.event_2_ticket_1
msgid "Standard"
msgstr "Standard"

#. module: event_sale
#: code:addons/event_sale/models/event.py:257
#, python-format
msgid "The registration must be paid"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:250
#, python-format
msgid "Ticket"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_ticket_search
msgid "Ticket Type"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:157
#, python-format
msgid "Ticket cannot belong to both the event category and the event itself."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_type__use_ticketing
msgid "Ticketing"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_type__event_ticket_ids
#: model_terms:ir.ui.view,arch_db:event_sale.event_type_view_form_inherit_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Tickets"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:256
#, python-format
msgid "To pay"
msgstr "To pay"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr ""

#. module: event_sale
#: model:product.product,uom_name:event_sale.product_product_event
#: model:product.template,uom_name:event_sale.product_product_event_product_template
msgid "Unit(s)"
msgstr ""

#. module: event_sale
#: selection:event.event.ticket,seats_availability:0
msgid "Unlimited"
msgstr ""

#. module: event_sale
#: model:event.event.ticket,name:event_sale.event_0_ticket_2
#: model:event.event.ticket,name:event_sale.event_2_ticket_2
msgid "VIP"
msgstr ""

#. module: event_sale
#: model:product.product,weight_uom_name:event_sale.product_product_event
#: model:product.template,weight_uom_name:event_sale.product_product_event_product_template
msgid "kg"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please give details about the registrations"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "reserved +"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "unconfirmed"
msgstr ""
