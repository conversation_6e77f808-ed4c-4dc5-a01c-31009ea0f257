<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_acquirer_form" model="ir.ui.view">
        <field name="name">PayPal Acquirer Form</field>
        <field name="model">payment.acquirer</field>
        <field name="inherit_id" ref="payment.payment_acquirer_form"/>
        <field name="arch" type="xml">
            <xpath expr='//group[@name="acquirer"]' position='inside'>
                <group attrs="{'invisible': [('provider', '!=', 'paypal')]}">
                    <field name="paypal_email_account"
                           attrs="{'required':[('provider', '=', 'paypal'), ('state', '!=', 'disabled')]}"/>
                    <!-- This field should no longer be used but is kept in debug mode for the time
                         being, until we are sure that the verification protocol of IPN can be used
                         for DPT notifications -->
                    <field name="paypal_pdt_token"/>
                    <field name="paypal_use_ipn"
                           attrs="{'required':[('provider', '=', 'paypal'), ('state', '!=', 'disabled')]}"/>
                    <a href="https://www.odoo.com/documentation/15.0/applications/general/payment_acquirers/paypal.html"
                       target="_blank"
                       colspan="2">
                        How to configure your paypal account?
                    </a>
                </group>
            </xpath>
        </field>
    </record>

    <record id="payment_transaction_form" model="ir.ui.view">
        <field name="name">PayPal Transaction Form</field>
        <field name="model">payment.transaction</field>
        <field name="inherit_id" ref="payment.payment_transaction_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='acquirer_reference']" position="after">
                <field name="paypal_type"
                       readonly="1"
                       attrs="{'invisible': [('provider', '!=', 'paypal')]}"
                       groups="base.group_no_one"/>
            </xpath>
        </field>
    </record>

</odoo>
