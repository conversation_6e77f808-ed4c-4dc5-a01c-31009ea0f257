# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mollie
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_mollie
#: model_terms:ir.ui.view,arch_db:payment_mollie.payment_acquirer_form
msgid "API Key"
msgstr "Chiave API"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Canceled payment with status: %s"
msgstr "Pagamento annullato con stato: %s"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Impossibile stabilire la connessione all'API."

#. module: payment_mollie
#: model:account.payment.method,name:payment_mollie.payment_method_mollie
#: model:ir.model.fields.selection,name:payment_mollie.selection__payment_acquirer__provider__mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__mollie_api_key
msgid "Mollie API Key"
msgstr "Mollie API Key"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Nessuna transazione trovata corrispondente al riferimento %s."

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Sistema di pagamento"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_account_payment_method
msgid "Payment Methods"
msgstr "Metodi di pagamento"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transazione di pagamento"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__provider
msgid "Provider"
msgstr "Fornitore"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "Dati ricevuti con stato di pagamento non valido: %s"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"Il fornitore di servizi di pagamento da utilizzare con questo acquirente"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__mollie_api_key
msgid ""
"The Test or Live API Key depending on the configuration of the acquirer"
msgstr ""
"La chiave API test o live che dipende dalla configurazione del fornitore"
