<?xml version="1.0" ?>
<odoo>
    <data>

        <record id="res_partner_view_form_inherit_mail" model="ir.ui.view">
            <field name="name">res.partner.view.form.inherit.mail</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='email']" position="replace">
                    <field name="is_blacklisted" invisible="1"/>
                    <label for="email" class="oe_inline"/>
                    <div class="o_row o_row_readonly">
                        <button name="mail_action_blacklist_remove" class="fa fa-ban text-danger"
                            title="This email is blacklisted for mass mailings. Click to unblacklist."
                            type="object" context="{'default_email': email}" groups="base.group_user"
                            attrs="{'invisible': [('is_blacklisted', '=', False)]}"/>
                        <field name="email" widget="email" context="{'gravatar_image': True}" attrs="{'required': [('user_ids','!=', [])]}"/>
                    </div>
                </xpath>
                <xpath expr="//sheet" position="after">
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </xpath>
            </field>
        </record>

        <record id="res_partner_view_kanban_inherit_mail" model="ir.ui.view">
            <field name="name">res.partner.view.kanban.inherit.mail</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.res_partner_kanban_view"/>
            <field name="arch" type="xml">
                <field name="type" position="after">
                    <field name="activity_state"/>
                </field>
                <xpath expr="//span[hasclass('oe_kanban_partner_links')]" position="after">
                    <field name="activity_ids" widget="kanban_activity"/>
                </xpath>
            </field>
        </record>

       <record id="res_partner_view_search_inherit_mail" model="ir.ui.view">
            <field name="name">res.partner.view.search.inherit.mail</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_res_partner_filter"/>
            <field name="arch" type="xml">
                    <filter name="inactive" position="after">
                        <filter invisible="1" string="Late Activities" name="activities_overdue"
                                domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                                help="Show all records which has next action date is before today"/>
                        <filter invisible="1" string="Today Activities" name="activities_today"
                                domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                        <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                                domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                        <separator/>
                    </filter>
            </field>
        </record>

        <record id="res_partner_view_tree_inherit_mail" model="ir.ui.view">
            <field name="name">res.partner.view.tree.inherit.mail</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='user_id']" position="after">
                    <field name="activity_ids" optional="show" widget="list_activity"/>
                </xpath>
            </field>
        </record>

        <record id="res_partner_view_activity" model="ir.ui.view">
            <field name="name">res.partner.activity</field>
            <field name="model">res.partner</field>
            <field name="arch" type="xml">
                <activity string="Contacts">
                    <field name="id"/>
                    <templates>
                        <div t-name="activity-box">
                            <img t-att-src="activity_image('res.partner', 'avatar_128', record.id.raw_value)" role="img" t-att-title="record.id.value" t-att-alt="record.id.value"/>
                            <div>
                                <field name="name" display="full"/>
                                <field name="parent_id" muted="1" display="full"/>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <!-- Add mail-defined activity view to standard action on partners to manage activities related to partner model -->
        <record id="base.action_partner_form" model="ir.actions.act_window">
            <field name="view_mode">kanban,tree,form,activity</field>
        </record>
        <record id="base.action_partner_customer_form" model="ir.actions.act_window">
            <field name="view_mode">kanban,tree,form,activity</field>
        </record>
        <record id="base.action_partner_supplier_form" model="ir.actions.act_window">
            <field name="view_mode">kanban,tree,form,activity</field>
        </record>

        <!--  Replace the default mass-mailing wizard in base with the composition wizard -->
        <record id="action_partner_mass_mail" model="ir.actions.act_window">
            <field name="name">Send email</field>
            <field name="res_model">mail.compose.message</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context" eval="{
                'default_composition_mode': 'mass_mail',
                'default_partner_to': '{{ object.id or \'\' }}',
                'default_use_template': False,
                'default_reply_to_force_new': True,
            }"/>
            <field name="binding_model_id" ref="base.model_res_partner"/>
            <field name="binding_view_types">list</field>
        </record>

    </data>
</odoo>
