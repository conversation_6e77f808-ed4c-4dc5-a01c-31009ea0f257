<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Filters for Dynamic Filter -->
        <record id="dynamic_snippet_latest_blog_post_filter" model="ir.filters">
            <field name="name">Latest Blog Posts</field>
            <field name="model_id">blog.post</field>
            <field name="user_id" eval="False" />
            <field name="domain">[('post_date', '&lt;=', context_today())]</field>
            <field name="sort">['post_date desc']</field>
            <field name="action_id" ref="website.action_website"/>
        </record>
        <record id="dynamic_snippet_most_viewed_blog_post_filter" model="ir.filters">
            <field name="name">Most Viewed Blog Posts</field>
            <field name="model_id">blog.post</field>
            <field name="user_id" eval="False" />
            <field name="domain">[('post_date', '&lt;=', context_today()), ('visits', '!=', False)]</field>
            <field name="sort">['visits desc']</field>
            <field name="action_id" ref="website.action_website"/>
        </record>
        <!-- Dynamic Filter -->
        <record id="dynamic_filter_latest_blog_posts" model="website.snippet.filter">
            <field name="name">Latest Blog Posts</field>
            <field name="filter_id" ref="website_blog.dynamic_snippet_latest_blog_post_filter"/>
            <field name="field_names">name,teaser,subtitle</field>
            <field name="limit" eval="16"/>
        </record>
        <record id="dynamic_filter_most_viewed_blog_posts" model="website.snippet.filter">
            <field name="name">Most Viewed Blog Posts</field>
            <field name="filter_id" ref="website_blog.dynamic_snippet_most_viewed_blog_post_filter"/>
            <field name="field_names">name,teaser,subtitle</field>
            <field name="limit" eval="16"/>
        </record>
    </data>
</odoo>
