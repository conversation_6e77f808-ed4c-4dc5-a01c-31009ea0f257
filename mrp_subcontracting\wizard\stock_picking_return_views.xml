<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_stock_return_picking_form_subcontracting" model="ir.ui.view">
        <field name="name">Return lines</field>
        <field name="model">stock.return.picking</field>
        <field name="inherit_id" ref="stock.view_stock_return_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='location_id']" position="before">
                <field name="subcontract_location_id" invisible="1"/>
                <field name="original_location_id" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='location_id']" position="attributes">
                <attribute name="domain">
                    ['|', '|', ('id', '=', original_location_id), ('return_location', '=', True), ('id', '=', subcontract_location_id)]
                </attribute>
            </xpath>
        </field>
    </record>
</odoo>
