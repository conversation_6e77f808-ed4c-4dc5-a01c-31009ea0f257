<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_tabs" name="Tabs">
    <section class="s_tabs pt48 pb48" data-vcss="001">
        <div class="container">
            <div class="s_tabs_main">
                <div class="s_tabs_nav mb-3">
                    <ul class="nav nav-pills" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="nav_tabs_link_1" data-toggle="tab" href="#nav_tabs_content_1" role="tab" aria-controls="nav_tabs_content_1" aria-selected="true">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="nav_tabs_link_2" data-toggle="tab" href="#nav_tabs_content_2" role="tab" aria-controls="nav_tabs_content_2" aria-selected="false">Profile</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="nav_tabs_link_3" data-toggle="tab" href="#nav_tabs_content_3" role="tab" aria-controls="nav_tabs_content_3" aria-selected="false">Contact</a>
                        </li>
                    </ul>
                </div>
                <div class="s_tabs_content tab-content">
                    <div class="tab-pane fade show active" id="nav_tabs_content_1" role="tabpanel" aria-labelledby="nav_tabs_link_1">
                        <div class="oe_structure oe_empty">
                            <section class="s_text_block">
                                <div class="container s_allow_columns">
                                    <p>Write one or two paragraphs describing your product or services.</p>
                                </div>
                            </section>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="nav_tabs_content_2" role="tabpanel" aria-labelledby="nav_tabs_link_2">
                        <div class="oe_structure oe_empty">
                            <section class="s_text_block">
                                <div class="container s_allow_columns">
                                    <p>To be successful your content needs to be useful to your readers.</p>
                                </div>
                            </section>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="nav_tabs_content_3" role="tabpanel" aria-labelledby="nav_tabs_link_3">
                        <div class="oe_structure oe_empty">
                            <section class="s_text_block">
                                <div class="container s_allow_columns">
                                    <p>Start with the customer – find out what they want and give it to them.</p>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<template id="s_tabs_options" inherit_id="website.snippet_options">
    <xpath expr="." position="inside">
        <div data-js="NavTabs" data-selector="section.s_tabs">
            <we-button data-add-item="" data-item=".tab-pane.active" data-no-preview="true" class="o_we_text_success ml-0" title="Add Tab" aria-label="Add Tab">
                <i class="fa fa-fw fa-plus"/>
            </we-button>
            <we-button data-remove-item="" data-item=".tab-pane.active" data-name="remove_tab_opt" data-no-preview="true" class="o_we_text_danger mr-3" title="Remove Tab" aria-label="Remove Tab">
                <i class="fa fa-fw fa-minus"/>
            </we-button>
        </div>
        <div data-js="NavTabsStyle" data-selector="section" data-target=".s_tabs_main">
            <we-select string="Style">
                <we-button data-set-style="tabs" data-name="tabs_opt" data-trigger="horizontal_opt">Tabs</we-button>
                <we-button data-set-style="pills" data-name="pills_opt" data-trigger="tabs_color_opt" data-trigger-value="">Buttons</we-button>
            </we-select>
            <we-colorpicker string="Tabs color" data-dependencies="tabs_opt" data-name="tabs_color_opt" data-select-style="true" data-css-property="background-color" data-color-prefix="bg-"/>
            <we-select string="Direction" data-dependencies="pills_opt">
                <we-button data-set-direction="horizontal" data-name="horizontal_opt">Horizontal</we-button>
                <we-button data-set-direction="vertical" data-trigger="left_alignment_opt">Vertical</we-button>
            </we-select>
            <we-select string="Alignment" data-apply-to=".s_tabs_nav:first .nav" data-dependencies="horizontal_opt">
                <we-button data-select-class="" data-name="left_alignment_opt">Left</we-button>
                <we-button data-select-class="justify-content-center">Center</we-button>
                <we-button data-select-class="justify-content-end">Right</we-button>
            </we-select>
            <we-select string="Fill and justify" data-apply-to=".s_tabs_nav:first .nav" data-dependencies="horizontal_opt">
                <we-button data-select-class="">Regular</we-button>
                <we-button data-select-class="nav-fill">Full Width</we-button>
                <we-button data-select-class="nav-justified">Equal Widths</we-button>
            </we-select>
            <we-divider/>
            <we-button-group string="Slide" data-apply-to=".s_tabs_content:first">
                <we-button class="fa fa-fw fa-long-arrow-left" title="Slide Left" data-select-class="s_tabs_slide_left"/>
                <we-button class="fa fa-fw fa-long-arrow-up" title="Slide Up" data-select-class="s_tabs_slide_up"/>
                <we-button class="fa fa-fw fa-long-arrow-down" title="Slide Down" data-select-class="s_tabs_slide_down"/>
                <we-button class="fa fa-fw fa-long-arrow-right" title="Slide Right" data-select-class="s_tabs_slide_right"/>
                <we-button class="fa fa-fw fa-ban" title="No Slide Effect" data-select-class=""/>
            </we-button-group>
        </div>
    </xpath>
</template>

<record id="website.s_tabs_001_scss" model="ir.asset">
    <field name="name">Tabs 001 SCSS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website/static/src/snippets/s_tabs/001.scss</field>
</record>

</odoo>
