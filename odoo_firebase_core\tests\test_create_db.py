from odoo.tests.common import TransactionCase
import odoo_firebase_core as odoo_firebase


class TestOdooFirebase(TransactionCase):

    def test_create_database_to_firebase(self):
        # Crear una base de datos de prueba
        db_name = 'Test-firebase-db'
        self.env['ir.config_parameter'].sudo().set_param('database.uuid', db_name)
        self.env['ir.config_parameter'].sudo().set_param('database.name', db_name)

        #Obtener los datos de la base de datos
        database_data = odoo_firebase.get_database_data(self.env)

        # Enviar los datos a Firebase
        firebase_config = {
            'apiKey': 'AIzaSyDZSPJtfSEXPcBrogK0KDaKXVS2lHUTCkg',
            'authDomain': '<EMAIL>.g?serviceaccount.com',
            'databaseURL': 'https://console.firebase.google.com/project/test-db-odoo/firestore/data/~2Flead~2F1',
            'storageBucket': 'gs://test-db-odoo.appspot.com'
        }
        firebase_database = odoo_firebase.FirebaseDatabase(firebase_config)
        result = firebase_database.set_data(db_name, database_data)

        # Verificar que los datos se hayan enviado correctamente a Firebase
        self.assertTrue(result, "Los datos no se enviaron correctamente a Firebase")