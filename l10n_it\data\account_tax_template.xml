<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <!-- Sales (Vendita) ...................................................................... -->
    <record id="22v" model="account.tax.template">
        <field name="description">22v</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">22%</field>
        <field name="sequence">1</field>
        <field name="amount">22</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_22"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="10v" model="account.tax.template">
        <field name="description">10v</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">10%</field>
        <field name="sequence">2</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
                }),
            ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="5v" model="account.tax.template">
        <field name="description">5v</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">5%</field>
        <field name="active">False</field>
        <field name="sequence">3</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="4v" model="account.tax.template">
        <field name="description">4v</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">4%</field>
        <field name="sequence">4</field>
        <field name="active">False</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="00v" model="account.tax.template">
        <field name="description">00v</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">0%</field>
        <field name="sequence">5</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_fuori"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- Purchase of Goods (Acquisti Merce) ................................................... -->
    <record id="22am" model="account.tax.template">
        <field name="description">22am</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">22% G</field>
        <field name="sequence">6</field>
        <field name="amount">22</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_22"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
    </record>
    <record id="10am" model="account.tax.template">
        <field name="description">10am</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">10% G</field>
        <field name="sequence">7</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
    </record>
    <record id="5am" model="account.tax.template">
        <field name="description">5am</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">5% G</field>
        <field name="sequence">8</field>
        <field name="active">False</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
    </record>
    <record id="4am" model="account.tax.template">
        <field name="description">4am</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">4% G</field>
        <field name="sequence">9</field>
        <field name="active">False</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
    </record>
    <record id="00am" model="account.tax.template">
        <field name="description">00am</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">0% G</field>
        <field name="sequence">10</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_fuori"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- Purchase of Services (Acquisti Servizi) .............................................. -->
    <record id="22as" model="account.tax.template">
        <field name="description">22as</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">22% S</field>
        <field name="sequence">11</field>
        <field name="amount">22</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_22"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
    </record>
    <record id="10as" model="account.tax.template">
        <field name="description">10as</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">10% S</field>
        <field name="sequence">12</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
    </record>
    <record id="5as" model="account.tax.template">
        <field name="description">5as</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">5% S</field>
        <field name="sequence">13</field>
        <field name="active">False</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
    </record>
    <record id="4as" model="account.tax.template">
        <field name="description">4as</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">4% S</field>
        <field name="sequence">14</field>
        <field name="active">False</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
        ]"/>
    </record>
    <record id="00as" model="account.tax.template">
        <field name="description">00as</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">0% S</field>
        <field name="sequence">15</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_fuori"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- Export ............................................................................... -->
    <record id="00eu" model="account.tax.template">
        <field name="description">00eu</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">0% EU</field>
        <field name="sequence">16</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_fuori"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- No tax ............................................................................... -->
    <record id="00art15v" model="account.tax.template">
        <field name="description">00art15v</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">0% Art.15</field>
        <field name="sequence">17</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_imp_esc_art_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="00art15a" model="account.tax.template">
        <field name="description">00art15a</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">0% Art.15</field>
        <field name="sequence">18</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_imp_esc_art_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- Reverse Charge Services .............................................................. -->
    <record id="22rcs" model="account.tax.template">
        <field name="description">22rcs</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">22% S RC</field>
        <field name="sequence">19</field>
        <field name="amount">22</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_22"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="10rcs" model="account.tax.template">
        <field name="description">10rcs</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">10% S RC</field>
        <field name="sequence">20</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="5rcs" model="account.tax.template">
        <field name="description">5rcs</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">5% S RC</field>
        <field name="sequence">21</field>
        <field name="active">False</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="4rcs" model="account.tax.template">
        <field name="description">4rcs</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">4% S RC</field>
        <field name="sequence">22</field>
        <field name="active">False</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="00rcs" model="account.tax.template">
        <field name="description">00rcs</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">0% S RC</field>
        <field name="sequence">23</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_fuori"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>

    <!-- Reverse Charge Goods (Merci) ......................................................... -->
    <record id="22rcm" model="account.tax.template">
        <field name="description">22rcm</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">22% G RC</field>
        <field name="sequence">24</field>
        <field name="amount">22</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_22"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="10rcm" model="account.tax.template">
        <field name="description">10rcm</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">10% G RC</field>
        <field name="sequence">25</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="5rcm" model="account.tax.template">
        <field name="description">5rcm</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">5% G RC</field>
        <field name="sequence">26</field>
        <field name="active">False</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="4rcm" model="account.tax.template">
        <field name="description">4rcm</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">4% G RC</field>
        <field name="sequence">27</field>
        <field name="active">False</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="00rcm" model="account.tax.template">
        <field name="description">00rcm</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">0% G RC</field>
        <field name="sequence">28</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_fuori"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>

    <!-- Reverse Charge 22% VAT deposit ....................................................... -->
    <record id="22rcd" model="account.tax.template">
        <field name="description">22rcd</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">22% G Deposit</field>
        <field name="sequence">29</field>
        <field name="amount">22</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_22"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="10rcd" model="account.tax.template">
        <field name="description">10rcd</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">10% G Deposit</field>
        <field name="sequence">30</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="5rcd" model="account.tax.template">
        <field name="description">5rcd</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">5% G Deposit</field>
        <field name="sequence">31</field>
        <field name="active">False</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="4rcd" model="account.tax.template">
        <field name="description">4rcd</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">4% G Deposit</field>
        <field name="sequence">32</field>
        <field name="active">False</field>
        <field name="amount">4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>
    <record id="00rcd" model="account.tax.template">
        <field name="description">00rcd</field>
        <field name="chart_template_id" ref="l10n_it_chart_template_generic"/>
        <field name="name">0% G Deposit</field>
        <field name="sequence">33</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="price_include">False</field>
        <field name="tax_group_id" ref="tax_group_fuori"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'plus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'minus_report_line_ids': [ref('tax_report_line_vp4')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_line_vp3'), ref('tax_report_line_vj3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('1601'),
                'minus_report_line_ids': [ref('tax_report_line_vp5')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2601'),
                'plus_report_line_ids': [ref('tax_report_line_vp4')],
            }),
        ]"/>
    </record>

</odoo>
