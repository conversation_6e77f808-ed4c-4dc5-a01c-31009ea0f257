# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_links
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:54+0000\n"
"PO-Revision-Date: 2017-09-20 09:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:53
#, python-format
msgid " clicks"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:104
#, python-format
msgid " countries"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:57
#, python-format
msgid "# of clicks"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.share_page_menu
msgid "<span title=\"Track this page to count clicks\">Track this Page</span>"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Campaign</strong>"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Medium</strong>"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Original URL</strong>"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Redirected URL</strong>"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Source</strong>"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Tracked Link</strong>"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "All Time"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Campaign <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" title=\"Defines the context of your link. It might be an "
"event you want to promote or a special promotion.\"/>"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:37
#, python-format
msgid "Copy"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:358
#, python-format
msgid "Generating link..."
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Get tracked link"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Month"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Week"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Link Tracker"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Medium <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" title=\"Defines the medium used to share your link. It "
"might be an email, or a Facebook Ads for instance.\"/>"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Most Clicked"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Newest"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Recently Used"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Share this page with a <strong>short link</strong> that includes "
"<strong>analytics trackers</strong>."
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Source <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" title=\"Defines the source from which your traffic will "
"come from, Facebook or Twitter for instance.\"/>"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Statistics"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:38
#, python-format
msgid "Stats"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:229
#: code:addons/website_links/static/src/js/website_links_charts.js:230
#: code:addons/website_links/static/src/js/website_links_charts.js:231
#, python-format
msgid "There is no data to show"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Those trackers can be used in Google Analytics to track clicks and visitors,"
" or in Odoo reports to track opportunities and related revenues."
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "URL"
msgstr "URL"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:225
#, python-format
msgid "Unable to get recent links"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:99
#, python-format
msgid "Undefined"
msgstr "Nedefinisan"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:248
#, python-format
msgid "You don't have any recent links."
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Your tracked links"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:23
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "cancel"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:8
#, python-format
msgid "clicks"
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "copy"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:268
#, python-format
msgid "e.g. Newsletter, Social Network, .."
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:265
#, python-format
msgid "e.g. Promotion of June, Winter Newsletter, .."
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:271
#, python-format
msgid "e.g. Search Engine, Website page, .."
msgstr ""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "e.g. https://www.odoo.com/page/contactus"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:22
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "ok"
msgstr ""

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:22
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "or"
msgstr "ili"
