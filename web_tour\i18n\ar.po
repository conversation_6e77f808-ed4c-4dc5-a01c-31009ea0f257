# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr "<strong><b>عمل رائع!</b> لقد تخطيت كافة مراحل هذه الجولة.</strong>"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Click here to go to the next step."
msgstr "اضغط هنا للانتقال إلى الخطوة التالية. "

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Click on the <i>Home icon</i> to navigate across apps."
msgstr "اضغط على <i>أيقونة الصفحة الرئيسية</i> للتنقل بين التطبيقات. "

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_id
msgid "Consumed by"
msgstr "تم استهلاكه بواسطة "

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
#, python-format
msgid "Disable Tours"
msgstr "تعطيل الجولات "

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
msgid "ID"
msgstr "المُعرف"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "القائمة"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Name"
msgstr "الاسم"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Onboarding tours"
msgstr "جولات التمهيد "

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Open bugger menu."
msgstr "فتح القائمة "

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Path"
msgstr "مسار"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Scroll to reach the next step."
msgstr "قم بالتمرير للوصول إلى الخطوة التالية. "

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Sequence"
msgstr "التسلسل "

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Start"
msgstr "بدء"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
#, python-format
msgid "Start Tour"
msgstr "بدء الجولة "

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Start tour"
msgstr "بدء جولة"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Test"
msgstr "اختبار"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Test tour"
msgstr "جولة تجريبية "

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Testing tours"
msgstr "الجولات التجريبية "

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr "نصيحة "

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Tour name"
msgstr "اسم الجولة "

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.js:0
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
#, python-format
msgid "Tours"
msgstr "جولات"
