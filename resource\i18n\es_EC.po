# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * resource
#
# Translators:
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-02-10 17:31+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: resource
#: code:addons/resource/resource.py:686
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_active
msgid "Active"
msgstr "Activo"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Días cerrados"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_code
msgid "Code"
msgstr "Código"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource_company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Compañía"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource_create_uid
msgid "Created by"
msgstr "Creado por:"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource_create_date
msgid "Created on"
msgstr "Creado en"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_dayofweek
msgid "Day of Week"
msgstr "Día de la Semana"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Define el horario del recurso."

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""
"Define horas laborables y tabla de tiempo que debe ser programada a los "
"miembros del proyecto"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
msgid "Duration"
msgstr "Duración"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_time_efficiency
msgid "Efficiency Factor"
msgstr "Factor de Eficiencia"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_date_to
msgid "End Date"
msgstr "Fecha Final"

#. module: resource
#: constraint:resource.calendar.leaves:0
msgid "Error! leave start-date must be lower then leave end-date."
msgstr ""
"¡Error! La fecha inicial de ausencia debe ser anterior a la fecha final de "
"ausencia."

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Friday"
msgstr "Viernes"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Agrupar por"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Horas"

#. module: resource
#: selection:resource.resource,resource_type:0
msgid "Human"
msgstr "Humano"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_id
#: model:ir.model.fields,field_description:resource.field_resource_resource_id
msgid "ID"
msgstr "ID"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves_resource_id
msgid ""
"If empty, this is a generic holiday for the company. If a resource is set, "
"the holiday/leave is only for this resource"
msgstr ""
"Si está vacío, es un día festivo para toda la compañía. Si hay un recurso "
"seleccionado, el festivo/ausencia es solo para ese recurso."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si el campo activo se desmarca, permite ocultar el registro del recurso sin "
"eliminarlo."

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Inactive"
msgstr "Inactivo"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar___last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance___last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves___last_update
#: model:ir.model.fields,field_description:resource.field_resource_resource___last_update
msgid "Last Modified on"
msgstr "Fecha de modificación"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource_write_uid
msgid "Last Updated by"
msgstr "Ultima Actualización por"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource_write_date
msgid "Last Updated on"
msgstr "Actualizado en"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Leave Detail"
msgstr "Detalle ausencia"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Leave Month"
msgstr "Mes de salida"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leave_ids
msgid "Leaves"
msgstr "Ausencias"

#. module: resource
#: selection:resource.resource,resource_type:0
msgid "Material"
msgstr "Maquinaria"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Monday"
msgstr "Lunes"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_name
#: model:ir.model.fields,field_description:resource.field_resource_resource_name
msgid "Name"
msgstr "Nombre"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Reason"
msgstr "Motivo"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_user_id
msgid "Related user name for the resource to manage its access."
msgstr "Usuario relacionado con el recurso para gestionar su acceso."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Resource"
msgstr "Recurso"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Calendar"
msgstr "Calendario recurso"

#. module: resource
#: model:ir.model,name:resource.model_resource_resource
msgid "Resource Detail"
msgstr "Detalle de recurso"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Leaves"
msgstr "Ausencias de recursos"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_resource_type
msgid "Resource Type"
msgstr "Tipo de Recurso"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_calendar_id
msgid "Resource's Calendar"
msgstr "Calendario del recurso"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Recursos"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Leaves"
msgstr "Ausencias de recursos"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""
"Los recursos le permiten crear y gestionar los recursos que deben participar "
"en una cierta fase de un proyecto. También puede definir su nivel de "
"eficiencia y carga de trabajo en base a sus horas de trabajo semanales."

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Saturday"
msgstr "Sábado"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Buscar recurso"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Leaves"
msgstr "Buscar ausencias en periodos de trabajo"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Buscar horario de trabajo"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_date_from
msgid "Start Date"
msgstr "Fecha Inicio"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance_hour_from
msgid "Start and End time of working."
msgstr "Inicio y fin del tiempo laborable"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_date_from
msgid "Starting Date"
msgstr "Fecha inicial"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Leave by Month"
msgstr "Fecha de inicio de salida por mes"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Sunday"
msgstr "Domingo"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_time_efficiency
msgid ""
"This field depict the efficiency of the resource to complete tasks. e.g  "
"resource put alone on a phase of 5 days with 5 tasks assigned to him, will "
"show a load of 100% for this phase by default, but if we put a efficiency of "
"200%, then his load will only be 50%."
msgstr ""
"Este campo representan la eficiencia de los recursos para completar las "
"tareas. por ejemplo, si puso recursos  en una fase de 5 días con 5 tareas "
"asignadas, se mostrará una carga de 100% para esta fase por defecto, pero si "
"ponemos una eficiencia del 200%, entonces su carga sólo será del 50%."

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Thursday"
msgstr "Jueves"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Tuesday"
msgstr "Martes"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Tipo"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Usuario"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Wednesday"
msgstr "Miércoles"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Detalle del trabajo"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_hour_from
msgid "Work from"
msgstr "Trabajar desde"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_hour_to
msgid "Work to"
msgstr "Trabajar hasta"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_manager
msgid "Workgroup Manager"
msgstr "Administrador del Grupo"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Period"
msgstr "Período de trabajo"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_resource_calendar_id
#: model:ir.ui.menu,name:resource.menu_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
msgid "Working Time"
msgstr "Horario de trabajo"
