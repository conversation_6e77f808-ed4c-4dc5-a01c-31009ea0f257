<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="quant_search_view" model="ir.ui.view">
        <field name="name">stock.quant.search</field>
        <field name="model">stock.quant</field>
        <field eval="10" name="priority"/>
        <field name="arch" type="xml">
            <search string="Quants">
                <field name="product_id"/>
                <field name="location_id"/>
                <field name="user_id"/>
                <field name="inventory_date"/>
                <field name="product_categ_id"/>
                <field name="package_id" groups="stock.group_tracking_lot"/>
                <field name="lot_id" groups="stock.group_production_lot"/>
                <field name="owner_id" groups="stock.group_tracking_owner"/>
                <group expand='0' string='Filters'>
                    <filter name='internal_loc' string="Internal Locations" domain="[('location_id.usage','=', 'internal')]"/>
                    <filter name='transit_loc' string="Transit Locations" domain="[('location_id.usage' ,'=', 'transit')]"/>
                    <separator/>
                    <filter name="on_hand" string="On Hand" domain="[('on_hand', '=', True)]"/>
                    <filter name="to_count" string="To Count" domain="[('inventory_date', '&lt;=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter name="to_apply" string="To Apply" domain="[('inventory_quantity_set', '=', True)]"/>
                    <separator/>
                    <filter name="negative" string="Negative Stock" domain="[('quantity', '&lt;', 0.0)]"/>
                    <filter name="reserved" string="Reservations" domain="[('reserved_quantity', '&gt;', 0.0)]"/>
                    <separator/>
                    <filter name="filter_in_date" date="in_date"/>
                    <separator/>
                    <filter name="my_count" string="My Counts" domain="[('user_id', '=', uid)]"/>
                </group>
                <group expand='0' string='Group by...'>
                    <filter string='Product' name="productgroup" context="{'group_by': 'product_id'}"/>
                    <filter string='Location' name="locationgroup" domain="[]" context="{'group_by': 'location_id'}"/>
                    <filter string='Owner' name="owner" context="{'group_by': 'owner_id'}" groups="stock.group_tracking_owner"/>
                    <filter string='Lot/Serial Number' name="Lot_Serial_number" context="{'group_by': 'lot_id'}" groups="stock.group_production_lot"/>
                    <filter string='Package' name="package" domain="[]" context="{'group_by': 'package_id'}" groups="stock.group_tracking_lot"/>
                    <filter string='Company' name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
               </group>
            </search>
        </field>
    </record>

    <record model="ir.ui.view" id="view_stock_quant_form_editable">
        <field name="name">stock.quant.form.editable</field>
        <field name="model">stock.quant</field>
        <field eval="11" name="priority"/>
        <field name="arch" type="xml">
            <form string="Inventory Valuation" create="false" edit="false" delete="false">
                <sheet>
                    <group>
                        <group>
                            <field name="tracking" invisible="1"/>
                            <field name="product_id" readonly="0" options="{'no_create': True}"/>
                            <field name="location_id" readonly="0" options="{'no_create': True}"/>
                            <field name="lot_id" groups="stock.group_production_lot"
                                attrs="{'readonly': [('tracking', 'not in', ['serial', 'lot'])], 'required': [('tracking', '!=', 'none')]}"
                                context="{'default_product_id': product_id, 'default_company_id': company_id}"/>
                            <field name="package_id" readonly="0" groups="stock.group_tracking_lot"/>
                            <field name="owner_id" readonly="0" groups="stock.group_tracking_owner" options="{'no_create': True}"/>
                            <field name='company_id' groups="base.group_multi_company"/>
                        </group>
                        <group>
                            <label for="quantity" string="Quantity On Hand"/>
                            <div class="o_row">
                                <field name="quantity"/>
                                <field name="product_uom_id" groups="uom.group_uom"/>
                            </div>
                            <label for="available_quantity" string="Available Quantity"/>
                            <div class="o_row">
                                <field name="available_quantity"/>
                                <field name="product_uom_id" groups="uom.group_uom"/>
                            </div>
                            <label for="reserved_quantity" string="Quantity Reserved"/>
                            <div class="o_row">
                                <field name="reserved_quantity"/>
                                <field name="product_uom_id" groups="uom.group_uom"/>
                            </div>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Inventory Report + Update Quantity (via product) view -->
    <record model="ir.ui.view" id="view_stock_quant_tree_editable">
        <field name="name">stock.quant.tree.editable</field>
        <field name="model">stock.quant</field>
        <field eval="10" name="priority"/>
        <field name="arch" type="xml">
            <tree editable="bottom"
                  create="1" edit="1" js_class="singleton_list"
                  sample="1">
                <field name="id" invisible="1"/>
                <field name="tracking" invisible="1"/>
                <field name="product_id" attrs="{'readonly': [('id', '!=', False)]}"
                       readonly="context.get('single_product', False)" force_save="1"
                       options="{'no_create': True}"/>
                <field name="product_categ_id" optional="hide"/>
                <field name="location_id" attrs="{'readonly': [('id', '!=', False)]}"
                       invisible="context.get('hide_location', False)"
                       options="{'no_create': True}"/>
                <field name="lot_id" groups="stock.group_production_lot"
                       attrs="{
                            'readonly': ['|', ('id', '!=', False), ('tracking', 'not in', ['serial', 'lot'])],
                            'required': [('tracking', '!=', 'none')]
                       }"
                       invisible="context.get('hide_lot', False)"
                       context="{'default_product_id': product_id, 'default_company_id': company_id}"/>
                <field name="package_id" groups="stock.group_tracking_lot"
                       attrs="{'readonly': [('id', '!=', False)]}"/>
                <field name="owner_id" groups="stock.group_tracking_owner"
                       attrs="{'readonly': [('id', '!=', False)]}"
                       options="{'no_create': True}"/>
                <field name="inventory_quantity_auto_apply" string="On Hand Quantity" readonly="0"/>
                <field name="available_quantity" optional="show"/>
                <field name="product_uom_id" groups="uom.group_uom"/>
                <field name='company_id' groups="base.group_multi_company" optional="show"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="view_stock_quant_tree">
        <field name="name">stock.quant.tree</field>
        <field name="model">stock.quant</field>
        <field eval="10" name="priority"/>
        <field name="arch" type="xml">
            <tree string="Inventory Valuation" js_class="inventory_report_list"
                  create="0" delete="0">
                <field name="product_id" invisible="context.get('single_product', False)"/>
                <field name="location_id" invisible="context.get('hide_location', False)"/>
                <field name="lot_id" groups="stock.group_production_lot"
                       invisible="context.get('hide_lot', False)"/>
                <field name="package_id" groups="stock.group_tracking_lot"/>
                <field name="owner_id" groups="stock.group_tracking_owner"/>
                <field name="available_quantity"/>
                <field name="quantity" string="On Hand Quantity"/>
                <field name="product_uom_id" groups="uom.group_uom"/>
                <field name='company_id' groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="view_stock_quant_pivot">
        <field name="name">stock.quant.pivot</field>
        <field name="model">stock.quant</field>
        <field name="arch" type="xml">
            <pivot string="Inventory" sample="1">
                <field name="product_id" type="row"/>
                <field name="location_id" type="col"/>
                <field name="quantity" type="measure"/>
            </pivot>
        </field>
    </record>

    <record id="stock_quant_view_graph" model="ir.ui.view">
        <field name="name">stock.quant.graph</field>
        <field name="model">stock.quant</field>
        <field name="arch" type="xml">
            <graph string="Stock Quant" sample="1">
                <field name="location_id"/>
                <field name="quantity" type="measure"/>
            </graph>
        </field>
    </record>

    <record model="ir.actions.server" id="action_view_quants"> <!-- Menu + python -->
        <field name="name">Inventory</field>
        <field name="model_id" ref="model_stock_quant"/>
        <field name="state">code</field>
        <field name="groups_id" eval="[(4, ref('stock.group_stock_user'))]"/>
        <field name="code">
            action = model.with_context(
                search_default_internal_loc=1,
                search_default_productgroup=1,
                search_default_locationgroup=1,
            ).action_view_quants()
        </field>
    </record>

    <record model="ir.actions.act_window" id="dashboard_open_quants"> <!-- Used in dashboard -->
        <field name="name">Update Quantity</field>
        <field name="context">{'search_default_internal_loc': 1, 'search_default_productgroup':1, 'search_default_locationgroup':1, 'inventory_mode':True}</field>
        <field name="res_model">stock.quant</field>
        <field name="view_id" ref="view_stock_quant_tree_editable"/>
    </record>

    <record model="ir.actions.act_window" id="location_open_quants"> <!-- Used in location -->
        <field name="context">{'search_default_productgroup': 1}</field>
        <field name="domain">[('location_id', 'child_of', active_ids)]</field>
        <field name="name">Current Stock</field>
        <field name="res_model">stock.quant</field>
        <field name="view_id" ref="view_stock_quant_tree"/>
    </record>

    <record id="quant_package_search_view" model="ir.ui.view">
        <field name="name">stock.quant.package.search</field>
        <field name="model">stock.quant.package</field>
        <field eval="10" name="priority"/>
        <field name="arch" type="xml">
            <search string="Package">
                <field name="name" string="Package Name"/>
                <field name="location_id"/>
                <field name="package_type_id"/>
                <group  expand='0' string='Group by...'>
                   <filter string='Location' name="location" domain="[]" context="{'group_by' : 'location_id'}"/>
                   <filter string='Package Type' name="package_type" domain="[]" context="{'group_by' : 'package_type_id'}"/>
               </group>
            </search>
        </field>
    </record>

    <record model="ir.ui.view" id="view_quant_package_form">
        <field name="name">stock.quant.package.form</field>
        <field name="model">stock.quant.package</field>
        <field eval="10" name="priority"/>
        <field name="arch" type="xml">
            <form string="Package">
                <header>
                    <button name="unpack" string="Unpack" type="object"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" name="action_view_picking"
                        string="Package Transfers" type="object"
                        widget="statinfo" icon="fa-arrows-v"/>
                    </div>
                    <div class="oe_title">
                        <label for="name" string="Package Reference"/>
                        <h1><field name="name" class="oe_inline" placeholder="e.g. PACK0000007"/></h1>
                    </div>
                    <group>
                        <group id='stock.package_location_group'>
                            <field name="package_type_id"/>
                            <field name='company_id' groups="base.group_multi_company"/>
                            <field name='owner_id' groups="stock.group_tracking_owner"/>
                            <field name="location_id" options="{'no_create': True}"/>
                        </group>
                    </group>
                    <separator string="Content"/>
                    <field name="quant_ids">
                        <tree>
                            <field name="product_id"/>
                            <field name="lot_id" groups="stock.group_production_lot"/>
                            <field name="quantity"/>
                            <field name="product_uom_id" groups="uom.group_uom"/>
                        </tree>
                    </field>
                </sheet>
            </form>
        </field>
    </record>

    <record model="ir.ui.view" id="view_quant_package_tree">
        <field name="name">stock.quant.package.tree</field>
        <field name="model">stock.quant.package</field>
        <field eval="10" name="priority"/>
        <field name="arch" type="xml">
            <tree string="Package" sample="1">
                <field name="display_name"/>
                <field name="package_type_id"/>
                <field name="location_id" options="{'no_create': True}"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="view_quant_package_kanban">
        <field name="name">stock.quant.package.kanban</field>
        <field name="model">stock.quant.package</field>
        <field name="arch" type="xml">
            <kanban sample="1">
                <templates>
                <field name="name"/>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click">
                            <div class="o_kanban_record_top mb0">
                                <div class="col-6">
                                    <strong class="o_kanban_record_title"><field name="name"/></strong>
                                </div>
                                <div class="col-6">
                                    <field name="package_type_id"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_package_view">
        <field name="context">{}</field>
        <field name="name">Packages</field>
        <field name="res_model">stock.quant.package</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new package
            </p><p>
                Packages are usually created via transfers (during pack operation) and can contain different products.
                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again.
            </p>
        </field>
    </record>

    <!-- Inventory Adjustments view -->
    <record model="ir.ui.view" id="view_stock_quant_tree_inventory_editable">
        <field name="name">stock.quant.inventory.tree.editable</field>
        <field name="model">stock.quant</field>
        <field eval="10" name="priority"/>
        <field name="arch" type="xml">
            <tree default_order="location_id, inventory_date, product_id, package_id, lot_id, owner_id" decoration-warning='is_outdated' editable="bottom" create="1" edit="1" import="1" js_class="singleton_list" sample="1">
                <header>
                    <button name="stock.action_stock_inventory_adjustement_name" groups="stock.group_stock_manager" type="action" string="Apply"/>
                    <button name="action_reset" type="object" string="Clear"/>
                    <button name="stock.action_stock_request_count" groups="stock.group_stock_manager" type="action" string="Request a Count"/>
                </header>
                <field name="id" invisible="1"/>
                <field name="is_outdated" invisible="1"/>
                <field name="tracking" invisible="1"/>
                <field name="inventory_quantity_set" invisible="1"/>
                <field name="location_id" domain="[('usage', 'in', ['internal', 'transit'])]" attrs="{'readonly': [('id', '!=', False)]}" invisible="context.get('hide_location', False)" options="{'no_create': True}"/>
                <field name="product_id" attrs="{'readonly': [('id', '!=', False)]}" readonly="context.get('single_product', False)" force_save="1" options="{'no_create': True}"/>
                <field name="product_categ_id" optional="hide"/>
                <field name="lot_id" groups="stock.group_production_lot"
                    attrs="{'readonly': ['|', ('id', '!=', False), ('tracking', 'not in', ['serial', 'lot'])]}"
                    invisible="context.get('hide_lot', False)"
                    context="{'default_product_id': product_id, 'default_company_id': company_id}"/>
                <field name="package_id" groups="stock.group_tracking_lot" attrs="{'readonly': [('id', '!=', False)]}"/>
                <field name="owner_id" groups="stock.group_tracking_owner" attrs="{'readonly': [('id', '!=', False)]}" options="{'no_create': True}"/>
                <field name="available_quantity" string="Available Quantity" decoration-danger="available_quantity &lt; 0" optional="hidden"/>
                <field name="quantity" optional="show" string="On Hand Quantity"/>
                <field name="product_uom_id" groups="uom.group_uom" string="UoM"/>
                <field name="inventory_quantity" widget="counted_quantity_widget"/>
                <field name="inventory_diff_quantity" string="Difference"  attrs="{'invisible': [('inventory_quantity_set', '=', False)]}" decoration-muted="inventory_diff_quantity == 0" decoration-danger="inventory_diff_quantity &lt; 0" decoration-success="inventory_diff_quantity &gt; 0" decoration-bf="inventory_diff_quantity != 0"/>
                <field name="inventory_date" optional="show"/>
                <field name="user_id" string="User" optional="show"/>
                <field name='company_id' groups="base.group_multi_company" optional="hide"/>
                <button name="action_inventory_history" type="object" class="btn btn-link text-info" icon="fa-history" string="History"/>
                <button name="action_apply_inventory" groups="stock.group_stock_manager" type="object" string="Apply" class="btn btn-link" icon="fa-save" attrs="{'invisible': [('inventory_quantity_set', '=', False)]}"/>
                <button name="action_set_inventory_quantity" type="object" string="Set" class="btn btn-link" icon="fa-bullseye" attrs="{'invisible': [('inventory_quantity_set', '=', True)]}"/>
                <button name="action_set_inventory_quantity_to_zero" type="object" string="Clear" class="btn text-warning" icon="fa-times" attrs="{'invisible': [('inventory_quantity_set', '=', False)]}"/>
            </tree>
        </field>
    </record>

    <record model="ir.actions.server" id="action_view_set_quants_tree">
        <field name="name">Set</field>
        <field name="model_id" ref="model_stock_quant"/>
        <field name="binding_model_id" ref="stock.model_stock_quant"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="groups_id" eval="[(4, ref('stock.group_stock_user'))]"/>
        <field name="code">
            action = records.action_set_inventory_quantity()
        </field>
    </record>

    <record model="ir.actions.server" id="action_view_inventory_tree"> <!-- Menu + python -->
        <field name="name">Inventory</field>
        <field name="model_id" ref="model_stock_quant"/>
        <field name="state">code</field>
        <field name="groups_id" eval="[(4, ref('stock.group_stock_user'))]"/>
        <field name="code">
            action = model.action_view_inventory()
        </field>
    </record>

    <menuitem id="menu_action_inventory_tree" name="Inventory Adjustments" parent="menu_stock_warehouse_mgmt" sequence="30" action="action_view_inventory_tree" />
    <menuitem id="menu_package" name="Packages" parent="menu_stock_inventory_control" action="action_package_view" groups="stock.group_tracking_lot" sequence="102"/>
    <menuitem id="menu_valuation" name="Inventory Report"
              parent="stock.menu_warehouse_report" sequence="100"
              action="action_view_quants"/>
</odoo>
