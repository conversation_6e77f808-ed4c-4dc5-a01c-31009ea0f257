# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-02 15:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_count
msgid "# Contracts"
msgstr "# Contratos"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_resource_calendar__contracts_count
msgid "# Contracts using it"
msgstr "# Contratos utilizándolo"

#. module: hr_contract
#: code:addons/hr_contract/report/hr_contract_history.py:0
#, python-format
msgid "%s's Contracts History"
msgstr "Historial de contratos de %s"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('calendar_mismatch', '=', False), ('state', '!=', 'open')]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                                    </span>\n"
"                                    <span class=\"o_calendar_warning_tooltip text-danger\">\n"
"                                        Calendar Mismatch : The employee's calendar does not match this contract's calendar. This could lead to unexpected behaviors.\n"
"                                    </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('calendar_mismatch', '=', False), ('state', '!=', 'open')]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                                    </span>\n"
"                                    <span class=\"o_calendar_warning_tooltip text-danger\">\n"
"                                        Discordancia de calendario: El calendario del empleado no concuerda con el calendario de este contrato. Esto podría llevar a comportamientos inesperados.\n"
"                                    </span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span attrs=\"{'invisible': [('calendar_mismatch', '=', False)]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                            </span>\n"
"                            <span class=\"o_calendar_warning_tooltip text-danger\">\n"
"                                Calendar Mismatch : The employee's calendar does not match its current contract calendar. This could lead to unexpected behaviors.\n"
"                            </span>"
msgstr ""
"<span attrs=\"{'invisible': [('calendar_mismatch', '=', False)]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                            </span>\n"
"                            <span class=\"o_calendar_warning_tooltip text-danger\">\n"
"                                Discordancia de calendarios: el calendario del empleado no concuerda con su calendario de contrato actual. Esto podría llevar a comportamientos inesperados.\n"
"                            </span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span class=\"o_stat_text text-danger\" attrs=\"{'invisible' : [('contract_warning', '=', False)]}\" title=\"In Contract Since\">\n"
"                                    In Contract Since\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text text-danger\" attrs=\"{'invisible' : [('contract_warning', '=', False)]}\" title=\"En contrato desde\">\n"
"                                    En contrato desde\n"
"                                </span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span class=\"o_stat_text text-danger\">\n"
"                                    Contracts\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text text-danger\">\n"
"                                    Contratos\n"
"                                </span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span class=\"o_stat_text text-success\" attrs=\"{'invisible' : "
"[('contract_warning', '=', True)]}\" title=\"In Contract Since\"> In "
"Contract Since</span>"
msgstr ""
"<span class=\"o_stat_text text-success\" attrs=\"{'invisible' : "
"[('contract_warning', '=', True)]}\" title=\"En contrato desde\"> En "
"contrato desde</span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ mes</span>"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__active
msgid "Active"
msgstr "Activo"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__active_employee
msgid "Active Employee"
msgstr "Empleado activo"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Active Employees"
msgstr "Empleados activos"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad de Excepción"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícono de tipo de actvidad"

#. module: hr_contract
#: model:res.groups,name:hr_contract.group_hr_contract_manager
msgid "Administrator"
msgstr "Administrador"

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:0
#, python-format
msgid ""
"An employee can only have one contract at the same time. (Excluding Draft and Cancelled contracts).\n"
"\n"
"Employee: %(employee_name)s"
msgstr ""
"Un empleado puede tener solo un contrato al mismo tiempo. (Excluyendo contratos en estado de borrador y cancelados).\n"
"\n"
"Empleado: %(employee_name)s"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Archived"
msgstr "Archivado"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_attachment_count
msgid "Attachment Count"
msgstr "Nº de archivos adjuntos"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_res_users__bank_account_id
msgid "Bank Account Number"
msgstr "Número de Cuenta Bancaria"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__calendar_mismatch
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__calendar_mismatch
msgid "Calendar Mismatch"
msgstr "Discordancia de calendarios"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__cancel
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_id
msgid "Company"
msgstr "Compañía"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__vehicle
#: model:ir.model.fields,field_description:hr_contract.field_res_users__vehicle
msgid "Company Vehicle"
msgstr "Vehículo de la empresa"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_country_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_country_id
msgid "Company country"
msgstr "País de la compañía"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
msgid "Contract"
msgstr "Contrato"

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:0
#, python-format
msgid ""
"Contract %(contract)s: start date (%(start)s) must be earlier than contract "
"end date (%(end)s)."
msgstr ""
"Contrato %(contract)s: la fecha de inicio (%(start)s) debe ser anterior a la"
" fecha de finalización (%(end)s)."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contracts_count
msgid "Contract Count"
msgstr "Recuento de contratos"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Details"
msgstr "Detalles del contrato"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
msgid "Contract End Date"
msgstr "Fecha de finalización de contrato"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract History"
msgstr "Historial de contratos"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract Name"
msgstr "Nombre del contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Reference"
msgstr "Referencia de contrato"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Start Date"
msgstr "Fecha de inicio del contrato"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_type_id
msgid "Contract Type"
msgstr "Tipo de contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_wage
msgid "Contract Wage"
msgstr "Salario del contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_warning
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_employee_view_search
msgid "Contract Warning"
msgstr "Advertencia de contrato"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_pending
#: model:mail.message.subtype,description:hr_contract.mt_department_contract_pending
msgid "Contract about to expire"
msgstr "Contrato a punto de vencer"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_close
msgid "Contract expired"
msgstr "Contrato vencido"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_history
msgid "Contract history"
msgstr "Histórico de contratación"

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_department_contract_pending
msgid "Contract to Renew"
msgstr "Contrato a renovar"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_form_action
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_ids
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract_history
#: model:ir.ui.menu,name:hr_contract.menu_human_resources_configuration_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_activity
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.resource_calendar_view_form
msgid "Contracts"
msgstr "Contratos"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_to_review_view_list_action
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Contracts to Review"
msgstr "Contratos por revisar"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__under_contract_state
msgid "Contractual Status"
msgstr "Estado contractual"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__country_id
msgid "Country"
msgstr "País"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__country_code
msgid "Country Code"
msgstr "Código de país"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Create"
msgstr "Crear"

#. module: hr_contract
#: model_terms:ir.actions.act_window,help:hr_contract.action_hr_contract
msgid "Create a new contract"
msgstr "Crear un nuevo contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__currency_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Current Contract"
msgstr "Contrato Actual"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Current Contracts"
msgstr "Contratos actuales"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee__contract_id
msgid "Current contract of the employee"
msgstr "Contrato actual del empleado"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Currently Under Contract"
msgstr "Actualmente bajo contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__default_resource_calendar_id
msgid "Default Working Hours"
msgstr "Horas laborales predeterminadas"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__department_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__department_id
msgid "Department"
msgstr "Departamento"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Asistente de salida"

#. module: hr_contract
#: code:addons/hr_contract/wizard/hr_departure_wizard.py:0
#, python-format
msgid ""
"Departure date can't be earlier than the start date of current contract."
msgstr ""
"La fecha de salida no puede ser anterior a la fecha de inicio del contrato "
"actual."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Edit Contract"
msgstr "Editar Contrato"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employed"
msgstr "Contratado"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__employee_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employee"
msgstr "Empleado"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_ids
msgid "Employee Contracts"
msgstr "Contratos de los empleados"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Employee Information"
msgstr "Información del empleado"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_res_users__bank_account_id
msgid "Employee bank salary account"
msgstr "Cuenta bancaria de salario del empleado."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__wage
msgid "Employee's monthly gross wage."
msgstr "Salario bruto mensual por empleado."

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_list_action
msgid "Employees"
msgstr "Empleados"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_end
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_end
msgid "End Date"
msgstr "Fecha de finalización"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_end
msgid "End date of the contract (if it's a fixed-term contract)."
msgstr ""
"Fecha de finalización del contrato (si es un contrato por tiempo "
"determinado)."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__trial_date_end
msgid "End date of the trial period (if there is one)."
msgstr "Fecha de finalización del periodo de prueba (si existe)."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__trial_date_end
msgid "End of Trial Period"
msgstr "Fin del periodo de prueba"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__close
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__close
#: model:mail.message.subtype,name:hr_contract.mt_contract_close
msgid "Expired"
msgstr "Vencido"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_public__first_contract_date
msgid "First Contract Date"
msgstr "Primera fecha del contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__done
msgid "Green"
msgstr "Verde"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__normal
msgid "Grey"
msgstr "Gris"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: hr_contract
#: model:ir.actions.server,name:hr_contract.ir_cron_data_contract_update_state_ir_actions_server
#: model:ir.cron,cron_name:hr_contract.ir_cron_data_contract_update_state
#: model:ir.cron,name:hr_contract.ir_cron_data_contract_update_state
msgid "HR Contract: update state"
msgstr "Contrato laboral: actualizar estado"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__hr_responsible_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__hr_responsible_id
msgid "HR Responsible"
msgstr "Responsable de RRHH"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_hired
msgid "Hire Date"
msgstr "Fecha de contratación"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__id
msgid "ID"
msgstr "ID"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__is_under_contract
msgid "Is Currently Under Contract"
msgstr "Actualmente bajo contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__job_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__job_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__kanban_state
msgid "Kanban State"
msgstr "Estado de kanban"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Late Activities"
msgstr "Actividades atrasadas"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjuntos principales"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error
msgid "Message Delivery error"
msgstr "Error de Envío de Mensaje"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Monthly Wage"
msgstr "Salario mensual"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mi fecha límite de actividad"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__name
msgid "Name"
msgstr "Nombre"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__draft
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__draft
msgid "New"
msgstr "Nuevo"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "No Contracts"
msgstr "Sin contratos"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Not Running"
msgstr "No en ejecución"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__blocked
msgid "Not Under Contract"
msgstr "No bajo contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__notes
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Notes"
msgstr "Notas"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes sin leer"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__hr_responsible_id
msgid "Person responsible for validating the employee's contracts."
msgstr "Persona responsable de validar los contratos del empleado."

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee_public
msgid "Public Employee"
msgstr "Empleado público"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__blocked
msgid "Red"
msgstr "Rojo"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Reference Working Time"
msgstr "Tiempo de trabajo de referencia"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_employee_public_view_form
msgid "Related User"
msgstr "Usuario relacionado"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_resource_calendar
msgid "Resource Working Time"
msgstr "Tiempo de Trabajo de Recursos"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__open
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__open
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Running"
msgstr "En proceso"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Running Contracts"
msgstr "Contratos en ejecución"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega del SMS"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Salary Information"
msgstr "Información del salario"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Salary Structure Type"
msgstr "Tipo de Estructura Salarial"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Search Contract"
msgstr "Buscar contrato"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Search Reference Contracts"
msgstr "Buscar contratos de referencia"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set Contract End Date"
msgstr "Establecer fecha de finalización de contrato"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set the end date on the current contract."
msgstr "Establecer la fecha de finalización en el contrato actual."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Show all records which have a next action date before today"
msgstr ""
"Mostrar todos los registros que tengan una próxima fecha de acción antes de "
"hoy"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_start
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_start
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_start
msgid "Start date of the contract."
msgstr "Fecha de inicio del contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__state
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_employee_public_view_form
msgid "Status"
msgstr "Estado"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya ha pasado\n"
"Hoy: La fecha límite es hoy\n"
"Planificada: futuras actividades."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__state
msgid "Status of the contract"
msgstr "Estado del contrato"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"EL código ISO del país en dos caracteres.\n"
"Puedes utilizar este campo para una búsqueda rápida."

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:0
#, python-format
msgid "The contract of %s is about to expire."
msgstr "El contrato de %s está próximo a vencer."

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_contract_pending
msgid "To Renew"
msgstr "A renovar"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Today Activities"
msgstr "Actividades de Hoy"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__done
msgid "Under Contract"
msgstr "Bajo contrato"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Nº de mensajes sin leer"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_expire
msgid "Visa Expiration Date"
msgstr "Fecha de expiración del visado"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_no
msgid "Visa No"
msgstr "Número de Visa"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__wage
msgid "Wage"
msgstr "Salario"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__permit_no
msgid "Work Permit No"
msgstr "Número de permiso de trabajo"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__resource_calendar_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Working Schedule"
msgstr "Programa de trabajo"
