# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Khwu<PERSON><PERSON>awa<PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Krisa C, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr "ช่องนี้เป็นช่องส่วนตัว ผู้คนจะต้องได้รับเชิญให้เข้าร่วม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires \"%s\" access"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires microphone access"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "%(activity_name)s: %(summary)s assigned to you"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""
"%(email)s ไม่ได้รับการยอมรับว่าเป็นอีเมลที่ถูกต้อง "
"ซึ่งมีความจำเป็นสำหรับการสร้างลูกค้าใหม่"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr "%(user_name)s เชิญให้คุณติดตาม %(document)s document: %(title)s"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s เชิญให้คุณติดตามเอกสารใหม่."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Message"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Messages"
msgstr "%d ข้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "%d days overdue"
msgstr "%d วัน ที่เลยกำหนด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "%d days overdue:"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (สำเนา)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s และ %s กำลังพิมพ์ ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s and %s have reacted with %s"
msgstr "%s และ %s ได้โต้ตอบกับ %s แล้ว"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s connected"
msgstr "%s เชื่อมต่อ"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "สร้าง %s แล้ว"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s from %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "%s has a request"
msgstr "%s มีคำขอ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s has reacted with %s"
msgstr "%s ได้โต้ตอบกับ %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s กำลังพิมพ์ ..."

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "%s started a live conference"
msgstr "%s เริ่มการประชุมสด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s และคนอื่น ๆ กำลังพิมพ์ ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and %s other persons have reacted with %s"
msgstr "%s, %s, %s และ %s บุคคลอื่นได้โต้ตอบกับ %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and 1 other person have reacted with %s"
msgstr "%s, %s, %s และอีก 1 คนได้โต้ตอบกับ %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s have reacted with %s"
msgstr "%s, %s, %s ได้โต้ตอบกับ %s แล้ว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "(from"
msgstr "(จาก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(เดิมกำหนดให้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid ", "
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid ", enter to"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "-&gt;"
msgstr "-&gt;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid ". Narrow your search to see more choices."
msgstr "จำกัดการค้นหาของคุณเพื่อดูตัวเลือกเพิ่มเติม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ".<br/>"
msgstr "<br/>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Future"
msgstr "0 อนาคต"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Late"
msgstr "0 ล่าช้า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Today"
msgstr "0 วันนี้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b attrs=\"{'invisible': [('no_record', '=', False)]}\" class=\"text-"
"warning\">No record for this model</b>"
msgstr ""
"<b attrs=\"{'invisible': [('no_record', '=', False)]}\" class=\"text-"
"warning\">ไม่มีบันทึกสำหรับรุ่นนี้</b>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<br><br>\n"
"            Type <b>@username</b> to mention someone, and grab his attention.<br>\n"
"            Type <b>#channel</b> to mention a channel.<br>\n"
"            Type <b>/command</b> to execute a command.<br>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">สร้างโดย <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">invited <a href=\"#\" data-oe-"
"model=\"res.partner\" data-oe-"
"id=\"%(new_partner_id)d\">%(new_partner_name)s</a> to the channel</div>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">joined the channel</div>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">left the channel</div>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"URL ของเอกสาร\"/>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a channel here.</p>"
msgstr "<p>Create a channel here.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a public or private channel.</p>"
msgstr "<p>Create a public or private channel.</p>"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"<p>Dear Sender,<br /><br />\n"
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"Please make sure you are using the correct address or contact us at %(default_email)s instead.<br /><br />\n"
"Kind Regards,</p>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "<span class=\"col-md-5 col-lg-4 col-sm-12 pl-0\">Force a language: </span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."
msgstr ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Activities</span>"
msgstr "<span class=\"o_form_label\">Activities</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Custom ICE server list</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr "<strong>Original note:</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr "<strong>Recommended Activities</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "การปรากฏตัวของรถบัสจะต้องมีผู้ใช้หรือแขก"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_partner_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "สมาชิกของช่องจะต้องเป็นพาร์ทเนอร์หรือแขก"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "ช่องประเภท 'แชท' ไม่สามารถมีผู้ใช้เกินสองคน"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr "ไม่ควรสร้างการแชทกับบุคคลมากกว่า 2 คน ให้สร้างกลุ่มแทน"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "การโต้ตอบข้อความจะต้องมาจากพาร์ทเนอร์หรือลูกค้า"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "A next activity can only be planned on models that use the chatter"
msgstr "A next activity can only be planned on models that use the chatter"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "การตั้งค่าระดับเสียงจะต้องมีพาร์ทเนอร์หรือลูกค้า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Accept"
msgstr "Accept"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "กลุ่มการเข้าถึง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "เข้าถึง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "การดำเนินการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
msgid "Action To Do"
msgstr "สิ่งที่จะทำ"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "มุมมอง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"การดำเนินการอาจทำให้เกิดพฤติกรรมเฉพาะเช่นการเปิดมุมมองปฏิทินหรือทำเครื่องหมายโดยอัตโนมัติว่าเสร็จสิ้นเมื่ออัปโหลดเอกสาร"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Activated by default when subscribing."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_channel__active
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__active_domain
msgid "Active domain"
msgstr "เปิดใช้งาน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#, python-format
msgid "Activities"
msgstr "กิจกรรม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_view.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
#, python-format
msgid "Activity"
msgstr "กิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Activity Mixin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "การตั้งค่ากิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "ประเภทกิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Activity Types"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "Activity User Type"
msgstr "Activity User Type"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "ประเภทกิจกรรม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "เพิ่มบัญชีดำอีเมล"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "เพิ่มผู้ติดตาม"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr "Add Followers can only be done on a mail thread model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_mail__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_message__add_sign
msgid "Add Sign"
msgstr "เพิ่มเครื่องหมาย"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.js:0
#, python-format
msgid "Add a Reaction"
msgstr "เพิ่มการโต้ตอบ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add a description"
msgstr "เพิ่มคำอธิบาย"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "Add a new %(document)s or send an email to %(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "เพิ่มที่อยู่อีเมลลงในบัญชีแบล็คลิสต์"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/text_emojis.xml:0
#, python-format
msgid "Add an emoji"
msgstr "เพิ่มอีโมจิ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Add attachment"
msgstr "Add attachment"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#, python-format
msgid "Add attachments"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "เพิ่มที่ติดต่อเพื่อแจ้งให้ทราบ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Add or join a channel"
msgstr "เพิ่มหรือเข้าร่วมช่อง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add users"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "เพิ่มข้อมูลรับรอง twilio ของคุณสำหรับเซิร์ฟเวอร์ ICE"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr "ไม่สามารถเพิ่มผู้ติดตามในช่องได้ ลองเพิ่มสมาชิกแทน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "ผู้ติดต่อเพิ่มเติม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "ขั้นสูง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr "ตั้งค่าขั้นสูง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
msgid "Alert"
msgstr "เตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr "ชื่อเรียก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_contact
msgid "Alias Contact Security"
msgstr "Alias Contact Security"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "ชื่อโดเมน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_name
msgid "Alias Name"
msgstr "ชื่อเรียก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_domain
msgid "Alias domain"
msgstr "ชื่อโดเมน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_model_id
msgid "Aliased Model"
msgstr "Aliased Model"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "นามแฝง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "All"
msgstr "ทั้งหมด"

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "ต้องระบุโทเค็นการเข้าถึงสำหรับไฟล์แนบแต่ละรายการ"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "An email is required for find_or_create to work"
msgstr "จำเป็นต้องมีอีเมลเพื่อให้ find_or_create ทำงาน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending an email."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "An error occurred while fetching messages."
msgstr "เกิดข้อผิดพลาดขณะเรียกข้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "An unexpected error occurred during the creation of the chat."
msgstr "เกิดข้อผิดพลาดที่ไม่คาดคิดระหว่างการสร้างการแชท"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And"
msgstr "และ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And 1 other member."
msgstr "และสมาชิกอีก 1 คน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Anonymous"
msgstr "ผู้ใช้นิรนาม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "ใช้กับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Apply"
msgstr "ใช้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid "Are you sure you want to delete this message?"
msgstr "คุณแน่ใจหรือว่าต้องการลบข้อความนี้?"

#. module: mail
#: code:addons/mail/wizard/mail_resend_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s mail delivery failures? You won't be "
"able to re-send these mails later!"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการยกเลิกการแบล็คลิสต์ที่อยู่อีเมลนี้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign to ..."
msgstr "มอบหมายให้..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign/unassign to me"
msgstr "มอบหมาย/ยกเลิกการมอบหมาย ให้ฉัน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#, python-format
msgid "Assigned to"
msgstr "มอบหมายให้"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "แนบเอกสาร"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "แนบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Attachment counter loading..."
msgstr "กำลังโหลดตัวนับไฟล์แนบ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "เอกสารแนบ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Authenticated Partners"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "ผู้เขียน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Author's avatar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_public_id
msgid "Authorized Group"
msgstr "กลุ่มที่ได้รับสิทธิ์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "ลบอัตโนมัติ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "สมัครสมาชิกกลุ่มอัตโนมัติ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_ids
msgid "Auto Subscription"
msgstr "สมัครสมาชิกอัตโนมัติ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "สมัครสมาชิกอัตโนมัติ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "Automated activity"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""
"กำหนดเวลากิจกรรมนี้โดยอัตโนมัติเมื่อกิจกรรมปัจจุบันถูกทำเครื่องหมายว่าเสร็จสิ้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
#, python-format
msgid "Avatar"
msgstr ""
"สนามนี้ถือเป็นภาพที่ใช้เป็นสัญลักษณ์สำหรับการติดต่อนี้ จำกัด 1024x1024px"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "อวตาร 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "อวตาร 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "อวตาร 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "อวตาร 512"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Avatar of OdooBot"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of guest"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of user"
msgstr "อวตารของผู้ใช้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "ห่างออกไป"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "พื้นฐาน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Best regards,"
msgstr "Best regards,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "Blacklist"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Blacklist Date"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "ที่อยู่บัญชีแบล็คลิสต์"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "ที่อยู่อีเมลบัญชีแบล็คลิสต์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "เนื้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Bot"
msgstr "บอท"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
msgid "Bounce"
msgstr "Bounce"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "Bounced"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Browser default"
msgstr "เบราว์เซอร์เริ่มต้น"

#. module: mail
#: code:addons/mail/models/mail_thread_cc.py:0
#, python-format
msgid "CC Email"
msgstr "CC Email"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Call"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Camera is off"
msgstr "กล้องกำลังปิดอยู่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "แก้ไขร่างกายได้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
msgid "Can Write"
msgstr "Can Write"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr "ไม่สามารถอัปเดตข้อความหรือผู้รับการแจ้งเตือนได้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "ยกเลิก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "ยกเลิกอีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Cancel notification in failure"
msgstr "Cancel notification in failure"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "ถูกยกเลิก"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "ยกเลิกแล้ว"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Canned Response / Shortcode"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__canned_response_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__canned_response_ids
msgid "Canned Responses"
msgstr "Canned Responses"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Carbon copy message recipients"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "ผู้รับสำเนาคาร์บอน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "Carbon copy recipients (placeholders may be used here)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "จับทั้งหมด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "Catchall Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "ประเภทการผูกมัด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Change Layout"
msgstr "เปลี่ยนเค้าโครง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Change layout"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "Change the background color of the related activities of this type."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Changed"
msgstr "Changed"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__channel_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "ช่องทาง"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(guest_names)s"
msgstr ""
"ช่อง \"%(channel_name)s\" ยอมรับเฉพาะสมาชิกของกลุ่ม \"%(group_name)s\" "
"ห้ามสำหรับ: %(guest_names)s"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(partner_names)s"
msgstr ""
"ช่อง \"%(channel_name)s\" ยอมรับเฉพาะสมาชิกของกลุ่ม \"%(group_name)s\" "
"ห้ามสำหรับ: %(partner_names)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_partner_id
msgid "Channel Partner"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Channel Type"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Channel settings"
msgstr "Channel settings"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr "ช่องทาง"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr "ช่องทาง/คู่ค้า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__chat
#, python-format
msgid "Chat"
msgstr "สนทนา"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr "Chat Shortcode"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"การแชทเป็นแบบส่วนตัวและไม่ซ้ำกันระหว่าง 2 คน "
"กลุ่มเป็นแบบส่วนตัวในกลุ่มผู้ได้รับเชิญ สามารถเข้าร่วมช่องได้อย่างอิสระ "
"(ขึ้นอยู่กับการกำหนดค่า)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Child Messages"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Choose an example"
msgstr "เลือกตัวอย่าง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "Click here to retry"
msgstr "คลิกที่นี่เพื่อลองอีกครั้ง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Click on your message"
msgstr "คลิกที่ข้อความของคุณ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Click to see the attachments"
msgstr "คลิกเพื่อดูเอกสารแนบ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
#, python-format
msgid "Close"
msgstr "ปิด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Close (Esc)"
msgstr "ปิด (Esc)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close chat window"
msgstr "Close chat window"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close conversation"
msgstr "ปิดการสนทนา"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__closed
msgid "Closed"
msgstr "ปิด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Comma-separated carbon copy recipients addresses"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Comma-separated ids of recipient partners"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"Comma-separated ids of recipient partners (placeholders may be used here)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Comma-separated recipient addresses"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr "Comma-separated recipient addresses (placeholders may be used here)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "ความคิดเห็น"

#. module: mail
#: model:ir.model,name:mail.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/mail_template/mail_template.js:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "เขียนอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "โหมดการเขียน"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "การตั้งค่า"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "กำหนดค่ารายการเซิร์ฟเวอร์ ICE ของคุณสำหรับ webRTC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "กำหนดค่าประเภทกิจกรรมของคุณ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "กำหนดค่าเซิร์ฟเวอร์อีเมลของคุณเอง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Confirm"
msgstr "ยืนยัน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "ยืนยัน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Congratulations, you're done with your activities."
msgstr "ขอแสดงความยินดี คุณทำกิจกรรมเสร็จแล้ว"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "ยินดีด้วย กล่องจดหมายของคุณว่างเปล่า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/widgets/discuss/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "ขอแสดงความยินดี กล่องจดหมายของคุณว่างเปล่า"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "การเชื่อมต่อล้มเหลว (ปัญหาเซิร์ฟเวอร์อีเมลขาออก)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "ถือว่าคำตอบเป็นเธรดใหม่"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "รายชื่อ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_partner__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this partner. This includes: creating, joining, pinning, "
"and new message posted."
msgstr ""
"ประกอบด้วยวันที่และเวลาของเหตุการณ์ที่น่าสนใจล่าสุดที่เกิดขึ้นในช่องนี้สำหรับพาร์ทเนอร์รายนี้"
" ซึ่งรวมถึง: การสร้าง การเข้าร่วม การปักหมุด และการโพสต์ข้อความใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "เนื้อหา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "เนื้อหา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fold_state
msgid "Conversation Fold State"
msgstr "สถานะการพับการสนทนา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_minimized
msgid "Conversation is minimized"
msgstr "การสนทนาจะลดลง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Conversations"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "ข้อความตีกลับที่กำหนดเอง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "ประเทศ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Create"
msgstr "สร้าง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Next Activity"
msgstr "สร้างกิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "สร้าง Uid"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Create group chat"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s"
msgstr "สร้าง %(document)s ใหม่"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "สร้าง %(document)s ใหม่โดยส่งอีเมลไปที่ %(email_link)s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Create or search channel..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Created"
msgstr "สร้างแล้ว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "สร้างโดย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Creating a new record..."
msgstr "กำลังสร้างสถิติใหม่..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "วันที่สร้าง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "หนังสือรับรอง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr "ผู้ใช้ปัจจุบันมีการแจ้งเตือนที่ติดดาวซึ่งเชื่อมโยงกับข้อความนี้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "ข้อความตีกลับที่กำหนดเอง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__custom_channel_name
msgid "Custom channel name"
msgstr "ชื่อช่องที่กำหนดเอง"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "ลูกค้าจำเป็นต้องแจ้งทาง กล่องจดหมาย/อีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "วันที่"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "วัน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Deadline"
msgstr "เส้นตาย"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Deafen"
msgstr "คนหูหนวก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "เรียน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "ประเภทการตกแต่ง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "ค่าเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__default_display_mode
msgid "Default Display Mode"
msgstr "โหมดการแสดงผลเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "โน๊ตเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "การสรุปเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "ผู้ใช้เริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_template__null_value
msgid "Default Value"
msgstr "ค่าเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_defaults
msgid "Default Values"
msgstr "ค่าเริ่มต้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "ผู้รับเริ่มต้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"ผู้รับเริ่มต้นของบันทึก:\n"
"- พาร์ทเนอร์ (โดยใช้รหัสในพาร์ทเนอร์หรือช่อง Partner_id) หรือ\n"
"- อีเมล (ใช้ email_from หรือช่องอีเมล)"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Define a new chat shortcode"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "ป้ายกำกับความล่าช้า"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "ประเภทความล่าช้า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Delay after releasing push-to-talk"
msgstr "ความล่าช้าหลังจากปล่อย push-to-talk"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "หน่วยล่าช้า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "ลบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "ลบอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr "ลบสําเนาข้อความ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "การส่งล้มเหลว"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Delivery failure"
msgstr "การส่งมอบล้มเหลว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "รายละเอียด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"คำอธิบายที่จะเพิ่มในข้อความที่โพสต์สำหรับประเภทย่อยนี้ "
"หากว่างเปล่าจะมีการเพิ่มชื่อแทน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"กำหนดวิธีการแสดงช่องตามค่าเริ่มต้นเมื่อเปิดจากลิงก์คำเชิญ "
"ไม่มีค่าจะหมายถึงข้อความที่แสดง (ไม่มีเสียง/วิดีโอ)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Direct Messages"
msgstr "ข้อความตรง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Discard"
msgstr "ยกเลิก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Discard delivery failures"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_cancel_action
msgid "Discard mail delivery failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Discard message delivery failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
#, python-format
msgid "Disconnect"
msgstr "ยกเลิกการเชื่อมต่อ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "Disconnected from the RTC call by the server"
msgstr "ถูกตัดการเชื่อมต่อจากการเรียก RTC โดยเซิร์ฟเวอร์"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: code:addons/mail/static/src/models/discuss/discuss.js:0
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Discuss"
msgstr "การสนทนา"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "แชทแถบด้านข้าง"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion Channel"
msgstr "ช่องทางการสนทนา"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "การสนทนา"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_cancel
msgid "Dismiss notification for resend by model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"แสดงตัวเลือกในเอกสารที่เกี่ยวข้อง "
"เพื่อเปิดตัวช่วยสร้างการเรียบเรียงด้วยเทมเพลตนี้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__res_name
msgid "Display name of the related document."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"อย่าเก็บสำเนาอีเมลไว้ในประวัติการสื่อสารของเอกสาร "
"(การส่งจดหมายจำนวนมากเท่านั้น)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#, python-format
msgid "Do you really want to delete \"%s\"?"
msgstr "คุณต้องการลบ \"%s\" หรือไม่?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "เอกสาร"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "ผู้ติดตามเอกสาร"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
msgid "Document Model"
msgstr "โมเดลเอกสาร"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "ชื่อเอกสาร"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Documentation"
msgstr "เอกสารกำกับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Launch Next"
msgstr "เสร็จสิ้นและเปิดตัวถัดไป"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "เสร็จสิ้นและกำหนดเวลาถัดไป"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Download"
msgstr "ดาวน์โหลด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Download logs"
msgstr "ดาวน์โหลดบันทึก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/drop_zone/drop_zone.xml:0
#, python-format
msgid "Drag Files Here"
msgstr "ลากไฟล์มาที่นี่"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Dropdown menu"
msgstr "เมนูแบบดรอปดาว์น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
msgid "Due Date"
msgstr "วันกำหนดจ่าย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "วันครบกำหนดเมื่อ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Due in %d days"
msgstr "ถึงกำหนดใน %d วัน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Due in %d days:"
msgstr "ถึงกำหนดใน %d วัน:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Due on"
msgstr "ครบกำหนดเมื่อ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "ประเภทวันครบกำหนด"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "อีเมลที่ซ้ำกัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "ระยะเวลาของกิจกรรมเสียงในหน่วย ms"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Edit"
msgstr "แก้ไข"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "แก้ไขพาร์ทเนอร์"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Edit Subscription of"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Edit subscription"
msgstr "แก้ไขการสมัครรับข้อมูลข่าวสาร"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "อีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "ที่อยู่อีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr "นามแฝงอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "อีเมลนามแฝง"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "อีเมลแทน Mixin"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "อีเมลบัญชีแบล็คลิสต์"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "การจัดการ CC อีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "การตั้งค่าอีเมล"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "การส่งอีเมลกลุ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "ตัวอย่างอีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "ค้นหาอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "แม่แบบอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "ตัวอย่างแม่แบบอีเมล"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
msgid "Email Templates"
msgstr "แม่แบบอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "เธรดอีเมล"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "ที่อยู่อีเมลมีอยู่แล้ว!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"ที่อยู่อีเมลของผู้ส่ง ฟิลด์นี้ถูกตั้งค่าเมื่อไม่พบคู่ที่ตรงกันและแทนที่ฟิลด์"
" author_id ในการแชท."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to which replies will be redirected"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr "ที่อยู่อีเมลที่การตอบกลับจะถูกเปลี่ยนเส้นทางเมื่อส่งอีเมลกลุ่ม"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""
"ที่อยู่อีเมลที่การตอบกลับจะถูกเปลี่ยนเส้นทางเมื่อส่งอีเมลกลุ่ม "
"ใช้เฉพาะเมื่อการตอบกลับไม่ได้ถูกบันทึกไว้ในกระทู้การสนทนาเดิม"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr "ที่อยู่อีเมลที่อยู่ในบัญชีแบล็คลิสต์จะไม่ได้รับอีเมลอีกต่อไป"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Email alias %(alias_name)s cannot be used on %(count)d records at the same "
"time. Please update records one by one."
msgstr ""
"ไม่สามารถใช้นามแฝงอีเมล %(alias_name)s กับ %(count)d บันทึกในเวลาเดียวกันได้"
" โปรดอัปเดตบันทึกทีละรายการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "cc อีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "ตัวช่วยการเขียนอีเมล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "ข้อความอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "โปรแกรมสร้างการส่งอีเมลซ้ำ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "เทมเพลตอีเมล"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "อีเมล"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Emojis"
msgstr "อีโมจิ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "เฉพาะพนักงานเท่านั้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "เปิดใช้การติดตามคำสั่งซื้อ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Enable desktop notifications to chat."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Envelope Example"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "ผิดพลาด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "ข้อความผิดพลาด"

#. module: mail
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr ""
"เกิดข้อผิดพลาดระหว่างการสื่อสารกับเซิร์ฟเวอร์การรับประกันของผู้จัดพิมพ์"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "เกิดข้อผิดพลาด พาร์ทเนอร์ไม่สามารถติดตามวัตถุเดียวกันสองครั้งได้"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__public
msgid "Everyone"
msgstr "ทุกคน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "ข้อยกเว้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__member_count
msgid "Excluding guests from count."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Exit full screen"
msgstr "ออกจากโหมดเต็มหน้าจอ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "ตัวกรองเพิ่มเติม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "จดหมายล้มเหลว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "ล้มเหลว"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render QWeb template : %s)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template : %s)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render template : %(xml_id)s (%(view_id)d)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "เหตุผลความล้มเหลว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "สาเหตุที่ความล้มเหลว"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"สาเหตุของความล้มเหลว โดยปกติจะเป็นข้อยกเว้นที่เซิร์ฟเวอร์อีเมลส่งออกไป "
"ซึ่งจัดเก็บไว้เพื่อความสะดวกในการแก้ไขข้อบกพร่องของปัญหาการส่งเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "ประเภทความล้มเหลว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "รายการโปรดโดย"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Feedback"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field
msgid "Field"
msgstr "ฟิลด์"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "ฟิลด์ \"กิจกรรมของอีเมล\" ไม่สามารถเปลี่ยนเป็น \"False\" ได้"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "ฟิลด์ \"แบล็คลิสต์ของอีเมล\" ไม่สามารถเปลี่ยนเป็น \"False\" ได้"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "ฟิลด์ \"เธรดอีเมล\" ไม่สามารถเปลี่ยนเป็น \"False\" ได้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_desc
msgid "Field Description"
msgstr "คำอธิบายฟิลด์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr "กลุ่มฟิลด์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_type
msgid "Field Type"
msgstr "ประเภทฟิลด์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "รายละเอียดฟิลด์"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"ฟิลด์ที่ใช้เพื่อเชื่อมโยงโมเดลที่เกี่ยวข้องกับโมเดลย่อย "
"เมื่อใช้การสมัครสมาชิกอัตโนมัติในเอกสารที่เกี่ยวข้อง ฟิลด์นี้ใช้เพื่อคำนวณ "
"getattr ( related_document.relation_field )"

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "ช่องข้อมูล"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or create a channel..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or start a conversation..."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__folded
msgid "Folded"
msgstr "พับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Follow"
msgstr "ติดตาม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
#, python-format
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (คู่ค้า)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "แบบฟอร์มผู้ติดตาม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Followers of"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "ผู้ติดตามเท่านั้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Following"
msgstr "กำลังติดตาม"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบอักษรที่ยอดเยี่ยมเช่น fa-tasks"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "อีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "จาก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Full composer"
msgstr "ผู้แต่งเต็ม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Full screen"
msgstr "เต็มหน้าจอ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "วิดีโอแบบเต็มหน้าจอ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Future"
msgstr "อนาคต"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "กิจกรรมในอนาคต"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "เกตเวย์"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Generic User From Record"
msgstr "ผู้ใช้ทั่วไปจากบันทึก"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "ไปยังหน้าการตั้งค่า"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__group
msgid "Group"
msgstr "กลุ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "จัดกลุ่มตาม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Group Name"
msgstr "กลุ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "จัดกลุ่มโดย…"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Grouped Chat"
msgstr "แชทเป็นกลุ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr "กลุ่ม"

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
#, python-format
msgid "Guest"
msgstr "ลูกค้า"

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name cannot be empty."
msgstr "ชื่อลูกค้าไม่สามารถเว้นว่างได้"

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name is too long."
msgstr "ชื่อของลูกค้ายาวเกินไป"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_guest_menu
msgid "Guests"
msgstr "แขก"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "See http://openerp.com"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "จัดการโดยอีเมลล์"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "จัดการในระบบ odoo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__has_cancel
msgid "Has Cancel"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "มีการกล่าวถึง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "ได้ปิดการใช้งานเสียงที่เข้ามา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
#: model:ir.model.fields,help:mail.field_mail_mail__has_error
#: model:ir.model.fields,help:mail.field_mail_message__has_error
msgid "Has error"
msgstr "มีข้อผิดพลาด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "ส่วนหัว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "สวัสดี"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Hello,"
msgstr "สวัสดี"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__help_message
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Help message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "ซ่อน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Hide Member List"
msgstr "ซ่อนรายชื่อสมาชิก"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "ซ่อนประเภทย่อยในตัวเลือกผู้ติดตาม"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"ซ่อนสำหรับผู้ใช้สาธารณะ/พอร์ทัล โดยไม่ขึ้นอยู่กับการกำหนดค่าประเภทย่อย"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "History"
msgstr "ประวัติ"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""
"ระยะเวลาที่การออกอากาศเสียงจะยังคงทำงานอยู่หลังจากผ่านเกณฑ์ระดับเสียงแล้ว"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "เซิร์ฟเวอร์ ICE"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "เซิร์ฟเวอร์ ICE"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.mail_channel_ice_servers_menu
msgid "ICE servers"
msgstr "เซิร์ฟเวอร์ ICE"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_channel__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "รหัส"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID ของเร็กคอร์ดหลักที่มีนามแฝง (ตัวอย่าง: โครงการที่มีนามแฝงในการสร้างงาน)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุกิจกรรมการยกเว้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Id ของทรัพยากรที่ติดตาม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "Identity"
msgstr "ตัวตน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Idle"
msgstr "ไม่ได้ใช้งาน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_unread
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked in the chatter. "
"Value is used to order tracking values."
msgstr ""
"หากตั้งค่าการแก้ไขทุกรายการที่ทำในฟิลด์นี้จะถูกติดตามในการพูดคุย "
"ค่าใช้เพื่อสั่งค่าการติดตาม"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""
"หากตั้งค่าไว้ ผู้จัดการคิวจะส่งอีเมลหลังจากวันที่ดังกล่าว หากไม่ได้ตั้งค่า "
"อีเมลจะถูกส่งโดยเร็วที่สุด เว้นแต่จะระบุเขตเวลา ซึ่งจะถือว่าอยู่ในเขตเวลา "
"UTC"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expressions "
"expression."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,help:mail.field_mail_channel__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"หากตั้งค่าไว้ "
"เนื้อหานี้จะถูกส่งไปยังผู้ใช้ที่ไม่ได้รับอนุญาตโดยอัตโนมัติแทนข้อความเริ่มต้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"หากที่อยู่อีเมลอยู่ในบัญชีดำ ผู้ติดต่อจะไม่ได้รับจดหมายจำนวนมากอีกต่อไป "
"จากรายชื่อใดๆ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"หากเป็นจริง คำตอบจะไม่อยู่ในเธรดการพูดคุยในเอกสารต้นฉบับ "
"แต่จะตรวจสอบการตอบกลับในการติดตาม message-id และเปลี่ยนเส้นทางตามนั้น "
"สิ่งนี้มีผลกระทบต่อ message-id ที่สร้างขึ้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"หากคุณได้ตั้งค่าโดเมนอีเมลที่รับทั้งหมดซึ่งเปลี่ยนเส้นทางไปยังเซิร์ฟเวอร์ "
"Odoo ให้ป้อนชื่อโดเมนที่นี่"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red envelope next"
" to each message."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#, python-format
msgid "Image"
msgstr "รูปภาพ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "รูปภาพ 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "รูปภาพ 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "รูปภาพ 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "รูปภาพ 512"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "รูปภาพคือลิงก์"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "นามแฝงที่ไม่ใช้งาน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "กล่องจดหมาย"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Incoming Call..."
msgstr "สายเรียกเข้า..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr "ระบุว่ากิจกรรมนี้ถูกสร้างขึ้นโดยอัตโนมัติและไม่ใช่โดยผู้ใช้รายใด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Info"
msgstr "ข้อมูลรายละเอียด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "โมเดลเริ่มต้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,help:mail.field_mail_mail__parent_id
#: model:ir.model.fields,help:mail.field_mail_message__parent_id
msgid "Initial thread message."
msgstr "ข้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Input device"
msgstr "อุปกรณ์อินพุต"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr "Integrations"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "ภายในเท่านั้น"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "ที่อยู่อีเมลที่ไม่ถูกต้อง"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "ที่อยู่อีเมลไม่ถูกต้อง %r"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"ตัวสั่งงานไม่ถูกต้อง จะต้องเป็นคำจำกัดความของพจนานุกรมคอมพิวเตอร์ตามตัวอักษร"
" เช่น \"{'field': 'value'}\""

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr "ฟิลด์อีเมลหลักไม่ถูกต้องในโมเดล %s"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""
"ค่าไม่ถูกต้องเมื่อสร้างช่องที่มีสมาชิก อนุญาตผู้ใช้เพียง 4 หรือ 6 "
"รายเท่านั้น"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr ""
"ค่าไม่ถูกต้องเมื่อสร้างช่องที่มีการเป็นสมาชิก อนุญาตผู้ใช้เพียง 0 "
"รายเท่านั้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invitation Link"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "คำเชิญให้ติดตาม %(document_model)s: %(document_name)s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Invite Follower"
msgstr "เชิญผู้ติดตาม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invite people"
msgstr "เชิญบุคคล"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to Channel"
msgstr "เชิญเข้าช่อง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to group chat"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "เชิญโปรแกรมสร้าง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__private
msgid "Invited people only"
msgstr "บุคคลที่ถูกเชิญชวน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "มีการใช้งานอยู่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "เป็นผู้ใช้ปัจจุบันหรือผู้เขียนรับเชิญ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "เป็นผู้แก้ไข"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_member
msgid "Is Member"
msgstr "เป็นสมาชิก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "อ่านแล้ว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_chat
msgid "Is a chat"
msgstr "เป็นแชท"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr "ช่องหมวดหมู่แชทแถบด้านข้างเปิดอยู่หรือไม่?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr "หมวดหมู่แชทแถบด้านข้างเปิดอยู่หรือไม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "ปิดเสียงไมโครโฟนอยู่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_pinned
msgid "Is pinned on the interface"
msgstr "ถูกปักหมุดไว้บนอินเทอร์เฟซ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "กำลังส่งวิดีโอของผู้ใช้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "เป็นการแชร์หน้าจอ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "Issue with audio"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr "เข้าร่วม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Call"
msgstr "เข้าร่วมการโทร"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Join Channel"
msgstr "เข้าร่วมช่อง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Video Call"
msgstr "เข้าร่วมวิดีโอคอล"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr "เข้าร่วมกลุ่ม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "LIVE"
msgstr "ไลฟ์สด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "ภาษา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fetched_message_id
msgid "Last Fetched"
msgstr "การดึงข้อมูลล่าสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__last_interest_dt
msgid "Last Interest"
msgstr "ดอกเบี้ยล่าสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session____last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers____last_update
#: model:ir.model.fields,field_description:mail.field_mail_guest____last_update
#: model:ir.model.fields,field_description:mail.field_mail_ice_server____last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype____last_update
#: model:ir.model.fields,field_description:mail.field_mail_notification____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template_preview____last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value____last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__seen_message_id
msgid "Last Seen"
msgstr "เห็นครั้งล่าสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "อัปเดตล่าสุดเมื่อ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Late"
msgstr "ล่าช้า"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "กิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__layout
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "ใบกำกับสินค้า/ใบแจ้งหนี้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Leave"
msgstr "ลา"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Leave this channel"
msgstr "ออกจากช่องสนทนานี้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_cc__email_cc
msgid "List of cc from incoming emails."
msgstr "รายการ cc จากอีเมลขาเข้า"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "List users in the current channel"
msgstr "รายชื่อผู้ใช้ในช่องปัจจุบัน"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "ผู้ฟังของช่อง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Load more"
msgstr "โหลดเพิ่ม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Loading"
msgstr "กำลังโหลด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Loading..."
msgstr "กำลังโหลด..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Log"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log a note"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Log a note..."
msgstr "บันทึกโน้ตแล้ว..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr "บันทึกกิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr "บันทึกหมายเหตุภายใน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Log an internal note..."
msgstr "บันทึกโน๊ตภายใน..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Log in the original discussion thread"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log note"
msgstr "บันทึกข้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.js:0
#, python-format
msgid "Logged in as %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
#, python-format
msgid "Mail"
msgstr "เมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "กิจกรรมอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "อีเมลแบล็คลิสต์"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "อีเมลแบล็คลิสต์ mixin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr "แบบฟอร์มช่องทางการส่งเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "ผู้แต่งการส่งเมล Mixin"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "Mail Failures"
msgstr "ความล้มเหลวของเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_rtc_session
msgid "Mail RTC session"
msgstr "เซสชัน RTC ของอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "มิกซ์อินการแสดงผลอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "เทมเพลตเมล"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "ตัวแก้ไขเทมเพลตอีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "เธรดอีเมล"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "ค่าการติดตามเมล"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"อีเมลถูกสร้างขึ้นเพื่อแจ้งให้ผู้คนทราบเกี่ยวกับ mail.message ที่มีอยู่"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr "จดหมาย: ตัวจัดการคิวอีเมล"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "กล่องจดหมายไม่พร้อมใช้งาน - %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Mailboxes"
msgstr "กล่องจดหมาย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "อีเมล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_partner__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_users__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "Manage Messages"
msgstr "จัดการข้อความ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr "จัดการคำตอบเป็นอีเมลขาเข้าใหม่ แทนที่จะตอบกลับไปที่ชุดข้อความเดียวกัน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Mark Done"
msgstr "ทำเครื่องหมายว่าเสร็จแล้ว"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Mark all read"
msgstr "ทำเครื่องหมายว่าอ่านแล้วทั้งหมด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr "เสร็จสิ้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Mark as Read"
msgstr "ทำเครื่องหมายว่าอ่านแล้ว"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "ตั้งเป็นสิงที่ต้องทำ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Mark as done"
msgstr "ทำเครื่องหมายว่าเสร็จสิ้น"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "การประชุม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__member_count
msgid "Member Count"
msgstr "จำนวนสมาชิก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr "สมาชิก"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"สมาชิกของกลุ่มเหล่านั้นจะถูกเพิ่มเป็นผู้ติดตามโดยอัตโนมัติ "
"โปรดทราบว่าพวกเขาจะสามารถจัดการการสมัครสมาชิกด้วยตนเองได้หากจำเป็น"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "Wizard"

#. module: mail
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "รวมกับพาร์ทเนอร์ดังต่อไปนี้:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#, python-format
msgid "Message"
msgstr "ข้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message #%s..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message %s..."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "ID ข้อความ"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "การแจ้งเตือนข้อความ"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "การโต้ตอบต่อข้อความ"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "การโต้ตอบต่อข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "ข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "ประเภทข้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Message delivery failure image"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__description
#: model:ir.model.fields,help:mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr "คำอธิบายข้อความ: หัวเรื่องหรือส่วนเริ่มต้นของเนื้อหา"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Message posted on \"%s\""
msgstr "ข้อความที่โพสต์เมื่อ \"%s\""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "ผู้รับข้อความ (อีเมล)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "การอ้างอิงข้อความ เช่น ตัวระบุข้อความก่อนหน้า"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid EmailMessage instance"
msgstr "ข้อความควรเป็นอินสแตนซ์ข้อความของอีเมลที่ถูกต้อง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"ประเภทย่อยของข้อความช่วยให้ประเภทข้อความมีความแม่นยำยิ่งขึ้น "
"โดยเฉพาะการแจ้งเตือนของระบบ ตัวอย่างเช่น "
"อาจเป็นการแจ้งเตือนที่เกี่ยวข้องกับบันทึกใหม่ (ใหม่) "
"หรือการเปลี่ยนแปลงขั้นตอนในกระบวนการ (การเปลี่ยนแปลงขั้นตอน) "
"ประเภทย่อยของข้อความช่วยให้ปรับแต่งการแจ้งเตือนที่ผู้ใช้ต้องการรับบนผนังได้อย่างแม่นยำ"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "ประเภทย่อยของข้อความ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"ประเภทย่อยของข้อความที่ตามมา "
"หมายถึงประเภทย่อยที่จะถูกผลักเข้าสู่วอลล์ของผู้ใช้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"ประเภทข้อความ: อีเมลสําหรับข้อความอีเมล, การแจ้งเตือนสําหรับข้อความของระบบ, "
"ข้อคิดเห็นสําหรับข้อความอื่น ๆ เช่นการตอบกลับของผู้ใช้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "ตัวระบุข้อความที่ไม่ซ้ำกัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Message-Id"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "ข้อความ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "ค้นหาข้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr "สามารถ<b>ติดดาว</b>ข้อความเพื่อเตือนให้คุณกลับมาตรวจสอบในภายหลังได้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr "ข้อความที่ทำเครื่องหมายว่าอ่านแล้วจะแสดงขึ้นในประวัติ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"ข้อความที่มีประเภทย่อยภายในจะแสดงให้เห็นโดยพนักงานเท่านั้น "
"หรือที่เรียกว่าสมาชิกของกลุ่ม base_user"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Messages with tracking values cannot be modified"
msgstr "ข้อความที่มีค่าการติดตามไม่สามารถแก้ไขได้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Minimum activity for voice detection"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "อีเมลหายไป"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email addresss"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr "โมเดล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "โมเดลมีการเปลี่ยนแปลง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "รูปแบบของทรัพยากรที่ตามมา"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr "โมเดลที่ประเภทย่อยนำไปใช้ หากเป็น False ชนิดย่อยนี้จะใช้กับทุกโมเดล"

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "โมเดล"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"การปรับเปลี่ยนโมเดลอาจมีผลกระทบต่อกิจกรรมที่มีอยู่โดยใช้ประเภทกิจกรรมนี้ "
"โปรดใช้ด้วยความระมัดระวัง"

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "A module to test the uninstall feature."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "เดือน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "More"
msgstr "เพิ่มเติม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Mute"
msgstr "ปิดเสียง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "หมดเขตกิจกรรมของฉัน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_channel__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
msgid "Name"
msgstr "ชื่อ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,help:mail.field_mail_mail__record_name
#: model:ir.model.fields,help:mail.field_mail_message__record_name
msgid "Name get of the related document."
msgstr "ชื่อที่ได้จากเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model:ir.model.fields,help:mail.field_mail_mail__needaction
#: model:ir.model.fields,help:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "ต้องดำเนินการ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "New Channel"
msgstr "ช่องทางใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "ค่าใหม่ของ Char"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "วันที่และเวลาของค่าใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "ค่าใหม่ของทศนิยม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "ค่าจำนวนเต็มใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_monetary
msgid "New Value Monetary"
msgstr "ค่าใหม่ทางการเงิน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "ค่าใหม่ของข้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/chat_window/chat_window.js:0
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "New message"
msgstr "ส่งข้อความใหม่"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages"
msgstr "ข้อความใหม่"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages appear here."
msgstr "ข้อความใหม่จะปรากฎขึ้นบริเวณนี้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "ค่าใหม่"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Next"
msgstr "ต่อไป"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Next (Right-Arrow)"
msgstr "ถัดไป (ลูกศรขวา)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "กิจกรรมถัดไป"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#, python-format
msgid "Next Activity"
msgstr "กิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "กำหนดกิจกรรมสุดท้าย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "กิจกรรมต่อไปที่มี"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr "ไม่มีข้อผิดพลาด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "No IM status available"
msgstr "ไม่มีสถานะของ IM"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "ไม่มีบันทึก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No channel found"
msgstr "ไม่พบช่อง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "No conversation selected."
msgstr "ไม่ได้เลือกการสนทนา"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_list/notification_list.xml:0
#, python-format
msgid "No conversation yet..."
msgstr "ยังไม่มีการสนทนา..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No history messages"
msgstr "ไม่มีประวัติข้อความ"

#. module: mail
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr "ไม่พบ message_id ในบริบท"

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr "ไม่พบผู้รับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No starred messages"
msgstr "ไม่มีข้อความที่ติดดาว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "No threading for answers"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No user found"
msgstr "ไม่พบผู้ใช้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "No user found that is not already a member of this channel."
msgstr "ไม่พบผู้ใช้ที่ไม่ได้เป็นสมาชิกของช่องนี้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "No users found"
msgstr "ไม่พบผู้ใช้"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
msgid "None"
msgstr "ไม่มี"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "อีเมลปกติ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:mail.message.subtype,name:mail.mt_note
#, python-format
msgid "Note"
msgstr "โน้ต"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "การแจ้งเตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "อีเมลแจ้งเตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "ประเภทการแจ้งเตือน"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_delete_notification
#: model:ir.cron,name:mail.ir_cron_delete_notification
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "การแจ้งเตือน: ลบการแจ้งเตือนที่เก่ากว่า 6 เดือน"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "การแจ้งเตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notify
msgid "Notify followers"
msgstr "แจ้งเตือนผู้ติดตาม"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr "แจ้งเตือนผู้ที่ติดตามเอกสาร (โพสแบบจำนวนมากเท่านั้น)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"จำนวนวัน/สัปดาห์/เดือนก่อนดำเนินการ "
"ช่วยให้สามารถวางแผนกำหนดเวลาการดำเนินการได้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_users__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_alert/notification_alert.xml:0
#, python-format
msgid ""
"Odoo Push notifications have been blocked. Go to your browser settings to "
"allow them."
msgstr ""
"การแจ้งเตือน Odoo Push ถูกปิดกั้น "
"ไปที่การตั้งค่าเบราว์เซอร์ของคุณเพื่อทำการอนุญาตก่อน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid ""
"Odoo will not have the permission to send native notifications on this "
"device."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Offline"
msgstr "Offline"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "ตกลง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "ค่าเก่าของ Char"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "ค่าเก่าของวันที่และเวลา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "ค่าเก่าของทศนิยม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "ค่าเก่าของจำนวนเต็ม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_monetary
msgid "Old Value Monetary"
msgstr "ค่าเก่าของการเงิน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "ค่าเก่าของข้อความ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "ค่าเก่า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr "เมื่อติดดาวข้อความแล้ว คุณสามารถกลับมาตรวจสอบได้ตลอดเวลาที่นี่"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_unique_user_id
msgid "One user should only have one mail user settings."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Online"
msgstr "ออนไลน์"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr "เฉพาะผู้ดูแลระบบเท่านั้นที่ได้รับอนุญาตให้ส่งออกข้อความอีเมล"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to use grouped read on message model"
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "เฉพาะโมเดลที่กำหนดเองเท่านั้นที่สามารถแก้ไขได้"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only logged notes can have their content updated on model '%s'"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only messages type comment can have their content updated"
msgstr "เฉพาะข้อความประเภทความคิดเห็นเท่านั้นที่สามารถอัปเดตเนื้อหาได้"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Only messages type comment can have their content updated on model "
"'mail.channel'"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Only users belonging to the \"%s\" group can modify dynamic templates."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__open
msgid "Open"
msgstr "เปิด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr "เปิดเอกสาร"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "Open chat"
msgstr "เปิดแชท"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_activity_notice/rtc_activity_notice.xml:0
#, python-format
msgid "Open conference:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Open in Discuss"
msgstr "เปิดในแชท"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Open profile"
msgstr "เปิดโปรไฟล์"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "ยกเลิกแล้ว"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID ทางเลือกของเธรด (บันทึก) ที่จะแนบข้อความขาเข้าทั้งหมด "
"แม้ว่าพวกเขาจะไม่ตอบกลับก็ตาม หากตั้งค่าไว้ "
"การดำเนินการนี้จะปิดใช้งานการสร้างระเบียนใหม่ทั้งหมด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr "ID mail_mail ทางเลือก ใช้เพื่อเพิ่มประสิทธิภาพการค้นหาเป็นหลัก"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"เซิร์ฟเวอร์ที่ต้องการเพิ่มเติมสำหรับอีเมลขาออก หากไม่ได้ตั้งค่า "
"ระบบจะใช้ลำดับความสำคัญสูงสุด"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template
msgid "Optional report to print and attach"
msgstr "รายงานเพิ่มเติมที่จะพิมพ์และแนบ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"ตัวเลือกภาษาการแปล (รหัส ISO) เพื่อเลือกเมื่อส่งอีเมล หากไม่ได้ตั้งค่าไว้ "
"ระบบจะใช้เวอร์ชันภาษาอังกฤษ โดยปกติควรเป็นนิพจน์ตัวแทนที่ให้ภาษาที่เหมาะสม "
"เช่น {{ object.partner_id.lang }}"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "ค่าทางเลือกที่จะใช้ถ้าช่องเป้าหมายว่างเปล่า"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"การสนทนาต้นฉบับ: คำตอบอยู่ในหัวข้อการสนทนาในเอกสารต้นฉบับ\n"
"ที่อยู่อีเมลอื่น: คำตอบไปที่ที่อยู่อีเมลที่กล่าวถึงในรหัสข้อความติดตาม แทนที่จะเป็นชุดการสนทนาในเอกสารต้นฉบับ\n"
"สิ่งนี้มีผลกระทบต่อรหัสข้อความที่สร้างขึ้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Original message was deleted"
msgstr "ข้อความต้นฉบับถูกลบแล้ว"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "กล่องขาออก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Outgoing Mail Server"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "อีเมลขาออก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "อีเมลล์ขาออก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "เกินกำหนด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "เขียนทับอีเมลผู้แต่ง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_user_id
msgid "Owner"
msgstr "เจ้าของ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "PDF file"
msgstr "ไฟล์ PDF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "แม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "ข้อความ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_model_id
msgid "Parent Model"
msgstr "โมเดลหลัก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID เธรดเรกคอร์ดหลัก"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"โมเดลหลักถือนามแฝง "
"โมเดลที่มีการอ้างอิงนามแฝงไม่จำเป็นต้องเป็นโมเดลที่กำหนดโดย alias_model_id "
"(ตัวอย่าง: โครงการ (parent_model) และงาน (แบบจำลอง))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"ประเภทย่อยหลัก ใช้สำหรับการสมัครสมาชิกอัตโนมัติ "
"ฟิลด์นี้ไม่ได้ตั้งชื่ออย่างถูกต้อง ตัวอย่างเช่น ในโปรเจ็กต์ parent_id "
"ของประเภทย่อยของโปรเจ็กต์อ้างอิงถึงประเภทย่อยที่เกี่ยวข้องกับงาน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
msgid "Partner"
msgstr "คู่ค้า"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "โปรไฟล์พาร์ทเนอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
msgid "Partner Readonly"
msgstr "พาร์ทเนอร์อ่านอย่างเดียว"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr "ร่วมมือกับข้อมูลเพิ่มเติมสำหรับการส่งอีเมลอีกครั้ง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "คู่ค้าที่ต้องดำเนินการ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid ""
"Pay attention: The followers of this document who were notified by email "
"will still be able to read the content of this message and reply to it."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "Permission denied"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "โทรศัพท์"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "สายเข้า"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template__copyvalue
msgid "Placeholder Expression"
msgstr "Placeholder Expression"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#, python-format
msgid "Planned"
msgstr "แผน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_box/activity_box.xml:0
#, python-format
msgid "Planned activities"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "วางแผนไว้แล้วใน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient/composer_suggested_recipient.js:0
#, python-format
msgid "Please complete customer's information"
msgstr "กรุณากรอกข้อมูลของลูกค้าให้ครบถ้วน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "โปรดติดต่อเราแทนการใช้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.js:0
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Please wait while the file is uploading."
msgstr "กรุณารอสักครู่ในขณะที่ไฟล์กำลังอัปโหลด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_container/chatter_container.xml:0
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Please wait..."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"นโยบายเกี่ยวกับวิธีจัดการกับการแจ้งเตือน Chatter:\n"
"- จัดการโดยอีเมล: การแจ้งเตือนจะถูกส่งไปยังที่อยู่อีเมลของคุณ\n"
"- จัดการใน Odoo: การแจ้งเตือนจะปรากฏในกล่องจดหมาย Odoo ของคุณ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"นโยบายการโพสต์ข้อความบนเอกสารโดยใช้เมลล์เกตเวย์\n"
"- ทุกคน: ทุกคนโพสต์ได้\n"
"- พาร์ทเนอร์: พาร์ทเนอร์ที่ได้รับการรับรองเท่านั้น\n"
"- ผู้ติดตาม: เฉพาะผู้ติดตามเอกสารที่เกี่ยวข้องหรือสมาชิกของช่องดังต่อไปนี้\n"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Granted"
msgstr "ได้รับสิทธิ์การเข้าถึงพอร์ทัลแล้ว"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Revoked"
msgstr "เพิกถอนการเข้าถึงพอร์ทัลแล้ว"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_post
msgid "Post on Multiple Documents"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "โพสต์บนเอกสาร"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Post your message on the thread"
msgstr "ฝากข้อความของคุณไว้ที่เธรด"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""
"การโพสต์ข้อความควรทำในเอกสารทางธุรกิจ ใช้ message_notify "
"เพื่อส่งการแจ้งเตือนไปยังผู้ใช้"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message with channels as listeners is not supported since Odoo "
"14.3+. Please update code accordingly."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Powered by"
msgstr "สนับสนุนโดย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "กิจกรรมก่อนหน้า"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "ที่อยู่ตอบกลับที่ต้องการ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Press a key to register it as the push-to-talk shortcut"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "ดูตัวอย่าง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "ตัวอย่างของ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Previous"
msgstr "Previous"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Previous (Left-Arrow)"
msgstr "ก่อนหน้า (ลูกศรซ้าย)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "ประเภทกิจกรรมก่อนหน้า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Print"
msgstr "พิมพ์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr "ความเป็นส่วนตัว"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Private channel"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category/discuss_sidebar_category.js:0
#, python-format
msgid "Public Channels"
msgstr "ช่องสาธารณะ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Public channel"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "สัญญาการรับประกันของผู้เผยแพร่"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr "สำนักพิมพ์: อัปเดตการแจ้งเตือน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "ทางลัด Push-To-Talk"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Push-to-talk key"
msgstr "คีย์ Push-to-talk"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Quick search..."
msgstr "ค้นหาอย่างรวดเร็ว..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "เซสชัน RTC"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_session_ids
msgid "RTC Sessions"
msgstr "เซสชัน RTC"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.mail_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "เซสชัน RTC"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr "ช่วงระหว่าง 0.0 ถึง 1.0 ขนาดขึ้นอยู่กับการใช้งานเบราว์เซอร์"

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Re:"
msgstr "ตอบกลับ:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "ลูกค้าที่กำลังโต้ตอบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "พาร์ทเนอร์ที่ตอบโต้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reaction"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
msgid "Reactions"
msgstr "โต้ตอบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "วันที่อ่าน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#, python-format
msgid "Ready"
msgstr "พร้อม"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "พร้อมส่ง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "เหตุผล"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "ได้รับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s and %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s, %s and more"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by Everyone"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "ผู้รับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "ผู้รับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "ประเภทกิจกรรมที่แนะนำ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "Record"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_force_thread_id
msgid "Record Thread ID"
msgstr "บันทึกหัวข้อ ID"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr "รายการ:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Redirect to another email address"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "อ้างอิง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Refuse"
msgstr "ปฏิเสธ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "ขอแสดงความนับถือ,"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Register new key"
msgstr "ลงทะเบียนรหัสใหม่"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Reject"
msgstr "ปฏิเสธ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "บริษัท ที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "รหัสเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "รูปแบบเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "ชื่อโมเดลเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "เทมเพลตจดหมายที่เกี่ยวข้อง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "ข้อความที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "คู่ค้าที่เกี่ยวข้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Relation Field"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Remove"
msgstr "ลบออก"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "ลบอีเมลออกจากโปรแกรมสร้างบัญชีแบล็คลิสต์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr "ลบการดำเนินการตามบริบทเพื่อใช้เทมเพลตนี้กับเอกสารที่เกี่ยวข้อง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Remove this follower"
msgstr "นำผู้ติดตามนี้ออก"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "โมเดลการแสดงผล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "ตอบกลับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "ตอบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "ตอบไปยัง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "ตอบกลับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Replying to"
msgstr "กำลังตอบกลับ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_name
msgid "Report Filename"
msgstr "ชื่อไฟล์รายงาน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "พาร์ทเนอร์ที่ส่งคำขอ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__res_users_settings_ids
msgid "Res Users Settings"
msgstr "การตั้งค่าผู้ใช้ Res"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Resend mail"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Resend to selected"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "ส่งโปรแกรมสร้างอีกครั้ง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Reset Zoom"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Reset Zoom (0)"
msgstr "รีเซ็ตการซูม (0)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "จำกัดการแสดงผลเทมเพลต"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates and Jinja rendering."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr "จำกัดรุ่นเทมเพลตอีเมลและการใช้ตัวยึดตำแหน่ง QWEB"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "ลองใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Rich-text Contents"
msgstr "เนื้อหาข้อความแบบ Rich-text"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "ข้อความแบบ Rich-text/HTML"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_inviting_session_id
msgid "Ringing session"
msgstr "เซสชันเสียงเรียกเข้า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Rotate"
msgstr "หมุน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Rotate (r)"
msgstr "หมุน (r)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "เซสชัน Rtc"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "เซิร์ฟเวอร์ SMTP"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "พนักงานขาย"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Save"
msgstr "บันทึก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "บันทึกเป็นแม่แบบใหม่"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr "บันทึกเป็นแม่แบบใหม่"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "กำหนดเวลา"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/activity/activity.js:0
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Schedule Activity"
msgstr "กำหนดการกิจกรรม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule activities to help you get things done."
msgstr "กำหนดเวลากิจกรรมเพื่อช่วยให้คุณทำสิ่งต่างๆ ได้สำเร็จ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Schedule activity"
msgstr "สร้างกำหนดการ"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "กำหนดเวลากิจกรรม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "กำหนดเวลากิจกรรม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "วันที่ตามกำหนดการ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "วันที่ส่งตามกำหนด"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr "ค้นหานามแฝง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr "ค้นหากลุ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "ค้นหาเซสชัน RTC"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.js:0
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#, python-format
msgid "Search user..."
msgstr "ค้นหาผู้ใช้..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr "ค้นหา: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s and %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s, %s and more"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by Everyone"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Select a user..."
msgstr "เลือกผู้ใช้..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"Select the action to do on each mail and correct the email address if "
"needed. The modified address will be saved on the corresponding contact."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__groups
#, python-format
msgid "Selected group of users"
msgstr "กลุ่มของผู้ใช้ที่ถูกเลือก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Selected users:"
msgstr "ผู้ใช้ที่เลือก:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "ส่ง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Send Again"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__send_mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__email
msgid "Send Email"
msgstr "ส่งอีเมล"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "ส่งอีเมล (%s)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "ส่งทันที"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send a message"
msgstr "ส่งข้อความ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Send a message to followers..."
msgstr "ส่งข้อความถึงผู้ติดตาม..."

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "ส่งอีเมล"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send message"
msgstr "ส่งข้อความ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "ที่อยู่ผู้ส่ง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"ที่อยู่ผู้ส่ง (อาจใช้ตัวยึดตำแหน่งที่นี่) หากไม่ได้ตั้งค่า "
"ค่าเริ่มต้นจะเป็นอีเมลแทนของผู้เขียนหากกำหนดค่าไว้ หรือที่อยู่อีเมล"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "ส่งแล้ว"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Server Action"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "ตั้งค่าใช้งานเป็น False เพื่อซ่อนช่องโดยไม่ต้องลบออก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Settings"
msgstr "ตั้งค่า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Share screen"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift left"
msgstr "เลื่อนไปทางซ้าย"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift right"
msgstr "เลื่อนไปทางขวา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__description
#: model:ir.model.fields,field_description:mail.field_mail_message__description
msgid "Short description"
msgstr "คำอธิบายโดยย่อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "รหัสย่อ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "ทางลัด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show All"
msgstr "Show All"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#, python-format
msgid "Show Followers"
msgstr "แสดงผู้ติดตาม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Show Member List"
msgstr "แสดงรายชื่อสมาชิก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Show a helper message"
msgstr "แสดงข้อความช่วยเหลือ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "Show all records which has next action date is before today"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show less"
msgstr "แสดงน้อยลง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show more"
msgstr "แสดงเพิ่มเติม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show only video"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Showing"
msgstr "กำลังแสดง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Sidebar"
msgstr "แถบด้านข้าง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "การดำเนินการของแถบด้านข้าง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"การดำเนินการของแถบด้านข้างเพื่อทำให้เทมเพลตนี้พร้อมใช้งานในบันทึกของโมเดลเอกสารที่เกี่ยวข้อง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "ต้นฉบับ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "ผู้ใช้เฉพาะ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"ระบุแบบจำลองหากกิจกรรมควรเป็นแบบเฉพาะและไม่พร้อมใช้งานเมื่อจัดการกิจกรรมสำหรับโมเดลอื่น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Spotlight"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "ติดดาว"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Call"
msgstr "เริ่มการโทร"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Video Call"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Start a conversation"
msgstr "เริ่มการสนทนา"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Start a meeting"
msgstr "เริ่มการประชุม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "State"
msgstr "สถานะ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "สถานะ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Stop adding users"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop camera"
msgstr "หยุดการใช้งานกล้อง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Stop replying"
msgstr "หยุดการตอบกลับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop screen sharing"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""
"สตริงที่จัดรูปแบบเพื่อแสดงคีย์โดยมีตัวดัดแปลงตามรูปแบบนี้: "
"shift.ctrl.alt.key เช่น: truthy.1.true.b"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_model_object_field
msgid "Sub-field"
msgstr "Sub-field"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_object
msgid "Sub-model"
msgstr "Sub-model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "เรื่อง"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr "เรื่อง (อาจใช้ตัวยึดตำแหน่งที่นี่)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr "ชื่อเรื่อง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Subject:"
msgstr "เรื่อง:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "การทดแทน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "ประเภทย่อย"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "ชนิดย่อย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "ข้อแนะนำ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "แนะนำกิจกรรมถัดไป"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr "แนะนำกิจกรรมเหล่านี้เมื่อกิจกรรมปัจจุบันถูกทำเครื่องหมายว่าเสร็จสิ้น"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
msgid "Summary"
msgstr "สรุป"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "พารามิเตอร์ของระบบ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
#, python-format
msgid "System notification"
msgstr "การแจ้งเตือนระบบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "โมเดลเป้าหมาย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "เลขประจำตัวผู้เสียภาษี"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__has_recommended_activities
msgid "Technical field for UX purpose"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_change
msgid "Technical field for UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__can_write
msgid "Technical field to hide buttons if the current user has no access."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""
"ข้อมูลทางเทคนิคเพื่อติดตามโมเดลตั้งแต่เริ่มต้นการแก้ไขเพื่อรองรับพฤติกรรมที่เกี่ยวข้องกับ"
" UX"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr "ชื่อทางเทคนิคของผู้ใช้ในบันทึก"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "ตัวอย่างแม่แบบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "ภาษาตัวอย่างเทมเพลต"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering for language should be called with a list of IDs."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called on a valid record IDs."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called only using on a list of IDs."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw)."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "แม่แบบ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Text file"
msgstr "ไฟล์ข้อความ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr " The"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr "ค่า 'วันครบกำหนดใน' ไม่สามารถเป็นค่าลบได้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "The FullScreen mode was denied by the browser"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""
"หมายเลขประจำตัวผู้เสียภาษี กรอกให้ครบถ้วนหากผู้ติดต่อต้องเสียภาษีของรัฐบาล "
"ใช้ในแถลงการณ์ทางกฎหมายบางฉบับ"

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "ไม่มีเอกสารแนบ %s หรือคุณไม่มีสิทธิ์ในการเข้าถึง"

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "The attachment %s does not exist."
msgstr "ไม่มีเอกสารแนบ %s"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "UUID ของช่องจะต้องไม่ซ้ำกัน"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already linked with "
"%(alias_model_name)s. Choose another alias or change it on the linked model."
msgstr ""
"อีเมลนามแฝง %(matching_alias_name)s เชื่อมโยงกับ %(alias_model_name)s แล้ว "
"เลือกนามแฝงอื่นหรือเปลี่ยนในโมเดลที่เชื่อมโยง"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used as "
"%(alias_duplicate)s alias. Please choose another alias."
msgstr ""
"อีเมลนามแฝง %(matching_alias_name)s ถูกใช้เป็นนามแฝง %(alias_duplicate)s "
"แล้ว โปรดเลือกนามแฝงอื่น"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used by the "
"%(document_name)s %(model_name)s. Choose another alias or change it on the "
"other document."
msgstr ""
"อีเมลนามแฝง %(matching_alias_name)s ถูกใช้แล้ว โดย %(document_name)s "
"%(model_name)s เลือกนามแฝงอื่นหรือเปลี่ยนในเอกสารอื่น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "อีเมลที่ส่งไปที่"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid "The escaped html code replacing the shortcut"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_composer_mixin.py:0
#, python-format
msgid "The field %s does not exist on the model %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "ผู้ติดต่อ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"โมเดล (ชนิดเอกสาร Odoo) ซึ่งสอดคล้องกับนามแฝงนี้ อีเมลขาเข้าใดๆ "
"ที่ไม่ตอบกลับเรกคอร์ดที่มีอยู่จะทำให้เกิดการสร้างเรกคอร์ดใหม่ของโมเดลนี้ "
"(เช่น งานโครงการ)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_channel__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"ชื่อของนามแฝงอีเมล เช่น 'งาน' หากคุณต้องการรับอีเมลสำหรับ "
"<<EMAIL>>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"เจ้าของบันทึกที่สร้างขึ้นเมื่อได้รับอีเมลในนามแฝงนี้ "
"หากไม่ได้ตั้งค่าฟิลด์นี้ ระบบจะพยายามค้นหาเจ้าของที่ถูกต้องตามที่อยู่ผู้ส่ง "
"(จาก) หรือจะใช้บัญชีผู้ดูแลระบบหากไม่พบผู้ใช้ระบบสำหรับที่อยู่นั้น"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr "ผู้จัดการคิวจะส่งอีเมลหลังวันที่"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"ไม่สามารถดำเนินการตามที่ร้องขอได้เนื่องจากข้อ จำกัด ด้านความปลอดภัย โปรดติดต่อผู้ดูแลระบบของคุณ\n"
"\n"
"(ประเภทเอกสาร:%s, การดำเนินการ:%s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/follower/follower.js:0
#, python-format
msgid "The subscription preferences were successfully applied."
msgstr "ใช้การตั้งค่าการสมัครสมาชิกเรียบร้อยแล้ว"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__model_id
#: model:ir.model.fields,help:mail.field_mail_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "ประเภทของเอกสารที่สามารถใช้กับเทมเพลตนี้ได้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "There are no messages in this conversation."
msgstr "ไม่มีข้อความในการสนทนานี้"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_rtc_session_channel_partner_unique
msgid "There can only be one rtc session per channel partner"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "This"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "This action will send an email."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"อีเมลนี้ถูกขึ้นบัญชีดำสำหรับการส่งจดหมายจำนวนมาก คลิกเพื่อยกเลิกบัญชีดำ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "ฟิลด์นี้ไม่คำนึงถึงตัวพิมพ์เล็กและตัวพิมพ์ใหญ่"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"ฟิลด์นี้ใช้เพื่อค้นหาที่อยู่อีเมล "
"เนื่องจากฟิลด์อีเมลหลักสามารถมีมากกว่าที่อยู่อีเมลได้"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "This is their first connection. Wish them luck."
msgstr "นี่เป็นการเชื่อมต่อครั้งแรกของพวกเขา ขอให้พวกเขาโชคดี"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"ตัวเลือกนี้จะลบการติดตามอีเมลหลังจากถูกส่งอย่างถาวร "
"รวมถึงจากเมนูทางเทคนิคในการตั้งค่า "
"เพื่อรักษาพื้นที่จัดเก็บข้อมูลของฐานข้อมูล Odoo ของคุณ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "This record has an exception activity."
msgstr "บันทึกนี้มีกิจกรรมยกเว้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Save the record before scheduling an activity!"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "This user can not be added in this channel"
msgstr "ไม่สามารถเพิ่มผู้ใช้รายนี้ในช่องนี้ได้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "gTif"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Thread Image"
msgstr "รูปภาพเธรด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Tiled"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "เขตเวลา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
msgid "To"
msgstr "ถึง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "ถึง (อีเมล)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "ถึง (คู่ค้า)"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To Do"
msgstr "ที่จะทำ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "To:"
msgstr "ถึง:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#, python-format
msgid "Today"
msgstr "วันนี้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "กิจกรรมวันนี้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Today:"
msgstr "วันนี้:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Tomorrow"
msgstr "พรุ้งนี้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Tomorrow:"
msgstr "พรุ่งนี้:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr "หัวข้อสนทนาในกลุ่มนี้..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"ค่าที่ติดตามจะถูกจัดเก็บไว้ในโมเดลที่แยกจากกัน "
"ฟิลด์นี้อนุญาตให้สร้างการติดตามใหม่และสร้างสถิติเกี่ยวกับโมเดลได้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "การติดตาม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "ค่าติดตาม"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "ค่าการติดตาม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__tracking_sequence
msgid "Tracking field sequence"
msgstr "ลำดับฟิลด์การติดตาม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "ค่าที่ติดตาม"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "ตัวกระตุ้น"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "ทริกเกอร์กิจกรรมถัดไป"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Turn camera on"
msgstr "เปิดกล้อง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "โทเค็นการตรวจสอบบัญชี Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "SID บัญชี Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "ประเภท"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "ประเภทของความล่าช้า"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""
"ประเภทของการดำเนินการกับเซิร์ฟเวอร์ มีค่าต่อไปนี้:\n"
"- 'Execute Python Code': บล็อกของรหัส python ที่จะดำเนินการ\n"
"- 'สร้าง': สร้างบันทึกใหม่ด้วยค่าใหม่\n"
"- 'อัปเดตบันทึก': อัปเดตค่าของบันทึก\n"
"- 'ดำเนินการหลายอย่าง': กำหนดการกระทำที่เรียกการกระทำอื่น ๆ ของเซิร์ฟเวอร์\n"
"- 'ส่งอีเมล': ส่งอีเมลโดยอัตโนมัติ (พูดคุย)\n"
"- 'เพิ่มผู้ติดตาม': เพิ่มผู้ติดตามในบันทึก (พูดคุย)\n"
"- 'สร้างกิจกรรมถัดไป': สร้างกิจกรรม (พูดคุย)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "พิมพ์กิจกรรมข้อยกเว้นบนบันทึก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Type the name of a person"
msgstr "พิมพ์ชื่อของบุคคล"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ SMTP ได้"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to log message, please configure the sender's email address."
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "ไม่สามารถโพสต์ข้อความ โปรดกำหนดค่าที่อยู่อีเมลของผู้ส่ง"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "ยกเลิกการแบล็คลิสต์"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Unblacklisting Reason: %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Undeafen"
msgstr "ไม่หูหนวก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Unfollow"
msgstr "เลิกติดตาม"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_alias_unique
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr "ขออภัย มีการใช้อีเมลแทนนี้แล้ว โปรดเลือกอีเมลที่ไม่ซ้ำ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "หน่วยความล่าช้า"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "ข้อผิดพลาดที่ไม่รู้จัก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Unmute"
msgstr "ยกเลิกการปิดเสียง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Unpin Conversation"
msgstr "เลิกปักหมุดการสนทนา"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Unstar all"
msgstr "ลบดาวทั้งหมด"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "พบประเภทรายงาน %s ที่ไม่รองรับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
#, python-format
msgid "Upload Document"
msgstr "อัปโหลดเอกสาร"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Upload file"
msgstr "อัปโหลดไฟล์"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#, python-format
msgid "Uploaded"
msgstr "อัปโหลด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Uploading"
msgstr "กำลังอัพโหลด"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""
"ใช้ 'ผู้ใช้เฉพาะ' เพื่อกำหนดผู้ใช้รายเดียวกันในกิจกรรมถัดไปเสมอ ใช้ "
"'ผู้ใช้ทั่วไปจากบันทึก' เพื่อระบุชื่อฟิลด์ของผู้ใช้ที่จะเลือกในบันทึก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Use Push-to-talk"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "ใช้เซิร์ฟเวอร์ Twilio ICE"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr "เปิดใช้งาน"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "ใช้แม่แบบ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "ใช้ฟีเจอร์กดเพื่อพูด"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "ใช้เพื่อแสดงสกุลเงินเมื่อติดตามมูลค่าทางการเงิน"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "ใช้ในการเรียงลำดับประเภทย่อย"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "ผู้ใช้งาน"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "การแสดงตนของผู้ใช้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "การตั้งค่าผู้ใช้"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "การตั้งค่าผู้ใช้"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "ระดับเสียงการตั้งค่าของผู้ใช้"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "การแจ้งเตือนเฉพาะผู้ใช้"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User field name"
msgstr "ชื่อฟิลด์ผู้ใช้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is a bot"
msgstr "ผู้ใช้เป็นบอท"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is idle"
msgstr "ผู้ใช้ไม่อยู่"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is offline"
msgstr "ผู้ใช้ออฟไลน์อยู่"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is online"
msgstr "ผู้ใช้กำลังออนไลน์"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "ผู้ใช้:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "ชื่อผู้ใช้"

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr "ผู้ใช้งาน"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Users in this channel: %(members)s %(dots)s and you."
msgstr "ผู้ใช้ในช่องนี้: %(members)s %(dots)s และคุณ"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"ผู้ใช้จะยังสามารถแสดงเทมเพลตได้\n"
"อย่างไรก็ตาม มีเพียงผู้แก้ไขเทมเพลตเมลเท่านั้นที่สามารถสร้างเทมเพลตไดนามิกใหม่หรือแก้ไขเทมเพลตที่มีอยู่ได้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"จำเป็นต้องใช้เซิร์ฟเวอร์อีเมลของคุณเองเพื่อส่ง/รับอีเมลในเวอร์ชันคอมมูนิตี้และองค์กร"
" ผู้ใช้ออนไลน์ได้รับประโยชน์จากเซิร์ฟเวอร์อีเมลที่พร้อมใช้งานแล้ว "
"(@mycompany.odoo.com)"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Value for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"ไม่สามารถตรวจสอบค่าสำหรับ `mail.catchall.domain.allowed` ได้\n"
"ควรเป็นรายการโดเมนที่คั่นด้วยเครื่องหมายจุลภาค เช่น example.com,example.org"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Video"
msgstr "วิดีโอ"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr "มุมมอง"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "ดู %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "ประเภทมุมมอง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "View image"
msgstr "ดูรูปภาพ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category/discuss_sidebar_category.xml:0
#, python-format
msgid "View or join channels"
msgstr "ดูหรือเข้าร่วมช่อง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Viewer"
msgstr "ผู้ดู"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "เสียง"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "ปริมาตร"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "ปริมาณต่อพาร์ทเนอร์"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "ปริมาณของพาร์ทเนอร์รายอื่น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Warning"
msgstr "คำเตือน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "สัปดาห์"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "What's your name?"
msgstr "คุณชื่ออะไร?"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr "Whether the message is an internal note (comment mode only)"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_activity
msgid "Whether this model supports activities."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_blacklist
msgid "Whether this model supports blacklist."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr "ใครสามารถติดตามกิจกรรมของกลุ่ม?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Write Feedback"
msgstr "เขียนข้อเสนอแนะ"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Wrong operation name (%s)"
msgstr "ชื่อการดำเนินการไม่ถูกต้อง (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "YYYY-MM-DD HH:MM:SS"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Yesterday"
msgstr "เมื่อวาน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Yesterday:"
msgstr "เมื่อวาน:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"คุณกำลังจะออกจากการสนทนากลุ่มนี้ และจะไม่สามารถเข้าถึงได้อีกต่อไป "
"เว้นแต่คุณจะได้รับเชิญอีกครั้ง คุณแน่ใจหรือไม่ว่าต้องการดำเนินการต่อ?"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "คุณอยู่คนเดียวในช่องนี้"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr "คุณกำลังสนทนาส่วนตัวกับ <b>@%s</b>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr "คุณอยู่ในช่อง <b>#%s</b>."

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "คุณไม่ได้รับอนุญาตให้อัปโหลดไฟล์แนบที่นี่"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "คุณเป็นผู้ดูแลช่องนี้ คุณแน่ใจหรือไม่ว่าต้องการออก?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"คุณสามารถทำเครื่องหมายข้อความว่า 'ติดดาว' ได้ "
"และข้อความนั้นจะแสดงในกล่องจดหมายนี้"

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "You can not write on %(field_name)s."
msgstr "คุณไม่สามารถเขียนใน %(field_name)s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only chat with existing users."
msgstr "คุณสามารถสนทนากับผู้ใช้ที่มีอยู่เท่านั้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/partner/partner.js:0
#, python-format
msgid "You can only chat with partners that have a dedicated user."
msgstr "คุณสามารถแชทได้เฉพาะกับพาร์ทเนอร์ที่มีผู้ใช้เฉพาะเท่านั้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging/messaging.js:0
#, python-format
msgid "You can only open the profile of existing channels."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only open the profile of existing users."
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"คุณไม่สามารถลบกลุ่ม \"ทั้งบริษัท\" ได้ เนื่องจากมีการเรียกใช้งานจากโมดูลอื่น"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address (%s)."
msgstr ""
"คุณไม่สามารถใช้สิ่งอื่นใดนอกจากตัวอักษรละตินที่ไม่มีเครื่องหมายเน้นเสียงในที่อยู่นามแฝง"
" (%s)"

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""
"คุณไม่มีสิทธิ์เข้าถึงอีเมลที่ไม่อยู่ในบัญชีแบล็คลิสต์ "
"โปรดติดต่อผู้ดูแลระบบของคุณ"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "คุณได้รับมอบหมายสำหรับ %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "คุณได้รับมอบหมายสำหรับ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You have been invited to #%s"
msgstr "คุณได้รับเชิญให้ #%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"คุณสามารถแนบไฟล์เข้ากับเทมเพลตนี้เพื่อเพิ่มลงในอีเมลทั้งหมดที่สร้างจากเทมเพลตนี้ได้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unpinned your conversation with %s."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unsubscribed from %s."
msgstr "คุณยกเลิกการสมัครจาก %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a chat!"
msgstr "คุณได้รับเชิญให้เข้าร่วมการสนทนา!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a meeting!"
msgstr "คุณได้รับเชิญให้เข้าร่วมการประชุม!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_author_prefix/message_author_prefix.xml:0
#, python-format
msgid "You:"
msgstr "คุณ:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr " "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Your browser does not support videoconference"
msgstr "เบราว์เซอร์ของคุณไม่รองรับการประชุมทางวิดีโอ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "Your browser does not support voice activation"
msgstr "เบราว์เซอร์ของคุณไม่รองรับการเปิดใช้งานด้วยเสียง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Your browser does not support webRTC."
msgstr "เบราว์เซอร์ของคุณไม่รองรับ webRTC"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Your name"
msgstr "ชื่อของคุณ"

#. module: mail
#: code:addons/mail/controllers/home.py:0
#, python-format
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""
"รหัสผ่านของคุณเป็นค่าเริ่มต้น (admin+)! "
"หากระบบนี้เปิดเผยต่อผู้ใช้ที่ไม่น่าเชื่อถือสิ่งสำคัญคือต้องเปลี่ยนทันทีด้วยเหตุผลด้านความปลอดภัย"
" ฉันจะจู้จี้คุณเกี่ยวกับเรื่องนี้!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom In"
msgstr "ขยายเข้า"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom In (+)"
msgstr "ซูมเข้า (+)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom Out"
msgstr "ขยายออก"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom Out (-)"
msgstr "ซูมออก (-)"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered partners"
msgstr "ที่อยู่ที่เชื่อมโยงกับพาร์ทเนอร์ที่ลงทะเบียน"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "หลังจากวันที่เสร็จสิ้น"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "หลังจากหมดเขตกิจกรรมครั้งก่อน"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %(name)s: %(error)s"
msgstr "นามแฝง %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "assigned you an activity"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "bounce"
msgstr "การตีกลับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "โดย"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "cancel"
msgstr "ยกเลิก"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"ไม่สามารถประมวลผลได้ ที่อยู่นี้\n"
"    ใช้เพื่อรวบรวมคำตอบและไม่ควรใช้เพื่อติดต่อโดยตรง"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "catchall"
msgstr "จับทั้งหมด"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "channel"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "วัน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "deaf"
msgstr "หูหนวก"

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_thread.py:0
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "document"
msgstr "เอกสาร"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "เสร็จสิ้น"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. \"mycompany.com\""
msgstr "เช่น \"mycompany.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "เช่น 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "เช่น ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Calendar: Reminder"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "เช่น พูดคุยเกี่ยวกับข้อเสนอ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "เช่น ตารางการประชุม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Users"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "e.g. support"
msgstr "เช่น การรองรับ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "เช่น true.true..f"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "escape to"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "for %s"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr "ทั่วไป"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "อ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "ได้รับการแก้ไขจาก:"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "นามแฝงที่กำหนดค่าไม่ถูกต้อง"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "นามแฝงที่กำหนดค่าไว้ไม่ถูกต้อง (บันทึกอ้างอิงที่ไม่รู้จัก)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "live"
msgstr "ไลฟ์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support model and res_id parameters anymore. Please "
"call message_post on record."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support subtype parameter anymore. Please give a valid"
" subtype_id or subtype_xmlid value instead."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "message_post partner_ids and must be integer list, not commands."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "โมเดล %s ไม่ยอมรับการสร้างเอกสาร"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "เดือน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "ms"
msgstr "ms"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "muted"
msgstr "ปิดเสียง"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/utils.js:0
#, python-format
msgid "now"
msgstr "ตอนนี้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "บน"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "on:"
msgstr "เมื่อ:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "or"
msgstr "หรือ"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "other members."
msgstr "สมาชิกคนอื่น"

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read less"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read more"
msgstr "อ่านเพิ่ม"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "record:"
msgstr "บันทึก:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""
"ตอบกลับเอกสารที่หายไป (%(model)s,%(thread)s) ถอยกลับไปที่การสร้างเอกสาร"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""
"ตอบกลับโมเดล %s ที่ไม่ยอมรับการอัปเดตเอกสาร ถอยกลับไปที่การสร้างเอกสาร"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "restricted to channel members"
msgstr ""

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to followers"
msgstr "จำกัดไว้เฉพาะผู้ติดตามเท่านั้น"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to known authors"
msgstr "จำกัดเฉพาะผู้เขียนที่รู้จักเท่านั้น"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "results out of"
msgstr "ผลลัพธ์จาก"

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr "การขาย"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "save"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "some specific addresses"
msgstr "ที่อยู่เฉพาะบางแห่ง"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "สตัน:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr "ไม่ระบุโมเดลเป้าหมาย"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "ทีม"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "this document"
msgstr "เอกสารนี้"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "to close for"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "toggle push-to-talk"
msgstr "เปิดใช้งานฟีเจอร์กดเพื่อพูด"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "เปลี่ยน:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "ข้อผิดพลาดที่ไม่รู้จัก"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "ไม่ทราบโมเดลเป้าหมาย %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "using"
msgstr "โดยใช้"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/many2one_avatar_user.xml:0
#, python-format
msgid "value"
msgstr "ค่า"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "สัปดาห์"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"
