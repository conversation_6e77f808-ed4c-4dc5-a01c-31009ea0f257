<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="payment.payment_acquirer_transfer" model="payment.acquirer">
        <field name="provider">transfer</field>
        <field name="state">enabled</field>
        <field name="redirect_form_view_id" ref="redirect_form"/>
        <field name="support_authorization">False</field>
        <field name="support_fees_computation">False</field>
        <field name="support_refund"></field>
        <field name="support_tokenization">False</field>
        <!-- Clear the default value to trigger the computation of the message -->
        <field name="pending_msg" eval="False"/>
    </record>

</odoo>
