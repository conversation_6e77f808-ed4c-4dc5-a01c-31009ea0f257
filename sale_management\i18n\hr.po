# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_management
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <ivic<PERSON>.<PERSON><PERSON><PERSON><PERSON>@storm.hr>, 2022
# 0ba0ac30481a756f36528ba6f9a4317e_6443a87 <52eefe24349934c364624ef40611b7a3_1010754>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <durdi<PERSON>.z<PERSON><PERSON><PERSON>@storm.hr>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Hr<PERSON><PERSON> <<EMAIL>>, 2022
# Martin <PERSON>, 2022
# <PERSON><PERSON><PERSON> <karolina.ton<PERSON>@storm.hr>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2022\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: sale_management
#: model:sale.order.template.line,name:sale_management.sale_order_template_line_1
msgid "4 Person Desk"
msgstr "Stol za 4 osobe"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
msgid "<span>Options</span>"
msgstr "<span>Opcije</span>"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__active
msgid "Active"
msgstr "Aktivan"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a note"
msgstr "Dodaj bilješku"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a product"
msgstr "Dodaj proizvod"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a section"
msgstr "Dodaj odlomak"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add one"
msgstr "Dodaj jedan"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add to cart"
msgstr "Dodaj u košaricu"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Add to order lines"
msgstr "DOdaj stavkama narudžbe"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total
msgid "All Sales"
msgstr "Sva prodaja"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Archived"
msgstr "Arhivirano"

#. module: sale_management
#: model:ir.ui.menu,name:sale_management.menu_product_attribute_action
msgid "Attributes"
msgstr "Značajke"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_uom_category_id
msgid "Category"
msgstr "Kategorija"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_company
msgid "Companies"
msgstr "Tvrtke"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__company_id
msgid "Company"
msgstr "Tvrtka"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_config_settings
msgid "Config Settings"
msgstr "Postavke"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Confirmation"
msgstr "Potvrda"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__mail_template_id
msgid "Confirmation Mail"
msgstr "Potvrdna mail poruka"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_option__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Konverzija jedinica mjere može se vršiti samo za jedinice mjere koje "
"pripadaju istoj kategoriji. Konverzija će se izvršiti temeljem omjera "
"jedinica mjere."

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Create standardized offers with default products"
msgstr "Kreiraj standardizirane ponude sa zadanim proizvodima"

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid "Create your quotation template"
msgstr "Kreiraj predložak ponude"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_company__sale_order_template_id
msgid "Default Sale Template"
msgstr "Zadani prodajni predložak"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__company_so_template_id
msgid "Default Template"
msgstr "Zadani predložak"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__name
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Description"
msgstr "Opis"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid ""
"Design your quotation templates using building blocks<br/>\n"
"                            <em attrs=\"{'invisible': [('module_sale_quotation_builder','=',False)]}\">Warning: this option will install the Website app.</em>"
msgstr ""
"Kreiraj svoj predložak ponude koristeći blokove<br/>\n"
"                            <em attrs=\"{'invisible': [('module_sale_quotation_builder','=',False)]}\">Upozorenje: ova opcija će instalirati Website aplikaciju.</em>"

#. module: sale_management
#: model:ir.model,name:sale_management.model_digest_digest
msgid "Digest"
msgstr "Sažetak"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Disc.%"
msgstr "Pop.%"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__discount
msgid "Discount (%)"
msgstr "Popust (%)"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_type
msgid "Display Type"
msgstr "Vrsta prikaza"

#. module: sale_management
#: code:addons/sale_management/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Nemate pristup, preskočite ove podatke za sažetak e-pošte korisnika"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentacija"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_non_accountable_fields_null
msgid ""
"Forbidden product, unit price, quantity, and UoM on non-accountable sale "
"quote line"
msgstr ""
"Zabranjeni proizvod, jedinična cijena, količina i JM u ne računovodstvenoj "
"liniji ponude."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__sequence
msgid "Gives the sequence order when displaying a list of optional products."
msgstr "Daje redoslijed kada se prikazuje popis neobaveznih proizvoda."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__sequence
msgid "Gives the sequence order when displaying a list of sale quote lines."
msgstr "Daje redoslijed kada se prikazuje popis stavaka ponude."

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__id
msgid "ID"
msgstr "ID"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__active
msgid ""
"If unchecked, it will allow you to hide the quotation template without "
"removing it."
msgstr ""
"Ako nije označeno, omogućit će vam da sakrijete predložak ponude bez "
"uklanjanja."

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total_value
msgid "Kpi All Sale Total Value"
msgstr "KPI Ukupna vrijednost sve prodaje"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option____last_update
msgid "Last Modified on"
msgstr "Zadnja promjena"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__line_id
msgid "Line"
msgstr "Redak"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_line_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Lines"
msgstr "Stavke"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_accountable_product_id_required
msgid "Missing required product and UoM on accountable sale quote line."
msgstr ""
"Nedostaju obavezna polja na stavkama narudžbe za koje je moguć izračun."

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Note"
msgstr "Bilješka"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__number_of_days
msgid "Number of days for the validity date computation of the quotation"
msgstr "Broj dana za izračun datuma valjanosti ponude"

#. module: sale_management
#: model:sale.order.template.option,name:sale_management.sale_order_template_option_1
msgid "Office Chair"
msgstr "Uredska stolica"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_payment
msgid "Online Payment"
msgstr "Online plaćanje"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_signature
msgid "Online Signature"
msgstr "Mrežni potpis"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_option_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Optional Products"
msgstr "Mogući proizvodi"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_option_ids
#: model:ir.model.fields,field_description:sale_management.field_sale_order_line__sale_order_option_ids
msgid "Optional Products Lines"
msgstr ""
"Opcionalne stavke\n"
" "

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Options"
msgstr "Opcije"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__is_present
msgid "Present on Quotation"
msgstr "Prisutno na ponudi"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_id
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Product"
msgstr "Proizvod"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__quantity
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_qty
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__quantity
msgid "Quantity"
msgstr "Količina"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Quantity:"
msgstr "Količina:"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__module_sale_quotation_builder
msgid "Quotation Builder"
msgstr "Izrađivač ponuda"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__number_of_days
msgid "Quotation Duration"
msgstr "Trajanje ponude"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__name
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_tree
msgid "Quotation Template"
msgstr "Predložak ponude"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Stavke predloška ponude"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation Template Lines"
msgstr "Reci predloška ponude"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "Opcije predloška ponude"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__sale_order_template_id
msgid "Quotation Template Reference"
msgstr "Referenca predloška ponude"

#. module: sale_management
#: model:ir.actions.act_window,name:sale_management.sale_order_template_action
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__group_sale_order_template
#: model:ir.ui.menu,name:sale_management.sale_order_template_menu
#: model:res.groups,name:sale_management.group_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Quotation Templates"
msgstr "Predlošci ponude"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation expires after"
msgstr "Ponuda istječe nakon"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove"
msgstr "Ukloni"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove one"
msgstr "Ukloni jedan"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr ""
"Zatražite online potpis od kupca kako bi se narudžbe automatski potvrdile."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr ""
"Zatražite online plaćanje kupca kako bi se narudžbe automatski potvrdile."

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_option
msgid "Sale Options"
msgstr "Opcija prodaje"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.digest_digest_view_form
msgid "Sales"
msgstr "Prodaja"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_line
msgid "Sales Order Line"
msgstr "Stavka prodajnog naloga"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__order_id
msgid "Sales Order Reference"
msgstr "Referenca prodajnog naloga"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Sales Quotation Template Lines"
msgstr "Reci predloška prodajne ponude"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Search Quotation Template"
msgstr "Pretraži predložak ponude"

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_section
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Section"
msgstr "Odlomak"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid ""
"Selling the same product in different sizes or colors? Try the product grid "
"and populate your orders with multiple quantities of each variant. This "
"feature also exists in the Purchase application."
msgstr ""
"Prodajete isti proizvod u različitim veličinama ili bojama? Isprobajte mrežu"
" proizvoda i popunite svoje narudžbe s više količina svake varijante. Ova "
"značajka postoji i u aplikaciji Nabava"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__sequence
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid ""
"Struggling with a complex product catalog? Try out the Product Configurator "
"to help sales configure a product with different options: colors, size, "
"capacity, etc. Make sale orders encoding easier and error-proof."
msgstr ""
"Mučite se sa složenim katalogom proizvoda? Isprobajte Konfigurator proizvoda"
" kako biste pomogli prodaji da konfigurira proizvod s različitim opcijama: "
"bojama, veličinom, kapacitetom itd. Učinite kreiranje prodajnih naloga "
"lakšima i sigurnima od pogreške."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__display_type
msgid "Technical field for UX purpose."
msgstr "Tehničko područje za UX namjenu."

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__note
msgid "Terms and conditions"
msgstr "Uvijeti o korištenju"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid ""
"The Administrator can set default Terms & Conditions in Sales Settings. "
"Terms set here will show up instead if you select this quotation template."
msgstr ""
"Administrator može postaviti zadane Uvjete i odredbe u postavkama prodaje. "
"Umjesto toga, pojavit će se ovdje postavljeni uvjeti ako odaberete ovaj "
"predložak ponude."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__mail_template_id
msgid ""
"This e-mail template will be sent on confirmation. Leave empty to send "
"nothing."
msgstr ""
"Ovaj predložak e-pošte bit će poslan nakon potvrde. Ostavite prazno da ništa"
" ne šaljete."

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__is_present
msgid ""
"This field will be checked if the option line's product is already present "
"in the quotation."
msgstr ""
"Ovo polje će biti provjereno ako je opcija stavke proizvod već prisutan u "
"ponudi."

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale1_management_0
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid "Tip: Odoo supports configurable products"
msgstr "Savjet: Odoo podržava proizvode koji se mogu konfigurirati"

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale_management_1
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid "Tip: Sell or buy products in bulk with matrixes"
msgstr "Savjet: prodajte ili kupujte proizvode na veliko s matricama"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__price_unit
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Unit Price:"
msgstr "Jedinična cijena"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_id
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__uom_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__uom_id
msgid "Unit of Measure "
msgstr "Mjerna jedinica "

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "UoM"
msgstr "UoM"

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid ""
"Use templates to create polished, professional quotes in minutes.\n"
"                Send these quotes by email and let your customers sign online.\n"
"                Use cross-selling and discounts to push and boost your sales."
msgstr ""
"Koristite predloške za stvaranje uglađenih, profesionalnih ponuda u nekoliko minuta.\n"
"Pošaljite ove ponude e-poštom i dopustite svojim klijentima da se potpišu na mreži.\n"
"Upotrijebite unakrsnu prodaju i popuste kako biste potaknuli i povećali svoju prodaju."

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid "You cannot add options to a confirmed order."
msgstr "Ne možete dodati opcije na potvrđenu narudžbu."

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"You cannot change the type of a sale quote line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"Ne možete promijeniti vrstu retka ponude za prodaju. Umjesto toga trebali "
"biste izbrisati trenutnu stavku i stvoriti novu odgovarajuće vrste."

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Vaša ponuda sadrži artikle tvrtke %(product_company)s dok Vaša ponuda pripada tvrtki %(quote_company)s. \n"
" Molimo promijenite tvrtku Vaše ponude ili uklonite artikle drugih tvrtki (%(bad_products)s)."

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid "Your template cannot contain products from multiple companies."
msgstr "Vaš predložak ne može sadržavati proizvode više tvrtki."

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"Your template contains products from company %(product_company)s whereas your template belongs to company %(template_company)s. \n"
" Please change the company of your template or remove the products from other companies."
msgstr ""
"Vaš predložak sadrži proizvode tvrtke %(product_company)s dok vaš predložak pripada tvrtki %(template_company)s. \n"
"Promijenite tvrtku svog predloška ili uklonite proizvode iz drugih tvrtki."

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "days"
msgstr "dana"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "e.g. Standard Consultancy Package"
msgstr "npr. Standardni savjetodavni paket"
