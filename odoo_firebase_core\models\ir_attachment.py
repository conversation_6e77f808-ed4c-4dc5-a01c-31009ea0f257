from odoo import models, fields


class OdooFireIrAttachment(models.Model):
    _inherit = "ir.attachment"

    firebase_item_ids = fields.One2many(
        string="Firebase items",
        comodel_name="firebase.storage.item",
        inverse_name="attachment_id"
    )
    cloud_key = fields.Char(
        string="Cloud key",
        compute="_compute_firebase_key"
    )
    cloud_path = fields.Char(
        string="Cloud path",
        copy=False,
        compute="_compute_firebase_key"
    )
    cloud_url = fields.Char(
        string="Cloud URL",
        copy=False,
        compute="_compute_firebase_key"
    )
    cloud_last_sync = fields.Datetime(
        string="Cloud Last Sync",
        copy=False,
        compute="_compute_firebase_key"
    )

    def _compute_firebase_key(self):
        for attach in self:
            if not attach.firebase_item_ids:
                attach.cloud_key = False
                attach.cloud_path = False
                attach.cloud_url = False
                attach.cloud_last_sync = False
                continue
            item = attach.firebase_item_ids.filtered(
                lambda x: x.rule_id.is_public
            )
            if not item:
                item = attach.firebase_item_ids[0]
            attach.cloud_key = item.cloud_key
            attach.cloud_path = item.cloud_path
            attach.cloud_url = item.cloud_url
            attach.cloud_last_sync = item.cloud_last_sync
