# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_mine
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# arfa simon<PERSON>i, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: crm_iap_mine
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "%d credits will be consumed to find %d companies."
msgstr "%d kredit akan dikonsumsi untuk mencari %d perusahaan."

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.enrich_company
msgid "<b>Contacts</b>"
msgstr "<b>Kontak</b>"

#. module: crm_iap_mine
#: model:mail.template,body_html:crm_iap_mine.lead_generation_no_credits
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p>Dear,</p>\n"
"    <p>There are no more credits on your IAP Lead Generation account.<br/>\n"
"    You can charge your IAP Lead Generation account in the settings of the CRM app.<br/></p>\n"
"    <p>Best regards,</p>\n"
"    <p>Odoo S.A.</p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p>Yth,</p>\n"
"    <p>Tidak ada lagi kredit pada akun Lead generation IAP Anda.<br/>\n"
"    Anda dapat mengisi akun Lead Generaiton IAP Anda di halaman pengaturan CRM.<br/></p>\n"
"    <p>Salam Hormat,</p>\n"
"    <p>Odoo S.A.</p>\n"
"</div>"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid ""
"<span attrs=\"{'invisible': [('error_type', '!=', 'no_result')]}\">Your "
"request did not return any result (no credits were used). Try removing some "
"filters.</span>"
msgstr ""
"<span attrs=\"{'invisible': [('error_type', '!=', "
"'no_result')]}\">Permintaan Anda tidak mengembalikkan hasil apapun (tidak "
"ada kredit yang digunakan). Coba hapus beberapa filter.</span>"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "<span class=\"col-md-6\">Extra contacts per Company</span>"
msgstr "<span class=\"col-md-6\">Kontak ekstra per Perusahaan</span>"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "<span class=\"o_stat_text\">Leads</span>"
msgstr "<span class=\"o_stat_text\">Lead</span>"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "<span class=\"o_stat_text\">Opportunities</span>"
msgstr "<span class=\"o_stat_text\">Opportunities</span>"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_238
msgid "Automobiles & Components"
msgstr "Automobil & Komponen"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__available_state_ids
msgid "Available State"
msgstr "Negara Bagian yang Tersedia"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_69_157
msgid "Banks & Insurance"
msgstr "Bank & Asuransi"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Buy credits."
msgstr "Beli kredit."

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_1
msgid "CEO"
msgstr "CEO"

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_industry
msgid "CRM IAP Lead Industry"
msgstr "CRM IAP Industri Lead"

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_mining_request
msgid "CRM Lead Mining Request"
msgstr "CRM Permintaan Lead Mining"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Cancel"
msgstr "Batal"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_162
msgid "Capital Goods"
msgstr "Barang Modal"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__color
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__color
msgid "Color Index"
msgstr "Indeks Warna"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_163
msgid "Commercial & Professional Services"
msgstr "Layanan Komersil & Profesional"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__search_type__companies
msgid "Companies"
msgstr "Perusahaan"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__search_type__people
msgid "Companies and their Contacts"
msgstr "Perusahaan dan Kontak mereka"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__company_size_max
msgid "Company Size Max"
msgstr "Ukuran Maksimal Perusahaan"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_167
msgid "Construction Materials"
msgstr "Material Konstruksi"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_30_155
msgid "Consumer Discretionary"
msgstr "Diskresioner Pelanggan"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_239
msgid "Consumer Durables & Apparel"
msgstr "Consumer Durables & Apparel"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_150_151
msgid "Consumer Services"
msgstr "Layanan Konsumer"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_33
msgid "Consumer Staples"
msgstr "Consumer Staples"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__country_ids
msgid "Countries"
msgstr "Negara"

#. module: crm_iap_mine
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Create a Lead Mining Request"
msgstr "Buat Permintaan Lead Mining"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__create_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__create_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__create_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__create_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__create_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__create_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__create_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__create_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Default Tags"
msgstr "Tag Default"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__display_lead_label
msgid "Display Lead Label"
msgstr "Tampilkan Label Lead"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__display_name
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__display_name
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__display_name
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__display_name
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_158_159
msgid "Diversified Financials & Financial Services"
msgstr "Keuangan Terdiversifikasi & Layanan Keuangan"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__state__done
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Done"
msgstr "Selesai"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__state__draft
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Draft"
msgstr "Draft"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.enrich_company
msgid "Email"
msgstr "Email"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_138_156
msgid "Energy & Utilities "
msgstr "Energi & Utilitas"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__state__error
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Error"
msgstr "Eror!"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__error_type
msgid "Error Type"
msgstr "Tipe Error"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__contact_filter_type
msgid "Filter on"
msgstr "Menyaring"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__filter_on_size
msgid "Filter on Size"
msgstr "Filter berdasarkan Ukruan"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_153_154
msgid "Food, Beverage & Tobacco"
msgstr "Makanan, Minuman & Tembakau"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "From"
msgstr "Dari"

#. module: crm_iap_mine
#. openerp-web
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_mine/static/src/xml/leads_tree_generate_leads_views.xml:0
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
#, python-format
msgid "Generate Leads"
msgstr "Buat Lead"

#. module: crm_iap_mine
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Generate new leads based on their country, industry, size, etc."
msgstr "Buat lead baru berdasarkan negara mereka, industri, ukuran, dsb."

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_ids
msgid "Generated Lead / Opportunity"
msgstr "Lead / Peluang yang Dibuat"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_160
msgid "Health Care Equipment & Services"
msgstr "Peralataan & Layanan Tenaga Kesehatan"

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_helpers
msgid "Helper methods for crm_iap_mine modules"
msgstr "Metode helper untuk modul crm_iap_mine "

#. module: crm_iap_mine
#: model:mail.template,name:crm_iap_mine.lead_generation_no_credits
#: model:mail.template,subject:crm_iap_mine.lead_generation_no_credits
msgid "IAP Lead Generation Notification"
msgstr "Notifikasi IAP Pembuatan Lead"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__id
msgid "ID"
msgstr "ID"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_168
msgid "Independent Power and Renewable Electricity Producers"
msgstr "Produsen Listrik Mandiri dan Terbarukan"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__industry_ids
msgid "Industries"
msgstr "Industri"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__name
msgid "Industry"
msgstr "Industri"

#. module: crm_iap_mine
#: model:ir.model.constraint,message:crm_iap_mine.constraint_crm_iap_lead_industry_name_uniq
msgid "Industry name already exists!"
msgstr "Nama industri sudah ada!"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__error_type__credits
msgid "Insufficient Credits"
msgstr "Kredit Tidak Mencukupi"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers____last_update
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry____last_update
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request____last_update
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role____last_update
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__write_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__write_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__write_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__write_uid
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_helpers__write_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__write_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__write_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__write_date
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_contacts_credits
msgid "Lead Contacts Credits"
msgstr "Kredit Kontak Lead"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_credits
msgid "Lead Credits"
msgstr "Kredit Lead"

#. module: crm_iap_mine
#: model:ir.ui.menu,name:crm_iap_mine.crm_menu_lead_generation
msgid "Lead Generation"
msgstr "Pembuatan Lead"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_lead__lead_mining_request_id
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Lead Mining Request"
msgstr "Permintaan Lead Mining"

#. module: crm_iap_mine
#: model:ir.actions.act_window,name:crm_iap_mine.crm_iap_lead_mining_request_action
#: model:ir.ui.menu,name:crm_iap_mine.crm_iap_lead_mining_request_menu_action
msgid "Lead Mining Requests"
msgstr "Permintaan-Permintaan Lead Mining"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_total_credits
msgid "Lead Total Credits"
msgstr "Total Kredit Lead"

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Prospek/Peluang"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__lead_type__lead
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Leads"
msgstr "Prospek"

#. module: crm_iap_mine
#: model:ir.model.fields,help:crm_iap_mine.field_crm_iap_lead_mining_request__available_state_ids
msgid "List of available states based on selected countries"
msgstr ""

#. module: crm_iap_mine
#. openerp-web
#: code:addons/crm_iap_mine/static/src/js/tours/crm_iap_lead.js:0
#, python-format
msgid ""
"Looking for more opportunities ?<br>Try the <b>Lead Generation</b> tool."
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_148
msgid "Materials"
msgstr "Material"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_86
msgid "Media"
msgstr "Media"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__name
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.enrich_company
msgid "Name"
msgstr "Nama"

#. module: crm_iap_mine
#: model:ir.model.constraint,message:crm_iap_mine.constraint_crm_iap_lead_seniority_name_uniq
msgid "Name already exists!"
msgstr "Nama sudah ada!"

#. module: crm_iap_mine
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "New"
msgstr "Baru"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__error_type__no_result
msgid "No Result"
msgstr "Tidak Ada Hasil"

#. module: crm_iap_mine
#. openerp-web
#: code:addons/crm_iap_mine/static/src/js/tours/crm_iap_lead.js:0
#, python-format
msgid "Now, just let the magic happen!"
msgstr "Now, just let the magic happen!"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__contact_number
msgid "Number of Contacts"
msgstr "Jumlah Kontak"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_count
msgid "Number of Generated Leads"
msgstr "Jumlah Lead yang Dibuat"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_number
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_tree
msgid "Number of Leads"
msgstr "Jumlah Lead"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__lead_type__opportunity
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Opportunities"
msgstr "Peluang"

#. module: crm_iap_mine
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Opportunity created by Odoo Lead Generation"
msgstr "Peluang dibuat oleh Odoo Lead Generation"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__role_ids
msgid "Other Roles"
msgstr "Peran Lain"

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_role
msgid "People Role"
msgstr "Peran Orang"

#. module: crm_iap_mine
#: model:ir.model,name:crm_iap_mine.model_crm_iap_lead_seniority
msgid "People Seniority"
msgstr "Senioritas Orang"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_161
msgid "Pharmaceuticals, Biotechnology & Life Sciences"
msgstr "Farmasi, Bioteknologi & Life Science"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.enrich_company
msgid "Phone"
msgstr "Telepon"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__preferred_role_id
msgid "Preferred Role"
msgstr "Peran Pilihan"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_114
msgid "Real Estate"
msgstr "Real Estate"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__name
msgid "Request Number"
msgstr "Nomor Permintaan"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_152
msgid "Retailing"
msgstr "Ritel"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Retry"
msgstr "Ulangi"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__reveal_ids
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__reveal_id
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_seniority__reveal_id
msgid "Reveal"
msgstr "Reveal"

#. module: crm_iap_mine
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__contact_filter_type__role
msgid "Role"
msgstr "Peran"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_role__name
msgid "Role Name"
msgstr "Nama Peran"

#. module: crm_iap_mine
#: model:ir.model.constraint,message:crm_iap_mine.constraint_crm_iap_lead_role_name_uniq
msgid "Role name already exists!"
msgstr "Nama peran sudah ada!"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__team_id
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Sales Team"
msgstr "Tim Penjualan"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__user_id
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Salesperson"
msgstr "Penjual"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__seniority_id
#: model:ir.model.fields.selection,name:crm_iap_mine.selection__crm_iap_lead_mining_request__contact_filter_type__seniority
msgid "Seniority"
msgstr "Senioritas"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_industry__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__company_size_min
msgid "Size"
msgstr "Ukuran"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_165
msgid "Software & Services"
msgstr "Software & Layanan"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__state_ids
msgid "States"
msgstr "Status"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__state
msgid "Status"
msgstr "Status"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "Submit"
msgstr "Menyerahkan"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__tag_ids
msgid "Tags"
msgstr "Label"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__search_type
msgid "Target"
msgstr "Target"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_166
msgid "Technology Hardware & Equipment"
msgstr "Hardware & Peralatan Teknologi"

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_149
msgid "Telecommunication Services"
msgstr "Layanan Telekomunikasi"

#. module: crm_iap_mine
#: model:ir.model.fields,help:crm_iap_mine.field_crm_iap_lead_industry__reveal_ids
msgid "The list of reveal_ids for this industry, separated with ','"
msgstr ""

#. module: crm_iap_mine
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "This makes a total of %d credits for this request."
msgstr "Ini berarti total kredit untuk permintaan ini adalah %d."

#. module: crm_iap_mine
#: model:crm.iap.lead.industry,name:crm_iap_mine.crm_iap_mine_industry_136
msgid "Transportation"
msgstr "Transportasi"

#. module: crm_iap_mine
#: model:ir.model.fields,field_description:crm_iap_mine.field_crm_iap_lead_mining_request__lead_type
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_search
msgid "Type"
msgstr "Jenis"

#. module: crm_iap_mine
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid ""
"Up to %d additional credits will be consumed to identify %d contacts per "
"company."
msgstr ""
"Sampai dengan %d kredit tambahan akan dikonsumsi untuk mengidentifikasi %d "
"kontak per perusahaan."

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "What do you need ?"
msgstr ""

#. module: crm_iap_mine
#. openerp-web
#: code:addons/crm_iap_mine/static/src/js/tours/crm_iap_lead.js:0
#, python-format
msgid "Which Industry do you want to target?"
msgstr "Industri mana yang Anda ingin sasar?"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "You do not have enough credits to submit this request."
msgstr ""
"Anda tidak memiliki kredit yang mencukupi untuk mengajukan permintaan ini."

#. module: crm_iap_mine
#: code:addons/crm_iap_mine/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Your request could not be executed: %s"
msgstr "Permintaan Anda tidak dapat dilakukan: %s"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_2
msgid "communications"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_3
msgid "consulting"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_4
msgid "customer_service"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.seniority,name:crm_iap_mine.crm_iap_mine_seniority_1
msgid "director"
msgstr "direktur"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_5
msgid "education"
msgstr ""

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "employees"
msgstr "karyawan"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_6
msgid "engineering"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.seniority,name:crm_iap_mine.crm_iap_mine_seniority_2
msgid "executive"
msgstr "eksekutif"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_7
msgid "finance"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_8
msgid "founder"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_9
msgid "health_professional"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_10
msgid "human_resources"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_11
msgid "information_technology"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_12
msgid "legal"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.seniority,name:crm_iap_mine.crm_iap_mine_seniority_3
msgid "manager"
msgstr "manajer"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_13
msgid "marketing"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_14
msgid "operations"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_15
msgid "owner"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_16
msgid "president"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_17
msgid "product"
msgstr "produk"

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_18
msgid "public_relations"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_19
msgid "real_estate"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_20
msgid "recruiting"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_21
msgid "research"
msgstr ""

#. module: crm_iap_mine
#: model:crm.iap.lead.role,name:crm_iap_mine.crm_iap_mine_role_22
msgid "sale"
msgstr "sale"

#. module: crm_iap_mine
#: model_terms:ir.ui.view,arch_db:crm_iap_mine.crm_iap_lead_mining_request_view_form
msgid "to"
msgstr "kepada"
