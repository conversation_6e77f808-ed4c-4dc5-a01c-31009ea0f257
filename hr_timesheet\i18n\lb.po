# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-23 11:31+0000\n"
"PO-Revision-Date: 2019-08-26 09:11+0000\n"
"Last-Translator: <PERSON> ALT <<EMAIL>>, 2019\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for project:</em>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
msgid "<span class=\"o_label\">Timesheets</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "<span class=\"o_stat_text\">Timesheets</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "<span> planned hours</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "<span>Date</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "<span>Description</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "<span>Responsible</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "<span>Time</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "<span>Timesheet Entries</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<strong>Duration: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "<strong>Total</strong>"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
msgid "Activities"
msgstr ""

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_timesheet_manager
msgid "Administrator"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "All"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_all
msgid "All Timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__allow_timesheets
msgid "Allow timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__analytic_account_active
msgid "Analytic Account"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Analytic Entry"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Apple App Store"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_synchro
msgid "Awesome Timesheet"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_activity_analysis
msgid "By Employee"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_project
msgid "By Project"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_task
msgid "By Task"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_company
msgid "Companies"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__total_hours_spent
msgid "Computed as: Time Spent + Sub-tasks Hours."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__effective_hours
msgid "Computed using the sum of the task work done."
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_menu_configuration
msgid "Configuration"
msgstr "Konfiguratioun"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Create timesheets upon time off validation"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__currency_id
msgid "Currency"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Date"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__department_id
msgid "Department"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Description"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__progress
msgid "Display progress of current task."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Duration"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Duration (%s)"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_effective
msgid "Effective Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__employee_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Employee"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__allow_timesheets
msgid "Enable timesheeting on the project."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid "Encoding Unit"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__encoding_uom_id
msgid "Encoding Uom"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Chrome Store"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Play Store"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Group By"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__effective_hours
msgid "Hours Spent"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__analytic_account_active
msgid ""
"If the active field is set to False, it will allow you to hide the account "
"without removing it."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Initially Planned Hours"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last month"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last week"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last year"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid "Log time on tasks"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_mine
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "My Timesheets"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "None"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_planned
msgid "Planned Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__progress
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__progress
msgid "Progress"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#, python-format
msgid "Project"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "Project -"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid "Project Time Unit"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_holidays
msgid "Record Time Off"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid "Record a new activity"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Remaining Hours"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_kanban_inherited_progress
msgid "Remaining hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reporting"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr ""

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_user
msgid "See all Timesheets"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Set the time unit used to record your timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.hr_timesheet_config_settings_action
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_config_settings_menu_action
msgid "Settings"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Spent Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Sub-tasks Hours Spent"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Sum of actually spent hours on the subtask(s)"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Synchronize time spent with our web/mobile apps"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "Task"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr ""

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_approver
msgid "Team Approver"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"The project you are timesheeting on is not linked to an active analytic "
"account. Set one on the project configuration."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "There are no timesheets."
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This Quarter"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This month"
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This task must be part of a project because there are some timesheets linked"
" to it."
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This week"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right unit of measure in your employees."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__timesheet_encode_uom_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This year"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Encoding"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Off"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
msgid "Timesheet"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__timesheet_cost
msgid "Timesheet Cost"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.report,name:hr_timesheet.timesheet_report
msgid "Timesheet Entries"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Timesheet by Date"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_from_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allow_timesheets
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__timesheet_ids
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_layout
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_report
msgid "Timesheets By Employee"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_project
msgid "Timesheets By Project"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_task
msgid "Timesheets By Task"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__allow_timesheets
msgid "Timesheets can be logged on this task."
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"To allow timesheet, your project %s should have an analytic account set."
msgstr ""

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Today"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
msgid "Total"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__total_hours_spent
msgid "Total Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__remaining_hours
msgid ""
"Total remaining time, can be re-estimated periodically by the assignee of "
"the task."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_uom_uom__timesheet_widget
msgid "Widget"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_uom_uom__timesheet_widget
msgid ""
"Widget used in the webclient when this unit is the one used to encode "
"timesheets."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"You can not log timesheets on this project since is linked to an inactive "
"analytic account. Please change it, or reactivate the current one to "
"timesheet on the project."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid ""
"You can register and track your workings hours by project every\n"
"                day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                customers if required."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "per hour"
msgstr ""
