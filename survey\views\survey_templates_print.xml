<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <!-- Survey: printable page view (all pages) -->
    <template id="survey_page_print" name="Survey: print page">
        <t t-call="survey.layout">
            <t t-set="survey_form_readonly" t-value="true"/>
            <t t-if="answer.test_entry" t-call="survey.survey_button_form_view" />
            <div class="wrap">
                <div class="o_survey_print container">
                    <div class='py-5 mt32'>
                        <h1><span t-field='survey.title' class="text-break"/></h1>
                        <t t-if="survey.description"><div t-field='survey.description' class="oe_no_empty text-break"/></t>
                        <t t-if="review" t-call="survey.survey_button_retake"/>
                    </div>
                    <div role="form">
                        <fieldset disabled="disabled">
                            <t t-foreach='survey.question_and_page_ids' t-as='question'>
                                <t t-if="question.is_page and
                                            (any(q in questions_to_display for q in question.question_ids)
                                            or not is_html_empty(question.description))">
                                    <hr t-if="question != survey.page_ids[0]" />
                                    <div class="o_page_header">
                                        <h1 t-field='question.title' class="text-break" />
                                        <div t-if="question.description" t-field='question.description' class="oe_no_empty text-break"/>
                                    </div>
                                </t>
                                <t t-if="not question.is_page and not answer or (question in answer.predefined_question_ids &amp; questions_to_display)" >
                                    <t t-set="answer_lines" t-value="answer.user_input_line_ids.filtered(lambda line: line.question_id == question)"/>
                                    <div class="js_question-wrapper" t-att-id="question.id">
                                        <h2>
                                            <span t-field='question.title' class="text-break"/>
                                            <span t-if="question.constr_mandatory" class="text-danger">*</span>
                                            <span t-if="scoring_display_correction" class="badge badge-pill" t-att-data-score-question="question.id"></span>
                                        </h2>
                                        <t t-if="question.description"><div class="text-muted oe_no_empty text-break" t-field='question.description'/></t>
                                        <t t-if="question.question_type == 'text_box'"><t t-call="survey.question_text_box"/></t>
                                        <t t-if="question.question_type == 'char_box'"><t t-call="survey.question_char_box"/></t>
                                        <t t-if="question.question_type == 'numerical_box'"><t t-call="survey.question_numerical_box"/></t>
                                        <t t-if="question.question_type == 'date'"><t t-call="survey.question_date"/></t>
                                        <t t-if="question.question_type == 'datetime'"><t t-call="survey.question_datetime"/></t>
                                        <t t-if="question.question_type == 'simple_choice'"><t t-call="survey.question_simple_choice"/></t>
                                        <t t-if="question.question_type == 'multiple_choice'"><t t-call="survey.question_multiple_choice"/></t>
                                        <t t-if="question.question_type == 'matrix'"><t t-call="survey.question_matrix"/></t>
                                        <div class="o_survey_question_error overflow-hidden border-0 py-0 px-3 alert alert-danger" role="alert"></div>
                                    </div>
                                </t>
                            </t>
                        </fieldset>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- simple template with no assets, to show title on the certification preview -->
    <template id="certification_preview">
        <html>
            <head>
                <title t-esc="'%s Preview' % page_title"/>
                <link rel="shortcut icon" href="/web/static/img/favicon.ico" type="image/x-icon"/>
            </head>
            <body style="margin:0;">
                <iframe type="application/pdf" t-att-src="preview_url" frameBorder="0" width="100%" height="100%"/>
            </body>
        </html>
    </template>
</data>
</odoo>
