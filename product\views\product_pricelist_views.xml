<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record model="ir.ui.view" id="product_pricelist_item_view_search">
            <field name="name">product.pricelist.item.search</field>
            <field name="model">product.pricelist.item</field>
            <field name="arch" type="xml">
                <search string="Products Price Rules Search">
                    <filter name="Product Rule" domain="[('applied_on', '=', '1_product')]"/>
                    <filter name="Variant Rule" domain="[('applied_on', '=', '0_product_variant')]" groups="product.group_product_variant"/>
                    <separator/>
                    <field name="pricelist_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="currency_id" groups="base.group_multi_currency"/>
                    <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Product" name="groupby_product" domain="[]" context="{'group_by': 'product_tmpl_id'}"/>
                        <filter string="Variant"
                          name="groupby_product_variant"
                          domain="[('applied_on', '=', '0_product_variant')]"
                          context="{'group_by': 'product_tmpl_id'}"
                          groups="product.group_product_variant"/>
                        <filter string="Pricelist"
                          name="groupby_vendor"
                          domain="[]"
                          context="{'group_by': 'pricelist_id'}"
                          groups="product.group_product_pricelist"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="product_pricelist_item_tree_view" model="ir.ui.view">
          <field name="name">product.pricelist.item.tree</field>
          <field name="model">product.pricelist.item</field>
          <field name="priority">10</field>
          <field name="arch" type="xml">
            <tree string="Price Rules">
              <field name="pricelist_id"/>
              <field name="name" string="Applied On"/>
              <field name="price"/>
              <field name="min_quantity" colspan="4"/>
              <field name="date_start" optional="hide"/>
              <field name="date_end" optional="hide"/>
              <field name="company_id" groups="base.group_multi_company" optional="show"/>
            </tree>
          </field>
        </record>

        <record id="product_pricelist_item_tree_view_from_product" model="ir.ui.view">
            <!-- Access and edit price rules from a given product/product variant -->
            <field name="name">product.pricelist.item.tree</field>
            <field name="model">product.pricelist.item</field>
            <field name="priority">100</field>
            <field name="arch" type="xml">
                <tree string="Pricelist Rules" editable="bottom">
                    <!-- Scope = coming from a product/product template -->
                    <field name="pricelist_id" string="Pricelist" options="{'no_create_edit':1, 'no_open': 1}"/>
                    <field name="name" string="Applied On"/>
                    <field name="categ_id" invisible="1"/>
                    <field name="product_tmpl_id"
                      invisible="context.get('active_model')!='product.category'"
                      attrs="{'required': [('applied_on', '=', '1_product')]}"
                      domain="[('categ_id', '=', context.get('default_categ_id', True)), '|', ('company_id', '=', company_id), ('company_id', '=', False)]"
                      options="{'no_create_edit':1, 'no_open': 1}"/>
                    <field name="product_id"
                      groups="product.group_product_variant"
                      invisible="context.get('product_without_variants', False)"
                      readonly="context.get('active_model')=='product.product'"
                      attrs="{'required': [('applied_on', '=', '0_product_variant')]}"
                      domain="['|', '|',
                        ('id', '=', context.get('default_product_id', 0)),
                        ('product_tmpl_id', '=', context.get('default_product_tmpl_id', 0)),
                        ('categ_id', '=', context.get('default_categ_id', 0)), '|', ('company_id', '=', company_id), ('company_id', '=', False)
                      ]"
                      options="{'no_create_edit':1, 'no_open': 1}"
                      />
                    <field name="min_quantity" colspan="4"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="fixed_price" string="Price" required='1'/>
                    <field name="date_start" optional="show"/>
                    <field name="date_end" optional="show"/>
                    <field name="applied_on" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company" optional="show" options="{'no_create':1, 'no_open': 1}"/>
                </tree>
            </field>
        </record>

        <record id="product_pricelist_item_form_view" model="ir.ui.view">
            <field name="name">product.pricelist.item.form</field>
            <field name="model">product.pricelist.item</field>
            <field name="arch" type="xml">
                <form string="Pricelist Rule">
                    <sheet>
                        <field name="name" invisible="1"/>
                        <group name="pricelist_rule_computation" groups="product.group_sale_pricelist" string="Price Computation">
                            <group name="pricelist_rule_method">
                                <field name="compute_price" string="Computation" widget="radio"/>
                            </group>
                            <div>
                                <div class="alert alert-info" role="alert" groups="uom.group_uom">
                                    The computed price is expressed in the default Unit of Measure of the product.
                                </div>
                            </div>
                        </group>
                        <group name="pricelist_rule_base" groups="product.group_sale_pricelist">
                            <group>
                                <field name="price" invisible="1"/>
                                <field name="fixed_price" widget="monetary" attrs="{'invisible':[('compute_price', '!=', 'fixed')]}"/>
                                <label for="percent_price" string="Discount" attrs="{'invisible':[('compute_price', '!=', 'percentage')]}"/>
                                <div class="o_row" attrs="{'invisible':[('compute_price', '!=', 'percentage')]}">
                                    <field name="percent_price" class="oe_inline" attrs="{'invisible':[('compute_price', '!=', 'percentage')]}"/>%%
                                </div>
                                <field name="base" attrs="{'invisible':[('compute_price', '!=', 'formula')]}"/>
                                <field name="base_pricelist_id" attrs="{
                                    'invisible': ['|', ('compute_price', '!=', 'formula'), ('base', '!=', 'pricelist')],
                                    'required': [('compute_price', '=', 'formula'), ('base', '=', 'pricelist')],
                                    'readonly': [('base', '!=', 'pricelist')]}"/>
                                <label for="price_discount" string="Discount" attrs="{'invisible':[('compute_price', '!=', 'formula')]}"/>
                                <div class="o_row" attrs="{'invisible':[('compute_price', '!=', 'formula')]}">
                                    <field name="price_discount"/>
                                    <span>%%</span>
                                </div>
                                <field name="price_surcharge" widget="monetary" string="Extra Fee" attrs="{'invisible':[('compute_price', '!=', 'formula')]}"/>
                                <field name="price_round" string="Rounding Method" attrs="{'invisible':[('compute_price', '!=', 'formula')]}"/>
                                <label string="Margins" for="price_min_margin" attrs="{'invisible':[('compute_price', '!=', 'formula')]}"/>
                                <div class="o_row" attrs="{'invisible':[('compute_price', '!=', 'formula')]}">
                                    <field name="price_min_margin" string="Min. Margin" class="oe_inline" widget="monetary" nolabel="1"/>
                                    <i class="fa fa-long-arrow-right mx-2 oe_edit_only" aria-label="Arrow icon" title="Arrow"/>
                                    <field name="price_max_margin" string="Max. Margin" class="oe_inline" widget="monetary" nolabel="1"/>
                                </div>
                            </group>
                            <group>
                                <div class="alert alert-info" role="alert" style="white-space: pre;" attrs="{'invisible':[('compute_price', '!=', 'formula')]}">
                                    <field name="rule_tip"/>
                                </div>
                            </group>
                        </group>

                        <group string="Conditions">
                            <group name="pricelist_rule_target">
                                <field name="applied_on" widget="radio"/>
                                <field name="categ_id" options="{'no_create':1}" attrs="{
                                    'invisible':[('applied_on', '!=', '2_product_category')],
                                    'required':[('applied_on', '=', '2_product_category')]}"/>
                                <field name="product_tmpl_id" options="{'no_create':1}" attrs="{
                                    'invisible':[('applied_on', '!=', '1_product')],
                                    'required':[('applied_on', '=', '1_product')]}"/>
                                <field name="product_id" options="{'no_create':1}" attrs="{
                                    'invisible':[('applied_on', '!=', '0_product_variant')],
                                    'required':[('applied_on', '=', '0_product_variant')]}"/>
                            </group>
                            <group name="pricelist_rule_limits">
                                <field name="min_quantity"/>
                                <label for="date_start" string="Validity"/>
                                <div class="o_row">
                                    <field name="date_start" widget="daterange" options='{"related_end_date": "date_end"}'/>
                                    <i class="fa fa-long-arrow-right mx-2 oe_edit_only" aria-label="Arrow icon" title="Arrow"/>
                                    <field name="date_end" widget="daterange" options='{"related_start_date": "date_start"}'/>
                                </div>
                            </group>
                            <group name="pricelist_rule_related" groups="base.group_no_one">
                                <field name="pricelist_id" invisible="1"/>
                                <field name="currency_id" groups="base.group_multi_currency"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="product_pricelist_view_search">
            <field name="name">product.pricelist.search</field>
            <field name="model">product.pricelist</field>
            <field name="arch" type="xml">
                <search string="Products Price Search">
                    <field name="name" string="Products Price"/>
                    <field name="currency_id" groups="base.group_multi_currency"/>
                    <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
                </search>
            </field>
        </record>


        <record id="product_pricelist_view_tree" model="ir.ui.view">
            <field name="name">product.pricelist.tree</field>
            <field name="model">product.pricelist</field>
            <field name="arch" type="xml">
                <tree string="Products Price List" sample="1">
                    <field name="sequence" widget="handle" />
                    <field name="name"/>
                    <field name="discount_policy" groups="product.group_discount_per_so_line"/>
                    <field name="currency_id" groups="base.group_multi_currency"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <record id="product_pricelist_view_kanban" model="ir.ui.view">
            <field name="name">product.pricelist.kanban</field>
            <field name="model">product.pricelist</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1">
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div id="product_pricelist" class="o_kanban_record_top mb0">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title"><span><field name="name"/></span></strong>
                                    </div>
                                    <strong><i class="fa fa-money" role="img" aria-label="Currency" title="Currency"></i> <field name="currency_id"/></strong>
                                </div>
                                <field name="discount_policy" groups="product.group_discount_per_so_line"/>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="product_pricelist_view" model="ir.ui.view">
            <field name="name">product.pricelist.form</field>
            <field name="model">product.pricelist</field>
            <field name="arch" type="xml">
                <form string="Products Price List">
                    <sheet>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <div class="oe_title">
                            <h1><field name="name" placeholder="e.g. USD Retailers"/></h1>
                        </div>
                        <group>
                          <group name="pricelist_settings">
                              <field name="currency_id" groups="base.group_multi_currency"/>
                              <field name="active" invisible="1"/>
                              <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                          </group>
                        </group>
                        <notebook>
                            <page name="pricelist_rules" string="Price Rules">
                              <field name="item_ids" nolabel="1" context="{'default_base':'list_price'}">
                                  <tree string="Pricelist Rules" editable="bottom">
                                      <field name="product_tmpl_id" string="Products" required="1"/>
                                      <field name="product_id" string="Variants"
                                        groups="product.group_product_variant"
                                        domain="[('product_tmpl_id', '=', product_tmpl_id)]"
                                        options="{'no_create':1}"/>
                                      <field name="min_quantity"/>
                                      <field name="fixed_price" string="Price"/>
                                      <field name="currency_id" invisible="1"/>
                                      <field name="pricelist_id" invisible="1"/>
                                      <!-- Pricelist ID is here only for related fields to be correctly computed -->
                                      <field name="date_start"/>
                                      <field name="date_end"/>
                                      <field name="base" invisible="1"/>
                                      <field name="applied_on" invisible="1"/>
                                      <field name="company_id" invisible="1"/>
                                  </tree>
                              </field>
                            </page>
                            <page name="pricelist_config" string="Configuration">
                                <group>
                                    <group name="pricelist_availability" string="Availability">
                                        <field name="country_group_ids" widget="many2many_tags"/>
                                    </group>
                                    <group name="pricelist_discounts" groups="product.group_discount_per_so_line" string="Discounts">
                                        <field name="discount_policy" widget="radio"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="product_pricelist_view_inherit" model="ir.ui.view">
            <field name="name">product.pricelist.form.inherit</field>
            <field name="model">product.pricelist</field>
            <field name="inherit_id" ref="product.product_pricelist_view"/>
            <field name="groups_id" eval="[(4, ref('product.group_sale_pricelist'))]"/>
            <field name="arch" type="xml">
                <!-- When in advanced pricelist mode : pricelist rules
                  Should open in a form view and not be editable inline anymore.
                -->
                <field name="item_ids" position="replace">
                  <field name="item_ids" nolabel="1" context="{'default_base':'list_price'}" groups="product.group_product_pricelist">
                      <tree string="Pricelist Rules">
                          <field name="name" string="Applicable On"/>
                          <field name="min_quantity"/>
                          <field name="price" string="Price"/>
                          <field name="date_start"/>
                          <field name="date_end"/>
                          <field name="base" invisible="1"/>
                          <field name="price_discount" invisible="1"/>
                          <field name="applied_on" invisible="1"/>
                          <field name="compute_price" invisible="1"/>
                      </tree>
                  </field>
                </field>
            </field>
        </record>

        <record model="ir.ui.view" id="inherits_website_sale_country_group_form">
            <field name="name">website_sale.country_group.form</field>
            <field name="model">res.country.group</field>
            <field name="inherit_id" ref="base.view_country_group_form"/>
            <field name="arch" type="xml">
                <group name="country_group" position="after">
                    <field name="pricelist_ids"/>
                </group>
            </field>
        </record>
        <record id="product_pricelist_action2" model="ir.actions.act_window">
            <field name="name">Pricelists</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">product.pricelist</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="product_pricelist_view_search" />
            <field name="context">{"default_base":'list_price'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new pricelist
              </p><p>
                A price is a set of sales prices or rules to compute the price of sales order lines based on products, product categories, dates and ordered quantities.
                This is the perfect tool to handle several pricings, seasonal discounts, etc.
              </p><p>
                You can assign pricelists to your customers or select one when creating a new sales quotation.
              </p>
            </field>
        </record>

        <record id="product_pricelist_item_action" model="ir.actions.act_window">
            <field name="name">Price Rules</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">product.pricelist.item</field>
            <field name="view_mode">tree,form</field>
        </record>
    </data>
</odoo>
