# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_picking_batch
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>FROM</strong>"
msgstr "<strong>DESDE</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>Número de lote/de serie</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Package</strong>"
msgstr "<strong>Paquete</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>Código de barras del producto</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Responsible:</strong>"
msgstr "<strong>Responsable</strong> "

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>TO</strong>"
msgstr "<strong>A</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>→</strong>"
msgstr "<strong>→</strong>"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de actividad de excepción"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actividad"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking.py:0
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "Add Operations"
msgstr "Agregar operaciones"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add pickings to"
msgstr "Agregar recolecciones a"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add to"
msgstr "Agregar"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_move_line.py:0
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree_detailed_wave
#, python-format
msgid "Add to Wave"
msgstr "Agregar a recolección por olas"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_to_batch_action_stock_picking
msgid "Add to batch"
msgstr "Agregar a lote"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_add_to_wave_action_stock_picking
msgid "Add to wave"
msgstr "Agregar a recolección por olas"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Allocation"
msgstr "Asignación"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__allowed_picking_ids
msgid "Allowed Picking"
msgstr "Recolección permitida"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Confirmación de orden parcial"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Barcode"
msgstr "Código de barras"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#: model:ir.actions.report,name:stock_picking_batch.action_report_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__batch_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
#, python-format
msgid "Batch Transfer"
msgstr "Traslado por lote"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_to_batch
msgid "Batch Transfer Lines"
msgstr "Líneas de traslado por lote"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_batch_action
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_batch_menu
msgid "Batch Transfers"
msgstr "Traslados por lote"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Batch Transfers not finished"
msgstr "Traslados por lote sin finalizar"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking__batch_id
msgid "Batch associated to this transfer"
msgstr "Lote asociado a este traslado"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Batches"
msgstr "Lotes"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Cancel"
msgstr "Cancelar"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "Cannot create wave transfers"
msgstr "No se pueden crear traslados con recolección por olas"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Check Availability"
msgstr "Comprobar disponibilidad"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Choose Labels Layout"
msgstr "Elegir diseño de etiquetas"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__company_id
msgid "Company"
msgstr "Empresa"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Confirm"
msgstr "Confirmar"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_batch
msgid "Count Picking Batch"
msgstr "Número de recolecciones por lote"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_wave
msgid "Count Picking Wave"
msgstr "Número de recolecciones por olas"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid "Create a new batch transfer"
msgstr "Crear un nuevo traslado por lote"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
msgid "Create a new wave transfer"
msgstr "Crear un nuevo traslado con recolección por olas"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_date
msgid "Created on"
msgstr "Creado el"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Detailed Operations"
msgstr "Operaciones detalladas"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__done
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Done"
msgstr "Hecho"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__draft
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Draft"
msgstr "Borrador"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (partners)"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__id
msgid "ID"
msgstr "ID"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "In Progress"
msgstr "En progreso"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__in_progress
msgid "In progress"
msgstr "En progreso"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave____last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch____last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Late Activities"
msgstr "Actividades atrasadas"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__line_ids
msgid "Line"
msgstr "Línea"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__picking_ids
msgid "List of transfers associated to this batch"
msgstr "Lista de traslados asociados a este lote"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__mode
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__mode
msgid "Mode"
msgstr "Modo"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree
msgid "Move Lines"
msgstr "Líneas de movimiento"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__name
msgid "Name of the batch transfer"
msgstr "Nombre del traslado por lote"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes sin leer"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_id
msgid "Operation Type"
msgstr "Tipo de operación"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Operations"
msgstr "Operaciones"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__user_id
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_to_batch__user_id
msgid "Person responsible for this batch transfer"
msgstr "Persona responsable de este traslado por lote"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_add_to_wave__user_id
msgid "Person responsible for this wave transfer"
msgstr "Persona responsable de este traslado con recolección por olas"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__picking_ids
msgid "Picking"
msgstr "Recolección"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipo de recolección"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"Please add 'Done' quantities to the batch picking to create a new pack."
msgstr ""
"Por favor, agregue cantidades \"hecho\" a la recolección por lote para crear"
" un paquete nuevo."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print"
msgstr "Imprimir"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print Labels"
msgstr "Imprimir etiquetas"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Product"
msgstr "Producto"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimientos de producto (línea de movimiento de existencias)"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Put in Pack"
msgstr "Incluir en el paquete"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Quantity"
msgstr "Cantidad"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__user_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Responsible"
msgstr "Responsable"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega del SMS"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__scheduled_date
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Scheduled Date"
msgstr "Fecha programada"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__scheduled_date
msgid ""
"Scheduled date for the transfers to be processed.\n"
"              - If manually set then scheduled date for all transfers in batch will automatically update to this date.\n"
"              - If not manually changed and transfers are added/removed/updated then this will be their earliest scheduled date\n"
"                but this scheduled date will not be set for all transfers in batch."
msgstr ""
"Fecha programada en la que se procesarán los traslados.\n"
"              - Si se configura manualmente, la fecha programada para todos los traslados en lote se actualizará automáticamente a esta fecha.\n"
"              - Si no se configura manualmente, y se agregan, remueven o actualizan los traslados, entonces esta será la fecha programada más temprana\n"
"                pero esta fecha programada no se establecerá en todos los traslados por lote."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Search Batch Transfer"
msgstr "Buscar traslado por lote"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Set quantities"
msgstr "Establecer cantidades"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_allocation
msgid "Show Allocation"
msgstr "Mostrar asignación"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_check_availability
msgid "Show Check Availability"
msgstr "Mostrar verificar disponibilidad"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_validate
msgid "Show Validate"
msgstr "Mostrar validar"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"Some transfers are still waiting for goods. Please check or force their "
"availability before setting this batch to done."
msgstr ""
"Algunos traslados todavía están esperando productos. Verifique o fuerce su "
"disponibilidad antes de configurar esta agrupación como realizada."

#. module: stock_picking_batch
#: model:mail.message.subtype,description:stock_picking_batch.mt_batch_state
#: model:mail.message.subtype,name:stock_picking_batch.mt_batch_state
msgid "Stage Changed"
msgstr "Se cambió la etapa"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__state
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "State"
msgstr "Estado"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Status"
msgstr "Estado"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya pasó\n"
"Hoy: la fecha límite es hoy\n"
"Planeada: futuras actividades."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_tree
msgid "Stock Batch Transfer"
msgstr "Traslado por lote de existencias"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "Destino del paquete de existencias"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_line_ids
msgid "Stock move lines"
msgstr "Líneas de movimiento de existencias"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_ids
msgid "Stock moves"
msgstr "Movimientos de existencias"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Summary:"
msgstr "Resumen:"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""
"Campo técnico que se utiliza para decidir si se debe mostrar el botón "
"\"Asignación\"."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__show_check_availability
msgid ""
"Technical field used to compute whether the check availability button should"
" be shown."
msgstr ""
"Campo técnico que se utiliza para calcular si se debe mostrar el botón de "
"comprobar disponibilidad."

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__show_validate
msgid ""
"Technical field used to decide whether the validate button should be shown."
msgstr ""
"Campo técnico que se utiliza para decidir si se debe mostrar el botón de "
"validar."

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"The following transfers cannot be added to batch transfer %s. Please check their states and operation types, if they aren't immediate transfers.\n"
"\n"
"Incompatibilities: %s"
msgstr ""
"Los siguientes traslados no se pueden agregar al traslado por lote %s. Verifique sus estados y tipos de operación si no son traslados inmediatos.\n"
"\n"
"Incompatibles: %s"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid ""
"The goal of the batch transfer is to group operations that may\n"
"            (needs to) be done together in order to increase their efficiency.\n"
"            It may also be useful to assign jobs (one person = one batch) or\n"
"            help the timing management of operations (tasks to be done at 1pm)."
msgstr ""
"El objetivo del traslado por lote es agrupar operaciones que pueden\n"
"            (deben) hacerse juntas con el fin de incrementar su eficiencia.\n"
"            También puede ser útil para asignar trabajos (una persona = un lote) o\n"
"            ayudar a la gestión del tiempo de las operaciones (por ejemplo, tareas que se deben realizar a la 1pm)."

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
msgid ""
"The goal of the wave transfer is to group operations from differents transfer\n"
"                    together in order to increase their efficiency.\n"
"                    It may also be useful to assign jobs (one person = one batch) or\n"
"                    help the timing management of operations (tasks to be done at 1pm)."
msgstr ""
"El objetivo del traslado por olas es agrupar operaciones de diferentes traslados\n"
"                    con el fin de incrementar su eficacia.\n"
"                    También puede ser útil para asignar trabajos (una persona = un lote) o\n"
"                    ayudar a la gestión de tiempo de las operaciones (tareas que deben realizarse a la 1pm)."

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected operations should belong to a unique company."
msgstr "Las operaciones seleccionadas deben pertenecer a una empresa única."

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_picking_to_batch.py:0
#, python-format
msgid "The selected pickings should belong to an unique company."
msgstr "Las recolecciones seleccionadas deben pertenecer a una empresa única."

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected transfers should belong to a unique company."
msgstr "Los traslados seleccionados deben pertenecer a una empresa única."

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected transfers should belong to the same operation type"
msgstr ""
"Los traslados seleccionados deben pertenecer al mismo tipo de operación"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__is_wave
msgid "This batch is a wave"
msgstr "Este lote es por olas"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Transfer"
msgstr "Traslado"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Transferred by"
msgstr "Trasladado por"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_ids
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Transfers"
msgstr "Traslados"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_code
msgid "Type of Operation"
msgstr "Tipo de operación"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Número de mensajes sin leer"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Validate"
msgstr "Validar"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__wave_id
msgid "Wave Transfer"
msgstr "Traslado con recolección por olas"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_add_to_wave
msgid "Wave Transfer Lines"
msgstr "Líneas de traslados con recolección por olas"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.action_picking_tree_wave
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_wave_menu
msgid "Wave Transfers"
msgstr "Traslados con recolección por olas"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Waves"
msgstr "Recolección por olas"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "You cannot delete Done batch transfers."
msgstr "No puede eliminar traslados por lote terminados."

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "You have to set some pickings to batch."
msgstr "Tiene que establecer algunas recolecciones a los lotes."

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__new
msgid "a new batch transfer"
msgstr "un nuevo traslado por lotes"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__new
msgid "a new wave transfer"
msgstr "un nuevo traslado con recolección por olas"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__existing
msgid "an existing batch transfer"
msgstr "un traslado por lotes existente"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__existing
msgid "an existing wave transfer"
msgstr "un traslado con recolección por olas existente "
