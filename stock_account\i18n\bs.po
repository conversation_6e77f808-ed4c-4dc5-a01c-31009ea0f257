# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_account
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>le <<EMAIL>>, 2018
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: stock_account
#: code:addons/stock_account/models/product.py:146
#: code:addons/stock_account/models/product.py:152
#, python-format
msgid "%s changed cost from %s to %s - %s"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__account_move_ids
msgid "Account Move"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "Svojstva računovodstvene zalihe"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_inventory__accounting_date
msgid "Accounting Date"
msgstr "Datum knjiženja"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "Knjiženja"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "Računovodstvene informacije"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings__module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Archived"
msgstr "Arhivirano"

#. module: stock_account
#: selection:product.category,property_valuation:0
msgid "Automated"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Available Products"
msgstr "Dostupni proizvodi"

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Average Cost (AVCO)"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history
msgid "Cancel"
msgstr "Otkaži"

#. module: stock_account
#: code:addons/stock_account/models/stock.py:510
#, python-format
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock.py:512
#, python-format
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Category"
msgstr "Kategorija"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Change Price"
msgstr "Promjeni cijenu"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.action_view_change_standard_price
#: model:ir.model,name:stock_account.model_stock_change_standard_price
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Change Standard Price"
msgstr "Promjeni standardnu cijenu"

#. module: stock_account
#: code:addons/stock_account/models/product.py:405
#, python-format
msgid ""
"Changing your cost method is an important change that will impact your "
"inventory valuation. Are you sure you want to make that change?"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history
msgid "Choose your date"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock.py:587
#, python-format
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Cost"
msgstr "Cijena (Koštanje)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__cost_method
msgid "Cost Method"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Costing"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__property_cost_method
msgid "Costing Method"
msgstr "Metoda koštanja"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price__counterpart_account_id
msgid "Counter-Part Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price__counterpart_account_id_required
msgid "Counter-Part Account Required"
msgstr ""

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.product_valuation_action
msgid "Create a new product valuation"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_inventory__accounting_date
msgid ""
"Date at which the accounting entries will be created in case of automated "
"inventory valuation. If empty, the inventoy date will be used."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Exhausted Stock"
msgstr "Iscrpljena zaliha"

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "First In First Out (FIFO)"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price__id
msgid "ID"
msgstr "ID"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_change_standard_price__new_price
msgid ""
"If cost price is increased, stock variation account will be debited and stock output account will be credited with the value = (difference of amount * quantity available).\n"
"If cost price is decreased, stock variation account will be creadited and stock input account will be debited."
msgstr ""
"Ako je cijena koštanja povećana, konto vrijednosti zalihe će dugovati i konto izlaza zalihe će potraživati za vrijednost = (razlika u vrijednosti * dostupna zaliha)\n"
"Ako je cijena koštanja smanjena, konto vrijenosti zalihe će potraživati i konto izlaza zalihe će dugovati."

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.product_valuation_action
msgid "If there are products, you will see its name and valuation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Include landed costs in product cost"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_inventory
msgid "Inventory"
msgstr "Skladište"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "Lokacije zalihe"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_quantity_history.py:25
#: model:ir.actions.act_window,name:stock_account.action_stock_inventory_valuation
#: model:ir.actions.act_window,name:stock_account.product_valuation_action
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__property_valuation
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
#, python-format
msgid "Inventory Valuation"
msgstr "Vrednovanje inventure"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_invoice_line
msgid "Invoice Line"
msgstr "Stavka fakture"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
msgid "Journal Entries"
msgstr "Dnevnički zapisi"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "Troškovi nabavke"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: stock_account
#: selection:product.category,property_valuation:0
msgid "Manual"
msgstr "Ručno"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__property_valuation
#: model:ir.model.fields,help:stock_account.field_product_template__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Negative Stock"
msgstr "Negativna zaliha"

#. module: stock_account
#: code:addons/stock_account/models/product.py:128
#, python-format
msgid "No difference between the standard price and the new price."
msgstr ""

#. module: stock_account
#: selection:product.template,property_valuation:0
msgid "Periodic (manual)"
msgstr ""

#. module: stock_account
#: selection:product.template,property_valuation:0
msgid "Perpetual (automated)"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price__new_price
msgid "Price"
msgstr "Cijena"

#. module: stock_account
#: model:ir.model,name:stock_account.model_procurement_group
msgid "Procurement Group"
msgstr "Grupa naručivanja"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
msgid "Product"
msgstr "Proizvod"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__qty_at_date
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_tree_valuation_at_date
msgid "Quantity"
msgstr "Količina"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_tree_valuation_at_date
msgid "Reference"
msgstr "Referenca"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__remaining_qty
msgid "Remaining Qty"
msgstr "Preostala kol."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__remaining_value
msgid "Remaining Value"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history
msgid "Retrieve the inventory valuation"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking
msgid "Return Picking"
msgstr "Prikupljanje proizvoda povrata"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr ""

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Standard Price"
msgstr "Standardna cijena"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_template__property_cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_fifo_manual_move_ids
msgid "Stock Fifo Manual Move"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_fifo_real_time_aml_ids
msgid "Stock Fifo Real Time Aml"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:stock_account.field_product_product__property_stock_account_input
#: model:ir.model.fields,field_description:stock_account.field_product_template__property_stock_account_input
msgid "Stock Input Account"
msgstr "Konto zaliha (primka)"

#. module: stock_account
#: code:addons/stock_account/__init__.py:26
#: code:addons/stock_account/__init__.py:31
#: code:addons/stock_account/models/account_chart_template.py:15
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_journal
#, python-format
msgid "Stock Journal"
msgstr "Dnevnik zaliha"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_move_id
msgid "Stock Move"
msgstr "Kretanje zalihe"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:stock_account.field_product_product__property_stock_account_output
#: model:ir.model.fields,field_description:stock_account.field_product_template__property_stock_account_output
msgid "Stock Output Account"
msgstr "Izlazni nalog zaliha"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_tree_valuation_at_date
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_product_tree2
#: model_terms:ir.ui.view,arch_db:stock_account.view_template_property_form
msgid "Stock Valuation"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "Konto vrednovanja zaliha"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "Konto vrednovanja zalihe (Dolazni)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "Konto vrednovanja zalihe (Odlazni)"

#. module: stock_account
#: code:addons/stock_account/models/stock.py:536
#, python-format
msgid ""
"The cost of %s is currently equal to 0. Change the cost or the configuration"
" of your product to avoid an incorrect valuation."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock.py:358
#, python-format
msgid ""
"The move lines are not in a consistent state: some are entering and other "
"are leaving the company."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock.py:369
#, python-format
msgid ""
"The move lines are not in a consistent states: they are doing an "
"intercompany in a single step while they should go through the intercompany "
"transit location."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock.py:367
#, python-format
msgid ""
"The move lines are not in a consistent states: they do not share the same "
"origin or destination company."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line__to_refund
msgid "To Refund (update SO/PO)"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line__to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_tree_valuation_at_date
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_variant_easy_edit_view_inherit
#: model_terms:ir.ui.view,arch_db:stock_account.view_template_property_form
msgid "Update Cost"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Koristi se za vrednovanje inventure u stvarnom vremenu. Kada je postavljeno "
"na virtualnu lokaciju (ne internog tipa), ovaj konto će se koristiti da nosi"
" vrijednost proizvoda koji se kreće iz interne lokacije u ovu lokaciju, "
"umjesto generisanja konta izlaza zalihe postavljenog na proizvodu. Ovo ne "
"utiče na interne lokacije."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Koristi se za vrednovanje inventure u stvarnom vremenu. Kada je postavljeno "
"na virtuelnu lokaciju (ne internog tipa), ovaj konto će se koristiti za "
"vrijednost proizvoda prenesenih van ove lokacije u neku internu lokaciju, "
"umjesto opšteg konta izlaza zalihe postavljenog na proizvodu. Ovo nema "
"efekta za interne lokacije."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_product_tree2
msgid "Valuation"
msgstr "Procijena vrijednosti"

#. module: stock_account
#: code:addons/stock_account/models/product.py:246
#, python-format
msgid "Valuation at date"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_value
#: model:ir.model.fields,field_description:stock_account.field_stock_move__value
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_account_aml
msgid "Value"
msgstr "Vrijednost"

#. module: stock_account
#: code:addons/stock_account/models/product.py:404
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_input_categ_id
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"incoming stock moves will be posted in this account, unless there is a "
"specific valuation account set on the source location. This is the default "
"value for all products in this category. It can also directly be set on each"
" product"
msgstr ""
"Kada se radi inventura u stvanom vremenu, protukonto stavki dnevnika za sva "
"ulazna kretanja zalihe će biti knjižena na ovaj konto, osim ako ne postoji "
"specifični konto vrednovanja inventure postavljen na izvornoj lokaciji. Ovo "
"je zadana vrijednost za sve proizvode u ovoj kategoriji. Može također da "
"bude postavljen na svakom proizvodu"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__property_stock_account_input
#: model:ir.model.fields,help:stock_account.field_product_template__property_stock_account_input
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"incoming stock moves will be posted in this account, unless there is a "
"specific valuation account set on the source location. When not set on the "
"product, the one from the product category is used."
msgstr ""
"Kada se radi vrednovanje inventure u stvarnom vremenu, protustavke stavkama "
"dnevnika za sve dolazeće poruke će biti knjižena na ovaj konto, osim u "
"slučaju da je specifični konto vrednovanja postavljen na izvornu lokaciju. "
"Kada nije postavljen na proizvodu, onaj sa kategorije proizvoda se koristi."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_output_categ_id
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"outgoing stock moves will be posted in this account, unless there is a "
"specific valuation account set on the destination location. This is the "
"default value for all products in this category. It can also directly be set"
" on each product"
msgstr ""
"Kada se radi vrednovanje inventure u realnom vremenu, protustavke stavki "
"dnevnika za sva izlazna kretanja će se knjižiti na ovaj konto, osim ako ne "
"postoji postavljen konto vrednovanja na lokaciji odredišta. Ovo je zadata "
"vrijednost svih proizvoda u ovoj kategoriji. Može također da bude postavljen"
" direktno na svaki proizvod."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__property_stock_account_output
#: model:ir.model.fields,help:stock_account.field_product_template__property_stock_account_output
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"outgoing stock moves will be posted in this account, unless there is a "
"specific valuation account set on the destination location. When not set on "
"the product, the one from the product category is used."
msgstr ""
"Kada se radi vredovanje inventure u stvarnom vremenu, protukonto stavkama "
"dnevnika knjiženja za kretanja zaliha će biti knjižena na ovaj konto, osim "
"ako ne specifirate konto vrednovanja postavljenog na odredišnoj lokaciji. "
"Kada nije postavljeno na proizvodu, onaj sa kategorije proizvoda se koristi."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_journal
msgid ""
"When doing real-time inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""
"Kada se radi inventura u stvarnom vremenu, ovo je računovodstveni dnevnik u "
"kojem će se zapisi automatski knjižiti kada se obrade kretanja zalihe."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_valuation_account_id
msgid ""
"When real-time inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""
"Kada je stvarna procjena inventure omogućena na proizvodu, ovaj konto će "
"nositi trenutnu vrijednost proizvoda."

#. module: stock_account
#: code:addons/stock_account/models/stock.py:508
#, python-format
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/product.py:130
#: code:addons/stock_account/models/stock.py:514
#, python-format
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "_Apply"
msgstr "_Primjeni"
