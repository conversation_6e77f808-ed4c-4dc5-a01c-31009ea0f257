# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_authorize
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-19 08:16+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Panama) (http://www.transifex.com/odoo/odoo-9/"
"language/es_PA/)\n"
"Language: es_PA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_authorize
#: model_terms:payment.acquirer,cancel_msg:payment_authorize.payment_acquirer_authorize
msgid "<span><i>Cancel,</i> Your payment has been cancelled.</span>"
msgstr ""

#. module: payment_authorize
#: model_terms:payment.acquirer,done_msg:payment_authorize.payment_acquirer_authorize
msgid ""
"<span><i>Done,</i> Your online payment has been successfully processed. "
"Thank you for your order.</span>"
msgstr ""

#. module: payment_authorize
#: model_terms:payment.acquirer,error_msg:payment_authorize.payment_acquirer_authorize
msgid ""
"<span><i>Error,</i> Please be aware that an error occurred during the "
"transaction. The order has been confirmed but won't be paid. Don't hesitate "
"to contact us if you have any questions on the status of your order.</span>"
msgstr ""

#. module: payment_authorize
#: model_terms:payment.acquirer,pending_msg:payment_authorize.payment_acquirer_authorize
msgid ""
"<span><i>Pending,</i> Your online payment has been successfully processed. "
"But your order is not validated yet.</span>"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer_authorize_login
msgid "API Login Id"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer_authorize_transaction_key
msgid "API Transaction Key"
msgstr ""

#. module: payment_authorize
#: model:payment.acquirer,name:payment_authorize.payment_acquirer_authorize
msgid "Authorize.Net"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.acquirer_form_authorize
msgid ""
"How to configure your Authorize.Net account (look for Getting Started "
"Guide) ?"
msgstr ""

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Método de pago"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_authorize
#: model_terms:payment.acquirer,pre_msg:payment_authorize.payment_acquirer_authorize
msgid ""
"You will be redirected to the Authorize website after clicking on the "
"payment button."
msgstr ""
