<?xml version="1.0"?>
<odoo>
    <record id="hr_leave_form_view_x1" model="ir.ui.view">
        <field name="name">hr.leave.inherit.view.form</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='col_right']" position="after">
                <group>
                    <field name="could_be_emergency" invisible="1"/>
                    <field name="is_emergency"
                           attrs="{'invisible': [('could_be_emergency', '!=', True)],'readonly': [('state', 'not in', ('draft', 'confirm'))]}"/>
                    <field name="emergency_type"
                           attrs="{'required':[('is_emergency','=',True)], 'invisible': [('is_emergency', '=', False)],'readonly': [('state', 'not in', ('draft', 'confirm'))]}"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="hr_leave_view_form_manager_x1" model="ir.ui.view">
        <field name="name">hr.leave.inherit.view.form.manager</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form_manager"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='department_id']" position="after">
                <field name="has_monetry_amount"/>
                <field name="leave_monetry_amount" attrs="{'invisible': [('has_monetry_amount', '!=', True)]}"/>
                <field name="is_been_taken" attrs="{'invisible': [('has_monetry_amount', '!=', True)]}"/>
            </xpath>
        </field>
    </record>


    <!--    HR Leave Type Configuration-->

    <record id="edit_holiday_status_form_x1" model="ir.ui.view">
        <field name="name">hr.leave.type.inherit.view.form</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.edit_holiday_status_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='code']" position="after">
                <field name="has_monetry_amount"/>
                <field name="salary_rule_id"
                       attrs="{'invisible': [('has_monetry_amount', '!=', True)],'required': [('has_monetry_amount', '=', True)]}"/>
                <field name="leave_monetry_amount" attrs="{'invisible': [('has_monetry_amount', '!=', True)]}"/>
            </xpath>
            <xpath expr="//field[@name='code']" position="attributes">
                <attribute name="attrs">{'required': [('has_monetry_amount', '=', True)]}
                </attribute>
            </xpath>
            <xpath expr="//field[@name='leave_validation_type']" position="after">
                <field name="has_constrains"/>
                <field name="has_emergency"/>
            </xpath>
            <xpath expr="//sheet/group" position="after">
                <notebook>
                    <page string="ضوابط" attrs="{'invisible': [('has_constrains', '!=', True)]}">
                        <field name="constrain_ids">
                            <tree editable="bottom">
                                <field name="duration"/>
                                <field name="interval"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </xpath>
        </field>
    </record>

    <!--    HR Leave Allocation-->
    <record id="hr_leave_allocation_view_form_x1" model="ir.ui.view">
        <field name="name">hr.leave.allocation.inherit.view.form</field>
        <field name="model">hr.leave.allocation</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_allocation_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="get_allocation_topaid_view"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-random"
                        groups="hr_employees_masarat.group_hr_leave_topaid"
                        string="تحويل الى قيمة مالية">
                </button>
            </xpath>
            <xpath expr="//group[@name='alloc_right_col']" position="after">
                <group>
                    <field name="has_monetry_amount"/>
                    <field name="leave_monetry_amount" attrs="{'invisible': [('has_monetry_amount', '!=', True)]}"/>
                    <field name="allocated_amount_paid" attrs="{'invisible': [('monetry_state', '!=', 'paid')]}"/>
                    <field name="is_allocated_amount_paid" invisible="1"/>
                </group>
            </xpath>
            <xpath expr="//field[@name='number_of_days_display']" position="before">
                <field name="leave_remaining"/>
            </xpath>
            <xpath expr="//field[@name='state']" position="before">
                <field name="monetry_state" widget="statusbar" statusbar_visible="paid"/>
            </xpath>
        </field>
    </record>

    <!--    HR Leave Allocation-->
    <record id="hr_leave_allocation_view_tree_x1" model="ir.ui.view">
        <field name="name">hr.leave.allocation.inherit.view.tree</field>
        <field name="model">hr.leave.allocation</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_allocation_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='duration_display']" position="before">
                <field name="leave_remaining_display"/>
            </xpath>
            <xpath expr="//field[@name='state']" position="before">
                <field name="monetry_state" widget="badge" decoration-info="monetry_state == 'not_paid'"
                       decoration-success="monetry_state == 'paid'"/>
            </xpath>
        </field>
    </record>

</odoo>

