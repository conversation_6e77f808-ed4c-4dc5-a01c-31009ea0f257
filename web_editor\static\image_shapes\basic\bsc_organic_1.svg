<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M.9765.8495c-.1199.2462-.7298.1759-.9132-.1055c-.1833-.285.06-.7774.3632-.7422c.3068.0352.6699.6015.55.8477z">
            <animate dur="12s" repeatCount="indefinite" attributeName="d" attributeType="XML"
            values="
            M.9765.8495c-.1199.2462-.7298.1759-.9132-.1055c-.1833-.285.06-.7774.3632-.7422c.3068.0352.6699.6015.55.8477z;
            M.9525.7766c-.1661.2762-.7774.3023-.9187.0448c-.1413-.2612.1873-.8061.4947-.8211c.3039-.0149.5865.5038.424.7763z;
            M.9837.8871c-.1025.2264-.7069.088-.9048-.2013c-.1979-.2893.0036-.7463.304-.6792c.3004.0671.7033.6541.6008.8805z;
            M.9765.8495c-.1199.2462-.7298.1759-.9132-.1055c-.1833-.285.06-.7774.3632-.7422c.3068.0352.6699.6015.55.8477z"
            calcMode="spline"
            keySplines=".56 .37 .43 .58;.56 .37 .43 .58;.56 .37 .43 .58"/>
        </path>    
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
