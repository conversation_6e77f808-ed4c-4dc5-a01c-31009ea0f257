# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth_exhibitor
# 
# Translators:
# <PERSON>Kodoo <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_booked_template
msgid "<b>Sponsor</b>:"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                        <span class=\"mandatory_mark\"> *</span>"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                        <span class=\"mandatory_mark\"> *</span>"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "<strong>Sponsor Details</strong>"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,help:website_event_booth_exhibitor.field_event_booth__sponsor_subtitle
msgid "Catchy marketing sentence for promote"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Contact me through a different email/phone."
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__use_sponsor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth_category__use_sponsor
msgid "Create Sponsor"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Description"
msgstr "Popis"

#. module: website_event_booth_exhibitor
#: model:ir.model,name:website_event_booth_exhibitor.model_event_booth
msgid "Event Booth"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model,name:website_event_booth_exhibitor.model_event_booth_category
msgid "Event Booth Category"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_search
msgid "Exhibitor type"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_search
msgid "Group By"
msgstr "Zoskupiť podľa"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,help:website_event_booth_exhibitor.field_event_booth__use_sponsor
#: model:ir.model.fields,help:website_event_booth_exhibitor.field_event_booth_category__use_sponsor
msgid "If set, when booking a booth a sponsor will be created for the user"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Phone"
msgstr "Telefón"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Picture"
msgstr "Obrázok"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Slogan"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_id
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_view_search
msgid "Sponsor"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_website_description
msgid "Sponsor Description"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_email
msgid "Sponsor Email"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_type_id
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth_category__sponsor_type_id
msgid "Sponsor Level"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_image_512
msgid "Sponsor Logo"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_mobile
msgid "Sponsor Mobile"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_name
msgid "Sponsor Name"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_phone
msgid "Sponsor Phone"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_subtitle
msgid "Sponsor Slogan"
msgstr ""

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth_category__exhibitor_type
msgid "Sponsor Type"
msgstr "Typ sponzora"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_search
msgid "Sponsor type"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_form
msgid "Sponsorship"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid ""
"This booth type allows you to have visibility on the event website. Please "
"fill in this form"
msgstr ""
