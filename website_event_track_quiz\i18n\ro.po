# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_quiz
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <fold<PERSON><EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_questions_count
msgid "# Quiz Questions"
msgstr "# Întrebări test"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid ". Try another search."
msgstr ". Încercați o altă căutare."

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.all_visitor_card
msgid "<span class=\"text-muted small font-weight-bold\">Points</span>"
msgstr "<span class=\"text-muted small font-weight-bold\">Puncte</span>"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "<span class=\"text-muted\">Points</span>"
msgstr "<span class=\"text-muted\">Puncte</span>"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_view_form
msgid "Add Quiz"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "All questions must be answered !"
msgstr "Trebuie să răspundeți la toate întrebările !"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
msgid "Allow multiple tries"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__text_value
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__answer_ids
msgid "Answer"
msgstr "Raspuns"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Check answers"
msgstr "Verificați răspunsurile"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Check your answers"
msgstr "Verifică-ți răspunsurile"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track_visitor__quiz_completed
msgid "Completed"
msgstr "Finalizat"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_0
msgid "Concrete Blocks Wall"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Congratulations, you scored a total of"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz_question
msgid "Content Quiz Question"
msgstr "Întrebare privind testul de conținut"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__is_correct
msgid "Correct"
msgstr "Corect"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__correct_answer_id
msgid "Correct Answer"
msgstr "Răspuns Corect"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Correct."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__create_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__create_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__create_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__create_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__create_date
msgid "Created on"
msgstr "Creat în"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__display_name
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__display_name
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_1
msgid "Even if there will be some."
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_0_1
msgid "Even if you have a big trunk, some long products need to be secured."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_event
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Event"
msgstr "Eveniment"

#. module: website_event_track_quiz
#: model:ir.actions.act_window,name:website_event_track_quiz.event_quiz_question_action
msgid "Event Quiz Questions"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.actions.act_window,name:website_event_track_quiz.event_quiz_action
msgid "Event Quizzes"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_track
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__event_track_id
msgid "Event Track"
msgstr "Urmărirea evenimentului"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__comment
msgid "Extra Comment"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_question_action
msgid ""
"From here you will be able to examine all quiz questions you have linked to "
"Tracks."
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_action
msgid ""
"From here you will be able to overview all quizzes you have linked to "
"Tracks."
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_view_form
msgid "Go to Quiz"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Group By"
msgstr "Grupează după"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_0
msgid "Hammer"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_1_0
msgid "Hammer won't be of any help here!"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__id
msgid "ID"
msgstr "ID"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_0_0
msgid ""
"In order to avoid accident, you need to secure any product of this kind "
"during transportation!"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Incorrect."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__is_quiz_completed
msgid "Is Quiz Done"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz____last_update
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer____last_update
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__write_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__write_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__write_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__write_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: website_event_track_quiz
#: model:ir.model.fields,help:website_event_track_quiz.field_event_quiz__repeatable
msgid "Let attendees reset the quiz and try again."
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_1_1
msgid "Lumbers need first to be cut from trees!"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Mobile sub-nav"
msgstr "Sub-nav mobil"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_2
msgid "Mud Wall"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_1
msgid "Music"
msgstr "Muzică"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__name
msgid "Name"
msgstr "Nume"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_0_1
msgid "No"
msgstr "Nu"

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_question_action
msgid "No Quiz Question yet!"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_action
msgid "No Quiz added yet!"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid "No user found for"
msgstr "Nu a fost găsit niciun utilizator pentru"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__awarded_points
msgid "Number of Points"
msgstr "Numărul de puncte"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Oopsie, you did not score any point on this quiz."
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_2
msgid "Open Source Apps"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_2
msgid "OpenWood is not an Open Source congres about Apps."
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Point"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__awarded_points
#, python-format
msgid "Points"
msgstr "Puncte"

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_13_quiz
msgid "Pretty. Ugly. Lovely."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__question_id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__name
msgid "Question"
msgstr "Întrebare"

#. module: website_event_track_quiz
#: code:addons/website_event_track_quiz/models/event_quiz.py:0
#, python-format
msgid ""
"Question \"%s\" must have 1 correct answer and at least 1 incorrect answer "
"to be valid."
msgstr ""

#. module: website_event_track_quiz
#: code:addons/website_event_track_quiz/models/event_quiz.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer to be valid."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz_answer
msgid "Question's Answer"
msgstr "Răspunsul întrebării"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__question_ids
msgid "Questions"
msgstr "Întrebări"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__quiz_id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_id
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_content
msgid "Quiz"
msgstr "Test"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_points
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track_visitor__quiz_points
msgid "Quiz Points"
msgstr "Puncte test"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_form
msgid "Quiz Question"
msgstr "Întrebare test"

#. module: website_event_track_quiz
#: model:ir.ui.menu,name:website_event_track_quiz.event_quiz_question_menu
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_tree
msgid "Quiz Questions"
msgstr "Întrebări test"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "Quiz validation error"
msgstr "Eroare de validare a testului"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_ids
#: model:ir.ui.menu,name:website_event_track_quiz.event_quiz_menu
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_tree
msgid "Quizzes"
msgstr "Teste"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Reset"
msgstr "Resetează"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_2
msgid "Scotch tape"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search"
msgstr "Caută"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search Attendees"
msgstr "Căutați participanți"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search courses"
msgstr "Căutați cursuri"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search users"
msgstr "Căutați utilizatori"

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_5_quiz
msgid "Securing your Lumber during transport"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__sequence
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_1
msgid "Steel Wall"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_1_1
msgid "Stores !"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_content
msgid "Take the Quiz"
msgstr "Luați testul"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "The correct answer was:"
msgstr "Răspunsul corect a fost:"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid "There is currently no leaderboard available"
msgstr "În prezent nu există niciun clasament disponibil"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr "A apărut o eroare la validarea acestui test."

#. module: website_event_track_quiz
#: model:ir.model.fields,help:website_event_track_quiz.field_event_quiz_answer__comment
msgid ""
"This comment will be displayed to the user if he selects this answer, after submitting the quiz.\n"
"                It is used as a small informational text helping to understand why this answer is correct / incorrect."
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr "Acest test a fost deja făcut. Reluarea nu este posibilă."

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_1
msgid "Tie-down straps and other wooden blocks"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Track"
msgstr "Pistă"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_5_question_0
msgid "Transporting lumber from stores to your house is safe."
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_1_0
msgid "Trees !"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__repeatable
msgid "Unlimited Tries"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "User rank"
msgstr "Rangul utilizatorului"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_1_2
msgid "Well, it could work but you will need a lot of tape!"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_1_quiz
msgid "What This Event Is All About"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_5_question_1
msgid "What kind of tool are needed to secure your lumber ?"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_13_question_0
msgid "What kind of wall is transformed here ?"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_1_question_0
msgid "What will we talk about during this event ?"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_1_question_1
msgid "Where does lumber comes from ?"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_0
msgid "Wood"
msgstr "Lemn"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_0_0
msgid "Yes"
msgstr "Da"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.all_visitor_card
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "You"
msgstr "Dumneavoastră"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_0
msgid "You're really smart !"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
msgid "e.g. Test your Knowledge"
msgstr "de exemplu. Testați-vă cunoștințele"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_form
msgid "e.g. What is Joe's favorite motto?"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "point!"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "points!"
msgstr ""
