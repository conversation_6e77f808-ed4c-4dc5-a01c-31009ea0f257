/* Copyright 2021 IT<PERSON>ra - <PERSON>
 * License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl). */

// Website main navbar and his AppsMenu button
#oe_main_menu_navbar {
    #oe_applications .dropdown-toggle::after {
        display: none;
    }
    a.full {
        > i {
            padding: 0 10px 0 0;
            font-size: 17px;
        }
        font-size: 20px;
        line-height: 46px;
        @include media-breakpoint-down(sm) {
            width: 46px;
            overflow: hidden;
        }
    }
    #oe_applications.o_responsive_loaded {
        a.full {
            width: 46px;
            overflow: hidden;
        }
    }
    .o_menu_brand {
        white-space: nowrap;
        padding: 0 16px 0 4px;
        text-transform: capitalize;
        @include media-breakpoint-down(sm) {
            display: none;
        }
    }
}
