<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_order_form_inherit_sale_project" model="ir.ui.view">
        <field name="name">sale.order.form.sale.project</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='preview_sale_order']" position="before">
                <button type="object" name="action_view_project_ids" class="oe_stat_button" icon="fa-puzzle-piece" attrs="{'invisible': ['|', ('state', 'in', ['draft', 'sent']), ('project_ids', '=', [])]}" groups="project.group_project_manager">
                    <field name="project_ids" invisible="1"/>
                    <field name="project_count" widget="statinfo" string="Projects"/>
                </button>
            </xpath>
            <xpath expr="//button[@name='action_view_invoice']" position="before">
                <button type="object" name="action_view_task" class="oe_stat_button" icon="fa-tasks" attrs="{'invisible': [('tasks_count', '=', 0)]}" groups="project.group_project_user">
                    <field name="tasks_count" widget="statinfo" string="Tasks"/>
                </button>
            </xpath>
            <xpath expr="//field[@name='analytic_account_id']" position="after">
                <field name="visible_project" invisible="1"/>
                <field name="project_id" options="{'no_create': True}" attrs="{'invisible': [('visible_project', '=', False)]}"/>
            </xpath>
        </field>
    </record>

</odoo>
