# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* http_routing
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>Nessel<PERSON>ch, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.403
msgid "403: Forbidden"
msgstr "403: Verboten"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid ""
"<b>Don't panic.</b> If you think it's our mistake, please send us a message "
"on"
msgstr ""
"<b>Kein Grund zur Panik.</b> Wenn Sie glauben, dass es sich um einen Fehler "
"unsererseits handelt, senden Sie uns bitte eine Nachricht an"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.error_message
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "<strong>Error message:</strong>"
msgstr "<strong>Fehlermeldung:</strong>"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.500
msgid "Back"
msgstr "Zurück"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "Error"
msgstr "Fehler"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "Error 404"
msgstr "Fehler 404"

#. module: http_routing
#: model:ir.model,name:http_routing.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-Routing"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
#: model_terms:ir.ui.view,arch_db:http_routing.500
msgid "Home"
msgstr "Home"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "Maybe you were looking for one of these <b>popular pages?</b>"
msgstr ""
"Vielleicht haben Sie nach einem dieser <b>meistbesuchten Seiten</b> gesucht?"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.400
msgid "Oops! Something went wrong."
msgstr "Ups! Ein Fehler ist aufgetreten."

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "QWeb"
msgstr "QWeb"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.400
msgid "Take a look at the error message below."
msgstr "Beachten Sie die unten stehende Benachrichtigung."

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "The error occurred while rendering the template"
msgstr "Der Fehler ist beim Rendering der Vorlage aufgetreten"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.403
msgid "The page you were looking for could not be authorized."
msgstr "Die Seite, die Sie hier suchen, konnte nicht authentifiziert werden."

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "Traceback"
msgstr "Traceback"

#. module: http_routing
#: model:ir.model,name:http_routing.model_ir_ui_view
msgid "View"
msgstr "Ansicht"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "We couldn't find the page you're looking for!"
msgstr "Wir konnten die von Ihnen gesuchte Seite nicht finden!"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "and evaluating the following expression:"
msgstr "und beim Evaluieren des folgenden Ausdrucks:"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "this page"
msgstr "diese Seite"
