# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website
# 
# Translators:
# <PERSON><PERSON><PERSON> <neman<PERSON><PERSON><PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jo<PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:724
#, python-format
msgid " Add Images"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:233
#: code:addons/website/models/website.py:302
#, python-format
msgid "%s (id:%s)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "&amp;times;"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.sitemap_index_xml
#: model_terms:ir.ui.view,arch_db:website.sitemap_xml
msgid "&lt;?xml version=\"1.0\" encoding=\"UTF-8\"?&gt;"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.default_xml
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:40
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:51
#, python-format
msgid "&times;"
msgstr "&puta;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
msgid "' did not match any pages."
msgstr ""

#. module: website
#: code:addons/website/models/website.py:723
#: code:addons/website/models/website.py:725
#: code:addons/website/models/website.py:743
#, python-format
msgid "(copy)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:29
#, python-format
msgid "(could be used in"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"(e.g. business name, location) tends to improve your ranking in search "
"engines and consequently increases your traffic. Do it:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ""
",\n"
"                                updated:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", updated:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "- The Odoo Team"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:23
#, python-format
msgid ""
".\n"
"            Changing its name will break these calls."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.default_csv
msgid "1,2,3"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "10s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "12"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "1s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "2s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "3s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
msgid "403: Forbidden"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.404
msgid "404: Page not found!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "5s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "<b>A Small Subtitle</b>"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:15
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:25
#, python-format
msgid ""
"<b>Click on a text</b> to start editing it. <i>It's that easy to edit your "
"content!</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"<b>Great stories are for everyone even when only written for\n"
"                        just one person.</b> If you try to write with a wide general\n"
"                        audience in mind, your story will ring false and be bland.\n"
"                        No one will be interested. Write for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"<b>Great stories have personality.</b> Consider telling\n"
"                        a great story that provides personality. Writing a story\n"
"                        with personality for potential clients will assists with\n"
"                        making a relationship connection. This shows up in small\n"
"                        quirks like word choices or phrases. Write from your point\n"
"                        of view, not from someone else's experience."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:74
#, python-format
msgid "<b>Install a contact form</b> to improve this page."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:79
#, python-format
msgid ""
"<b>Install new apps</b> to get more features. Let's install the <i>'Contact "
"form'</i> app."
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.action_module_theme
msgid "<b>No theme module found!</b>"
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.action_module_website
msgid "<b>No website module found!</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<b>Set Custom Image...</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<br/>\n"
"                                    Make sure the Advanced Options are set to your country and language."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-arrows\"/>Background Image Sizing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-arrows-h\"/>Images spacing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-clock-o\"/>Scroll Speed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-clock-o\"/>Slideshow speed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-diamond\"/> Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-expand\"/>Margin"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-eyedropper\"/>Background Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-eyedropper\"/>Overlay color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-indent\"/> Inner content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-magic icon-fix\"/> Effect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-magic\"/>Mode"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-magnet\"/>Float"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-paint-brush\"/>Styling"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-picture-o\"/>Background Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"                Add a language..."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-plus-circle\"/>Add Slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-plus-circle\"/>Add images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-refresh\"/>Re-order"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-th\"/>Columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-th-large\"/> Structure"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<i class=\"fa fa-th-large\"/> WEBSITE <b class=\"caret\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-trash\"/>Remove all images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-trash-o\"/>Remove Slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>\n"
"                        The whole process may take a few hours, some discussions with your colleagues and\n"
"                    several cups of coffee to go through. But don't worry, you can return to this tool at any time.</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>\n"
"                        Whether you're a beginner or a pro, we have everything you need to plan, create,\n"
"                    publish and grow your site, blog or online store.</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>\n"
"                    This Planner will help you think about your website and provide tips and ideas to inspire you. There are examples and explanations to guide you through the process of creating a top-notch, high quality website that meets all your needs.</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<i>Congratulations on taking the leap and deciding to build your own "
"website!</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<i>We wish you a fun time!</i>"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:54
#, python-format
msgid ""
"<p><b>That's it.</b> Your homepage is live.</p><p>Continue adding more pages"
" to your site or edit this page to make it even more awesome.</p>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_floating
msgid ""
"<small class=\"text-muted\">A great way to catch your reader's attention is "
"to tell a story. Everything you consider writing can be told as a "
"story.</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
msgid "<small>Author of this quote</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
msgid "<small>John Doe, CEO</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span class=\"caret\"/>\n"
"                            <span class=\"sr-only\">Toggle Dropdown</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install now"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                                <strong>Tips for a good domain:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-pencil\"/>Edit"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-plus\"/>New"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"o_add_images\" style=\"cursor: pointer;\"><i class=\"fa fa-"
"plus-circle\"/> Add Images</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-pencil\"/>\n"
"                                        <strong>On your own</strong><br/>\n"
"                                    </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa fa-user\"/>\n"
"                                        <strong>External services</strong><br/>\n"
"                                    </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid ""
"<span class=\"sr-only\">Toggle navigation</span>\n"
"                <span class=\"icon-bar\"/>\n"
"                <span class=\"icon-bar\"/>\n"
"                <span class=\"icon-bar\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span/>\n"
"                                <span class=\"css_publish\">Unpublished</span>\n"
"                                <span class=\"css_unpublish\">Published</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<span>$</span><b style=\"font-size: 60px\">125</b><small>.00</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<span>$</span><b style=\"font-size: 60px\">35</b><small>.00</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<span>$</span><b style=\"font-size: 60px\">65</b><small>.00</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_button
msgid ""
"<span>Contact Us Now</span>\n"
"                        <i class=\"fa fa-chevron-right\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share
msgid "<span>Share</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>... and your location too!</strong><br/>\n"
"                                Millions of people use Google Maps everyday. Whether you are a restaurant\n"
"                                or a huge business, you have no excuse not to register your location in"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>1. Your main menu items</strong> and 2. Your secondary menu items"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>3. Your footer titles</strong> and 4. Your footer links"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_button
msgid "<strong>50,000+ companies run Odoo to grow their businesses.</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Add features and content</strong><br/>\n"
"                                You can put more content in the intermediate section of your homepage (one\n"
"                                or two scrolls down from the visible section), but try to keep your\n"
"                                paragraphs easy to read and avoid walls of text."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Advertise</strong><br/>\n"
"                                If you have a small budget to advertise your website (around $100), you\n"
"                                should start a"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Be announced on the Odoo Twitter account</strong><br/>\n"
"                                We like to hear from people using Odoo to build their website. Tweet us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Be linked by trusted websites</strong><br/>\n"
"                                The more websites that link to your website, the more Google trusts it to be worthwhile. Here are some tips:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Call-to-action</strong><br/>\n"
"                                Add one, and only one, call-to-action to your homepage. Your homepage is\n"
"                                the page that gets the most visitors.<br/>\n"
"                                It's good practice to try to engage with your visitors on the homepage:\n"
"                                Contact Us, Subscribe to Get More Information, Check the Catalog, etc."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Discuss live</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Display social proof</strong><br/>\n"
"                                Below your main banner or call-to-action, it's a good practice to put\n"
"                                social proofs about your products or services; customer quotes, videos,\n"
"                                photos of your products in actions, twitter quotes, etc."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "<strong>Error message:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Find 3 websites that you like</strong> and write down what you like "
"about them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Find a catchy headline</strong><br/>\n"
"                                Make sure anyone who visits your site understands what it’s about without too much reading.<br/>\n"
"                                To do that, the visible section of your homepage (above the fold) needs to be simple and straightforward. For example, use pictures of your work, screenshots of your app, a short and catchy hook."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>How to get a custom domain name?</strong><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Initial Teaser</strong><br/>\n"
"                                Before launching to the masses, it's good practice to tease around 100 of\n"
"                                your contacts. Start with your friends, colleagues and family. Whether or\n"
"                                not they're interested in your business, they will provide you with some precious feedback."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Launch a forum</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Next actions:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Now try to write down you footer structure:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Publish documentation</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Publish events</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Reference your images...</strong><br/>\n"
"                                If you are an artist or a photographer, you can potentially bring in a lot\n"
"                                of visitors by referencing your images so they are displayed in image\n"
"                                search engines, like Google Images.\n"
"                                To do that,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Send mass mails</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>Start a blog</strong><br/>\n"
"                                Search engines loves websites that get new content regularly. An excellent way is publishing blog posts. If you have the time and dedication to write good articles,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>The big launch</strong><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Use Google Webmaster</strong><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Use contact forms</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Use relevant keywords</strong><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"<strong>With that in mind, try to define the structure of your main "
"menu:</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>You own your domain name?</strong><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "<strong>Your website objectives</strong> by priority:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "@odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid "A Great Headline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "A Punchy Headline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_act_server_website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid "A good subtitle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"A great way to catch your reader's attention is to tell a story.\n"
"                        Everything you consider writing can be told as a story."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great<br/>feature, in clear words."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_module_website_version
msgid "A/B Testing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "API Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.aboutus_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.footer_default
#: model_terms:website.page,arch_db:website.aboutus_page
msgid "About us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cdn_activated
msgid "Activate CDN for assets"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_active
#: model:ir.model.fields,field_description:website.field_website_redirect_active
msgid "Active"
msgstr "Aktivan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit you design need.\n"
"                        To duplicate, delete or move columns, select the\n"
"                        column and use the top icons to perform your action."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:53
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:49
#, python-format
msgid "Add Menu Entry"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Add a great slogan"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:49
#, python-format
msgid "Add page in menu"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:55
#, python-format
msgid "Add this feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Adding relevant and popular keywords"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Advertise a keyword if you answer YES to the three following questions:"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:50
#, python-format
msgid "After having checked how it looks on mobile, <b>close the preview</b>."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"After working on this step, you may want to go back and refine your "
"objectives or modify your visitors interests. Those 3 first steps are "
"essentially linked, and the more time your spend on refining them, the "
"better your website will be!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Aim for a .com and/or your country extension"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic person in life who can say she loves what she does.\n"
"                                She mentors 100+ in-house developers and looks after the community of over\n"
"                                thousands developers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid ""
"All these icons are licensed under creative commons so that you can use "
"them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Allows your community to ask and answer questions about their interests"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly or\n"
"                                indirectly, implying any approval, association, sponsorship, endorsement, or\n"
"                                affiliation with any linked website, unless specifically stated herein."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "An error occured while rendering the template"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "And a great subtitle too"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Announce it on social media: Twitter, Facebook, LinkedIn, Youtube, etc."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Apply"
msgstr "Primjeni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Apply it to your website from your database admin page"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_arch_db
msgid "Arch Blob"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_arch_fs
msgid "Arch Filename"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:60
#, python-format
msgid "Are you sure you want to delete this page ?"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment_website_url
msgid "Attachment URL"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Attract new leads"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_auto_redirect_lang
msgid "Autoredirect Language"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_act_server_website_published
msgid "Available on the Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Avoid special characters"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Avoid using a mass survey, it's way more efficient to have\n"
"                                personal discussions with everyone. If you engage with them in the\n"
"                                reviewing process, they will even help you promote your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Back"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Be mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Being clear on why you're creating your site is an essential first step to "
"ensure your content and structure meet business objectives."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Best if you have language skills"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Build awareness and credibility"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Building Profile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Buy it at a provider:"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_cdn_url
#: model:ir.model.fields,field_description:website.field_website_cdn_url
msgid "CDN Base URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_cdn_filters
#: model:ir.model.fields,field_description:website.field_website_cdn_filters
msgid "CDN Filters"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Can I afford for advertising on the keyword?"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:106
#: code:addons/website/static/src/xml/website.xml:27
#: model_terms:ir.ui.view,arch_db:website.500
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "Cancel"
msgstr "Odustani"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Change Background"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Change Icons"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Check its availability"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Check your references before deciding to work with you"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_field_parent
msgid "Child Field"
msgstr "Podređeno polje"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_child_id
msgid "Child Menus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph\n"
"                        about it. It does not have to be long, but it should\n"
"                        reinforce your image."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Circle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "City"
msgstr "Grad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Click here to unfold a Good Footer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Click here to unfold a Good Homepage"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Click on the icon to adapt it to your feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
#: model_terms:ir.ui.view,arch_db:website.website2_homepage
#: model_terms:ir.ui.view,arch_db:website.website2_homepage_page_ir_ui_view
#: model_terms:website.page,arch_db:website.website2_homepage_page
msgid "Click to customize this text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client Secret"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
msgid "Clone this page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:51
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Communicate"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Community &amp; Enterprise users: apply a redirection from your Domain "
"Manager platform"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "Preduzeća"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_company_id
msgid "Company"
msgstr "Preduzeće"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Company name"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "Postavka"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:24
#, python-format
msgid "Confirmation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Congratulations, you're done!"
msgstr "Čestitamo, sve ste završili !"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid "Connect with us"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403 model_terms:ir.ui.view,arch_db:website.404
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Contact Us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.footer_default
#: model_terms:ir.ui.view,arch_db:website.s_banner
#: model_terms:ir.ui.view,arch_db:website.s_big_message
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.website2_contactus
#: model_terms:ir.ui.view,arch_db:website.website2_contactus_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.website2_homepage
#: model_terms:ir.ui.view,arch_db:website.website2_homepage_page_ir_ui_view
#: model:website.menu,name:website.menu_contactus
#: model:website.menu,name:website.website2_menu_contactus
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.website2_contactus_page
#: model_terms:website.page,arch_db:website.website2_homepage_page
msgid "Contact us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.website2_contactus
#: model_terms:ir.ui.view,arch_db:website.website2_contactus_page_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.website2_contactus_page
msgid "Contact us about anything related to our company or services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "Contact us »"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Content"
msgstr "Sadržaj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Content Delivery Network (CDN)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:388
#: code:addons/website/static/src/xml/website.xml:26
#, python-format
msgid "Continue"
msgstr "Nastavi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Copyright &amp;copy;"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:328
#, python-format
msgid "Create Menu"
msgstr "Kreiraj Meni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Create a"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Create a Google Project and Get a Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Create a community"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Create several versions of your website pages"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_create_uid
#: model:ir.model.fields,field_description:website.field_website_menu_create_uid
#: model:ir.model.fields,field_description:website.field_website_page_create_uid
#: model:ir.model.fields,field_description:website.field_website_redirect_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_create_date
#: model:ir.model.fields,field_description:website.field_website_menu_create_date
#: model:ir.model.fields,field_description:website.field_website_page_create_date
#: model:ir.model.fields,field_description:website.field_website_redirect_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cubes"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize Theme"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:32
#, python-format
msgid ""
"Customize any block through this menu. Try to change the background of the "
"banner."
msgstr ""

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_dashboard
msgid "Dashboard"
msgstr "Kontrolna ploča"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default"
msgstr "Podrazumevano"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_default_lang_id
msgid "Default Language"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_default_lang_id
msgid "Default language"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_default_lang_code
#: model:ir.model.fields,field_description:website.field_website_default_lang_code
msgid "Default language code"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:41
#, python-format
msgid "Define Keywords"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Delete Blocks"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:741
#, python-format
msgid "Delete Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture\n"
"                        that illustrates your message. Click on the picture to\n"
"                        change it's <em>rounded corner</em> style."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
msgid "Delete this page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Demo Logo"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:23
#: code:addons/website/static/src/xml/website.pageProperties.xml:32
#, python-format
msgid "Dependencies"
msgstr "Zavisnosti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:20
#, python-format
msgid "Description"
msgstr "Opis"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Design"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disable autoplay"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:45
#: code:addons/website/static/src/js/menu/seo.js:343
#, python-format
msgid "Discard"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Discuss and Comments"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_display_name
#: model:ir.model.fields,field_description:website.field_website_menu_display_name
#: model:ir.model.fields,field_description:website.field_website_page_display_name
#: model:ir.model.fields,field_description:website.field_website_published_mixin_display_name
#: model:ir.model.fields,field_description:website.field_website_redirect_display_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:89
#, python-format
msgid "Do you want to edit the company data ?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Domain"
msgstr "Domen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Domain Name"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:35
#, python-format
msgid "Don't forget to update all links referring to this page."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Don't go over 6 items, otherwise your main menu will be difficult to use."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Don't hesitate in"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Download the technical documentation of a product"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Downtown"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:45
#, python-format
msgid "Drag a menu to the right to create a sub-menu"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:36
#, python-format
msgid "Drag another block in your page, below the cover."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:20
#, python-format
msgid "Drag the <i>Cover</i> block and drop it in your page."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Duplicate"
msgstr "Udvostruči"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Duplicate blocks to add more features."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Easy to remember and spell"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit"
msgstr "Uredi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:417
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit Top Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
msgid "Edit code in backend"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit in backend"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:73
#, python-format
msgid "Edit my Analytics Client ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"Edit the content below this line to adapt the default \"page not found\" "
"page."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:35
#, python-format
msgid "Edit the menu"
msgstr ""

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "End"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Enterprise package"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "Error"
msgstr "Greška"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Examples"
msgstr "Primeri"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:61
#, python-format
msgid "Existing Page, URL or Email"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_well
msgid ""
"Explain the benefits you offer. Don't write about products or\n"
"            services here, write about solutions."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_xml_id
msgid "External ID"
msgstr "Externi ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_social_facebook
msgid "Facebook Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fast"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_favicon
msgid "Favicon"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_panel
msgid "Feature Title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Finally don't try to make all your content accessible from the main menu or the footer.\n"
"                            Indeed, a page can also be accessible through a direct link or button, from any other page of your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Find Inspiration"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fixed"
msgstr "Fiksno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Float"
msgstr "Pokretni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Folded list"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:6
#, python-format
msgid "Follow all the"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Footer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"For example, use pictures to help break up large areas of text by altering\n"
"                                    Text-Image and Image-Text blocks. If you prefer icons, use the Features or Feature Grid blocks."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind Company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing and the Customer Experience strategies."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Free"
msgstr "Slobodan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid ""
"From the main container, you can change the background to highlight "
"features."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "General Terms and Conditions / Service Policies"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Gengo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Get an understanding of your methodology"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:340
#, python-format
msgid ""
"Get this page efficiently referenced in search engines to attract more "
"visitors."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Get your opportunities filled up in our integrated CRM application"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_social_github
msgid "GitHub Account"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:48
#, python-format
msgid "Go To Page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:27
#, python-format
msgid "Go to Link"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:20
#: code:addons/website/static/src/xml/website.backend.xml:22
#, python-format
msgid "Go to Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "GoDaddy"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:46
#, python-format
msgid ""
"Good Job! You created your first page. Let's check how this page looks like "
"on <b>mobile devices</b>."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Google Adwords"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:89
#: model:ir.model.fields,field_description:website.field_res_config_settings_has_google_analytics
#, python-format
msgid "Google Analytics"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_google_analytics_key
#: model:ir.model.fields,field_description:website.field_website_google_analytics_key
msgid "Google Analytics Key"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_has_google_analytics_dashboard
msgid "Google Analytics in Dashboard"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:92
#, python-format
msgid ""
"Google Analytics initialization failed. Maybe this domain is not whitelisted"
" in your Google Analytics project for this client ID."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Google Business"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_google_management_client_id
#: model:ir.model.fields,field_description:website.field_website_google_management_client_id
msgid "Google Client ID"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_google_management_client_secret
#: model:ir.model.fields,field_description:website.field_website_google_management_client_secret
msgid "Google Client Secret"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_has_google_maps
#: model_terms:ir.ui.view,arch_db:website.company_description
msgid "Google Maps"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_google_maps_api_key
msgid "Google Maps API Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Google Webmaster"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_social_googleplus
msgid "Google+ Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Great Value"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.aboutus_page_ir_ui_view
#: model_terms:website.page,arch_db:website.aboutus_page
msgid "Great products for great people"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grid"
msgstr "Mreža"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Group By"
msgstr "Grupiši po"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_groups_id
msgid "Groups"
msgstr "Grupe"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Grow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML/CSS Editor"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP routing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "Headline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Here is a checklist of actions to help you launch a new website efficiently:"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:110
#, python-format
msgid "Hide this page from search results"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
#: model_terms:ir.ui.view,arch_db:website.footer_default
#: model:website.menu,name:website.menu_homepage
#: model:website.menu,name:website.website2_menu_homepage
msgid "Home"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:4
#: model:ir.model.fields,field_description:website.field_website_homepage_id
#: model:ir.model.fields,field_description:website.field_website_page_is_homepage
#: model_terms:ir.ui.view,arch_db:website.403 model_terms:ir.ui.view,arch_db:website.404
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.website_planner
#, python-format
msgid "Homepage"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website2_homepage
#: model_terms:ir.ui.view,arch_db:website.website2_homepage_page_ir_ui_view
#: model_terms:website.page,arch_db:website.website2_homepage_page
msgid "Homepage 0.0.0.0"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:102
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "How to get my Client ID"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:97
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "How to get my Tracking ID"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_id
#: model:ir.model.fields,field_description:website.field_website_menu_id_7239
#: model:ir.model.fields,field_description:website.field_website_page_id
#: model:ir.model.fields,field_description:website.field_website_published_mixin_id
#: model:ir.model.fields,field_description:website.field_website_redirect_id
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_id
msgid "ID"
msgstr "ID"

#. module: website
#: model:ir.model.fields,help:website.field_website_page_xml_id
msgid "ID of the view defined in xml file"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset one or more templates to their <strong>factory "
"settings</strong>."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_page_groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_page_active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Improve your conversion from visitors to customers"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:68
#, python-format
msgid "In description"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:69
#, python-format
msgid "In page's content"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:67
#, python-format
msgid "In title"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:108
#: model_terms:ir.ui.view,arch_db:website.index_management
#, python-format
msgid "Indexed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_inherit_id
msgid "Inherited View"
msgstr "Nasleđeni pregled"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Install Gengo Translator app"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "Instaliraj Jezik"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install new language"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Install new languages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Modules"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the numbers and\n"
"                                improves them. She is determined to drive success and delivers her professional\n"
"                                acumen to bring Company at the next level."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_is_visible
msgid "Is Visible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Is the keyword popular in Google?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Is the person searching for it likely to buy my product or\n"
"                                    service? When starting out, you want to advertise on\n"
"                                    “buying intent” keywords (i.e. when people are clearly looking to buy)."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:19
#, python-format
msgid "It looks like your file is being called by"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:209
#, python-format
msgid ""
"It might be possible to edit the relevant items or fix the issue in <a "
"href=\"%s\">the classic Odoo interface</a>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"It's often much easier not to start with a blank page. Take a look at your competitors\n"
"                        and similar companies around the world. They probably have the same objectives and\n"
"                        visitors that you do. How did they transform that into a website?<br/>\n"
"                        Try to find 3 websites that have remarkable characteristics."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "It’s time to grow your traffic!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_button
msgid "Join us and make your company a better place."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_key
msgid "Key"
msgstr "Ključ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:48
#, python-format
msgid "Keyword"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_language_ids
#: model:ir.model.fields,field_description:website.field_website_language_ids
msgid "Languages"
msgstr "Jezici"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Large"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website___last_update
#: model:ir.model.fields,field_description:website.field_website_menu___last_update
#: model:ir.model.fields,field_description:website.field_website_page___last_update
#: model:ir.model.fields,field_description:website.field_website_published_mixin___last_update
#: model:ir.model.fields,field_description:website.field_website_redirect___last_update
#: model:ir.model.fields,field_description:website.field_website_seo_metadata___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:110
#, python-format
msgid "Last Month"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_write_uid
#: model:ir.model.fields,field_description:website.field_website_page_write_uid
#: model:ir.model.fields,field_description:website.field_website_redirect_write_uid
#: model:ir.model.fields,field_description:website.field_website_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_write_date
#: model:ir.model.fields,field_description:website.field_website_page_write_date
#: model:ir.model.fields,field_description:website.field_website_redirect_write_date
#: model:ir.model.fields,field_description:website.field_website_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:109
#, python-format
msgid "Last Week"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:111
#, python-format
msgid "Last Year"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Launch"
msgstr "Pokreni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Learn what question your customers have, what problems they encounter"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:65
#, python-format
msgid "Legend:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:5
#, python-format
msgid "Let's start designing."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:55
#, python-format
msgid "Link"
msgstr "Veza"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:81
#, python-format
msgid "Link my Analytics Account"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_social_linkedin
msgid "LinkedIn Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other websites"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "List of Features"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:82
#, python-format
msgid "Loading..."
msgstr "Učitavam..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Local Events"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_id
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Main Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
msgid "Manage Your Pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Your Website Pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Manage channels to structure your content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
msgid "Manage this page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Masonry"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403 model_terms:ir.ui.view,arch_db:website.404
msgid "Maybe you were looking for one of these popular pages ?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Meet the Executive Team"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:241
#: model:ir.model.fields,field_description:website.field_website_menu_name
#, python-format
msgid "Menu"
msgstr "Meni"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:338
#, python-format
msgid "Menu Label"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:243
#, python-format
msgid "Menus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Message"
msgstr "Poruka"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as Commercial\n"
"                                Director in the software industry, Mich has helped Company to get where it\n"
"                                is today. Mich is among the best minds."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/mobile_view.js:54
#, python-format
msgid "Mobile preview"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_model
#: model:ir.model.fields,field_description:website.field_website_page_model_ids
msgid "Model"
msgstr "Model"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_model_data_id
msgid "Model Data"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:58
#, python-format
msgid "Most searched topics related to your keywords, ordered by importance:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to first"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to last"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to next"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to previous"
msgstr ""

#. module: website
#: selection:website.redirect,type:0
msgid "Moved permanently"
msgstr ""

#. module: website
#: selection:website.redirect,type:0
msgid "Moved temporarily"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "My Website"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:40
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Name"
msgstr "Naziv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Name and favicon of your website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Namecheap"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:41
#: code:addons/website/static/src/xml/website.contentMenu.xml:58
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "New Page"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_new_window
msgid "New Window"
msgstr "Novi Prozor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "Novine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
msgid "Next"
msgstr "Sledeće"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No-scroll"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "None"
msgstr "Prazno"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:66
#, python-format
msgid "Not used"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_language_count
msgid "Number of languages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "OVH"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Objectives"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 1 for three columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 2 for three columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 3 for three columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_picture
msgid "Odoo CMS - a big picture"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_floating
msgid "Odoo CMS- Sample image floating"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Odoo image and text block"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
msgid ""
"Odoo provides essential platform for our project management.\n"
"                                                Things are better organized and more visible with it."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Odoo text and image block"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:5
#, python-format
msgid "On Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"One way is by optimizing your indexing on search engines like Google "
"naturally, without paying anything. The earliest you show up in the search, "
"the more visitors you get. This strategy called <strong>SEO</strong> "
"involves several actions."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Online users:"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_page_mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Optimize SEO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Organize and publish events to meet your prospects and customers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Other Info"
msgstr "Ostale informacije"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Our Offers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid "Our Products &amp; Services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.aboutus_page_ir_ui_view
#: model_terms:website.page,arch_db:website.aboutus_page
msgid "Our Team"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.aboutus_page_ir_ui_view
#: model_terms:website.page,arch_db:website.aboutus_page
msgid ""
"Our products are designed for small to medium size companies willing to optimize\n"
"                                                      their performance."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid ""
"Our products are designed for small to medium size companies willing to optimize\n"
"                            their performance."
msgstr ""

#. module: website
#: code:addons/website/models/website.py:206
#: code:addons/website/models/website.py:275
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view_page_ids
#: model:ir.model.fields,field_description:website.field_website_page_page_ids
#, python-format
msgid "Page"
msgstr "Stranica"

#. module: website
#: code:addons/website/models/website.py:213
#, python-format
msgid "Page <b>%s</b> contains a link to this page"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:282
#, python-format
msgid "Page <b>%s</b> is calling this file"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_website_indexed
msgid "Page Indexed"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:46
#, python-format
msgid "Page Name"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:51
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Page Properties"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:42
#: code:addons/website/static/src/xml/website.seo.xml:14
#, python-format
msgid "Page Title"
msgstr "Naslov stranice"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:53
#: model:ir.model.fields,field_description:website.field_website_page_url
#, python-format
msgid "Page URL"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:208
#: code:addons/website/models/website.py:277
#: model:ir.ui.menu,name:website.menu_website_pages_list
#, python-format
msgid "Pages"
msgstr "Strane"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Panama Sky"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_panel
msgid ""
"Panels are a great tool to compare offers or to emphasize on\n"
"                key features. To compare products, use the inside columns."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_parent_left
msgid "Parent Left"
msgstr "Roditelj lijevo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_parent_id
msgid "Parent Menu"
msgstr "Roditeljski Meni"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_parent_right
msgid "Parent Rigth"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_post
msgid "Partner Detail"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_partner_id
msgid "Partner-related data of the user"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Partners"
msgstr "Partneri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Pay services like"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Peak"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "People"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Plan"
msgstr ""

#. module: website
#: model:web.planner,tooltip_planner:website.planner_website
msgid ""
"Plan your website strategy, design your pages, sell your products and grow "
"your online business!"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_web_planner
msgid "Planner"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Please install a theme in order to customize your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Post content on other people blogs, forums, websites, that are related to "
"your environment."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
msgid "Prev"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:28
#, python-format
msgid "Preview"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Promote"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:339
#, python-format
msgid "Promote Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote page on the web"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Promote your catalog of services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Provide fast, professional and accurate information to your visitors"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_partner_id
msgid "Public Partner"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_user_id
msgid "Public User"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:41
#: code:addons/website/static/src/xml/website.pageProperties.xml:122
#, python-format
msgid "Publish"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Publish multimedias to document your products/services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Publish the link of your website through your social media channels "
"(Twitter, Facebook, LinkedIn)."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:42
#, python-format
msgid "Publish your page by clicking on the <b>Save</b> button."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:37
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Published"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:133
#: model:ir.model.fields,field_description:website.field_website_page_date_publish
#, python-format
msgid "Publishing Date"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "QWeb"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Qualify your visitors in order to reach them out with marketing campaigns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Reach your customers and prospects on a recurring basis"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Ready For Launch!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Recipient"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Redeem a $75 coupon code to start your campaign."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_redirect_url_from
msgid "Redirect From"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:62
#, python-format
msgid "Redirect Old URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_redirect_url_to
msgid "Redirect To"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_redirect_type
msgid "Redirection Type"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_redirect_list
msgid "Redirects"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Reduce the time and resources spent on support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Regroup similar pages into a sub-menu, so they will appear under a unique "
"drop-down menu."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_menu_ids
msgid "Related Menus"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_page_id
msgid "Related Page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:58
#, python-format
msgid "Remove this suggestion from dashboard"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:68
#, python-format
msgid "Rename Page To:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Reset selected templates"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Reset templates"
msgstr ""

#. module: website
#: model:res.groups,name:website.group_website_publisher
msgid "Restricted Editor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded corners"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sails"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Sample images"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:96
#: code:addons/website/static/src/js/menu/content.js:44
#: code:addons/website/static/src/js/menu/seo.js:342
#, python-format
msgid "Save"
msgstr "Sačuvaj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Second List"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "See Google Analytics data on your Website Dashboard"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "See and buy your products"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "See how to translate on your own"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:387
#, python-format
msgid "Select a Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid "Select and delete blocks to remove some features."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_message
msgid "Sell Online. Easily."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Sell more online"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Send <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_comment
msgid "Send a Message to our Partners"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Send an email to all your contacts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.website2_contactus
#: model_terms:ir.ui.view,arch_db:website.website2_contactus_page_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.website2_contactus_page
msgid "Send us an email"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_sequence
#: model:ir.model.fields,field_description:website.field_website_page_priority
#: model:ir.model.fields,field_description:website.field_website_redirect_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Settings"
msgstr "Podešavanja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shadows"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Share experience on similar projects"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view_customize_show
#: model:ir.model.fields,field_description:website.field_website_page_customize_show
msgid "Show As Optional Inherit"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:86
#, python-format
msgid "Show in Top Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Show your address on a map in the"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Simple and obvious"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Slider Odoo Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideshow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Small"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_company_form_inherit_website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Social Media"
msgstr ""

#. module: website
#: code:addons/website/controllers/main.py:218
#, python-format
msgid "Sort by Name"
msgstr ""

#. module: website
#: code:addons/website/controllers/main.py:217
#, python-format
msgid "Sort by Url"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Square"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Start with the customer – find out what they want\n"
"                        and give it to them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Starter package"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:50
#, python-format
msgid "Stay on this page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Subtitle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Subtitle 2"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Subtitle 3"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:58
#, python-format
msgid "Suggested"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_feature_grid
msgid ""
"Tell features the visitor would like to know, not what you'd like to say."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the<br/>customer for this feature."
msgstr ""

#. module: website
#: code:addons/website/models/website.py:225
#: code:addons/website/models/website.py:295
#, python-format
msgid "Template"
msgstr "Šablon"

#. module: website
#: code:addons/website/models/website.py:231
#, python-format
msgid "Template <b>%s (id:%s)</b> contains a link to this page"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:301
#, python-format
msgid "Template <b>%s (id:%s)</b> is calling this file"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Template fallback"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:227
#: code:addons/website/models/website.py:297
#, python-format
msgid "Templates"
msgstr "Šabloni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_partner_post
msgid "Thank you for posting a message !"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The Banner or Big Picture building blocks are good choices for that."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "The error occured while rendering the template"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "The following error was raised in the website controller"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_blog_post_website_url
#: model:ir.model.fields,help:website.field_delivery_carrier_website_url
#: model:ir.model.fields,help:website.field_event_event_website_url
#: model:ir.model.fields,help:website.field_event_track_website_url
#: model:ir.model.fields,help:website.field_hr_employee_website_url
#: model:ir.model.fields,help:website.field_hr_job_website_url
#: model:ir.model.fields,help:website.field_im_livechat_channel_website_url
#: model:ir.model.fields,help:website.field_product_template_website_url
#: model:ir.model.fields,help:website.field_res_partner_grade_website_url
#: model:ir.model.fields,help:website.field_res_partner_tag_website_url
#: model:ir.model.fields,help:website.field_res_partner_website_url
#: model:ir.model.fields,help:website.field_slide_channel_website_url
#: model:ir.model.fields,help:website.field_slide_slide_website_url
#: model:ir.model.fields,help:website.field_website_page_website_url
#: model:ir.model.fields,help:website.field_website_published_mixin_website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_act_server_website_url
msgid "The full URL to access the server action through the website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
msgid "The page you were looking for could not be authorized."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.404
msgid ""
"The page you were looking for could not be found; it is possible you have\n"
"                        typed the address incorrectly, but it has most probably been removed due\n"
"                        to the recent website reorganisation."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "The selected templates will be reset to their factory settings."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "The shorter the better"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
msgid "There are currently no pages for your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"There are so many ways to communicate with your customers, visitors and\n"
"                        prospects that it's sometimes difficult to know what to put your energy into. Here are\n"
"                        some advices on what to focus on next."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:80
#, python-format
msgid "There is no data currently available."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between the website\n"
"                                (\"Website operator\", \"us\", \"we\" or \"our\") and you (\"User\", \"you\" or \"your\")."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"This Agreement sets forth the general terms and conditions of your use of this website\n"
"                                and any of its products or services (collectively, \"Website\" or \"Services\")."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings_favicon
#: model:ir.model.fields,help:website.field_website_favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings_default_lang_code
#: model:ir.model.fields,help:website.field_website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are administrator of "
"this site."
msgstr ""

#. module: website
#: code:addons/website/models/website.py:246
#, python-format
msgid "This page is in the menu <b>%s</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Time-consuming"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these\n"
"                        three columns using the right icon of each block.\n"
"                        Then, duplicate one of the column to create a new\n"
"                        one as a copy."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"To get an external assessment of your website, you can also submit it to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:382
#: model:website.menu,name:website.main_menu
#: model:website.menu,name:website.website2_main_menu
#, python-format
msgid "Top Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "Traceback"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits in Google Analytics"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Tracking ID"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/rte.summernote.js:17
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Translate"
msgstr "Prevedi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Translating your website into other languages is the best way to broaden its"
" audience."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_social_twitter
msgid "Twitter Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:76
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Type"
msgstr "Tip"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:96
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "UA-XXXXXXXX-Y"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings_cdn_filters
#: model:ir.model.fields,help:website.field_website_cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Ultimate package"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.index_management
msgid "Unindexed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:37
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Unpublished"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Update your internal documents: footer of sales order, contracts, invoices, "
"business cards, etc."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu_url
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
msgid "Url"
msgstr "URL"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use Of Cookies"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_cdn_activated
msgid "Use a Content Delivery Network (CDN)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:97
#, python-format
msgid "Use as Homepage"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Use the Button block or the Newsletter subscription one (if Mass Mailing "
"installed)."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.robots
msgid ""
"User-agent: *\n"
"Sitemap:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Fast"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Slow"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_view_id
msgid "View"
msgstr "View"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_arch
#: model:ir.model.fields,field_description:website.field_website_page_arch_base
msgid "View Architecture"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_name
msgid "View Name"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_type
msgid "View Type"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_mode
msgid "View inheritance mode"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_inherit_children_ids
msgid "Views which inherit from this one"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_post_website_published
#: model:ir.model.fields,field_description:website.field_event_track_website_published
#: model:ir.model.fields,field_description:website.field_hr_employee_website_published
#: model:ir.model.fields,field_description:website.field_hr_job_website_published
#: model:ir.model.fields,field_description:website.field_im_livechat_channel_website_published
#: model:ir.model.fields,field_description:website.field_product_template_website_published
#: model:ir.model.fields,field_description:website.field_res_partner_website_published
#: model:ir.model.fields,field_description:website.field_slide_channel_website_published
#: model:ir.model.fields,field_description:website.field_slide_slide_website_published
#: model:ir.model.fields,field_description:website.field_website_page_website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin_website_published
msgid "Visible in Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Visitors might not be interested in your products or services when they come"
" to your site. They could want to learn something, improve their life, grow "
"their business, find out more about you, etc. What great content can you "
"offer your visitors? Why should they stay on your website?"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:70
#, python-format
msgid "Visits"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.aboutus_page_ir_ui_view
#: model_terms:website.page,arch_db:website.aboutus_page
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                                                      life through disruptive products. We build great products to solve your\n"
"                                                      business problems."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_default
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                            life through disruptive products. We build great products to solve your\n"
"                            business problems."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:37
#, python-format
msgid "We found these ones:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "We hope this planner helped you to create your website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.website2_contactus
#: model_terms:ir.ui.view,arch_db:website.website2_contactus_page_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.website2_contactus_page
msgid "We'll do our best to get back to you as soon as possible."
msgstr ""

#. module: website
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_id
#: model:ir.model.fields,field_description:website.field_website_menu_website_id
#: model:ir.model.fields,field_description:website.field_website_page_website_id
#: model:ir.model.fields,field_description:website.field_website_redirect_website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
msgid "Website"
msgstr "Internet stranica"

#. module: website
#: model:ir.actions.act_window,name:website.action_module_website
msgid "Website Apps"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_domain
msgid "Website Domain"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_favicon
msgid "Website Favicon"
msgstr ""

#. module: website
#: model:ir.actions.act_url,name:website.action_website_homepage
msgid "Website Homepage"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_website_name
#: model:ir.model.fields,field_description:website.field_website_name
msgid "Website Name"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Website Pages"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_act_server_website_path
msgid "Website Path"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_redirect
msgid "Website Redirect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_redirect_form_view
msgid "Website Redirect Settings"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_redirect_list
#: model_terms:ir.ui.view,arch_db:website.website_redirect_tree_view
msgid "Website Redirects"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Website Settings"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_module_theme
msgid "Website Theme"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Title"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_post_website_url
#: model:ir.model.fields,field_description:website.field_delivery_carrier_website_url
#: model:ir.model.fields,field_description:website.field_event_event_website_url
#: model:ir.model.fields,field_description:website.field_event_track_website_url
#: model:ir.model.fields,field_description:website.field_hr_employee_website_url
#: model:ir.model.fields,field_description:website.field_hr_job_website_url
#: model:ir.model.fields,field_description:website.field_im_livechat_channel_website_url
#: model:ir.model.fields,field_description:website.field_product_template_website_url
#: model:ir.model.fields,field_description:website.field_res_partner_grade_website_url
#: model:ir.model.fields,field_description:website.field_res_partner_tag_website_url
#: model:ir.model.fields,field_description:website.field_res_partner_website_url
#: model:ir.model.fields,field_description:website.field_slide_channel_website_url
#: model:ir.model.fields,field_description:website.field_slide_slide_website_url
#: model:ir.model.fields,field_description:website.field_website_page_website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin_website_url
msgid "Website URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_act_server_website_url
msgid "Website Url"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of the\n"
"                                User by this site. The User may configure his / her browser to notify and\n"
"                                reject the installation of the cookies sent by us."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_blog_website_meta_description
#: model:ir.model.fields,field_description:website.field_blog_post_website_meta_description
#: model:ir.model.fields,field_description:website.field_blog_tag_website_meta_description
#: model:ir.model.fields,field_description:website.field_event_event_website_meta_description
#: model:ir.model.fields,field_description:website.field_event_track_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_documentation_toc_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_forum_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_post_website_meta_description
#: model:ir.model.fields,field_description:website.field_forum_tag_website_meta_description
#: model:ir.model.fields,field_description:website.field_hr_job_website_meta_description
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_meta_description
#: model:ir.model.fields,field_description:website.field_product_public_category_website_meta_description
#: model:ir.model.fields,field_description:website.field_product_template_website_meta_description
#: model:ir.model.fields,field_description:website.field_res_partner_website_meta_description
#: model:ir.model.fields,field_description:website.field_slide_channel_website_meta_description
#: model:ir.model.fields,field_description:website.field_slide_slide_website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page_website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_website_meta_description
msgid "Website meta description"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_blog_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_blog_post_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_blog_tag_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_event_event_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_event_track_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_documentation_toc_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_forum_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_post_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_forum_tag_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_hr_job_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_product_public_category_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_product_template_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_res_partner_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_slide_channel_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_slide_slide_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page_website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_blog_blog_website_meta_title
#: model:ir.model.fields,field_description:website.field_blog_post_website_meta_title
#: model:ir.model.fields,field_description:website.field_blog_tag_website_meta_title
#: model:ir.model.fields,field_description:website.field_event_event_website_meta_title
#: model:ir.model.fields,field_description:website.field_event_track_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_documentation_toc_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_forum_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_post_website_meta_title
#: model:ir.model.fields,field_description:website.field_forum_tag_website_meta_title
#: model:ir.model.fields,field_description:website.field_hr_job_website_meta_title
#: model:ir.model.fields,field_description:website.field_ir_ui_view_website_meta_title
#: model:ir.model.fields,field_description:website.field_product_public_category_website_meta_title
#: model:ir.model.fields,field_description:website.field_product_template_website_meta_title
#: model:ir.model.fields,field_description:website.field_res_partner_website_meta_title
#: model:ir.model.fields,field_description:website.field_slide_channel_website_meta_title
#: model:ir.model.fields,field_description:website.field_slide_slide_website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page_website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata_website_meta_title
msgid "Website meta title"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
msgid "Website: Dashboard"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.action_partner_post
msgid "Website: Partner Post and Thanks Demo"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.action_partner_comment
msgid "Website: Partners Comment Form"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page_website_ids
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install_website_ids
msgid "Websites to translate"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "Welcome"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:4
#, python-format
msgid "Welcome to your"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:10
#, python-format
msgid "What do you want to do?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "What may <strong>interest your visitors?</strong>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "With a great subtitle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Write a good article about your business and publish it on article database "
"websites (e.g. squidoo.com). Send it to press websites and to your local "
"newspapers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax_slider
msgid ""
"Write a quote here from one of your customers. Quotes are a\n"
"                                                great way to build confidence in your products or services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or\n"
"                        services.<br/>To be successful your content needs to be\n"
"                        useful to your readers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid ""
"Write one or two paragraphs describing your product,\n"
"                        services or a specific feature. To be successful\n"
"                        your content needs to be useful to your readers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature. To be successful your content needs to be useful to your readers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_big_message
msgid "Write one sentence to convince visitor about your message."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know,<br/>not what you want to show."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can also pay for a sponsored indexing and place advertisements on web pages that show results from search\n"
"                        engine queries. This is called <strong>SEA</strong>."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You can set a custom domain name (e.g. yourcompany.com) for both the website\n"
"                        and your emails. Your website address is as important to your branding as the\n"
"                        name of your business or organization, so put some thought into changing it\n"
"                        for a proper domain."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:91
#, python-format
msgid "You do not seem to have access to this Analytics Account."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"You have many blocks available: References, Quotes Slider, Twitter "
"Scroller,etc."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:90
#, python-format
msgid "You need to log in to your Google Account before:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use of\n"
"                                any website which you access through a link from this Website. Your linking to\n"
"                                any other off-site pages or other websites is at your own risk."
msgstr ""

#. module: website
#: model_terms:ir.actions.act_window,help:website.action_module_theme
#: model_terms:ir.actions.act_window,help:website.action_module_website
msgid "You should try other search criteria."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:100
#, python-format
msgid "Your Client ID:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Your Slide Title"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:95
#, python-format
msgid "Your Tracking ID:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "Your Website Title"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:11
#, python-format
msgid "Your current changes will be saved automatically."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Your homepage is the most important page of your website. This is where visitors get their first impressions of you. An efficient homepage will prompt them to stay on your site, guide them towards your content, strengthen your company's branding and more.<br/>\n"
"                        Here are some pointers to help you get started."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Your menu must be built for your visitors and not for your own purposes or objectives.<br/>\n"
"                        It needs to be organized, so that they see all the main content at a glance.<br/>\n"
"                        It needs to be clear, so that they get the content they expect when clicking an item."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.edit_website_pages
msgid "Your search '"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"Your visitors won't see the footer immediately. Use it for links you don't "
"want in your main menu (e.g. social media, terms and conditions, privacy "
"terms, etc.)."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_social_youtube
msgid "Youtube Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"a reference to your new website. We will retweet it (we have 30,000 "
"followers) and feature it in our different communications."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "add keywords in picture's description."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "and evaluating the following expression:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "campaign."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "check out our Blog application"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:41
#, python-format
msgid "describing your page content"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:70
#, python-format
msgid "e.g. About Us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "etc."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: About us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: Blog, Success stories, References, Events, Jobs"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: Contact us, Our Customers, Privacy Policy, Events, Blog, Jobs"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "ex: interesting contents, texts and articles"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "for a free evaluation of the usability of your homepage."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:41
#, python-format
msgid "found(s)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "free website"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:28
#, python-format
msgid "how your page will be listed on Google"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://plus.google.com/+Odooapps"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://twitter.com/Odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://www.facebook.com/odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://www.linkedin.com/company/odoo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://www.youtube.com/user/OpenERPonline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "https://youraccount.github.io"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "in their SEO metadata, including tags, title and description."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
msgid "ir.actions.server"
msgstr "ir.actions.server"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "ir.attachment"
msgstr "ir.attachment"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "ir.qweb"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "learn how people find your website,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "make optimizations for Google referencing."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "name"
msgstr "Naziv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "or Edit Master"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "peek.usertesting.com"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "sending us an email"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:6
#, python-format
msgid "signs to get your website ready in no time."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.robots
msgid "sitemap.xml"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "through the content of your pages;"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"to describe\n"
"                    <br/> your experience or to suggest improvements!"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "to get started."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid ""
"to get your content translated by real professional translators. Quality may"
" vary."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "understand your search traffic,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "url"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "user / month (billed annually)"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings_website_id
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "website"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "website.published.mixin"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_planner
msgid "will help you:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "with"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "yes"
msgstr ""
