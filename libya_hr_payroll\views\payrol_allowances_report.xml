<?xml version="1.0"?>
<odoo>
    <template id="report_libya_allowances">
        <t t-call="web.html_container">
            <!--        <t t-foreach="docs" t-as="o">-->
            <t t-call="web.external_layout">
                <div class="page" style="direction: rtl;"><br/><br/><br/><br/>
                    <h5 class="text-center">كشف بالمرتب وتفصيل الإضافات</h5>
                    <t t-if="location">
                        <h5 class="text-center"> الموقع : <span t-esc="location"/></h5>
                    </t>
                    <h6 class="text-center"> &#160;لشهر <strong t-esc="date_month"/>&#160;<span>/</span><span
                            t-esc="date_year"/></h6>

                    <table class="table table-bordered">
                        <thead>
                            <tr class="text-center">
                                <th>م</th>
                                <th>الاسم</th>
                                <th>الأساسي</th>
                                <th>فروقات</th>
                                <th>علاوة مسؤولية</th>
                                <th>علاوات اخرى</th>
                                <th>إجمالي المرتب</th>
                                <th>الضمان الاجتماعي للموظف</th>
                                <th>التضامن للموظف</th>
                                <th>كروت هاتف</th>
                                <th>كروت نت</th>
                                <th>علاوة لجان</th>
                                <th>صافي المرتب</th>
                                <th>نهاية الخدمة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="payslip_ids" t-as="payslip" class="text-center">


                                <td style="background-color:#D0D0D0;"><span t-esc="payslip_index + 1"/></td>
                                <td>
                                    <span t-esc="payslip['employee_name']"/>
                                </td>
                                <td><span t-esc="'%.3f'% payslip['basic']"/></td>

                                <t t-if="payslip['Other']">
                                    <td><span t-esc="'%.3f'% payslip['Other']"/></td>
                                </t>
                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>

                                <t t-if="payslip['HRA']">
                                    <td><span t-esc="'%.3f'% payslip['HRA']"/></td>
                                </t>
                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>
                                <t t-if="payslip['Meal']">
                                    <td><span t-esc="'%.3f'% payslip['Meal']"/></td>
                                </t>
                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>

                                <t t-if="payslip['TOBASIC']">
                                <td><span t-esc="'%.3f'% payslip['TOBASIC']"/></td>
                                </t>

                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>

                                <t t-if="payslip['MDED']">
                                    <td><span t-esc="'%.3f'% payslip['MDED']"/></td>
                                </t>
                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>

                                <t t-if="payslip['TDDED']">
                                    <td><span t-esc="'%.3f'% payslip['TDDED']"/></td>
                                </t>
                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>

                                <t t-if="payslip['Travel']">
                                    <td><span t-esc="'%.3f'% payslip['Travel']"/></td>
                                </t>
                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>

                                <t t-if="payslip['DA']">
                                    <td><span t-esc="'%.3f'% payslip['DA']"/></td>
                                </t>
                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>

                                <t t-if="payslip['Medical']">
                                    <td><span t-esc="'%.3f'% payslip['Medical']"/></td>
                                </t>
                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>

                                <t t-if="payslip['NETSALARY']">
                                    <td><span t-esc="'%.3f'% payslip['NETSALARY']"/></td>
                                </t>

                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>

                                <t t-if="payslip['EOS']">
                                    <td><span t-esc="'%.3f'% payslip['EOS']"/></td>
                                </t>

                                <t t-else="">
                                    <td><span>-</span></td>
                                </t>


                            </tr>
                        </tbody>
                        <thead style="background-color:#D0D0D0;">
                            <tr class="text-center">

                                <th colspan="2">الإجمالي</th>
                                <th><span t-esc="'%.2f'% sum(m['basic'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['Other'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['HRA'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['Meal'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['TOBASIC'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['MDED'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['TDDED'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['Travel'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['DA'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['Medical'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['NETSALARY'] for m in payslip_ids)"/></th>
                                <th><span t-esc="'%.2f'% sum(m['EOS'] for m in payslip_ids)"/></th>

                            </tr>
                        </thead>

                    </table><br/>
                    <div class="row">
                        <h5 style="margin-left:420px;margin-right:100px;">
                            إعداد
                        </h5>
                        <h5 style="margin-left:350px;">
                            المراجعة الداخلية
                        </h5>
                        <h5>
                            المديـــــــر العام
                        </h5>
                    </div>
                    <div class="row">
                        <h5 style="margin-left:280px;margin-right:30px;">
                            ------------------------------
                        </h5>
                        <h5 style="margin-left:280px;">
                            ------------------------------
                        </h5>
                        <h5>
                            ------------------------------
                        </h5>
                    </div><br/><br/>
                    <div class="text-center">
                        <h5>
                            توقيع وختم المصرف بالإستلام
                        </h5>
                        <h5>
                            --------------------------------
                        </h5>
                    </div>

                </div>
            </t>
        </t>
        <!--    </t>-->
    </template>
</odoo>
