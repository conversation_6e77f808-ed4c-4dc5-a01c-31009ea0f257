# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# Harcogourmet, 2022
# Jonatan Gk, 2022
# marc<PERSON>, 2022
# eriiikgt, 2022
# Cristian<PERSON>ruz<PERSON>, 2022
# eric<PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2022
# Ó<PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-26 11:51+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Martin Trigaux, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr " Dades recuperades"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "\" categoria."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_categories_recursive
msgid "#{'Unfold' if c.id in category.parents_and_self.ids else 'Fold'}"
msgstr "#{'Unfold' if c.id in category.parents_and_self.ids else 'Fold'}"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s revisar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "%s resenyes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "&amp; Shipping"
msgstr "i enviament"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "'. Mostrant els resultats per'"

#. module: website_sale
#: model:product.template.attribute.value,name:website_sale.product_1_attribute_3_value_1
msgid "1 year"
msgstr "1 any"

#. module: website_sale
#: model:product.template.attribute.value,name:website_sale.product_1_attribute_3_value_2
msgid "2 year"
msgstr "2 anys"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>Comunicació: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<b>Shipping: </b>"
msgstr "<b>Enviament: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "<b>Your order: </b>"
msgstr "<b>La vostra comanda: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"                Garantia de retorn de 30 dies<br/>\n"
"                Enviament: 2-3 dies laborables"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<i class=\"fa fa-arrow-right\"/> Add payment acquirers"
msgstr "<i class=\"fa fa-arrow-right\"/> Afegiu métodes de pagament"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid "<i class=\"fa fa-bolt mr-2\"/>BUY NOW"
msgstr "<i class=\"fa fa-bolt mr-2\"/>COMPRAR ARA"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "<i class=\"fa fa-check\"/> Ship to this address"
msgstr "<i class=\"fa fa-check\"/> Enviar a aquesta adreça"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                            <span>Back</span>"
msgstr ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                            <span>Tornar</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                    <span>Return to Cart</span>"
msgstr ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                    <span>Tornar a la cistella</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_footer
msgid "<i class=\"fa fa-chevron-left\"/> Return to Cart"
msgstr "<i class=\"fa fa-chevron-left\"/> Tornar a la cistella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-edit\"/> Edit"
msgstr "<i class=\"fa fa-edit\"/> Editar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "<i class=\"fa fa-eraser mr-1\"/><u>Clear filters</u>"
msgstr "<i class=\"fa fa-eraser mr-1\"/><u>Netejar els filtres</u>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-plus-square\"/>\n"
"                                                        <span>Add an address</span>"
msgstr ""
"<i class=\"fa fa-plus-square\"/>\n"
"                                                        <span>Afegir una adreça</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Imprimir"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "<i class=\"fa fa-shopping-cart mr-2\"/>ADD TO CART"
msgstr "<i class=\"fa fa-shopping-cart mr-2\"/>Afegir a la cistella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/crm_team.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Aquí podeu trobar totes les cistelles abandonades, és a dir, les cistelles generades pels visitants del vostre lloc web fa més d’una hora que encara no s’han confirmat.</p>\n"
"                        <p>Heu d’enviar un correu electrònic als clients per animar-los!</p>\n"
"                    "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. Please "
"contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-muted\">No es permet el nom de la companyia o "
"el número d'IVA una vegada que s'han emès els documents per al seu compte. "
"Si us plau, posi's en contacte amb nosaltres directament per a aquesta "
"operació</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                        <span class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span class=\"\">Processar compra</span>\n"
"                                        <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                    <span class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span class=\"\">Processar compra</span>\n"
"                                    <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid ""
"<span class=\"d-none d-lg-inline font-weight-bold text-muted\">Sort "
"By:</span>"
msgstr ""
"<span class=\"d-none d-lg-inline font-weight-bold text-muted\">Ordenar "
"per:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid ""
"<span class=\"fa fa-chevron-down fa-border float-right\" role=\"img\" aria-"
"label=\"Details\" title=\"Details\"/>"
msgstr ""
"<span class=\"fa fa-chevron-down fa-border float-right\" role=\"img\" aria-"
"label=\"Details\" title=\"Details\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"fa fa-chevron-left fa-2x\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"
msgstr ""
"<span class=\"fa fa-chevron-left fa-2x\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                        <span class=\"\">Continue Shopping</span>"
msgstr ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                        <span class=\"\">Continuar comprant</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                    Continue<span class=\"d-none d-md-inline\"> Shopping</span>"
msgstr ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                    Continuar<span class=\"d-none d-md-inline\"> comprant</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"fa fa-chevron-left\"/> Previous"
msgstr "<span class=\"fa fa-chevron-left\"/> Anterior"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"fa fa-chevron-right fa-2x\" role=\"img\" aria-label=\"Next\" "
"title=\"Next\"/>"
msgstr ""
"<span class=\"fa fa-chevron-right fa-2x\" role=\"img\" aria-label=\"Next\" "
"title=\"Next\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Abandoned Carts</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Cistelles abandonades</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Assignment</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Assignació</span>\n"
"                           <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Confirmation Email</span>"
msgstr "<span class=\"o_form_label\">Correu de confirmació</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Invoicing Policy</span>"
msgstr "<span class=\"o_form_label\">Política de facturació</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Dona'ns la teva opinió</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr "<span class=\"s_website_form_label_content\">Pujar un document</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">La seva referència</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('product_variant_count', "
"'&lt;=', 1)]}\">Based on variants</span>"
msgstr ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('product_variant_count', "
"'&lt;=', 1)]}\">Basat en variants</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<span>Confirm</span>\n"
"                                    <i class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span>Confirmar</span>\n"
"                                    <i class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Confirmed</span>"
msgstr "<span>Confirmat</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<span>Next</span>\n"
"                                            <i class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span>Següent</span>\n"
"                                            <i class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>Comanda</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "<span>Process Checkout</span>"
msgstr "<span>Processar compra</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>Malauradament, la comanda no es pot confirmar perquè l'import del pagament no coincideix amb l'import de la cistella.\n"
"                        Si us plau, contacteu amb el responsable de la botiga per a més informació.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>Vídeo de presentació</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<span>Your payment has been authorized.</span>"
msgstr "<span>El seu pagament s'ha autoritzat.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form_web_terms
msgid "<strong class=\"align-top\">URL: </strong>"
msgstr "<strong class=\"align-top\">Enllaç: </strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid ""
"<strong class=\"o_categories_collapse_title text-"
"uppercase\">Categories</strong>"
msgstr ""
"<strong class=\"o_categories_collapse_title text-"
"uppercase\">Categories</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "<strong>Add to Cart</strong>"
msgstr "<strong>Afegir a la cistella</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                    If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>No s'ha trobat cap opció de pagament adequada.</strong><br/>\n"
"                                    Si creieu que és un error, contacteu amb l'administrador del lloc web."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Payment Information:</strong>"
msgstr "<strong>Informació del pagament:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total:</strong>"
msgstr "<strong>Total:</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Cart</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">S00060</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br/><br/>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr/>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\" t-out=\"company.name or ''\">My Company (San Francisco)</td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"company.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"company.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"<!-- HEADER -->\n"
"<tr>\n"
"<td align=\"center\" style=\"min-width: 590px;\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"<tr><td valign=\"middle\">\n"
"<t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"<span style=\"font-size: 10px;\">Your Cart</span><br/>\n"
"<span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">S00060</span>\n"
"</td><td valign=\"middle\" align=\"right\">\n"
"<img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"</td></tr>\n"
"<tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"<hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"</td></tr>\n"
"</table>\n"
"</td>\n"
"</tr>\n"
"<!-- CONTENT -->\n"
"<tr>\n"
"<td align=\"center\" style=\"min-width: 590px;\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"<tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"<h1 style=\"color:#A9A9A9;\">TENS ARTICLES A LA CISTELLA.</h1>\n"
"Voleu completar la compra?<br/><br/>\n"
"<t t-if=\"object.order_line\">\n"
"<t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"<hr/>\n"
"<table width=\"100%\">\n"
"<tr>\n"
"<td style=\"padding: 10px; width:150px;\">\n"
"<img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"</td>\n"
"<td>\n"
"<strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"</td>\n"
"<td width=\"100px\" align=\"right\">\n"
"<t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"</t>\n"
"<hr/>\n"
"</t>\n"
"<div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"<a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"Resume order\n"
"</a>\n"
"</div>\n"
"<div style=\"text-align: center;\"><strong>Gràcies per comprar a <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"</td></tr>\n"
"<tr><td style=\"text-align:center;\">\n"
"<hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"</td></tr>\n"
"</table>\n"
"</td>\n"
"</tr>\n"
"<!-- FOOTER -->\n"
"<tr>\n"
"<td align=\"center\" style=\"min-width: 590px;\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"<tr><td valign=\"middle\" align=\"left\" t-out=\"company.name or ''\">My Company (San Francisco)</td></tr>\n"
"<tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"<t t-out=\"company.phone or ''\">******-123-4567</t>\n"
"<t t-if=\"company.email\">\n"
"| <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a>\n"
"</t>\n"
"<t t-if=\"company.website\">\n"
"| <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"</t>\n"
"</td></tr>\n"
"</table>\n"
"</td>\n"
"</tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"<tr><td style=\"text-align: center; font-size: 13px;\">\n"
"Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"</td></tr>\n"
"</table>\n"
"</td></tr>\n"
"</table>"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__terms_url
msgid "A preview will be available at this URL."
msgstr "Una vista prèvia estarà disponible en aquest enllaç."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""
"Un producte pot ser un producte físic o un servei que vostè embeni als seus "
"clients."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "A short description that will also appear on documents."
msgstr "Una breu descripció que també apareixerà en els documents."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "AT A GLANCE"
msgstr "D'UN COP D'ULL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "Abandonat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "Cistella abandonada"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/models/crm_team.py:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#, python-format
msgid "Abandoned Carts"
msgstr "Cistelles abandonades"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "Cistelles abandonades per recuperar"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "Retard abandonat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Abandoned carts are all carts left unconfirmed by website visitors. You can "
"find them in *Website > Orders > Abandoned Carts*. From there you can send "
"recovery emails to visitors who entered their contact details."
msgstr ""
"Les cistelles abandonades són les que romanen sense confirmar pels visitants"
" del lloc web. Podeu trobar-les a *Lloc web > Comandes > Cistelles "
"abandonades*. Des d'allí podeu enviar mails de recuperació als visitants que"
" van introduir detalls de contacte."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "Quant a productes de venda creuada"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "Mida de fitxer acceptable"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr "Accessoris per al producte"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""
"Els accessoris apareixen quan el client revisa la cistella abans del "
"pagament (estratègia de venda creuada)."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "Productes Accessoris"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Número de compte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add a Media"
msgstr "Afegeix un suport"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Add one"
msgstr "Afegir-ne un"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add price per base unit of measure on your Products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
msgid "Add to Cart"
msgstr "Afegir a la Cistella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
msgid "Add to Cart <i class=\"fa fa-fw fa-shopping-cart\"/>"
msgstr "Afegeix a la cistella <i class=\"fa fa-fw fa-shopping-cart\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Address"
msgstr "Adreça"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "Tots els Productes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "Totes les llistes de preus"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_utils.xml:0
#, python-format
msgid "All results"
msgstr "Tots els resultats"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr ""
"Permet als compradors comparar productes basant-se en els seus atributs"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "Permet a l'usuari escollir una llista de preus"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
msgid "Alternative Products"
msgstr "Productes Alternatius"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.recommended_products
msgid "Alternative Products:"
msgstr "Productes alternatius:"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "Quantitat de cistelles abandonades"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "Aplicar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"Aplicar descomptes manuals en  línies de comandes o visualitzar descomptes "
"calculats des de les tarifes (opció a activar a la configuració de la "
"tarifa)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Apply specific prices per country, discounts, etc."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "Assignació de comandes en línia"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Atributs"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Average Order"
msgstr "Mitjana d'ordres"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Nom del Banc "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "Comptador d'unitats base"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "Nom de la unitat base"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "Preu de la unitat base"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
#: model:ir.ui.menu,name:website_sale.website_base_unit_menu
msgid "Base Units"
msgstr "Unitats base"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "Ves amb compte!"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "BeNeLux"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "Benelux"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Best Sellers"
msgstr "Els més venuts"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Billing"
msgstr "Facturació"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing Address"
msgstr "Adreça de facturació"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Bin"
msgstr "Binari"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "Binaris"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""
"Incrementa les teves vendes amb dos tipus de programes de descompte: "
"promocions i vals descompte. Es pot fixar condicions específiques "
"(productes, clients, import mínim, període). Les recompenses poden ser "
"descomptes (% o import fixe) o productes gratuïts  "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "Fons"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Box"
msgstr "Quadre"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "Caixes"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "Marca"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "Gabinets"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Campaigns"
msgstr "Campanyes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "Pot la imatge 1024 ser ampliada"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "Pot publicar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Can be used as payment toward future orders."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Capture order payments when the delivery is completed."
msgstr "Captura els pagaments de la comanda quan es completi el lliurament."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "Quantitat Cistella"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "Mail de recuperació de cistella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Cart is abandoned after"
msgstr "Una cistella es considera abandonada al cap de"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "Ja ha estat enviat un mail de recuperació de cistella"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Carts"
msgstr "Cistelles"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "Les cistelles es marquen com abandonades desprès d'aquest temps"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Categories"
msgstr "Categories"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""
"Les categories s'utilitzen per a mostrar els teus productes a través de la "
"interfície de pantalla tàctil"

#. module: website_sale
#: code:addons/website_sale/models/product_template.py:0
#, python-format
msgid "Categories:"
msgstr "Categories:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "Categoria"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "Descripció de la categoria"

#. module: website_sale
#: code:addons/website_sale/models/product_template.py:0
#, python-format
msgid "Category:"
msgstr "Categoria:"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Chair"
msgstr "Cadira"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "Càtedres"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"No està permès canviar el NIF un cop s'han expedit document(s) pel compte. "
"Si us plau, contacteu directament amb nosaltres per a fer-ho."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"No es permet canviar el nom de l'empresa un cop s'han expedit document(s) "
"pel compte. Si us plau contacteu directament amb nosaltres per a fer-ho."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once documents have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "Categories fill"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"No es permet canviar el vostre nom un cop s'hagin emès les factures per al "
"vostre compte. Si us plau, poseu-vos en contacte amb nosaltres directament "
"per a aquesta operació."

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_open_website_sale_onboarding_payment_acquirer_wizard
msgid "Choose a payment method"
msgstr "Trieu una forma de pagament"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "Nadal"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "Ciutat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr ""
"Feu clic a <i>'Nou'</i> a la cantonada superior dreta per crear el vostre "
"primer producte."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "Fes clic aquí"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on <em>Continue</em> to create the product."
msgstr "Feu clic en <em>Continuar</em> per crear el producte."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on this button so your customers can see it."
msgstr "Feu clic en aquest botó perquè els vostres clients puguin veure-ho."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Comma-separated list of parts of product names"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
#, python-format
msgid "Company Name"
msgstr "Nom d'empresa"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "Components"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "Calcular despeses d'enviament i envia-ho amb Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Calcular despeses d'enviament amb DHL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Calcular despeses d'enviament amb FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Calcular despeses d'enviament amb UPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Calcular despeses d'enviament amb USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Calcular despeses d'enviament amb bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Calcular despeses d'enviament en comandes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustos de configuració"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Confirm Order"
msgstr "Confirmar comanda"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Confirm orders when you get paid."
msgstr "Confirmeu les comandes quan us paguin."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "Confirmada"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "Comandes confirmades"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Conversion"
msgstr "Conversió"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "Couches"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "País"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "Crea un nou producte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_date
msgid "Created on"
msgstr "Creat el"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "Data de creació"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "Categoria actual o totes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "Unitat de mesura personalitzada"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "Client"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "País del client"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "Comentaris del client"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
msgid "Customers"
msgstr "Clients"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "Connector DHL Express"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "Moneda per defecte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist"
msgstr "Tarifa per defecte"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr ""
"Definiu una unitat personalitzada per mostrar en el preu per unitat de camp "
"de mesura."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "Definiu una categoria nova"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete"
msgstr "Eliminar"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__internal
msgid ""
"Delivery methods are only used internally: the customer doesn't pay for "
"shipping costs"
msgstr ""
"Mètodes d'enviament només son utilitzat internament: el client no paga pels "
"costos d'envio"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__website
msgid ""
"Delivery methods are selectable on the website: the customer pays for "
"shipping costs"
msgstr ""
"Els mètodes d'enviaments són seleccionables al lloc web: el client paga pels"
" costos d'enviament"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "Descripció"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "Descripció per al lloc web"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "Taules"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "Determini l'ordre de visualització al lloc web de comerç electrònic"

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "Digerir"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_digital
msgid "Digital Content"
msgstr "Contingut Digital"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display a prompt with optional products when adding to cart"
msgstr "Mostra un missatge amb productes opcionals en afegir a la cistella"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr ""
"Mostra el preu de la unitat base a les pàgines de comerç electrònic. "
"Establiu-ho a 0 per amagar-ho per aquest producte."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "Mostrat a la part inferior de les pàgines de productes"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr ""
"Mostra la unitat personalitzada per als productes si està definida o la "
"unitat de mesura seleccionada en cas contrari."

#. module: website_sale
#: code:addons/website_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Sense accés, omet aquesta informació per a usuaris amb correu electrònic"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__done
msgid "Done"
msgstr "Fet"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Double click here to set an image describing your product."
msgstr "Doble clic per crear una imatge que descrigui el seu producte."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Drag building blocks here to customize the header for \""
msgstr ""
"Arrossegueu els blocs de construcció aquí per a personalitzar la capçalera "
"de \""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr "Arrossegui aquest bloc i deixi'l en la seva pàgina."

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Drawer"
msgstr "Dibuixador"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "Calaixos"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "Camps extres de comerç electrònic"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr "E-Commerce Extra Info Shown a la pàgina del producte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "Codi promocional"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "EUR"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Edit"
msgstr "Editar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form_web_terms
msgid "Edit in Website Builder"
msgstr "Edita al Constructor de llocs web"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr "Editi el preu d'aquest producte prement en l'import."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "Editar aquesta adreça"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_email_account
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "Correu electrònic"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email Template"
msgstr "Plantilla de correu electrònic"

#. module: website_sale
#: model:ir.model,name:website_sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistent de redacció de correu electrònic"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email sent to the customer after the checkout"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "Codi insertat"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Enter a name for your new product"
msgstr "Introduïu un nom pel vostre producte"

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "Error! No podeu crear categories recursives."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
msgid "Extra Info"
msgstr "Informació extra"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Extra Product Media"
msgstr "Producte multimèdia extra"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "Imatges extravariants"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "Multimèdia extravariant"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Featured"
msgstr "Destacats"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "FedEx"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "Camp"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "Camp d'Etiqueta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "Nom de camp"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "Del lloc web"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "Furnitures"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Generate an invoice from orders ready for invoicing."
msgstr "Generar una factura des de comandes llestes per facturar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr ""
"Genereu la factura automàticament quan es confirmi el pagament en línia"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_gift_card
msgid "Gift Card"
msgstr "Targeta Regal"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""
"Indica l'ordre de seqüència quan es mostra una llista de categories de "
"producte."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to cart"
msgstr "Ves a la cistella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "Concediu descomptes a les línies de comandes de venda"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
msgid "Grid"
msgstr "Graella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "Agrupar per"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutament HTTP"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
msgid "Hidden"
msgstr "Ocult"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr "Mida enorme del fitxer. La imatge s'ha d'optimitzar/reduir."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "I agree to the"
msgstr "Estic d'acord amb la"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "I have a promo code"
msgstr "Tinc un codi promocional"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__id
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "Imatge"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "Imatge 1024Comment"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "Imatge 128"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "Image 256"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "Imatge 512"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "Nom de la imatge"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "Correu no vàlid. Si us plau proporcioneu un correu electrònic vàlid."

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_invoices_ecommerce
msgid "Invoices"
msgstr "Factures"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "Facturació"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "Està publicat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "Emet factures als clients"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr ""
"Està prohibit modificar una comanda de venda que no estigui en estat de "
"projecte."

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "Assentament comptable"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__just_done
msgid "Just done"
msgstr "Acabat de fer"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "KPI del valor total de totes les vendes"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lamp"
msgstr "Làmpada"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "Làmpades"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_public_category____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "Últim mes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "Última comanda de venda en línia"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "Darrera setmana"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "Últim any"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "Dreta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let returning shoppers save products in a wishlist"
msgstr ""
"Deixar que els compradors que retornin guardin els productes en una llista "
"de desitjos"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr "Feu que el client introdueixi una adreça d’enviament"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Let's create your first product."
msgstr "Creem el teu primer producte."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Let's now take a look at your administration dashboard to get your eCommerce"
" website ready in no time."
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lightbulb sold separately"
msgstr "Bombeta venuda per separat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "Línia de la comanda vinculada"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
msgid "List"
msgstr "Llista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Log In"
msgstr "Iniciar sessió"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "Gestionar Cupons i codis de promoció"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Medium"
msgstr "Mitja"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "ID del compte comercial"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Mètode"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "Multimèdia"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
#, python-format
msgid "My Cart"
msgstr "La meva Cistella"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Name"
msgstr "Nom"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "Nom curt"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/controllers/main.py:0
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "New Product"
msgstr "Nou Producte"

#. module: website_sale
#: model:product.ribbon,html:website_sale.new_ribbon
msgid "New!"
msgstr "Nou"

#. module: website_sale
#: model:ir.filters,name:website_sale.dynamic_snippet_newest_products_filter
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "Productes més nous"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Newest arrivals"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Next <span class=\"fa fa-chevron-right\"/>"
msgstr "Següent <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "No s'han trobat cistelles abandonades"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "No s'ha definit cap producte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in category \""
msgstr "No hi ha cap producte definit a la categoria \""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "Encara no hi ha vistes de productes per a aquest visitant"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "No redirect when the user adds a product to cart."
msgstr "No redirigir quan l'usuari afegeix un producte a la cistella."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "Sense resultats"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr "No hi ha resultats per a \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "No s'han trobat resultats per a '"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_utils.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "No s'ha trobat cap resultat. Proveu una altra cerca."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__none
msgid "No shipping management on website"
msgstr "No hi ha gestió d'enviament al lloc web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "Cap"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__not_done
msgid "Not done"
msgstr "Sense fer"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "Nombre de cistelles abandonades"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Number of Columns"
msgstr "Nombre de columnes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "Nombre de columnes de la graella a la botiga"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__cart_abandoned_delay
msgid "Number of hours after which the cart is considered abandoned."
msgstr ""
"Nombre d'hores a partir de les quals la cistella es considera abandonada."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Number of products"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "Nombre de productes a la graella de la botiga"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "On wheels"
msgstr "Sobre rodes"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr "Una vegada premeu <b>Desar</b>, el vostre producte s'actualitzarà."

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "Vendes en línia"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "Anàlisi de vendes en línia"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "Només serveis"

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"Només es permeten els llocs web de l'empresa.\n"
"Deixeu el camp Empresa buit o seleccioneu un lloc web d'aquesta empresa."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "Comerç electrònic de codi obert"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Open your website app here."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr ""
"Cal optimitzar! Redueix la mida de la imatge o augmenta la configuració de "
"compressió."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Option for: %s"
msgstr "Opció per: %s"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Option: %s"
msgstr "Opció: %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Optional Products"
msgstr "Productes opcionals"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__option_line_ids
msgid "Options Linked"
msgstr "Opcions vinculades"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "O escanegeu-me amb la vostra aplicació bancària."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "Data comanda"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "Línies de comanda mostrades al lloc web"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_order_line
msgid ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."
msgstr ""
"Línies de comanda a ser mostrades al lloc web. No s'han d'utilitzar amb un "
"propòsit de càlcul."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "Order Total"
msgstr "Total de la comanda"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
#, python-format
msgid "Orders"
msgstr "Comandes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Orders Followup"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "Comandes a facturar"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Orders to Invoice"
msgstr "Comandes a facturar"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Orders/Day"
msgstr "Comandes/Dia"

#. module: website_sale
#: model:product.ribbon,html:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "Estoc esgotat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "Token d'identitat PDT"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_page
msgid "Page"
msgstr "Pàgina"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "Categoria pare"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "Ruta arrel"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr "Pares i jo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now"
msgstr "Pagar Ara"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay with"
msgstr "Paga amb"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Mètode de pagament"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_acquirers
msgid "Payment Acquirers"
msgstr "Mètodes de pagament"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_icons
msgid "Payment Icons"
msgstr "Icones de pagament"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Instruccions de pagament"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Forma de pagament"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "Taulers de pagament"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "Transaccions de pagament"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Payments to Capture"
msgstr "Pagaments a capturar"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "Tipus d'usuari Paypal"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Pedal-based opening system"
msgstr "Sistema d'obertura basat en Pedal"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "Telèfon"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "Número de telèfon"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "Introduïu un URL de vídeo vàlid."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "Per favor processa la teva cistella actual."

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Month"
msgstr "Mes anterior"

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Week"
msgstr "Setmana anterior"

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Year"
msgstr "Any anterior"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price"
msgstr "Preu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price (high to low)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price (low to high)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Price - High to Low"
msgstr "Preu - alt a baix"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Price - Low to High"
msgstr "Preu - Baix a alt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "Preu per unitat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "Llista de preus disponibles per aquesta tenda virtual/lloc web"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "Tarifa"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_product_pricelist3
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Tarifes de preus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Fixar preu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "Imprimir"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "Processar la comanda un cop rebut el pagament."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#, python-format
msgid "Product"
msgstr "Producte"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "Accessoris de productes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "Atribut del producte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "Categoria del producte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
msgid "Product Comparison Tool"
msgstr "Eina de comparació de productes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "Imatge de Producte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "Imatges de producte"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.product
#, python-format
msgid "Product Name"
msgstr "Nom producte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr "Camps addicionals de la pàgina de producte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "Preus de producte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "Categories públiques de productes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "Plantilla de producte"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Línia d' atributs de plantilla de producte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Plantilla de producte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "Variants de producte"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_variants
msgid "Product Variants"
msgstr "Variants de producte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "Vistes de productes"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "Historial de vistes de productes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product names"
msgstr "Noms dels productes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product prices displaying in web catalog"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "Llaç del producte"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_settings
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "Productes"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "Productes venuts recentment amb"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr "Productes venuts recentment amb el producte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "Vistes de productes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Provide customers with product-specific links or downloadable content in the"
" confirmation page of the checkout process if the payment gets through. To "
"do so, attach some files to a product using the new Files button and publish"
" them."
msgstr ""
"Proporciona als clients enllaços específics per a productes o contingut "
"descarregable a la pàgina de confirmació del procés de pagament si el "
"pagament arriba a través. Per a fer-ho, adjunteu alguns fitxers a un "
"producte utilitzant el botó Fitxers nous i publiqueu-los."

#. module: website_sale
#: code:addons/website_sale/models/product_image.py:0
#, python-format
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr ""
"L'URL de vídeo proporcionat per a «%s» no és vàlid. Introduïu un URL de "
"vídeo vàlid."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
msgid "Published"
msgstr "Publicat"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "Moure cap avall"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "Empènyer cap avall"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "Empènyer cap amunt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "Moure cap amunt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Qty:"
msgstr "Qtat:"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#, python-format
msgid "Quantity"
msgstr "Quantitat"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "REVENUE BY"
msgstr "INGRESSOS PER"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Rating"
msgstr "Valoració"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Rating Average"
msgstr "Valoració mitjana"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Valoració darrers comentaris"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "Darrera imatge de valoració"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "Darrer valor de valoració"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "Comptador de valoracions"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__rating_last_feedback
msgid "Reason of the rating"
msgstr "Raó de la valoració"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "Productes venuts recentment"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "Productes vistos fa poc"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "S'ha enviat un correu electrònic de recuperació"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "Correu electrònic de recuperació per enviar"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Reinforced for heavy loads"
msgstr "Reforçat per càrregues pesades"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "Eliminar de la cistella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Remove one"
msgstr "Eliminar un"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_reporting
msgid "Reporting"
msgstr "Informes"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir les publicacions en aquest portal web."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Review Order"
msgstr "Revisar la comanda"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Ribbon"
msgstr "Cinta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Ribbon background color"
msgstr "Color de fons de la cinta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html_class
msgid "Ribbon class"
msgstr "Classe de cinta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html
msgid "Ribbon html"
msgstr "Cinta HTML"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Ribbon text color"
msgstr "Color del text de la cinta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "Dreta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimització SEO"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sale_ribbon
msgid "Sale"
msgstr "Venda"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "Anàlisi de venda"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#, python-format
msgid "Sales"
msgstr "Vendes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "Anàlisi de vendes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Informe d'anàlisi de vendes"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "Comanda de venda"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línia comanda de venda"

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Sales Order: Cart Recovery Email"
msgstr "Comanda de venda: Mail de recuperació de cistella"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Month"
msgstr "Vendes des del darrer mes"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Week"
msgstr "Vendes des de la setmana passada"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Year"
msgstr "Vendes des de l'últim any"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "Equip de vendes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "Comercial"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "Mostra"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "Cerca comandes de vendes abandonades"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""
"Seleccioni <b>Nou producte</b> per crear-lo i gestionar les seves propietats"
" per accelerar les vendes."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Select this address"
msgstr "Seleccionar aquesta adreça"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "Seleccionable"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell content to download or URL links"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr ""
"Variants de la Venda d'un producte utilitzant atributs (mida, color, etc.)"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr "Envieu un correu electrònic de recuperació de la cistella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "Enviar un correu electrònic de recuperació"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Send a recovery email to visitors who haven't completed their order."
msgstr ""
"Envia un correu electrònic de recuperació als visitants que no han completat"
" la seva comanda."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send a recovery email when a cart is abandoned"
msgstr ""
"Enviar un correu electrònic de recuperació quan s'abandoni una cistella"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "Nom SEO"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Sequence"
msgstr "Seqüència"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "Serveis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Ship to the same address\n"
"                                                    <span class=\"ship_to_other text-muted\" style=\"display: none\">&amp;nbsp;(<i>Your shipping address will be requested later) </i></span>"
msgstr ""
"Envia a la mateixa adreça\n"
"                                                    <span class=\"ship_to_other text-muted\" style=\"display: none\">&amp;nbsp;(<i>La vostra adreça d'enviament es demanarà més endavant) </i></span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping"
msgstr "Enviament"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping Address"
msgstr "Direcció d'enviament"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "Costos d'enviament"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__sale_delivery_settings
msgid "Shipping Management"
msgstr "Gestió d'enviament"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Shop"
msgstr "Botiga"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "Botiga - Comanda"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "Botiga - Confirmada"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Acquirer"
msgstr "Botiga - Seleccioneu el mètode de pagament"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "Cistella de la compra"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty Cart"
msgstr "Mostrar la cistella buida"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid "Show categories"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "Show options"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "Registrar-se"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Sit comfortably"
msgstr "Seu còmodament"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "Mida"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "Mida X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "Mida Y"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Slanted"
msgstr "Inclinada"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sold"
msgstr "Venut"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "Esgotat"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "Alguns camps obligatoris estan buits."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sources"
msgstr "Origens"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "Estat / Província"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_company__website_sale_onboarding_payment_acquirer_state
msgid "State of the website sale onboarding payment acquirer step"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "Estat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_add_on_page
#: model:ir.model.fields,field_description:website_sale.field_website__cart_add_on_page
msgid "Stay on page after adding to cart"
msgstr "Mantenir a la pàgina desprès d'afegir a la cistella"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street 2"
msgstr "Carrer 2"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street <span class=\"d-none d-md-inline\"> and Number</span>"
msgstr "Carrer <span class=\"d-none d-md-inline\"> i número</span>"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Clau publicable de Stripe"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Clau secreta de Stripe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal:"
msgstr "Subtotal:"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""
"Suggereix alternatives al vostre client (estratègia de vendes). Aquests "
"productes apareixen a la pàgina del producte."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested Accessories:"
msgstr "Accessoris suggerits:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "Accessoris suggerits a la cistella de comerç electrònic"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid "VAT"
msgstr "CIF/NIF"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tag"
msgstr "Etiqueta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes:"
msgstr "Impostos:"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website__all_pricelist_ids
msgid "Technical: Used to recompute pricelist_ids"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "Terms and Conditions"
msgstr "Termes i condicions "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "Gràcies per la seva comanda."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "El #1"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The access token is invalid."
msgstr "El testimoni d'accés no és vàlid."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has been updated. Please refresh the page."
msgstr "S'ha actualitzat la cistella. Si us plau, refresqueu la pàgina."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr "URL completa per accedir al document a través del lloc web."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr "La combinació no existeix. Per tant, no pot afegir-se a la cistella."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr "El producte no existeix. Per tant, no pot afegir-se a la cistella."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""
"El mode seleccionat aquí s'aplica com a política de facturació de qualsevol "
"producte nou creat però no de productes ja existents."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The order has been canceled."
msgstr "La comanda ha estat cancel·lada."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Customize and enable 'eCommerce categories' to view all eCommerce "
"categories."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr ""
"El temps per a marcar una cistella com abandonada es pot canviar a la "
"configuració."

#. module: website_sale
#: code:addons/website_sale/models/product_product.py:0
#, python-format
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr ""
"El valor del recompte de la unitat base ha de ser més gran que 0. Utilitzeu "
"0 per amagar el preu per unitat en aquest producte."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "No hi ha cap comanda confirmada del lloc web"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "There is no recent confirmed order."
msgstr "No hi ha cap comanda confirmada recent."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "Encara no hi ha cap comanda no pagada del lloc web"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "There isn't any UTM tag detected in orders"
msgstr "No s'ha detectat cap etiqueta UTM a les comandes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "Aquesta combinació no existeix."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"This email template is suggested by default when you send a recovery email."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "Aquesta es la seva cistella actual."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "Aquest producte no té cap combinació vàlida."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "Aquest producte ja no està disponible."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "Aquest producte no està publicat."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "Aquest codi de promoció no està disponible."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails Position"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "Nombre total de productes visualitzats"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "Nombre total de vistes sobre productes"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "True"
msgstr "Cert"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr ""
"Cert només per als filtres de producte que requereixen un productid perquè "
"es refereixen a la venda creuada"

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr "Visualització del preu UOM per a eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "UPS"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__terms_url
msgid "URL"
msgstr "URL"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "URL d'un vídeo per mostrar el vostre producte."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "USPS"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr ""
"Unitat de mesura del preu per unitat en productes de comerç electrònic."

#. module: website_sale
#: model:product.product,uom_name:website_sale.product_product_1
#: model:product.product,uom_name:website_sale.product_product_1b
#: model:product.template,uom_name:website_sale.product_product_1_product_template
msgid "Units"
msgstr "Unitats"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "Impagat"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
#, python-format
msgid "Unpaid Orders"
msgstr "Comandes no pagades"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "No publicat"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Untaxed Total Sold"
msgstr "Total venut sense impostos"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Upload a file from your local library."
msgstr "Pengeu un fitxer de la biblioteca local."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Validate"
msgstr "Validar"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "URL del vídeo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "View Cart ("
msgstr "Veure cistella ("

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "Visualitza el producte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "Visibilitat"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "Visible"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "Visible al lloc web actual"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "Pàgines visitades"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "Productes visitats"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "Vistes de productes de visitants"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "Historial de vistes de productes de visitants"

#. module: website_sale
#: model:product.product,name:website_sale.product_product_1
#: model:product.product,name:website_sale.product_product_1b
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "Garantia"

#. module: website_sale
#: model:product.product,description_sale:website_sale.product_product_1
#: model:product.product,description_sale:website_sale.product_product_1b
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""
"Garantia, remès al comprador d'un article pel seu fabricant, amb la promesa "
"de reparar o substituir si és necessari en un termini determinat de temps."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_validate.js:0
#, python-format
msgid "We are waiting for confirmation from the bank or the payment provider"
msgstr "Estem esperant la confirmació del banc o del proveïdor de pagaments"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_payment__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Website"
msgstr "Lloc web"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_payment_acquirer_onboarding_wizard
msgid "Website Payment acquire onboarding wizard"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "Categoria del producte al lloc web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "Categoria web publica"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "Seqüència de la pàgina web"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "Botiga del lloc web"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtre de fragments de lloc web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "URL del lloc web"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "Visitant del lloc web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "Meta descripció del lloc web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta paraules del lloc web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "Mata títol del lloc web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imatge del lloc web"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
#: model:ir.model.fields,help:website_sale.field_account_payment__website_id
msgid "Website through which this invoice was created."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "Llocs web"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_crm_team__website_ids
msgid "Websites using this Sales Team"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Whiteboard"
msgstr "Tauler"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "Llista de preferits"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""
"Amb el primer mode podeu establir diversos preus en el formulari de "
"configuració del producte (des de la pestanya Vendes). Amb el segon, "
"establiu preus i regles de càlcul de les llistes de preus."

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid "With this action, '%s' website would not have any pricelist available."
msgstr ""
"Amb aquesta acció, el lloc web '%s' no tindria cap llista de preus "
"disponible."

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "With three feet"
msgstr "Amb tres peus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>billing and shipping</b> addresses at the same time!<br/>\n"
"                                            If you want to modify your shipping address, create a"
msgstr ""
"Esta editant les seves adreces de <b>facturació i enviament</b> alhora.<br/>\n"
"                                            Si voleu modificar la vostra adreça d’enviament, creeu un"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "No teniu cap comanda del lloc web"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "No teniu cap comanda per facturar des del lloc web"

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "Heu deixat productes a la cistella"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"Aquí trobareu totes les cistelles abandonades pels vostres visitants.\n"
"                Si van completar la seva adreça, hauríeu d'enviar-los un correu electrònic de recuperació."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Your Address"
msgstr "La Teva Adreça"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Your Address\n"
"                                        <small> or </small>"
msgstr ""
"La vostra adreça\n"
"                                        <small> o </small>"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "El vostre correu electrònic"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "El vostre nom"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "Your cart is empty!"
msgstr "La teva cistella és buida!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "La vostra cistella anterior ja s'ha completat."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "Codi postal"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "code..."
msgstr "codi..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "p. ex. làmpada,bin"

#. module: website_sale
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
#, python-format
msgid "eCommerce"
msgstr "Comerç electrònic "

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
msgid "eCommerce Categories"
msgstr "Categories comerç electrònic"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter Visibility"
msgstr "Visibilitat del filtre eCommerce"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "Vendes eCommerce"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_delivery
msgid "eCommerce Shipping Costs"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Shop"
msgstr "Botiga d'eCommerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.search_count_box
msgid "found)"
msgstr "trobat)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "hours."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr "Si voleu fusionar la vostra cistella prèvia amb l'actual."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""
"Si voleu restaurar la cistella anterior. La vostra cistella actual serà "
"substituida per la cistella anterior."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "a la categoria \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "item(s))"
msgstr "element(s))"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "nova adreça"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "terms &amp; conditions"
msgstr "termes &amp; condicions"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr "per segir la seva comanda."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Background"
msgstr "⌙ Background"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Mode"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Position"
msgstr "⌙ Posició"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Text"
msgstr ""
