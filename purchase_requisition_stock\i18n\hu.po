# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition_stock
# 
# Translators:
# <PERSON>, 2021
# krnkris, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> Nagy <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON> Nagy <<EMAIL>>, 2021\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition_line__move_dest_id
msgid "Downstream Move"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Minimum készlet szabály"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__picking_type_id
msgid "Operation Type"
msgstr "Művelet típus"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__procurement_group_id
msgid "Procurement Group"
msgstr "Beszerzési csoport"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_order
msgid "Purchase Order"
msgstr "Beszerzési megrendelés"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "Beszerzési kérelem"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition_line
msgid "Purchase Requisition Line"
msgstr "Beszerzési kérelem sor"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_move__requisition_line_ids
msgid "Requisition Line"
msgstr "Kérelem sor"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_move
msgid "Stock Move"
msgstr "Készletmozgás"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Készletszabály"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__warehouse_id
msgid "Warehouse"
msgstr "Raktár"
