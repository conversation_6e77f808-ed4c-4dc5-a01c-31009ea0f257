# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fetchmail_outlook
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-30 10:59+0000\n"
"PO-Revision-Date: 2022-05-17 12:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: fetchmail_outlook
#: model_terms:ir.ui.view,arch_db:fetchmail_outlook.fetchmail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Outlook account"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Verbind je Outlook-account"

#. module: fetchmail_outlook
#: model_terms:ir.ui.view,arch_db:fetchmail_outlook.fetchmail_server_view_form
msgid ""
"<i class=\"fa fa-cog\"/>\n"
"                        Edit Settings"
msgstr ""
"<i class=\"fa fa-cog\"/>\n"
"                        Bewerk instellingen"

#. module: fetchmail_outlook
#: model_terms:ir.ui.view,arch_db:fetchmail_outlook.fetchmail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('use_microsoft_outlook_service', '=', False), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge badge-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('use_microsoft_outlook_service', '=', False), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge badge-success\">\n"
"                        Outlook-token geldig\n"
"                    </span>"

#. module: fetchmail_outlook
#: model:ir.model.fields,field_description:fetchmail_outlook.field_fetchmail_server__microsoft_outlook_uri
msgid "Authentication URI"
msgstr "Authenticatie URL"

#. module: fetchmail_outlook
#: model:ir.model,name:fetchmail_outlook.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "Inkomende mailserver"

#. module: fetchmail_outlook
#: model:ir.model.fields,field_description:fetchmail_outlook.field_fetchmail_server__is_microsoft_outlook_configured
msgid "Is Outlook Credential Configured"
msgstr "Zijn Outlook inloggegevens geconfigureerd"

#. module: fetchmail_outlook
#: model_terms:ir.ui.view,arch_db:fetchmail_outlook.fetchmail_server_view_form
msgid "Outlook"
msgstr "Outlook"

#. module: fetchmail_outlook
#: model:ir.model.fields,field_description:fetchmail_outlook.field_fetchmail_server__microsoft_outlook_access_token
msgid "Outlook Access Token"
msgstr "Outlook toegangstoken"

#. module: fetchmail_outlook
#: model:ir.model.fields,field_description:fetchmail_outlook.field_fetchmail_server__microsoft_outlook_access_token_expiration
msgid "Outlook Access Token Expiration Timestamp"
msgstr "Vervaltijdstempel van Outlook Access Token"

#. module: fetchmail_outlook
#: model:ir.model.fields,field_description:fetchmail_outlook.field_fetchmail_server__use_microsoft_outlook_service
msgid "Outlook Authentication"
msgstr "Outlook authenticatie"

#. module: fetchmail_outlook
#: model:ir.model.fields,field_description:fetchmail_outlook.field_fetchmail_server__microsoft_outlook_refresh_token
msgid "Outlook Refresh Token"
msgstr "Outlook ververs token"

#. module: fetchmail_outlook
#: code:addons/fetchmail_outlook/models/fetchmail_server.py:0
#, python-format
msgid "Outlook mail server %r only supports IMAP server type."
msgstr "Outlook-mailserver %r ondersteunt alleen het IMAP-servertype."

#. module: fetchmail_outlook
#: code:addons/fetchmail_outlook/models/fetchmail_server.py:0
#, python-format
msgid ""
"Please leave the password field empty for Outlook mail server %r. The OAuth "
"process does not require it"
msgstr ""
"Laat het wachtwoordveld leeg voor Outlook mailserver %r. Het OAuth-proces "
"vereist dit niet"

#. module: fetchmail_outlook
#: code:addons/fetchmail_outlook/models/fetchmail_server.py:0
#, python-format
msgid "SSL is required ."
msgstr "SSL is vereist."

#. module: fetchmail_outlook
#: model_terms:ir.ui.view,arch_db:fetchmail_outlook.fetchmail_server_view_form
msgid ""
"Setup your Outlook API credentials in the general settings to link a Outlook"
" account."
msgstr ""
"Stel uw Outlook API-inloggegevens in de algemene instellingen in om een "
"Outlook-account te koppelen."

#. module: fetchmail_outlook
#: model:ir.model.fields,help:fetchmail_outlook.field_fetchmail_server__microsoft_outlook_uri
msgid "The URL to generate the authorization code from Outlook"
msgstr "De URL om de authenticatiecode te genereren van Outlook"
