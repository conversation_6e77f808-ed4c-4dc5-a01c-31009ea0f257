# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_calendar
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-10 11:35+0000\n"
"PO-Revision-Date: 2017-10-10 11:35+0000\n"
"Language-Team: Nepali (https://www.transifex.com/odoo/teams/41243/ne/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ne\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Tutorial"
msgstr ""

#. module: google_calendar
#: model:ir.ui.menu,name:google_calendar.menu_calendar_google_tech_config
msgid "API Credentials"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:89
#, python-format
msgid "Accounts"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:79
#, python-format
msgid ""
"All events have been disconnected from your previous account. You can now "
"restart the synchronization"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:57
#, python-format
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:83
#, python-format
msgid ""
"An error occured while disconnecting events from your previous account. "
"Please retry or contact your administrator."
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Attendee information"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Calendar"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_cal_id
msgid "Calendar ID"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings_cal_client_id
msgid "Client_id"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings_cal_client_secret
msgid "Client_key"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:54
#: code:addons/google_calendar/static/src/js/google_calendar.js:58
#, python-format
msgid "Configuration"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_display_name
msgid "Display Name"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:66
#, python-format
msgid "Do you want to do this now?"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Event"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:84
#, python-format
msgid "Event disconnection error"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:80
#, python-format
msgid "Event disconnection success"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_attendee_google_internal_event_id
msgid "Google Calendar Event Id"
msgstr ""

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
#: model:ir.cron,cron_name:google_calendar.ir_cron_sync_all_cals
#: model:ir.cron,name:google_calendar.ir_cron_sync_all_cals
msgid "Google Calendar: synchronization"
msgstr ""

#. module: google_calendar
#: sql_constraint:calendar.attendee:0
msgid "Google ID should be unique!"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/google_calendar.py:687
#, python-format
msgid ""
"Google is lost... the next synchro will be a full synchro. \n"
"\n"
" %s"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_id
msgid "ID"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:65
#, python-format
msgid ""
"In order to do this, you first need to disconnect all existing events from "
"the old account."
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_users_google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar___last_update
msgid "Last Modified on"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_last_sync_date
msgid "Last synchro date"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_attendee_oe_synchro_date
msgid "Odoo Synchro Date"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event_oe_update_date
msgid "Odoo Update Date"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:46
#, python-format
msgid "Redirection"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_rtoken
msgid "Refresh Token"
msgstr ""

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.action_config_settings_google_calendar
msgid "Settings"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:117
#, python-format
msgid "Sync with <b>Google</b>"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:50
#, python-format
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:64
#, python-format
msgid ""
"The account you are trying to synchronize (%s) is not the same as the last "
"one used (%s)!"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_token_validity
msgid "Token Validity"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings_server_uri
msgid "URI for tuto"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users_google_calendar_token
msgid "User token"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
msgid "Users"
msgstr ""

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:42
#, python-format
msgid "You will be redirected to Google to authorize access to your calendar!"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/google_calendar.py:295
#, python-format
msgid "Your token is invalid or has been revoked !"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar
msgid "google.calendar"
msgstr ""

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "res.config.settings"
msgstr ""
