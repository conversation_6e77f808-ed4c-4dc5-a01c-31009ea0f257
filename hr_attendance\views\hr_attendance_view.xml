<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- views -->

    <record id="view_attendance_tree" model="ir.ui.view">
        <field name="name">hr.attendance.tree</field>
        <field name="model">hr.attendance</field>
        <field name="arch" type="xml">
            <tree string="Employee attendances" edit="0" sample="1">
                <field name="employee_id"/>
                <field name="check_in"/>
                <field name="check_out"/>
                <field name="worked_hours" string="Work Hours" widget="float_time"/>
            </tree>
        </field>
    </record>

    <record id="view_attendance_tree_inherit" model="ir.ui.view">
        <field name="name">hr.attendance.tree.inherit</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.view_attendance_tree"/>
        <field name="groups_id" eval="[(4, ref('hr_attendance.group_hr_attendance_user'))]"/>
        <field name="arch" type="xml">
            <tree position="attributes">
                <attribute name="edit">1</attribute>
                <attribute name="editable">bottom</attribute>
            </tree>
        </field>
    </record>

    <record id="view_hr_attendance_kanban" model="ir.ui.view">
        <field name="name">hr.attendance.kanban</field>
        <field name="model">hr.attendance</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile" sample="1">
                <field name="employee_id"/>
                <field name="check_in"/>
                <field name="check_out"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div>
                                <img t-att-src="kanban_image('hr.employee', 'image_128', record.employee_id.raw_value)" t-att-title="record.employee_id.value" t-att-alt="record.employee_id.value" class="oe_kanban_avatar o_image_24_cover mr4"/>
                                <span class="o_kanban_record_title">
                                    <strong><t t-esc="record.employee_id.value"/></strong>
                                </span>
                            </div>
                            <hr class="mt4 mb8"/>
                            <div class="o_kanban_record_subtitle">
                                <i class="fa fa-calendar" aria-label="Period" role="img" title="Period"></i>
                                <t t-esc="record.check_in.value"/>
                                - <t t-esc="record.check_out.value"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_attendance_view_form" model="ir.ui.view">
        <field name="name">hr.attendance.form</field>
        <field name="model">hr.attendance</field>
        <field name="arch" type="xml">
            <form string="Employee attendances" edit="0">
                <sheet>
                    <group>
                        <field name="employee_id"/>
                        <field name="check_in"/>
                        <field name="check_out"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_attendance_view_form_inherit" model="ir.ui.view">
        <field name="name">hr.attendance.form.inherit</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_form"/>
        <field name="groups_id" eval="[(4, ref('hr_attendance.group_hr_attendance_user'))]"/>
        <field name="arch" type="xml">
            <form position="attributes">
                <attribute name="edit">1</attribute>
            </form>
        </field>
    </record>

    <record id="hr_attendance_view_filter" model="ir.ui.view">
        <field name="name">hr_attendance_view_filter</field>
        <field name="model">hr.attendance</field>
        <field name="arch" type="xml">
            <search string="Hr Attendance Search">
                <field name="employee_id"/>
                <field name="department_id" operator="child_of"/>
                <filter string="My Attendances" name="myattendances" domain="[('employee_id.user_id.id', '=', uid)]" />
                <separator/>
                <filter string="Check In" name="check_in_filter" date="check_in" default_period="last_month"/>
                <filter string="No Check Out" name="nocheckout" domain="[('check_out', '=', False)]" />
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Check In" name="groupby_name" context="{'group_by': 'check_in'}"/>
                    <filter string="Check Out" name="groupby_check_out" context="{'group_by': 'check_out'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="hr_attendance_view_pivot" model="ir.ui.view">
        <field name="name">hr.attendance.pivot</field>
        <field name="model">hr.attendance</field>
        <field name="arch" type="xml">
            <pivot string="Attendance" sample="1">
                <field name="employee_id" type="row"/>
                <field name="check_in" type="col"/>
                <field name="worked_hours" type="measure" widget="float_time"/>
            </pivot>
        </field>
    </record>

    <record id="hr_attendance_view_graph" model="ir.ui.view">
        <field name="name">hr.attendance.graph</field>
        <field name="model">hr.attendance</field>
        <field name="arch" type="xml">
            <graph string="Attendance" sample="1">
                <field name="employee_id"/>
                <field name="check_in"/>
                <field name="worked_hours" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- actions -->

    <record id="hr_attendance_action" model="ir.actions.act_window">
        <field name="name">Attendances</field>
        <field name="res_model">hr.attendance</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="context">{"search_default_today":1}</field>
        <field name="search_view_id" ref="hr_attendance_view_filter" />
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No attendance records found
            </p><p>
                The attendance records of your employees will be displayed here.
            </p>
        </field>
    </record>

    <record id="hr_attendance_action_employee" model="ir.actions.act_window">
        <field name="name">Attendances</field>
        <field name="res_model">hr.attendance</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create': False}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No attendance records to display
            </p><p>
                The attendance records of your employees will be displayed here.
            </p>
        </field>
    </record>

    <record id="hr_attendance_action_graph" model="ir.actions.act_window">
        <field name="name">Attendance Analysis</field>
        <field name="res_model">hr.attendance</field>
        <field name="view_mode">graph,pivot</field>
        <field name="view_id"></field>  <!-- force empty -->
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
    </record>

    <record id="hr_attendance_action_graph_filtered" model="ir.actions.act_window">
        <field name="name">Attendance Analysis</field>
        <field name="res_model">hr.attendance</field>
        <field name="view_mode">graph,pivot</field>
        <field name="context">{
            'search_default_department_id': [active_id],
            'default_department_id': active_id}
        </field>
        <field name="view_id"></field>  <!-- force empty -->
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
    </record>

    <record id="hr_attendance_action_kiosk_mode" model="ir.actions.client">
        <field name="name">Attendances</field>
        <field name="tag">hr_attendance_kiosk_mode</field>
        <field name="target">fullscreen</field>
    </record>

    <record id="hr_attendance_action_my_attendances" model="ir.actions.client">
        <field name="name">Attendance</field>
        <field name="tag">hr_attendance_my_attendances</field>
        <field name="target">main</field>
    </record>

    <record id="hr_attendance_action_greeting_message" model="ir.actions.client">
        <field name="name">Message</field>
        <field name="tag">hr_attendance_greeting_message</field>
    </record>

    <!-- Menus -->

    <menuitem id="menu_hr_attendance_root" name="Attendances" sequence="90" groups="hr_attendance.group_hr_attendance,hr_attendance.group_hr_attendance_kiosk" web_icon="hr_attendance,static/description/icon.png"/>

    <menuitem id="menu_hr_attendance_my_attendances" name="Check In / Check Out" parent="menu_hr_attendance_root" sequence="1" groups="hr_attendance.group_hr_attendance" action="hr_attendance_action_my_attendances"/>

    <menuitem id="menu_hr_attendance_kiosk_no_user_mode" name="Kiosk Mode" parent="menu_hr_attendance_root" sequence="10" groups="hr_attendance.group_hr_attendance_kiosk" action="hr_attendance_action_kiosk_mode"/>

    <menuitem id="menu_hr_attendance_manage_attendances" name="Manager" parent="menu_hr_attendance_root" sequence="20" groups="hr_attendance.group_hr_attendance_user"/>

    <menuitem id="menu_hr_attendance_view_attendances" name="Attendances" parent="menu_hr_attendance_manage_attendances" sequence="10" groups="hr_attendance.group_hr_attendance_user" action="hr_attendance_action"/>

    <menuitem id="menu_hr_attendance_view_employees_kanban" name="Employees" parent="menu_hr_attendance_manage_attendances" sequence="15" groups="hr_attendance.group_hr_attendance_user" action="hr.open_view_employee_list_my"/>

    <menuitem id="menu_hr_attendance_report" name="Reporting" parent="menu_hr_attendance_root" sequence="30" groups="hr_attendance.group_hr_attendance_user" action="hr_attendance_action_graph"/>
</odoo>
