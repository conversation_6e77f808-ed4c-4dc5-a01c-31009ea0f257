# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_iban
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Ha <PERSON>tem <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: Ha Ketem <<EMAIL>>, 2022\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: base_iban
#. openerp-web
#: code:addons/base_iban/static/src/js/iban_widget.js:0
#, python-format
msgid "Account isn't IBAN compliant."
msgstr "החשבון לא תואם ל-IBAN."

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "חשבונות בנק"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr "לא ניתן לחשב את ה- BBAN משום שמספר החשבון אינו IBAN."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "IBAN"
msgstr "IBAN"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""
"ה- IBAN לא נראה נכון. היית צריך להכניס משהו כזה %s\n"
"איפה B = קוד בנק לאומי, S = קוד סניף, C = מספר חשבון, k = בדוק ספרות"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr "ה- IBAN אינו חוקי, הוא אמור להתחיל בקוד הארץ"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "There is no IBAN code."
msgstr "אין קוד IBAN."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr "ה- IBAN הזה אינו עובר את בדיקת האימות, נא אמת אותו."
