<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="pos_config_view_form" model="ir.ui.view">
        <field name="name">pos.config.form.view.inherit.pos.sale.product.configuration</field>
        <field name="model">pos.config</field>
        <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
        <field name="arch" type="xml">
            <div id="iface_big_scrollbars" position="after">
                <div class="col-12 col-lg-6 o_setting_box" id="iface_open_product_info">
                    <div class="o_setting_left_pane">
                        <field name="iface_open_product_info"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="iface_open_product_info"/>
                        <div class="text-muted">
                            Display the 'Product Info' page when a product with optional products are added in the customer cart
                        </div>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>
