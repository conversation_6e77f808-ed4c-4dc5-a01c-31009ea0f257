# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"- Groupe de taxes : la taxe est un ensemble de sous-taxes\n"
"- Fixe : le montant de la taxe est fixe quel que soit le prix\n"
"- Pourcentage du prix : la taxe est proportionnelle au prix :\n"
"      p.ex. : 100 * (1+10%) = 110 (prix exprimé hors taxe)\n"
"                  110 / (1+10%) = 100 (prix exprimé TTC)\n"
"- Pourcentage du prix, taxe comprise : le prix est exprimé taxe comprise\n"
"      p.ex. : 180 / (1 - 10%) = 200\n"
"                 200 * (1 - 10%) = 180"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_applicable
msgid "Applicable Code"
msgstr "Code applicable"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Calcule le montant de la taxe en retournant la variable 'result'.\n"
"\n"
":param base_amount: réel, montant réel sur lequel la taxe est appliquée\n"
":param price_unit: réel\n"
":param quantity: réel\n"
":param company: enregistrement de res.company\n"
":param product: enregistrement de product.product ou None\n"
":param partner: enregistrement de res.partner ou None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Calcule le montant de la taxe en retournant la variable 'result'.\n"
"\n"
":param base_amount: réel, montant réel sur lequel la taxe est appliquée\n"
":param price_unit: réel\n"
":param quantity: réel\n"
":param product: enregistrement de product.product ou None\n"
":param partner: enregistrement de res.partner ou None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Détermine si la taxe sera appliquée en retournant la variable 'result' à True ou False.\n"
"\n"
":param price_unit: réel\n"
":param quantity: réel\n"
":param company: enregistrement de res.company\n"
":param product: enregistrement de product.product ou None\n"
":param partner: enregistrement de res.partner ou None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Détermine si la taxe sera appliquée en retournant la variable \"result\" à True ou False.\n"
"\n"
":param price_unit: réel\n"
":param quantity: réel\n"
":param product: enregistrement de product.product ou None\n"
":param partner: enregistrement de res.partner ou None"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax_template__amount_type__code
msgid "Python Code"
msgstr "Code Python"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Taxe"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Calcul de la taxe"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Modèles pour les taxes"

#. module: account_tax_python
#: code:addons/account_tax_python/models/account_tax.py:0
#: code:addons/account_tax_python/models/account_tax.py:0
#, python-format
msgid ""
"You entered invalid code %r in %r taxes\n"
"\n"
"Error : %s"
msgstr ""
"Vous avez saisi un code invalide %r dans %r taxes\n"
"\n"
"Erreur : %s"
