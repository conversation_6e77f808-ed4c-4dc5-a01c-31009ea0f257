<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- VAT domestic sale-->
    <record id="vyc_tuz_21" model="account.tax.template">
        <field name="chart_template_id" ref="cz_chart_template"/>
        <field name="name">DPH na výstupu 21%</field>
        <field name="description">21%</field>
        <field name="amount">21</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('chart_cz_343221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343221'),
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_vat_21"/>
    </record>

    <record id="vyc_tuz_15" model="account.tax.template">
        <field name="chart_template_id" ref="cz_chart_template"/>
        <field name="name">DPH na výstupu 15%</field>
        <field name="description">15%</field>
        <field name="amount">15</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="0"/>
        <field name="tax_group_id" ref="tax_group_vat_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343215'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343215'),
            }),
        ]"/>
    </record>

    <record id="vyc_tuz_12" model="account.tax.template">
        <field name="chart_template_id" ref="cz_chart_template"/>
        <field name="name">DPH na výstupu 12%</field>
        <field name="description">12%</field>
        <field name="amount">12</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="0"/>
        <field name="tax_group_id" ref="tax_group_vat_12"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343212'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343212'),
            }),
        ]"/>
    </record>

    <record id="vyc_tuz_0" model="account.tax.template">
        <field name="chart_template_id" ref="cz_chart_template"/>
        <field name="name">DPH na výstupu 0%</field>
        <field name="description">0%</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_vat_0"/>
    </record>

    <!-- VAT domestic purchase -->

    <record id="vsc_tuz_21" model="account.tax.template">
        <field name="chart_template_id" ref="cz_chart_template"/>
        <field name="name">DPH na vstupu 21%</field>
        <field name="description">21%</field>
        <field name="amount">21</field>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="0"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_vat_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343121'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343121'),
            }),
        ]"/>
    </record>

    <record id="vsc_tuz_15" model="account.tax.template">
        <field name="chart_template_id" ref="cz_chart_template"/>
        <field name="name">DPH na vstupu 15%</field>
        <field name="description">15%</field>
        <field name="amount">15</field>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="0"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_vat_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343115'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343115'),
            }),
        ]"/>
    </record>

    <record id="vsc_tuz_12" model="account.tax.template">
        <field name="chart_template_id" ref="cz_chart_template"/>
        <field name="name">DPH na vstupu 12%</field>
        <field name="description">12%</field>
        <field name="amount">12</field>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="0"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_vat_12"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343112'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343112'),
            }),
        ]"/>
    </record>


    <!-- European Union -->
    <!-- =========================================================== -->

    <record id="vyc_dod_eu" model="account.tax.template">
        <field name="chart_template_id" ref="cz_chart_template"/>
        <field name="name">Dodání do EU</field>
        <field name="description">0%</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_vat_0"/>
    </record>

    <record id="vsc_nad_eu" model="account.tax.template">
        <field name="chart_template_id" ref="cz_chart_template"/>
        <field name="name">Pořízení z EU</field>
        <field name="description">21%</field>
        <field name="amount">21.0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343121'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343121'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('chart_cz_343221'),
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_vat_21"/>
    </record>
</odoo>
