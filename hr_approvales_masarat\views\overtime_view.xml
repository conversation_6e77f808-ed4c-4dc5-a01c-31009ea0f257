<?xml version="1.0"?>
<odoo>
    <record id="overtime_view_form" model="ir.ui.view">
        <field name="name">hr.masarat.overtime.form</field>
        <field name="model">hr.masarat.overtime</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                    <button string="Manager Approval"
                            attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}"
                            name="make_manager_approval" type="object" class="oe_highlight"/>
                    <button string="Manager Refuse"
                            attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}"
                            name="make_manager_refused" type="object"/>
                    <button string="HR Approval" name="make_hr_approval" type="object" class="oe_highlight"
                            groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <button string="HR Refuse" name="make_hr_refused" type="object"
                            groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <!--                    <button string="Cancel" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','=','draft')]}" name="make_cancel_approval" type="object"/>-->
                    <button string="Cancel" attrs="{'invisible': [('state','=','draft')]}" name="make_cancel_approval"
                            type="object"/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="employee_id" placeholder="Employee"
                                   attrs="{'readonly': [('is_hr_group', '!=', 'yes')]}"/>
                            <field name="is_hr_group" invisible="1"/>
                        </h2>
                    </div>
                    <group>
                        <group string="Request Info">
                            <field name="is_manager" invisible="1"/>
                            <field name="manager_id" options='{"no_open": True}'/>
                            <field name="request_date"/>
                        </group>
                        <group string="Total Overtime Hours">
                            <field name="overtime_total_hours" widget="float_time"/>
                        </group>
                        <group>
                            <field name="Note"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Overtime Date">
                            <field name="overtime_line_ids" attrs="{'readonly': [('state','!=','draft')]}">
                                <tree editable="bottom">
                                    <field name="overtime_type"/>
                                    <!--                                    <field name="overtime_location"/>-->
                                    <field name="start_hour"/>
                                    <field name="end_hour"/>
                                    <field name="overtime_date"/>
                                    <field name="overtime_hours" widget="float_time"/>
                                    <field name="description"/>
                                </tree>
                                <form>
                                    <group>
                                        <group>
                                            <field name="overtime_type"/>
                                            <field name="description"/>
                                        </group>
                                        <group>
                                            <field name="start_hour"/>
                                            <field name="end_hour"/>
                                            <field name="overtime_date" invisible="1"/>
                                            <field name="overtime_hours" widget="float_time"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="overtime_view_tree" model="ir.ui.view">
        <field name="name">hr.masarat.overtime.tree</field>
        <field name="model">hr.masarat.overtime</field>
        <field name="arch" type="xml">
            <tree>
                <field name="employee_id"/>
                <field name="overtime_total_hours" widget="float_time"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="action_overtime_view" model="ir.actions.act_window">
        <field name="name">طلبات إضافي</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.overtime</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem
            id="menu_overtime"
            name="طلبات إضافي"
            parent="hr_masarat_approvals"
            action="action_overtime_view"
            sequence="3"/>
</odoo>

