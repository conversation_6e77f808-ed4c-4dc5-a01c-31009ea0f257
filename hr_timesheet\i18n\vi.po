# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# Vo <PERSON>h Thuy, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "%s Spent"
msgstr "%s đã dùng"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "(%(sign)s%(hours)s:%(minutes)s remaining)"
msgstr "(còn lại %(sign)s%(hours)s:%(minutes)s)"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "(%s days remaining)"
msgstr "(còn lại %s ngày)"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid "<b class=\"tip_title\">Tip: Record your Timesheets faster</b>"
msgstr "<b class=\"tip_title\">Mẹo: Ghi lại bảng chấm công nhanh hơn</b>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for employee:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">Bảng chấm công của nhân "
"viên:</em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for project:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">Bảng chấm công của dự án:</em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for task:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">Bảng chấm công cho nhiệm "
"vụ:</em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets on </em>"
msgstr "<em class=\"font-weight-normal text-muted\">Bảng chấm công </em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Ngày\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o \" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o \" title=\"Giá trị đặt ở đây áp dụng "
"cho công ty cụ thể.\" groups=\"base.group_multi_company\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Giá trị đặt ở đây áp dụng cho"
" công ty cụ thể. \" groups=\"base.group_multi_company\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Đã ghi</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "<span class=\"o_stat_text\">Timesheets</span>"
msgstr "<span class=\"o_stat_text\">Bảng chấm công</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Hours Spent on Sub-tasks:</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Days Spent on Sub-tasks:</span>"
msgstr ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Số giờ dùng cho nhiệm vụ phụ:</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Số ngày dùng cho nhiệm vụ phụ:</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Sub-tasks Hours Spent</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Sub-tasks Days Spent</span>"
msgstr ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Số giờ nhiệm vụ phụ đã dùng</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Số ngày nhiệm vụ phụ đã dùng</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Days)</span>"
msgstr "<span style=\"margin-right: 15px;\">Tổng (Ngày)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Hours)</span>"
msgstr "<span style=\"margin-right: 15px;\">Tổng (Giờ)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
msgid "<span> day(s)</span>"
msgstr "<span> ngày</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
msgid "<span> hour(s)</span>"
msgstr "<span> giờ</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Date</span>"
msgstr "<span>Ngày</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Description</span>"
msgstr "<span>Mô tả</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Project</span>"
msgstr "<span>Dự án</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Responsible</span>"
msgstr "<span>Chịu trách nhiệm</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Task</span>"
msgstr "<span>Nhiệm vụ</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Days recorded:</strong>"
msgstr "<strong>Ngày đã ghi:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<strong>Duration: </strong>"
msgstr "<strong>Thời lượng: </strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Hours recorded:</strong>"
msgstr "<strong>Số giờ đã ghi:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task_planned_hours_template
msgid "<strong>Planned Days:</strong>"
msgstr "<strong>Số ngày dự kiến:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<strong>Progress:</strong>"
msgstr "<strong>Tiến trình:</strong>"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__analytic_account_active
msgid "Active Analytic Account"
msgstr "Tài khoản phân tích đang hoạt động"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_timesheet_manager
msgid "Administrator"
msgstr "Quản trị viên"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Tất cả"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_all
msgid "All Timesheets"
msgstr "Tất cả bảng chấm công"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__allow_timesheets
msgid "Allow timesheets"
msgstr "Cho phép bảng chấm công"

#. module: hr_timesheet
#: code:addons/hr_timesheet/__init__.py:0
#, python-format
msgid "Analysis"
msgstr "Phân tích"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__analytic_account_id
msgid "Analytic Account"
msgstr "Tài khoản phân tích"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Analytic Entry"
msgstr "Bút toán phân tích"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Dòng phân tích"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project is linked for financial management. "
"Use an analytic account to record cost and revenue on your project."
msgstr ""
"Tài khoản phân tích mà dự án này được liên kết để quản lý tài chính. Sử dụng"
" tài khoản phân tích để ghi lại chi phí và doanh thu cho dự án của bạn."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Apple App Store"
msgstr "Apple App Store"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "Application Settings"
msgstr "Cài đặt ứng dụng"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_ids
msgid "Associated Timesheets"
msgstr "Bảng chấm công liên quan"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_synchro
msgid "Awesome Timesheet"
msgstr "Bảng chấm công hiệu quả"

#. module: hr_timesheet
#: model:project.task.type,legend_blocked:hr_timesheet.internal_project_default_stage
msgid "Blocked"
msgstr "Đã chặn"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_activity_analysis
msgid "By Employee"
msgstr "Theo nhân viên"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_project
msgid "By Project"
msgstr "Theo dự án"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_task
msgid "By Task"
msgstr "Theo nhiệm vụ"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "Cộng tác viên dự án đã chia sẻ"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_menu_configuration
msgid "Configuration"
msgstr "Cấu hình"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_task_create_timesheet
msgid "Create Timesheet from task"
msgstr "Tạo bảng chấm công từ nhiệm vụ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Date"
msgstr "Ngày"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Days Spent"
msgstr "Ngày đã dùng"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Days recorded on sub-tasks:"
msgstr "Số ngày ghi lại trong nhiệm vụ phụ:"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr "Giá trị dự án mặc định cho bảng chấm công được tạo từ loại nghỉ phép."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Delete"
msgstr "Xoá"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__department_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Department"
msgstr "Phòng ban"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Describe your activity..."
msgstr "Mô tả hoạt động của bạn..."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__description
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Description"
msgstr "Mô tả"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
#, python-format
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__progress
msgid "Display progress of current task."
msgstr "Hiển thị tiến độ của công việc hiện tại."

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#, python-format
msgid "Download our App"
msgstr "Tải về ứng dụng của chúng tôi"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Duration"
msgstr "Thời lượng"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Duration (Days)"
msgstr "Khoảng thời gian (ngày)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Duration (Hours)"
msgstr "Khoảng thời gian (giờ)"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_effective
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Effective Hours"
msgstr "Giờ hiệu quả"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__employee_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Employee"
msgstr "Nhân viên"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr "Nhắc nhở nhân viên"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__allow_timesheets
msgid "Enable timesheeting on the project."
msgstr "Bật bảng chấm công cho dự án."

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__encode_uom_in_days
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr "Mã hóa đơn vị theo ngày"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid "Encoding Unit"
msgstr "Đơn vị tính giờ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__encoding_uom_id
msgid "Encoding Uom"
msgstr "Đơn vị tính giờ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Generate timesheets upon time off validation"
msgstr "Tạo bảng chấm công khi xác nhận nghỉ phép"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Chrome Store"
msgstr "Google Chrome Store"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Play Store"
msgstr "Google Play Store"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__has_planned_hours_tasks
msgid "Has Planned Hours Tasks"
msgstr "Có số giờ nhiệm vụ dự kiến"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Hours"
msgstr "Giờ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__effective_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Hours Spent"
msgstr "Số giờ đã dùng"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Hours recorded on sub-tasks:"
msgstr "Số giờ ghi lại trong nhiệm vụ phụ:"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__id
msgid "ID"
msgstr "ID"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__reminder_manager_allow
msgid "If checked, send an email to all manager"
msgstr "Nếu chọn, sẽ gửi một email đến tất cả quản lý"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid ""
"If checked, send an email to all users who have not recorded their timesheet"
msgstr ""
"Nếu chọn, sẽ gửi email đến tất cả người dùng chưa ghi lại bảng chấm công của"
" họ"

#. module: hr_timesheet
#: model:project.task.type,legend_normal:hr_timesheet.internal_project_default_stage
msgid "In Progress"
msgstr "Đang tiến hành"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Initially Planned Days"
msgstr "Số ngày dự kiến ban đầu"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Initially Planned Hours"
msgstr "Số giờ dự kiến ban đầu"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#: model:project.task.type,name:hr_timesheet.internal_project_default_stage
#, python-format
msgid "Internal"
msgstr "Nội bộ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__internal_project_id
msgid "Internal Project"
msgstr "Dự án nội bộ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__is_encode_uom_days
msgid "Is Encode Uom Days"
msgstr "Là mã hóa đơn vị theo ngày"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__is_internal_project
msgid "Is Internal Project"
msgstr "Là dự án nội bộ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last month"
msgstr "Tháng trước"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last week"
msgstr "Tuần trước"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last year"
msgstr "Năm ngoái"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid "Log time on tasks"
msgstr "Ghi nhận thời gian trên nhiệm vụ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_manager_allow
msgid "Manager Reminder"
msgstr "Quản lý nhắc nhở"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Meeting"
msgstr "Cuộc họp"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "My Team's Projects"
msgstr "Dự án của đội"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "My Team's Tasks"
msgstr "Nhiệm vụ của đội"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_mine
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_user
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "My Timesheets"
msgstr "Bảng chấm công của tôi"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Mới nhất"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid "No activities found"
msgstr "Không tìm thấy hoạt động"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid "No activities found. Let's start a new one!"
msgstr "Không tìm thấy hoạt động. Hãy tạo hoạt động mới!"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Không"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__overtime
msgid "Overtime"
msgstr "Quá giờ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "Đối tác"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_planned
msgid "Planned Hours"
msgstr "Số giờ dự kiến"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Đơn vị tính của sản phẩm"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__progress
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__progress
#, python-format
msgid "Progress"
msgstr "Tiến trình"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
#, python-format
msgid "Project"
msgstr "Dự án"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid "Project Time Unit"
msgstr "Đơn vị thời gian dự án"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_project
msgid "Project's Timesheets"
msgstr "Bảng chấm công của dự án"

#. module: hr_timesheet
#: model:project.task.type,legend_done:hr_timesheet.internal_project_default_stage
msgid "Ready"
msgstr "Sẵn sàng"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
msgid "Record a new activity"
msgstr "Ghi nhận hoạt động mới"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid ""
"Record your timesheets in an instant by pressing Shift + the corresponding "
"hotkey to add 15min to your projects."
msgstr ""
"Ghi lại bảng chấm công ngay lập tức bằng cách bấm Shift + phím nóng tương "
"ứng để thêm 15 phút vào dự án."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "Recorded"
msgstr "Đã ghi nhận"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Remaining Days"
msgstr "Số ngày còn lại"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Days:"
msgstr "Số ngày còn lại:"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Remaining Hours"
msgstr "Số giờ còn lại"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Hours:"
msgstr "Số giờ còn lại:"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__remaining_hours
msgid "Remaining Invoiced Time"
msgstr "Thời gian xuất hóa đơn còn lại"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reporting"
msgstr "Báo cáo"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Save"
msgstr "Lưu"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Save time"
msgstr "Lưu thời gian"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/xml/qr_modal_template.xml:0
#, python-format
msgid "Scan this QR code to get the Awesome Timesheet app:"
msgstr "Quét mã QR để tải ứng dụng Bảng chấm công hiệu quả:"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Tìm tất cả"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "Tìm kiếm trong mô tả"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Employee"
msgstr "Tìm kiếm trong nhân viên"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "Tìm kiếm trong dự án"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Task"
msgstr "Tìm kiếm trong nhiệm vụ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "See timesheet entries"
msgstr "Xem mục nhập bảng chấm công"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets managers"
msgstr "Gửi email nhắc nhở định kỳ cho quản lý bảng chấm công"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets users"
msgstr "Gửi email nhắc nhở định kỳ cho người dùng bảng chấm công"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.hr_timesheet_config_settings_action
msgid "Settings"
msgstr "Cài đặt"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Sub-tasks Hours Spent"
msgstr "Số giờ nhiệm vụ phụ đã dùng"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Planned Days"
msgstr "Số ngày nhiệm vụ phụ dự kiến"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Planned Hours"
msgstr "Số giờ nhiệm vụ phụ dự kiến"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#, python-format
msgid "Task"
msgstr "Nhiệm vụ"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task_create_timesheet__task_id
msgid "Task for which we are creating a sales order"
msgstr "Nhiệm vụ mà chúng tôi đang tạo đơn bán hàng"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_task
msgid "Task's Timesheets"
msgstr "Bảng chấm công của nhiệm vụ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
msgid "Task:"
msgstr "Nhiệm vụ:"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Phân tích nhiệm vụ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "Tasks in Overtime"
msgstr "Nhiệm vụ trong quá giờ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "The Internal Project of a company should be in that company."
msgstr "Dự án nội bộ của một công ty phải nằm trong công ty đó."

#. module: hr_timesheet
#: model:ir.model.constraint,message:hr_timesheet.constraint_project_task_create_timesheet_time_positive
msgid "The timesheet's time must be positive"
msgstr "Thời gian của bảng chấm công phải là số dương"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "There are no timesheets."
msgstr "Hiện không có bảng chấm công."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"These projects have some timesheet entries referencing them. Before removing"
" these projects, you have to remove these timesheet entries."
msgstr ""
"Những dự án này có mục nhập bảng chấm công liên quan. Trước khi xóa bỏ dự "
"án, bạn phải xóa các mục nhập bảng chấm công này."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"These tasks have some timesheet entries referencing them. Before removing "
"these tasks, you have to remove these timesheet entries."
msgstr ""
"Những nhiệm vụ này có mục nhập bảng chấm công liên quan. Trước khi xóa bỏ "
"nhiệm vụ, bạn phải xóa các mục nhập bảng chấm công này."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This Quarter"
msgstr "Quý này"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This month"
msgstr "Tháng này"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This project has some timesheet entries referencing it. Before removing this"
" project, you have to remove these timesheet entries."
msgstr ""
"Dự án này có mục nhập bảng chấm công liên quan. Trước khi xóa bỏ dự án, bạn "
"phải xóa các mục nhập bảng chấm công này."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This task has some timesheet entries referencing it. Before removing this "
"task, you have to remove these timesheet entries."
msgstr ""
"Nhiệm vụ này có mục nhập bảng chấm công liên quan. Trước khi xóa bỏ nhiệm "
"vụ, bạn phải xóa các mục nhập bảng chấm công này."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This task must be part of a project because there are some timesheets linked"
" to it."
msgstr ""
"Nhiệm vụ này phải là một phần của dự án vì có bảng chấm công liên kết với "
"nhiệm vụ."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This week"
msgstr "Tuần này"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right unit of measure in your employees."
msgstr ""
"Mục này sẽ đặt đơn vị được sử dụng trong dự án và nhiệm vụ.\n"
"Nếu bạn sử dụng bảng chấm công liên kết với dự án, đừng quên cài đặt đúng đơn vị cho nhân viên của bạn."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__timesheet_encode_uom_id
#: model:ir.model.fields,help:hr_timesheet.field_res_company__timesheet_encode_uom_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""
"Việc này sẽ đặt đơn vị tính sử dụng để tính giờ bảng chấm công. Việc này sẽ cung cấp công cụ \n"
"        và hướng dẫn trợ giúp tính giờ. Tất cả báo cáo sẽ vẫn được tính theo giờ (giá trị mặc định)."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This year"
msgstr "Năm nay"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__time_spent
msgid "Time"
msgstr "Thời gian"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Encoding"
msgstr "Tính thời gian"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Off"
msgstr "Nghỉ phép"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Time Spent (Days)"
msgstr "Thời gian dùng (Ngày)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Time Spent (Hours)"
msgstr "Thời gian dùng (giờ)"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr ""
"Thời gian được sử dụng cho nhiệm vụ phụ (và nhiệm vụ phụ của chúng) của "
"nhiệm vụ này."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__effective_hours
msgid "Time spent on this task, excluding its sub-tasks."
msgstr "Thời gian dùng cho nhiệm vụ này, không gồm nhiệm vụ phụ."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr "Thời gian dùng cho nhiệm vụ này, bao gồm cả nhiệm vụ phụ."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time unit used to record your timesheets"
msgstr "Đơn vị thời gian được sử dụng để ghi bảng chấm công"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet"
msgstr "Bảng chấm công"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr "Hoạt động bảng chấm công"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__timesheet_cost
msgid "Timesheet Cost"
msgstr "Chi phí bảng chấm công"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet Costs"
msgstr "Chi phí bảng chấm công"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_count
msgid "Timesheet Count"
msgstr "Đếm bảng chấm công"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "Đơn vị tính bảng chấm công"

#. module: hr_timesheet
#: model:ir.actions.report,name:hr_timesheet.timesheet_report
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_project
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
msgid "Timesheet Entries"
msgstr "Mục nhập bảng chấm công"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Timesheet by Date"
msgstr "Bảng chấm công theo ngày"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#: code:addons/hr_timesheet/models/project.py:0
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_from_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allow_timesheets
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__timesheet_ids
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_layout
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#, python-format
msgid "Timesheets"
msgstr "Bảng chấm công"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Timesheets Control"
msgstr "Quản lý bảng chấm công"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_report
msgid "Timesheets by Employee"
msgstr "Bảng chấm công theo nhân viên"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_project
msgid "Timesheets by Project"
msgstr "Bảng chấm công theo dự án"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_task
msgid "Timesheets by Task"
msgstr "Bảng chấm công theo nhiệm vụ"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__allow_timesheets
msgid "Timesheets can be logged on this task."
msgstr "Bảng chấm công được ghi nhận ở nhiệm vụ này."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets must be created with an active employee."
msgstr ""

#. module: hr_timesheet
#: model:digest.tip,name:hr_timesheet.digest_tip_hr_timesheet_0
msgid "Tip: Record your Timesheets faster"
msgstr "Mẹo: Ghi lại bảng chấm công nhanh hơn"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Today"
msgstr "Hôm nay"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
msgid "Total"
msgstr "Tổng"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Days"
msgstr "Tổng ngày"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__total_hours_spent
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Hours"
msgstr "Tổng giờ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__total_timesheet_time
msgid "Total Timesheet Time"
msgstr "Tổng thời gian bảng chấm công"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__total_timesheet_time
msgid ""
"Total number of time (in the proper UoM) recorded in the project, rounded to"
" the unit."
msgstr ""
"Tổng số thời gian (theo đơn vị phù hợp) được ghi lại trong dự án, được làm "
"tròn theo đơn vị."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__remaining_hours
msgid ""
"Total remaining time, can be re-estimated periodically by the assignee of "
"the task."
msgstr ""
"Tổng thời gian còn lại, có thể được ước tính lại định kỳ bởi người được phân"
" công nhiệm vụ."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "Total:"
msgstr "Tổng:"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Track your time from anywhere, even offline, with our web/mobile apps"
msgstr ""
"Theo dõi thời gian mọi nơi, kể cả khi ngoại tuyến, với ứng dụng web/di động "
"của chúng tôi"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Theo dõi số giờ làm việc theo dự án mỗi ngày và xuất hóa đơn số giờ đó cho "
"khách hàng. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Training"
msgstr "Đào tạo"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__has_planned_hours_tasks
msgid "True if any of the project's task has a set planned hours"
msgstr "Đúng nếu bất kỳ nhiệm vụ nào của dự án đã đặt số giờ dự kiến"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__user_id
msgid "User"
msgstr "Người dùng"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_approver
msgid "User: all timesheets"
msgstr "Người dùng: tất cả bảng chấm công"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_user
msgid "User: own timesheets only"
msgstr "Người dùng: chỉ sở hữu bảng chấm công"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#, python-format
msgid "View App"
msgstr "Xem ứng dụng"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_uom_uom__timesheet_widget
msgid "Widget"
msgstr "Tiện ích"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_uom_uom__timesheet_widget
msgid ""
"Widget used in the webclient when this unit is the one used to encode "
"timesheets."
msgstr ""
"Tiện ích được sử dụng cho trang web khách hàng khi đơn vị này được sử dụng "
"để tính thời gian bảng chấm công."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot access timesheets that are not yours."
msgstr "Bạn không thể truy cập bảng chấm công của người khác."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"You cannot add timesheets to a project linked to an inactive analytic "
"account."
msgstr ""
"Bạn không thể thêm bảng chấm công vào dự án được liên kết với một tài khoản "
"phân tích không hoạt động."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"You cannot add timesheets to a project or a task linked to an inactive "
"analytic account."
msgstr ""
"Bạn không thể sử dụng bảng chấm công hoặc nhiệm vụ được liên kết với một tài"
" khoản phân tích không hoạt động."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"You cannot log timesheets on this project since it is linked to an inactive "
"analytic account. Please change this account, or reactivate the current one "
"to timesheet on the project."
msgstr ""
"Bạn không thể tạo nhật ký bảng chấm công cho dự án này vì nó được liên kết "
"với tài khoản phân tích không hoạt động. Hãy thay đổi tài khoản này, hoặc "
"kích hoạt lại tài khoản hiện tại để tạo bảng chấm công cho dự án."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot set an archived employee to the existing timesheets."
msgstr "Bạn không thể chọn nhân viên đã lưu trữ cho bảng chấm công hiện có."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "You cannot use timesheets without an analytic account."
msgstr "Bạn không thể sử dụng bảng chấm công mà không có tài khoản phân tích."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "days"
msgstr "ngày"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
msgid "for the"
msgstr "cho"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "hours"
msgstr "giờ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "per hour"
msgstr "mỗi giờ"
