<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- The taxes are sorted in the file: Type of tax > Scope of tax > Tax Due

         * PURCHASE
            - Goods (Based on Invoice)
            - Services (Based on Invoice)
            - Services (Based on Payment)
         * SALE
            - Goods (Based on Invoice)
            - Services (Based Payment)
    -->



    <!-- ########### PURCHASE, Goods (Based on Invoice) ########### -->
    <record model="account.tax.template" id="tva_acq_normale">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20%</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="9"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_specifique">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 8,5%</field>
        <field name="description">TVA 8,5%</field>
        <field name="amount" eval="8.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_85"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_intermediaire">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10%</field>
        <field name="description">TVA 10%</field>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_reduite">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5%</field>
        <field name="description">TVA 5,5%</field>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_super_reduite">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1%</field>
        <field name="description">TVA 2,1%</field>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <!-- CARBURANT -->
    <record model="account.tax.template" id="tva_purchase_good_fuel">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% CARBURANT</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="9"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 80,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),

            (0,0, {
                'factor_percent': 20,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 80,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),

            (0,0, {
                'factor_percent': 20,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_purchase_good_fuel_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% CARBURANT TTC</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="9"/>
        <field name="price_include" eval="1"/>
        <field name="include_base_amount" eval="1"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 80,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),

            (0,0, {
                'factor_percent': 20,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 80,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),

            (0,0, {
                'factor_percent': 20,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <!-- TTC -->
    <record model="account.tax.template" id="tva_acq_normale_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% TTC</field>
        <field name="description">TVA 20%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_specifique_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 8,5% TTC</field>
        <field name="description">TVA 8,5%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="8.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="tax_group_id" ref="tax_group_tva_85"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_intermediaire_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10% TTC</field>
        <field name="description">TVA 10%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_reduite_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5% TTC</field>
        <field name="description">TVA 5,5%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_super_reduite_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1% TTC</field>
        <field name="description">TVA 2,1%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <!-- IMMO -->
    <record model="account.tax.template" id="tva_imm_normale">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% IMMO</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_imm_specifique">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 8,5% IMMO</field>
        <field name="description">TVA 8,5%</field>
        <field name="amount" eval="8.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_85"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_imm_intermediaire">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10% IMMO</field>
        <field name="description">TVA 10%</field>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_imm_reduite">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5% IMMO</field>
        <field name="description">TVA 5,5%</field>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_imm_super_reduite">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1% IMMO</field>
        <field name="description">TVA 2,1%</field>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
    </record>

    <!-- IMPORT -->
    <record model="account.tax.template" id="tva_import_outside_eu_20">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% IMPORT</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="11"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I1_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_I1_taxe')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I1_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_I1_taxe')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_import_outside_eu_10">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10% IMPORT</field>
        <field name="description">TVA 10%</field>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="11"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I2_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_I2_taxe')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I2_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_I2_taxe')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_import_outside_eu_8_5">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 8,5% IMPORT</field>
        <field name="description">TVA 8,5%</field>
        <field name="amount" eval="8.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="11"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_85"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I3_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_I3_taxe')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I3_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_I3_base')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_import_outside_eu_5_5">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5% IMPORT</field>
        <field name="description">TVA 5,5%</field>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="11"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I4_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_I4_taxe')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I4_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_I4_taxe')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_import_outside_eu_2_1">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1% IMPORT</field>
        <field name="description">TVA 2,1%</field>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="11"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I5_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_I5_taxe')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A4'), ref('l10n_fr.tax_report_I5_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20'), ref('l10n_fr.tax_report_24')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_I5_taxe')],
                'account_id': ref('pcg_4453'),
            }),
        ]"/>
    </record>

    <!-- INTRACOM -->
    <record model="account.tax.template" id="tva_intra_normale_biens">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% EU M</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
    </record>



    <!-- ########### PURCHASE, Services (Based on Invoice) ########### -->
    <record model="account.tax.template" id="tva_intra_normale_services">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% EU S</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A3'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A3'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_purchase_service_20_import">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% IMPORT</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_B4'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44531'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_B4'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445663'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44531'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_purchase_service_0">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 0% EXO</field>
        <field name="description">TVA 0%</field>
        <field name="amount" eval="0.00"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="tax_exigibility">on_invoice</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44574"/>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),
           (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),
           (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>


    <!-- ########### PURCHASE, Services (Based on Payment) ########### -->
    <record model="account.tax.template" id="tva_acq_encaissement">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20%</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44564" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="include_base_amount" eval="1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_intermediaire_encaissement">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10%</field>
        <field name="description">TVA 10%</field>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44564" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_encaissement_reduite">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5%</field>
        <field name="description">TVA 5,5%</field>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44564" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_encaissement_super_reduite">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1% </field>
        <field name="description">TVA 2,1%</field>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44564" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <!-- TTC -->
    <record model="account.tax.template" id="tva_acq_encaissement_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% TTC</field>
        <field name="description">TVA 20%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44564" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_intermediaire_encaissement_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10% TTC</field>
        <field name="description">TVA 10%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44564" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_encaissement_reduite_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5% TTC</field>
        <field name="description">TVA 5,5%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44564"/>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_acq_encaissement_super_reduite_TTC">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1% TTC</field>
        <field name="description">TVA 2,1%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="active" eval="0"/>
        <field name="cash_basis_transition_account_id" ref="pcg_44564"/>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44566'),
            }),
        ]"/>
    </record>

    <!-- Others -->
    <record model="account.tax.template" id="tva_purchase_imm_normale">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% IMMO</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44564" />
        <field name="tax_scope">service</field>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="include_base_amount" eval="1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_19')],
                'account_id': ref('pcg_44562'),
            }),
        ]"/>
    </record>


    <!-- ########### SALE, Goods (Based on Invoice) ########### -->
    <record model="account.tax.template" id="tva_normale">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20%</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_08_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_08_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intermediaire">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10%</field>
        <field name="description">TVA 10%</field>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="active" eval="0"/>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_9B_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_9B_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_reduite">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5%</field>
        <field name="description">TVA 5,5%</field>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="active" eval="0"/>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_09_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_09_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_specifique">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 8,5%</field>
        <field name="description">TVA 8,5%</field>
        <field name="amount" eval="8.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_85"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_10_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_10_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_10_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_10_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_super_reduite">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1%</field>
        <field name="description">TVA 2,1%</field>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_11_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_11_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <!-- TTC -->
    <record model="account.tax.template" id="tva_normale_ttc">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% TTC</field>
        <field name="description">TVA 20%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="20"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="active" eval="0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intermediaire_ttc">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10% TTC</field>
        <field name="description">TVA 10%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_9B_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_9B_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_specifique_ttc">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 8,5% TTC</field>
        <field name="description">TVA 8,5%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="8.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_85"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_10_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_10_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_10_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_10_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_reduite_ttc">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5% TTC</field>
        <field name="description">TVA 5,5%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_09_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_09_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_super_reduite_ttc">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1% TTC</field>
        <field name="description">TVA 2,1%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_11_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_11_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <!-- Others (EXO, EXPORT, INTRACOM) -->
    <record model="account.tax.template" id="tva_sale_good_0">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 0% EXO</field>
        <field name="description">TVA 0%</field>
        <field name="amount" eval="0.00"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_E2')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_E2')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>  <!-- previous id: tva_0 -->

    <record model="account.tax.template" id="tva_sale_good_export_0">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 0% EXPORT</field>
        <field name="description">TVA 0%</field>
        <field name="amount" eval="0.00"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_E1')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_E1')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>  <!-- previous id: tva_export_0 -->

    <record model="account.tax.template" id="tva_sale_good_intra_0">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 0% EU M</field>
        <field name="description">TVA 0%</field>
        <field name="amount" eval="0.00"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_F2')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_F2')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>  <!-- previous id: tva_intra_0 -->


    <!-- ########### SALE, Services (Based Payment) ########### -->
    <record model="account.tax.template" id="tva_normale_encaissement">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20%</field>
        <field name="description">TVA 20%</field>
        <field name="amount" eval="20.0"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44574"/>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_08_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_08_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intermediaire_encaissement">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10%</field>
        <field name="description">TVA 10%</field>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44574"/>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="include_base_amount" eval="1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_9B_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_9B_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_reduite_encaissement">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5%</field>
        <field name="description">TVA 5,5%</field>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44574" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_09_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_09_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_super_reduite_encaissement">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1%</field>
        <field name="description">TVA 2,1%</field>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44574" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_11_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_11_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <!-- TTC -->
    <record model="account.tax.template" id="tva_normale_encaissement_ttc">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 20% TTC</field>
        <field name="description">TVA 20%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="20"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_445800" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_08_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_08_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intermediaire_encaissement_ttc">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10% TTC</field>
        <field name="description">TVA 10%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_445800" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_9B_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_9B_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_reduite_encaissement_ttc">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5% TTC</field>
        <field name="description">TVA 5,5%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_445800" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_09_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_09_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_super_reduite_encaissement_ttc">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1% TTC</field>
        <field name="description">TVA 2,1%</field>
        <field name="price_include" eval="1"/>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_445800" />
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_11_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A1'), ref('l10n_fr.tax_report_11_base')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44571'),
            }),
        ]"/>
    </record>

    <!-- Others (EXO, EXPORT, INTRACOM) -->
    <record model="account.tax.template" id="tva_sale_service_0">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 0% EXO</field>
        <field name="description">TVA 0%</field>
        <field name="amount" eval="0.00"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44574"/>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_E2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_E2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>  <!-- previous id: tva_0 -->

    <record model="account.tax.template" id="tva_sale_service_export_0">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 0% EXPORT</field>
        <field name="description">TVA 0%</field>
        <field name="amount" eval="0.00"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44574"/>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_E2')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_E2')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>  <!-- previous id: tva_export_0 -->

    <record model="account.tax.template" id="tva_sale_service_intra_0">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 0% EU S</field>
        <field name="description">TVA 0%</field>
        <field name="amount" eval="0.00"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_scope">service</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44574"/>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_E2')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_E2')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>  <!-- previous id: tva_intra_0 -->


    <!-- ACHATS INTRACOMMUNAUTAIRE -->
    <record model="account.tax.template" id="tva_intra_specifique_biens">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 8,5% EU M</field>
        <field name="description">TVA 8,5%</field>
        <field name="amount" eval="8.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_85"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_10_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_10_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_10_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_10_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intra_specifique_services">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 8,5% EU S</field>
        <field name="description">TVA 8,5%</field>
        <field name="amount" eval="8.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_85"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A3'),
                                         ref('l10n_fr.tax_report_10_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_17'),
                                         ref('l10n_fr.tax_report_10_taxe')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A3'),
                                          ref('l10n_fr.tax_report_10_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_17'),
                                          ref('l10n_fr.tax_report_10_taxe')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intra_intermediaire_biens">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10% EU M</field>
        <field name="description">TVA 10%</field>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_9B_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_9B_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intra_intermediaire_services">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 10% EU S</field>
        <field name="description">TVA 10%</field>
        <field name="amount" eval="10.0"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A3'), ref('l10n_fr.tax_report_9B_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A3'), ref('l10n_fr.tax_report_9B_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_9B_taxe')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intra_reduite_biens">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5% EU M</field>
        <field name="description">TVA 5,5%</field>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_09_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_09_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intra_reduite_services">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 5,5% EU S</field>
        <field name="description">TVA 5,5%</field>
        <field name="amount" eval="5.5"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_55"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A3'), ref('l10n_fr.tax_report_09_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A3'), ref('l10n_fr.tax_report_09_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_09_taxe')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intra_super_reduite_biens">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1% EU M</field>
        <field name="description">TVA 2,1%</field>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">consu</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_11_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_B2'), ref('l10n_fr.tax_report_11_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe'), ref('l10n_fr.tax_report_17')],
                'account_id': ref('pcg_4452'),
            }),
        ]"/>
    </record>

    <record model="account.tax.template" id="tva_intra_super_reduite_services">
        <field name="chart_template_id" ref="l10n_fr_pcg_chart_template"/>
        <field name="name">TVA 2,1% EU S</field>
        <field name="description">TVA 2,1%</field>
        <field name="amount" eval="2.1"/>
        <field name="amount_type">percent</field>
        <field name="sequence" eval="10"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_scope">service</field>
        <field name="include_base_amount" eval="1"/>
        <field name="active" eval="0"/>
        <field name="tax_group_id" ref="tax_group_tva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_A3'), ref('l10n_fr.tax_report_11_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_A3'), ref('l10n_fr.tax_report_11_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('l10n_fr.tax_report_20')],
                'account_id': ref('pcg_445662'),
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('l10n_fr.tax_report_11_taxe')],
                'account_id': ref('pcg_44521'),
            }),
        ]"/>
    </record>

</odoo>
