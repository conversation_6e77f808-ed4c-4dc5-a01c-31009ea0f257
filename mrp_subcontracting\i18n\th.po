# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_report_mrp_report_bom_structure
msgid "BOM Structure Report"
msgstr "รายงานโครงสร้าง BOM"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_bom
msgid "Bill of Material"
msgstr "บิลวัสดุ"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__type
msgid "BoM Type"
msgstr "ประเภท BoM"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_product_template_search_view
msgid "Can be Subcontracted"
msgstr "สามารถทำสัญญาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid ""
"Choose a vendor of type subcontractor if you want to subcontract the product"
msgstr ""
"เลือกผู้จำหน่ายประเภทผู้รับเหมาสัญญาช่วง ถ้าคุณต้องการทำสัญญาช่วงสินค้า"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Consumed"
msgstr "บริโภค"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Continue"
msgstr "ต่อไป"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__move_line_raw_ids
msgid "Detail Component"
msgstr "รายละเอียดส่วนประกอบ"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Discard"
msgstr "ละทิ้ง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__display_action_record_components
msgid "Display Action Record Components"
msgstr "แสดงการดำเนินการบันทึกส่วนประกอบ"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__facultative
msgid "Facultative"
msgstr "เกี่ยวกับสิทธิ์"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__subcontracting_has_been_recorded
msgid "Has been recorded?"
msgstr "ได้รับการบันทึกแล้ว?"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__hide
msgid "Hide"
msgstr "ซ่อน"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_quant__is_subcontract
msgid "Is Subcontract"
msgstr "เป็นสัญญาช่วง"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "ทำตามคำสั่ง"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move_line.py:0
#, python-format
msgid ""
"Make sure you validate or adapt the related resupply picking to your "
"subcontractor in order to avoid inconsistencies in your stock."
msgstr ""
"มั่นใจว่าคุณได้ตรวจสอบหรือปรับเปลี่ยนการเลือกวัสดุเพิ่มเติมที่เกี่ยวข้องกับผู้รับเหมาช่วงของคุณ"
" เพื่อหลีกเลี่ยงความไม่สอดคล้องกันในสต๊อกของคุณ"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__mandatory
msgid "Mandatory"
msgstr "จำเป็น"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
#, python-format
msgid "Nothing to record"
msgstr "ไม่มีอะไรให้บันทึก"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_quant.py:0
#, python-format
msgid "Operation not supported"
msgstr "ไม่รองรับการทำงาน"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_product
msgid "Product"
msgstr "สินค้า"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "ย้ายสินค้า ( ไลน์เคลื่อนย้ายสต๊อก )"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_production
msgid "Production Order"
msgstr "คำสั่งผลิต"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_quant
msgid "Quants"
msgstr "วิเคราะห์เชิงปริมาณ"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Raw Materials for %s"
msgstr "วัตถุดิบสำหรับ%s"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Record Production"
msgstr "บันทึกการผลิต"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Record components"
msgstr "บันทึกส่วนประกอบ"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Register components for subcontracted product"
msgstr "ลงทะเบียนส่วนประกอบสำหรับสินค้ารับเหมาช่วง"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Reserved"
msgstr "สำรองแล้ว"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_route_id
#, python-format
msgid "Resupply Subcontractor"
msgstr "การจัดหาผู้รับเหมาช่วงใหม่"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:stock.location.route,name:mrp_subcontracting.route_resupply_subcontractor_mto
#, python-format
msgid "Resupply Subcontractor on Order"
msgstr "การจัดหาผู้รับเหมาช่วงใหม่บนคำสั่ง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply Subcontractors"
msgstr "การจัดหาผู้รับเหมาช่วงใหม่"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply subcontractors with components"
msgstr "การจัดหาผู้รับเหมาช่วงใหม่ด้วยส่วนประกอบ"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_return_picking
msgid "Return Picking"
msgstr "การรับคืน"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Sequence Resupply Subcontractor"
msgstr "ลำดับการจัดหาผู้รับเหมาช่วงใหม่"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Sequence subcontracting"
msgstr "ลำดับสัญญาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__show_subcontracting_details_visible
msgid "Show Subcontracting Details Visible"
msgstr "แสดงรายละเอียดการรับเหมาช่วงที่มองเห็นได้"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move
msgid "Stock Move"
msgstr "ย้ายสต๊อก"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_rule
msgid "Stock Rule"
msgstr "กฎสต๊อก"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Subcontract"
msgstr "สัญญาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking__subcontract_location_id
msgid "Subcontract Location"
msgstr "ตำแหน่งสัญญาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid "Subcontracted"
msgstr "สัญญาช่วง"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__mrp_bom__type__subcontract
#, python-format
msgid "Subcontracting"
msgstr "สัญญาช่วง"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/res_company.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__subcontracting_location_id
#, python-format
msgid "Subcontracting Location"
msgstr "ตำแหน่งสัญญาช่วง"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.quant_subcontracting_search_view
msgid "Subcontracting Locations"
msgstr "ตำแหน่งสัญญาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_mto_pull_id
msgid "Subcontracting MTO Rule"
msgstr "กฎ MTO สัญญาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_pull_id
msgid "Subcontracting MTS Rule"
msgstr "กฎ MTS สัญญาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_type_id
msgid "Subcontracting Operation Type"
msgstr "ประเภทการปฏิบัติสัญญาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_resupply_type_id
msgid "Subcontracting Resupply Operation Type"
msgstr "ประเภทการปฏิบัติการจัดหาสัญญาช่วง"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.report_mrp_bom_line_inherit_mrp_subcontracting
msgid "Subcontracting:"
msgstr "สัญญารับเหมาช่วง:"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Subcontracting: "
msgstr "สัญญารับเหมาช่วง: "

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__is_subcontractor
msgid "Subcontractor"
msgstr "ผู้รับเหมาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid "Subcontractor Location"
msgstr "ตำแหน่งผู้รับเหมาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__subcontractor_ids
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.view_partner_mrp_subcontracting_filter
msgid "Subcontractors"
msgstr "ผู้รับเหมาช่วง"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "รายการราคาซัพพลายเออร์"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__is_subcontract
msgid "The move is a subcontract receipt"
msgstr "การย้ายคือใบเสร็จรับเงินสัญญาช่วง"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,help:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid ""
"The stock location used as source and destination when sending        goods "
"to this contact during a subcontracting process."
msgstr ""
"ตำแหน่งสต๊อกที่ใช้เป็นแหล่งที่มาและปลายทางเมื่อส่งสินค้าไปยังผู้ติดต่อรายนี้ในระหว่างกระบวนการรับเหมาช่วง"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "This MO isn't related to a subcontracted move"
msgstr "MO นี้ไม่เกี่ยวข้องกับการย้ายผู้รับเหมาช่วง"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "To subcontract, use a planned transfer."
msgstr "ในการทำสัญญาช่วง ใช้การโอนที่วางแผนไว้"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_form_view
msgid "Total Consumed"
msgstr "การบริโภครวม"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_picking
msgid "Transfer"
msgstr "โอน"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_warehouse
msgid "Warehouse"
msgstr "คลังสินค้า"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""
"ตัวช่วยสร้างในกรณีการบริโภคในการเตือน / จำกัดและส่วนประกอบอื่น ๆ "
"ถูกนำมาใช้สำหรับ MO (ที่เกี่ยวข้องกับ bom)"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not set a Bill of Material with operations or by-product line as "
"subcontracting."
msgstr ""
"คุณไม่สามารถกำหนดบิลวัสดุที่มีการปฏิบัติการหรือไลน์ผลิตภัณฑ์พลอยได้เป็นสัญญาช่วงได้"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for %s"
msgstr "คุณต้องป้อนหมายเลขซีเรียลสำหรับ %s"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for each line of %s"
msgstr "คุณต้องป้อนหมายเลขซีเรียลสำหรับแต่ล่ะไลน์ของ %s"

#. module: mrp_subcontracting
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid ""
"You must indicate a non-zero amount consumed for at least one of your "
"components"
msgstr ""
"คุณต้องระบุจำนวนที่ไม่เป็นศูนย์สำหรับส่วนประกอบของคุณอย่างน้อยหนึ่งรายการ"
