<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tax_report" model="account.tax.report">
        <field name="name">Tax Report</field>
        <field name="country_id" ref="base.au"/>
    </record>

    <record id="account_tax_report_gstrpt_sale_total" model="account.tax.report.line">
        <field name="name">GST amounts you owe the Tax Office from sales</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="formula">None</field>
    </record>

    <record id="account_tax_report_gstrpt_g1" model="account.tax.report.line">
        <field name="name">G1: Total Sales (including any GST)</field>
        <field name="tag_name">G1</field>
        <field name="code">G1</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_sale_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g2" model="account.tax.report.line">
        <field name="name">G2: Export sales</field>
        <field name="tag_name">G2</field>
        <field name="code">G2</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_g1"/>
        </record>

    <record id="account_tax_report_gstrpt_g3" model="account.tax.report.line">
        <field name="name">G3: Other GST-free sales</field>
        <field name="tag_name">G3</field>
        <field name="code">G3</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_g1"/>
        </record>

    <record id="account_tax_report_gstrpt_g4" model="account.tax.report.line">
        <field name="name">G4: Input taxed sales</field>
        <field name="tag_name">G4</field>
        <field name="code">G4</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="5"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_g1"/>
        </record>

    <record id="account_tax_report_gstrpt_g5" model="account.tax.report.line">
        <field name="name">G5: G2 + G3 + G4</field>
        <field name="code">G5</field>
        <field name="formula">G2+G3+G4</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="6"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_sale_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g6" model="account.tax.report.line">
        <field name="name">G6: Total sales subject to GST (G1 minus G5)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="7"/>
        <field name="code">G6</field>
        <field name="formula">G1-G5</field>
        <field name="parent_id" ref="account_tax_report_gstrpt_sale_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g7" model="account.tax.report.line">
        <field name="name">G7: Adjustments (if applicable)</field>
        <field name="tag_name">G7</field>
        <field name="code">G7</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="8"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_g6"/>
    </record>

    <record id="account_tax_report_gstrpt_g8" model="account.tax.report.line">
        <field name="name">G8: Total sales subject to GST after adjustments (G6 + G7)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="9"/>
        <field name="code">G8</field>
        <field name="formula">G6+G7</field>
        <field name="parent_id" ref="account_tax_report_gstrpt_sale_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g9" model="account.tax.report.line">
        <field name="name">G9: GST on sales (G8 divided by eleven)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="code">G9</field>
        <field name="sequence" eval="10"/>
        <field name="formula">G8/11</field>
        <field name="parent_id" ref="account_tax_report_gstrpt_sale_total"/>
    </record>

    <!-- Purchase Report -->
    <record id="account_tax_report_gstrpt_purchase_total" model="account.tax.report.line">
        <field name="name">GST amounts the Tax Office owes you from purchases</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="101"/>
        <field name="formula">None</field>
    </record>

    <record id="account_tax_report_gstrpt_g10" model="account.tax.report.line">
        <field name="name">G10: Capital purchases (including any GST)</field>
        <field name="tag_name">G10</field>
        <field name="code">G10</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="102"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_purchase_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g11" model="account.tax.report.line">
        <field name="name">G11: Non-capital purchases (including GST)</field>
        <field name="tag_name">G11</field>
        <field name="code">G11</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="103"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_purchase_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g12" model="account.tax.report.line">
        <field name="name">G12: G10 + G11</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="104"/>
        <field name="code">G12</field>
        <field name="formula">G10+G11</field>
        <field name="parent_id" ref="account_tax_report_gstrpt_purchase_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g13" model="account.tax.report.line">
        <field name="name">G13: Purchases for making input taxed sales</field>
        <field name="tag_name">G13</field>
        <field name="code">G13</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="105"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_g12"/>
    </record>

    <record id="account_tax_report_gstrpt_g14" model="account.tax.report.line">
        <field name="name">G14: Purchases without GST in the price</field>
        <field name="tag_name">G14</field>
        <field name="code">G14</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="106"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_g12"/>
    </record>

    <record id="account_tax_report_gstrpt_g15" model="account.tax.report.line">
        <field name="name">G15: Estimated purchases for private use or not income tax deductible</field>
        <field name="tag_name">G15</field>
        <field name="code">G15</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="107"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_g12"/>
    </record>

    <record id="account_tax_report_gstrpt_g16" model="account.tax.report.line">
        <field name="name">G16: G13 + G14 + G15</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="108"/>
        <field name="code">G16</field>
        <field name="formula">G13+G14+G15</field>
        <field name="parent_id" ref="account_tax_report_gstrpt_purchase_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g17" model="account.tax.report.line">
        <field name="name">G17: Total purchases subject to GST (G12 minus G16) </field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="109"/>
        <field name="code">G17</field>
        <field name="formula">G12-G16</field>
        <field name="parent_id" ref="account_tax_report_gstrpt_purchase_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g18" model="account.tax.report.line">
        <field name="name">G18: Adjustments (if applicable)</field>
        <field name="tag_name">G18</field>
        <field name="code">G18</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="110"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_g17"/>
    </record>

    <record id="account_tax_report_gstrpt_g19" model="account.tax.report.line">
        <field name="name">G19: Total purchases subject to GST after adjustments (G17 + G18) </field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="111"/>
        <field name="code">G19</field>
        <field name="formula">G17+G18</field>
        <field name="parent_id" ref="account_tax_report_gstrpt_purchase_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g20a" model="account.tax.report.line">
        <field name="name">GST on purchases (G19 divided by eleven)</field>
        <field name="code">GP</field>
        <field name="formula">G19/11</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="112"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_purchase_total"/>
    </record>

    <record id="account_tax_report_gstrpt_gstonly" model="account.tax.report.line">
        <field name="name">GST only purchases</field>
        <field name="tag_name">ONLY</field>
        <field name="code">ONLY</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="113"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_purchase_total"/>
    </record>

    <record id="account_tax_report_gstrpt_g20b" model="account.tax.report.line">
        <field name="name">G20: GST on purchases</field>
        <field name="formula">GP+ONLY</field>
        <field name="report_id" ref="tax_report"/>
        <field name="code">G20</field>
        <field name="sequence" eval="114"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_purchase_total"/>
    </record>


    <!-- Summary -->
    <record id="account_tax_report_gstrpt_summary" model="account.tax.report.line">
        <field name="name">Summary</field>
        <field name="sequence" eval="201"/>
        <field name="formula">None</field>
        <field name="report_id" ref="tax_report"/>
    </record>

    <record id="account_tax_report_gstrpt_summary_1a" model="account.tax.report.line">
        <field name="name">1A: GST on sales</field>
        <field name="code">1A</field>
        <field name="sequence" eval="201"/>
        <field name="formula">G9</field>
        <field name="parent_id" eval="account_tax_report_gstrpt_summary"/>
        <field name="report_id" ref="tax_report"/>
    </record>

    <record id="account_tax_report_gstrpt_summary_1b" model="account.tax.report.line">
        <field name="name">1B: GST on purchases</field>
        <field name="code">1B</field>
        <field name="sequence" eval="202"/>
        <field name="formula">G20</field>
        <field name="parent_id" eval="account_tax_report_gstrpt_summary"/>
        <field name="report_id" ref="tax_report"/>
    </record>

    <record id="account_tax_report_gstrpt_summary_9" model="account.tax.report.line">
        <field name="name">9: Your payment</field>
        <field name="sequence" eval="203"/>
        <field name="formula">1A-1B</field>
        <field name="parent_id" eval="account_tax_report_gstrpt_summary"/>
        <field name="report_id" ref="tax_report"/>
    </record>


    <!-- Comparison -->
    <record id="account_tax_report_gstrpt_comparison" model="account.tax.report.line">
        <field name="name">Comparison</field>
        <field name="report_id" ref="tax_report"/>
        <field name="tag_name" eval="False"/>
        <field name="sequence" eval="301"/>
        <field name="parent_id" eval="False"/>
        <field name="report_id" ref="tax_report"/>
    </record>

    <record id="account_tax_report_gstrpt_comparison" model="account.tax.report.line">
        <field name="formula">None</field>
    </record>

    <record id="account_tax_report_gstrpt_comparison_worksheet" model="account.tax.report.line">
        <field name="name">GST from worksheet (G20-G9)</field>
        <field name="formula">G20-G9</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="202"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_comparison"/>
    </record>

    <record id="account_tax_report_gstrpt_comparison_gl" model="account.tax.report.line">
        <field name="name">GST from General Ledger</field>
        <field name="tag_name">GST from General Ledger</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="203"/>
        <field name="parent_id" ref="account_tax_report_gstrpt_comparison"/>
    </record>

</odoo>
