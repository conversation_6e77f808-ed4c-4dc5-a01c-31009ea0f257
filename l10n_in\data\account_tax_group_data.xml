<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="sgst_group" model="account.tax.group">
            <field name="name">SGST</field>
            <field name="country_id" ref="base.in"/>
        </record>
        <record id="cgst_group" model="account.tax.group">
            <field name="name">CGST</field>
            <field name="country_id" ref="base.in"/>
        </record>
        <record id="igst_group" model="account.tax.group">
            <field name="name">IGST</field>
            <field name="country_id" ref="base.in"/>
        </record>
        <record id="cess_group" model="account.tax.group">
            <field name="name">CESS</field>
            <field name="country_id" ref="base.in"/>
        </record>
        <record id="gst_group" model="account.tax.group">
            <field name="name">GST</field>
            <field name="country_id" ref="base.in"/>
        </record>
        <record id="exempt_group" model="account.tax.group">
            <field name="name">Exempt</field>
            <field name="country_id" ref="base.in"/>
        </record>
        <record id="nil_rated_group" model="account.tax.group">
            <field name="name">Nil Rated</field>
            <field name="country_id" ref="base.in"/>
        </record>
        <record id="non_gst_supplies_group" model="account.tax.group">
            <field name="name">Non GST Supplies</field>
            <field name="country_id" ref="base.in"/>
        </record>
    </data>
</odoo>
