<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <div t-name="TranslationDialog" class="o_translation_dialog">
        <div class="row" t-foreach="widget.data" t-as="term">
            <div t-attf-class="col-12 #{widget.showSrc ? 'col-lg-2' : 'col-lg-3'} #{widget.currentInterfaceLanguage == term.lang ? 'o_language_current' : ''}">
                <t t-esc="term.langName"></t>
            </div>
            <div t-if="widget.showSrc" class="col-12 col-lg-3 source">
                <t t-esc="term.source"></t>
            </div>
            <div t-attf-class="col-12 #{widget.showSrc ? 'col-lg-7' : 'col-lg-9'} translation">
                <input t-if="!widget.isText" type="text"
                       class="o_field_char o_input"
                       t-att-value="term.value"
                       t-att-data-id="term.id"/>

                <textarea t-if="widget.isText"
                          t-esc="term.value"
                          t-att-data-id="term.id"
                          t-att-rows="widget.showSrc ? 2 : 5"
                          class="o_field_text o_field_translate o_field_widget o_input">
                </textarea>
            </div>
        </div>
    </div>
</templates>
