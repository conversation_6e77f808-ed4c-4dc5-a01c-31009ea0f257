# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>-Nessel<PERSON>ch, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-10 14:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Dati prelevati"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Amount:</b>"
msgstr "<b>Importo:</b>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Reference:</b>"
msgstr "<b>Riferimento:</b>"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""
"<h3>Effettuare un pagamento a: </h3><ul><li>Banca: %s</li><li>Numero conto: "
"%s</li><li>Titolare conto: %s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr "<i class=\"fa fa-arrow-circle-right\"/> Ritorna al profilo"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "<i class=\"fa fa-trash\"/> Delete"
msgstr "<i class=\"fa fa-trash\"/> Elimina"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"
msgstr ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">Metodi di pagamento salvati</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "<span><i class=\"fa fa-arrow-right\"/> Get my Stripe keys</span>"
msgstr "<span><i class=\"fa fa-arrow-right\"/> Ottenere i miei Stripe key</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span><i class=\"fa fa-arrow-right\"/> How to configure your PayPal "
"account</span>"
msgstr ""
"<span><i class=\"fa fa-arrow-right\"/> Configura il tuo account "
"PayPal</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span>Start selling directly without an account; an email will be sent by "
"Paypal to create your new account and collect your payments.</span>"
msgstr ""
"<span>Inizia a vendere direttamente; verrà inviata una e-mail da Paypal per "
"creare il nuovo conto e incassare i pagamenti.</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid "<strong>No suitable payment acquirer could be found.</strong>"
msgstr ""
"<strong>Non abbiamo potuto trovare un acquirente di pagamenti "
"adatto.</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Non è stata trovata un'opzione di pagamento adatta.</strong><br/>\n"
"Se credi che si tratti di un errore, contatta l'amministratore del sito."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment acquirer configuration."
msgstr ""
"<strong>Attenzione!</strong> C'è un rimborso in sospeso per questo pagamento.\n"
"Aspetta un attimo che venga elaborato. Se il rimborso è ancora in sospeso tra\n"
"pochi minuti, controlla la configurazione del tuo acquirente di pagamenti."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid ""
"<strong>Warning</strong> Creating a payment acquirer from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>Attenzione</strong> La creazione di un acquirente di pagamenti usando il pulsante <em>CREARE</em> non è supportata.\n"
"                        Usa l'azione <em>Duplicara</em> per favore."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure your are logged in as the right partner "
"before making this payment."
msgstr ""
"<strong>Attenzione</strong> Assicurati di aver effettuato il login come "
"partner giusto prima di effettuare questo pagamento."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>Attenzione</strong> La valuta manca o non è corretta."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>Attenzione</strong> Devi essere connesso per poter pagare."

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction with reference %s already exists."
msgstr "Un'operazione di pagamento con riferimento %s esiste già."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(acq_name)s)."
msgstr ""
"Una richiesta di rimborso di %(amount)s è stata inviata. Il pagamento sarà "
"creato presto. Riferimento della transazione di rimborso: %(ref)s "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "Per creare una nuova transazione di pagamento è richiesto un token."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated (%(acq_name)s)."
msgstr ""
"È stata avviata una transazione con riferimento %(ref)s (%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token_name)s (%(acq_name)s)."
msgstr ""
"Una transazione con riferimento %(ref)s è stata avviata utilizzando il "
"metodo di pagamento %(token_name)s (%(acq_name)s)."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__access_token
msgid "Access Token"
msgstr "Token di accesso"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Account"
msgstr "Conto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Numero conto"

#. module: payment
#: code:addons/payment/models/account_payment_method.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
#, python-format
msgid "Acquirer"
msgstr "Sistema"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_id
msgid "Acquirer Account"
msgstr "Account sistema"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_reference
msgid "Acquirer Reference"
msgstr "Riferimento sistema"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__acquirer_ids
msgid "Acquirers"
msgstr "Sistemi"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Acquirers list"
msgstr "Elenco sistemi"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Activate"
msgstr "Attiva"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Activate Stripe"
msgstr "Attiva Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Attivo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_active
msgid "Add Extra Fees"
msgstr "Aggiungi commissioni supplementari"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "Indirizzo"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "Permetti di salvare metodi di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Amount"
msgstr "Importo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "Importo disponibile per rimborso"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "Importo massimo"

#. module: payment
#. openerp-web
#: code:addons/payment/controllers/portal.py:0
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "An error occurred during the processing of this payment."
msgstr "Si è verificato un errore durante l'elaborazione di questo pagamento."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Apply"
msgstr "Applica"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "In archivio"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Are you sure you want to delete this payment method?"
msgstr "Sei sicuro di voler eliminare questo metodo di pagamento?"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Annullare veramente la transazione autorizzata? Questa azione non può essere"
" revocata."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_authorization
msgid "Authorize Mechanism Supported"
msgstr "Procedura Authorize supportata"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__auth_msg
msgid "Authorize Message"
msgstr "Messaggio di autorizzazione"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "Autorizzata"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Transazioni autorizzate"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Availability"
msgstr "Disponibilità"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Bank"
msgstr "Banca"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Bank Accounts"
msgstr "Conti bancari"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Nome banca"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr "Modello documento di richiamata"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_is_done
msgid "Callback Done"
msgstr "Callback effetuato"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr "Hash di richiamata"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr "Metodo di richiamata"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Record ID"
msgstr "Callback Record ID"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
#, python-format
msgid "Cancel"
msgstr "Annulla"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "Annullata"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__cancel_msg
msgid "Canceled Message"
msgstr "Messaggio annullato"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Cancelled payments"
msgstr "Pagamenti annullati"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__capture_manually
msgid "Capture Amount Manually"
msgstr "Registra importo manualmente"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "Registra transazione"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"Cattura l'importo da Odoo, quando la consegna è completata.\n"
"Usa questo se vuoi addebitare le carte dei tuoi clienti solo quando\n"
"sei sicuro di poter spedire la merce a loro."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Check here"
msgstr "Fai clic"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_open_payment_onboarding_payment_acquirer_wizard
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "Scegliere un metodo di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "Città"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Click here to be redirected to the confirmation page."
msgstr "Fare clic qui per essere reindirizzati alla pagina di conferma."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid "Close"
msgstr "Chiudi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "Codice"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__color
msgid "Color"
msgstr "Colore"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Communication"
msgstr "Comunicazioni"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__company_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "Azienda"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Configuration"
msgstr "Configurazione"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Configure"
msgstr "Configura"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Confirm Deletion"
msgstr "Conferma eliminazione"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "Confermato"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_id
msgid "Corresponding Module"
msgstr "Modulo corrispondente"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__country_ids
msgid "Countries"
msgstr "Nazioni"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "Nazione"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "Crea token"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_acquirer
msgid "Create a new payment acquirer"
msgstr "Crea un nuovo sistema di pagamento"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "Create a new payment token"
msgstr "Crea un nuovo token di pagamento"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_icon
msgid "Create a payment icon"
msgstr "Crea una carta di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Credentials"
msgstr "Credenziali"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_stripe
msgid "Credit & Debit Card"
msgstr "Carta di credito e di debito"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_adyen
msgid "Credit Card (powered by Adyen)"
msgstr "Carta di credito (supportata da Adyen)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_alipay
msgid "Credit Card (powered by Alipay)"
msgstr "Carta di credito (supportata da Alipay)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_authorize
msgid "Credit Card (powered by Authorize)"
msgstr "Carta di credito (supportata da Authorize)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_buckaroo
msgid "Credit Card (powered by Buckaroo)"
msgstr "Carta di credito (supportata da Buckaroo)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_ogone
msgid "Credit Card (powered by Ogone)"
msgstr "Carta di credito (powered by Ogone)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payulatam
msgid "Credit Card (powered by PayU Latam)"
msgstr "Carta di credito (supportata da PayU Latam)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payumoney
msgid "Credit Card (powered by PayUmoney)"
msgstr "Carta di credito (supportata da PayUMoney)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_sips
msgid "Credit Card (powered by Sips)"
msgstr "Carta di credito (supportata da Sips)"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit card (via Stripe)"
msgstr "Carta di credito (via Stripe)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Istruzioni di pagamento personalizzate"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__sequence
msgid "Define the display order"
msgstr "Definisci l'ordine di visualizzazione"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__description
msgid "Description"
msgstr "Descrizione"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__display_as
msgid "Description of the acquirer for customers"
msgstr "Descrizione dell'acquirente per clienti"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Disabled"
msgstr "Disabilitato"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "Dismiss"
msgstr "Ignora"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_icon__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_as
msgid "Displayed as"
msgstr "Visualizzato come"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__done
msgid "Done"
msgstr "Completata"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__done_msg
msgid "Done Message"
msgstr "Messaggio di conclusione"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "Bozza"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Email"
msgstr "E-mail"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Enable credit &amp; debit card payments supported by Stripe"
msgstr ""
"Abilita i pagamenti con carta di credito e di debito supportati da Stripe"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__enabled
msgid "Enabled"
msgstr "Abilitato"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "Errore"

#. module: payment
#. openerp-web
#: code:addons/payment/models/payment_transaction.py:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Error: %s"
msgstr "Errore: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__fees
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Fees"
msgstr "Commissioni"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_fees_computation
msgid "Fees Computation Supported"
msgstr "Calcolo commissioni supportato"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr "Commissioni fisse nazionali"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr "Commissioni fisse internazionali"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__acquirer_id
msgid "Force Payment Acquirer"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_link_wizard__acquirer_id
msgid ""
"Force the customer to pay via the specified payment acquirer. Leave empty to"
" allow the customer to choose among all acquirers."
msgstr ""
"Forza il cliente a pagare tramite l'acquirer di pagamento specificato. "
"Lasciare vuoto per permettere al cliente di scegliere tra tutti gli "
"acquirer."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "From"
msgstr "Da"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__full_only
msgid "Full Only"
msgstr "Solo completo"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "Generazione link di pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Generazione link di pagamento vendite"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "Genera link di pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "Instradamento HTTP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__has_multiple_acquirers
msgid "Has Multiple Acquirers"
msgstr "Ha più Acquirer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "Ha un rimborso in sospeso"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "Il pagamento è stato post-trattato"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pre_msg
msgid "Help Message"
msgstr "Messaggio di aiuto"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__new_user
msgid "I don't have a Paypal account"
msgstr "Non ho un conto PayPal"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__existing_user
msgid "I have a Paypal account"
msgstr "Ho un conto PayPal"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_icon__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "If not defined, the acquirer name will be used."
msgstr "Se non definito, viene usato il nome del sistema."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "If the payment hasn't been confirmed you can contact us."
msgstr "Se il pagamento non viene confermato è possibile contattarci."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr ""
"Se credi che si tratti di un errore, contatta l'amministratore del sito."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image_128
#: model:ir.model.fields,field_description:payment.field_payment_icon__image
msgid "Image"
msgstr "Immagine"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__image_payment_form
msgid "Image displayed on the payment form"
msgstr "Immagine visualizzata nel modulo di pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,help:payment.field_payment_acquirer__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the acquirer."
msgstr ""
"In modalità test, un falso pagamento viene elaborato attraverso un'interfaccia di pagamento di prova.\n"
"Questa modalità è consigliata quando si imposta l'acquirer."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__inline_form_view_id
msgid "Inline Form Template"
msgstr "Modello modulo inline"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Install"
msgstr "Installa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_state
msgid "Installation State"
msgstr "Stato installazione"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Installed"
msgstr "Installato"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Internal server error"
msgstr "Errore interno del server"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "Fattura/e"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "Fatture"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "Conteggio Fatture"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "È stato post-trattato"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "It is currently linked to the following documents:"
msgstr "Attualmente è collegato ai seguenti documenti:"

#. module: payment
#: model:ir.model,name:payment.model_account_journal
msgid "Journal"
msgstr "Registro"

#. module: payment
#: model:ir.model,name:payment.model_account_move
msgid "Journal Entry"
msgstr "Registrazione contabile"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__just_done
msgid "Just done"
msgstr "Appena completato"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "Percorso di arrivo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Lingua"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_icon____last_update
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "Ultima data di modifica dello stato"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Leave empty to allow all acquirers"
msgstr "Lascia vuoto per permettere tutti gli acquirenti"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_meth_link
msgid "Manage payment methods"
msgstr "Gestione metodi di pagamento"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "Manuale"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "Massimo rimborso consentito"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "ID account commerciante"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "Messaggio"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Messages"
msgstr "Messaggi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Metodo"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Multiple payment options selected"
msgstr "Più opzioni di pagamento selezionate"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__name
#: model:ir.model.fields,field_description:payment.field_payment_icon__name
#: model:ir.model.fields,field_description:payment.field_payment_token__name
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Name"
msgstr "Nome"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__provider__none
msgid "No Provider Set"
msgstr "Nessun fornitore impostato"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Acquirer menu."
msgstr ""
"Nessun metodo di pagamento manuale trovato per questa azienda. Crearne uno "
"dal menù del sistema di pagamento."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "No payment has been processed."
msgstr "Non è stato elaborato alcun pagamento."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "No payment option selected"
msgstr "Nessun metodo di pagamento selezionato"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__not_done
msgid "Not done"
msgstr "Non completato"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "Not verified"
msgstr "Non verificato"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from acquirers allowing to capture the amount are "
"available."
msgstr ""
"Si noti che sono disponibili solo i token degli acquirenti che permettono di"
" catturare l'importo."

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"Nota: non sono disponibili i token dei sistemi impostati per autorizzare "
"solo le transazioni (al posto di registrare l'importo)."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Modulo Odoo Enterprise"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "Pagamento offline tramite token"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online Payments"
msgstr "Pagamenti online"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "Pagamento online diretto"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "Pagamento online via token"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "Pagamento online con reindirizzamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online payments enabled"
msgstr "Pagamenti online attivati"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Only administrators can access this data."
msgstr "Dati accessibili solo agli amministratori."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be captured."
msgstr "Solo le transazioni autorizzate possono essere catturate."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be voided."
msgstr "Solo le transazioni autorizzate possono essere annullate."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only confirmed transactions can be refunded."
msgstr "Solo le transazioni confermate possono essere rimborsate."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "Operazione"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "Altro"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr "Altro sistema di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "Token di identità PDT"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__partial
msgid "Partial"
msgstr "Parziale"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "Partner"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "Nome partner"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Pay"
msgstr "Paga"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:payment.acquirer,name:payment.payment_acquirer_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payumoney
msgid "PayUmoney"
msgstr "PayUMoney"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "Pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Acquirer"
msgstr "Sistema di pagamento"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_list
msgid "Payment Acquirers"
msgstr "Sistemi di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__available_acquirer_ids
msgid "Payment Acquirers Available"
msgstr "Acquirenti di pagamento disponibili"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "Importo pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Followup"
msgstr "Controllo pagamenti"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Form"
msgstr "Modulo di pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_icon
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Payment Icon"
msgstr "Carta di pagamento"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_icon
#: model:ir.ui.menu,name:payment.payment_icon_menu
msgid "Payment Icons"
msgstr "Carte di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Istruzioni di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__journal_id
msgid "Payment Journal"
msgstr "Registro pagamenti"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "Link di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Metodo di pagamento"

#. module: payment
#: model:ir.model,name:payment.model_account_payment_method_line
msgid "Payment Methods"
msgstr "Metodi di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__description
msgid "Payment Ref"
msgstr "Rif. pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "Procedura guidata rimborso pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "Token di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "Conteggio token di pagamento"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model:ir.ui.menu,name:payment.payment_token_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "Token di pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__transaction_id
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
msgid "Payment Transaction"
msgstr "Transazione di pagamento"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "Transazioni di pagamento"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "Transazioni di pagamento legate al token"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer_onboarding_wizard
msgid "Payment acquire onboarding wizard"
msgstr "Procedura di attivazione sistema di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__payment_acquirer_selection
msgid "Payment acquirer selected"
msgstr "Acquirente di pagamento selezionato"

#. module: payment
#: model:ir.model,name:payment.model_account_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "Pagamenti"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments failed"
msgstr "Pagamenti non riusciti"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments received"
msgstr "Pagamenti ricevuti"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "Tipologia utente PayPal"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "In sospeso"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pending_msg
msgid "Pending Message"
msgstr "Messaggio di attesa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Telefono"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select a payment option."
msgstr "Usa un metodo di pagamento per favore."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select only one payment option."
msgstr "Seleziona solo un metodo di pagamento per favore."

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set an amount smaller than %s."
msgstr "Impostare un importo inferiore a %s."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "Please switch to company '%s' to make this payment."
msgstr "Passa all'azienda '%s' per effettuare questo pagamento."

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the following transfer details"
msgstr "Usare le seguenti informazioni per il bonifico"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the order name as communication reference."
msgstr "Usare il nome dell'ordine come riferimento per le comunicazioni."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Please wait ..."
msgstr "Attendere..."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "Elaborato da"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment.field_payment_token__provider
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Provider"
msgstr "Fornitore"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Reason:"
msgstr "Motivo:"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Reason: %s"
msgstr "Motivo: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "Reindirizzamento del template del modulo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Reference"
msgstr "Riferimento"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "Il riferimento deve essere univoco."

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
#, python-format
msgid "Refund"
msgstr "Note di credito"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "Importo rimborsi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "Importo rimborsato"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "Note di credito"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__refunds_count
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "Conteggio rimborsi"

#. module: payment
#: model:ir.model,name:payment.model_account_payment_register
msgid "Register Payment"
msgstr "Registra pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "ID documento correlato"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "Modello documento correlato"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "Addebito diretto SEPA"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_account_journal_form
msgid "SETUP"
msgstr "SETUP"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "Save Payment Method"
msgstr "Salva metodo di pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Save my payment details"
msgstr "Salva i miei dettagli di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr "token di pagamento salvato"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "Token di pagamento salvato"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Select countries. Leave empty to use everywhere."
msgstr "Selezionare le nazioni. Lasciare vuoto per usare ovunque."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "Metodo selezionato di attivazione del pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__sequence
#: model:ir.model.fields,field_description:payment.field_payment_icon__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Server Error"
msgstr "Errore del server"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Server error:"
msgstr "Errore del server:"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_allow_tokenization
msgid "Show Allow Tokenization"
msgstr "Mostra consenti tokenizzazione"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_auth_msg
msgid "Show Auth Msg"
msgstr "Mostra msg aut"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_cancel_msg
msgid "Show Cancel Msg"
msgstr "Mostra msg ann."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_credentials_page
msgid "Show Credentials Page"
msgstr "Mostra la pagina delle credenziali"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_done_msg
msgid "Show Done Msg"
msgstr "Mostra msg fatto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_payment_icon_ids
msgid "Show Payment Icon"
msgstr "Mostra l'icona di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pending_msg
msgid "Show Pending Msg"
msgstr "Mostra msg in sospeso"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pre_msg
msgid "Show Pre Msg"
msgstr "Mostra msg prec."

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sips
msgid "Sips"
msgstr "Sips"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "Pagamento di origine"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "Transazione di origine"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "Stato"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_acquirer_onboarding_state
msgid "State of the onboarding payment acquirer step"
msgstr "Stato della fase di attivazione del sistema di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "Stato"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.acquirer,name:payment.payment_acquirer_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Chiave pubblica Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Chiave privata Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "Token di pagamento adatto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_icon_ids
msgid "Supported Payment Icons"
msgstr "Carte di pagamento supportate"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,help:payment.field_account_payment_register__use_electronic_payment_method
msgid "Technical field used to hide or show the payment_token_id if needed."
msgstr ""
"Campo tecnico utilizzato per nascondere o mostrare il payment_toke_id se "
"necessario."

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_test
msgid "Test"
msgstr "Prova"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__test
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.manage
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Test Mode"
msgstr "Modalità test"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__provider
#: model:ir.model.fields,help:payment.field_payment_token__provider
#: model:ir.model.fields,help:payment.field_payment_transaction__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"Il fornitore di servizi di pagamento da utilizzare con questo acquirente"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "The access token is invalid."
msgstr "Il token di accesso non è valido."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__acquirer_ref
msgid "The acquirer reference of the token of the transaction"
msgstr "Il riferimento dell'acquirente del token della transazione"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__acquirer_reference
msgid "The acquirer reference of the transaction"
msgstr "Il riferimento acquirente della transazione"

#. module: payment
#: code:addons/payment/wizards/payment_refund_wizard.py:0
#, python-format
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr ""
"L'importo da rimborsare deve essere positivo e non può essere superiore a "
"%s."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__name
msgid "The anonymized acquirer reference of the payment method"
msgstr "Il riferimento anonimo dell'acquirente del metodo di pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__color
msgid "The color of the card in kanban view"
msgstr "Il colore della carta nella vista kanban"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "Il messaggio informativo complementare sullo stato"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__country_ids
msgid ""
"The countries for which this payment acquirer is available.\n"
"If none is set, it is available for all countries."
msgstr ""
"I paesi per cui questo acquirente di pagamento è disponibile.\n"
"Se non è impostato, è disponibile per tutti i paesi."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__description
msgid "The description shown in the card in kanban view "
msgstr "La descrizione mostrata nella carta nella vista kanban"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__fees
msgid "The fees amount; set by the system as it depends on the acquirer"
msgstr ""
"L'importo delle tasse; impostato dal sistema in quanto dipende dall'acquirer"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The following fields must be filled: %s"
msgstr "I seguenti campi devono essere compilati: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "Il riferimento interno della transazione"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__journal_id
msgid "The journal in which the successful transactions are posted"
msgstr "Il giornale in cui sono registrate le transazioni di successo"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__acquirer_ids
msgid "The list of acquirers supporting this payment icon"
msgstr "L'elenco degli acquirenti che supportano questa icona di pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "Il messaggio mostrato se il pagamento è autorizzato"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__cancel_msg
msgid ""
"The message displayed if the order is canceled during the payment process"
msgstr ""
"Il messaggio visualizzato se l'ordine viene annullato durante il processo di"
" pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr ""
"Il messaggio visualizzato se l'ordine è stato fatto con successo dopo il "
"processo di pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr ""
"Il messaggio visualizzato se l'ordine è in sospeso dopo il processo di "
"pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr ""
"Il messaggio visualizzato per spiegare e aiutare il processo di pagamento"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""
"Il pagamento dovrebbe essere diretto, con reindirizzamento, o fatto con un "
"token."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "The related payment is posted: %s"
msgstr "Il relativo pagamento è stato registrato: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "Il percorso a cui l'utente viene reindirizzato dopo la transazione"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "Il pagamento alla fonte dei relativi pagamenti di rimborso"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of related refund transactions"
msgstr "La transazione di origine delle transazioni di rimborso collegate"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr ""
"Il modello che rende un modulo presentato per reindirizzare l'utente quando "
"effettua un pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr ""
"Il modello che rende il modulo di pagamento inline quando si effettua un "
"pagamento diretto"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(acq_name)s)."
msgstr ""
"La transazione con riferimento %(ref)s per %(amount)s ha incontrato un "
"errore (%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(acq_name)s)."
msgstr ""
"La transazione con riferimento %(ref)s per %(amount)s è stato autorizzato "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(acq_name)s)."
msgstr ""
"La transazione con riferimento %(ref)s per %(amount)s è stato confirmato "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is canceled "
"(%(acq_name)s)."
msgstr ""
"La transazione con riferimento %(ref)s per %(amount)s è annullato "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is pending "
"(%(acq_name)s)."
msgstr ""
"La transazione con riferimento %(ref)sper %(amount)s è in sospeso "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "The value of the payment amount must be positive."
msgstr "Il valore dell'importo di pagamento deve essere positivo."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "Non ci sono transazioni da mostrare"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "Non c'è nulla da pagare."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"acquirer's database, allowing the customer to reuse it for a next purchase."
msgstr ""
"Questo controlla se i clienti possono salvare i loro metodi di pagamento come token di pagamento.\n"
"Un token di pagamento è un link anonimo ai dettagli del metodo di pagamento salvato nel\n"
"database dell'acquirente, permettendo al cliente di riutilizzarlo per il prossimo acquisto."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__image
#: model:ir.model.fields,help:payment.field_payment_icon__image_payment_form
msgid ""
"This field holds the image used for this payment icon, limited to 64x64 px"
msgstr ""
"Questo campo contiene l'immagine utilizzata per questa icona di pagamento, "
"limitata a 64x64 px"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment "
"acquirers. Setting an email for this partner is advised."
msgstr ""
"Il partner non ha un'e-mail, potrebbero esserci dei problemi con alcuni "
"sistemi di pagamento. È consigliato impostare un'e-mail per il partner."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "This payment has not been processed yet."
msgstr "Questo pagamento non è stato ancora elaborato."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has been verified by our system."
msgstr "Questo metodo di pagamento è stato verificato dal nostro sistema."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has not been verified by our system."
msgstr "Questo metodo di pagamento non è stato verificato dal nostro sistema."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "This transaction has been cancelled."
msgstr "La transazione è stata annullata."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_tokenization
msgid "Tokenization Supported"
msgstr "Tokenizzazione supportata"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"Transaction authorization is not supported by the following payment "
"acquirers: %s"
msgstr ""
"L'autorizzazione delle transazioni non è supportata dai seguenti acquirenti "
"di pagamento: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "Transazioni"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__support_refund
msgid "Type of Refund Supported"
msgstr "Tipo di rimborso supportato"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Unable to contact the Odoo server."
msgstr "Impossibile contattare il server Odoo."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Upgrade"
msgstr "Aggiorna"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "Usa metodo di pagamento elettronico"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "Convalida del metodo di pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr "Commissioni variabili nazionali (in percentuale)"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Variable fees must always be positive and below 100%."
msgstr ""
"Le commissioni variabili devono essere sempre positive e inferiori al 100%."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr "Commissioni variabili internazionali (in percentuale)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__verified
msgid "Verified"
msgstr "Verificato"

#. module: payment
#: model:ir.model,name:payment.model_ir_ui_view
msgid "View"
msgstr "Visualizza"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "Annulla transazione"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Waiting for payment"
msgstr "In attesa di pagamento"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Warning!"
msgstr "Attenzione!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to delete your payment method."
msgstr "Non siamo in grado di eliminare il tuo metodo di pagamento."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr "Impossibile trovare il pagamento, ma non c'è da preoccuparsi."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr "Non siamo in grado di elaborare il tuo pagamento."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to save your payment method."
msgstr "Non siamo in grado di salvare il tuo metodo di pagamento."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/post_processing.js:0
#, python-format
msgid "We are processing your payment, please wait ..."
msgstr "Elaborazione del pagamento in corso, attendere ..."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are waiting for the payment acquirer to confirm the payment."
msgstr "In attesa della conferma di pagamento da parte del sistema."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr ""
"Se un token di pagamento deve essere creato durante il post-trattamento "
"della transazione"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__callback_is_done
msgid "Whether the callback has already been executed"
msgstr "Se il callback è già stato effetuato o meno"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_transfer
msgid "Wire Transfer"
msgstr "Bonifico bancario"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You can click here to be redirected to the confirmation page."
msgstr "Fare clic qui per essere reindirizzati alla pagina di conferma."

#. module: payment
#: code:addons/payment/models/account_journal.py:0
#, python-format
msgid ""
"You can't delete a payment method that is linked to an acquirer in the enabled or test state.\n"
"Linked acquirer(s): %s"
msgstr ""
"Non puoi eliminare un metodo di pagamento che è collegato a un acquirente nello stato abilitato o di prova.\n"
"Acquirente collegato: %s"

#. module: payment
#: code:addons/payment/models/ir_ui_view.py:0
#, python-format
msgid "You cannot delete a view that is used by a payment acquirer."
msgstr "Non puoi eliminare una vista usata da un'acquirente di pagamento."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "You do not have access to this payment token."
msgstr "Non hai accesso a questo token di pagamento."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr "Tra pochi minuti arriverà una conferma di pagamento via e-mail."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is confirmed."
msgstr "Dopo la conferma di pagamento verrà inviata una notifica."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is fully confirmed."
msgstr "Dopo la conferma completa di pagamento, verrà inviata una notifica."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order has been processed."
msgstr "L'ordine è stato elaborato."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order is being processed, please wait ..."
msgstr "L'ordine è in corso di elaborazione, attendere..."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been authorized."
msgstr "Il pagamento è stato autorizzato."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been cancelled."
msgstr "Il pagamento è stato annullato."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment has been received but need to be confirmed manually."
msgstr ""
"Il pagamento è stato ricevuto ma è necessario confermarlo manualmente."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_test
#, python-format
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr ""
"Il pagamento è stato elaborato con successo ma è in attesa di approvazione."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been successfully processed. Thank you!"
msgstr "Il pagamento è stato elaborato con successo. Grazie!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment is in pending state."
msgstr "Il pagamento si trova in stato di attesa."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "CAP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "CAP"

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
#: model:ir.cron,cron_name:payment.cron_post_process_payment_tx
#: model:ir.cron,name:payment.cron_post_process_payment_tx
msgid "payment: post-process transactions"
msgstr "pagamento: transazioni post-elaborazione"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show less"
msgstr "mostra di meno"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show more"
msgstr "Mostra di più"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "to choose another payment method."
msgstr "per scegliere un altro sistema di pagamento."
