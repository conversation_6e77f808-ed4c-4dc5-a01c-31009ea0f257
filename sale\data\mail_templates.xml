<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">
    <template id="mail_notification_paynow_online" inherit_id="mail.mail_notification_paynow" name="Quotation: Sign and Pay mail notification template">
        <xpath expr="//t[@t-set='access_name']" position="after">
            <t t-if="record._name == 'sale.order'">
                <t t-set="is_transaction_pending" t-value="record.get_portal_last_transaction().state == 'pending'"/>
                <t t-if="record.has_to_be_signed(include_draft=True)">
                    <t t-if="record.has_to_be_paid()" t-set="access_name">
                        <t t-if="is_transaction_pending">View Quotation</t>
                        <t t-else="">Review, Sign &amp; Pay Quotation</t>
                    </t>
                    <t t-else="" t-set="access_name">Review, Accept &amp; Sign Quotation</t>
                </t>
                <t t-elif="record.has_to_be_paid(include_draft=True) and not is_transaction_pending">
                    <t t-set="access_name">Review, Accept &amp; Pay Quotation</t>
                </t>
                <t t-elif="record.state in ('draft', 'sent')">
                    <t t-set="access_name">View Quotation</t>
                </t>
            </t>
        </xpath>
    </template>
</data></odoo>
