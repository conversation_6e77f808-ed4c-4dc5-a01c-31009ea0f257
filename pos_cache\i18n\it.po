# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_cache
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__cache_ids
msgid "Cache"
msgstr "Cache"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__compute_user_id
msgid "Cache compute user"
msgstr "Utente calcolo cache"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__config_id
msgid "Config"
msgstr "Configurazione"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__id
msgid "ID"
msgstr "ID"

#. module: pos_cache
#: model_terms:ir.ui.view,arch_db:pos_cache.pos_config_view_form_inherit_pos_cache
msgid "Invalidate cache"
msgstr "Invalida cache"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__limit_products_per_request
msgid "Limit Products Per Request"
msgstr "Limitare i prodotti per richiesta"

#. module: pos_cache
#. openerp-web
#: code:addons/pos_cache/static/src/js/pos_cache.js:0
#, python-format
msgid "Loading"
msgstr "Caricamento"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__oldest_cache_time
msgid "Oldest cache time"
msgstr "Orario cache più vecchia"

#. module: pos_cache
#: model:ir.actions.server,name:pos_cache.refresh_pos_cache_cron_ir_actions_server
#: model:ir.cron,cron_name:pos_cache.refresh_pos_cache_cron
#: model:ir.cron,name:pos_cache.refresh_pos_cache_cron
msgid "PoS: refresh cache"
msgstr "POS: aggiorna cache"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_cache
msgid "Point of Sale Cache"
msgstr "Cache per il punto vendita"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configurazione punto vendita"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_domain
msgid "Product Domain"
msgstr "Dominio prodotto"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_fields
msgid "Product Fields"
msgstr "Campi prodotto"
