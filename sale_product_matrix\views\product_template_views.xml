<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="product_template_grid_view_form" model="ir.ui.view">
        <field name="name">product.template.form.inherit.sale.product.matrix</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='variants']" position="inside">
                <group name="product_mode" attrs="{'invisible': [('has_configurable_attributes', '=', False)]}">
                    <group string="Sales Variant Selection">
                        <field name="has_configurable_attributes" invisible="1"/>
                        <field name="product_add_mode" widget="radio" nolabel="1"/>
                    </group>
                </group>
            </xpath>
        </field>
    </record>

    <record id="product_template_view_form" model="ir.ui.view">
        <field name="name">product.template.form.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="sale_product_configurator.product_template_view_form"/>
        <field name="arch" type="xml">
            <field name="optional_product_ids" position="before">
                <field name="product_add_mode" widget="radio" invisible="1"/>
            </field>
            <field name="optional_product_ids" position="attributes">
                <attribute name="attrs">{'invisible': [('product_add_mode', '=', 'grid')]}</attribute>
            </field>
        </field>
    </record>
</odoo>
