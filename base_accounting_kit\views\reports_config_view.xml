<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Account Financial Report -->

        <record id="view_account_financial_report_form" model="ir.ui.view">
            <field name="name">account.financial.report.form</field>
            <field name="model">account.financial.report</field>
            <field name="arch" type="xml">
                <form string="Account Report">
                    <group col="4">
                        <field name="name"/>
                        <field name="parent_id"/>
                        <field name="sequence"/>
                        <field name="type"/>
                        <field name="sign"/>
                        <field name="style_overwrite"/>
                    </group>
                    <notebook
                            attrs="{'invisible': [('type','not in',['accounts','account_type', 'account_report'])]}">
                        <page string="Report">
                            <group>
                                <field name="display_detail"
                                       attrs="{'invisible': [('type','not in',['accounts','account_type'])]}"/>
                                <field name="account_report_id"
                                       attrs="{'invisible': [('type', '!=', 'account_report')]}"/>
                            </group>
                            <field name="account_ids"
                                   attrs="{'invisible': [('type', '!=', 'accounts')]}"/>
                            <field name="account_type_ids"
                                   attrs="{'invisible': [('type', '!=', 'account_type')]}"/>
                        </page>
                    </notebook>
                </form>
            </field>
        </record>

        <record id="view_account_financial_report_tree" model="ir.ui.view">
            <field name="name">account.financial.report.tree</field>
            <field name="model">account.financial.report</field>
            <field name="arch" type="xml">
                <tree string="Account Report">
                    <field name="name"/>
                    <field name="parent_id" invisible="1"/>
                    <field name="type"/>
                    <field name="account_report_id"/>
                </tree>
            </field>
        </record>

        <record id="view_account_financial_report_search" model="ir.ui.view">
            <field name="name">account.financial.report.search</field>
            <field name="model">account.financial.report</field>
            <field name="arch" type="xml">
                <search string="Account Report">
                    <field name="name" string="Account Report"/>
                    <field name="type"/>
                    <field name="account_report_id"/>
                    <group expand="0" string="Group By">
                        <filter string="Parent Report"
                                name="filter_parent_rep"
                                domain=""
                                context="{'group_by':'parent_id'}"/>
                        <filter string="Report Type"
                                name="filter_rep_type"
                                domain="[]"
                                context="{'group_by':'type'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_account_financial_report_tree"
                model="ir.actions.act_window">
            <field name="name">Financial Reports</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">account.financial.report</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id"
                   ref="view_account_financial_report_search"/>
            <field name="view_id" ref="view_account_financial_report_tree"/>
        </record>

        <menuitem id="menu_account_financial_reports_tree"
                  name="Account Reports" parent="account.account_account_menu"
                  action="action_account_financial_report_tree"
        />
    </data>
</odoo>
