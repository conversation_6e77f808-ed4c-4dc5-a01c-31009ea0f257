/**
 * This file regroups the rules which apply on elements which are shared between
 * all renderers. For field default rules, see the fields.scss file.
 */

// Invisible modifier (can be inside the view, the button area, ...)
.o_invisible_modifier {
    display: none!important;
}

// Status
// This should normally be put in fields.scss but these classes are used outside
// of `.o_field_widget` so it needs to be placed at an upper level.
.o_status {
    @extend .rounded-circle;
    background-color: gray('200');
    height: 12px;
    width: 12px;
    box-shadow: inset 0 0 0 1px rgba($black, .2);

    &.o_status_green {
        @extend .bg-success;
    }

    &.o_status_red {
        @extend .bg-danger;
    }

    .dropdown-item > & {
        margin-bottom: .2em;
        transform: translateX(-50%);
    }
}

.o_btn-link-as-button {
    padding: 2px;
    font-size:12px;

    & > a {
        margin-bottom: -4px !important;
        margin-left: 3px;
    }
}

// No content helper
.o_view_nocontent {
    @include o-position-absolute(30%, 0, 0, 0);
    pointer-events: none;
    z-index: 1;

    .o_nocontent_help {
        @include o-nocontent-empty;

        .o_view_nocontent_smiling_face:before {
            @extend %o-nocontent-init-image;
            @include size(120px, 140px);
            background: transparent url(/web/static/img/smiling_face.svg) no-repeat center;
        }

        .o_view_nocontent_neutral_face:before {
            @extend %o-nocontent-init-image;
            @include size(120px, 140px);
            background: transparent url(/web/static/img/neutral_face.svg) no-repeat center;
        }

        .o_view_nocontent_empty_folder:before {
            @extend %o-nocontent-empty-document;
        }

        .o_empty_custom_dashboard {
            min-height: 327px;
            margin-left: -$grid-gutter-width/2;
            margin-top: -$grid-gutter-width/2;
            padding: 100px 0 0 137px;
            background: transparent url(/web/static/img/graph_background.png) no-repeat 0 0;
        }
    }
}

.o_view_sample_data {
    .o_list_table {
        cursor: default !important;

        & > thead .o_list_record_selector {
            pointer-events: none;
        }
    }

    .custom-checkbox {
        pointer-events: none !important;
    }

    .o_nocontent_help {
        border-radius: 20%;
        background-color: #f9f9f9;
        box-shadow: 0 0 120px 100px #f9f9f9;
    }

    .o_sample_data_disabled {
        opacity: 0.33;
        pointer-events: none;
        user-select: none;
    }
}
