# #-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_customer
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# #-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_customer
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-08 10:10+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Bulgarian (http://www.transifex.com/odoo/odoo-9/language/"
"bg/)\n"
"Language: bg\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/> Всички"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_active
msgid "Active"
msgstr "Активен"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:68
#, python-format
msgid "All Countries"
msgstr "Всички Държави"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag_classname
msgid "Bootstrap class to customize the color"
msgstr "Bootstrap class to customize the color"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_name
msgid "Category Name"
msgstr "Име на категория"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_classname
msgid "Class"
msgstr "Клас"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Click to create a new partner tag."
msgstr "Натисни за да създадеш нов партньорски етикет."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_create_uid
msgid "Created by"
msgstr "Създадено от"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_create_date
msgid "Created on"
msgstr "Създадено на"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_id
msgid "ID"
msgstr "ID"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "Implemented By"
msgstr "Имплементирано от"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag___last_update
msgid "Last Modified on"
msgstr "Последно променено на"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_write_uid
msgid "Last Updated by"
msgstr "Последно обновено от"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_write_date
msgid "Last Updated on"
msgstr "Последно обновено на"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage the partner tags to better classify them on your website.\n"
"                    You can add the filter by tag on your website in the "
"\"Customize\" menu."
msgstr ""
"Управлявайте партньорските етикети за да можете по лесно да ги класифицирате "
"и сегментирате на страницата си .\n"
"                    Можете да филтирата по етикет(таг) на вашата страница "
"през меню \"Къстъмизация\"."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No result found"
msgstr "Няма намерени резултати"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
#: model_terms:ir.ui.view,arch_db:website_customer.footer_custom
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our References"
msgstr "Нашите Препоръки"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Partner"
msgstr "Партньор"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "Партньорски Етикет"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ... "
msgstr ""
"Партньорски Етикети - могат да се използват на уебсайта за да се намират "
"клиенти по сектори, сегменти или ..."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_partner_ids
msgid "Partners"
msgstr "Партньори"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
msgid "References"
msgstr "Отпратки"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "References by Country"
msgstr "Препракти по Държави"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "References by Tag"
msgstr "Препракти по Етикети"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "Търсене"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Trusted by millions worldwide"
msgstr "С доверието на милиони по света"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "Етикети на уебсайта"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_website_tag_ids
msgid "Website tags"
msgstr "Етикети на уебсайта"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "Карта на света"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "reference(s))"
msgstr "препратки"

#~ msgid "Partner Tags"
#~ msgstr "Партньорски Етикети"

#~ msgid "Tags"
#~ msgstr "Етикети"

#~ msgid "The full URL to access the document through the website."
#~ msgstr "Пълният URL за достъп до документа през уебсайта."

#~ msgid "Visible in Website"
#~ msgstr "Видим в уеб сайта"

#~ msgid "Website URL"
#~ msgstr "URL на Уебсайта"
