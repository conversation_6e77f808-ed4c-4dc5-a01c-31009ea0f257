# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            قد تضطر إلى تنفيذ إجراءات يدوياً. "

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.sale_order_inherited_form_purchase
msgid "<span class=\"o_stat_text\">Purchase</span>"
msgstr "<span class=\"o_stat_text\">المشتريات</span> "

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.purchase_order_inherited_form_sale
msgid "<span class=\"o_stat_text\">Sale</span>"
msgstr "<span class=\"o_stat_text\">البيع</span> "

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "الاستثناء (الاستثناءات) الحاصلة في أمر (أوامر) الشراء: "

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Exception(s) occurred on the sale order(s):"
msgstr "الاستثناء (الاستثناءات) الحاصلة في أمر (أوامر) البيع: "

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s):"
msgstr "استثناء (الاستثناءات): "

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_ids
msgid "Generated Purchase Lines"
msgstr "بنود الشراء المنشأة"

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_template__service_to_purchase
msgid ""
"If ticked, each time you sell this product through a SO, a RfQ is "
"automatically created to buy the product. Tip: don't forget to set a vendor "
"on the product."
msgstr ""
"إذا كان محدداً، في كل مرة تبيع فيها هذا المنتج من خلال أمر بيع، يتم إنشاء "
"طلب عرض سعر تلقائياً لشراء المنتج. نصيحة: لا تنسَ تعيين مورّد للمنتج. "

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Manual actions may be needed."
msgstr "قد تضطر إلى تنفيذ إجراءات يدوياً. "

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order__purchase_order_count
msgid "Number of Purchase Order Generated"
msgstr "عدد أوامر الشراء المُنشأة "

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order__sale_order_count
msgid "Number of Source Sale"
msgstr "عدد المبيعات المصدرية "

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_count
msgid "Number of generated purchase items"
msgstr "عدد عناصر الشراء المُنشأة "

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid "Ordered quantity decreased!"
msgstr "تم تقليل المكمية المطلوبة! "

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_line_id
msgid "Origin Sale Item"
msgstr "عنصر البيع الأصلي"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_product_template
msgid "Product Template"
msgstr "قالب المنتج"

#. module: sale_purchase
#: model:ir.model.constraint,message:sale_purchase.constraint_product_template_service_to_purchase
msgid "Product that is not a service can not create RFQ."
msgstr "لا يمكن إنشاء طلب عرض سعر لمنتج ما لم يكن خدمة."

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "أمر شراء"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "بند أمر الشراء"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid "Purchase Order generated from %s"
msgstr "أمر شراء تم إنشاؤه من %s "

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_sale_order_line__purchase_line_ids
msgid ""
"Purchase line generated by this Sales item on order confirmation, or when "
"the quantity was increased."
msgstr ""
"بند الشراء الذي أنشأه عنصر المبيعات هذا عند تأكيد الأمر، أو عند زيادة "
"الكمية."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.product_template_form_view_inherit
msgid "Reordering"
msgstr "إعادة الطلب "

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_order_id
msgid "Sale Order"
msgstr "أمر البيع"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_purchase
#: code:addons/sale_purchase/models/purchase_order.py:0
#, python-format
msgid "Sources Sale Orders %s"
msgstr "أوامر البيع المصدرية %s "

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_template__service_to_purchase
msgid "Subcontract Service"
msgstr "التعاقد من الباطن بشأن الخدمة "

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid ""
"There is no vendor associated to the product %s. Please define a vendor for "
"this product."
msgstr "لا يوجد مورّد مرتبط بالمنتج %s. يرجى تحديد مورّد لهذا المنتج. "

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the purchase order if needed."
msgstr ""
"لقد قمت بتقليل الكمية المطلوبة! لا تنس تحديث أمر الشراء يدوياً إن لزم الأمر."
" "

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "cancelled"
msgstr "ملغي"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "of"
msgstr "من"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "ordered instead of"
msgstr "تم طلبه عوضاً عن "
