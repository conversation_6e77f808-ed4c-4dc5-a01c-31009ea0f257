<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form_sale" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.sale.stock.sale</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='ups']" position="after">
                <div class="col-12 col-lg-6 o_setting_box" id="display_incoterms_setting">
                    <div class="o_setting_left_pane">
                        <field name="group_display_incoterm"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="group_display_incoterm"/>
                        <div class="text-muted">
                            Display incoterms on orders &amp; invoices
                        </div>
                        <div class="content-group" attrs="{'invisible': [('group_display_incoterm','=',False)]}">
                            <div class="mt8">
                                <button name="%(account.action_incoterms_tree)d" icon="fa-arrow-right" type="action" string="Incoterms" class="btn-link"/>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="res_config_settings_view_form_stock" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.sale.stock.stock</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="stock.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <div id="warning_info" position="after">
                <div class="col-12 col-lg-6 o_setting_box">
                    <div class="o_setting_right_pane">
                        <label for="default_picking_policy"/>
                        <div class="text-muted">
                            When to start shipping
                        </div>
                        <div class="content-group">
                            <div class="mt16">
                                <field name="default_picking_policy" class="o_light_label" widget="selection"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <xpath expr="//h2[@id='schedule_info']" position="attributes">
                <attribute name="invisible">0</attribute>
            </xpath>
            <div id="sale_security_lead" position="replace">
                <div class="col-12 col-lg-6 o_setting_box" title="Margin of error for dates promised to customers. Products will be scheduled for procurement and delivery that many days earlier than the actual promised date, to cope with unexpected delays in the supply chain.">
                    <div class="o_setting_left_pane">
                        <field name="use_security_lead"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="use_security_lead"/>
                        <a href="https://www.odoo.com/documentation/15.0/applications/inventory_and_mrp/inventory/management/planning/scheduled_dates.html" title="Documentation" class="mr-2 o_doc_link" target="_blank"></a>
                        <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." role="img" aria-label="Values set here are company-specific." groups="base.group_multi_company"/>
                        <div class="text-muted">
                            Schedule deliveries earlier to avoid delays
                        </div>
                        <div class="content-group">
                            <div class="mt16" attrs="{'invisible': [('use_security_lead','=',False)]}">
                                <span>Move forward expected delivery dates by <field name="security_lead" class="oe_inline"/> days</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </field>
    </record>

</odoo>
