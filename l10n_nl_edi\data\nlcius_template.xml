<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <template id="export_nlcius_invoice_partner"
                  inherit_id="account_edi_ubl_bis3.export_bis3_invoice_partner"
                  primary="True">
            <xpath expr="//*[local-name()='CountrySubentity']" position="replace"/>
        </template>

        <template id="export_nlcius_invoice"
                  inherit_id="account_edi_ubl_bis3.export_bis3_invoice"
                  primary="True">
            <xpath expr="//*[local-name()='AccountingSupplierParty']" position="replace">
                <cac:AccountingSupplierParty t-call="l10n_nl_edi.export_nlcius_invoice_partner"
                xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2">
                    <t t-set="partner_vals" t-value="supplier_vals"/>
                </cac:AccountingSupplierParty>
            </xpath>
            <xpath expr="//*[local-name()='AccountingCustomerParty']" position="replace">
                <cac:AccountingCustomerParty t-call="l10n_nl_edi.export_nlcius_invoice_partner"
                xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2">
                    <t t-set="partner_vals" t-value="customer_vals"/>
                </cac:AccountingCustomerParty>
            </xpath>
        </template>

    </data>
</odoo>
