<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="product_label_layout_form_picking" model="ir.ui.view">
        <field name="name">product.label.layout.form</field>
        <field name="model">product.label.layout</field>
        <field name="mode">primary</field>
        <field name="priority">25</field>
        <field name="inherit_id" ref="product.product_label_layout_form"/>
        <field name="arch" type="xml">
            <xpath expr='//field[@name="custom_quantity"]' position="before">
                <field name="picking_quantity"/>
            </xpath>
            <xpath expr='//field[@name="custom_quantity"]' position="attributes">
                <attribute name="attrs">
                    {'invisible': [('picking_quantity', '=', 'picking')]}
                </attribute>
            </xpath>
        </field>
    </record>
</odoo>
