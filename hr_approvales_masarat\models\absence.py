# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, timedelta, date
from odoo.exceptions import ValidationError
from dateutil.relativedelta import relativedelta

class HrEmployeeAbsence(models.Model):
    _name = "hr.masarat.absence"

    _inherit = ['mail.thread', 'mail.activity.mixin']

    _rec_name = 'absence_name'
    absence_name = fields.Char(compute='get_absence_name', store=True)

    state = fields.Selection(selection=[('draft', 'Draft'),
                                        ('manager_approval', 'Manager Approval'),
                                        ('manager_refused', 'Manager Refused'),
                                        ('hr_approval', 'HR Approval'),
                                        ('hr_refused', 'HR Refused')], default='draft', string="State")
    request_date = fields.Date(string="Date of Request", readonly=True, default=lambda self: fields.Date.to_string(date.today()))
    employee_id = fields.Many2one('hr.employee', string="Employee")
    manager_id = fields.Many2one('hr.employee', readonly=True, related='employee_id.parent_id', string="Manager")
    absence_start_at = fields.Date(string='Absence Start Date',required=True)
    absence_end_at = fields.Date(string='Absence End Date',required=True)
    total_absence_days = fields.Integer(string="Total Absence Days",compute="compute_absence", default=0, store=True, compute_sudo=True)
    absence_type = fields.Selection(selection=[('work_outside', 'تكليف بعمل'),
                                               ('emergency', 'ظرف طارئ'),
                                               ('other_reason', 'أسباب أخرى')], string="Absence Type",required=True)
    Note = fields.Text(string="Details and Reasons for Absence")

    is_manager = fields.Char(compute='call_with_sudo_is_manager')
    is_hr_group = fields.Char(compute='call_with_sudo_is_hr_group')

    def get_if_hr_group(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for rec in self:
            if hr_group:
                rec.is_hr_group = 'yes'
            else:
                rec.is_hr_group = 'no'

    def compute_button_visible(self):
        for rec in self:
            if rec.manager_id.user_id.id == self._uid:
                rec.is_manager = '1'
            else:
                rec.is_manager = '0'

    @api.onchange('employee_id')
    def call_with_sudo_is_manager(self):
        self.sudo().compute_button_visible()

    @api.depends('is_hr_group')
    def call_with_sudo_is_hr_group(self):
        self.sudo().get_if_hr_group()

    @api.depends('employee_id', 'absence_start_at')
    def get_absence_name(self):
        for elem in self:
            elem.absence_name = False
            if elem.employee_id and elem.absence_start_at:
                elem.absence_name = elem.employee_id.name + '-Absence Request-' + str(elem.absence_start_at)[:10]

    def make_cancel_approval(self):
        self.state = 'draft'
    def make_manager_approval(self):
        self.state = 'manager_approval'
    def make_manager_refused(self):
        self.state = 'manager_refused'
    def make_hr_approval(self):
        self.state = 'hr_approval'
    def make_hr_refused(self):
        self.state = 'hr_refused'

    @api.model
    def default_get(self, fields):
        res = super(HrEmployeeAbsence, self).default_get(fields)
        user_id = self._context.get('uid')
        employee_id = self.env['hr.employee'].search([('user_id', '=', user_id)])
        res['employee_id'] = employee_id.id
        ## Check For Hr Group
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        if hr_group:
            res['is_hr_group'] = 'yes'
        else:
            res['is_hr_group'] = 'no'
        ########################

        return res

    @api.onchange('absence_start_at','absence_end_at')
    def check_availability2(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for elem in self:
            if elem.absence_start_at and elem.absence_end_at:
                if elem.absence_start_at > elem.absence_end_at:
                    raise ValidationError('Absence Start Date most be less than End Date!')

                date_gap = (datetime.now().date() - elem.absence_end_at).days
                if date_gap >= 4 and (not hr_group):
                    raise ValidationError('Absence End Date most be less than 4 Days!')

    @api.depends('absence_start_at','absence_end_at')
    def compute_absence(self):
        for elem in self:
            elem.total_absence_days = 0
            if elem.absence_start_at and elem.absence_end_at:
                days = []
                leave_obj = self.env['hr.leave.report'].search([
                    ('employee_id', '=', elem.employee_id.id), ('state', '=', 'validate'), ('leave_type', '=', 'request'),
                    '|',('date_to', '>=', elem.absence_start_at),('date_from', '>=', elem.absence_start_at),
                    '|', ('date_to', '<=', elem.absence_end_at), ('date_from', '<=', elem.absence_end_at)])

                global_leave_days = self.sudo().env['resource.calendar'].search([('name','=',elem.employee_id.contract_id.resource_calendar_id.name)]).global_leave_ids

                gld_days=[]
                for gld in global_leave_days:
                    sd = gld.date_from.date()
                    ed = gld.date_to.date()
                    while sd <= ed:
                        gld_days.append(sd)
                        sd = sd + timedelta(days=1)

                leave_days = []
                for ee in leave_obj:
                    sd = ee.date_from.date()
                    ed = ee.date_to.date()
                    while sd <= ed:
                        leave_days.append(sd)
                        sd = sd+ timedelta(days=1)
                #print(leave_days)
                start_day = elem.absence_start_at
                end_day = elem.absence_end_at
                while start_day <= end_day:
                    if start_day.weekday() in (4, 5):
                        start_day = start_day + timedelta(days=1)
                        continue
                    if start_day in leave_days:
                        start_day = start_day + timedelta(days=1)
                        continue
                    if start_day in gld_days:
                        start_day = start_day + timedelta(days=1)
                        continue
                    days.append(start_day)
                    start_day = start_day + timedelta(days=1)
                #print(days)
                elem.total_absence_days=len(days)
            else:
                elem.total_absence_days = 0

    def unlink(self):
        for elem in self:
            if elem.state != 'draft':
                raise ValidationError('You cannot delete a Absence request which is not in draft state')
            return super(HrEmployeeAbsence, self).unlink()

    # def action_send_notification_to_maneger(self):
    #     template_id = self.env.ref('hr_approvales_masarat.absence_approval_template').id
    #     self.env['mail.template'].browse(template_id).send_mail(self.id, force_send=True)
    #
    # @api.model
    # def create(self, vals_list):
    #     obj = super(HrEmployeeAbsence, self).create(vals_list)
    #     self.sudo().action_send_notification_to_maneger()
    #     return obj

    def action_send_notification_to_maneger(self,employee_id,recode_id):
        employee = self.env['hr.employee'].search([('id', '=', employee_id)])

        email_to = employee.parent_id.work_email
        email_from = employee.work_email

        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)
        body = """
        <div dir="rtl">
            <p><font style="font-size: 14px;">Your Employee """+employee.name+""", requested absence approval, </font></p>
            <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            <a href="%s">Request Link</a>
        </div>""" % (web_base_url)

        template_id = self.env['mail.mail'].create({
            'subject':'إذن غياب',
            'email_from':email_from,
            'email_to': email_to,
            'body_html':body
        })
        #### freaa
        template_id.send()

    @api.model
    def create(self, vals_list):
        obj = super(HrEmployeeAbsence, self).create(vals_list)
        recode_id = obj.id
        employee_id = obj.employee_id.id
        self.sudo().action_send_notification_to_maneger(employee_id, recode_id)
        return obj



class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'


    approved_absence_days = fields.Integer(string='Approved Absence Days', compute='get_absence_days', store=True, default = 0)
    non_approved_absence_days = fields.Integer(string='Absence Days', compute='get_absence_days', store=True, default = 0)

    @api.depends('employee_id', 'date_to', 'date_from')
    def get_absence_days(self):
        for payslip in self:
            if not payslip.employee_id.contract_id.resource_calendar_id.there_is_letancy:
                continue
            if payslip.date_from and payslip.date_to:
                ########## Last Month Date
                fmt = '%Y-%m-%d'
                pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'
                attendance_date_from = (datetime.strptime(pre_date, fmt))
                attendance_date_to = (datetime.strptime(pre_date, fmt) + relativedelta(months=1, day=1, days=-1))
                ##############

                payslip.approved_absence_days = 0  ## init
                payslip.non_approved_absence_days = 0  ## init

                skip_list = []
                # Attended Days
                attended = self.env['hr.attendance'].search(
                    [('employee_id', '=', payslip.employee_id.id), ('attendance_date', '>=', attendance_date_from),
                     ('attendance_date', '<=', attendance_date_to)])
                for days in attended:
                    skip_list.append(str(days.attendance_date)[:10])

                # Global Leave Days
                global_leave_days = self.env['resource.calendar'].search(
                    [('name', '=', payslip.employee_id.contract_id.resource_calendar_id.name)]).global_leave_ids
                for gld in global_leave_days:
                    sd = gld.date_from.date()
                    ed = gld.date_to.date()
                    while sd <= ed:
                        skip_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)

                # Leave Days
                leave_obj = self.env['hr.leave.report'].search(
                    [('employee_id', '=', payslip.employee_id.id), ('state', '=', 'validate'),
                     ('leave_type', '=', 'request'), '|', ('date_to', '>=', attendance_date_from),
                     ('date_from', '>=', attendance_date_from), '|', ('date_to', '<=', attendance_date_to),
                     ('date_from', '<=', attendance_date_to)])
                for ee in leave_obj:
                    sd = ee.date_from.date()
                    ed = ee.date_to.date()
                    while sd <= ed:
                        skip_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)

                ## Work Assignment
                work_assignment_obj = self.env['hr.masarat.work.assignment'].search(
                    [('employee_id', '=', payslip.employee_id.id), ('state', 'in', ('hr_approval', 'manager_approval')),
                     '|', ('end_date', '>=', attendance_date_from), ('start_date', '>=', attendance_date_from), '|',
                     ('end_date', '<=', attendance_date_to), ('start_date', '<=', attendance_date_to)])
                for ee in work_assignment_obj:
                    sd = ee.start_date
                    ed = ee.end_date
                    while sd <= ed:
                        skip_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)

                # Approved work_outside Absence Days
                approved_absence_list = []
                approved_absence = self.env['hr.masarat.absence'].search(
                    [('absence_type', '=', 'work_outside'),
                     ('employee_id', '=', payslip.employee_id.id),
                     ('state', '!=', 'draft'), '|',
                     ('absence_start_at', '>=', attendance_date_from),
                     ('absence_end_at', '>=', attendance_date_from), '|',
                     ('absence_start_at', '<=', attendance_date_to),
                     ('absence_end_at', '<=', attendance_date_to)])
                for days in approved_absence:
                    sd = days.absence_start_at
                    ed = days.absence_end_at
                    while sd <= ed:
                        skip_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)

                # Approved Absence Days
                approved_absence_list = []
                approved_absence = self.env['hr.masarat.absence'].search(
                    [('absence_type', '!=', 'work_outside'),
                     ('employee_id', '=', payslip.employee_id.id),
                     ('state', '!=', 'draft'), '|',
                     ('absence_start_at', '>=', attendance_date_from),
                     ('absence_end_at', '>=', attendance_date_from), '|',
                     ('absence_start_at', '<=', attendance_date_to),
                     ('absence_end_at', '<=', attendance_date_to)])
                for days in approved_absence:
                    sd = days.absence_start_at
                    ed = days.absence_end_at
                    while sd <= ed:
                        approved_absence_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)

                ###################
                #############
                abs_days = []
                approv_abs_days = []
                start_day = attendance_date_from
                end_day = attendance_date_to
                while start_day <= end_day:
                    if start_day.weekday() in (4, 5):
                        start_day = start_day + timedelta(days=1)
                        continue
                    if str(start_day)[:10] in skip_list:
                        start_day = start_day + timedelta(days=1)
                        continue
                    if str(start_day)[:10] in approved_absence_list:
                        approv_abs_days.append(start_day)  ## Approved Absence Days
                        start_day = start_day + timedelta(days=1)
                        continue
                    abs_days.append(start_day)
                    start_day = start_day + timedelta(days=1)

                payslip.approved_absence_days = len(approv_abs_days)
                payslip.non_approved_absence_days = len(abs_days)