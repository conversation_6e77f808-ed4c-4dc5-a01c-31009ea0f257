// Retrive the tab's height by summ its properties
$o-wslides-tabs-height: ($nav-link-padding-y*2) + ($font-size-base * $line-height-base);

// Overal page bg-color: Blend it 'over' the color chosen by the user
// ($body-bg), rather than force it replacing the variable's value.
$o-wslides-color-bg: mix($body-bg, #efeff4);

$o-wslides-color-dark1: #47525f;
$o-wslides-color-dark2: #1f262d;
$o-wslides-color-dark3: #101216;
$o-wslides-fs-side-width: 300px;


// Common to new slides pages
// **************************************************
.o_quiz_gradient {
    background-image: linear-gradient(120deg, #875A7B, darken(#875A7B, 10%));
}

.o_quiz_main {
    background-color: $o-wslides-color-bg;

    .o_wslides_home_nav {
        top: -40px;

        @include media-breakpoint-up(lg) {
            font-size: 1rem;

            .o_wslides_nav_navbar_right {
                padding-left: $spacer;
                margin-left: auto;
                border-left: 1px solid $border-color;
            }
        }
    }

    .o_quiz_js_quiz {
        i.o_quiz_js_quiz_icon {
            cursor: pointer;
        }

        i.o_quiz_js_quiz_icon:hover {
            color: black !important;
        }
    }

    .o_quiz_js_quiz_question {
        .list-group-item  {
            font-size: 1rem;

            input:checked + i.fa-circle {
                color: $primary !important;
            }
        }

        &.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        &.completed-disabled{
            pointer-events: none;
        }
    }

    a.o_quiz_js_quiz_is_correct {
        color: black;
        input:checked + i.fa-check-circle-o {
            color: $primary !important;
        }
    }

    .o_quiz_js_quiz_sequence_highlight {
        background-color: #1252F3;
        height: 1px;
        z-index: 3;

        &:before, &:after {
            content: "";
            @include size(6px);
            display: block;
            border-radius: 100%;
            background-color: inherit;
            @include o-position-absolute(-2px, -2px);
        }

        &:after {
            right: auto;
            left: -2px;
        }
    }
}
