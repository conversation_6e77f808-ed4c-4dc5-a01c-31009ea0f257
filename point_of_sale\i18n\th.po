# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Odo<PERSON> Thaidev <<EMAIL>>, 2021
# <PERSON>hwu<PERSON><PERSON>aen<PERSON>awang <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2023
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-05 14:42+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr "การคืนเงิน"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr "%(pos_name)s (ไม่ได้ใช้)"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr "%s การชำระเงิน POS ของ %s ใน %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order ?"
msgstr "%s มียอดรวมของ %s คุณแน่ใจหรือว่าต้องการลบคำสั่งนี้ ?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "& invoice"
msgstr "และใบแจ้งหนี้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "(Both will be sent by email)"
msgstr "(ทั้งสองจะถูกส่งทางอีเมล)"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr "(ช่วยเหลือสำหรับ %(session)s)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "(as of opening)"
msgstr "(ในขณะที่เปิด)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "(update)"
msgstr "(อัปเดต)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid ". Please contact your manager to accept the closing difference."
msgstr "โปรดติดต่อผู้จัดการของคุณเพื่อยอมรับส่วนต่างในการปิด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"จัดการ\" title=\"Manage\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to manage tax-included prices"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>บริหารจัดการราคารวมภาษีได้อย่างไร"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"ตะกร้าสินค้า\"/>"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "<p>Dear %s,<br/>Here is your electronic ticket for the %s. </p>"
msgstr "<p>เรียน %s<br/>นี่คือทิกเก็ตอิเล็กทรอนิกส์ของคุณสำหรับ%s </p>"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid ""
"<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first "
"session.</p>"
msgstr ""
"<p>พร้อมสำหรับดูได้ที่ <b>อินเทอร์เฟซ POS</b>? มาเริ่มเซสชันแรกกันเลย</p>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid ""
"<span attrs=\"{'invisible': [('is_total_cost_computed','=', "
"True)]}\">TBD</span>"
msgstr ""
"<span attrs=\"{'invisible': [('is_total_cost_computed','=', "
"True)]}\">TBD</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะบริษัท\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Authorized Employees</span>"
msgstr "<span class=\"o_form_label\">พนักงานที่ได้รับอนุญาต</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Barcodes</span>"
msgstr "<span class=\"o_form_label\">บาร์โค้ด</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Default Pricelist</span>"
msgstr "<span class=\"o_form_label\">รายการราคาเริ่มต้น</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Journal Entries</span>"
msgstr "<span class=\"o_form_label\">รายการบันทึกประจำวัน</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Order Reference</span>"
msgstr "<span class=\"o_form_label\">อ้างอิงคำสั่ง</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Payment Methods</span>"
msgstr "<span class=\"o_form_label\">วิธีการชำระเงิน</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>ข้ามหน้าจอแสดงตัวอย่าง</b></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Cash Balance</span>"
msgstr "<span>ยอดปิดเงินคงเหลือล่าสุด</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Date</span>"
msgstr "<span>วันที่ปิดล่าสุด</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>รายงาน</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>มุมมอง</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""
"<strong> &gt; ช่องทางการชำระเงิน</strong>\n"
"                                    เพื่อติดตั้งช่องทางการชำระเงิน และสร้างวิธีการชำระเงินแบบครบวงจร"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "? คลิก\"ยืนยัน\"เพื่อตรวจสอบการชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr "กรุณาใส่ชื่อลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""
"ตัวระบุที่ไม่ซ้ำกันทั่วโลกสำหรับการกำหนดค่า pos นี้ "
"ซึ่งใช้เพื่อป้องกันความขัดแย้งในข้อมูลที่สร้างโดยลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr "ลำดับหมายเลขที่เพิ่มขึ้นทุกครั้งที่ผู้ใช้เริ่มเซสชัน pos ต่อ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "ลำดับหมายเลขที่เพิ่มขึ้นในแต่ละคำสั่ง"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr ""
"เซสชันคือช่วงเวลาหนึ่ง โดยปกติคือหนึ่งวัน "
"ในระหว่างที่คุณขายผ่านการขายหน้าร้าน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr ""
"ขณะนี้มีการเปิดเซสชันสำหรับ PoS นี้ "
"การตั้งค่าบางอย่างสามารถเปลี่ยนแปลงได้หลังจากปิดเซสชันแล้วเท่านั้น"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "ลำดับหมายเลขที่ไม่ซ้ำของเซสชันสำหรับคำสั่ง"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr "ข้อความสั้นที่จะแทรกเป็นส่วนท้ายในใบเสร็จที่พิมพ์ออกมา"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr "ข้อความสั้นที่จะแทรกเป็นส่วนหัวในใบเสร็จที่พิมพ์ออกมา"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr "จำนวน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr "ยอมรับเคล็ดลับของลูกค้าหรือแปลงการเปลี่ยนแปลงเป็นคำแนะนำ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Accept payments difference and post a profit/loss journal entry"
msgstr "ยอมรับส่วนต่างของการชำระเงินและลงรายการบัญชีบันทึกกำไร/ขาดทุน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr "รับชำระเงินด้วยหกสถานีการชำระเงิน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr "รับชำระเงินด้วยสถานีการชำระเงิน Vantiv"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "รับชำระเงินด้วยสถานีชำระเงิน Adyen"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "บัญชี"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "การปัดเศษเงินสดในบัญชี"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "เทมเพลตแผนภูมิบัญชี"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr "ย้ายบัญชี"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr "การชำระเงินในบัญชีที่แสดงการชำระเงินแบบรวมและแบบแยกส่วนธนาคาร"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "บัญชี"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "บันทึกบัญชีที่ใช้ในการสร้างใบแจ้งหนี้"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr ""
"สมุดรายวันการบัญชีที่ใช้ในการลงรายการบัญชีสมุดรายวันของเซสชัน POS "
"และการชำระเงินตามใบแจ้งหนี้ของ POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#, python-format
msgid "Add"
msgstr "เพิ่ม"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.js:0
#, python-format
msgid "Add Customer Note"
msgstr "เพิ่มหมายเหตุลูกค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Add Tip"
msgstr "เพิ่มคำแนะนำ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add a custom message to header and footer"
msgstr "เพิ่มข้อความที่กำหนดเองในส่วนหัวและส่วนท้าย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Add a customer"
msgstr "เพิ่มลูกค้า"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "เพิ่มวิธีการชำระเงินใหม่"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add notes on order lines to be printed on receipt and invoice"
msgstr "เพิ่มหมายเหตุในไลน์คำสั่งที่จะพิมพ์ในใบเสร็จและใบแจ้งหนี้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Address"
msgstr "ที่อยู่"

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "ผู้ดูแลระบบ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
msgid "Advanced Cash Control"
msgstr "การควบคุมเงินสดขั้นสูง"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Advanced Pricelists"
msgstr "รายการราคาขั้นสูง"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "สถานีชำระเงิน Adyen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "All active orders"
msgstr "คำสั่งที่ใช้งานอยู่ทั้งหมด"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"รายการราคาที่มีทั้งหมดจะต้องอยู่ในสกุลเงินเดียวกับบริษัทหรือเป็นสมุดรายวันการขายที่ตั้งค่า"
" ณ จุดขายนี้ หากคุณใช้แอปพลิเคชันการบัญชี"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"วิธีการชำระเงินทั้งหมดจะต้องอยู่ในสกุลเงินเดียวกับสมุดรายวันการขายหรือสกุลเงินของบริษัท"
" หากไม่ได้ตั้งค่าไว้"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "ไลน์การขายทั้งหมด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow discounts per line"
msgstr "อนุญาตให้ส่วนลดต่อไลน์"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow global discounts on orders"
msgstr "อนุญาตให้ส่วนลดทั่วโลกสำหรับคำสั่ง"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Allow the use of coupon and promotion programs in PoS."
msgstr "อนุญาตให้ใช้คูปองและโปรแกรมโปรโมชันใน PoS"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Allow the use of gift card"
msgstr "อนุญาตให้ใช้บัตรของขวัญ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid ""
"Allow to write notes for customer on Orderlines. This will be shown in the "
"receipt."
msgstr ""
"อนุญาตให้เขียนบันทึกสำหรับลูกค้าใน ไลน์คำสั่ง สิ่งนี้จะแสดงในใบเสร็จรับเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "Allowed Pricelists"
msgstr "รายการราคาที่อนุญาต"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr "ช่วยให้ทราบว่ามีการคำนวณต้นทุนรวมทั้งหมดของไลน์คำสั่งแล้วหรือไม่"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr "ช่วยให้ทราบว่าได้คำนวณต้นทุนรวมแล้วหรือไม่"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
msgid "Amount"
msgstr "จำนวน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "จำนวนส่วนต่างที่อนุญาต"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr "ยอดเงินคงเหลือ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "จำนวนทั้งหมด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""
"เกิดข้อผิดพลาดขณะพยายามปิดเซสชัน\n"
"คุณจะถูกเปลี่ยนเส้นทางไปยังส่วนหลังเพื่อปิดเซสชันด้วยตนเอง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr ""
"เกิดข้อผิดพลาดขณะโหลดราคาสินค้า ตรวจสอบให้แน่ใจว่ารายการราคาทั้งหมดมีอยู่ใน "
"POS"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "การระบุภายในของการขายหน้าร้าน"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "มีการเปิดเซสชันอื่นสำหรับการขายหน้าร้านนี้"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "แน่ใจหรือว่าลูกค้าต้องการจ่าย"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "เร็วที่สุดเท่าที่จะทำได้"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (recommended)"
msgstr "ในช่วงปิดเซสชั่น (แนะนำ)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""
"เมื่อปิดเซสชัน: การเลือกจะถูกสร้างขึ้นสำหรับทั้งเซสชันเมื่อปิด\n"
"แบบเรียลไทม์: แต่ละคำสั่งที่ส่งไปยังเซิร์ฟเวอร์จะสร้างการรับของตัวเอง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "คุณลักษณะ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Authorized Difference"
msgstr "ส่วนต่างที่อนุญาต"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr "เซสชันที่สร้างขึ้นโดยอัตโนมัติสำหรับคำสั่ง Orphan ละเว้นในข้อจำกัด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "พิมพ์ใบเสร็จอัตโนมัติ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "เปิดลิ้นชักเก็บเงินอัตโนมัติ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "มีหมวดหมู่สินค้า PoS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Available Pricelists"
msgstr "รายการราคาที่มี"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "มีอยู่ใน POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "ราคาเฉลี่ย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ControlButtonPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Back"
msgstr "กลับ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/NumberPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenNumpad.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Backspace"
msgstr "ย้อนถอยหลัง"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr "ธนาคาร"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr "การชำระเงินผ่านธนาคาร"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement
msgid "Bank Statement"
msgstr "ใบแจ้งยอดจากธนาคาร"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Barcode"
msgstr "บาร์โค้ด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Nomenclature"
msgstr "การตีความบาร์โค้ด"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "กฎของบาร์โค้ด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "เครื่องสแกนบาร์โค้ด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Scanner/Card Reader"
msgstr "เครื่องสแกนบาร์โค้ด/เครื่องอ่านบัตร"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "จำนวนฐาน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr "ส่วนต่างก่อนปิด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "บิล"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Bills &amp; Receipts"
msgstr "บิลและใบเสร็จ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Buffer:"
msgstr "บัฟเฟอร์:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_partners_loading
msgid ""
"By default, 100 partners are loaded.\n"
"When the session is open, we keep on loading all remaining partners in the background.\n"
"In the meantime, you can use the 'Load Customers' button to load partners from database."
msgstr ""
"ตามค่าเริ่มต้น ระบบจะโหลดพันธมิตร 100 ราย\n"
"เมื่อเปิดเซสชั่น เราจะโหลดพันธมิตรที่เหลือทั้งหมดในเบื้องหลังต่อไป\n"
"ในระหว่างนี้ คุณสามารถใช้ปุ่ม 'โหลดลูกค้า' เพื่อโหลดพันธมิตรจากฐานข้อมูล"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "ข้ามการพิมพ์ด้วยเบราว์เซอร์และพิมพ์ผ่านพร็อกซีฮาร์ดแวร์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "CASH"
msgstr "เงินสด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "CHANGE"
msgstr "เงินทอน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "CLOSING CONTROL"
msgstr "ควบคุมการปิด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Can't change customer"
msgstr "ไม่สามารถเปลี่ยนลูกค้าได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Can't mix order with refund products with new products."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "ยกเลิก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr "ยกเลิกคำร้องขอการชำระเงิน"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "ยกเลิกแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot Invoice"
msgstr "ไม่สามารถออกใบแจ้งหนี้ได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#, python-format
msgid "Cannot access product information screen if offline."
msgstr "ไม่สามารถเข้าถึงหน้าจอข้อมูลสินค้าได้หากออฟไลน์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Cannot close the session when offline."
msgstr "ไม่สามารถปิดเซสชั่นขณะออฟไลน์ได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot invoice order from closed session."
msgstr "ไม่สามารถออกคำสั่งใบแจ้งหนี้จากเซสชันที่ปิด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Cannot modify a tip"
msgstr "ไม่สามารถแก้ไขทิปได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "ไม่สามารถทอนเงินได้ถ้าไม่ระบุวิธีการชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr "ชื่อผู้ถือบัตร"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#, python-format
msgid "Cash"
msgstr "เงินสด"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_out
msgid "Cash Box Out"
msgstr "กล่องเงินสดออก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash In"
msgstr "เงินสดเข้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/CashMoveButton.xml:0
#, python-format
msgid "Cash In/Out"
msgstr "เงินสดเข้า/ออก"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "บันทึกเงินสด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash Out"
msgstr "เงินออก"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Cash Register"
msgstr "ลงทะเบียนเงินสด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
msgid "Cash Rounding"
msgstr "การปัดเศษเงินสด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "การปัดเศษเงินสด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_ids
msgid "Cash Statements"
msgstr "งบเงินสด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr "เงินสดเข้า/ออกของ %s ถูกละเลย"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register for %s"
msgstr "เครื่องบันทึกเงินสดสำหรับ %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
msgid "Cash rounding"
msgstr "การปัดเศษเงินสด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.account_cashbox_line_view_tree
msgid "Cashbox balance"
msgstr "ยอดเงินคงเหลือในกล่องเงินสด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Cashdrawer"
msgstr "ลิ้นชักเก็บเงิน"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Cashier"
msgstr "แคชเชียร์"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"หมวดหมู่ใช้เพื่อเรียกดูสินค้าของคุณผ่านทาง\n"
"                อินเทอร์เฟซหน้าจอสัมผัส"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "หมวดหมู่"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "ชื่อหมวดหมู่"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Category Pictures"
msgstr "หมวดหมู่ภาพ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr "หมวดหมู่ที่ใช้ในระบบขายหน้าร้าน"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "เก้าอี้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Change"
msgstr "ทอน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Change Customer"
msgstr "เงินทอนลูกค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Change Tip"
msgstr "เปลี่ยนทิป"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr "ตรวจสอบว่าควรชั่งน้ำหนักสินค้าโดยใช้การรวมเครื่องชั่งฮาร์ดแวร์หรือไม่"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "ตรวจสอบว่าคุณต้องการให้สินค้านี้ปรากฏในการขายหน้าร้านหรือไม่"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr ""
"ตรวจสอบว่าคุณต้องการจัดกลุ่มสินค้าในหมวดหมู่นี้ในคำสั่งการขายหน้าร้านหรือไม่"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "ตรวจสอบจำนวนกล่องเงินสดที่เปิดและปิด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Check the internet connection then try again."
msgstr "ตรวจสอบการเชื่อมต่ออินเทอร์เน็ตแล้วลองอีกครั้ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr ""
"ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต จากนั้นลองซิงค์อีกครั้งโดยคลิกที่ปุ่ม wifi "
"สีแดง (ด้านขวาบนของหน้าจอ)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "หมวดหมู่ย่อย"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""
"เลือกตำแหน่งทางการเงินเฉพาะที่คำสั่งโดยขึ้นอยู่กับชนิดของลูกค้า (ยกเว้นภาษี "
"ในสถานที่ การซื้อกลับบ้าน และอื่น ๆ)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Choose among fiscal positions when processing an order"
msgstr "เลือกระหว่างสถานะทางการเงินเมื่อประมวลผลคำสั่ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "City"
msgstr "เมือง"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Click here to close the session"
msgstr "คลิกที่นี่เพื่อปิดเซสชั่น"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "ลูกค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Connected"
msgstr "หน้าจอลูกค้าเชื่อมต่อแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Disconnected"
msgstr "หน้าจอลูกค้าถูกตัดการเชื่อมต่อ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Client Screen Unsupported. Please upgrade the IoT Box"
msgstr "หน้าจอลูกค้าไม่รองรับ โปรดอัปเกรด IoT Box"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Warning"
msgstr "คำเตือนหน้าจอลูกค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/HeaderButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Close"
msgstr "ปิด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr "ปิดเซสชั่น"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr "ปิดเซสชันและโพสต์รายการ"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr "ปิดตัวช่วยสร้างเซสชัน"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "ปิดและโพสต์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Closing ..."
msgstr "การปิด ..."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "การควบคุมการปิด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "วันที่ปิด"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr "ส่วนต่างของการปิดใน%s (%s)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Closing session error"
msgstr "ข้อผิดพลาดในการปิดเซสชัน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr "มูลค่าเหรียญ/บิล"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Coins/Bills"
msgstr "เหรียญ/บิล"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr "รวมกัน %s การชำระเงิน POS จาก %s"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "บริษัท"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
msgid "Company has chart of accounts"
msgstr "บริษัทมีผังบัญชี"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Configuration for journal entries of PoS orders"
msgstr "การกำหนดค่าสำหรับรายการบันทึกของคำสั่ง PoS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr "การกำหนดค่า &gt; การตั้งค่า"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Configure at least one Point of Sale."
msgstr "กำหนดค่าการขายหน้าร้านอย่างน้อยหนึ่งจุด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#, python-format
msgid "Confirm"
msgstr "ยืนยัน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#, python-format
msgid "Confirm ?"
msgstr "ยืนยัน ?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices to your PoS directly without an IoT Box"
msgstr "เชื่อมต่ออุปกรณ์กับ PoS ของคุณโดยตรงโดยไม่ต้องใช้ IoT Box"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "เชื่อมต่ออุปกรณ์กับ PoS ของคุณโดยไม่ต้องใช้กล่อง IoT"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices using an IoT Box"
msgstr "เชื่อมต่ออุปกรณ์โดยใช้ IoT Box"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connected Devices"
msgstr "เชื่อมต่ออุปกรณ์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr "เชื่อมต่อไม่ได้เป็นเจ้าของ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr "กำลังเชื่อมต่อกับพร็อกซี"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Connecting to the IoT Box"
msgstr "กำลังเชื่อมต่อกับกล่อง IoT"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Connection Error"
msgstr "การเชื่อมต่อล้มเหลว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Connection error"
msgstr "การเชื่อมต่อล้มเหลว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is aborted"
msgstr "การเชื่อมต่อถูกยกเลิก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is lost"
msgstr "ขาดการเชื่อมต่อ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "การเชื่อมต่อกับกล่องไอโอทีล้มเหลว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "การเชื่อมต่อกับเครื่องพิมพ์ล้มเหลว"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Continue Selling"
msgstr "ทำการขายต่อ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Continue selling"
msgstr "ทำการขายต่อ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "อัตราการแปลง"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "อัตราการแปลงจากสกุลเงินของบริษัทเป็นสกุลเงินในการสั่ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Cost:"
msgstr "ต้นทุน:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Counted"
msgstr "จำนวน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Country"
msgstr "ประเทศ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Coupon and Promotion Programs"
msgstr "โปรแกรมคูปองและโปรโมชัน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Coupons & Promotions"
msgstr "คูปองและโปรโมชัน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Create"
msgstr "สร้าง"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr "สร้างคำสั่ง POS ใหม่"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Create a new PoS"
msgstr "สร้าง PoS ใหม่"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "สร้างตัวเลือกสินค้าใหม่"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "อัตราแลกเปลี่ยน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "เซสชันปัจจุบัน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "เซสชันปัจจุบันที่รับผิดชอบ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "สถานะเซสชันปัจจุบัน"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr "ที่กำหนดเอง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "ลูกค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr "บัญชีของลูกค้า"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Customer Display"
msgstr "แสดงลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr "หน้าจอแสดงผลของลูกค้า"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "ใบแจ้งหนี้ของลูกค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr "หมายเหตุลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid "Customer Notes"
msgstr "หมายเหตุลูกค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Customer Required"
msgstr "ความต้องการของลูกค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr "ลูกค้าเป็นสิ่งจำเป็นสำหรับ %s วิธีการชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Customer tips, cannot be modified directly"
msgstr "เคล็ดลับลูกค้า ไม่สามารถแก้ไขได้โดยตรง"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "ลูกค้า"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "แดชบอร์ด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#, python-format
msgid "Date"
msgstr "วันที่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Days"
msgstr "วัน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Debug Window"
msgstr "หน้าต่างดีบัก"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__account_id
msgid "Default Account"
msgstr "บัญชีเริ่มต้น"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr "บัญชีลูกหนี้เริ่มต้น (PoS)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "ตำแหน่งทางการเงินเริ่มต้น"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Intermediary Account"
msgstr "บัญชีตัวกลางเริ่มต้น"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
msgid "Default Pricelist"
msgstr "รายการราคาเริ่มต้น"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "ภาษีการขายเริ่มต้น"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "ภาษีการขายเริ่มต้น"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "ภาษีขายเริ่มต้นสำหรับสินค้า"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "หน่วยวัดเริ่มต้นที่ใช้สำหรับการปฏิบัติการสต๊อกทั้งหมด"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "กำหนดหมวดหมู่ใหม่"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Define the smallest coinage of the currency used to pay"
msgstr "กำหนดเหรียญที่เล็กที่สุดของสกุลเงินที่ใช้ในการจ่าย"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr "กำหนดเหรียญที่เล็กที่สุดของสกุลเงินที่จะใช้ชำระด้วยเงินสด"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr ""
"กำหนดชื่อวิธีการชำระเงินที่จะแสดงในระบบขายหน้าร้านเมื่อมีการเลือกการชำระเงิน"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__barcode_nomenclature_id
msgid ""
"Defines what kind of barcodes are available and how they are assigned to "
"products, customers and cashiers."
msgstr ""
"กำหนดประเภทของบาร์โค้ดที่สามารถใช้ได้และวิธีกำหนดบาร์โค้ดให้กับสินค้า ลูกค้า"
" และแคชเชียร์"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "การตรวจสอบความถูกต้องล่าช้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Delete"
msgstr "ลบ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr "ลบคำสั่งที่ชำระเงินแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Paid Orders ?"
msgstr "ลบคำสั่งที่ชำระเงินแล้ว ?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr "ลบคำสั่งที่ยังไม่ได้ชำระ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Unpaid Orders ?"
msgstr "ลบคำสั่งที่ยังไม่ได้ชำระ ?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Deselect Customer"
msgstr "ยกเลิกการเลือกลูกค้า"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_organizer
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "ออแกไนเซอร์ตั้งโต๊ะ"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_pad
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr "แผ่นรองโต๊ะ"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "โต๊ะ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr "บัญชีปลายทาง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr "บัญชีปลายทางเป็นแบบอ่านอย่างเดียว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_difference
#, python-format
msgid "Difference"
msgstr "ส่วนต่าง"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr "ส่วนต่างเมื่อปิดเซสชัน PoS"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr "ส่วนต่างระหว่างยอดคงเหลือการปิดตามทฤษฎีกับยอดคงเหลือการปิดจริง"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "ไดเจส"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Direct Devices"
msgstr "อุปกรณ์โดยตรง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Disc"
msgstr "ส่วนลด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "ส่วนลด%"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "ส่วนลด:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Discard"
msgstr "ละทิ้ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Disconnected"
msgstr "ตัดการเชื่อมต่อ"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_consumable
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
msgid "Discount"
msgstr "ส่วนลด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "ส่วนลด (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "การแจ้งเตือนส่วนลด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discount:"
msgstr "ส่วนลด:"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "สินค้าลดราคา"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discounts"
msgstr "ลดราคา"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Dismiss"
msgstr "ยกเลิก"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_display_categ_images
msgid "Display Category Pictures"
msgstr "แสดงรูปภาพของหมวดหมู่"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Display pictures of product categories"
msgstr "แสดงรูปภาพหมวดหมู่สินค้า"

#. module: point_of_sale
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "ไม่มีสิทธิ์เข้าถึง ข้ามข้อมูลนี้สำหรับอีเมลไดเจสของผู้ใช้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr "คุณต้องการเปิดรายชื่อลูกค้าเพื่อเลือกลูกค้าหรือไม่?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Do you want to print using the web printer?"
msgstr "คุณต้องการพิมพ์โดยใช้เครื่องพิมพ์เว็บหรือไม่?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Documentation"
msgstr "เอกสารกำกับ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Don't show again"
msgstr "ไม่ต้องแสดงอีก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "Done by"
msgstr "ทำโดย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr "ดาวน์โหลดคำสั่งที่ชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr "ดาวน์โหลดคำสั่งที่ยังไม่ได้ชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Download error traceback"
msgstr "ดาวน์โหลดการติดตามข้อผิดพลาด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientLine.xml:0
#, python-format
msgid "EDIT"
msgstr "แก้ไข"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Either the server is inaccessible or browser is not connected online."
msgstr "เซิร์ฟเวอร์ไม่สามารถเข้าถึงได้หรือเบราว์เซอร์ไม่ได้เชื่อมต่อออนไลน์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Electronic Scale"
msgstr "เครื่องชั่งอิเล็กทรอนิกส์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Email"
msgstr "อีเมล"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "Email Receipt"
msgstr "ใบเสร็จอีเมล"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Email sent."
msgstr "ส่งอีเมลแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Employee"
msgstr "พนักงาน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"พนักงานสามารถสแกนป้ายของตนหรือป้อน PIN เพื่อเข้าสู่ระบบเซสชัน PoS "
"ข้อมูลประจำตัวเหล่านี้สามารถกำหนดค่าได้ในแท็บ *การตั้งค่าทรัพยากรบุคคล* "
"ของแบบฟอร์มพนักงาน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Empty Order"
msgstr "คำสั่งที่ว่างเปล่า"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"เปิดใช้งานการสแกนบาร์โค้ดด้วยเครื่องสแกนบาร์โค้ดที่เชื่อมต่อจากระยะไกลและการรูดบัตรด้วยเครื่องอ่านบัตร"
" Vantiv"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "เปิดใช้งานการรวมเครื่องชั่งอิเล็กทรอนิกส์"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,help:point_of_sale.field_pos_order__invoice_group
msgid "Enables invoice generation from the Point of Sale."
msgstr "เปิดใช้งานการสร้างใบแจ้งหนี้จากการขายหน้าร้าน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr "พบข้อผิดพลาดขณะโหลดภาพ กรุณาลองอีกครั้ง."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "วันสิ้นสุด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "ยอดคงเหลือปลายทาง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error"
msgstr "ผิดพลาด"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "ข้อผิดพลาด ! คุณไม่สามารถสร้างหมวดหมู่แบบเรียกซ้ำได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Error with Traceback"
msgstr "เกิดข้อผิดพลาดกับการตรวจสอบย้อนกลับ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr "ข้อผิดพลาด: ไม่มีการเชื่อมต่ออินเทอร์เน็ต"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Existing orderlines"
msgstr "รายการคำสั่งที่มีอยู่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Exit Pos"
msgstr "ออกจาก Pos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_expected
#, python-format
msgid "Expected"
msgstr "ควาดหวัง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr "ส่งออกคำสั่งที่ชำระเงินแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr "ส่งออกคำสั่งที่ยังไม่ได้ชำระเงิน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "ข้อมูลเพิ่มเติม"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr "ผ้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr "การรับที่ล้มเหลว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Financials"
msgstr "การเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr "เสร็จสิ้นการนำเข้าคำสั่ง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position"
msgstr "ฐานะทางการเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Fiscal Position not found"
msgstr "ไม่พบสถานะทางการเงิน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position per Order"
msgstr "ฐานะทางการเงินต่อคำสั่ง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Positions"
msgstr "ฐานะทางการเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Fiscal data module error"
msgstr "ข้อผิดพลาดของโมดูลข้อมูลทางการเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบอักษรที่ยอดเยี่ยมเช่น fa-tasks"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Footer"
msgstr "ส่วนท้าย"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "สำหรับหน้าจอสัมผัสอุตสาหกรรมที่ไม่แม่นยำ"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr "บังคับให้ปิดเซสชัน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force Done"
msgstr "บังคับเสร็จสิ้น"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force done"
msgstr "บังคับเสร็จสิ้น"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr "บังคับบัญชีคงค้าง"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr ""
"บังคับให้ตั้งค่าลูกค้าเมื่อใช้วิธีการชำระเงินนี้และแยกรายการสมุดรายวันสำหรับลูกค้าแต่ละราย"
" อาจทำให้กระบวนการปิดช้าลง"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr "จากการชำระเงินตามใบแจ้งหนี้"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr "ชื่อผลิตภัณฑ์แบบเต็ม"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Generation of your order references"
msgstr "การสร้างการอ้างอิงคำสั่งของคุณ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Gift Cards"
msgstr "บัตรของขวัญ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Gift card"
msgstr "บัตรของขวัญ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Give customer rewards, free samples, etc."
msgstr "ให้รางวัลแก่ลูกค้า ตัวอย่างฟรี และอื่น ๆ "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "ให้ลำดับการเรียงลำดับเมื่อแสดงรายการประเภทสินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
msgid "Global Discounts"
msgstr "ส่วนลดทั่วโลก"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "ไปยัง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr "เกินกว่าที่อนุญาต"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "จัดกลุ่มตาม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "กลุ่มผลิตภัณฑ์ใน POS"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr "การเชื่อมต่อ HTTPS กับกล่อง IoT ล้มเหลว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Events"
msgstr "อีเวนต์ฮาร์ดแวร์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Status"
msgstr "สถานะฮาร์ดแวร์"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
msgid "Has Active Session"
msgstr "มีเซสชันที่ใช้งานอยู่"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "มีการควบคุมเงินสด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr "มีไลน์การคืน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Header"
msgstr "ส่วนหัว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
msgid "Header & Footer"
msgstr "ส่วนหัวและส่วนท้าย"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "ซ่อนการใช้สถานีชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#, python-format
msgid "Home"
msgstr "โฮม"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "How would you like to receive your receipt"
msgstr "คุณต้องการรับใบเสร็จอย่างไร"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ไอดี"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "สำคัญ: รายงานบักจากการขายหน้าร้านของ Odoo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
msgid "IP Address"
msgstr "ที่อยู่ IP"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุกิจกรรมการยกเว้น"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr "ระบุลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr ""
"หากรายการสั่งซื้อนี้เป็นการคืนเงิน "
"รายการสั่งซื้อที่คืนเงินจะถูกระบุในช่องนี้"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"หากคุณส่งมอบผลิตภัณฑ์ทั้งหมดในคราวเดียว "
"ใบสั่งจัดส่งจะถูกกำหนดเวลาตามระยะเวลารอคอยสินค้าที่ยิ่งใหญ่ที่สุด "
"มิฉะนั้นจะยึดตามระยะเวลาที่สั้นที่สุด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr "Iface หน้าจอแสดงผลของลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
msgid "Image"
msgstr "รูปภาพ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Import Orders"
msgstr "คำสั่งนำเข้า"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "ปรับปรุงการนำทางสำหรับหน้าจอสัมผัสอุตสาหกรรมที่ไม่แม่นยำ"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "กำลังดำเนินการ"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "หากต้องการลบการขาย จะต้องเป็นรายการใหม่หรือยกเลิก"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time"
msgstr "แบบเรียลไทม์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr "ที่อยู่สำหรับจัดส่งไม่ถูกต้อง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr "การปัดเศษไม่ถูกต้อง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#, python-format
msgid "Info"
msgstr "ข้อมูลรายละเอียด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr "ข้อความข้อมูล"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
msgid "Initial Category"
msgstr "หมวดหมู่เริ่มต้น"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"การติดตั้งผังบัญชีจากการตั้งค่าทั่วไปของ\n"
"                แอพออกใบแจ้งหนี้/บัญชีจะสร้างการชำระเงินผ่านธนาคารและเงินสด\n"
"                วิธีการโดยอัตโนมัติ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "การชำระเงินด้วยบัตรแบบผสานรวม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "บัญชีตัวกลาง"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "หมวดหมู่ภายใน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "บันทึกภายใน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Invalid action"
msgstr "การดำเนินการไม่ถูกต้อง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Invalid amount"
msgstr "จำนวนเงินที่ไม่ถูกต้อง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Invalid email."
msgstr "อีเมลไม่ถูกต้อง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Invalid product lot"
msgstr "ล็อตผลิตภัณฑ์ไม่ถูกต้อง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "คลังสินค้า"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "การจัดการคลังสินค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "ใบแจ้งหนี้"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Invoice Journal"
msgstr "บันทึกใบแจ้งหนี้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr "การชำระเงินตามใบแจ้งหนี้สำหรับ %s (%s) โดยใช้ %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Invoiced"
msgstr "ออกใบแจ้งหนี้แล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__invoice_group
msgid "Invoicing"
msgstr "การออกใบแจ้งหนี้"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box"
msgstr "กล่อง IoT "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box IP Address"
msgstr "ที่อยู่ IP ของกล่อง IoT"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "ออกใบแจ้งหนี้แล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr "คืนเงินแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr "คำนวณต้นทุนทั้งหมดแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "กำลังใช้สกุลเงินของบริษัท"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "เป็นบาร์/ร้านอาหาร"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "มีการติดตั้งบัญชีเต็มรูปแบบ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr "นี้ทิปแล้วเหรอ?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr "การชำระเงินนี้มีการเปลี่ยนแปลงหรือไม่?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"ห้ามแก้ไขภาษีที่ใช้ในใบสั่ง POS ที่ไม่ได้ผ่านรายการ คุณต้องปิดเซสชัน POS "
"ก่อนแก้ไขภาษี"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr "JS Tests"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
msgid "Journal"
msgstr "สมุดบันทึก"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "รายการบันทึกบัญชี"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "รายการบันทึก"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Journal Items"
msgstr "รายการบันทึก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Keep Session Open"
msgstr "เปิดเซสชันไว้"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "ค่า Kpi Pos รวม"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.led_lamp
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "หลอดไฟ LED"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "ป้าย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Language"
msgstr "ภาษา"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "แถบเลื่อนขนาดใหญ่"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "เงินสดปิดเซสชันล่าสุด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "วันที่ปิดเซสชันล่าสุด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr "หนัง"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr "เว้นว่างไว้เพื่อใช้บัญชีเริ่มต้นจากการตั้งค่าบริษัท"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""
"เว้นว่างไว้เพื่อใช้บัญชีเริ่มต้นจากการตั้งค่าของบริษัท\n"
"บัญชีที่ใช้เป็นบัญชีคงค้างเมื่อสร้างบันทึกการชำระเงินทางบัญชีสำหรับการชำระเงินผ่านธนาคาร"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""
"เว้นว่างไว้เพื่อใช้บัญชีเริ่มต้นจากการตั้งค่าบริษัท\n"
"แทนที่บัญชีลูกหนี้ของบริษัท (สำหรับระบบขายหน้าร้าน) ที่ใช้ในรายการสมุดรายวัน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr "เว้นว่างไว้เพื่อใช้บัญชีลูกหนี้ของลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""
"เว้นว่างไว้เพื่อใช้บัญชีลูกหนี้ของลูกค้า\n"
"กำหนดสมุดรายวันที่จะจองการชำระเงินสะสม (หรือการชำระเงินแต่ละรายการ ถ้าระบุลูกค้าเป็นจริง) หลังจากการปิดเซสชัน\n"
"สำหรับสมุดรายวันเงินสด เราจะเขียนโดยตรงไปยังบัญชีเริ่มต้นในสมุดรายวันผ่านทางรายการใบแจ้งยอด\n"
"สำหรับสมุดรายวันธนาคาร เราจะเขียนไปยังบัญชีคงค้างที่ระบุไว้ในวิธีการชำระเงินนี้\n"
"อนุญาตให้เฉพาะเงินสดและสมุดรายวันธนาคารเท่านั้น"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.letter_tray
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "ถาดจดหมาย"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_amount
msgid "Limited Partners Amount"
msgstr "จำกัดจำนวนพาร์ทเนอร์"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_loading
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Partners Loading"
msgstr "จำกัดการโหลดพาร์ทเนอร์"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_loading
msgid "Limited Product Loading"
msgstr "จำกัดการโหลดผลิตภัณฑ์"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_amount
msgid "Limited Products Amount"
msgstr "สินค้ามีจำนวนจำกัด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Products Loading"
msgstr "กำลังโหลดสินค้าจำนวนจำกัด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "ไลน์เบอร์"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "List of Cash Registers"
msgstr "รายชื่อเครื่องบันทึกเงินสด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Load Customers"
msgstr "โหลดลูกค้า"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining partners in the background"
msgstr "โหลดพาร์ทเนอร์ที่เหลือทั้งหมดในเบื้องหลัง"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining products in the background"
msgstr "โหลดสินค้าที่เหลืออยู่ทั้งหมดในพื้นหลัง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Loading"
msgstr "กำลังโหลด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Loading Image Error"
msgstr "กำลังโหลดรูปภาพผิดพลาด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr "หน้าจอแสดงผลของลูกค้าท้องถิ่น"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "หมายเลขลำดับการเข้าสู่ระบบ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Logo"
msgstr "โลโก้"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "ชื่อล็อต"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/OrderWidget.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "ล็อต/หมายเลขซีเรียลที่ต้องการ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "ล็อต/หมายเลขซีเรียล"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_loyalty
msgid "Loyalty Program"
msgstr "โปรแกรมลูกค้าสมาชิก"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Loyalty program to use for this point of sale."
msgstr "โปรแกรมลูกค้าสมาชิกที่ใช้โดยการขายหน้าร้านนี้"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.magnetic_board
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "กระดานแม่เหล็ก"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "ทำการชำระ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"จัดทำรายการราคาหลายรายการในจุดขาย "
"คุณยังสามารถใช้รายการราคากับลูกค้าเฉพาะจากแบบฟอร์มการติดต่อของพวกเขา "
"(ในแท็บการขาย) เพื่อให้ถูกต้อง "
"รายการราคานี้ต้องแสดงอยู่ที่นี่เป็นรายการราคาที่มีอยู่ "
"มิเช่นนั้นจะใช้รายการราคาเริ่มต้น"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr ""
"ตรวจสอบให้แน่ใจว่าคุณใช้ IoT Box v18.12 หรือสูงกว่า นำทางไปยัง %s "
"เพื่อรับใบรับรอง IoT Box ของคุณ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card"
msgstr "จัดการบัตรของขวัญ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card."
msgstr "จัดการบัตรของขวัญ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "จัดการโปรโมชั่นและโปรแกรมคูปอง"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion and coupon programs."
msgstr "จัดการโปรแกรมโปรโมชั่นและคูปอง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
msgid "Manual Discounts"
msgstr "ส่วนลดด้วยตนเอง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "อัตราส่วน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr "อัตราส่วน (%)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Margin:"
msgstr "อัตราส่วน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr "เกินขีดจำกัดสูงสุด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Maximum value reached"
msgstr "ถึงมูลค่าสูงสุดแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "วิธีการ"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Miscellaneous"
msgstr "เบ็ดเตล็ด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
msgid "Module Pos Hr"
msgstr "โมดูล Pos Hr"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.monitor_stand
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "ขาตั้งจอมอนิเตอร์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#, python-format
msgid "More..."
msgstr "มากกว่า..."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "เซสชั่นของฉัน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Name"
msgstr "ชื่อ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Need customer to invoice"
msgstr "ต้องการให้ลูกค้าเพื่อออกใบแจ้งหนี้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""
"ต้องการบัญชีขาดทุนสำหรับการบันทึกรายวันต่อไปนี้เพื่อลงบันทึกจำนวนขาดทุน: "
"%s\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr ""
"ต้องการบัญชีกำไรสำหรับบันทึกรายวันต่อไปนี้เพื่อลงบันทึกจำนวนเงินที่ได้รับเพิ่ม:"
" %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Network Error"
msgstr "ข้อผิดพลาดเครือข่าย"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "ใหม่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "New Order"
msgstr "คำสั่งใหม่"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "เซสชันใหม่"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.newspaper_rack
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "ชั้นวางหนังสือพิมพ์"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Next Order List"
msgstr "รายการคำสั่งถัดไป"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "No"
msgstr "ไม่"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "No Taxes"
msgstr "ไม่มีภาษี"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr "ไม่พบใบแจ้งยอดเงินสดสำหรับเซสชันนี้ ไม่สามาถบันทึกเงินสดคืนได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "No customer found"
msgstr "ไม่พบลูกค้า"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "ยังไม่มีข้อมูล!"

#. module: point_of_sale
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr "ไม่มีลิงก์ไปยังใบแจ้งหนี้สำหรับ%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
#, python-format
msgid "No orders found"
msgstr "ไม่พบคำสั่ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid "No product found"
msgstr "ไม่พบสินค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "No results found for \""
msgstr "ไม่พบผลลัพธ์สำหรับ \""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "No sequence defined on the journal"
msgstr "ไม่มีลำดับที่กำหนดไว้ในบันทึก"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid "No sessions found"
msgstr "ไม่พบเซสชั่น"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "None"
msgstr "ไม่มี"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "ไม่ออกใบแจ้งหนี้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Notes"
msgstr "หมายเหตุ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_opened_session
msgid "Number of Opened Session"
msgstr "จำนวนเซสชั่นที่เปิด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Partners Loaded"
msgstr "จำนวนพาร์ทเนอร์ที่โหลด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "จำนวนการพิมพ์"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Products Loaded"
msgstr "จำนวนสินค้าที่โหลด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr "จำนวนคำสั่งซื้อที่ขอคืนเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr "จำนวนสินค้าที่คืนเงินในคำสั่งซื้อนี้"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of partners loaded can not be 0"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of product loaded can not be 0"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "OPENING CASH CONTROL"
msgstr "การเปิดการควบคุมเงินสด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Offline"
msgstr "Offline"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Offline Error"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Offline Orders"
msgstr "การสั่งซื้อแบบออฟไลน์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Ok"
msgstr "ตกลง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Ongoing"
msgstr "ต่อเนื่อง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr ""
"อนุญาตให้ใช้เฉพาะปริมาณติดลบสำหรับรายการการคืนเงินนี้ คลิก +/- "
"เพื่อแก้ไขจำนวนที่ต้องการคืนเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "ใช้การปัดเศษเป็นเงินสดเท่านั้น"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr ""
"เฉพาะสมุดรายวันประเภท 'เงินสด' หรือ 'ธนาคาร' "
"เท่านั้นที่สามารถใช้กับวิธีการชำระเงินได้"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load a limited number of customers at the opening of the PoS."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load most common products at the opening of the PoS."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only on cash methods"
msgstr "เฉพาะวิธีเงินสดเท่านั้น"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"เฉพาะผู้ใช้ที่มีสิทธิ์การเข้าถึงระดับผู้จัดการสำหรับแอป PoS "
"เท่านั้นที่สามารถแก้ไขราคาสินค้าตามคำสั่งซื้อได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""
"รองรับเฉพาะรูปแบบรูปภาพที่เข้ากันได้กับเว็บ เช่น .png หรือ .jpeg เท่านั้น"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr "เปิดกล่องเงินสด"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Open POS Menu"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "เปิดเซสชัน PoS ที่ใช้วิธีการชำระเงินนี้"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr "เปิดวาระการขาย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Open session"
msgstr "เปิดเซสชัน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr "เปิดป๊อปอัปรายละเอียดเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr "เปิดโดย"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Opened Sessions"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "เปิดโดย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Opening"
msgstr "เปิด"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "เปิดการควบคุม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "วันที่เริ่มต้น"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr "บันทึกการเปิด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Opening cash"
msgstr "เปิดเงินสด"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.account_cashbox_line_action
msgid "Opening/Closing Values"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation Type"
msgstr "ประเภทการดำเนินงาน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Operation type used to record product pickings <br/>\n"
"                                    Products will be taken from the default source location of this operation type"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "ประเภทการดำเนินการจะแสดงในแดชบอร์ดสินค้าคงคลัง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "Order"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Order %s"
msgstr "คำสั่งซื้อ %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr "คำสั่งซื้อ %s ไม่ได้รับชำระเต็มจำนวน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "จำนวนคำสั่ง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "วันที่สั่งซื้อ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
msgid "Order IDs Sequence"
msgstr "รหัสคำสั่งซื้อตามลำดับ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "ลำดับรหัสรายการสั่งซื้อ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "รายการสั่งสินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
msgid "Order Ref"
msgstr "อ้างอิงคำสั่ง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "หมายเลขลำดับคำสั่งซื้อ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Order is empty"
msgstr "คำสั่งซื้อว่างเปล่า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Order is not synced. Check your internet connection"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "รายการคำสั่งซื้อ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr "รายการสั่งซื้อในฟิลด์นี้คือรายการที่คืนเงินรายการสั่งซื้อนี้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/TicketButton.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "รายการสั่งซื้อ"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "การวิเคราะห์คำสั่งซื้อ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
msgid "Other Devices"
msgstr "อุปกรณ์อื่นๆ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "ข้อมูลอื่นๆ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Others"
msgstr "อื่น ๆ "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr "บัญชีคงค้าง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr "คำสั่งซื้อ POS"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr "คำสั่งซื้อ POS %s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "รายการคำสั่งซื้อ POS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "รายการคำสั่งซื้อ POS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "คำสั่งซื้อ POS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "รายการคำสั่งซื้อ POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr "วิธีการชำระเงิน POS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr "หมวดหมู่สินค้า POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "การขาย POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr "เซสซัน POS"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "POS error"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr "รายการคำสั่งซื้อ POS %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "ชำระเงินแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "หมวดสินค้าแม่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Partner"
msgstr "คู่ค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__partner_load_background
msgid "Partner Load Background"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#, python-format
msgid "Pay"
msgstr "จ่าย"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "ชำระเงินคำสั่งซื้อ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "การชำระเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "วันที่ชําระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr "วิธีการชำระเงิน"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
msgid "Payment Methods"
msgstr "วิธีการชำระเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr "ข้อมูลใบเสร็จรับเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "การอ้างอิงการชําระเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr "สถานะการชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment Successful"
msgstr "ชำระเงินสำเร็จ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr "เทอร์มินัลการชำระเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "รหัสธุรกรรมการชำระเงิน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Payment methods available"
msgstr "มีวิธีการชำระเงินให้เลือก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment request pending"
msgstr "คำขอชำระเงินอยู่ระหว่างดำเนินการ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment reversed"
msgstr "การชำระเงินกลับรายการแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "การชำระ"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "วิธีการชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Payments in"
msgstr "ชำระเงินใน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "Payments:"
msgstr "การชำระเงิน:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Pending Electronic Payments"
msgstr ""

#. module: point_of_sale
#: model:ir.filters,name:point_of_sale.filter_orders_per_session
msgid "Per session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"ผู้ที่ใช้เครื่องบันทึกเงินสด อาจเป็นผู้ช่วยเหลือ นักศึกษา "
"หรือลูกจ้างชั่วคราว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Phone"
msgstr "โทรศัพท์"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Pick which product categories are available"
msgstr "เลือกหมวดหมู่สินค้าที่มีอยู่"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "การรับสินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "จำนวนการเบิกสินค้า"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr "การเลือก POS"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr "การรับสินค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Picture"
msgstr "รูปภาพ"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr "พลาสติก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr "โปรดยืนยันจำนวนเงินจำนวนมาก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "โปรดตรวจสอบว่ากล่องไอโอทียังคงเชื่อมต่ออยู่หรือไม่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr "โปรดตรวจสอบว่าเครื่องพิมพ์ยังคงเชื่อมต่ออยู่หรือไม่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue"
msgstr ""
"โปรดตรวจสอบว่าเครื่องพิมพ์ยังคงเชื่อมต่ออยู่หรือไม่\n"
"เบราว์เซอร์บางตัวไม่อนุญาตให้มีการโทร HTTP จากเว็บไซต์ไปยังอุปกรณ์ในเครือข่าย (ด้วยเหตุผลด้านความปลอดภัย) หากเป็นกรณีนี้ คุณจะต้องปฏิบัติตามเอกสารของ Odoo สำหรับ 'ใบรับรองที่ลงนามด้วยตนเองสำหรับเครื่องพิมพ์ ePOS' และ 'การเชื่อมต่อที่ปลอดภัย (HTTPS)' เพื่อแก้ไขปัญหานี้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Please check your internet connection and try again."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr ""
"กรุณาปิดเซสซันการขายหน้าร้านทั้งหมดในช่วงเวลานี้ก่อนที่จะปิด "
"เซสชั่นที่เปิดคือ:%s "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr "โปรดกำหนดค่าวิธีการชำระเงินใน POS ของคุณ"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr "โปรดกำหนดบัญชีรายได้สำหรับสินค้านี้: \"%s\" (รหัส:%d)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr "กรุณาพิมพ์ใบแจ้งหนี้จากการทำงานเบื้องหลัง"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "กรุณาระบุพาร์ทเนอร์สำหรับการขาย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Please select a payment method."
msgstr "กรุณาเลือกวิธีการชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please select the Customer"
msgstr "กรุณาเลือกลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pos_categ_id
msgid "PoS Category"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Interface"
msgstr "อินเตอร์เฟซ PoS"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr "คำสั่ง PoS"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Product Categories"
msgstr "หมวดหมู่สินค้า PoS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "หมวดหมู่สินค้า PoS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "ระบบการขายหน้าร้าน"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_pos
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model:ir.ui.menu,name:point_of_sale.menu_pos_config_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Point of Sale"
msgstr "จุดขาย"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "การวิเคราะห์การขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_id
msgid "Point of Sale Category"
msgstr "หมวดจุดขาย"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "การกำหนดค่าการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "การตั้งค่าจุดขาย"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
msgid "Point of Sale Configurations"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "รายละเอียดการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "รายงานรายละเอียดการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "รายงานใบแจ้งหนี้ของระบบการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
msgid "Point of Sale Journal"
msgstr "สมุดรายวันระบบการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "โปรแกรมชำระเงินของระบบการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "กลุ่มผู้จัดการระบบขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "ประเภทการดำเนินงานของระบบการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "ไลน์คำสั่งการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "คำสั่งการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "รายงานคำสั่งขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "วิธีการชำระเงินการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "การชำระเงินหน้าร้าน"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "เซสชั่นการขายหน้าร้าน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr "การทดสอบระบบการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "กลุ่มผู้ใช้ระบบขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "การกำหนดค่า Pos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "คำสั่ง Pos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "จำนวนคำสั่ง Pos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "รายการคำสั่งซื้อ Pos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr "การชำระเงิน Pos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "หมวดหมู่สินค้าสำหรับจุดขาย"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr "เซสซัน Pos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "ระยะเวลาเซสซัน Pos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "สถานะเซสชัน Pos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "ชื่อผู้ใช้เซสชัน POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "เซสชัน Pos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
msgid "PosBox"
msgstr "PosBox"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr "ไม่อนุญาตให้ใช้ปริมาณที่เป็นบวก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Postcode"
msgstr "รหัสไปรษณีย์"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "ลงบันทึก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Previous Order List"
msgstr "รายการคำสั่งก่อนหน้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Price"
msgstr "ราคา"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Price Control"
msgstr "การควบคุมราคา"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Price Unit"
msgstr "ราคาต่อหน่วย"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr "ลดราคาจาก %s -> %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Price excl. VAT:"
msgstr "ราคาไม่รวม ภาษีมูลค่าเพิ่ม:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#, python-format
msgid "Price list"
msgstr "รายการราคา"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "ราคาสินค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "รายการราคา"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "รายการราคา"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "ราคา"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "พิมพ์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ControlButtons/ReprintReceiptButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#, python-format
msgid "Print Receipt"
msgstr "พิมพ์ใบเสร็จรับเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr "พิมพ์รายงานยอดขายทั้งหมดของเซสชัน PoS ปัจจุบัน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print invoices on customer request"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "พิมพ์ใบเสร็จรับเงินโดยอัตโนมัติเมื่อลงทะเบียนการชำระเงินแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "พิมพ์ผ่าน Proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Printer"
msgstr "เครื่องพิมพ์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "ไม่รองรับการพิมพ์บนเบราว์เซอร์บางตัว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid ""
"Printing is not supported on some browsers due to no default printing "
"protocol is available. It is possible to print your tickets by making use of"
" an IoT Box."
msgstr ""
"ไม่รองรับการพิมพ์บนเบราว์เซอร์บางตัวเนื่องจากไม่มีโปรโตคอลการพิมพ์เริ่มต้น "
"เป็นไปได้ที่จะพิมพ์ทิกเก็ตของคุณโดยใช้กล่องไอโอที"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "กลุ่มจัดซื้อ"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "สินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "หมวดหมู่สินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_configurator
msgid "Product Configurator"
msgstr "การกำหนดค่าสินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_load_background
msgid "Product Load Background"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product Prices"
msgstr "ราคาสินค้า"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "สินค้า หมวดหมู่สินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "ปริมาณสินค้า"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "รูปแบบสินค้า"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "หน่วยวัดสินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "หน่วยวัดสินค้า"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "หมวดของหน่วยวัดของสินค้า"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "แบบที่ต่างกันของสินค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Product information"
msgstr "ข้อมูลสินค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid ""
"Product is not loaded. Tried loading the product from the server but there "
"is a network error."
msgstr ""
"สินค้าไม่ได้โหลด พยายามโหลดสินค้าจากเซิร์ฟเวอร์ "
"แต่มีข้อผิดพลาดของเครือข่ายอินเตอร์เน็ต"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product prices on receipts"
msgstr "ราคาสินค้าในใบเสร็จรับเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
msgid "Product tips"
msgstr "เคล็ดลับสินค้า"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "สินค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Connected"
msgstr "เชื่อมต่อพร็อกซีแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr "ยกเลิกการเชื่อมต่อพร็อกซีแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Warning"
msgstr "คำเตือนพร็อกซี"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Qty"
msgstr "ปริมาณ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "จำนวน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "REASON"
msgstr "เหตุผล"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr "อ่านเครื่องชั่งน้ำหนัก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr "พร้อมที่จะเปิดตัว <b>การขายหน้าร้าน</b> ของคุณแล้วหรือยัง?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Reason"
msgstr "เหตุผล"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Receipt"
msgstr "การรับ"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr "ใบเสร็จ %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
msgid "Receipt Footer"
msgstr "ท้ายใบเสร็จ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
msgid "Receipt Header"
msgstr "หัวใบเสร็จ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr "หมายเลขใบเสร็จ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Receipt Printer"
msgstr "เครื่องพิมพ์ใบเสร็จ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "บันทึกการชำระเงินด้วยเครื่องเทอร์มินัลในสมุดรายวันนี้"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "เซสชันการกู้คืน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Refresh Display"
msgstr "รีเฟรชจอแสดงผล"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Refund"
msgstr "คืนเงิน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr "รายการใบสั่งคืนเงิน"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr "การคืนเงินคำสั่งซื้อ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#, python-format
msgid "Refunded"
msgstr "คืนเงินแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr "คำสั่งซื้อที่คืนเงินแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr "รายการคำสั่งซื้อที่คืนเงินแล้ว"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr "คำสั่งซื้อที่คืนเงินแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr "จำนวนคำสั่งซื้อที่คืนเงินแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr "จำนวนที่คืนเงินแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "Refunding %s in "
msgstr "กำลังคืนเงิน %s ใน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "การคืนเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Remaining"
msgstr "เหลืออยู่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr "คำสั่งซื้อที่ไม่ซิงค์ที่เหลืออยู่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Remove"
msgstr "ลบออก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Replenishment"
msgstr "การเติมสินค้า"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "การรายงาน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Reprint Invoice"
msgstr "พิมพ์ใบแจ้งหนี้อีกครั้ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Request sent"
msgstr "ส่งคำขอแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Reset"
msgstr "คืนค่าเดิม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "จำกัดการแก้ไขราคาเฉพาะผู้จัดการ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
msgid "Restrict Product Categories"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Restrict price modification to managers"
msgstr "จำกัดการแก้ไขราคาให้กับผู้จัดการ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Retry"
msgstr "ลองใหม่"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "คืนสินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "คืนแล้ว"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "การกลับรายการของ:%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr "คำขอกลับรายการส่งไปยังเทอร์มินัล"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse"
msgstr "รายการที่บันทึกในสมุดบัญชี"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "ชำระเงินย้อนหลัง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Review"
msgstr "รีวิว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Rounding"
msgstr "การปัดเศษ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Rounding Method"
msgstr "Rounding Factor"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr "ข้อผิดพลาดในการปัดเศษในรายการการชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr "เรียกใช้การทดสอบ JS ระบบการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "SN"
msgstr "SN"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "จำนวนรายการขาย"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "รายการขาย"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "รายละเอียดการขาย"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sales Journal"
msgstr "สมุดรายวันการขาย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Save"
msgstr "บันทึก"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Save this page and come back here to set up the feature."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scale"
msgstr "เครื่องชั่ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan"
msgstr "สแกน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr "สแกน EAN-13"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "สแกนผ่าน Proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scanner"
msgstr "เครื่องสแกน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Search Customers"
msgstr "ค้นหาลูกค้า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Search Orders..."
msgstr "ค้นหาคำสั่งซื้อ..."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidgetControlPanel.xml:0
#, python-format
msgid "Search Products..."
msgstr "ค้นหาสินค้า..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "ค้นหารายการขาย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#, python-format
msgid "Select"
msgstr "เลือก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr "เลือกสถานะทางการเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select an order"
msgstr "เลือกคำสั่งซื้อ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Select either Cash In or Cash Out before confirming."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Select product attributes"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/HomeCategoryBreadcrumb.js:0
#, python-format
msgid "Select the category"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#, python-format
msgid "Select the pricelist"
msgstr "เลือกรายการราคา"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr "เลือกสินค้าที่จะคืนเงินและกำหนดจำนวน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__selectable_categ_ids
msgid "Selectable Categ"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sell products and deliver them later."
msgstr "ขายสินค้าและส่งมอบในภายหลัง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send"
msgstr "ส่ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send Payment Request"
msgstr "ส่งคำขอการชำระเงิน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Send by email"
msgstr "ส่งทางอีเมล"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr "การส่งอีเมลล้มเหลว กรุณาลองอีกครั้ง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "เลขลำดับ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr "หมายเลขซีเรียล/ล็อต"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "ให้บริการโดย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Server Error"
msgstr "เซิร์ฟเวอร์ผิดพลาด"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr "วาระการขาย"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "วาระการขาย"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "รายการสมุดรายวันเซสชัน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Session ids:"
msgstr "วาระการขาย:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Session is closed"
msgstr ""

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "เซสชั่นเปิดมากกว่า 7 วัน"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "วาระการขาย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Set Customer"
msgstr "ตั้งลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
msgid "Set Maximum Difference"
msgstr "ตั้งค่าความแตกต่างสูงสุด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Set Weight"
msgstr "ตั้งน้ำหนัก"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr ""
"กำหนดความแตกต่างสูงสุดที่อนุญาตระหว่างจำนวนเงินที่คาดหวังและที่นับได้ในระหว่างการปิดเซสชั่น"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr ""
"กำหนดความแตกต่างสูงสุดที่อนุญาตระหว่างจำนวนเงินที่คาดหวังและที่นับได้ในระหว่างการปิดเซสชั่น"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#, python-format
msgid "Set fiscal position"
msgstr "กำหนดสถานะทางการเงิน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "ตั้งราคาหลายรายการต่อผลิตภัณฑ์ ส่วนลดอัตโนมัติ ฯลฯ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set of coins/bills that will be used in opening and closing control"
msgstr "ชุดเหรียญ/ธนบัตร ที่จะใช้ควบคุมการเปิดปิด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set shop-specific prices, seasonal discounts, etc."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Set the new quantity"
msgstr "กำหนดปริมาณใหม่"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Settings"
msgstr "ตั้งค่า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Ship Later"
msgstr "จัดส่งในภายหลัง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
msgid "Shipping Policy"
msgstr "นโยบายการจัดส่ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Shopping cart"
msgstr "รถเข็นสินค้า"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "แสดงการชำระเงินให้กับลูกค้าด้วยหน้าจอที่เชื่อมต่อจากระยะไกล"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Show customers checkout in a pop-up window. Can be moved to a second screen."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid ""
"Show customers checkout in a pop-up window. Recommend to be moved to a "
"second screen visible to the client."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
msgid "Show employee login screen"
msgstr "แสดงหน้าจอเข้าสู่ระบบของพนักงาน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr "หก"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr "Six เทอร์มินัลการชำระเงิน"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr "ขนาด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#, python-format
msgid "Skip"
msgstr "ข้าม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "ข้ามหน้าจอแสดงตัวอย่าง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryBreadcrumb.xml:0
#, python-format
msgid "Slash"
msgstr "Slash"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.small_shelf
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "ชั้นวางขนาดเล็ก"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Some Cash Registers are already posted. Please reset them to new in order to close the session.\n"
"Cash Registers: %r"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr "หมายเลขซีเรียล/ล็อตบางส่วนหายไป"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"ไม่สามารถส่งคำสั่งซื้อบางรายการไปยังเซิร์ฟเวอร์ได้เนื่องจากข้อผิดพลาดในการกำหนดค่า"
" คุณสามารถออกจากระบบขายหน้าร้านได้ "
"แต่อย่าปิดเซสชันก่อนที่ปัญหาจะได้รับการแก้ไข"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"ไม่สามารถส่งคำสั่งซื้อบางรายการไปยังเซิร์ฟเวอร์ได้เนื่องจากปัญหาการเชื่อมต่ออินเทอร์เน็ต"
" คุณสามารถออกจากระบบขายหน้าร้านได้ "
"แต่อย่าปิดเซสชันก่อนที่ปัญหาจะได้รับการแก้ไข"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr ""
"การประมวลผลภายหลังการซิงค์คำสั่งซื้อบางส่วนอาจล้มเหลวทั้งหมด "
"(หากไม่ใช่ทั้งหมด)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Specific route"
msgstr "เส้นทางเฉพาะ"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr "ระบุล็อตสินค้า/หมายเลขซีเรียลในรายการใบสั่งซื้อ POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
msgid "Spefic route for products delivered later."
msgstr "เส้นทางเฉพาะสำหรับสินค้าที่จัดส่งในภายหลัง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
msgid "Start Category"
msgstr "เริ่มหมวดหมู่"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Start selling from a default product category"
msgstr "เริ่มขายจากหมวดหมู่สินค้าเริ่มต้น"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "ยอดเงินเริ่มต้น"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "State"
msgstr "สถานะ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "สถานะ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "ย้ายสต็อก"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "กฎสต๊อก"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr "ควรอัพเดตสต็อกเมื่อจะทำการปิด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Street"
msgstr "ที่อยู่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
#, python-format
msgid "Subtotal"
msgstr "รวม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "ยอดไม่รวมภาษี"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "ยอดยังไม่ลด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Successfully imported"
msgstr "นำเข้าเรียบร้อยแล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr "สร้างเงินสด %s จาก %s สำเร็จแล้ว"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Sum of opening balance and transactions."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "ผลรวมของยอดรวมย่อย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr "เชื่อมต่อการซิงโครไนซ์แล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr "กำลังเชื่อมต่อการซิงโครไนซ์แล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr "ยกเลิกการเชื่อมต่อการซิงโครไนซ์แล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr "เกิดข้อผิดพลาดในการซิงโครไนซ์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "TOTAL"
msgstr "ทั้งหมด"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_out
msgid "Take Money In/Out"
msgstr "นำเงินออก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "ภาษี"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "ยอดภาษี"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
msgid "Tax Display"
msgstr "แสดงภาษี"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Tax ID"
msgstr "เลขประจำตัวผู้เสียภาษี"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime
msgid "Tax Regime"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "ค่าการเลือกระบบภาษี"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "ราคาไม่รวมภาษี"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "ราคารวมภาษี"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "ภาษี"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "ภาษีที่จะใช้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Taxes:"
msgstr "ภาษี :"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid ""
"Technical field which is used to hide use_payment_terminal when no payment "
"interfaces are installed."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "โทร:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#, python-format
msgid ""
"The Point of Sale could not find any product, client, employee or action "
"associated with the scanned barcode."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr ""
"ระบบขายหน้าร้านรองรับเฉพาะกลยุทธ์การปัดเศษแบบ \"เพิ่มรายการปัดเศษ\" เท่านั้น"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr ""
"จำนวนเงินต้องไม่สูงกว่าจำนวนเงินที่ครบกำหนด "
"หากคุณไม่ได้กำหนดค่าวิธีการชำระเงินด้วยเงินสด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount of your payment lines must be rounded to validate the "
"transaction."
msgstr ""
"ยอดเงินของรายการการชำระเงินของคุณต้องถูกปัดเศษเพื่อตรวจสอบความถูกต้องของธุรกรรม"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr ""
"กลยุทธ์การปัดเศษเงินสดของระบบขายหน้าร้าน %(pos)s ต้องเป็น: '%(value)s'"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr "รายการราคาเริ่มต้นจะต้องรวมอยู่ในรายการราคาที่มีอยู่"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr "รายการราคาเริ่มต้นจะต้องไม่เป็นของบริษัทหรือระบบการขายหน้าร้านใด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The fiscal data module encountered an error while receiving your order."
msgstr "โมดูลข้อมูลทางการเงินเกิดข้อผิดพลาดขณะรับคำสั่งซื้อของคุณ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr ""
"สถานะทางบัญชีที่ใช้ในใบสั่งเดิมไม่ได้ถูกโหลด "
"ตรวจสอบให้แน่ใจว่าได้โหลดแล้วโดยเพิ่มลงในการกำหนดค่า POS"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""
"ชื่อโฮสต์หรือที่อยู่ IP ของพร็อกซีฮาร์ดแวร์ "
"จะถูกตรวจจับอัตโนมัติหากเว้นว่างไว้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"สมุดรายวันใบแจ้งหนี้จะต้องอยู่ในสกุลเงินเดียวกันกับสมุดรายวันการขายหรือสกุลเงินของบริษัท"
" ถ้าไม่ได้ถูกตั้งค่าไว้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal of the point of sale %s must belong to the same company."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "The maximum difference allowed is"
msgstr ""

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique !"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "จำนวนใบสั่งขายหน้าร้านที่เกี่ยวข้องกับลูกค้ารายนี้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr ""
"ไม่สามารถส่งคำสั่งซื้อไปยังเซิร์ฟเวอร์ได้ "
"เนื่องจากข้อผิดพลาดที่ไม่ทราบสาเหตุ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr ""
"คำสั่งซื้อได้รับการซิงโครไนซ์ก่อนหน้านี้ "
"โปรดสร้างใบแจ้งหนี้จากส่วนหลังสำหรับคำสั่งซื้อ:"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr "ไม่อนุญาตให้ใช้วิธีการชำระเงินที่เลือกในการกำหนดค่าของเซสชัน POS"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr "วิธีการชำระเงินสำหรับการขายหน้าร้าน %s ต้องเป็นของบริษัท"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,help:point_of_sale.field_pos_session__config_id
msgid "The physical point of sale you will use."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr ""
"โปรแกรมจุดขายจะแสดงสินค้าในหมวดหมู่นี้ก่อนในหน้าแรก \n"
"หากไม่ระบุจะแสดงสินค้าทุกชนิดแทน"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"ระบบการขายหน้าร้านจะแสดงเฉพาะสินค้าที่อยู่ในแผนผังหมวดหมู่ที่เลือกเท่านั้น "
"หากไม่มีการระบุหมวดหมู่ สินค้าที่มีอยู่ทั้งหมดจะแสดงขึ้น"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_display_categ_images
msgid "The product categories will be displayed with pictures."
msgstr "หมวดหมู่ของสินค้าจะมีภาพประกอบ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "อัตราของสกุลเงินต่ออีกสกุลเงินของอัตราที่ใช้ในวันที่สั่งซื้อ"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""
"หน้าพิมพ์ใบเสร็จจะถูกข้ามหากใบเสร็จสามารถพิมพ์อัตโนมัติได้ \n"
"ดู การตั้งค่า \"พิมพ์ใบเสร็จอัตโนมัติ\""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "ใบเสร็จจะถูกพิมพ์โดยอัตโนมัติเมื่อสิ้นสุดการสั่งซื้อแต่ละครั้ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr ""
"ปริมาณที่ขอคืนเงินสูงกว่าปริมาณที่สั่งซื้อ มีการร้องขอ %s "
"ในขณะที่สามารถคืนเงินได้เพียง %s เท่านั้น"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the refundable quantity"
" of %s."
msgstr "ปริมาณที่ขอคืนเงินสูงกว่าปริมาณที่สามารถขอคืนได้ %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The sales journal of the point of sale %s must belong to its company."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr "ลูกค้าที่เลือกจำเป็นต้องมีที่อยู่"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr "รายการราคาเริ่มต้นจะต้องไม่เป็นของบริษัทหรือระบบการขายหน้าร้านใด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "เซิร์ฟเวอร์พบข้อผิดพลาดขณะรับคำสั่งซื้อของคุณ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr "เซสชั่นนี้ถูกเปิดมาเป็นเวลานานผิดปกติ โปรดพิจารณาในการปิด"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""
"ธุรกรรมได้รับการประมวลผลโดย Adyen ตั้งค่าข้อมูลประจำตัว Adyen "
"ของคุณเกี่ยวกับวิธีการชำระเงินที่เกี่ยวข้อง"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr ""
"ธุรกรรมได้รับการประมวลผลโดย Six ตั้งค่าที่อยู่ IP "
"ของเครื่องเทอร์มินัลเกี่ยวกับวิธีการชำระเงินที่เกี่ยวข้อง"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr ""
"ธุรกรรมได้รับการประมวลผลโดย Vantiv ตั้งค่าข้อมูลประจำตัว Vantiv "
"ของคุณเกี่ยวกับวิธีการชำระเงินที่เกี่ยวข้อง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "ยอดปิดบัญชีตามทฤษฎี"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "There are"
msgstr "มี"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "There are no products in this category."
msgstr "ไม่มีสินค้าในหมวดนี้."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"ยังคงมีคำสั่งซื้ออยู่ในสถานะร่างบนเซสชัน ชำระเงินหรือยกเลิกคำสั่งซื้อต่อไปนี้เพื่อตรวจสอบเซสชัน:\n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr "มีคำสั่งซื้อที่ไม่ซิงค์กัน คุณต้องการซิงค์คำสั่งซื้อเหล่านี้หรือไม่"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""
"มีความแตกต่างระหว่างจำนวนเงินที่จะผ่านรายการและจำนวนใบสั่ง "
"ซึ่งอาจเกิดจากการเปลี่ยนแปลงการตั้งค่าการกำหนดค่าทางบัญชีหรือภาษี"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr "มีการชำระเงินทางอิเล็กทรอนิกส์อยู่ในระหว่างดำเนินการ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is at least one pending electronic payment.\n"
"Please finish the payment with the terminal or cancel it then remove the payment line."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr ""
"ไม่มีการกำหนดค่าผังบัญชีในบริษัท "
"โปรดไปที่การตั้งค่าการออกใบแจ้งหนี้เพื่อติดตั้งผังบัญชี"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please add a cash payment method in the point of sale configuration."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"จุดขายนี้ยังไม่มีการชำระเงินที่รองรับการทอนเงิน\n"
"\n"
"กรุณารับชำระให้ตรงกับยอดสินค้า ถ้าไม่เช่นนั้นให้เพิ่มการชำระเงินสดในจุดขายนี้"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_box.py:0
#, python-format
msgid "There is no cash register for this PoS Session"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr "ไม่มีเครื่องบันทึกเงินสดในเซสชั่นนี้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr ""
"ต้องมีสินค้าอย่างน้อยหนึ่งรายการในคำสั่งซื้อของคุณก่อนจึงจะสามารถตรวจสอบและออกใบแจ้งหนี้ได้"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"This account is used as intermediary account when nothing is set in a "
"payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"ฟิลด์นี้แสดงให้เห็นถึงผลต่างสูงสุดที่อนุญาตระหว่างยอดคงเหลือสิ้นสุดและเงินสดตามทฤษฎีเมื่อปิดเซสชันแล้วสำหรับผู้จัดการที่ไม่ใช่"
" POS หากถึงค่าสูงสุดนี้ "
"ผู้ใช้จะได้รับข้อความแสดงข้อผิดพลาดเมื่อปิดเซสชันโดยแจ้งว่าเขาจำเป็นต้องติดต่อกับผู้จัดการของเขา"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr ""
"ช่องนี้มีไว้เพื่อส่งรหัสของกลุ่มผู้จัดการ POS ไปยังลูกค้าบนการขายหน้าร้าน"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr ""
"ฟิลด์นี้มีไว้เพื่อส่ง ID ของกลุ่มผู้ใช้ POS ไปยังลูกค้าบนการขายหน้าร้าน"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"This invoice has been created from the point of sale session: <a href=# "
"data-oe-model=pos.order data-oe-id=%d>%s</a>"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__customer_note
msgid "This is a note destined to the customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "This is a technical field used for the domain of pricelist_id."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"สิ่งนี้มีประโยชน์สำหรับร้านอาหารที่มีบริการนอกสถานที่และบริการแบบซื้อกลับบ้านซึ่งระบุถึงอัตราภาษีที่เฉพาะเจาะจง"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr ""
"สมุดรายวันนี้เชื่อมโยงกับวิธีการชำระเงิน คุณไม่สามารถแก้ไขประเภทของมันได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""
"การดำเนินการนี้จะทำลายคำสั่งซื้อที่ค้างชำระทั้งหมดในเบราว์เซอร์ "
"คุณจะสูญเสียข้อมูลที่ยังไม่ได้บันทึกทั้งหมดและออกจากระบบการขายหน้าร้าน "
"การดำเนินการนี้ไม่สามารถยกเลิกได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"การดำเนินการนี้จะทำลายคำสั่งซื้อที่ชำระเงินทั้งหมดจากที่จัดเก็บในตัวเครื่องอย่างถาวร"
" คุณจะสูญเสียข้อมูลทั้งหมดและการดำเนินการนี้ไม่สามารถยกเลิกได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""
"คำสั่งซื้อนี้มีรายการการคืนเงินสำหรับ %s แล้ว "
"เราไม่สามารถเปลี่ยนลูกค้าที่เกี่ยวข้องได้ "
"สร้างคำสั่งซื้อใหม่สำหรับลูกค้าใหม่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#, python-format
msgid "This order is empty"
msgstr "คำสั่งซื้อนี้ว่างเปล่า"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"คำสั่งนี้ยังไม่ได้ซิงค์กับเซิร์ฟเวอร์ "
"ตรวจสอบให้แน่ใจว่ามีการซิงค์แล้วลองอีกครั้ง"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "This product is used as reference on customer receipts."
msgstr "สินค้านี้ใช้เป็นข้อมูลอ้างอิงในใบเสร็จรับเงินของลูกค้า"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""
"ลำดับนี้ถูกสร้างขึ้นโดยอัตโนมัติโดย Odoo "
"แต่คุณสามารถเปลี่ยนเพื่อกำหนดหมายเลขอ้างอิงของรายการคำสั่งซื้อของคุณได้"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"ลำดับนี้ถูกสร้างขึ้นโดยอัตโนมัติจาก Odoo "
"แต่คุณสามารถเปลี่ยนเพื่อปรับแต่งหมายเลขอ้างอิงของคำสั่งซื้อของคุณได้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr "เซสชันนี้ถูกปิดแล้ว"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr "ภาษีนี้ใช้กับสินค้าใหม่ที่สร้างขึ้นในแค็ตตาล็อก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Tip"
msgstr "ทิป"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr "จำนวนทิป"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tip Product"
msgstr "สินค้า\"ทิป\""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_tip
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tips"
msgstr "เคล็ดลับ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr "เคล็ดลับ:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "จะปิด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "To Pay"
msgstr "ค้างจ่าย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "To Refund: %s"
msgstr "จำนวนเงินที่จะคืน: %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "การชั่งน้ำหนักด้วยเครื่องชั่ง"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "ออกใบแจ้งหนี้"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "หากต้องการบันทึกคำสั่งซื้อใหม่ ให้เริ่มเซสชันใหม่"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "หากต้องการคืนสินค้า คุณต้องเปิดเซสชั่นใน POS %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_ship
msgid "To ship"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "รวม"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "ธุรกรรมเงินสดทั้งหมด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Cost:"
msgstr "ค่าใช้จ่ายทั้งหมด:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "ส่วนลดทั้งหมด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Total Due"
msgstr "ครบกำหนดชำระทั้งหมด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Margin:"
msgstr "กำไรทั้งหมด:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "ชำระแล้วทั้งหมด (มีการปัดเศษ)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "จำนวนเงินที่ชำระทั้งหมด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "ราคารวม"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Price excl. VAT:"
msgstr "ราคารวม ไม่รวมภาษีมูลค่าเพิ่ม:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Total Taxes"
msgstr "ภาษีทั้งหมด"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "จำนวนเงินที่ชำระทั้งหมด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr "ค่าใช้จ่ายทั้งหมด"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total of all paid sales orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Total of closing cash control lines."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Total of opening cash control lines."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "จำนวนรวม"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "ทั้งหมด:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "Balance as calculated based on Opening Balance and transaction lines"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr "ธุรกรรมถูกยกเลิก"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "การโอน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
msgid "Type"
msgstr "ประเภท"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "ประเภทของบัตรที่ใช้"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "พิมพ์กิจกรรมข้อยกเว้นบนบันทึก"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"ไม่สามารถปิดและตรวจสอบเซสชันได้\n"
"โปรดตั้งค่าบัญชีภาษีที่เกี่ยวข้องในแต่ละรายการการแบ่งส่วนของภาษีต่อไปนี้:\n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to download invoice."
msgstr "ไม่สามารถดาวน์โหลดใบแจ้งหนี้ได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to invoice order."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr ""
"ไม่สามารถแก้ไขการกำหนดค่า PoS นี้ได้ เนื่องจากคุณไม่สามารถแก้ไข %s "
"ในขณะที่เซสชันเปิดอยู่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Unable to save changes."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr "ไม่สามารถแสดงข้อมูลเกี่ยวกับข้อผิดพลาดนี้ได้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr "ไม่สามารถซิงค์คำสั่งซื้อได้"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "ราคาต่อหน่วย"

#. module: point_of_sale
#: model:product.product,uom_name:point_of_sale.desk_organizer
#: model:product.product,uom_name:point_of_sale.desk_pad
#: model:product.product,uom_name:point_of_sale.led_lamp
#: model:product.product,uom_name:point_of_sale.letter_tray
#: model:product.product,uom_name:point_of_sale.magnetic_board
#: model:product.product,uom_name:point_of_sale.monitor_stand
#: model:product.product,uom_name:point_of_sale.newspaper_rack
#: model:product.product,uom_name:point_of_sale.product_product_consumable
#: model:product.product,uom_name:point_of_sale.product_product_tip
#: model:product.product,uom_name:point_of_sale.small_shelf
#: model:product.product,uom_name:point_of_sale.wall_shelf
#: model:product.product,uom_name:point_of_sale.whiteboard
#: model:product.product,uom_name:point_of_sale.whiteboard_pen
#: model:product.template,uom_name:point_of_sale.desk_organizer_product_template
#: model:product.template,uom_name:point_of_sale.desk_pad_product_template
#: model:product.template,uom_name:point_of_sale.led_lamp_product_template
#: model:product.template,uom_name:point_of_sale.letter_tray_product_template
#: model:product.template,uom_name:point_of_sale.magnetic_board_product_template
#: model:product.template,uom_name:point_of_sale.monitor_stand_product_template
#: model:product.template,uom_name:point_of_sale.newspaper_rack_product_template
#: model:product.template,uom_name:point_of_sale.product_product_consumable_product_template
#: model:product.template,uom_name:point_of_sale.product_product_tip_product_template
#: model:product.template,uom_name:point_of_sale.small_shelf_product_template
#: model:product.template,uom_name:point_of_sale.wall_shelf_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_pen_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_product_template
msgid "Units"
msgstr "หน่วย"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#, python-format
msgid "Unknown Barcode"
msgstr "ไม่ทราบบาร์โค้ด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr "ไม่ทราบข้อผิดพลาด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Unsupported File Format"
msgstr "รูปแบบไฟล์ที่ไม่รองรับ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr "คำสั่งที่ยังไม่ได้ซิงค์"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "หน่วยวัด"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr "อัปเดตจำนวนในสต็อก"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "ใช้เทอร์มินัลการชำระเงิน"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use a default specific tax regime"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
msgid "Use a pricelist."
msgstr "ใช้รายการราคา"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr "ใช้บาร์โค้ดเพื่อสแกนสินค้า บัตรลูกค้า และอื่นๆ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Use employee credentials to log in to the PoS session and switch cashier"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "ผู้ใช้งาน"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "ป้ายกำกับผู้ใช้"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "ภาษี:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Valid product lot"
msgstr "ล็อตสินค้าที่ถูกต้อง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Validate"
msgstr "ตรวจสอบ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr "Vantiv (สหรัฐอเมริกาและแคนาดา)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr "เทอร์มินัลการชำระเงิน Vantiv"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Waiting for card"
msgstr "รอรับบัตร"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.wall_shelf
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "ชั้นวางของติดผนัง"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Warehouse"
msgstr "คลังสินค้า"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Weighing"
msgstr "การชั่งน้ำหนัก"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "น้ำหนักสินค้า"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "เมื่อสินค้าทุกอย่างพร้อม"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"เมื่อใดก็ตามที่คุณปิดเซสชัน "
"รายการหนึ่งรายการจะถูกสร้างขึ้นในสมุดรายวันการบัญชีต่อไปนี้สำหรับใบสั่งทั้งหมดที่ไม่ได้รับการออกใบแจ้งหนี้"
" ใบแจ้งหนี้จะถูกบันทึกในการบัญชีแยกต่างหาก"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "ไวท์บอร์ด"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard_pen
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "ปากกาไวท์บอร์ด"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "With a"
msgstr "กับ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Would you like to load demo data?"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr "ใช่"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"คุณไม่ได้รับอนุญาตให้เปลี่ยนการกำหนดค่าการปัดเศษเงินสดในขณะที่เซสชั่น POS "
"ที่ใช้งานนั้นเปิดอยู่แล้ว"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr "คุณไม่ได้รับอนุญาตให้เปลี่ยนแปลงปริมาณนี้"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""
"คุณกำลังพยายามขายสินค้าที่มีหมายเลขซีเรียล/ล็อต แต่บางรายการไม่ได้ตั้งค่าไว้\n"
"คุณต้องการดำเนินการต่อหรือไม่?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid ""
"You can't validate a bank statement that is used in an opened Session of a "
"Point of Sale."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""
"คุณไม่สามารถ: สร้างคำสั่งซื้อ POS จากอินเทอร์เฟซแบ็กเอนด์ "
"หรือยกเลิกการตั้งค่ารายการราคา หรือสร้าง pos.order ในการทดสอบ python "
"ด้วยเครื่องมือแบบฟอร์มได้ หรือแก้ไขมุมมองแบบฟอร์มในสตูดิโอหากไม่มีคำสั่งซื้อ"
" PoS อยู่"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""
"คุณไม่สามารถปิด POS ได้ เมื่อไม่มีการลงรายการบัญชีใบแจ้งหนี้\n"
"ใบแจ้งหนี้: %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr "คุณไม่สามารถปิด POS ได้เมื่อใบสั่งยังคงอยู่ในแบบร่าง"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr "คุณไม่สามารถสร้างเซสชันก่อนวันที่ล็อคบัญชีได้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid "You cannot delete a bank statement linked to Point of Sale session."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr "คุณไม่สามารถลบหมวดหมู่การขายหน้าร้านในขณะที่เซสชันยังคงเปิดอยู่ได้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/product.py:0
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid ""
"You cannot delete a product saleable in point of sale while a session is "
"still opened."
msgstr ""
"คุณไม่สามารถลบสินค้าที่ขายในการขายหน้าร้านในขณะที่เซสชันยังคงเปิดอยู่ได้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_partner.py:0
#, python-format
msgid ""
"You cannot delete contacts while there are active PoS sessions. Close the "
"session(s) %s first."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "You do not have any products"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr "คุณไม่มีสิทธิ์เข้าถึงข้อมูลการควบคุมการปิดระบบการขายหน้าร้านได้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You don't have the access rights to set the point of sale cash box."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You have enabled the \"Identify Customer\" option for %s payment method,but "
"the order %s does not contain a customer."
msgstr ""
"คุณได้เปิดใช้งานตัวเลือก \"ระบุลูกค้า\" สำหรับวิธีการชำระเงิน "
"%sแต่คำสั่งซื้อ %s ไม่มีลูกค้า"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid ""
"You have to define which payment method must be available in the point of "
"sale by reusing existing bank and cash through \"Accounting / Configuration "
"/ Journals / Journals\". Select a journal and check the field \"PoS Payment "
"Method\" from the \"Point of Sale\" tab. You can also create new payment "
"methods directly from menu \"PoS Backend / Configuration / Payment "
"Methods\"."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr "คุณต้องปัดเศษรายการการชำระเงินของคุณ ไม่ถูกปัดเศษ"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You have to select a pricelist in the sale form !\n"
"Please set one before choosing a product."
msgstr ""
"คุณต้องเลือกรายการราคาในแบบฟอร์มการขาย !\n"
"โปรดตั้งค่าก่อนเลือกสินค้า"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You have to select a pricelist in the sale form."
msgstr "คุณต้องเลือกรายการราคาในแบบฟอร์มการขาย"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"คุณต้องระบุสินค้าทุกรายการที่จะขายผ่านการขายผ่านอินเทอร์เฟซการขายหน้าร้าน"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr "คุณต้องมีการกำหนดค่าวิธีการชำระเงินอย่างน้อยหนึ่งวิธีเพื่อเริ่มเซสชัน"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr "คุณต้องมีบัญชีขาดทุนและกำไรบนบันทึกเงินสดของคุณ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr "คุณต้องเลือกลูกค้าก่อนจึงจะสามารถออกใบแจ้งหนี้หรือจัดส่งคำสั่งได้"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "คุณต้องมอบหมายการขายหน้าร้านสำหรับเซสชันของคุณ"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr ""
"เซสชัน PoS ของคุณเปิดอยู่ตั้งแต่ %(date)sเราขอแนะนำให้คุณปิดและสร้างใหม่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "ZIP"
msgstr "รหัสไปรษณีย์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "at"
msgstr "ที่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "available,"
msgstr "มีอยู่"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "belong to another session:"
msgstr "เป็นของเซสชั่นอื่น:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "discount"
msgstr "ส่วนลด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr "เช่น เงินสด"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Company Address, Website"
msgstr "เช่น ที่อยู่บริษัท เว็บไซต์"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr "เช่น NYC Shop"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "เช่น นโยบายการคืนสินค้า ขอขอบคุณที่ซื้อสินค้ากับเรา!"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "เช่น น้ำอัดลม"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "for an order of"
msgstr "สำหรับคำสั่งของ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "forecasted"
msgstr "พยากรณ์"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "in"
msgstr "ใน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#, python-format
msgid "items"
msgstr "รายการ"

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "โน้ต"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "open sessions"
msgstr "เปิดเซสชั่น"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "orders"
msgstr "คำสั่ง"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "out"
msgstr "ออก"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "paid orders"
msgstr "คำสั่งที่ชำระเงินแล้ว"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr "ส่งคืน"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders"
msgstr "คำสั่งที่ค้างชำระ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr "ไม่สามารถนำเข้าคำสั่งที่ค้างชำระได้"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr "โดยใช้"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_products_loading
msgid ""
"we load all starred products (favorite), all services, recent inventory movements of products, and the most recently updated products.\n"
"When the session is open, we keep on loading all remaining products in the background.\n"
"In the meantime, you can click on the 'database icon' in the searchbar to load products from database."
msgstr ""
"เราโหลดผลิตภัณฑ์ติดดาวทั้งหมด (รายการโปรด) บริการทั้งหมด การเคลื่อนย้ายสินค้าคงคลังของผลิตภัณฑ์ล่าสุด และผลิตภัณฑ์ที่อัปเดตล่าสุด\n"
"เมื่อเปิดเซสชั่น เราจะทำการโหลดผลิตภัณฑ์ที่เหลืออยู่ทั้งหมดในเบื้องหลัง\n"
"ในระหว่างนี้ คุณสามารถคลิกที่ 'ไอคอนฐานข้อมูล' ในแถบค้นหาเพื่อโหลดผลิตภัณฑ์จากฐานข้อมูล"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr "ซ้ำกับคำสั่งที่มีอยู่"
