<?xml version="1.0"?>
<odoo>

        <!-- Partner kanban view inherit -->
        <record id="crm_lead_partner_kanban_view" model="ir.ui.view">
            <field name="name">res.partner.kanban.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.res_partner_kanban_view"/>
            <field name="priority" eval="10"/>
            <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
            <field name="arch" type="xml">
                <field name="mobile" position="after">
                    <field name="opportunity_count"/>
                </field>
                <xpath expr="//span[hasclass('oe_kanban_partner_links')]" position="inside">
                    <span class="badge badge-pill" t-if="record.opportunity_count.value>0">
                        <i class="fa fa-fw fa-star" aria-label="Opportunities" role="img" title="Opportunities"/><t t-esc="record.opportunity_count.value"/>
                    </span>
                </xpath>
            </field>
        </record>

        <!-- Add contextual button on partner form view -->
        <record id="view_partners_form_crm1" model="ir.ui.view">
            <field name="name">view.res.partner.form.crm.inherited1</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field eval="1" name="priority"/>
            <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
            <field name="arch" type="xml">
                <data>
                    <div name="button_box" position="inside">
                        <button class="oe_stat_button o_res_partner_tip_opp" type="object"
                            name="action_view_opportunity"
                            icon="fa-star"
                            groups="sales_team.group_sale_salesman"
                            context="{'default_partner_id': active_id, 'default_type':'opportunity'}">
                            <field string="Opportunities" name="opportunity_count" widget="statinfo"/>
                        </button>
                    </div>
                </data>
            </field>
        </record>

</odoo>
