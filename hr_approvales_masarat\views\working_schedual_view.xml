<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <record id="resource_calendar_form_x1" model="ir.ui.view">
        <field name="name">resource.calendar.form.ef</field>
        <field name="model">resource.calendar</field>
        <field name="inherit_id" ref="resource.resource_calendar_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='resource_details']" position="after">
                <group>
                    <group>
                        <field name="there_is_letancy"/>
                        <field name="checkin_tolarence"
                               attrs="{'invisible':[('there_is_letancy','!=',True)], 'required':[('there_is_letancy','!=',True)]}"/>
                        <field name="checkout_tolarence"
                               attrs="{'invisible':[('there_is_letancy','!=',True)], 'required':[('there_is_letancy','!=',True)]}"/>
                        <field name="total_working_hour" widget="float_time"
                               attrs="{'invisible':[('there_is_letancy','!=',True)], 'required':[('there_is_letancy','!=',True)]}"/>
                        <field name="penalty"
                               attrs="{'invisible':[('there_is_letancy','!=',True)], 'required':[('there_is_letancy','!=',True)]}"/>
                    </group>
                    <group>
                        <field name="latency_allawence_approved"
                               attrs="{'invisible':[('there_is_letancy','!=',True)], 'required':[('there_is_letancy','!=',True)]}"/>
                        <field name="latency_approval_count"
                               attrs="{'invisible':[('there_is_letancy','!=',True)], 'required':[('there_is_letancy','!=',True)]}"/>
                    </group>
                    <group>
                        <field name="has_half_day_leave_start" attrs="{'invisible':[('there_is_letancy','!=',True)]}"/>
                        <field name="half_day_leave_start" widget="float_time"
                               attrs="{'invisible':[('has_half_day_leave_start','!=',True)], 'required':[('has_half_day_leave_start','=',True)]}"/>
                    </group>
                </group>
            </xpath>
            <xpath expr="//field[@name='attendance_ids']" position="after">
                <group>

                    <group>
                        <field name="custom_calender"/>
                        <field name="custom_calender_start"
                               attrs="{'invisible':[('custom_calender','!=',True)], 'required':[('custom_calender','=',True)]}"/>
                        <field name="custom_calender_end"
                               attrs="{'invisible':[('custom_calender','!=',True)] , 'required':[('custom_calender','=',True)]}"/>
                    </group>
                </group>
                <field name="custom_attendance_ids" widget="section_one2many"
                       attrs="{'invisible':[('custom_calender','!=',True)]}"/>
            </xpath>
        </field>
    </record>
</odoo>