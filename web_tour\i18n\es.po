# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""
"<strong><b>¡Buen trabajo!</b> Pasaste por todos los pasos de este "
"recorrido.</strong>"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Click here to go to the next step."
msgstr "Haga clic aquí para ir al siguiente paso."

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Click on the <i>Home icon</i> to navigate across apps."
msgstr ""
"Haga click en el <i>Icono de Inicio</i> para navegar a través de las "
"aplicaciones."

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_id
msgid "Consumed by"
msgstr "Consumido por"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
#, python-format
msgid "Disable Tours"
msgstr "Desactivar recorridos"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "Ruta HTTP "

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
msgid "ID"
msgstr "ID"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "Menú"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Name"
msgstr "Nombre"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Onboarding tours"
msgstr "Recorridos de incorporación"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_step_utils.js:0
#, python-format
msgid "Open bugger menu."
msgstr "Abrir el menú bugger."

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Path"
msgstr "Ruta"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tip.js:0
#, python-format
msgid "Scroll to reach the next step."
msgstr "Desplácese para llegar al siguiente paso."

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Sequence"
msgstr "Secuencia"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Start"
msgstr "Iniciar"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
#, python-format
msgid "Start Tour"
msgstr "Comenzar recorrido"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Start tour"
msgstr "Comenzar tour"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Test"
msgstr "Test"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Test tour"
msgstr "Recorrido de prueba"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Testing tours"
msgstr "Visitas de prueba"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr "Propina"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Tour name"
msgstr "Nombre del recorrido"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/debug/tour_dialog_component.js:0
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
#, python-format
msgid "Tours"
msgstr "Recorridos"
