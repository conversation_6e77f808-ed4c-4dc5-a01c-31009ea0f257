<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.AttachmentCard" owl="1">
        <div>
            <t t-if="attachmentCard">
                <div class="o_AttachmentCard d-flex o-has-card-details"
                     t-att-class="{
                            'o-downloadable': !attachmentCard.attachmentList.composerView,
                            'o-isUploading': attachmentCard.attachment.isUploading,
                            'o-viewable': attachmentCard.attachment.isViewable,
                            }" t-att-title="attachmentCard.attachment.displayName ? attachmentCard.attachment.displayName : undefined" role="menu" t-att-aria-label="attachmentCard.attachment.displayName" t-att-data-id="attachmentCard.attachment.localId"
                >
                    <!-- Image style-->
                    <!-- o_image from mimetype.scss -->
                    <div class="o_AttachmentCard_image o_image" t-on-click="attachmentCard.onClickImage" t-att-class="{'o-attachment-viewable': attachmentCard.attachment.isViewable,}" role="menuitem" aria-label="Preview" t-att-tabindex="attachmentCard.attachment.isViewable ? 0 : -1" t-att-aria-disabled="!attachmentCard.attachment.isViewable" t-att-data-mimetype="attachmentCard.attachment.mimetype">
                    </div>
                    <!-- Attachment details -->
                    <div class="o_AttachmentCard_details d-flex justify-content-center">
                        <t t-if="attachmentCard.attachment.displayName">
                            <div class="o_AttachmentCard_filename overflow-hidden text-nowrap">
                                <t t-esc="attachmentCard.attachment.displayName"/>
                            </div>
                        </t>
                        <t t-if="attachmentCard.attachment.extension">
                            <div class="o_AttachmentCard_extension text-uppercase">
                                <t t-esc="attachmentCard.attachment.extension"/>
                            </div>
                        </t>
                    </div>
                    <!-- Attachment aside -->
                    <t t-if="(!attachmentCard.attachmentList.composerView or attachmentCard.attachment.isEditable)">
                        <div class="o_AttachmentCard_aside position-relative overflow-hidden" t-att-class="{ 'o-has-multiple-action d-flex flex-column': !attachmentCard.attachmentList.composerView and attachmentCard.attachment.isEditable }">
                            <!-- Uploading icon -->
                            <t t-if="attachmentCard.attachment.isUploading and attachmentCard.attachmentList.composerView">
                                <div class="o_AttachmentCard_asideItem o_AttachmentCard_asideItemUploading d-flex justify-content-center align-items-center" title="Uploading">
                                    <i class="fa fa-spin fa-spinner"/>
                                </div>
                            </t>
                            <!-- Uploaded icon -->
                            <t t-if="!attachmentCard.attachment.isUploading and attachmentCard.attachmentList.composerView">
                                <div class="o_AttachmentCard_asideItem o_AttachmentCard_asideItemUploaded d-flex justify-content-center align-items-center" title="Uploaded">
                                    <i class="fa fa-check"/>
                                </div>
                            </t>
                            <!-- Remove button -->
                            <t t-if="attachmentCard.attachment.isEditable">
                                <div class="o_AttachmentCard_asideItem o_AttachmentCard_asideItemUnlink d-flex justify-content-center align-items-center" t-att-class="{ 'o-pretty position-absolute': attachmentCard.attachmentList.composerView }" tabindex="0" aria-label="Remove" role="menuitem" t-on-click="attachmentCard.onClickUnlink" title="Remove">
                                    <i class="fa fa-trash"/>
                                </div>
                            </t>
                            <!-- Download button -->
                            <t t-if="!attachmentCard.attachmentList.composerView and !attachmentCard.attachment.isUploading">
                                <div class="o_AttachmentCard_asideItem o_AttachmentCard_asideItemDownload d-flex justify-content-center align-items-center" tabindex="0" aria-label="Download" role="menuitem" t-on-click="attachmentCard.attachment.onClickDownload" title="Download">
                                    <i class="fa fa-download"/>
                                </div>
                            </t>
                        </div>
                    </t>
                    <t t-if="attachmentCard.hasDeleteConfirmDialog">
                        <AttachmentDeleteConfirmDialog
                            attachmentLocalId="attachmentCard.attachment.localId"
                            t-on-dialog-closed="attachmentCard.onDeleteConfirmDialogClosed"
                        />
                    </t>
                </div>
            </t>
        </div>
    </t>
</templates>
