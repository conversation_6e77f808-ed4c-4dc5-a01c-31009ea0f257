<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_task_form2_inherit_pad_project" model="ir.ui.view">
        <field name="name">project.task.form.inherit</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project.view_task_form2"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='description']" position="attributes">
                <attribute name="attrs">{'invisible': [('use_pad', '=', True)], 'readonly': [('use_pad', '=', True)]}</attribute>
            </xpath>
            <field name="description" position="after">
                <field name="use_pad" invisible="1"/>
                <field name="description_pad" widget="pad" attrs="{'invisible': [('use_pad', '=', False)], 'readonly': [('use_pad', '=', False)]}"/>
            </field>
        </field>
    </record>

    <record id="project_project_view_form" model="ir.ui.view">
        <field name="name">project.project.view.form</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.edit_project"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='rating_settings']" position="after">
                <div class="col-lg-6 o_setting_box" id="pad_settings">
                    <div class="o_setting_left_pane">
                        <field name="use_pads"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="use_pads" string="Collaborative Pads"/>
                        <div class="text-muted">
                            Edit tasks' description collaboratively in real time.<br/>
                            See each author's text in a distinct color.
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
