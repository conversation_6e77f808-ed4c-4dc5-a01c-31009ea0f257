# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * note
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Chile) (https://www.transifex.com/odoo/teams/41243/es_CL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_open
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Active"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Archive"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "By sticky note Category"
msgstr ""

#. module: note
#: model_terms:ir.actions.act_window,help:note.note_tag_action
msgid "Click to add a new tag."
msgstr ""

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid "Click to add a personal note."
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_color
#: model:ir.model.fields,field_description:note.field_note_tag_color
msgid "Color Index"
msgstr ""

#. module: note
#: model:ir.ui.menu,name:note.menu_note_configuration
msgid "Configuration"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_create_uid
#: model:ir.model.fields,field_description:note.field_note_stage_create_uid
#: model:ir.model.fields,field_description:note.field_note_tag_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_create_date
#: model:ir.model.fields,field_description:note.field_note_stage_create_date
#: model:ir.model.fields,field_description:note.field_note_tag_create_date
msgid "Created on"
msgstr "Creado en"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_date_done
msgid "Date done"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Delete"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_display_name
#: model:ir.model.fields,field_description:note.field_note_stage_display_name
#: model:ir.model.fields,field_description:note.field_note_tag_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage_fold
msgid "Folded by Default"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Future Activities"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_id
#: model:ir.model.fields,field_description:note.field_note_stage_id
#: model:ir.model.fields,field_description:note.field_note_tag_id
msgid "ID"
msgstr "ID (identificación)"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note___last_update
#: model:ir.model.fields,field_description:note.field_note_stage___last_update
#: model:ir.model.fields,field_description:note.field_note_tag___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_write_uid
#: model:ir.model.fields,field_description:note.field_note_stage_write_uid
#: model:ir.model.fields,field_description:note.field_note_tag_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_write_date
#: model:ir.model.fields,field_description:note.field_note_stage_write_date
#: model:ir.model.fields,field_description:note.field_note_tag_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Late Activities"
msgstr ""

#. module: note
#: model:note.stage,name:note.demo_note_stage_03
#: model:note.stage,name:note.note_stage_03
msgid "Later"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "My Activities"
msgstr ""

#. module: note
#: model:note.stage,name:note.note_stage_00
msgid "New"
msgstr "Nueva"

#. module: note
#: model:ir.model,name:note.model_note_note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Note"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_memo
msgid "Note Content"
msgstr ""

#. module: note
#: model:ir.model,name:note.model_note_stage
msgid "Note Stage"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_name
msgid "Note Summary"
msgstr ""

#. module: note
#: model:ir.model,name:note.model_note_tag
msgid "Note Tag"
msgstr ""

#. module: note
#: model:ir.actions.act_window,name:note.action_note_note
#: model:ir.ui.menu,name:note.menu_note_notes
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model:note.stage,name:note.note_stage_04
msgid "Notes"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_user_id
#: model:ir.model.fields,field_description:note.field_note_stage_user_id
msgid "Owner"
msgstr ""

#. module: note
#: model:ir.model.fields,help:note.field_note_stage_user_id
msgid "Owner of the note stage"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_sequence
#: model:ir.model.fields,field_description:note.field_note_stage_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_stage_id
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Stage"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage_name
msgid "Stage Name"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_form
msgid "Stage of Notes"
msgstr ""

#. module: note
#: model:ir.actions.act_window,name:note.action_note_stage
#: model:ir.ui.menu,name:note.menu_notes_stage
#: model_terms:ir.ui.view,arch_db:note.view_note_note_tree
msgid "Stages"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_tree
msgid "Stages of Notes"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_stage_ids
msgid "Stages of Users"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_tag_name
msgid "Tag Name"
msgstr ""

#. module: note
#: sql_constraint:note.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: note
#: model:ir.actions.act_window,name:note.note_tag_action
#: model:ir.model.fields,field_description:note.field_note_note_tag_ids
#: model:ir.ui.menu,name:note.notes_tag_menu
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_form
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_tree
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Tags"
msgstr ""

#. module: note
#: model:note.stage,name:note.note_stage_02
msgid "This Week"
msgstr ""

#. module: note
#: model:note.stage,name:note.demo_note_stage_01
#: model:note.stage,name:note.note_stage_01
msgid "Today"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Today Activities"
msgstr ""

#. module: note
#: model:note.stage,name:note.demo_note_stage_02
msgid "Tomorrow"
msgstr ""

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"Use notes to organize personal tasks or notes. All\n"
"            notes are private; no one else will be able to see them. However\n"
"            you can share some notes with other people by inviting followers\n"
"            on the note. (Useful for meeting minutes, especially if\n"
"            you activate the pad feature for collaborative writings)."
msgstr ""

#. module: note
#: model:ir.model.fields,help:note.field_note_stage_sequence
msgid "Used to order the note stages"
msgstr ""

#. module: note
#: model:ir.model,name:note.model_res_users
msgid "Users"
msgstr ""

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"You can customize how you process your notes/tasks by adding,\n"
"            removing or modifying columns."
msgstr ""
