# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_it_stock_ddt
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-11 14:02+0000\n"
"PO-Revision-Date: 2022-01-11 14:02+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_it_stock_ddt
#: model:ir.actions.report,print_report_name:l10n_it_stock_ddt.action_report_ddt
msgid ""
"'DDT - %s - %s' % (object.partner_id.name or '', object.l10n_it_ddt_number)"
msgstr ""

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<b>Note:</b>"
msgstr "<b>Nota:</b>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<b>Total:</b>"
msgstr "<b>Totale:</b>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<b>Transportation Method Details: </b>"
msgstr "<b>Dettagli metodo di trasporto</b>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<span><strong>Customer Address:</strong></span>"
msgstr "<span><strong>Indirizzo Cliente:</strong></span>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<span><strong>Warehouse Address:</strong></span>"
msgstr "<span><strong>Indirizzo Magazzino:</strong></span>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Carrier Signature</strong>"
msgstr "<strong>Firma Corriere</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Company Signature</strong>"
msgstr "<strong>Firma Azienda</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Customer Signature</strong>"
msgstr "<strong>Firma Cliente</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Product</strong>"
msgstr "<strong>Prodotto</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Quantity</strong>"
msgstr "<strong>Quantità</strong>"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "<strong>Total Value</strong>"
msgstr "<strong>Valore Totale</strong>"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__attemped_sale
msgid "Attempted Sale"
msgstr "Tentata Vendita"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Carrier"
msgstr "Corriere"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Carrier Condition"
msgstr "Termini di Resa"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_country_code
msgid "Country Code"
msgstr "Codice Nazione"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_method__courier
msgid "Courier service"
msgstr "Servizio Corriere"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.view_picking_form_inherit_l10n_it_ddt
msgid "DDT Information"
msgstr "Informazioni DDT"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_ddt_number
msgid "DDT Number"
msgstr "Numero DDT"

#. module: l10n_it_stock_ddt
#: model:ir.actions.report,name:l10n_it_stock_ddt.action_report_ddt
msgid "DDT report"
msgstr "DDT"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.account_invoice_view_form_inherit_ddt
msgid "DDTs"
msgstr "DDT"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__display_name
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking_type__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Documento di Trasporto"
msgstr ""

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__evaluation
msgid "Evaluation"
msgstr "Valutazione"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__gift
msgid "Gift"
msgstr "Regalo"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Gross Weight (kg)"
msgstr "Peso lordo (kg)"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_move__id
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__id
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking_type__id
msgid "ID"
msgstr ""

#. module: l10n_it_stock_ddt
#: model:ir.model,name:l10n_it_stock_ddt.model_account_move
msgid "Journal Entry"
msgstr "Registrazione contabile"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_bank_statement_line__l10n_it_ddt_ids
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_move__l10n_it_ddt_ids
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_payment__l10n_it_ddt_ids
msgid "L10N It Ddt"
msgstr ""

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_bank_statement_line__l10n_it_ddt_count
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_move__l10n_it_ddt_count
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_payment__l10n_it_ddt_count
msgid "L10N It Ddt Count"
msgstr ""

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking_type__l10n_it_ddt_sequence_id
msgid "L10N It Ddt Sequence"
msgstr ""

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_account_move____last_update
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking____last_update
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking_type____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: l10n_it_stock_ddt
#: code:addons/l10n_it_stock_ddt/models/account_invoice.py:0
#, python-format
msgid "Linked deliveries"
msgstr "Spedizioni associate"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__loaned_use
msgid "Loaned for Use"
msgstr "Comodato d'uso"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Order"
msgstr "Ordine"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__outsourcing
msgid "Outsourcing"
msgstr "Conto Lavorazione"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_parcels
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Parcels"
msgstr "Colli"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Picking Number"
msgstr "Numero prelievo"

#. module: l10n_it_stock_ddt
#: model:ir.model,name:l10n_it_stock_ddt.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipologia prelievo"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.view_picking_form_inherit_l10n_it_ddt
msgid "Print"
msgstr "Stampa"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_method__recipient
msgid "Recipient"
msgstr "Destinatario"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__repair
msgid "Repair"
msgstr "Conto Riparazione"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__sale
msgid "Sale"
msgstr "Vendita"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_method__sender
msgid "Sender"
msgstr "Mittente"

#. module: l10n_it_stock_ddt
#: code:addons/l10n_it_stock_ddt/models/stock_picking.py:0
#, python-format
msgid "Sequence"
msgstr "Sequenza"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Shipping Date"
msgstr "Data transporto"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__substitution
msgid "Substitution"
msgstr "Sostituzione"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,help:l10n_it_stock_ddt.field_stock_picking__l10n_it_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Il codice ISO nazionale in 2 caratteri. \n"
"Puoi usare questo campo per la ricerca veloce."

#. module: l10n_it_stock_ddt
#: model:ir.model,name:l10n_it_stock_ddt.model_stock_picking
#: model:ir.model.fields.selection,name:l10n_it_stock_ddt.selection__stock_picking__l10n_it_transport_reason__transfer
msgid "Transfer"
msgstr "Trasferimento"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_transport_method
msgid "Transport Method"
msgstr "Metodo di trasporto"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_transport_method_details
msgid "Transport Note"
msgstr "Nota di trasporto"

#. module: l10n_it_stock_ddt
#: model:ir.model.fields,field_description:l10n_it_stock_ddt.field_stock_picking__l10n_it_transport_reason
msgid "Transport Reason"
msgstr "Ragione trasporto"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Transportation Method"
msgstr "Metodo di trasporto"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "Transportation Reason"
msgstr "Ragione trasporto"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "VAT"
msgstr "Pta IVA"

#. module: l10n_it_stock_ddt
#: model_terms:ir.ui.view,arch_db:l10n_it_stock_ddt.report_ddt_view
msgid "VAT:"
msgstr "Pta IVA:"
