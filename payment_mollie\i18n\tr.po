# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mollie
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: payment_mollie
#: model_terms:ir.ui.view,arch_db:payment_mollie.payment_acquirer_form
msgid "API Key"
msgstr "API Anahtarı"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Canceled payment with status: %s"
msgstr "Durumu iptal edilen ödeme: %s"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "API bağlantısı kurulamadı."

#. module: payment_mollie
#: model:account.payment.method,name:payment_mollie.payment_method_mollie
#: model:ir.model.fields.selection,name:payment_mollie.selection__payment_acquirer__provider__mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__mollie_api_key
msgid "Mollie API Key"
msgstr "Mollie API Anahtarı"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Referans %s eşleşen bir işlem bulunamadı."

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Ödeme Alıcısı"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_account_payment_method
msgid "Payment Methods"
msgstr "Ödeme Yöntemleri"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_transaction
msgid "Payment Transaction"
msgstr "Ödeme İşlemi"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__provider
msgid "Provider"
msgstr "Sağlayıcı"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "Geçersiz ödeme durumuyla alınan veriler: %s"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "Bu alıcı ile birlikte kullanılacak Ödeme Hizmeti Sağlayıcısı"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__mollie_api_key
msgid ""
"The Test or Live API Key depending on the configuration of the acquirer"
msgstr "Alıcının yapılandırmasına bağlı olarak Test veya Canlı API Anahtarı"
