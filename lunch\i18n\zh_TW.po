# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* lunch
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_temaki
msgid "1 Avocado - 1 Salmon - 1 Eggs - 1 Tuna"
msgstr "1 個鱷梨 - 1 個鮭魚 - 1 個雞蛋 - 1 個金槍魚"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_chirashi
msgid "2 Tempuras, Cabbages, Onions, Sesame Sauce"
msgstr "2 天婦羅、捲心菜、洋蔥、芝麻醬"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_5
#: model:lunch.product,name:lunch.product_4formaggi
msgid "4 Formaggi"
msgstr "4 福馬吉"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_salmon
msgid "4 Sushi Salmon - 6 Maki Salmon - 4 Sashimi Salmon"
msgstr "4 鮭魚壽司 - 6 Maki 鮭魚 - 4 鮭魚生魚片"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_maki
msgid "6 Maki Salmon - 6 Maki Tuna - 6 Maki Shrimp/Avocado"
msgstr "6 Maki 鮭魚 - 6 Maki 金槍魚 - 6 Maki 蝦/鱷梨"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Receive button\" "
"title=\"Receive button\"/>"
msgstr ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Receive button\" "
"title=\"Receive button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_report_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"日期\" title=\"日期\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_report_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Amount\" title=\"Amount\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Amount\" title=\"Amount\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Money\" title=\"Money\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Money\" title=\"Money\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Order button\" "
"title=\"Order button\"/>"
msgstr ""
"<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Order button\" "
"title=\"Order button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Cancel button\"/>"
msgstr ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Cancel button\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Lunch Overdraft</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">午餐透支額</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"此處設定的值是特定於每間公司的。\" aria-label=\"此處設定的值是特定於每間公司的。\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: lunch
#: model:mail.template,body_html:lunch.lunch_order_mail_supplier
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Lunch Order</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"    <div>\n"
"        <t t-set=\"lines\" t-value=\"ctx.get('lines', [])\"/>\n"
"        <t t-set=\"order\" t-value=\"ctx.get('order')\"/>\n"
"        <t t-set=\"currency\" t-value=\"user.env['res.currency'].browse(order.get('currency_id'))\"/>\n"
"        <p>\n"
"        Dear <t t-out=\"order.get('supplier_name', '')\">Laurie Poiret</t>,\n"
"        </p><p>\n"
"        Here is, today orders for <t t-out=\"order.get('company_name', '')\">LunchCompany</t>:\n"
"        </p>\n"
"\n"
"        <t t-if=\"sites\">\n"
"            <br/>\n"
"            <p>Location</p>\n"
"            <t t-foreach=\"site\" t-as=\"site\">\n"
"                <p><t t-out=\"site['name'] or ''\"/> : <t t-out=\"site['address'] or ''\"/></p>\n"
"            </t>\n"
"            <br/>\n"
"        </t>\n"
"\n"
"        <table>\n"
"            <thead>\n"
"                <tr style=\"background-color:rgb(233,232,233);\">\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Product</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Comments</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Person</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Site</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Qty</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Price</strong></th>\n"
"                </tr>\n"
"            </thead>\n"
"            <tbody>\n"
"                <tr t-foreach=\"lines\" t-as=\"line\">\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['product'] or ''\">Sushi salmon</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\">\n"
"                    <t t-if=\"line['toppings']\">\n"
"                        <t t-out=\"line['toppings'] or ''\">Soy sauce</t>\n"
"                    </t>\n"
"                    <t t-if=\"line['note']\">\n"
"                        <div style=\"color: rgb(173,181,189);\" t-out=\"line['note'] or ''\">With wasabi.</div>\n"
"                    </t>\n"
"                    </td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['username'] or ''\">lap</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['site'] or ''\">Office 1</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"line['quantity'] or ''\">10</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"format_amount(line['price'], currency) or ''\">$ 1.00</td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\"><strong>Total</strong></td>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\" align=\"right\"><strong t-out=\"format_amount(order['amount_total'], currency) or ''\">$ 10.00</strong></td>\n"
"                </tr>\n"
"            </tbody>\n"
"        </table>\n"
"\n"
"        <p>Do not hesitate to contact us if you have any questions.</p>\n"
"    </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <t t-if=\"user.company_id.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <t t-if=\"user.company_id.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_control_accounts
msgid ""
"A cashmove can either be an expense or a payment.<br>\n"
"            An expense is automatically created at the order receipt.<br>\n"
"            A payment represents the employee reimbursement to the company."
msgstr ""
"現金轉移可以是費用或付款。<br>\n"
"            訂單收據會自動建立費用。<br>\n"
"            付款代表員工對公司的報銷。"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_statbutton
msgid "A product is defined by its name, category, price and vendor."
msgstr "通過產品的名稱、類別、價格和供應商來定義一個產品。"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__notification_moment__am
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__moment__am
msgid "AM"
msgstr "上午"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_needaction
msgid "Action Needed"
msgstr "需採取行動"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__active
#: model:ir.model.fields,field_description:lunch.field_lunch_order__active
#: model:ir.model.fields,field_description:lunch.field_lunch_product__active
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__active
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__active
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
msgid "Active"
msgstr "啟用"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_ids
msgid "Activities"
msgstr "活動"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Add To Cart"
msgstr "添加到購物車"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__address
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Address"
msgstr "地址"

#. module: lunch
#: model:res.groups,name:lunch.group_lunch_manager
msgid "Administrator"
msgstr "管理員"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__name
msgid "Alert Name"
msgstr "提醒名稱"

#. module: lunch
#: model:lunch.alert,name:lunch.alert_office_3
msgid "Alert for Office 3"
msgstr "警示 - 辦公室 3"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__mode__alert
msgid "Alert in app"
msgstr "在APP中提醒"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_alert_menu
msgid "Alerts"
msgstr "警示"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__amount
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__amount
msgid "Amount"
msgstr "總額"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_search
msgid "Archived"
msgstr "已歸檔"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_1
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_2
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_3
msgid "Are extras available for this product"
msgstr "該產品是否有額外的功能"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_attachment_count
msgid "Attachment Count"
msgstr "附件數"

#. module: lunch
#: model:ir.model.constraint,message:lunch.constraint_lunch_supplier_automatic_email_time_range
msgid "Automatic Email Sending Time should be between 0 and 12"
msgstr "自動電子信件發送時間應介於0到12之間"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Availability"
msgstr "可用"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Available Today"
msgstr "今日可用數量"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_1
msgid "Available Toppings 1"
msgstr "可用的配料 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_2
msgid "Available Toppings 2"
msgstr "可用的配料 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_3
msgid "Available Toppings 3"
msgstr "可用的配料 3"

#. module: lunch
#: model:lunch.product,name:lunch.product_bacon
#: model:lunch.product,name:lunch.product_bacon_0
msgid "Bacon"
msgstr "熏肉"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_bacon
#: model_terms:lunch.product,description:lunch.product_bacon_0
msgid "Beef, Bacon, Salad, Cheddar, Fried Onion, BBQ Sauce"
msgstr "牛肉、培根、沙拉、切達干酪、炸洋蔥、燒烤醬"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_cheese_burger_0
#: model_terms:lunch.product,description:lunch.product_cheeseburger
msgid "Beef, Cheddar, Salad, Fried Onions, BBQ Sauce"
msgstr "牛肉、切達干酪、沙拉、炸洋蔥、燒烤醬"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_1
#: model:lunch.product,name:lunch.product_Bolognese
msgid "Bolognese Pasta"
msgstr "意大利肉醬麵"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_country
msgid "Brie, Honey, Walnut Kernels"
msgstr "布里、蜂蜜、核桃仁"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_burger
msgid "Burger"
msgstr "漢堡包"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
msgid "By Employee"
msgstr "按員工"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "By User"
msgstr "使用者"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Cancel"
msgstr "取消"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__cancelled
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Cancelled"
msgstr "已取消"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_action_payment
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_payment
msgid "Cash Moves"
msgstr "現金憑證"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_cashmove_report
msgid "Cashmoves report"
msgstr "現金憑證報告"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Categories"
msgstr "類別"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Category"
msgstr "類別"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__mode__chat
msgid "Chat notification"
msgstr "聊天通知"

#. module: lunch
#: model:lunch.product,name:lunch.product_cheese_ham
msgid "Cheese And Ham"
msgstr "奶酪和火腿"

#. module: lunch
#: model:lunch.product,name:lunch.product_cheese_burger_0
#: model:lunch.product,name:lunch.product_cheeseburger
msgid "Cheese Burger"
msgstr "乳酪漢堡"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_cheese_ham
msgid "Cheese, Ham, Salad, Tomatoes, cucumbers, eggs"
msgstr "奶酪、火腿、沙拉、西紅柿、黃瓜、雞蛋"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_4
#: model:lunch.product,name:lunch.product_chicken_curry
msgid "Chicken Curry"
msgstr "咖哩雞"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_chirashi
msgid "Chirashi"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__city
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "City"
msgstr "城市"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid ""
"Click on the <span class=\"fa fa-phone text-success\" title=\"Order button\"></span> to announce that the order is ordered.<br>\n"
"            Click on the <span class=\"fa fa-check text-success\" title=\"Receive button\"></span> to announce that the order is received.<br>\n"
"            Click on the <span class=\"fa fa-times-circle text-danger\" title=\"Cancel button\"></span> red X to announce that the order isn't available."
msgstr ""
"點選<span class=\"fa fa-phone text-success\" title=\"Order button\"></span>以宣佈訂單已訂購。<br>\n"
"            點選<span class=\"fa fa-check text-success\" title=\"Receive button\"></span> 宣佈訂單已收到。\n"
" <br>\n"
"            點選<span class=\"fa fa-times-circle text-danger\" title=\"Cancel button\"></span> 紅色X宣佈訂單不可用。"

#. module: lunch
#: model:lunch.product,name:lunch.product_club
#: model:lunch.product,name:lunch.product_club_0
msgid "Club"
msgstr "俱樂部"

#. module: lunch
#: model:lunch.product,name:lunch.product_coke_0
msgid "Coca Cola"
msgstr "可口可樂"

#. module: lunch
#: model:ir.model,name:lunch.model_res_company
msgid "Companies"
msgstr "公司"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__company_id
msgid "Company"
msgstr "公司"

#. module: lunch
#: model:ir.model,name:lunch.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_config
msgid "Configuration"
msgstr "配置"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_controller_common.js:0
#, python-format
msgid "Configure Your Order"
msgstr "配置您的訂單"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Confirm"
msgstr "確認"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_report_action_control_accounts
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_control_accounts
msgid "Control Accounts"
msgstr "控制帳戶"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action_control_suppliers
#: model:ir.ui.menu,name:lunch.lunch_order_menu_control_suppliers
msgid "Control Vendors"
msgstr "供應商控制"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__country_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Country"
msgstr "國家"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_control_accounts
msgid "Create a new payment"
msgstr "建立一個新的付款"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Create a new product category"
msgstr "建立一個新的產品類別"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_statbutton
msgid "Create a new product for lunch"
msgstr "建立一個新的午餐菜品"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_alert_action
msgid "Create new lunch alerts"
msgstr "建立新的午餐提醒"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_location__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__create_uid
msgid "Created by"
msgstr "創立者"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_location__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__create_date
msgid "Created on"
msgstr "建立於"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__cron_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__cron_id
msgid "Cron"
msgstr "玉米"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__currency_id
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__currency_id
msgid "Currency"
msgstr "幣別"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
msgid "Currently inactive"
msgstr "目前未啟用"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__date
msgid "Date"
msgstr "日期"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__delivery
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__delivery__delivery
msgid "Delivery"
msgstr "交貨"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__description
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__description
#: model:ir.model.fields,field_description:lunch.field_lunch_order__product_description
#: model:ir.model.fields,field_description:lunch.field_lunch_product__description
msgid "Description"
msgstr "說明"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Discard"
msgstr "取消"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__mode
msgid "Display"
msgstr "顯示"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_location__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_reorder_button
msgid "Display Reorder Button"
msgstr "顯示重新排序按鈕"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_drinks
msgid "Drinks"
msgstr "饮料"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__email
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__send_by__mail
msgid "Email"
msgstr "電郵"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_month
msgid "Employee who ordered last month"
msgstr "上個月訂購的員工"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_week
msgid "Employee who ordered last week"
msgstr "上周訂購的員工"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_year
msgid "Employee who ordered last year"
msgstr "去年訂購的員工"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__everyone
msgid "Everyone"
msgstr "每一個人"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_1
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_1
msgid "Extra 1 Label"
msgstr "額外1個標籤"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_1
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_1
msgid "Extra 1 Quantity"
msgstr "額外1個數量"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_2
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_2
msgid "Extra 2 Label"
msgstr "額外2個標籤"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_2
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_2
msgid "Extra 2 Quantity"
msgstr "額外2個數量"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_3
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_3
msgid "Extra 3 Label"
msgstr "額外3個標籤"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_3
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_3
msgid "Extra 3 Quantity"
msgstr "額外3個數量"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_toppings
msgid "Extras"
msgstr "額外的"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_1
msgid "Extras 1"
msgstr "額外 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_2
msgid "Extras 2"
msgstr "額外 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_3
msgid "Extras 3"
msgstr "額外 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_users__favorite_lunch_product_ids
msgid "Favorite Lunch Product"
msgstr "喜好的午餐產品"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__favorite_user_ids
msgid "Favorite User"
msgstr "最愛使用者"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr "郵箱格式「名稱<email@domain>」"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__email_formatted
msgid "Formatted Email"
msgstr "格式化的信件"

#. module: lunch
#: model_terms:lunch.order,product_description:lunch.order_line_2
#: model_terms:lunch.product,description:lunch.product_italiana
msgid "Fresh Tomatoes, Basil, Mozzarella"
msgstr "新鮮番茄、羅勒、馬蘇里拉奶酪"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__fri
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__fri
msgid "Fri"
msgstr "五"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Friday"
msgstr "星期五"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_3
#: model:lunch.product,name:lunch.product_gouda
msgid "Gouda Cheese"
msgstr "高達奶酪"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Group By"
msgstr "分組依據"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_club
#: model_terms:lunch.product,description:lunch.product_club_0
msgid "Ham, Cheese, Vegetables"
msgstr "火腿、奶酪、蔬菜"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: lunch
#: model:ir.module.category,description:lunch.module_lunch_category
msgid ""
"Helps you handle your lunch needs, if you are a manager you will be able to "
"create new products, cashmoves and to confirm or cancel orders."
msgstr "幫您處理午餐需求，如果您是一個管理者，您能建立產品、現金轉移並確認或者取消訂單。"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Here you can access all categories for the lunch products."
msgstr "在這裡您可以存取所有的午餐產品的種類。"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_by_supplier
msgid "Here you can see today's orders grouped by vendors."
msgstr "這裡您可以看到以代理商分組的所有今天的訂單。"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_account
msgid ""
"Here you can see your cash moves.<br>A cash move can either be an expense or a payment.\n"
"            An expense is automatically created when an order is received while a payment is a reimbursement to the company encoded by the manager."
msgstr ""
"此處可以顯示現金的轉移。<br>現金轉移可以為費用或付款。\n"
"當收到一個午餐單時，會自動建立費用。而付款則為管理員定義的向公司的繳款."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__id
#: model:ir.model.fields,field_description:lunch.field_lunch_location__id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__id
msgid "ID"
msgstr "ID"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_needaction
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_unread
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_1920
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_1920
msgid "Image"
msgstr "圖像"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_1024
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_1024
msgid "Image 1024"
msgstr "圖像 1024"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__image_128
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_128
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_128
msgid "Image 128"
msgstr "圖像 128"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__image_1920
msgid "Image 1920"
msgstr "圖像 1920"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_256
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_256
msgid "Image 256"
msgstr "圖像 256"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_512
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_512
msgid "Image 512"
msgstr "圖像 512"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Information, allergens, ..."
msgstr "資訊、過敏原……"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__available_today
msgid "Is Displayed Today"
msgstr "今天顯示"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_favorite
msgid "Is Favorite"
msgstr "為最愛"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_new
msgid "Is New"
msgstr "為新"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_users__last_lunch_location_id
msgid "Last Lunch Location"
msgstr "最後的午餐地點"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_location____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_order____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_product____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_topping____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__last_order_date
msgid "Last Order Date"
msgstr "最後訂購日期"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_location__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_location__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__location_ids
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__available_location_ids
msgid "Location"
msgstr "地點"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__name
msgid "Location Name"
msgstr "位置名稱"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_location_menu
msgid "Locations"
msgstr "位置"

#. module: lunch
#: model:ir.module.category,name:lunch.module_lunch_category
#: model:ir.ui.menu,name:lunch.menu_lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Lunch"
msgstr "午餐"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_alert
msgid "Lunch Alert"
msgstr "午餐提醒"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_alert_action
msgid "Lunch Alerts"
msgstr "午餐提醒"

#. module: lunch
#: code:addons/lunch/models/lunch_cashmove.py:0
#: code:addons/lunch/report/lunch_cashmove_report.py:0
#: model:ir.model,name:lunch.model_lunch_cashmove
#, python-format
msgid "Lunch Cashmove"
msgstr "午餐現金轉移"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_topping
msgid "Lunch Extras"
msgstr "午餐附加費"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_kanban_view.js:0
#, python-format
msgid "Lunch Kanban"
msgstr "午餐看板"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_list_view.js:0
#, python-format
msgid "Lunch List"
msgstr "午餐清單"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__lunch_location_id
msgid "Lunch Location"
msgstr "午餐地點"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_location_action
#: model:ir.model,name:lunch.model_lunch_location
msgid "Lunch Locations"
msgstr "午餐地點"

#. module: lunch
#: model:lunch.product,name:lunch.product_maki
msgid "Lunch Maki 18pc"
msgstr "午餐 Maki 18pc"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_company__lunch_minimum_threshold
msgid "Lunch Minimum Threshold"
msgstr "午餐最低門檻"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_order
msgid "Lunch Order"
msgstr "午餐訂單"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product
msgid "Lunch Product"
msgstr "工作餐產品"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product_category
msgid "Lunch Product Category"
msgstr "工作餐產品類別"

#. module: lunch
#: model:lunch.product,name:lunch.product_salmon
msgid "Lunch Salmon 20pc"
msgstr "午餐鮭魚 20pc"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_supplier
msgid "Lunch Supplier"
msgstr "午餐供應商"

#. module: lunch
#: model:lunch.product,name:lunch.product_temaki
msgid "Lunch Temaki mix 3pc"
msgstr "午餐  Temaki mix 3pc"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_cancel
msgid "Lunch: Cancel meals"
msgstr "午餐：取消用餐"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_confirm
msgid "Lunch: Receive meals"
msgstr "午餐：收到餐食"

#. module: lunch
#: model:mail.template,name:lunch.lunch_order_mail_supplier
msgid "Lunch: Send by email"
msgstr "午餐：通過電子郵件發送"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_alert_cron_sa_224
msgid "Lunch: alert chat notification (Alert for Office 3)"
msgstr "午餐：提醒聊天通知（提醒辦公室 3）。"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_225
msgid "Lunch: send automatic email to Coin gourmand"
msgstr "午餐：發送自動電子郵件給coin gourmand"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_221
msgid "Lunch: send automatic email to Lunch Supplier"
msgstr "午餐：發送自動電子郵件給午餐供應商"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_226
msgid "Lunch: send automatic email to Pizza Inn"
msgstr "午餐：發送自動電子郵件給Pizza Inn"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_228
msgid "Lunch: send automatic email to Sushi Shop"
msgstr "午餐：發送自動電子郵件給壽司店"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_227
msgid "Lunch: send automatic email to The Corner"
msgstr "午餐：發送自動電子郵件給The Corner"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_res_config_settings__currency_id
msgid "Main currency of the company."
msgstr "公司的主貨幣。"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_admin
msgid "Manager"
msgstr "管理員"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__company_lunch_minimum_threshold
msgid "Maximum Allowed Overdraft"
msgstr "最大允許超支額度"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Maximum overdraft that your employees can reach"
msgstr "員工可以達到的最大超支額"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__message
msgid "Message"
msgstr "消息"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_ids
msgid "Messages"
msgstr "訊息"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__moment
msgid "Moment"
msgstr "時刻"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__mon
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__mon
msgid "Mon"
msgstr "一"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Monday"
msgstr "星期一"

#. module: lunch
#: model:lunch.product,name:lunch.product_mozzarella
msgid "Mozzarella"
msgstr "奶酪"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_mozzarella
msgid "Mozzarella, Pesto, Tomatoes"
msgstr "馬蘇里拉奶酪、香蒜醬、番茄"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_report_action_account
msgid "My Account"
msgstr "我的帳戶"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_form
msgid "My Account History"
msgstr "我的帳戶歷史"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "My Account grouped"
msgstr "我的帳戶已分組"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_title
msgid "My Lunch"
msgstr "我的午餐"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_menu_tree
msgid "My Order History"
msgstr "我的訂單歷史"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "My Orders"
msgstr "我的銷售訂單"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__name
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__name
msgid "Name"
msgstr "名稱"

#. module: lunch
#: model:lunch.product,name:lunch.product_Napoli
msgid "Napoli Pasta"
msgstr "那不勒斯意大利面"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_product_kanban_order
msgid "New"
msgstr "新增"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_menu_form
msgid "New Order"
msgstr "新訂單"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__new_until
msgid "New Until"
msgstr "新的直到"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一個活動日曆事件"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__delivery__no_delivery
msgid "No Delivery"
msgstr "沒有交貨"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_account
msgid "No cash move yet"
msgstr "無現金憑證"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid "No lunch order yet"
msgstr "暫無午餐訂單"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action
msgid "No previous order found"
msgstr "前訂單沒有找到"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "None"
msgstr "無"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__0_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__0_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__0_more
msgid "None or More"
msgstr "沒有或更多"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Not Received"
msgstr "未收到"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_controller_common.js:0
#, python-format
msgid "Not enough money in your wallet"
msgstr "您的錢包裡沒有足夠的錢"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__note
msgid "Notes"
msgstr "備註"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_by_supplier
msgid "Nothing to order today"
msgstr "今天沒有可以訂購的"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__notification_moment
msgid "Notification Moment"
msgstr "通知時刻"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__notification_time
msgid "Notification Time"
msgstr "通知時間"

#. module: lunch
#: model:ir.model.constraint,message:lunch.constraint_lunch_alert_notification_time_range
msgid "Notification time must be between 0 and 12"
msgstr "通知時間必須介於0到12之間"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要處理的消息數量"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_unread_counter
msgid "Number of unread messages"
msgstr "未讀訊息的數量"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__1_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__1_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__1_more
msgid "One or More"
msgstr "一個或多個"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__1
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__1
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__1
msgid "Only One"
msgstr "只有一個"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "Order"
msgstr "訂單"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__date
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Order Date"
msgstr "報價日期"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__automatic_email_time
msgid "Order Time"
msgstr "訂單時間"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_action_order
msgid "Order Your Lunch"
msgstr "訂購午餐"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Order lines Tree"
msgstr "訂單明細樹形"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#, python-format
msgid "Order now"
msgstr "立即訂購"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__ordered
msgid "Ordered"
msgstr "已訂購"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Orders"
msgstr "銷售訂單"

#. module: lunch
#: model:mail.template,subject:lunch.lunch_order_mail_supplier
msgid "Orders for {{ ctx['order']['company_name'] }}"
msgstr "{{ ctx['order']['company_name'] }} 的訂單"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Overdraft"
msgstr "超支"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__notification_moment__pm
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__moment__pm
msgid "PM"
msgstr "下午"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_pasta
msgid "Pasta"
msgstr "意大利麵"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
msgid "Payment"
msgstr "付款"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid ""
"Payments are used to register liquidity movements. You can process those "
"payments by your own means or by using installed facilities."
msgstr "支付用於登記流動性。您可以通過自己的方式或使用安裝的設施來處理這些付款。"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__phone
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__send_by__phone
msgid "Phone"
msgstr "電話"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_pizza
msgid "Pizza"
msgstr "比薩"

#. module: lunch
#: model:lunch.product,name:lunch.product_funghi
msgid "Pizza Funghi"
msgstr "比薩蘑菇"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_2
#: model:lunch.product,name:lunch.product_italiana
msgid "Pizza Italiana"
msgstr "意大利比薩"

#. module: lunch
#: model:lunch.product,name:lunch.product_margherita
#: model:lunch.product,name:lunch.product_pizza_0
msgid "Pizza Margherita"
msgstr "瑪格麗特比薩"

#. module: lunch
#: model:lunch.product,name:lunch.product_vege
msgid "Pizza Vegetarian"
msgstr "比薩 素食"

#. module: lunch
#: model_terms:lunch.alert,message:lunch.alert_office_3
msgid "Please order"
msgstr "請訂購"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__price
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__price
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Price"
msgstr "價格"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__product_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product"
msgstr "商品"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_available_at
msgid "Product Availability"
msgstr "可用的產品"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_category_action
#: model:ir.ui.menu,name:lunch.lunch_product_category_menu
msgid "Product Categories"
msgstr "產品類別"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
msgid "Product Categories Form"
msgstr "產品類別表單"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__name
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_tree
msgid "Product Category"
msgstr "產品分類"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__product_count
msgid "Product Count"
msgstr "產品數量"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__product_image
msgid "Product Image"
msgstr "產品圖片"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__name
#: model:ir.model.fields,field_description:lunch.field_lunch_product__name
msgid "Product Name"
msgstr "產品名稱"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product Search"
msgstr "產品搜尋"

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "Product is no longer available."
msgstr "產品不再可用。"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_action
#: model:ir.actions.act_window,name:lunch.lunch_product_action_statbutton
#: model:ir.ui.menu,name:lunch.lunch_product_menu
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_kanban
msgid "Products"
msgstr "產品"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_form
msgid "Products Form"
msgstr "產品表單"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_tree
msgid "Products List"
msgstr "產品清單"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_tree
msgid "Products Tree"
msgstr "產品樹形"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__quantity
msgid "Quantity"
msgstr "數量"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Re-order"
msgstr "重排序"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Receive"
msgstr "收貨"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Received"
msgstr "已接收"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__recipients
msgid "Recipients"
msgstr "收件人"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid "Register a payment"
msgstr "登記付款"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Reset"
msgstr "重置"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__responsible_id
msgid "Responsible"
msgstr "負責人"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: lunch
#: model:lunch.product,name:lunch.product_chirashi
msgid "Salmon and Avocado"
msgstr "鮭魚和酪梨"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_sandwich
msgid "Sandwich"
msgstr "三明治"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__sat
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__sat
msgid "Sat"
msgstr "六"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Saturday"
msgstr "星期六"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Save"
msgstr "儲存"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Search"
msgstr "搜尋"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__send_by
msgid "Send Order By"
msgstr "發送訂單"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_config_settings_action
#: model:ir.ui.menu,name:lunch.lunch_settings_menu
msgid "Settings"
msgstr "設定"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__until
msgid "Show Until"
msgstr "顯示直到"

#. module: lunch
#: model:lunch.product,name:lunch.product_spicy_tuna
msgid "Spicy Tuna"
msgstr "辣金槍魚"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__state_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "State"
msgstr "狀態"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__state
msgid "Status"
msgstr "狀態"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__street
msgid "Street"
msgstr "街道"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Street 2..."
msgstr "街道2..."

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Street..."
msgstr "街道..."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__street2
msgid "Street2"
msgstr "街道第二行"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid "Summary of all lunch orders, grouped by vendor and by date."
msgstr "按服務商和日期來概括所有午餐訂單。"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__sun
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__sun
msgid "Sun"
msgstr "日"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Sunday"
msgstr "星期日"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__supplier_id
msgid "Supplier"
msgstr "供應商"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_sushi
msgid "Sushi"
msgstr "壽司"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_temaki
msgid "Temaki"
msgstr "手捲"

#. module: lunch
#: model:lunch.product,name:lunch.product_country
msgid "The Country"
msgstr "按國家"

#. module: lunch
#: code:addons/lunch/models/lunch_product.py:0
#, python-format
msgid ""
"The following product categories are archived. You should either unarchive the categories or change the category of the product.\n"
"%s"
msgstr ""
"以下產品類別已存檔。您應該取消歸檔類別或更改產品的類別。\n"
"%s"

#. module: lunch
#: code:addons/lunch/models/lunch_product.py:0
#, python-format
msgid ""
"The following suppliers are archived. You should either unarchive the suppliers or change the supplier of the product.\n"
"%s"
msgstr ""
"以下供應商已存檔。您應該取消歸檔供應商或更改產品的供應商。\n"
"%s"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_product_category__product_count
msgid "The number of products related to this category"
msgstr "與此類別相關的產品數量"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__responsible_id
msgid ""
"The responsible is the person that will order lunch for everyone. It will be"
" used as the 'from' when sending the automatic email."
msgstr "負責人是為每個人訂購午餐的人。它將在發送自動電子信件時用作「發件人」。"

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "The vendor related to this order is not available today."
msgstr "與此訂單相關的供應商今天不可用。"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action
msgid ""
"There is no previous order recorded. Click on \"My Lunch\" and then create a"
" new lunch order."
msgstr "未記錄先前訂單。點選「我的午餐」然後建立新增午餐訂單。"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_order
msgid "There is no product available today"
msgstr "今天沒有產品可用 "

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_topping__topping_category
msgid "This field is a technical field"
msgstr "該領域是技術領域"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__recurrency_end_date
msgid "This field is used in order to "
msgstr "此欄位用於"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__available_today
msgid "This is True when if the supplier is available today"
msgstr "如果供應商今天可用，這是正確的"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__thu
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__thu
msgid "Thu"
msgstr "四"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Thursday"
msgstr "星期四"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__tz
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__tz
msgid "Timezone"
msgstr "時區"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_widget.js:0
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__new
#, python-format
msgid "To Order"
msgstr "訂購"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_payment_dialog
msgid "To add some money to your wallet, please contact your lunch manager."
msgstr "要為您的錢包添加一些錢，請聯繫您的午餐管理員。"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_location_action
msgid "To see some locations, create one using the create button"
msgstr "要查看某些位置，請使用「建立」按鈕建立一個位置"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_order
msgid ""
"To see some products, check if your vendors are available today and that you"
" have configured some products"
msgstr "要查看某些產品，請檢查您的供應商是否可用，以及您是否配置了某些產品"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Today"
msgstr "今天"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action_by_supplier
#: model:ir.ui.menu,name:lunch.lunch_order_menu_by_supplier
msgid "Today's Orders"
msgstr "今天的訂單"

#. module: lunch
#: model_terms:lunch.order,product_description:lunch.order_line_5
#: model_terms:lunch.product,description:lunch.product_4formaggi
msgid "Tomato sauce, Olive oil, Fresh Tomatoes, Onions, Vegetables, Parmesan"
msgstr "番茄醬、橄欖油、新鮮番茄、洋蔥、蔬菜、巴馬干酪"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_Napoli
msgid "Tomatoes, Basil"
msgstr "番茄、羅勒"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_margherita
#: model_terms:lunch.product,description:lunch.product_pizza_0
msgid "Tomatoes, Mozzarella"
msgstr "番茄、馬蘇里拉奶酪"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_vege
msgid "Tomatoes, Mozzarella, Mushrooms, Peppers, Olives"
msgstr "番茄、馬蘇里拉奶酪、蘑菇、辣椒、橄欖"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_funghi
msgid "Tomatoes, Mushrooms, Mozzarella"
msgstr "番茄、蘑菇、馬蘇里拉奶酪"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__topping_category
msgid "Topping Category"
msgstr "配料類別"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_1
msgid "Topping Ids 1"
msgstr "配料 Ids 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_2
msgid "Topping Ids 2"
msgstr "配料 Ids 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_3
msgid "Topping Ids 3"
msgstr "配料 Ids 3"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
#, python-format
msgid "Total"
msgstr "總計"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__price
msgid "Total Price"
msgstr "總價"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__tue
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__tue
msgid "Tue"
msgstr "二"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Tuesday"
msgstr "星期二"

#. module: lunch
#: model:lunch.product,name:lunch.product_tuna
msgid "Tuna"
msgstr "金槍魚"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_tuna
msgid "Tuna, Mayonnaise"
msgstr "金槍魚、蛋黃醬"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_unread
msgid "Unread Messages"
msgstr "未讀消息"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未讀消息計數器"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__recurrency_end_date
msgid "Until"
msgstr "直到"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__user_id
#: model:res.groups,name:lunch.group_lunch_user
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "User"
msgstr "使用者"

#. module: lunch
#: model:ir.model,name:lunch.model_res_users
msgid "Users"
msgstr "使用者"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__supplier_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__supplier_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__partner_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Vendor"
msgstr "供應商"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Vendor Orders by Date"
msgstr "按日期交貨的訂單"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_vendors_action
#: model:ir.ui.menu,name:lunch.lunch_vendors_menu
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Vendors"
msgstr "供應商"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__wed
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__wed
msgid "Wed"
msgstr "三"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Wednesday"
msgstr "星期三"

#. module: lunch
#: code:addons/lunch/controllers/main.py:0
#, python-format
msgid ""
"You are trying to impersonate another user, but this can only be done by a "
"lunch manager"
msgstr "您正在嘗試冒充其他使用者，但這只能由午餐管理員完成"

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "You have to order one and only one %s"
msgstr "您必須訂購一個且只有一個%s"

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "You should order at least one %s"
msgstr "您應該至少訂購一個%s"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#, python-format
msgid "Your Account"
msgstr "您的帳號"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#, python-format
msgid ""
"Your cart\n"
"                ("
msgstr ""
"您的購物車\n"
"                ("

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#, python-format
msgid "Your order"
msgstr "您的訂單"

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid ""
"Your wallet does not contain enough money to order that. To add some money "
"to your wallet, please contact your lunch manager."
msgstr "你的錢包裡沒有足夠的錢來訂購。要將一些錢存入您的錢包，請聯繫您的午餐管理員。"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "ZIP"
msgstr "郵編"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__zip_code
msgid "Zip"
msgstr "郵政編碼"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "alert form"
msgstr "警示表單"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_form
msgid "cashmove form"
msgstr "現金轉移表格"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
msgid "cashmove tree"
msgstr "現金轉移列表"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "e.g. Order before 11am"
msgstr "例如上午11點前下單"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "e.g. The Pizzeria Inn"
msgstr "例如 比薩店"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
msgid "lunch cashmove"
msgstr "午餐現金轉移"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "lunch employee payment"
msgstr "午餐員工付款"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_kanban
msgid "on"
msgstr "在"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_kanban
msgid "to"
msgstr "到"
