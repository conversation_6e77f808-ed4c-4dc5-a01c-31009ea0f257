<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record model='l10n_ar.afip.responsibility.type' id='res_IVARI'>
        <field name='code'>1</field>
        <field name='name'>IVA Responsable Inscripto</field>
        <field name='active' eval="True"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_IVARNI'>
        <field name='code'>2</field>
        <field name='name'>IVA Responsable no Inscripto</field>
        <field name='active' eval="False"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_IVANR'>
        <field name='code'>3</field>
        <field name='name'>IVA no Responsable</field>
        <field name='active' eval="False"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_IVAE'>
        <field name='code'>4</field>
        <field name='name'>IVA Sujeto Exento</field>
        <field name='active' eval="True"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_CF'>
        <field name='code'>5</field>
        <field name='name'>Consumidor Final</field>
        <field name='active' eval="True"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_RM'>
        <field name='code'>6</field>
        <field name='name'>Responsable Monotributo</field>
        <field name='active' eval="True"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_NOCATEG'>
        <field name='code'>7</field>
        <field name='name'>Sujeto no Categorizado</field>
        <field name='active' eval="False"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_EXT'>
        <field name='code'>9</field>
        <field name='name'>Cliente / Proveedor del Exterior</field>
        <field name='active' eval="True"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_IVA_LIB'>
        <field name='code'>10</field>
        <field name='name'>IVA Liberado – Ley Nº 19.640</field>
        <field name='active' eval="True"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_IVARI_AP'>
        <field name='code'>11</field>
        <field name='name'>IVA Responsable Inscripto – Agente de Percepción</field>
        <field name='active' eval="False"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_EVENTUAL'>
        <field name='code'>12</field>
        <field name='name'>Pequeño Contribuyente Eventual</field>
        <field name='active' eval="False"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_MON_SOCIAL'>
        <field name='code'>13</field>
        <field name='name'>Monotributista Social</field>
        <field name='active' eval="False"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_EVENTUAL_SOCIAL'>
        <field name='code'>14</field>
        <field name='name'>Pequeño Contribuyente Eventual Social</field>
        <field name='active' eval="False"/>
    </record>
    <record model='l10n_ar.afip.responsibility.type' id='res_IVA_NO_ALC'>
        <field name='code'>99</field>
        <field name='name'>IVA No Alcanzado</field>
    </record>
</odoo>
