# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* gift_card
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: gift_card
#: model:ir.model.fields,help:gift_card.field_product_product__detailed_type
#: model:ir.model.fields,help:gift_card.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Складируемый - это продукт, для которого вы отслеживаете остатки на складе. "
"Необходимо установить приложение Склад. Потребляемый - это продукт, для "
"которого остатки не учитываются. Услуга - это нематериальный продукт, "
"который вы предоставляете."

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__balance
msgid "Balance"
msgstr "Баланс"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__code
msgid "Code"
msgstr "Код"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__company_id
msgid "Company"
msgstr "Компания"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__create_uid
msgid "Created by"
msgstr "Создан"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__create_date
msgid "Created on"
msgstr "Создан"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: gift_card
#: code:addons/gift_card/models/product.py:0
#, python-format
msgid "Deleting the Gift Card Pay product is not allowed."
msgstr "Удаление продукта Gift Card Pay запрещено."

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: gift_card
#: model:ir.model.fields.selection,name:gift_card.selection__gift_card__state__expired
msgid "Expired"
msgstr "Истёк"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__expired_date
msgid "Expired Date"
msgstr "Дата истечения срока"

#. module: gift_card
#: code:addons/gift_card/models/gift_card.py:0
#, python-format
msgid "Gift #%s"
msgstr "Поадрок #%s"

#. module: gift_card
#: model:ir.model,name:gift_card.model_gift_card
#: model:ir.model.fields.selection,name:gift_card.selection__product_template__detailed_type__gift
#: model:product.product,name:gift_card.gift_card_product_50
#: model:product.product,name:gift_card.pay_with_gift_card_product
#: model:product.template,name:gift_card.gift_card_product_50_product_template
#: model:product.template,name:gift_card.pay_with_gift_card_product_product_template
#: model_terms:ir.ui.view,arch_db:gift_card.gift_card_view_search
#: model_terms:ir.ui.view,arch_db:gift_card.gift_card_view_tree
msgid "Gift Card"
msgstr "Подарочная карта"

#. module: gift_card
#: model:ir.actions.act_window,name:gift_card.gift_card_action
msgid "Gift Cards"
msgstr "Подарочные карты"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__id
msgid "ID"
msgstr "Идентификатор"

#. module: gift_card
#: model:ir.model.fields,help:gift_card.field_gift_card__partner_id
msgid "If empty, all users can use it"
msgstr "Если пусто, может быть использовано всеми пользователями."

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__initial_amount
msgid "Initial Amount"
msgstr "Первоначальная сумма"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__name
msgid "Name"
msgstr "Название"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__partner_id
msgid "Partner"
msgstr "Партнёр"

#. module: gift_card
#: model:ir.model,name:gift_card.model_product_template
msgid "Product Template"
msgstr "Шаблон продукта"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_product_product__detailed_type
#: model:ir.model.fields,field_description:gift_card.field_product_template__detailed_type
msgid "Product Type"
msgstr "Тип продукта"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__state
msgid "State"
msgstr "Статус"

#. module: gift_card
#: model:ir.model.constraint,message:gift_card.constraint_gift_card_unique_gift_card_code
msgid "The gift card code must be unique."
msgstr "Код подарочной карты должен быть уникальным."

#. module: gift_card
#: model:ir.model.constraint,message:gift_card.constraint_gift_card_check_amount
msgid "The initial amount must be positive."
msgstr "Начальная сумма должна быть положительной."

#. module: gift_card
#: model:product.product,uom_name:gift_card.gift_card_product_50
#: model:product.product,uom_name:gift_card.pay_with_gift_card_product
#: model:product.template,uom_name:gift_card.gift_card_product_50_product_template
#: model:product.template,uom_name:gift_card.pay_with_gift_card_product_product_template
msgid "Units"
msgstr "Единицы"

#. module: gift_card
#: model:ir.model.fields.selection,name:gift_card.selection__gift_card__state__valid
#: model_terms:ir.ui.view,arch_db:gift_card.gift_card_view_search
msgid "Valid"
msgstr "Корректный"
