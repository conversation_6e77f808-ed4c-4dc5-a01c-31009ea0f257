<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record  id="email_message_tree_view" model="ir.ui.view">
        <field name="name">mail.mail.form.fetchmail</field>
        <field name="model">mail.mail</field>
        <field name="inherit_id" ref="mail.view_mail_form"/>
        <field name="arch" type="xml">
            <field name="references" position="after">
                <field name="fetchmail_server_id"/>
            </field>
        </field>
    </record>

    <record id="act_server_history" model="ir.actions.act_window">
        <field name="name">Messages</field>
        <field name="res_model">mail.mail</field>
        <field name="domain">[('email_from', '!=', False), ('fetchmail_server_id', '=', active_id)]</field>
        <field name="context">{'search_default_server_id': active_id, 'default_fetchmail_server_id': active_id}</field>
        <field name="binding_model_id" ref="model_fetchmail_server"/>
        <field name="binding_view_types">form</field>
    </record>

</odoo>
