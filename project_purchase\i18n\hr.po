# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_purchase
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: project_purchase
#: model:ir.model.fields,field_description:project_purchase.field_project_project__purchase_orders_count
msgid "# Purchase Orders"
msgstr ""

#. module: project_purchase
#: model_terms:ir.ui.view,arch_db:project_purchase.project_project_form_view_inherited
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Purchase Orders\n"
"                        </span>"
msgstr ""

#. module: project_purchase
#: model:ir.model,name:project_purchase.model_project_project
msgid "Project"
msgstr "Projekt"

#. module: project_purchase
#: code:addons/project_purchase/models/project.py:0
#: code:addons/project_purchase/models/project.py:0
#, python-format
msgid "Purchase Orders"
msgstr "Nalozi u nabavi"
