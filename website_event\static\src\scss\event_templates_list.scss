.o_wevent_sidebar_social > .o_wevent_social_link {
    $o_link_size: 3em;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    @include size($o_link_size);
    margin: 0 ($spacer * .25) ($spacer * .5) ($spacer * .25);
    line-height: $o_link_size;
    background-color: gray('100');
    border: $border-width solid $border-color;
    border-radius: 50%;
    text-align: center;
    @include hover-focus {
        background-color: gray('300');
    }
}

// Index
.o_wevent_index {
    // Events List
    .o_wevent_events_list {
        header {
            a {
                transition: all .25s ease-in-out;
                @include hover-focus {
                    transform: scale(1.05);
                }
            }
        }
        @include media-breakpoint-up(sm) {
            #o_wevent_index_main_col article div.col {
                min-width: 0;
            }
        }
        &.opt_event_list_cards_bg {
            @if (color('body') == $o-portal-default-body-bg) {
                @extend .bg-200;
            }
        }
        .opt_events_list_columns {
            header {
                height: 200px;
            }
        }
        .opt_events_list_rows {
            @include media-breakpoint-down(sm) {
                header {
                    height: 200px;
                }
            }
        }
        .o_wevent_badge_event {
            @include o-position-absolute($top: 0, $left: 0);
            @include border-left-radius(0);
            padding: ($spacer * .5) $card-spacer-x;
            transform: translateY(-50%);
        }
        .o_wevent_event_date {
            top: $card-spacer-x;
            right: $card-spacer-x;
            display: flex;
            flex-direction: column;
            justify-content: center;
            @include size(4rem);
            border-radius: 50%;
            text-align: center;

            .o_wevent_event_day {
                font-size: 1.125rem;
                font-weight: 300;
                line-height: 1;
            }
            .o_wevent_event_month {
                font-size: 0.75rem;
                font-weight: $font-weight-bold;
                text-transform: uppercase;
            }
        }
        .o_wevent_participating,
        .o_wevent_unpublished {
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: $card-spacer-y $card-spacer-x;
            text-align: right;
        }
        .card-title {
            color: $body-color;
        }
    }
    .o_wevent_sidebar_title {
        margin: 0 0 ($spacer * 1.5) 0;
        border-bottom: $border-width solid $border-color;
        padding: 0 0 ($spacer * .5) 0;
        font-weight: $font-weight-bold;
        text-transform: uppercase;
    }
    .o_wevent_sidebar_block {
        margin: 0 0 ($spacer * 2.5) 0;
    }
    .o_wevent_sidebar_figure {
        position: relative;

        .figure-img {
            margin-bottom: 0;
        }
        .figure-caption {
            @include o-position-absolute($left: 0, $bottom: 0);
            width: 100%;
            padding: $spacer;
            background-color: rgba($color: #000, $alpha: 0.5);
            @include border-bottom-radius($border-radius);
            color: #fff;
        }
    }
    #o_wevent_index_main_col .form-inline {
        width: 100%;
    }
}
