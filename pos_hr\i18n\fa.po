# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "<span class=\"o_form_label oe_edit_only\">Allowed Employees </span>"
msgstr "<span class=\"o_form_label oe_edit_only\">کارمندان اجاره‌دار </span>"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "صندوقدار"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/useSelectEmployee.js:0
#, python-format
msgid "Change Cashier"
msgstr "تغییر صندوق‌دار"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "کارمند"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr "کارمند: %s - پیکربندی‌(های) نقطه فروش: %s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__employee_ids
msgid "Employees with access"
msgstr "کارمند با درسترسی"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__employee_ids
msgid "If left empty, all employees can log in to the PoS session"
msgstr "اگر انتخاب نشود همه کارمندان می‌توانند در نشست نقطه فروش وارد شوند"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/useSelectEmployee.js:0
#, python-format
msgid "Incorrect Password"
msgstr "گذرواژه نادرست"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Log in to"
msgstr "ورود به"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/useSelectEmployee.js:0
#, python-format
msgid "Password ?"
msgstr "گذرواژه؟"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"شخصی که از صندوق پول استفاده می کند. این می تواند یک دانش آموز یا یک کارمند "
"موقت باشد."

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "پیکربندی پایانه فروش"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "سفارش های پایانه فروش"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "گزارش سفارشات نقطه فروش"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Scan your badge"
msgstr "نشان خود را اسکن کنید"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Select Cashier"
msgstr "انتخاب صندوق‌دار"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr ""
"نمی‌توانید کارمندی که ممکن است شیفت فعال داشته باشد حذف کنید ابتدا شیفت(ها) "
"را ببندید: \n"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "or"
msgstr "یا"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/CashierName.xml:0
#, python-format
msgid "selectCashier"
msgstr "انتخاب صندوق‌دار"
