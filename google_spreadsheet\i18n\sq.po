# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_spreadsheet
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Albanian (https://www.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_spreadsheet
#. openerp-web
#: code:addons/google_spreadsheet/static/src/xml/addtospreadsheet.xml:3
#, python-format
msgid "Add to Google Spreadsheet"
msgstr ""

#. module: google_spreadsheet
#: model:ir.model,name:google_spreadsheet.model_google_drive_config
msgid "Google Drive templates config"
msgstr ""

#. module: google_spreadsheet
#: model_terms:ir.actions.act_window,help:google_spreadsheet.action_ir_attachment_google_spreadsheet_tree
#: model:ir.actions.act_window,name:google_spreadsheet.action_ir_attachment_google_spreadsheet_tree
#: model:ir.ui.menu,name:google_spreadsheet.menu_reporting_dashboard_google_spreadsheets
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.view_ir_attachment_google_spreadsheet_form
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.view_ir_attachment_google_spreadsheet_tree
msgid "Google Spreadsheets"
msgstr ""

#. module: google_spreadsheet
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.view_ir_attachment_google_spreadsheet_form
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.view_ir_attachment_google_spreadsheet_tree
msgid "Name"
msgstr ""

#. module: google_spreadsheet
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.res_config_settings_view_form
msgid ""
"Please use the settings of Google Drive\n"
"                            on the left or above."
msgstr ""

#. module: google_spreadsheet
#: model:ir.model.fields,help:google_spreadsheet.field_res_config_settings_google_drive_uri_copy
msgid "The URL to generate the authorization code from Google"
msgstr ""

#. module: google_spreadsheet
#: model:ir.model.fields,field_description:google_spreadsheet.field_res_config_settings_google_drive_uri_copy
msgid "URI"
msgstr ""

#. module: google_spreadsheet
#: model:ir.model,name:google_spreadsheet.model_res_config_settings
msgid "res.config.settings"
msgstr ""
