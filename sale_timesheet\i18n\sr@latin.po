# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_timesheet
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: N<PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_invoice.py:32
#: code:addons/sale_timesheet/models/project.py:26
#, python-format
msgid ""
"\n"
"                <p class=\"oe_view_nocontent_create\">\n"
"                    Click to record timesheets.\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "<b>Total</b>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "<span class=\"o_label\">Overview</span>"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analiticki red"

#. module: sale_timesheet
#: selection:account.analytic.line,timesheet_invoice_type:0
msgid "Billable Fixed"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billable Hours"
msgstr ""

#. module: sale_timesheet
#: selection:account.analytic.line,timesheet_invoice_type:0
msgid "Billable Time"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line_timesheet_invoice_type
msgid "Billable Type"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Billable fixed"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Billable time"
msgstr ""

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.timesheet_filter_billing
msgid "Billing Rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Rate"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Cost"
msgstr "Cijena koštanja"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_report_cost_revenue
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_report_cost_revenue
msgid "Costs and Revenues"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_tracking:0
msgid "Create a new project but no task"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_tracking:0
msgid "Create a task in a new project"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_tracking:0
msgid "Create a task in an existing project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_hr_employee_currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale_timesheet
#: selection:product.template,service_tracking:0
msgid "Don't create task"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_hr_employee
msgid "Employee"
msgstr "Zaposleni"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Fixed"
msgstr "Fiksno"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Fixed Price Projects"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_search_view_sale_timesheet
msgid "Fixed price services"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Hours"
msgstr "Sati"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_invoice
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line_timesheet_invoice_id
msgid "Invoice"
msgstr "Faktura"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product_service_policy
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template_service_policy
msgid "Invoice based on"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line_timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Invoiced"
msgstr "Fakturisano"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line_is_service
msgid "Is a Service"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_search_view_sale_timesheet
msgid "Milestone services"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_policy:0
msgid "Milestones (manually set quantities on order)"
msgstr ""

#. module: sale_timesheet
#: selection:account.analytic.line,timesheet_invoice_type:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "No task found"
msgstr ""

#. module: sale_timesheet
#: selection:account.analytic.line,timesheet_invoice_type:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Non Billable"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non Billable Hours"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Non billable"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_invoice_timesheet_count
msgid "Number of timesheets"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product_service_tracking
#: model:ir.model.fields,help:sale_timesheet.field_product_template_service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or "
"task. From those, you can track the service you are selling."
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_policy:0
msgid "Ordered quantities"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:45
#, python-format
msgid "Overview"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_fixed
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_milestone
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_time_based
msgid "Products"
msgstr "Proizvodi"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Profitability"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product_project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template_project_id
msgid "Project"
msgstr "Projekat"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_project_project_id
msgid "Project associated to this sale"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "Project(s) Overview"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:107
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_project_ids
#, python-format
msgid "Projects"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_project_ids
msgid "Projects used in this sales order."
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order
msgid "Quotation"
msgstr "Ponuda"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Rates"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line_timesheet_revenue
msgid "Revenue"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project_sale_line_id
msgid ""
"Sale order line from which the project has been created. Used for "
"tracability."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_sale_service_inherit_form2
msgid "Sales Order"
msgstr "Prodajni Nalog"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_sale_service_inherit_form2
msgid "Sales Order Item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project_sale_line_id
msgid "Sales Order Line"
msgstr "Stavka naloga za prodaju"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line_is_service
msgid ""
"Sales Order item should generate a task and/or a project, depending on the "
"product settings."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product_project_id
#: model:ir.model.fields,help:sale_timesheet.field_product_template_project_id
msgid ""
"Select a non billable project on which tasks can be created. This setting "
"must be set for each company."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product_service_tracking
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template_service_tracking
msgid "Service Tracking"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Setup your fixed price services"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Setup your milestone services"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Setup your time-based services"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line_task_id
msgid "Task"
msgstr "Zadatak"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:234
#, python-format
msgid ""
"Task Created (%s): <a href=# data-oe-model=project.task data-oe-id=%d>%s</a>"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line_task_id
msgid "Task generated by the sales order item"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:109
#: code:addons/sale_timesheet/controllers/main.py:141
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_tasks_count
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
#, python-format
msgid "Tasks"
msgstr "Zadaci"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_tasks_ids
msgid "Tasks associated to this sale"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "There is no timesheet for now."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:237
#, python-format
msgid ""
"This task has been created from: <a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Time and material"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Time by people"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_search_view_sale_timesheet
msgid "Time-based services"
msgstr ""

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/js/timesheet_plan.js:143
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_pivot_revenue
#, python-format
msgid "Timesheet"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_hr_employee_timesheet_cost
msgid "Timesheet Cost"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_timesheet_count
msgid "Timesheet activities"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:101
#: code:addons/sale_timesheet/controllers/main.py:130
#: code:addons/sale_timesheet/models/account_invoice.py:26
#: model:ir.model.fields,field_description:sale_timesheet.field_account_invoice_timesheet_ids
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
#, python-format
msgid "Timesheets"
msgstr "Karneti ( vremena rada)"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:20
#, python-format
msgid "Timesheets of %s"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_policy:0
msgid "Timesheets on tasks"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "To invoice"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:32
#, python-format
msgid ""
"You can not modify already invoiced timesheets (linked to a Sales order "
"items invoiced on Time and material)."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:74
#, python-format
msgid ""
"You cannot delete a task related to a Sales Order. You can only archive this"
" task."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_employee_extd_form
msgid "per hour"
msgstr ""
