<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <div t-name="MobileSaleOrderManagementScreen" class="screen-full-width" owl="1">
        <div t-if="mobileState.showDetails" class="leftpane">
            <OrderDetails order="orderManagementContext.selectedOrder" />
            <div class="pads">
                <div class="control-buttons">
                    <t t-foreach="controlButtons" t-as="cb" t-key="cb.name">
                        <t t-component="cb.component" t-key="cb.name" />
                    </t>
                </div>
                <div class="subpads">
                    <ActionpadWidget client="selectedClient" />
                    <NumpadWidget />
                </div>
            </div>
            <div class="back-to-list" t-on-click="mobileState.showDetails = false">
                <span>Back to list</span>
            </div>
        </div>
        <div t-else="" class="rightpane">
            <div class="flex-container">
                <SaleOrderManagementControlPanel />
                <SaleOrderList orders="orders" initHighlightedOrder="orderManagementContext.selectedOrder" />
            </div>
        </div>
    </div>

</templates>
