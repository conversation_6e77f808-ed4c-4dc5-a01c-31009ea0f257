<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.base.vat</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <div id="eu_service" position="after">
                <div class="col-12 col-lg-6 o_setting_box"
                    id="vies_service_setting"
                    title="If this checkbox is ticked, you will not be able to save a contact if its VAT number cannot be verified by the European VIES service.">
                    <div class="o_setting_left_pane">
                        <field name="vat_check_vies"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="vat_check_vies"/>
                        <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company" role="img"/>
                        <div class="text-muted">
                           Verify VAT numbers using the European VIES service
                        </div>
                    </div>
                </div>
            </div>
        </field>
    </record>

</odoo>
