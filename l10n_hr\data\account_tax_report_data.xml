<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tax_report" model="account.tax.report">
        <field name="name">Tax Report</field>
        <field name="country_id" ref="base.hr"/>
    </record>

    <record id="account_tax_report_line_ostali_porezi" model="account.tax.report.line">
        <field name="name">Ostali porezi, carine, trošarine i sl.</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
    </record>

    <record id="account_tax_report_line_porez" model="account.tax.report.line">
        <field name="name">Porez na potrošnju</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_ostali_porezi"/>
    </record>

    <record id="account_tax_report_line_pdv" model="account.tax.report.line">
        <field name="name">PDV</field>
        <field name="formula">(((koje + (izvozne + isporuke + tuzemne + ostale) + isporuke_po) + (izdani_10 + izdani_25)) + (pretporez_10 + (0.7 * pretporez_25_70 + 0.3 * pretporez_25_30) + placeni_uvozu + placeni_usluge_10 + placeni_usluge_25 + pretporez_0)) + (((izdani_racuni + izdani_racuni_25) + (pretporez_10_tax + pretporez_25_tax + placeni_uvozu_tax + placeni_usluge_10_tax + placeni_usluge_25_ta))) + ((nepriznati_pretporez_25_other + (0.3 * nepriznati_pretporez_25_30) + (0.7 * nepriznati_pretporez_25_70)) + pretporez_koji + (nepriznati_pretporez_25_tax) + (pretporez_koji_neplaceni + pretporez_koji_neplaceni_25 + pretporez_koji_neplaceni_usluge_25))</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
    </record>

    <record id="account_tax_report_line_obrazac_pdv" model="account.tax.report.line">
        <field name="name">OBRAZAC PDV</field>
        <field name="formula">(((koje + (izvozne + isporuke + tuzemne + ostale) + isporuke_po) + (izdani_10 + izdani_25)) + (pretporez_10 + (0.7 * pretporez_25_70 + 0.3 * pretporez_25_30) + placeni_uvozu + placeni_usluge_10 + placeni_usluge_25 + pretporez_0)) + (((izdani_racuni + izdani_racuni_25) + (pretporez_10_tax + pretporez_25_tax + placeni_uvozu_tax + placeni_usluge_10_tax + placeni_usluge_25_ta)))</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_pdv"/>
    </record>

    <record id="account_tax_report_line_osnovica" model="account.tax.report.line">
        <field name="name">O S N O V I C A</field>
        <field name="formula">((koje + (izvozne + isporuke + tuzemne + ostale) + isporuke_po) + (izdani_10 + izdani_25)) + (pretporez_10 + (0.7 * pretporez_25_70 + 0.3 * pretporez_25_30) + placeni_uvozu + placeni_usluge_10 + placeni_usluge_25 + pretporez_0)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_obrazac_pdv"/>
    </record>

    <record id="account_tax_report_line_obracun" model="account.tax.report.line">
        <field name="name">Obračun isporuka (I+II)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_osnovica"/>
    </record>

    <record id="account_tax_report_line_obracun_isporuke" model="account.tax.report.line">
        <field name="name">I. Isporuke ne podliježu / oslobođene</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_obracun"/>
    </record>

    <record id="account_tax_report_line_koje" model="account.tax.report.line">
        <field name="name">I.1. Koje ne podliježu oporezivanju</field>
        <field name="tag_name">I.1. Koje ne podliježu oporezivanju</field>
        <field name="code">koje</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_obracun_isporuke"/>
    </record>

    <record id="account_tax_report_line_oslobodene" model="account.tax.report.line">
        <field name="name">I.2. Oslobođene ukupno</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_obracun_isporuke"/>
    </record>

    <record id="account_tax_report_line_izvozne" model="account.tax.report.line">
        <field name="name">I.2.1. Izvozne</field>
        <field name="tag_name">I.2.1. Izvozne</field>
        <field name="code">izvozne</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_oslobodene"/>
    </record>

    <record id="account_tax_report_line_isporuke" model="account.tax.report.line">
        <field name="name">I.2.2. Isporuke dobara</field>
        <field name="tag_name">I.2.2. Isporuke dobara</field>
        <field name="code">isporuke</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_oslobodene"/>
    </record>

    <record id="account_tax_report_line_tuzemne" model="account.tax.report.line">
        <field name="name">I.2.3. Tuzemne  - bez prava odbitka</field>
        <field name="tag_name">I.2.3. Tuzemne  - bez prava odbitka</field>
        <field name="code">tuzemne</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_oslobodene"/>
    </record>

    <record id="account_tax_report_line_ostale" model="account.tax.report.line">
        <field name="name">I.2.4. Ostale – s pravom odbitka</field>
        <field name="tag_name">I.2.4. Ostale – s pravom odbitka</field>
        <field name="code">ostale</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_oslobodene"/>
    </record>

    <record id="account_tax_report_line_isporuke_po" model="account.tax.report.line">
        <field name="name">I.3. Isporuke po stopi od 0%</field>
        <field name="tag_name">I.3. Isporuke po stopi od 0%</field>
        <field name="code">isporuke_po</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_obracun_isporuke"/>
    </record>

    <record id="account_tax_report_line_oporezive" model="account.tax.report.line">
        <field name="name">II. Oporezive isporuke</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_obracun"/>
    </record>

    <record id="account_tax_report_line_izdani_10" model="account.tax.report.line">
        <field name="name">II.1 Izdani računi po stopi 10%</field>
        <field name="tag_name">II.1 Izdani računi po stopi 10% (osnovica)</field>
        <field name="code">izdani_10</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_oporezive"/>
    </record>

    <record id="account_tax_report_line_izdani_22_i_23" model="account.tax.report.line">
        <field name="name">II.2 Izdani računi po stopi 22% i 23%</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_oporezive"/>
    </record>

    <record id="account_tax_report_line_izdani_25" model="account.tax.report.line">
        <field name="name">II.3 Izdani računi po stopi 25%</field>
        <field name="tag_name">II.3 Izdani računi po stopi 25% (osnovica)</field>
        <field name="code">izdani_25</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_oporezive"/>
    </record>

    <record id="account_tax_report_line_nenaplaceni" model="account.tax.report.line">
        <field name="name">II.4 Nenaplaćeni izvoz</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_oporezive"/>
    </record>

    <record id="account_tax_report_line_oslobodenje" model="account.tax.report.line">
        <field name="name">II.5 Oslobođenje izvoza – putnički promet</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="5"/>
        <field name="parent_id" ref="account_tax_report_line_oporezive"/>
    </record>

    <record id="account_tax_report_line_obracunani" model="account.tax.report.line">
        <field name="name">III. OBRAČUNANI PRETPOREZ</field>
        <field name="formula">pretporez_10 + (0.7 * pretporez_25_70 + 0.3 * pretporez_25_30) + placeni_uvozu + placeni_usluge_10 + placeni_usluge_25 + pretporez_0</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_osnovica"/>
    </record>

    <record id="account_tax_report_line_pretporez_10" model="account.tax.report.line">
        <field name="name">III.1. Pretporez 10%</field>
        <field name="tag_name">III.1. Pretporez 10% (osnovica)</field>
        <field name="code">pretporez_10</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani"/>
    </record>

    <record id="account_tax_report_line_pretporez_22_i_23" model="account.tax.report.line">
        <field name="name">III.2. Pretporez 22% i23%</field>
        <field name="tag_name">III.2. Pretporez 22% i23%</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani"/>
    </record>

    <record id="account_tax_report_line_pretporez_25" model="account.tax.report.line">
        <field name="name">III.3. Pretporez 25%</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="formula">0.3 * pretporez_25_30 + 0.7 * pretporez_25_70</field>
        <field name="parent_id" ref="account_tax_report_line_obracunani"/>
    </record>

    <record id="account_tax_report_line_pretporez_25_30" model="account.tax.report.line">
        <field name="name">III.3. Pretporez 25% (30%)</field>
        <field name="tag_name">III.3. Pretporez 25% (30%)</field>
        <field name="code">pretporez_25_30</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_pretporez_25"/>
    </record>

    <record id="account_tax_report_line_pretporez_25_70" model="account.tax.report.line">
        <field name="name">III.3. Pretporez 25% (70%)</field>
        <field name="tag_name">III.3. Pretporez 25% (70%)</field>
        <field name="code">pretporez_25_70</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_pretporez_25"/>
    </record>

    <record id="account_tax_report_line_placeni_uvozu" model="account.tax.report.line">
        <field name="name">III.4. Plaćeni PP pri uvozu</field>
        <field name="tag_name">III.4. Plaćeni PP pri uvozu (osnovica)</field>
        <field name="code">placeni_uvozu</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani"/>
    </record>

    <record id="account_tax_report_line_placeni_usluge_10" model="account.tax.report.line">
        <field name="name">III.5. Plaćeni PP na ino usluge 10%</field>
        <field name="tag_name">III.5. Plaćeni PP na ino usluge 10% (osnovica)</field>
        <field name="code">placeni_usluge_10</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="5"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani"/>
    </record>

    <record id="account_tax_report_line_placeni_usluge_22_i_23" model="account.tax.report.line">
        <field name="name">III.6. Plaćeni PP na ino usluge 22% i 23%</field>
        <field name="tag_name">III.6. Plaćeni PP na ino usluge 22% i 23%</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="6"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani"/>
    </record>

    <record id="account_tax_report_line_placeni_usluge_25" model="account.tax.report.line">
        <field name="name">III.7. Plaćeni PP na ino usluge 25%</field>
        <field name="tag_name">III.7. Plaćeni PP na ino usluge 25% (osnovica)</field>
        <field name="code">placeni_usluge_25</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="7"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani"/>
    </record>

    <record id="account_tax_report_line_pretporez_0" model="account.tax.report.line">
        <field name="name">III.0. Pretporez 0%</field>
        <field name="tag_name">III.0. Pretporez 0%</field>
        <field name="code">pretporez_0</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="8"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani"/>
    </record>

    <record id="account_tax_report_line_pdv_porez" model="account.tax.report.line">
        <field name="name">PDV – POREZ – razlika za uplatu/preplata</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_obrazac_pdv"/>
    </record>

    <record id="account_tax_report_line_porezna" model="account.tax.report.line">
        <field name="name">VI. POREZNA OBVEZA U RAZDOBLJU</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_pdv_porez"/>
    </record>

    <record id="account_tax_report_line_isporuke_ne" model="account.tax.report.line">
        <field name="name">I. Isporuke ne podliježu / oslobođene</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_porezna"/>
    </record>

    <record id="account_tax_report_line_oporezive_isporuke" model="account.tax.report.line">
        <field name="name">II. Oporezive isporuke</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_porezna"/>
    </record>

    <record id="account_tax_report_line_izdani_racuni" model="account.tax.report.line">
        <field name="name">II.1 Izdani računi po stopi 10%</field>
        <field name="tag_name">II.1 Izdani računi po stopi 10% (porez)</field>
        <field name="code">izdani_racuni</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_oporezive_isporuke"/>
    </record>

    <record id="account_tax_report_line_izdani_racuni_22_i_23" model="account.tax.report.line">
        <field name="name">II.2 Izdani računi po stopi 22% i 23%</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_oporezive_isporuke"/>
    </record>

    <record id="account_tax_report_line_izdani_racuni_25" model="account.tax.report.line">
        <field name="name">II.3 Izdani računi po stopi 25%</field>
        <field name="tag_name">II.3 Izdani računi po stopi 25% (porez)</field>
        <field name="code">izdani_racuni_25</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_oporezive_isporuke"/>
    </record>

    <record id="account_tax_report_line_oslobodenje_izvoza" model="account.tax.report.line">
        <field name="name">II.4 Oslobođenje izvoza – putnički promet</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_oporezive_isporuke"/>
    </record>

    <record id="account_tax_report_line_obracunani_pretporez" model="account.tax.report.line">
        <field name="name">III. OBRAČUNANI PRETPOREZ</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_porezna"/>
    </record>

    <record id="account_tax_report_line_pretporez_10_tax" model="account.tax.report.line">
        <field name="name">III.1. Pretporez 10%</field>
        <field name="tag_name">III.1. Pretporez 10% (porez)</field>
        <field name="code">pretporez_10_tax</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani_pretporez"/>
    </record>

    <record id="account_tax_report_line_pretporez_22_i_23_tax" model="account.tax.report.line">
        <field name="name">III.2. Pretporez 22% i 23%</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani_pretporez"/>
    </record>

    <record id="account_tax_report_line_pretporez_25_tax" model="account.tax.report.line">
        <field name="name">III.3. Pretporez 25%</field>
        <field name="tag_name">III.3. Pretporez 25%</field>
        <field name="code">pretporez_25_tax</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani_pretporez"/>
    </record>

    <record id="account_tax_report_line_placeni_uvozu_tax" model="account.tax.report.line">
        <field name="name">III.4. Plaćeni PP pri uvozu</field>
        <field name="tag_name">III.4. Plaćeni PP pri uvozu (porez)</field>
        <field name="code">placeni_uvozu_tax</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani_pretporez"/>
    </record>

    <record id="account_tax_report_line_placeni_usluge_10_tax" model="account.tax.report.line">
        <field name="name">III.5. Plaćeni PP na ino usluge 10%</field>
        <field name="tag_name">III.5. Plaćeni PP na ino usluge 10% (porez)</field>
        <field name="code">placeni_usluge_10_tax</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="5"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani_pretporez"/>
    </record>

    <record id="account_tax_report_line_placeni_usluge_22_i_23_tax" model="account.tax.report.line">
        <field name="name">III.6. Plaćeni PP na ino usluge 22% i 23%</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="6"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani_pretporez"/>
    </record>

    <record id="account_tax_report_line_placeni_usluge_25_tax" model="account.tax.report.line">
        <field name="name">III.7. Plaćeni PP na ino usluge 25%</field>
        <field name="tag_name">III.7. Plaćeni PP na ino usluge 25% (porez)</field>
        <field name="code">placeni_usluge_25_ta</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="7"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani_pretporez"/>
    </record>

    <record id="account_tax_report_line_ispravci" model="account.tax.report.line">
        <field name="name">III.8. Ispravci pretporeza</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="8"/>
        <field name="parent_id" ref="account_tax_report_line_obracunani_pretporez"/>
    </record>

    <record id="account_tax_report_line_uplaceno" model="account.tax.report.line">
        <field name="name">Uplaćeno u razdoblju</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_pdv_porez"/>
    </record>

    <record id="account_tax_report_line_pdv_ostalo" model="account.tax.report.line">
        <field name="name">PDV - OSTALO</field>
        <field name="formula">(nepriznati_pretporez_25_other + (0.3 * nepriznati_pretporez_25_30) + (0.7 * nepriznati_pretporez_25_70)) + pretporez_koji + (nepriznati_pretporez_25_tax) + (pretporez_koji_neplaceni + pretporez_koji_neplaceni_25 + pretporez_koji_neplaceni_usluge_25)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_pdv"/>
    </record>

    <record id="account_tax_report_line_ostali_podaci" model="account.tax.report.line">
        <field name="name">Ostali podaci</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_pdv_ostalo"/>
    </record>

    <record id="account_tax_report_line_ispravak" model="account.tax.report.line">
        <field name="name">1. ZA ISPRAVAK PRETPOREZA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_ostali_podaci"/>
    </record>

    <record id="account_tax_report_line_nabava" model="account.tax.report.line">
        <field name="name">1.1. NABAVA NEKRETNINA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_ispravak"/>
    </record>

    <record id="account_tax_report_line_prodaja" model="account.tax.report.line">
        <field name="name">1.2. PRODAJA NEKRETNINA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_ispravak"/>
    </record>

    <record id="account_tax_report_line_nabava_osobnih" model="account.tax.report.line">
        <field name="name">1.3. NABAVA OSOBNIH VOZILA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_ispravak"/>
    </record>

    <record id="account_tax_report_line_prodaja_osobnih" model="account.tax.report.line">
        <field name="name">1.4. PRODAJA OSOBNIH VOZILA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_ispravak"/>
    </record>

    <record id="account_tax_report_line_nabava_dugotrajne" model="account.tax.report.line">
        <field name="name">1.5. NABAVA DUGOTRAJNE IMOVINE</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="5"/>
        <field name="parent_id" ref="account_tax_report_line_ispravak"/>
    </record>

    <record id="account_tax_report_line_prodaja_dugotrajne" model="account.tax.report.line">
        <field name="name">1.6. PRODAJA DUGOTRAJNE IMOVINE</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="6"/>
        <field name="parent_id" ref="account_tax_report_line_ispravak"/>
    </record>

    <record id="account_tax_report_line_otudenje" model="account.tax.report.line">
        <field name="name">2. OTUĐENJE/STJECANJE GOSPODARSKE CJELINE ILI POGONA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_ostali_podaci"/>
    </record>

    <record id="account_tax_report_line_nabava_dobara" model="account.tax.report.line">
        <field name="name">3. NABAVA DOBARA I USLUGA ZA REPREZENTACIJU</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_ostali_podaci"/>
    </record>

    <record id="account_tax_report_line_nabava_osobnih_vozila" model="account.tax.report.line">
        <field name="name">4. NABAVA OSOBNIH VOZILA I DRUGIH SREDSTAVA ZA OSOBNI PRIJEVOZ</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_ostali_podaci"/>
    </record>

    <record id="account_tax_report_line_osnovica_za_obracun" model="account.tax.report.line">
        <field name="name">5. OSNOVICA ZA OBRAČUN VLASTITE POTROŠNJE ZA OSOBNA VOZILA NABAV</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="5"/>
        <field name="parent_id" ref="account_tax_report_line_ostali_podaci"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez" model="account.tax.report.line">
        <field name="name">NEPRIZNATI PRETPOREZ</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="formula">0.3 * nepriznati_pretporez_25_30 + 0.7 * nepriznati_pretporez_25_70 + nepriznati_pretporez_25_other + pretporez_koji</field>
        <field name="parent_id" ref="account_tax_report_line_pdv_ostalo"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_osnovica_3070" model="account.tax.report.line">
        <field name="name">NEPRIZNATI PRETPOREZ (osnovica 30 ili 70%)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="formula">0.3 * nepriznati_pretporez_25_30 + 0.7 * nepriznati_pretporez_25_70 + nepriznati_pretporez_25_other</field>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_10" model="account.tax.report.line">
        <field name="name">Nepriznati pretporez 10% (o)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_osnovica_3070"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_22_i_23" model="account.tax.report.line">
        <field name="name">Nepriznati pretporez 22% i 23% (o)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_osnovica_3070"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_25" model="account.tax.report.line">
        <field name="name">Nepriznati pretporez 25% (o)</field>
        <field name="formula">0.3 * nepriznati_pretporez_25_30 + 0.7 * nepriznati_pretporez_25_70 + nepriznati_pretporez_25_other</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_osnovica_3070"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_25_30" model="account.tax.report.line">
        <field name="name">Nepriznati pretporez 25% (o) (30%)</field>
        <field name="tag_name">Nepriznati pretporez 25% (o) (30%)</field>
        <field name="code">nepriznati_pretporez_25_30</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_25"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_25_70" model="account.tax.report.line">
        <field name="name">Nepriznati pretporez 25% (o) (70%)</field>
        <field name="tag_name">Nepriznati pretporez 25% (o) (70%)</field>
        <field name="code">nepriznati_pretporez_25_70</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_25"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_25_other" model="account.tax.report.line">
        <field name="name">Nepriznati pretporez 25% (o)</field>
        <field name="tag_name">Nepriznati pretporez 25% (o)</field>
        <field name="code">nepriznati_pretporez_25_other</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_25"/>
    </record>

    <record id="account_tax_report_line_pretporez_koji" model="account.tax.report.line">
        <field name="name">Pretporez koji još nije priznan (uključivo i neplaćeni R-2)</field>
        <field name="tag_name">Pretporez koji još nije priznan (uključivo i neplaćeni R-2)</field>
        <field name="code">pretporez_koji</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_porez" model="account.tax.report.line">
        <field name="name">NEPRIZNATI PRETPOREZ (porez)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_3070" model="account.tax.report.line">
        <field name="name">NEPRIZNATI PRETPOREZ (30 ili 70%)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_porez"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_10_tax" model="account.tax.report.line">
        <field name="name">Nepriznati pretporez 10% (p)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_3070"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_22_i_23_tax" model="account.tax.report.line">
        <field name="name">Nepriznati pretporez 22% i 23% (p)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_3070"/>
    </record>

    <record id="account_tax_report_line_nepriznati_pretporez_25_tax" model="account.tax.report.line">
        <field name="name">Nepriznati pretporez 25% (p)</field>
        <field name="tag_name">Nepriznati pretporez 25% (p)</field>
        <field name="code">nepriznati_pretporez_25_tax</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez_3070"/>
    </record>

    <record id="account_tax_report_line_pretporez_koji_ukupno" model="account.tax.report.line">
        <field name="name">Pretporez koji još nije priznan (ukupno)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_nepriznati_pretporez"/>
    </record>

    <record id="account_tax_report_line_pretporez_koji_neplaceni" model="account.tax.report.line">
        <field name="name">Pretporez koji još nije priznan (neplaćeni R2)</field>
        <field name="tag_name">Pretporez koji još nije priznan (neplaćeni R2)</field>
        <field name="code">pretporez_koji_neplaceni</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_pretporez_koji_ukupno"/>
    </record>

    <record id="account_tax_report_line_pretporez_koji_neplaceni_25" model="account.tax.report.line">
        <field name="name">Pretporez koji još nije priznan (neplaćeni UVOZ dobara 25%)</field>
        <field name="tag_name">Pretporez koji još nije priznan (neplaćeni UVOZ dobara 25%)</field>
        <field name="code">pretporez_koji_neplaceni_25</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_pretporez_koji_ukupno"/>
    </record>

    <record id="account_tax_report_line_pretporez_koji_neplaceni_usluge_25" model="account.tax.report.line">
        <field name="name">Pretporez koji još nije priznan (neplaćene INO usluge 25%)</field>
        <field name="tag_name">Pretporez koji još nije priznan (neplaćene INO usluge 25%)</field>
        <field name="code">pretporez_koji_neplaceni_usluge_25</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_pretporez_koji_ukupno"/>
    </record>

    <record id="account_tax_report_line_pretporez_koji_neplaceni_usluge_10" model="account.tax.report.line">
        <field name="name">Pretporez koji još nije priznan (neplaćene INO usluge 10%)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_pretporez_koji_ukupno"/>
    </record>

</odoo>
