# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fr_pos_cert
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-27 14:38+0000\n"
"PO-Revision-Date: 2021-10-27 14:38+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/res_company.py:0
#, python-format
msgid "(Receipt ref.: %s)"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid "According to French law, you cannot delet a point of sale order."
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"According to the French law, you cannot modify a %s. Forbidden fields: %s."
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"According to the French law, you cannot modify a point of sale order line. "
"Forbidden fields: %s."
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"According to the French law, you cannot modify a point of sale order. "
"Forbidden fields: %s."
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.form_view_account_sale_closing
msgid "Account Closing"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/res_company.py:0
#, python-format
msgid ""
"Accounting is not unalterable for the company %s. This mechanism is designed"
" for companies where accounting is unalterable."
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"An error occured when computing the inalterability. Impossible to get the "
"unique previous posted point of sale order."
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields.selection,name:l10n_fr_pos_cert.selection__account_sale_closing__frequency__annually
msgid "Annual"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#, python-format
msgid "Annual Closing"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_account_bank_statement
msgid "Bank Statement"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__date_closing_stop
msgid "Closing Date"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__frequency
msgid "Closing Type"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__company_id
msgid "Company"
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "Contrôle des données du point de vente"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/res_company.py:0
#, python-format
msgid "Corrupted data on point of sale order with id %s."
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__create_date
msgid "Created on"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__cumulative_total
msgid "Cumulative Grand Total"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields.selection,name:l10n_fr_pos_cert.selection__account_sale_closing__frequency__daily
msgid "Daily"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#, python-format
msgid "Daily Closing"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__date_closing_start
msgid "Date from which the total interval is computed"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__date_closing_stop
msgid "Date to which the values are computed"
msgstr ""

#. module: l10n_fr_pos_cert
#. openerp-web
#: code:addons/l10n_fr_pos_cert/static/src/js/pos.js:0
#, python-format
msgid "Deleting of orders is not allowed."
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement_line__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_config__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_session__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_report_l10n_fr_pos_cert_report_pos_hash_integrity__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_res_company__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "First Entry"
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "First Hash"
msgstr ""

#. module: l10n_fr_pos_cert
#. openerp-web
#: code:addons/l10n_fr_pos_cert/static/src/js/pos.js:0
#, python-format
msgid "Fiscal Data Module error"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_account_fiscal_position
msgid "Fiscal Position"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.ui.menu,name:l10n_fr_pos_cert.pos_fr_statements_menu
msgid "French Statements"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__name
msgid "Frequency and unique sequence number"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.actions.server,name:l10n_fr_pos_cert.account_sale_closing_annually_ir_actions_server
#: model:ir.cron,cron_name:l10n_fr_pos_cert.account_sale_closing_annually
#: model:ir.cron,name:l10n_fr_pos_cert.account_sale_closing_annually
msgid "Generate Annual Sales Closing"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.actions.server,name:l10n_fr_pos_cert.account_sale_closing_daily_ir_actions_server
#: model:ir.cron,cron_name:l10n_fr_pos_cert.account_sale_closing_daily
#: model:ir.cron,name:l10n_fr_pos_cert.account_sale_closing_daily
msgid "Generate Daily Sales Closing"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.actions.server,name:l10n_fr_pos_cert.account_sale_closing_monthly_ir_actions_server
#: model:ir.cron,cron_name:l10n_fr_pos_cert.account_sale_closing_monthly
#: model:ir.cron,name:l10n_fr_pos_cert.account_sale_closing_monthly
msgid "Generate Monthly Sales Closing"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_report_l10n_fr_pos_cert_report_pos_hash_integrity
msgid "Get french pos hash integrity result as PDF."
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.pos_order_form_inherit
msgid "Hash"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.actions.report,name:l10n_fr_pos_cert.action_report_pos_hash_integrity
msgid "Hash integrity result PDF"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement_line__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_config__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order_line__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_session__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_report_l10n_fr_pos_cert_report_pos_hash_integrity__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_res_company__id
msgid "ID"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__l10n_fr_hash
msgid "Inalteralbility Hash"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__l10n_fr_secure_sequence_number
msgid "Inalteralbility No Gap Sequence #"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_res_company__l10n_fr_pos_cert_sequence_id
msgid "L10N Fr Pos Cert Sequence"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__l10n_fr_string_to_hash
msgid "L10N Fr String To Hash"
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid ""
"La chaîne de hachage est conforme: il n’est pas possible d’altérer les données\n"
"                                            sans casser la chaîne de hachage pour les pièces ultérieures."
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "Last Entry"
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "Last Hash"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement_line____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_config____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order_line____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_session____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_report_l10n_fr_pos_cert_report_pos_hash_integrity____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_res_company____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__last_order_hash
msgid "Last Order entry's inalteralbility hash"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__last_order_id
msgid "Last Pos Order"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__last_order_id
msgid "Last Pos order included in the grand total"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_fr_pos_cert
#. openerp-web
#: code:addons/l10n_fr_pos_cert/static/src/js/pos.js:0
#, python-format
msgid "Missing Country"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields.selection,name:l10n_fr_pos_cert.selection__account_sale_closing__frequency__monthly
msgid "Monthly"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#, python-format
msgid "Monthly Closing"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__name
msgid "Name"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.actions.server,name:l10n_fr_pos_cert.action_check_pos_hash_integrity
#: model:ir.ui.menu,name:l10n_fr_pos_cert.menu_check_move_integrity_reporting
msgid "POS Inalterability Check"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__total_interval
msgid "Period Total"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_pos_config
msgid "Point of Sale Configuration"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_pos_order
msgid "Point of Sale Orders"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_pos_session
msgid "Point of Sale Session"
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "Résultat du test d'intégrité -"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_account_sale_closing
msgid "Sale Closing"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#, python-format
msgid ""
"Sale Closings are not meant to be written or deleted under any "
"circumstances."
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.actions.act_window,name:l10n_fr_pos_cert.action_list_view_account_sale_closing
#: model:ir.ui.menu,name:l10n_fr_pos_cert.menu_account_closing
#: model:ir.ui.menu,name:l10n_fr_pos_cert.menu_account_closing_reporting
msgid "Sales Closings"
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.actions.act_window,help:l10n_fr_pos_cert.action_list_view_account_sale_closing
msgid ""
"Sales closings run automatically on a daily, monthly and annual basis. It "
"computes both period and cumulative totals from all the sales entries posted"
" in the system after the previous closing."
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid ""
"Selon l’article 286 du code général des impôts français, toute livraison de bien ou prestation\n"
"                                        de services ne donnant pas lieu à facturation et étant enregistrée au moyen d’un logiciel ou\n"
"                                        d’un système de caisse doit satisfaire à des conditions d’inaltérabilité et de sécurisation des\n"
"                                        données en vue d’un contrôle de l’administration fiscale.\n"
"                                        <br/>\n"
"                                        <br/>\n"
"                                        Ces conditions sont respectées via une fonction de hachage des ventes du Point de Vente.\n"
"                                        <br/>\n"
"                                        <br/>"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__sequence_number
msgid "Sequence #"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__date_closing_start
msgid "Starting Date"
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.actions.act_window,help:l10n_fr_pos_cert.action_list_view_account_sale_closing
msgid "The closings are created by Odoo"
msgstr ""

#. module: l10n_fr_pos_cert
#. openerp-web
#: code:addons/l10n_fr_pos_cert/static/src/js/pos.js:0
#, python-format
msgid "The company %s doesn't have a country set."
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__currency_id
msgid "The company's currency"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/res_company.py:0
#, python-format
msgid ""
"There isn't any order flagged for data inalterability yet for the company "
"%s. This mechanism only runs for point of sale orders generated after the "
"installation of the module France - Certification CGI 286 I-3 bis. - POS"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"This session has been opened another day. To comply with the French law, you"
" should close sessions on a daily basis. Please close session %s and open a "
"new one."
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__total_interval
msgid ""
"Total in receivable accounts during the interval, excluding overlapping "
"periods"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__cumulative_total
msgid "Total in receivable accounts since the beginnig of times"
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid ""
"Toutes les ventes effectuées via le Point de Vente\n"
"                                                sont bien dans la chaîne de hachage."
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_fiscal_position.py:0
#, python-format
msgid ""
"You cannot modify a fiscal position used in a POS order. You should archive "
"it and create a new one."
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_bank_statement.py:0
#, python-format
msgid ""
"You cannot modify anything on a bank statement (name: %s) that was created "
"by point of sale operations."
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_bank_statement.py:0
#, python-format
msgid ""
"You cannot modify anything on a bank statement line (name: %s) that was "
"created by point of sale operations."
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"You cannot overwrite the values ensuring the inalterability of the point of "
"sale."
msgstr ""
