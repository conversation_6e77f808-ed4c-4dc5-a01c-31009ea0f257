<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
<template id="report_purchaserequisitions">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-call="web.external_layout">
                <t t-set="address">
                    <div t-field="o.vendor_id"
                        t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                    <p t-if="o.vendor_id.vat"><t t-esc="o.company_id.account_fiscal_country_id.vat_label or 'Tax ID'"/>: <span t-field="o.vendor_id.vat"/></p>
                </t>
                <div class="page">
                    <div class="oe_structure"/>

                    <h2><span t-out="o.type_id.name"/> <span t-field="o.name"/></h2>

                    <div class="row my-2">
                        <div class="col-3">
                            <strong><span t-out="o.type_id.name"/> Reference:</strong><br/>
                            <span t-field="o.name"/>
                        </div>
                        <div class="col-3">
                            <strong>Scheduled Ordering Date:</strong><br/>
                            <span t-field="o.ordering_date"/>
                        </div>
                        <div class="col-3">
                            <strong>Agreement Deadline:</strong><br/>
                            <span t-field="o.date_end"/>
                        </div>
                        <div class="col-3">
                            <strong>Source:</strong><br/>
                            <span t-field="o.origin"/>
                        </div>
                    </div>

                    <t t-if="o.line_ids">
                        <h3>Products</h3>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th><strong>Product</strong></th>
                                    <th><strong>Description</strong></th>
                                    <th class="text-right"><strong>Qty</strong></th>
                                    <th class="text-center" groups="uom.group_uom">
                                        <strong>Product UoM</strong>
                                    </th>
                                    <th t-if="o.type_id == env.ref('purchase_requisition.type_single')">Price Unit</th>
                                    <th class="text-right"><strong>Scheduled Date</strong></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="o.line_ids" t-as="line_ids">
                                    <td>
                                        <t t-if="line_ids.product_id.code"><!--internal reference exists-->
                                            [ <span t-field="line_ids.product_id.code"/> ]
                                        </t>
                                        <span t-field="line_ids.product_id.name"/>
                                    </td>
                                    <td>
                                        <span t-field="line_ids.product_description_variants"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line_ids.product_qty"/>
                                    </td>
                                    <t>
                                        <td class="text-center" groups="uom.group_uom">
                                            <span t-field="line_ids.product_uom_id.name"/>
                                        </td>
                                    </t>
                                    <td t-if="o.type_id == env.ref('purchase_requisition.type_single')">
                                        <span t-field="line_ids.price_unit" t-options='{"widget": "monetary", "display_currency": line_ids.requisition_id.currency_id}'/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line_ids.schedule_date"/>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </t>
                    
                    <t t-if="o.purchase_ids">
                        <h3>Requests for Quotation Details</h3>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th><strong>Vendor </strong></th>
                                    <th class="text-right"><strong>Date</strong></th>
                                    <th class="text-right"><strong>Reference </strong></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="o.purchase_ids" t-as="purchase_ids">
                                    <td>
                                        <span t-field="purchase_ids.partner_id.name"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="purchase_ids.date_order"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="purchase_ids.name"/>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </t>

                    <div t-if="o.description" t-out="o.description"/>

                    <div class="oe_structure"/>
                </div>
            </t>
        </t>
    </t>
</template>
</data>
</odoo>
