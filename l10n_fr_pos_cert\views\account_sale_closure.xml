<odoo>
    <record id="list_view_account_sale_closing" model="ir.ui.view">
        <field name="name">Sales Closings</field>
        <field name="model">account.sale.closing</field>
        <field name="arch" type="xml">
            <tree create="false" import="false">
                <field name="date_closing_start"/>
                <field name="date_closing_stop"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="currency_id" invisible="1"/>
                <field name="frequency"/>
                <field name="sequence_number" groups="base.group_no_one"/>
                <field name="total_interval"/>
                <field name="cumulative_total"/>
            </tree>
        </field>
    </record>

    <record id="form_view_account_sale_closing" model="ir.ui.view">
        <field name="name">Sales Closings</field>
        <field name="model">account.sale.closing</field>
        <field name="arch" type="xml">
            <form create="false" edit="false" string="Account Closing">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="date_closing_start"/>
                            <field name="date_closing_stop"/>
                            <field name="frequency"/>
                            <field name="sequence_number" groups="base.group_no_one"/>
                        </group>
                        <group>
                            <field name="total_interval"/>
                            <field name="cumulative_total"/>
                            <field name="last_order_id" groups="account.group_account_readonly"/>
                            <field name="last_order_hash" groups="account.group_account_readonly"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="currency_id" invisible="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_list_view_account_sale_closing" model="ir.actions.act_window">
        <field name="name">Sales Closings</field>
        <field name="res_model">account.sale.closing</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_nocreate">
                The closings are created by Odoo
                </p><p>
                Sales closings run automatically on a daily, monthly and annual basis. It computes both period and cumulative totals from all the sales entries posted in the system after the previous closing.
            </p>
        </field>
    </record>

    <menuitem action="action_list_view_account_sale_closing" id="menu_account_closing_reporting" parent="l10n_fr.account_reports_fr_statements_menu" sequence="90"/>
</odoo>
