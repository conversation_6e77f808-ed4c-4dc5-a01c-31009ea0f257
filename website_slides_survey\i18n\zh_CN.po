# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides_survey
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_slides_survey
#: model:mail.template,body_html:website_slides_survey.mail_template_user_input_certification_failed
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant' or ''\">participant</t><br/><br/>\n"
"        Unfortunately, you have failed the certification and are no longer a member of the course: <t t-out=\"object.slide_partner_id.channel_id.name or ''\">Basics of Gardening</t>.<br/><br/>\n"
"        Don't hesitate to enroll again!\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.slide_partner_id.channel_id.website_url)\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                Enroll now\n"
"            </a>\n"
"        </div>\n"
"        Thank you for your participation.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        亲爱的 <t t-out=\"object.partner_id.name or 'participant' or ''\">参与者</t><br/><br/>\n"
"        不幸的是，您未通过认证并且不再是该课程的成员: <t t-out=\"object.slide_partner_id.channel_id.name or ''\">园艺基础</t>.<br/><br/>\n"
"        不要犹豫再次报名！\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.slide_partner_id.channel_id.website_url)\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                现在报名\n"
"            </a>\n"
"        </div>\n"
"        感谢您的参与。\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "<i class=\"fa fa-arrow-right mr-1\"/>All Certifications"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>所有认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid ""
"<i class=\"fa fa-download\" aria-label=\"Download certification\" "
"title=\"Download Certification\"/>"
msgstr ""
"<i class=\"fa fa-download\" aria-label=\"下载证书\" title=\"Download "
"Certification\"/>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download "
"certification\" title=\"Download certification\"/> Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"下载 certification\""
" title=\"Download certification\"/> 下载证书"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_fill_form_done_inherit_website_slides
msgid ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share certification\" title=\"Share certification\"/>\n"
"                        Share your certification"
msgstr ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share certification\" title=\"分享认证\"/>\n"
"                        分享您的认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"Share\"/>"
msgstr "<i class=\"fa fa-share-alt\" aria-label=\"分享\" title=\"分享\"/>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.badge_content
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr "<i class=\"text-muted\"> 授予用户</i>"

#. module: website_slides_survey
#: model:survey.question,description:website_slides_survey.furniture_certification_page_1
#: model:survey.survey,description:website_slides_survey.furniture_certification
msgid "<p>Test your furniture knowledge!</p>"
msgstr "<p>测试您的家具知识！</p>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.courses_home_inherit_survey
msgid "<span class=\"ml-1\">Certifications</span>"
msgstr "<span class=\"ml-1\">证书</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('nbr_certification', '&gt;', 0)]}\">Finished</span>\n"
"                <span class=\"o_stat_text\" attrs=\"{'invisible': [('nbr_certification', '=', 0)]}\">Certified</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('nbr_certification', '&gt;', 0)]}\">已完成</span>\n"
"                <span class=\"o_stat_text\" attrs=\"{'invisible': [('nbr_certification', '=', 0)]}\">已鉴定</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.all_user_card
msgid "<span class=\"text-muted small font-weight-bold\">Certifications</span>"
msgstr "<span class=\"text-muted small font-weight-bold\">证书</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.top3_user_card
msgid "<span class=\"text-muted\">Certifications</span>"
msgstr "<span class=\"text-muted\">证书</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Certified</span>"
msgstr "<span class=\"text-muted\">认证</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Finished</span>"
msgstr "<span class=\"text-muted\">已完成</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.course_main
msgid "<span>Start Now</span><i class=\"fa fa-chevron-right ml-2 align-middle\"/>"
msgstr "<span>立即开始</span><i class=\"fa fa-chevron-right ml-2 align-middle\"/>"

#. module: website_slides_survey
#: model:ir.model.constraint,message:website_slides_survey.constraint_slide_slide_check_survey_id
msgid "A slide of type 'certification' requires a certification."
msgstr "“认证”类型的幻灯片需要认证。"

#. module: website_slides_survey
#: model:ir.model.constraint,message:website_slides_survey.constraint_slide_slide_check_certification_preview
msgid "A slide of type certification cannot be previewed."
msgstr "无法预览类型认证的幻灯片。"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_form
msgid "Add Certification"
msgstr "添加认证"

#. module: website_slides_survey
#: model_terms:ir.actions.act_window,help:website_slides_survey.slide_slide_action_certification
#: model_terms:ir.actions.act_window,help:website_slides_survey.survey_survey_action_slides
#: model_terms:ir.actions.act_window,help:website_slides_survey.survey_survey_action_slides_report
msgid "Add a new certification"
msgstr "添加新的认证"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_3
msgid "Ash"
msgstr "灰烬"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_5
msgid "Bed"
msgstr "床"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_4
msgid "Beech"
msgstr "榉木"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid "Begin Certification"
msgstr "开始认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.course_main
msgid "Begin your <b>certification</b> today!"
msgstr "今天开始 <b>认证</b>!"

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/js/slides_upload.js:0
#: code:addons/website_slides_survey/static/src/js/slides_upload.js:0
#: code:addons/website_slides_survey/static/src/xml/website_slide_upload.xml:0
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__survey_id
#: model:ir.model.fields.selection,name:website_slides_survey.selection__slide_slide__slide_type__certification
#, python-format
msgid "Certification"
msgstr "认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.badge_content
msgid "Certification Badges"
msgstr "认证徽标"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_survey__slide_channel_ids
msgid "Certification Courses"
msgstr "认证课程"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_survey__slide_ids
msgid "Certification Slides"
msgstr "认证幻灯片"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide_partner__survey_scoring_success
msgid "Certification Succeeded"
msgstr "认证成功"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide_partner__user_input_ids
msgid "Certification attempts"
msgstr "认证尝试"

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/js/slides_certification_upload_toast.js:0
#, python-format
msgid "Certification created"
msgstr "已创建认证"

#. module: website_slides_survey
#: model:mail.template,name:website_slides_survey.mail_template_user_input_certification_failed
msgid "Certification failed email"
msgstr "认证失败邮件"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.o_wss_certification_icon
msgid "Certification icon"
msgstr "认证图标"

#. module: website_slides_survey
#: code:addons/website_slides_survey/controllers/slides.py:0
#, python-format
msgid "Certification slides are completed when the survey is succeeded."
msgstr "成功完成调查后，认证幻灯片就会完成。"

#. module: website_slides_survey
#: model:ir.actions.act_window,name:website_slides_survey.slide_slide_action_certification
#: model:ir.actions.act_window,name:website_slides_survey.survey_survey_action_slides
#: model:ir.actions.act_window,name:website_slides_survey.survey_survey_action_slides_report
#: model:ir.ui.menu,name:website_slides_survey.website_slides_menu_courses_certification
#: model:ir.ui.menu,name:website_slides_survey.website_slides_menu_report_certification
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "Certifications"
msgstr "认证"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_1
msgid "Chair"
msgstr "椅子"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_channel
msgid "Course"
msgstr "课程"

#. module: website_slides_survey
#: code:addons/website_slides_survey/models/survey_survey.py:0
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_survey_view_form
#, python-format
msgid "Courses"
msgstr "课程"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_survey__slide_channel_count
msgid "Courses Count"
msgstr "课程数量"

#. module: website_slides_survey
#: model:slide.slide,name:website_slides_survey.slide_slide_demo_6_0
msgid "DIY Furniture Certification"
msgstr "DIY家具认证"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_3
msgid "Desk"
msgstr "桌子"

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#, python-format
msgid "Download certification"
msgstr "下载证书"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_1
msgid "Fir"
msgstr "冷杉"

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/js/slides_certification_upload_toast.js:0
#, python-format
msgid ""
"Follow this link to add questions to your certification. <a href=\"%s\">Edit"
" certification</a>"
msgstr "按照此链接向您的认证添加问题。 <a href=\"%s\">编辑认证</a>"

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1
msgid "Furniture"
msgstr "家具"

#. module: website_slides_survey
#: model:slide.slide,name:website_slides_survey.slide_slide_demo_5_4
#: model:survey.survey,title:website_slides_survey.furniture_certification
msgid "Furniture Creation Certification"
msgstr "家具创作认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_fill_form_done_inherit_website_slides
msgid "Go back to course"
msgstr "回到课程"

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slide_upload.xml:0
#, python-format
msgid "How to upload a certification on your course?"
msgstr "如何上传课程认证？"

#. module: website_slides_survey
#: model:survey.question,comments_message:website_slides_survey.furniture_certification_page_1
#: model:survey.question,comments_message:website_slides_survey.furniture_certification_page_1_question_1
#: model:survey.question,comments_message:website_slides_survey.furniture_certification_page_1_question_2
#: model:survey.question,comments_message:website_slides_survey.furniture_certification_page_1_question_3
msgid "If other, please specify:"
msgstr "如果有其它，请指定"

#. module: website_slides_survey
#: model_terms:slide.slide,description:website_slides_survey.slide_slide_demo_6_0
msgid "It's time to test your knowledge!"
msgstr "考验您知识的时候到了！"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "No certifications yet!"
msgstr "还没认证！"

#. module: website_slides_survey
#: model_terms:slide.slide,description:website_slides_survey.slide_slide_demo_5_4
msgid ""
"Now that you have completed the course, it's time to test your knowledge!"
msgstr "现在您已经完成了课程，是时候测试您的知识了!"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_channel__nbr_certification
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__nbr_certification
msgid "Number of Certifications"
msgstr "证书数量"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_2
msgid "Oak"
msgstr "橡木"

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#, python-format
msgid "Pass Certification"
msgstr "通过认证测试"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_user_input__slide_id
msgid "Related course slide"
msgstr "相关课程幻灯片"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "Score :"
msgstr "分数："

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1_question_2
msgid "Select all the furniture shown in the video"
msgstr "选择视频中显示的所有家具"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_4
msgid "Shelf"
msgstr "架子"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "幻灯片/合作伙伴装饰为 多对多m2m"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_user_input__slide_partner_id
msgid "Slide membership information for the logged in user"
msgstr "为登录用户滑动成员资格信息"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_slide
msgid "Slides"
msgstr "幻灯片"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_user_input__slide_partner_id
msgid "Subscriber information"
msgstr "订阅者信息"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_survey_survey
msgid "Survey"
msgstr "调查"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "调查用户输入"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_2
msgid "Table"
msgstr "桌台"

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
#, python-format
msgid "Test Certification"
msgstr "测试认证"

#. module: website_slides_survey
#: model:survey.question,validation_error_msg:website_slides_survey.furniture_certification_page_1
#: model:survey.question,validation_error_msg:website_slides_survey.furniture_certification_page_1_question_1
#: model:survey.question,validation_error_msg:website_slides_survey.furniture_certification_page_1_question_2
#: model:survey.question,validation_error_msg:website_slides_survey.furniture_certification_page_1_question_3
msgid "The answer you entered is not valid."
msgstr "您输入的答案无效。"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_survey__slide_channel_ids
msgid ""
"The courses this survey is linked to through the e-learning application"
msgstr "此调查通过在线学习应用程序链接到的课程"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_slide_slide__slide_type
msgid ""
"The document type will be set automatically based on the document URL and "
"properties (e.g. height and width for presentation and document)."
msgstr "将根据文档网址和属性（例如演示文稿的高度和宽度以及文档）自动设置文档类型。"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_user_input__slide_id
msgid "The related course slide when there is no membership information"
msgstr "没有会员资料时，相关课程会滑动"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_survey__slide_ids
msgid "The slides this survey is linked to through the e-learning application"
msgstr "此调查通过在线学习应用程序链接到的幻灯片"

#. module: website_slides_survey
#: model:survey.question,constr_error_msg:website_slides_survey.furniture_certification_page_1
#: model:survey.question,constr_error_msg:website_slides_survey.furniture_certification_page_1_question_1
#: model:survey.question,constr_error_msg:website_slides_survey.furniture_certification_page_1_question_2
#: model:survey.question,constr_error_msg:website_slides_survey.furniture_certification_page_1_question_3
msgid "This question requires an answer."
msgstr "此问题需要答案。"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__slide_type
msgid "Type"
msgstr "类型"

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1_question_3
msgid "What do you think about the content of the course? (not rated)"
msgstr "您认为这门课的内容怎么样?(未评级)"

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1_question_1
msgid "What type of wood is the best for furniture?"
msgstr "什么样的木头最适合做家具?"

#. module: website_slides_survey
#: code:addons/website_slides_survey/controllers/slides.py:0
#, python-format
msgid "You are not allowed to create a survey."
msgstr "您不能创建调查。"

#. module: website_slides_survey
#: code:addons/website_slides_survey/controllers/slides.py:0
#, python-format
msgid "You are not allowed to link a certification."
msgstr "您不能链接认证。"

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slide_upload.xml:0
#, python-format
msgid ""
"You can create your certification from here or use an existing one. Once "
"your certification is created, you can still edit it in backend."
msgstr "您可以从此处创建您的认证或使用现有的认证。创建认证后，您仍然可以在后端对其进行编辑。"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.badge_content
msgid ""
"You can gain badges by passing certifications. Here is a list of all available certification badges.\n"
"                            <br/>Follow the links to reach new heights and skill up!"
msgstr ""
"您可以通过认证获得徽标。这是所有可用认证徽标的列表。\n"
"1按照链接达到新的高度和技能！"

#. module: website_slides_survey
#: model:mail.template,subject:website_slides_survey.mail_template_user_input_certification_failed
msgid ""
"You have failed the course: {{ object.slide_partner_id.channel_id.name }}"
msgstr "您的课程失败了: {{ object.slide_partner_id.channel_id.name }}"
