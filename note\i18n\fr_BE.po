# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * note
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2015-09-19 08:22+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: French (Belgium) (http://www.transifex.com/odoo/odoo-9/"
"language/fr_BE/)\n"
"Language: fr_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_open
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Active"
msgstr "Actif"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Archive"
msgstr "Archive"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "By sticky note Category"
msgstr "Par catégorie de note collantes"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid "Click to add a personal note."
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_color
#: model:ir.model.fields,field_description:note.field_note_tag_color
msgid "Color Index"
msgstr "Index de la couleur"

#. module: note
#: model:ir.ui.menu,name:note.menu_note_configuration
msgid "Configuration"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_create_uid
#: model:ir.model.fields,field_description:note.field_note_stage_create_uid
#: model:ir.model.fields,field_description:note.field_note_tag_create_uid
msgid "Created by"
msgstr "Créé par"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_create_date
#: model:ir.model.fields,field_description:note.field_note_stage_create_date
#: model:ir.model.fields,field_description:note.field_note_tag_create_date
msgid "Created on"
msgstr "Créé le"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_date_done
msgid "Date done"
msgstr "Date de fin"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Delete"
msgstr "Supprimer"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_display_name
#: model:ir.model.fields,field_description:note.field_note_stage_display_name
#: model:ir.model.fields,field_description:note.field_note_tag_display_name
msgid "Display Name"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage_fold
msgid "Folded by Default"
msgstr "Replié par défaut"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Group By"
msgstr "Grouper par"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_id
#: model:ir.model.fields,field_description:note.field_note_stage_id
#: model:ir.model.fields,field_description:note.field_note_tag_id
msgid "ID"
msgstr "ID"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note___last_update
#: model:ir.model.fields,field_description:note.field_note_stage___last_update
#: model:ir.model.fields,field_description:note.field_note_tag___last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_write_uid
#: model:ir.model.fields,field_description:note.field_note_stage_write_uid
#: model:ir.model.fields,field_description:note.field_note_tag_write_uid
msgid "Last Updated by"
msgstr "Derniere fois mis à jour par"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_write_date
#: model:ir.model.fields,field_description:note.field_note_stage_write_date
#: model:ir.model.fields,field_description:note.field_note_tag_write_date
msgid "Last Updated on"
msgstr "Dernière mis à jour le"

#. module: note
#: model:note.stage,name:note.demo_note_stage_03
#: model:note.stage,name:note.note_stage_03
msgid "Later"
msgstr "Plus tard"

#. module: note
#: model:note.stage,name:note.note_stage_00
msgid "New"
msgstr "Nouveau"

#. module: note
#: code:addons/note/note.py:165
#, python-format
msgid "New Note"
msgstr ""

#. module: note
#: model:ir.model,name:note.model_note_note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Note"
msgstr "Note"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_memo
msgid "Note Content"
msgstr "Contenu de la note"

#. module: note
#: model:ir.model,name:note.model_note_stage
msgid "Note Stage"
msgstr "Etape de la note"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_name
msgid "Note Summary"
msgstr "Résumé de la note"

#. module: note
#: model:ir.model,name:note.model_note_tag
msgid "Note Tag"
msgstr "Tag de la note"

#. module: note
#: model:ir.actions.act_window,name:note.action_note_note
#: model:ir.ui.menu,name:note.menu_note_notes
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model:note.stage,name:note.note_stage_04
msgid "Notes"
msgstr "Notes"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_user_id
#: model:ir.model.fields,field_description:note.field_note_stage_user_id
msgid "Owner"
msgstr "Propriétaire"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage_user_id
msgid "Owner of the note stage."
msgstr "Propriétaire de l'étape de la note"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_sequence
#: model:ir.model.fields,field_description:note.field_note_stage_sequence
msgid "Sequence"
msgstr "Séquence"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_stage_id
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Stage"
msgstr "Etape"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage_name
msgid "Stage Name"
msgstr "Nom de l'étape"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_form
msgid "Stage of Notes"
msgstr "Etape des notes"

#. module: note
#: model:ir.actions.act_window,name:note.action_note_stage
#: model:ir.ui.menu,name:note.menu_notes_stage
#: model_terms:ir.ui.view,arch_db:note.view_note_note_tree
msgid "Stages"
msgstr "Etapes"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_tree
msgid "Stages of Notes"
msgstr "Etapes des notes"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_stage_ids
msgid "Stages of Users"
msgstr "Etapes des utilisateurs"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_tag_name
msgid "Tag Name"
msgstr "Nom du tag"

#. module: note
#: sql_constraint:note.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_tag_ids
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Tags"
msgstr "Tags"

#. module: note
#: model:note.stage,name:note.demo_note_stage_04
#: model:note.stage,name:note.note_stage_02
msgid "This Week"
msgstr "Cette semaine"

#. module: note
#: model:note.stage,name:note.demo_note_stage_01
#: model:note.stage,name:note.note_stage_01
msgid "Today"
msgstr "Aujourd'hui"

#. module: note
#: model:note.stage,name:note.demo_note_stage_02
msgid "Tomorrow"
msgstr "Demain"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"Use notes to organize personal tasks or notes. All\n"
"            notes are private; no one else will be able to see them. "
"However\n"
"            you can share some notes with other people by inviting "
"followers\n"
"            on the note. (Useful for meeting minutes, especially if\n"
"            you activate the pad feature for collaborative writings)."
msgstr ""

#. module: note
#: model:ir.model.fields,help:note.field_note_stage_sequence
msgid "Used to order the note stages"
msgstr "Utilisé pour ordonner les étapes des notes"

#. module: note
#: model:ir.model,name:note.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"You can customize how you process your notes/tasks by adding,\n"
"            removing or modifying columns."
msgstr ""

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Date du dernier message posté sur l'enregistrement."

#~ msgid "Followers"
#~ msgstr "Abonnés"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si coché, les nouveaux messages requierent votre attention. "

#~ msgid "Last Message Date"
#~ msgstr "Date du dernier message"

#~ msgid "Messages"
#~ msgstr "Messages"

#~ msgid "Messages and communication history"
#~ msgstr "Messages et historique des communications"

#~ msgid "Unread Messages"
#~ msgstr "Messages non lus"
