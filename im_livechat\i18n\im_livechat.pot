# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 01:53+0000\n"
"PO-Revision-Date: 2022-08-24 01:53+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "# Messages"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s is typing..."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Display the button' displays the chat button on the pages.\n"
"* 'Auto popup' displays the button and automatically open the conversation pane.\n"
"* 'Hide the button' hides the chat button on the pages."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "-------- Show older messages --------"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"<span class=\"text-muted\">Define rules for your live support channel. You "
"can apply an action for the given URL, and per country.<br/>To identify the "
"country, GeoIP must be installed on your server, otherwise, the countries of"
" the rule will not be taken into account.</span>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Action"
msgstr ""

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Anonymous"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__anonymous_name
msgid "Anonymous Name"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Ask something ..."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Attendees"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Auto popup"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Auto popup timer"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Avatar"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bad"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bounced"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Canceled"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_canned_response_action
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Changed"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Channel"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__channel_type
msgid "Channel Type"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
msgid "Channels"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Chat with one of our collaborators"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close chat window"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Conversation Sent"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__country_id
msgid "Country"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__country_id
msgid "Country of the visitor of the channel"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid "Create a new canned response"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation Date"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day number of the session (1 is Monday, 7 is Sunday)"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Auto popup' otherwise this parameter will not be "
"taken into account."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Delete"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Did we correctly answer your question ?"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Display the button"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Document not downloadable"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Download"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Error"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Explain your note"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Good"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Group By..."
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
#, python-format
msgid "Have a Question? Chat with us."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr ""

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide the button"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "History"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "How may I help you?"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Idle"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
#, python-format
msgid "Image"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Invalid email address"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_3
msgid "Joel Willis, Marc Demo"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_2
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_6
msgid "Joel Willis, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator____last_update
msgid "Last Modified on"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr ""

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/components/discuss/discuss.js:0
#: code:addons/im_livechat/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__mail_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_mail_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audiance, the duration of a session, etc."
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
msgid "Livechat Username"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_active
msgid "Livechat session is active until visitor leave the conversation."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading older messages..."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Read"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Todo"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as todo"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
msgid "Message"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
msgid "Name"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "New messages"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Next"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "No history found"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Note by"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "Number of conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "OK"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Offline"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Online"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Oops! Something went wrong."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_operator_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_operator_id
msgid "Operator for this specific channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"Operators\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "PDF file"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please check your internet connection."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait..."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Previous"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Print"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_form_livechat
msgid "Rating"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_avg
msgid "Rating Average"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_image
msgid "Rating Last Image"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_value
msgid "Rating Last Value"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_count
msgid "Rating count"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Rating: %s"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
msgid "Ratings"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Ready"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Receive a copy of this conversation"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by Everyone"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by:"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reply"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reset Zoom"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Reset to default colors"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Rotate"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Say something"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Search history"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by Everyone"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by:"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Send"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Sent"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Session Date"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Session Form"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Session expired... Please refresh and try again."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start date of the conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start hour of the conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Thank you for your feedback"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__name
msgid "The name of the channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide Button', "
"the chat button will be hidden on the specified URL from the visitors "
"located in these 2 countries. This feature requires GeoIP installed on your "
"server."
msgstr ""

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr ""

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer the user in second."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer(sec)"
msgstr ""

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Today"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Try again"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Type <b>:shortcut</b> to insert a canned response in your message.<br>"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Undefined"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploaded"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploading"
msgstr ""

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is idle"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is offline"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is online"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
msgid "Users"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Video"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Viewer"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Visitor"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_0
msgid "Visitor #234, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_1
msgid "Visitor #323, Marc Demo"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_4
msgid "Visitor #532, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_5
msgid "Visitor #649, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_7
msgid "Visitor #722, Marc Demo"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Visitor has left the conversation."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.mail_channel_livechat_1
msgid "Visitor, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Yesterday"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Your chatter history is empty"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom In"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom Out"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "from"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "<EMAIL>"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "on"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read less"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read more"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "unnamed"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr ""
