from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class BSSICPasswordResetRequest(models.Model):
    """Password Reset Request Model - Proxy to bssic.request"""
    _name = 'bssic.password.reset.request'
    _description = 'BSSIC Password Reset Request'
    _inherit = 'bssic.request'
    _auto = False  # Don't create database table
    _table = 'bssic_request'  # Use the same table as bssic.request

    # Password reset specific fields
    username = fields.Char('Username', tracking=True)
    device_type = fields.Selection([
        ('internet', 'Internet'),
        ('system', 'System'),
        ('swift', 'Swift'),
        ('other', 'Other')
    ], string='Device Type', tracking=True)
    request_reason = fields.Selection([
        ('password_reset', 'Password Reset (Forgotten/Unable to login)'),
        ('account_reactivation', 'Account Reactivation (Account/Device locked)')
    ], string='Request Reason', tracking=True)

    @api.constrains('username', 'device_type', 'request_reason', 'state', 'show_password_fields')
    def _check_required_password_fields(self):
        """Validate required fields for password reset requests"""
        for record in self:
            if record.show_password_fields and record.state != 'draft':
                if not record.username:
                    raise UserError(_('Username is required for Password Reset requests.'))
                if not record.device_type:
                    raise UserError(_('Device Type is required for Password Reset requests.'))
                if not record.request_reason:
                    raise UserError(_('Request Reason is required for Password Reset requests.'))

    def _get_password_reset_request_type(self):
        """Helper method to get password reset request type"""
        return self.env['bssic.request.type'].search([
            ('code', '=', 'password_reset')
        ], limit=1)

    @api.model
    def create(self, vals):
        """Redirect create to bssic.request with password reset defaults"""
        # Set request type code for password reset
        if not vals.get('request_type_code'):
            vals['request_type_code'] = 'password_reset'

        # Find and set the password reset request type
        if not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'password_reset')
            ], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        return self.env['bssic.request'].create(vals)

    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """Redirect search to bssic.request with password reset filter"""
        request_model = self.env['bssic.request']
        # Add filter for password reset requests
        password_args = args + [('request_type_id.code', '=', 'password_reset')]
        return request_model.search(password_args, offset=offset, limit=limit, order=order, count=count)

    def write(self, vals):
        """Redirect write to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).write(vals)

    def unlink(self):
        """Redirect unlink to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).unlink()

    @api.model
    def browse(self, ids):
        """Redirect browse to bssic.request"""
        return self.env['bssic.request'].browse(ids)

    def read(self, fields=None, load='_classic_read'):
        """Redirect read to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).read(fields=fields, load=load)

    @api.model
    def default_get(self, fields_list):
        """Set default values for password reset requests"""
        res = super(BSSICPasswordResetRequest, self).default_get(fields_list)

        # Set default request type for password reset
        if 'request_type_code' in fields_list:
            res['request_type_code'] = 'password_reset'

        if 'request_type_id' in fields_list:
            request_type = self._get_password_reset_request_type()
            if request_type:
                res['request_type_id'] = request_type.id

        return res

    def write(self, vals):
        """Override write to add logging"""
        try:
            result = super(BSSICPasswordResetRequest, self).write(vals)
            if 'state' in vals:
                _logger.info(f"Updated password reset request {self.name} state to {vals['state']}")
            return result
        except Exception as e:
            _logger.error(f"Failed to update password reset request {self.name}: {str(e)}")
            raise
