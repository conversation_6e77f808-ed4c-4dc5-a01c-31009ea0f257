# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap_mail
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-building text-primary\"/>\n"
"                    <b>Company type</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-building text-primary\"/>\n"
"                    <b>Type de société</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-calendar text-primary\"/>\n"
"                    <b>Founded</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-calendar text-primary\"/>\n"
"                    <b>Fondée</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-cube text-primary\"/>\n"
"                    <b>Technologies Used</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-cube text-primary\"/>\n"
"                    <b>Technologies Utilisées</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-envelope text-primary\"/>\n"
"                    <b>Email</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-envelope text-primary\"/>\n"
"                    <b>Email</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-globe text-primary\"/>\n"
"                    <b>Timezone</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-globe text-primary\"/>\n"
"                    <b>Fuseau horaire</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-industry text-primary\"/>\n"
"                    <b>Sectors</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-industry text-primary\"/>\n"
"                    <b>Secteurs</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                    <b>Estimated revenue</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                    <b>Revenus estimés</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-phone text-primary\"/>\n"
"                    <b>Phone</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-phone text-primary\"/>\n"
"                    <b>Téléphone</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-twitter text-primary\"/>\n"
"                    <b>Twitter</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-twitter text-primary\"/>\n"
"                    <b>Twitter</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw mr-2 fa-users text-primary\"/>\n"
"                    <b>Employees</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-users text-primary\"/>\n"
"                    <b>Employés</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "<span> per year</span>"
msgstr "<span> par an</span>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "Company Logo"
msgstr "Logo de la société"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "followers"
msgstr "abonnés"
