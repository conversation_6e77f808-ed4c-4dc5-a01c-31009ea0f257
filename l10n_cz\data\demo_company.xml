<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_cz" model="res.partner">
        <field name="name">CZ Company</field>
        <field name="vat">CZ12345679</field>
        <field name="street">Pařížská Street 25/31</field>
        <field name="city">Praha</field>
        <field name="country_id" ref="base.cz"/>
        <field name="state_id" ref="base.state_L"/>
        <field name="zip"></field>
        <field name="phone">+420 5 12 34 56 78</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.czexample.com</field>
    </record>

    <record id="demo_company_cz" model="res.company">
        <field name="name">CZ Company</field>
        <field name="partner_id" ref="partner_demo_company_cz"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_cz')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_cz.demo_company_cz'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_cz.cz_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_cz.demo_company_cz')"/>
    </function>
</odoo>
