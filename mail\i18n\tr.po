# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> YILMAZ <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Ali <PERSON> AĞCA <<EMAIL>>, 2021
# <PERSON>zlem Atalay <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Gökhan E<PERSON>ğdu <<EMAIL>>, 2021
# <PERSON>lk<PERSON>r Püskül <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>rat <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Martin Trigaux, 2021
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Murat Durmuş <<EMAIL>>, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr " Bu kanal özeldir. Bu kanala katılabilmek için davet alınmalıdır."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires \"%s\" access"
msgstr "\"%s\", \"%s\" erişimi gerektirir"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires microphone access"
msgstr "\"%s\" mikrofon erişimi gerektirir"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "%(activity_name)s: %(summary)s assigned to you"
msgstr "%(activity_name)s: %(summary)s sana atandı"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""
"%(email)s geçerli bir e-posta olarak tanınmıyor. Bu, yeni bir müşteri "
"oluşturmak için gereklidir."

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr ""
"%(user_name)s Sizi, bu belgeyi takip etmeye %(document)s davet ediyor: "
"%(title)s"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s Sizi, yeni belgeyi takip etmeye davet ediyor."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Message"
msgstr "%d Mesaj"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Messages"
msgstr "%d Mesaj"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "%d days overdue"
msgstr "%d gün geciken"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "%d days overdue:"
msgstr "%d gün geciken:"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopya)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s ve %s yazıyor ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s and %s have reacted with %s"
msgstr "%s ve %s %s tepki gösterdi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s connected"
msgstr "%s bağlandı"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s oluşturuldu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s from %s"
msgstr "%s dan %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "%s has a request"
msgstr "%s bir isteği var"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s has reacted with %s"
msgstr "%s %s tepki gösterdi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s yazıyor..."

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "%s started a live conference"
msgstr "%s canlı bir konferans başlattı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s ve daha fazlası yazıyor ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and %s other persons have reacted with %s"
msgstr "%s, %s, %s ve %s diğer kişi %s ile tepki verdi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and 1 other person have reacted with %s"
msgstr "%s, %s, %s ve 1 kişi daha %s ile tepki verdi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s have reacted with %s"
msgstr "%s %s %s %s tepki gösterdi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "(from"
msgstr "(kimden"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(başlangıçta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid ", "
msgstr ", "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid ", enter to"
msgstr ", girin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "-&gt;"
msgstr "-&gt;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid ". Narrow your search to see more choices."
msgstr ". Daha fazla seçenek görmek için aramanızı daraltın."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ".<br/>"
msgstr ".<br/>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Future"
msgstr "0 Gelecek"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Late"
msgstr "0 Geciken"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Today"
msgstr "0 Bu Gün"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b attrs=\"{'invisible': [('no_record', '=', False)]}\" class=\"text-"
"warning\">No record for this model</b>"
msgstr ""
"<b attrs=\"{'invisible': [('no_record', '=', False)]}\" class=\"text-"
"warning\">Bu model için kayıt yok</b>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<br><br>\n"
"            Type <b>@username</b> to mention someone, and grab his attention.<br>\n"
"            Type <b>#channel</b> to mention a channel.<br>\n"
"            Type <b>/command</b> to execute a command.<br>"
msgstr ""
"<br><br>\n"
"           Birinden bahsetmek için <b>@username</b> yazın ve dikkatini çekin.<br>\n"
"           Bir kanaldan bahsetmek için <b>#channel</b> yazın.<br>\n"
"           Bir komutu yürütmek için <b>/command</b> yazın.<br>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">oluşturuldu<a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">invited <a href=\"#\" data-oe-"
"model=\"res.partner\" data-oe-"
"id=\"%(new_partner_id)d\">%(new_partner_name)s</a> to the channel</div>"
msgstr ""
"<div class=\"o_mail_notification\"><a href=\"#\" data-oe-"
"model=\"res.partner\" data-oe-"
"id=\"%(new_partner_id)d\">%(new_partner_name)s'yi</a> kanala davet "
"etti</div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">joined the channel</div>"
msgstr "<div class=\"o_mail_notification\">kanala katıldı</div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">left the channel</div>"
msgstr "<div class=\"o_mail_notification\">kanaldan ayrıldı</div>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p><b>Gerçek zamanlı, doğrudan mesajları kullanarak</b> iş arkadaşlarınızla "
"konuşun.</p><p><i>Öncelikle ayarlar uygulamasından kullanıcıları davet "
"etmelisiniz.</i></p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>Buradaki kanal üylerine </b> bir mesaj yazın.</p> <p>Birini "
"bilgilendirmek için<i>'@'</i> kullanabilir yada başka kanaldan link "
"kullanmak için <i>'#'</i>kullanabilirsiniz. Mesajınıza <i>'/'</i> ile "
"başlarsanız olası komut listesini görebilirsiniz.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Kanallar farklı konu ve gruplar arasında organize olmayı "
"kolaylaştırır.</p> <p>İlk kanalınızı <b>oluşturmayı deneyin</b> (örn: "
"pazarlama, ürün XYZ, iş sonrası parti, vb).</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a channel here.</p>"
msgstr "<p>Burada bir kanal oluşturun.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a public or private channel.</p>"
msgstr "<p>Genel veya özel bir kanal oluşturun..</p>"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"<p>Dear Sender,<br /><br />\n"
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"Please make sure you are using the correct address or contact us at %(default_email)s instead.<br /><br />\n"
"Kind Regards,</p>"
msgstr ""
"<p>Sayın Gönderici,<br /><br />\n"
"Aşağıdaki ileti %(alias_display_name)s adresi tarafından kabul edilemedi.\n"
"Yalnızca %(contact_description)s onunla iletişim kurmasına izin verilir.<br /><br />\n"
"Lütfen doğru adresi kullandığınızdan emin olun veya bunun yerine %(default_email) s adresinden bizimle iletişime geçin.<br /><br />\n"
"İçten dileklerimle</p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Toplu e-posta postalama</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">seçilen kayıtlar</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">mevcut arama filtresi</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Belge takipçileri ve</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    Arama kriterlerinizle eşleşen tüm kayıtları göndermek istiyorsanız, bu kutuyu işaretleyin:\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    Sadece seçili kayıtları göndermek istiyorsanız, lütfen bu seçim kutusunun işaretini kaldırın:\n"
"                                </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "<span class=\"col-md-5 col-lg-4 col-sm-12 pl-0\">Force a language: </span>"
msgstr "<span class=\"col-md-5 col-lg-4 col-sm-12 pl-0\">Bir dili zorla: </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."
msgstr ""
"<span class=\"fa fa-info-circle\"/> Dikkat: Bu postayı seçmediğiniz "
"alıcılara tekrar göndermek mümkün olmayacaktır."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Activities</span>"
msgstr "<span class=\"o_form_label\">Aktiviteler</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Custom ICE server list</span>"
msgstr "<span class=\"o_form_label\">Özel ICE sunucu listesi</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Ekle</span>\n"
"<span class=\"o_stat_text\">Bağlam Eylemi</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Kaldırma</span>\n"
"<span class=\"o_stat_text\">Bağlam Eylemi</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""
"<strong>\n"
"                                    Sadece ıd seçili olanlar değil,\n"
"                                    son arama filtrenizle eşleşen tüm kayıtlar postalanacaktır.\n"
"                                </strong><br/>\n"
"                                E-mail, listedeki seçili tüm kayıtlar için gönderilmiş olacaktır.<br/>\n"
"                                Bu sihirbazı onaylamak, muhtemelen tarayıcınızı bir kaç dakikalığına bloke edecektir."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""
"<strong>İçsel iletişim</strong>: Yanıtlama dahili bir not gönderir. "
"Takipçilere e-posta bildirimi gönderilmez."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""
"<strong>Sadece liste görünümünde işaretlenmiş olan kayıtlar kullanıcaktır.</strong><br/>\n"
"                                E-mail, listede seçili olan tüm kayıtlara gönderilecektir."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr "<strong>Orijinal not:</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr "<strong>Önerilen Aktiviteler</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Bir Python sözlüğü, bu rumuz için yeni kayıtlar oluştururken varsayılan "
"değerleri sağlamak için değerlendirilir."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "Bir otobüs varlığının bir kullanıcısı veya misafiri olmalıdır."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_partner_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "Kanal üyesi İş ortağı veya konuk olmalıdır."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "\"Sohbet\" türündeki bir kanalın ikiden fazla kullanıcısı olamaz."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr ""
"2'den fazla kişiyle sohbet oluşturulmamalıdır. Bunun yerine bir grup "
"oluşturun."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "Mesaj tepkisi bir İş ortağından veya misafirden gelmelidir."

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "A next activity can only be planned on models that use the chatter"
msgstr ""
"Bir sonraki aktivite yalnızca sohbeti kullanan modellerde planlanabilir"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr ""
"Bir kısa kod, bir klavye kısayoludır. Örneğin, # gm yazarsınız ve "
"\"Günaydın\" a dönüşür."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "Ses düzeyi ayarında bir İş ortağı veya misafir bulunmalıdır."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Accept"
msgstr "Kabul Et"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Erişim Grupları"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "Erişim Jetonu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "Aksiyon"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Eylem Gerekiyor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
msgid "Action To Do"
msgstr "Yapılması Gerekenler"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Pencere Eylem Görünümü"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Eylemler, takvim görünümünü açma gibi belirli davranışları tetikleyebilir "
"veya bir belge yüklendiği zaman otomatik olarak yapılmış olarak "
"işaretleyebilir"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Abone olurken varsayılan olarak etkinleştirilir."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_channel__active
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr "Etkin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__active_domain
msgid "Active domain"
msgstr "Etkin alan adı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#, python-format
msgid "Activities"
msgstr "Aktiviteler"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_view.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
#, python-format
msgid "Activity"
msgstr "Aktivite"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivite İstisna Donatımı"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Aktivite Birleştirme"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "Etkinlik Ayarları"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Aktivite Durumu"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Aktivite Türü"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivite Simge Tipi"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Aktivite Türleri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "Activity User Type"
msgstr "Kullanıcı Aktivite Türü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "Aktivite  Türü"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "E-posta Kara Listesi Ekle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Takipçi Ekle"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr "Takipçi Ekleme sadece bir posta dizisi modeli için yapılabilir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_mail__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_message__add_sign
msgid "Add Sign"
msgstr "İmza Ekle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.js:0
#, python-format
msgid "Add a Reaction"
msgstr "Bir Tepki Ekle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add a description"
msgstr "Bir Tanım Ekle"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr ""
"%(document)sbağlantısına yeni bir %(email_link)sekleyin veya e-posta "
"gönderin"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "Kara listeye e-posta adresi ekleme"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/text_emojis.xml:0
#, python-format
msgid "Add an emoji"
msgstr "Emoji ekle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Add attachment"
msgstr "Eklenti ekle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#, python-format
msgid "Add attachments"
msgstr "Ek ekle"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Bilgilendirilecek kişi ekle..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Add or join a channel"
msgstr "Kanal ekle veya kanala katıl"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add users"
msgstr "Kullanıcı ekleme"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "ICE sunucuları için twilio kimlik bilgilerinizi ekleme"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr ""
"Kanallara takipçi eklemek mümkün değildir. Bunun yerine üye eklemeyi "
"düşünün."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Ek Kişiler"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Gelişmiş"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr "Gelişmiş Ayarlar"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
msgid "Alert"
msgstr "İkaz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr "Rumuz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_contact
msgid "Alias Contact Security"
msgstr "Rumuz İletişim Güvenliği"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "Alias Domain"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_name
msgid "Alias Name"
msgstr "Rumuz Adı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_domain
msgid "Alias domain"
msgstr "Rumuz alan adı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_model_id
msgid "Aliased Model"
msgstr "Rumuzlanan Model"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Rumuzlar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "All"
msgstr "Tümü"

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "Her ek için bir erişim belirteci sağlanmalıdır."

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "An email is required for find_or_create to work"
msgstr "An email is required for find_or_create to work"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending an email."
msgstr "E-posta gönderirken bir hata oluştu."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "An error occurred while fetching messages."
msgstr "İletiler alınırken bir hata oluştu."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "An unexpected error occurred during the creation of the chat."
msgstr "Sohbetin oluşturulması sırasında beklenmeyen bir hata oluştu."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And"
msgstr "Ve"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And 1 other member."
msgstr "Ve 1 diğer üye."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Anonymous"
msgstr "İsimsiz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Buna uygulanır"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Apply"
msgstr "Uygula"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid "Are you sure you want to delete this message?"
msgstr "Bu iletiyi silmek istediğinden emin misin?"

#. module: mail
#: code:addons/mail/wizard/mail_resend_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s mail delivery failures? You won't be "
"able to re-send these mails later!"
msgstr ""
"%s posta teslim hatalarını atmak istediğinizden emin misiniz? Bu postaları "
"daha sonra tekrar gönderemezsiniz!"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr ""
"Bu E-posta Adresini kara listeden çıkarmak istediğinizden emin misiniz?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign to ..."
msgstr "Atama ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign/unassign to me"
msgstr "Bana ata/atamayı kaldır"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#, python-format
msgid "Assigned to"
msgstr "Atanan"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""
"Atanan  %s kullanıcısının dokümana erişimi yok ve bu aktiviteyi "
"gerçekleştiremiyor."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Bir dosya ekle"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "Ek"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Attachment counter loading..."
msgstr "Ek sayacı yükleniyor..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Ekler"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""
"Ekler, model / res_id üzerinden bir belgeye ve bu alandaki iletiye bağlanır."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Kimliği Doğrulanmış İş Ortakları"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Üretici"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Mesajı yazan. Ayarlanmamışsa, email_from hiç bir iş ortağıyla eşleşmeyen bir"
" eposta adrsini tutabilir."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Yazanın Avatarı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_public_id
msgid "Authorized Group"
msgstr "Yetkili Grup"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Otomatik Sil"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "Otomatik Abone Gruplar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_ids
msgid "Auto Subscription"
msgstr "Otomatik Abonelik"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Otomatik abonelik"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "Otomatik aktivite"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""
"Geçerli etkinlik tamamlandı olarak işaretlendikten sonra bu etkinliği "
"otomatik olarak zamanlayın."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Avatar of OdooBot"
msgstr "OdooBot Avatarı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of guest"
msgstr "Misafirin avatarı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of user"
msgstr "Kullanıcı avatarı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "Dışarıda"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Temel"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Best regards,"
msgstr "Saygılarımızla,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "Karaliste"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Karaliste Tarihi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Kara Listeye Alınmış Adres"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "Blacklisted Email Addresses"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "Gövde"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Bot"
msgstr "Bot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
msgid "Bounce"
msgstr "İletilmeyen E-Posta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "İletilmeyen"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Browser default"
msgstr "Tarayıcı varsayılanı"

#. module: mail
#: code:addons/mail/models/mail_thread_cc.py:0
#, python-format
msgid "CC Email"
msgstr "CC Eposta"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Ara"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Camera is off"
msgstr "Kamera kapalı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "Gövdeyi Düzenleyebilir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
msgid "Can Write"
msgstr "Yazabilir"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr "İleti veya bildirimin alıcısı güncellenemiyor."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "İptal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "E-posta İptal Et"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Cancel notification in failure"
msgstr "Bildirimi başarısız olarak iptal et"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "İptal Edildi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "İptal Edildi"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Hazır yanıt / Kısa kod"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__canned_response_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__canned_response_ids
msgid "Canned Responses"
msgstr "Hazır Cevaplar"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "CC (carbon kopya) mesaj alıcıları"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "Carbon copy recipients"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "Alıcı karbon kopyası (burada yer tutucular kullanılabilir)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "Catchall"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "Catchall E-postası"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "İlgili Kopyası (CC)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "Zincirleme Tip"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Change Layout"
msgstr "Yerleşim Planını Değiştir"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Change layout"
msgstr "Görünümü Değiştir"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "Bu tür ilgili etkinliklerin arka plan rengini değiştirin."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Changed"
msgstr "Değişmiş"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__channel_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "Kanal"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(guest_names)s"
msgstr ""
"\"%(channel_name)s\" kanalı yalnızca \"%(group_name)s\" grubunun üyelerini "
"kabul eder. Yasak olduğu için: %(guest_names)s"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(partner_names)s"
msgstr ""
"\"%(channel_name)s\" kanalı yalnızca \"%(group_name)s\" grubunun üyelerini "
"kabul eder. Yasak olduğu için: %(partner_names)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_partner_id
msgid "Channel Partner"
msgstr "Kanal İş Ortağı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Kanal Türü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Channel settings"
msgstr "Kanal ayarları"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr "Kanallar"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr "Kanallar / İş Ortak"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__chat
#, python-format
msgid "Chat"
msgstr "Sohbet"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr "Sohbet Kısa Kodu"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Sohbet 2 kişi arasında özel ve benzersizdir. Grup, davet edilen kişiler "
"arasında özeldir. Kanal serbestçe birleştirilebilir (yapılandırmasına bağlı "
"olarak)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Alt Mesajlar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Choose an example"
msgstr "Bir örnek seçin"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "Click here to retry"
msgstr "Yeniden denemek için buraya tıklayın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Click on your message"
msgstr "Mesajınıza tıklayın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Click to see the attachments"
msgstr "Ekleri görmek için tıklayın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
#, python-format
msgid "Close"
msgstr "Kapat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Close (Esc)"
msgstr "Kapat (Esc)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close chat window"
msgstr "Sohbet penceresini kapat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close conversation"
msgstr "Sohbeti kapat"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__closed
msgid "Closed"
msgstr "Kapanmış"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Virgülle ayrılmış karbon kopya alıcı adresleri"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Alıcı iş ortakların kimlikleri virgülle ayrılmış"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"Alıcı iş ortaklarının virgülle ayrılmıi id leri (burada yer tutucular "
"kullanılabilir)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Virgülle ayrılmış alıcı adresleri"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr "Virgülle ayrılmış alıcı adresleri (burada yertutucular kullanılır)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Yorum"

#. module: mail
#: model:ir.model,name:mail.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/mail_template/mail_template.js:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "E-posta Yaz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Kompozisyon modu"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "ICE sunucu listenizi webRTC için yapılandırma"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Aktivite türlerini yapılandırın"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "Configure your own email servers"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Confirm"
msgstr "Onayla"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Doğrulama"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Congratulations, you're done with your activities."
msgstr "Tebrikler, faaliyetlerinizi bitirdiniz."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "Tebrikler, gelen kutunuz boş"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/widgets/discuss/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "Tebrikler, gelen kutunuz boş!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Bağlantı başarısız (giden posta sunucusu sorunu)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "Yanıtları yeni iş parçacığı olarak değerlendirir"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Kontaklar"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_partner__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this partner. This includes: creating, joining, pinning, "
"and new message posted."
msgstr ""
"Bu iş ortağı için bu kanalda gerçekleşen son ilginç olayın tarihini ve "
"saatini içerir. Buna şunlar dahildir: oluşturma, katılma, sabitleme ve "
"gönderilen yeni iletiler."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "İçerik"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "İçerikler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fold_state
msgid "Conversation Fold State"
msgstr "Konuşmanın Katlama Durumu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_minimized
msgid "Conversation is minimized"
msgstr "Konuşma en aza indirildi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Conversations"
msgstr "Konuşmalar"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Bu kişi için iletilmeyen e-posta sayısı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "Ülke"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Create"
msgstr "Oluştur"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Next Activity"
msgstr "Sonraki Aktiviteyi Oluştur"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "Kullanıcı Kimliği Oluştur"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Create group chat"
msgstr "Grup sohbeti oluşturma"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s"
msgstr "Create new %(document)s"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "Create new %(document)s by sending an email to %(email_link)s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Create or search channel..."
msgstr "Kanal oluştur veya ara..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Created"
msgstr "Oluşturulma"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Oluşturan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Creating a new record..."
msgstr "Yeni bir kayıt oluşturuluyor..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Oluşturulma Tarihi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "Kimlik bilgisi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "Para Birimi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr "Geçerli kullanıcı bu mesaja bağlı yıldızlı bir bildirime sahiptir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Özel Geri Dönme Mesajı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__custom_channel_name
msgid "Custom channel name"
msgstr "Özel kanal adı"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "Gelen kutusu / e-posta bildirimi için müşteri gerekiyor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Tarih"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Gün"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Deadline"
msgstr "Zaman Sınırı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Deafen"
msgstr "Sağır Edici"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Sayın"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Dekorasyon Tipi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Öntanımlı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__default_display_mode
msgid "Default Display Mode"
msgstr "Varsayılan Görünüm Modu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "Varsayılan Not"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "Öntanımlı Özet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Öntanımlı Kullanıcı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_template__null_value
msgid "Default Value"
msgstr "Öntanımlı Değer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_defaults
msgid "Default Values"
msgstr "Öntanımlı Değerler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Öntanımlı alıcılar"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Raporun öntanımlı alıcıları:\n"
"- is ortağı (iş ortağı veya iş ortağı_id alanı kullanılarak) VEYA\n"
"- eposta (eposta gönderen veya eposta alanı kullanılarak)"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Define a new chat shortcode"
msgstr "Yeni bir sohbet kısa kodu tanımlayın"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "Delay Label"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "Gecikme Türü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Delay after releasing push-to-talk"
msgstr "Bas-konuş özelliğini yayınladıktan sonra gecikme"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "Gecikme birimleri"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "Sil"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "E-postaları Sil"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr "Mesaj Kopyalamayı Sil"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Gönderim Başarısız"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Delivery failure"
msgstr "Teslimat hatası"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Açıklama"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Bu alt tip için gönderilen mesaja eklenecek açıklama. Geçersizse, yerine adı"
" eklenecektir."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"Davet bağlantısından açılırken kanalın varsayılan olarak nasıl "
"görüntüleneceğini belirler. Değer yok, metin görüntüleme anlamına gelir "
"(ses/video yok)."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Direct Messages"
msgstr "Doğrudan Mesajlar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Discard"
msgstr "Vazgeç"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Discard delivery failures"
msgstr "Teslimat hatalarını sil"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_cancel_action
msgid "Discard mail delivery failures"
msgstr "Posta teslimi hatalarını at"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Discard message delivery failures"
msgstr "İleti teslim hatalarını atma"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
#, python-format
msgid "Disconnect"
msgstr "Bağlantıyı Kes"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "Disconnected from the RTC call by the server"
msgstr "Sunucu tarafından RTC çağrısıyla bağlantısı kesildi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: code:addons/mail/static/src/models/discuss/discuss.js:0
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Discuss"
msgstr "Mesajlaşma"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "Tartışma Kenar çubuğu"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion Channel"
msgstr "Mesajlaşma Kanalı"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Mesajlaşmalar"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_cancel
msgid "Dismiss notification for resend by model"
msgstr "Modele göre yeniden gönderme bildirimini reddet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Bu şablon ile bir yazma sihirbazı açmak için ilgili belgelerde bir seçenek "
"göster"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__res_name
msgid "Display name of the related document."
msgstr "İlgili dokümanın görünüm adı."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"E-postanın bir kopyasını belge iletişim geçmişinde saklamayın (yalnızca "
"toplu postalama)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#, python-format
msgid "Do you really want to delete \"%s\"?"
msgstr "\"%s\" gerçekten silmek istiyor musunuz?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Belge"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Belge Takipçileri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
msgid "Document Model"
msgstr "Belge Modeli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Belge Adı"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Documentation"
msgstr "Belgeleme"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Done"
msgstr "Biten"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Launch Next"
msgstr "Bitti ve Başlat Sonraki"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "Biten & Sonrakini Planla"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Download"
msgstr "İndir"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Download logs"
msgstr "Kayıtları indirme"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/drop_zone/drop_zone.xml:0
#, python-format
msgid "Drag Files Here"
msgstr "Dosyaları buraya sürükle"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Dropdown menu"
msgstr "Açılır Menü"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
msgid "Due Date"
msgstr "Bitiş Tarihi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "Son Tarih"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Due in %d days"
msgstr "Kalan süre %d gün"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Due in %d days:"
msgstr "Kalan süre %d gün:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Due on"
msgstr "Tarihine kadar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "Vade türü"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "Kopyalanmış E-posta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "Ms cinsinden ses aktivitesinin süresi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr "Dinamik Yertutucu Oluşturucu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Edit"
msgstr "Düzenle"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "İş Ortaklarını Düzenle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Edit Subscription of"
msgstr "Abonelik düzenle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Edit subscription"
msgstr "Abonelik düzenle"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "E-Posta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "E-Posta Adresi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr "E-Posta Rumuzu"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "E-posta Rumuzları"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "E-Posta Rumuzları"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "E-posta Karalistesi"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "E-posta CC yönetimi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "E-Posta Yapılandırma"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "Email Mass Mailing"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "E-posta Önizle"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "E-posta Ara"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "E-posta Şablonu"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "E-posta Şablonu Önizleme"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
msgid "Email Templates"
msgstr "E-posta Şablonları"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "E-Posta İşlemleri"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "E-posta adresi zaten var!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Gönderenin e-posta adresi. Eşleşen bir ortak bulunmadığında ve sohbetteki "
"author_id alanının yerini aldığında bu alan ayarlanır."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to which replies will be redirected"
msgstr "Yanıtların yeniden yönlendirileceği e-posta adresi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr ""
"Toplu e-posta gönderirken yanıtların yeniden yönlendirileceği e-posta adresi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""
"Toplu e-posta gönderirken yanıtların yeniden yönlendirileceği e-posta "
"adresi; yalnızca yanıt orijinal tartışma dizisinde oturum açmadığında "
"kullanılır."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr ""
"Kara listeye alınan e-posta adresleri artık E-posta postalarını almaz."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Email alias %(alias_name)s cannot be used on %(count)d records at the same "
"time. Please update records one by one."
msgstr ""
"%(alias_name)s e-posta takma adı, %(count)d kayıtlarında aynı anda "
"kullanılamaz. Lütfen kayıtları tek tek güncelleyin."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "E-posta cc"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "E-posta yazma sihirbazı"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "E-posta mesajı"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "E-posta tekrar gönder sihirbazı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "E-posta Şablonları"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "E-postalar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Emojis"
msgstr "Emojis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "Employee Only"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "Sıralı İzlemeyi Etkinleştir"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Enable desktop notifications to chat."
msgstr "Sohbet etmek için masaüstü bildirimlerini etkinleştirin."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Envelope Example"
msgstr "Zarf Örneği"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "Hata"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "Hata Mesajı"

#. module: mail
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "Yayıncı garanti sunucuyla iletişim sırasında hata oluştu."

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"İstisnasız hata. Muhtemelen bildirim kayıtlarının eşzamanlı erişim "
"güncellemesi yapılıyor. Lütfen bir yönetici ile görüşün."

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr ""
"Istisnasız bir hata. Muhtemelen, hesaplanan alıcılar olmadan bir e-posta "
"göndermekten kaynaklanmaktadır."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Hata, bir iş ortağı aynı nesneyi iki defa izleyemez."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__public
msgid "Everyone"
msgstr "Herkes"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "İstisna"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__member_count
msgid "Excluding guests from count."
msgstr "Misafirleri sayımdan hariç tut."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Exit full screen"
msgstr "Tam ekrandan çık"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Gelişmiş Filtreler..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Posta Hatası"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Başarısız"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render QWeb template : %s)"
msgstr "QWeb şablonu oluşturulamadı: %s)"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template : %s)"
msgstr "Şablon oluşturulamadı inline_template: %s)"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render template : %(xml_id)s (%(view_id)d)"
msgstr "Şablon işlenemedi : %(xml_id)s (%(view_id)d)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Başarısızlık Nedeni"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "Başarısızlık nedeni"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Arıza nedeni. Arıza nedeni. Bu genellikle e-posta sunucusu tarafından "
"atılan, e-posta sorunlarının ayıklanmasını kolaylaştırmak için saklanır."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Başarısızlık türü"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Favori"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Feedback"
msgstr "Geri Bildirim"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field
msgid "Field"
msgstr "Alan"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "\"Posta Aktivite\" alanı \"Yanlış\" olarak değiştirilemez."

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "\"Posta Kara Listesi\" alanı \"Yanlış\" olarak değiştirilemez."

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "\"Posta Konusu\" alanı \"Yanlış\" olarak değiştirilemez."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_desc
msgid "Field Description"
msgstr "Alan Açıklaması"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr "Alan Grupları"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_type
msgid "Field Type"
msgstr "Alan Türü"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "Alan ayrıntıları"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Alan, ilişkili modeli bir ilişkili belgede alt modele otomatik abonelik "
"kullanılarak bağlamak için kullanılır. Bu alan "
"getattr(related_document.relation_field) hesaplamak için kullanılır."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Alanlar"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Son yer tutucu tanımı, istenen şablon alanına kopyalanıp-yapıştırılmak "
"üzere."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or create a channel..."
msgstr "Kanal bulma veya oluşturma..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or start a conversation..."
msgstr "Bir sohbet bulun veya başlatın..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__folded
msgid "Folded"
msgstr "Katlanmış"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Follow"
msgstr "Takip Et"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
#, python-format
msgid "Followers"
msgstr "Takipçiler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Takipçiler (İş ortakları)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Takipçi Formu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Followers of"
msgstr "Takipçileri"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Yalnızca takipçiler"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Following"
msgstr "Takip Ediliyor"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome ikonları örn. fa-tasks"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "Biçimlendirilmiş E-posta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "Başlangıç"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Full composer"
msgstr "Tam besteci"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Full screen"
msgstr "Tam Ekran"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "Tam ekran video"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Future"
msgstr "Gelecek"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Sonraki Aktiviteler"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "Ağ Geçidi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Generic User From Record"
msgstr "Kayıttan Genel Kullanıcı"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Yapılandırma paneline git"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__group
msgid "Group"
msgstr "Grup"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Grupla"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Group Name"
msgstr "Grup Adı"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Grupla ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Grouped Chat"
msgstr "Gruplandırılmış Sohbet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr "Gruplar"

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
#, python-format
msgid "Guest"
msgstr "Misafir"

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name cannot be empty."
msgstr "Konuğun adı boş bırakılamaz."

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name is too long."
msgstr "Konuğun adı çok uzun."

#. module: mail
#: model:ir.ui.menu,name:mail.mail_guest_menu
msgid "Guests"
msgstr "Misafir"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Yönlendirme"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "E-Posta ile"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Odoo İçinden"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__has_cancel
msgid "Has Cancel"
msgstr "İptal Etti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Mention İçeren"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "Mesaj Var"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "Gelen sesi devre dışı bıraktı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
#: model:ir.model.fields,help:mail.field_mail_mail__has_error
#: model:ir.model.fields,help:mail.field_mail_message__has_error
msgid "Has error"
msgstr "Hata var"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Başlıklar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "Merhaba"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Hello,"
msgstr "Merhaba,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__help_message
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Help message"
msgstr "Yardım mesajı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Gizli"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Hide Member List"
msgstr "Üye Listesini Gizle"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Takipçi seçeneklerinde alt tipi gizle"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"Hide to public / portal users, independently from subtype configuration."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "History"
msgstr "Geçmiş"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""
"Ses düzeyi eşiğini geçtikten sonra ses yayınının ne kadar süre etkin "
"kalacağı"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "ICE Sunucuları"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "ICE sunucusu"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.mail_channel_ice_servers_menu
msgid "ICE servers"
msgstr "ICE sunucuları"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_channel__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Rumuzu tutan üst kayıt ID (örnek: görev oluşturma rumuzunu tutan proje)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "İkon"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Bir istisna aktivite gösteren simge."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "İzlenen kaynağın ID si"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "Identity"
msgstr "Künye"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Idle"
msgstr "Idle"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_unread
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_unread
msgid "If checked, new messages require your attention."
msgstr "İşaretliyse, yeni mesajlar dikkatinize sunulacak."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretliyse, bazı mesajlar gönderi hatası içermektedir."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr ""
"Bu onay kutusu işaretlendiğinde, Ortaklar belgeyi takip etmeye "
"başladıklarına dairbir e-posta uyarısı alır."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked in the chatter. "
"Value is used to order tracking values."
msgstr ""
"Ayarlandıysa, bu alanda yapılan her değişiklik sohbet cihazında izlenir. "
"Değer, izleme değerlerini sipariş etmek için kullanılır."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""
"Ayarlanırsa, kuyruk yöneticisi e-postayı tarihten sonra gönderir. "
"Ayarlanmazsa, e-posta mümkün olan en kısa sürede gönderilecektir. Bir saat "
"dilimi belirtilmediği sürece, UTC saat diliminde olduğu kabul edilir."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expressions "
"expression."
msgstr ""
"Ayarlanırsa, kuyruk yöneticisi e-postayı tarihten sonra gönderir. "
"Ayarlanmazsa, e-posta mümkün olan en kısa sürede gönderilecektir. Dinamik "
"ifadeler ifadesini kullanabilirsiniz."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,help:mail.field_mail_channel__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Eğer ayarlanırsa, standart mesaj yerine yetkili olmayan kullanıcılara bu "
"içerik gönderilecektir."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Eğer e-posta adresi kara listede ise, bağlantı, toplu e-posta gönderiminde, "
"herhangi bir listeden mail almayacaktır"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"Doğruysa, yanıtlar orijinal belge tartışma dizisine girmez. Bunun yerine, "
"ileti kimliğini izlemede reply_to kontrol eder ve buna göre yeniden "
"yönlendirilir. Bunun oluşturulan ileti kimliği üzerinde etkisi vardır."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Eğer alan adınıza gelen tüm emailleri catch-all tekniği ile Odoo'ya "
"yönlendirmek istiyorsanız buraya bir alan adı girin."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red envelope next"
" to each message."
msgstr ""
"Bunları yeniden göndermek istiyorsanız, Şimdi iptal et'i tıklayın, ardından "
"bildirimi tıklayın ve her iletinin yanındaki kırmızı zarfı tıklayarak "
"bunları tek tek inceleyin."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "Twillio'yu TURN/STUN sunucu sağlayıcısı olarak kullanmak istiyorsanız"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all failures"
msgstr "Tüm hataları yoksay"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#, python-format
msgid "Image"
msgstr "Görsel"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "Görsel 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "Görsel 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "Görsel 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "Görsel 512"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "Görsel, bir bağlanttıdır"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "Pasif rumuz"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "Gelen Kutusu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Incoming Call..."
msgstr "Gelen Çağrı..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""
"Bu aktivite herhangi bir kullanıcı tarafından değil, otomatik olarak "
"oluşturulduğunu gösterir."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Info"
msgstr "Info"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "İlk model"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,help:mail.field_mail_mail__parent_id
#: model:ir.model.fields,help:mail.field_mail_message__parent_id
msgid "Initial thread message."
msgstr "İlk mesaj konusu."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Input device"
msgstr "Giriş aygıtı"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr "Entegrasyonlar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Sadece Dahili"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Geçersiz e-posta adresi"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "Geçersiz e-posta adresi %r"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Geçersiz ifade, değişmez bir python sözlük tanımı olmalıdır, örn. "
"\"{'field': 'value'}\""

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr "%smodelinde geçersiz birincil e-posta alanı"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""
"Üyelerle bir kanal oluştururken geçersiz değer, yalnızca 4 veya 6 tanesine "
"izin verilir."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr ""
"Üyelikleri olan bir kanal oluştururken geçersiz değer, yalnızca 0'a izin "
"verilir."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invitation Link"
msgstr "Davet Bağlantısı"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "Invitation to follow %(document_model)s: %(document_name)s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Invite Follower"
msgstr "Takipci Davet Edin"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invite people"
msgstr "İnsanları davet et"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to Channel"
msgstr "Kanala Davet Et"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to group chat"
msgstr "Grup sohbetine davet etme"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Davet sihirbazı"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__private
msgid "Invited people only"
msgstr "Sadece Davet Edilen Kişiler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "Is Active"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "Mevcut kullanıcı veya konuk yazar mı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "İş Düzenleyicisi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_member
msgid "Is Member"
msgstr "Üye"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Okuyanmı mu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_chat
msgid "Is a chat"
msgstr "Bir sohbet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr "Tartışma kenar çubuğu kategori kanalı açık mı?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr "Tartışma kenar çubuğu kategorisi sohbeti açık mı?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "Mikrofonun sesi kapalı mı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_pinned
msgid "Is pinned on the interface"
msgstr "Arayüzde sabitlenmiş mi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "Kullanıcı videosu gönderiyor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "Ekranı paylaşıyor mu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "Issue with audio"
msgstr "Ses sorunu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr "Katıl"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Call"
msgstr "Çağrıya Katıl"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Join Channel"
msgstr "Kanala Katıl"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Video Call"
msgstr "Video Görüşmesine Katıl"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr "Bir Gruba Katılın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "LIVE"
msgstr "CANLI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Dil"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fetched_message_id
msgid "Last Fetched"
msgstr "Son Alınan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__last_interest_dt
msgid "Last Interest"
msgstr "Son İlgi Alanı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session____last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers____last_update
#: model:ir.model.fields,field_description:mail.field_mail_guest____last_update
#: model:ir.model.fields,field_description:mail.field_mail_ice_server____last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype____last_update
#: model:ir.model.fields,field_description:mail.field_mail_notification____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template_preview____last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value____last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__seen_message_id
msgid "Last Seen"
msgstr "Son Görülen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "Son Güncelleme"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Late"
msgstr "Geciken"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Geciken Aktiviteler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__layout
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Düzen"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Leave"
msgstr "İzin"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Leave this channel"
msgstr "Bu kanaldan ayrı"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_cc__email_cc
msgid "List of cc from incoming emails."
msgstr "Gelen e-postalardaki cc listesi."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr "Bu belgenin takipçisi olarak eklenecek iş ortakları."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "List users in the current channel"
msgstr "Güncel kanaldaki kullanıcı listesi"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Bir Kanal Dinleyicileri"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Load more"
msgstr "Daha fazla yükle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Loading"
msgstr "Yükleniyor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Loading..."
msgstr "Yükleniyor..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Log"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log a note"
msgstr "Bir not kaydedin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Log a note..."
msgstr "Not oluştur..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr "Aktivite Oluştur"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr "Bir İç Not Ekleme"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Log an internal note..."
msgstr "Bir iç not ekle..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Log in the original discussion thread"
msgstr "Orijinal tartışma dizisinde oturum açın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log note"
msgstr "Not Ekle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.js:0
#, python-format
msgid "Logged in as %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
#, python-format
msgid "Mail"
msgstr "Postalama"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "Posta Aktivitesi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Mail Aktivite Tipi"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "Posta Kara Listesi"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "Posta Kara Listesi karışımı"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr "Mail Kanal Formu"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "Posta Oluşturucu Karışımı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "Mail Failures"
msgstr "Posta Hataları"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_rtc_session
msgid "Mail RTC session"
msgstr "Mail RTC oturumu"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Mail Render Mixin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "E-Posta Şablonu"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "Posta Şablonu Düzenleyicisi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "Posta Konusu"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Posta İzleme Değeri"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr "Varolan bir mail.message halkı bilgilendirmek için posta oluşturuldu"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr "Mail: E-mail Sıra Yöneticisi"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "Posta kutusu kullanılamıyor - %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Mailboxes"
msgstr "Posta kutuları"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "Postalar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_partner__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_users__message_main_attachment_id
msgid "Main Attachment"
msgstr "Ana Ek"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "Manage Messages"
msgstr "Mesajları Yönet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr ""
"Yanıtları, aynı konuya giden yanıtlar yerine yeni gelen e-postalar olarak "
"yönetin."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Mark Done"
msgstr "Biten İşaretle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Mark all read"
msgstr "Hepsini okundu işaretle"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr "Biten Olarak İşaretle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Mark as Read"
msgstr "Hepsini Okundu İşaretle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "Yapılacak Olarak İşaretle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Mark as done"
msgstr "Yapıldı olarak işaretle"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Toplantı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__member_count
msgid "Member Count"
msgstr "Üye Sayısı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr "Üyeler"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"O grubun üyeleri otomatik olarak takipçiler olarak eklenecektir. Gerek "
"duydulduğunda aboneliklerini elle değiştirebileceklerini unutmayın."

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "İş Ortağı Birleştirme Sihirbazı"

#. module: mail
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "Aşağıdaki iş ortakları ile birleştirilmiş:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#, python-format
msgid "Message"
msgstr "Mesaj"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message #%s..."
msgstr "Mesaj #%s..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message %s..."
msgstr "Mesaj %s..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Mesaj Teslim hatası"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "Mesaj ID"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Mesaj Bildirimleri"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "Mesaj Tepkisi"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "Mesaj Tepkileri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Mesaj Kayıt Adı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Mesaj Tipi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Message delivery failure image"
msgstr "İleti teslim hatası resmi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__description
#: model:ir.model.fields,help:mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr "Mesaj tanımı: konu ya da, gövdenin başlangıcı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Message posted on \"%s\""
msgstr "\"%s\" mesajında yayınlanan mesaj"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Posta alıcıları (eposta)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "Mesaj referansları, tanımlayıcılar ya da önceki mesajlar"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid EmailMessage instance"
msgstr "Message should be a valid EmailMessage instance"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Mesaj alt tipi mesaja daha doğru bir tip verecektir, özellikle sistem "
"bildirimleri için. Örneğin, yeni bir kayıtla ilgili bir bildirim olabilir "
"(Yeni), ya da bir işlemde aşama değişmesi (Aşama değişikliği). Mesaj alt "
"tipleri, kullanıcının duvarında görmek istediği bildirimleri daha doğru "
"ayarlamasını sağlar."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Mesaj alt-tipleri"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Mesaj alt tipleri izlendi, kullanıcının Duvarına iteklenecek al tipler "
"anlamına gelir."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Mesaj türü: e-posta mesajı için e-posta, sistem mesajı için bildirim,bir "
"kullanıcı cevaplar gibi diğer mesajlar için Yorum"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Tekil mesaj tanımlayıcısı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Mesaj-Id"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "Mesajlar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Mesaj Ara"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr ""
"Mesajlar <b>daha sonra tekrar kontrol etmek üzere</b> yıldızla "
"işaretlenebilir."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr "Okundu olarak işaretlenen mesajlar geçmişte görünecektir."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"İç alt nesneleri içeren mesajlar sadece base_user grubuna ait yani personel "
"tarafından görüntülenebilir"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Messages with tracking values cannot be modified"
msgstr "İzleme değerleri olan iletiler değiştirilemez"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Minimum activity for voice detection"
msgstr "Ses algılama için minimum etkinlik"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "Eksik e-posta"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email addresss"
msgstr "Eksik e-posta adresleri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr "Model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "Model değişti"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "İzlenen kaynağın modeli"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Model alt tipi için de geçerlidir. Yanlış ise, bu alt tip tüm modeller için "
"geçerlidir."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Modeller"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"Modeli değiştirmek, bu etkinlik türünü kullanarak mevcut aktiviteler "
"üzerinde etkili olabilir, dikkatli olun."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Modül Kaldırma"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Ay"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "More"
msgstr "Daha"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Mute"
msgstr "Sessiz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Aktivite Zaman Sınırım"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_channel__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
msgid "Name"
msgstr "Adı"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,help:mail.field_mail_mail__record_name
#: model:ir.model.fields,help:mail.field_mail_message__record_name
msgid "Name get of the related document."
msgstr "İlgili belge adı."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""
"Oluşturulan çıktı için kullanılacak isim (yer tutucular içerebilir)\n"
"Uzantı atlanabilir ve sonra çıktı türünden gelebilir."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model:ir.model.fields,help:mail.field_mail_mail__needaction
#: model:ir.model.fields,help:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Eylem Gerektiriyor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "New Channel"
msgstr "Yeni Kanal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Yeni Karakter Değeri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Yeni Tarih Saat Değeri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Yeni Değer Kaydırma"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Yeni Tam Sayı Değeri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_monetary
msgid "New Value Monetary"
msgstr "Yeni Parasal Değer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Yeni Metin Değeri"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/chat_window/chat_window.js:0
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "New message"
msgstr "Yeni mesaj"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages"
msgstr "Yeni mesajlar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages appear here."
msgstr "Yeni mesajlar burada görünür."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "Yeni değerler"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Next"
msgstr "Sonraki"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Next (Right-Arrow)"
msgstr "Sonraki (Sağ Ok)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Sonraki Aktiviteler"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#, python-format
msgid "Next Activity"
msgstr "Sonraki Aktivite"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Sonraki Aktivite Zaman Sınırı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Sonraki Aktivite Özeti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Sonraki Aktivite Türü"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Sıradaki Mevcut Aktiviteler"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr "Hata yok"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "No IM status available"
msgstr "Kullanılabilir IM durumu yok"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "Kayıt Yok"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No channel found"
msgstr "Hiç Kanal Bulunamadı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "No conversation selected."
msgstr "Hiçbir görüşme seçilmedi."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_list/notification_list.xml:0
#, python-format
msgid "No conversation yet..."
msgstr "Henüz konuşma yok ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No history messages"
msgstr "Geçmiş mesajı yok"

#. module: mail
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr "Bağlamda  _id found bulunamadı"

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr "Alıcı bulunamadı."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No starred messages"
msgstr "Yıldızlı mesaj yok"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "Cevaplar için hiçbir iş parçacığı yok"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No user found"
msgstr "Kullanıcı bulunamadı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "No user found that is not already a member of this channel."
msgstr "Bu kanala üye olmayan kullanıcı bulunamadı."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "No users found"
msgstr "Kullanıcı bulunamadı"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
msgid "None"
msgstr "Hiçbiri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "Normalleştirilmiş E-Posta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:mail.message.subtype,name:mail.mt_note
#, python-format
msgid "Note"
msgstr "Not"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Bildirimler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "Bildirim e-postası"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Bildirim türü"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_delete_notification
#: model:ir.cron,name:mail.ir_cron_delete_notification
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "Bildirim: 6 Aydan eski Bildirimleri Sil"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "Bildirimler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notify
msgid "Notify followers"
msgstr "Takipçileri bilgilendir"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Belgenin takipçilerini bilgilendir (sadece toplu e-postalama)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Eylemlerin Adedi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"Eylemi gerçekleştirmeden önceki gün / hafta / ay sayısı. Eylemin son "
"tarihinin planlanmasına izin verir."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Hata adedi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Eylem gerektiren mesaj adedi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslimat hatası olan mesaj adedi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_users__message_unread_counter
msgid "Number of unread messages"
msgstr "Okunmamış mesaj adedi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_alert/notification_alert.xml:0
#, python-format
msgid ""
"Odoo Push notifications have been blocked. Go to your browser settings to "
"allow them."
msgstr ""
"Odoo Push bildirimleri engellendi. İzin vermek için tarayıcı ayarlarınıza "
"gidin."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid ""
"Odoo will not have the permission to send native notifications on this "
"device."
msgstr "Odoo, bu cihazda yerel bildirimler gönderme iznine sahip olmayacak."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Offline"
msgstr "Çevrimdışı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "Tamam"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Eski Karakter Değeri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Eski Tarih Saat Değeri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Eski Sayısal Değer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Eski Tam Sayı Değeri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_monetary
msgid "Old Value Monetary"
msgstr "Eski Parasal Değer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Eski Metin Değeri"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "Eski değerler"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""
"Bir mesaj yıldızla işaretlendikten sonra, istediğiniz zaman geri gelip "
"tekrar inceleme yapabilirsiniz."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_unique_user_id
msgid "One user should only have one mail user settings."
msgstr "Bir kullanıcının yalnızca bir posta kullanıcı ayarı olmalıdır."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Online"
msgstr "Çevrimiçi"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr "Yalnızca yöneticiler posta iletisini dışa aktarabilir"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to use grouped read on message model"
msgstr ""
"Yalnızca yöneticilerin gruplandırılmış mesajda okuma modelini kullanmasına "
"izin verilir"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "Sadece özel modeller değiştirilebilir."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only logged notes can have their content updated on model '%s'"
msgstr ""
"Yalnızca günlüğe kaydedilen notların içeriği '%s' modelinde güncellenebilir"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only messages type comment can have their content updated"
msgstr "Yalnızca yorum türündeki iletilerin içeriği güncellenebilir"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Only messages type comment can have their content updated on model "
"'mail.channel'"
msgstr ""
"Yalnızca yorum türündeki iletilerin içeriği \"mail.channel\" modelinde "
"güncellenebilir"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Only users belonging to the \"%s\" group can modify dynamic templates."
msgstr ""
"Yalnızca \"%s\" grubuna ait kullanıcılar dinamik şablonları değiştirebilir."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__open
msgid "Open"
msgstr "Açık"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr "Belge Aç"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr "Üst Belgeyi Aç"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "Open chat"
msgstr "Konuşmayı aç"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_activity_notice/rtc_activity_notice.xml:0
#, python-format
msgid "Open conference:"
msgstr "Açık konferans:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Open in Discuss"
msgstr "Tartışmayı Aç"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Open profile"
msgstr "Profili aç"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "Devre Dışı Bırakıldı"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Yanıtlamasalar bile gelen mesajların ekleneceği isteğe bağlı başlık (kayıt) "
"IDsi. Ayarlanırsa, yeni kayıtların oluşumunu tamamen engelleyecektir."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr "Optional mail_mail ID. Used mainly to optimize searches."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Giden postalar için yeğlenen seçimli sunucu. Eğer ayarlanmaışsa, en yüksek "
"öncelikli olan kullanılacaktır."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template
msgid "Optional report to print and attach"
msgstr "Yazdırıp eklemek için seçmeli çıktı"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"E-posta gönderirken seçmek için isteğe bağlı çeviri dili (ISO kodu). "
"Ayarlanmazsa, İngilizce sürüm kullanılacaktır. Bu genellikle uygun dili "
"sağlayan bir yer tutucu ifadesi olmalıdır, örneğin {{ object.partner_id.lang"
" }}."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Hedef alanı boşsa kullanılacak seçimli değer"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"Özgün Tartışma: Yanıtlar özgün belge tartışma iş parçacığına gider. \n"
" Başka Bir E-posta Adresi: Yanıtlar, özgün belge tartışma konusu yerine izleme iletisi kimliğinde belirtilen e-posta adresine gider. \n"
" Bunun oluşturulan ileti kimliği üzerinde bir etkisi vardır."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Original message was deleted"
msgstr "Orijinal mesaj silindi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Çıkış Bekleyen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Giden Mail Server"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Giden Postalar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Giden mail sunucusu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "Geciken"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Yazarın e-postasını geçersiz kılar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_user_id
msgid "Owner"
msgstr "Sahibi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "PDF file"
msgstr "PDF dosyası"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Üst"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Üst Mesaj"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_model_id
msgid "Parent Model"
msgstr "Üst Model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Üst Kayıt Mesaj Dizisi ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Rumuzun bulunduğu üst model. Rumuz referansın bulunduğu model zorunlu olarak"
" alias_model_id tarafından verilen model değildir.  (örnek: project "
"(parent_model) ve görev (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Otomatik abonelik için kullanılan alt alt türü. Bu alan doğru bir şekilde "
"adlandırılmamış. Örneğin, bir projede, proje alt türlerinin parent_id'i, "
"görevle ilgili alt türlere başvurur."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
msgid "Partner"
msgstr "İş Ortağı"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "İş Ortağı Profili"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
msgid "Partner Readonly"
msgstr "Ortak Salt Okunur"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr "Partner with additional information for mail resend"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Eylem İhtiyacı Olan İş Ortakları"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid ""
"Pay attention: The followers of this document who were notified by email "
"will still be able to read the content of this message and reply to it."
msgstr ""
"Dikkat edin: Bu belgenin e-posta ile bilgilendirilen takipçileri, bu mesajın"
" içeriğini okumaya ve yanıtlamaya devam edebilecektir."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "Permission denied"
msgstr "İzin reddedildi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "Telefon"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "Telefon görüşmesi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template__copyvalue
msgid "Placeholder Expression"
msgstr "Yer Tutucu İfade"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#, python-format
msgid "Planned"
msgstr "Planlanan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_box/activity_box.xml:0
#, python-format
msgid "Planned activities"
msgstr "Planlanan  Aktiviteler"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "Planlanan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient/composer_suggested_recipient.js:0
#, python-format
msgid "Please complete customer's information"
msgstr "Lütfen müşteri bilgilerini tamamlayın"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "Bunun yerine lütfen bize ulaşın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.js:0
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Please wait while the file is uploading."
msgstr "Dosya yüklenirken lütfen bekleyin."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_container/chatter_container.xml:0
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Please wait..."
msgstr "Lütfen bekleyin..."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"Chatter bildirimlerinin nasıl ele alınacağına ilişkin politika:\n"
"- E-postalarla Ele Alın: bildirimler e-posta adresinize gönderilir\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Posta geçidini kullanarak gönderilecek mesajların kuralları:\n"
"- herkes: herkes posta gönderebilir\n"
"- iş ortakları: sadece onaylanmış iş ortakları\n"
"- takipçiler: sadece ilgili dokümanın veya takipçi kanalların takipçileri\n"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Granted"
msgstr "Portal Erişimi Verildi"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Revoked"
msgstr "Portal Erişimi İptal Edildi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_post
msgid "Post on Multiple Documents"
msgstr "Post on Multiple Documents"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "Bir dokümanda yayınla"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Post your message on the thread"
msgstr "Mesajınızı mesaj dizisine gönderin"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""
"Mesaj göndermek bir iş belgesine yapılmalıdır. Bir kullanıcıya bildirim "
"göndermek için message_notify kullanın."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message with channels as listeners is not supported since Odoo "
"14.3+. Please update code accordingly."
msgstr ""
"Odoo 14.3+ sürümünden beri, kanalların dinleyici olarak mesaj göndermesi "
"desteklenmemektedir. Lütfen kodu uygun şekilde güncelleyin."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Powered by"
msgstr "Hazırlayan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Önceki Aktiviteler"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "Preferred response address"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Press a key to register it as the push-to-talk shortcut"
msgstr "Bas-konuş kısayolu olarak kaydetmek için bir tuşa basın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Önizle"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "Bunun önizlemesi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Previous"
msgstr "Önceki"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Previous (Left-Arrow)"
msgstr "Önceki (Sol Ok)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Önceki Aktivite Türü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Print"
msgstr "Yazdır"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr "Özel"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Private channel"
msgstr "Özel Kanal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category/discuss_sidebar_category.js:0
#, python-format
msgid "Public Channels"
msgstr "Genel Kanallar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Public channel"
msgstr "Herkese açık kanal"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Yayıncı Garanti Sözleşmesi"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr "Yayıncı: Güncelleme Bildirimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "Bas-Konuş kısayolu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Push-to-talk key"
msgstr "Bas-konuş tuşu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Quick search..."
msgstr "Hızlı arama..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "RTC Oturumu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_session_ids
msgid "RTC Sessions"
msgstr "RTC Oturumları"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.mail_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "RTC oturumları"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr "0,0 ile 1,0 arasında değişir, ölçek tarayıcı uygulamasına bağlıdır"

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Re:"
msgstr "Yanıt:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "Tepki Veren Konuk"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "Tepki Veren İş Ortağı"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reaction"
msgstr "Tepkiler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
msgid "Reactions"
msgstr "Tepkiler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "Okuma Tarihi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#, python-format
msgid "Ready"
msgstr "Hazır"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Gönderime Hazır"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "Sebep"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Alınan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s"
msgstr "Tarafından alındı %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s and %s"
msgstr "%s ve %s tarafından alındı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s, %s and more"
msgstr "%s, %s ve daha fazlası tarafından alındı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by Everyone"
msgstr "Herkes Tarafından Alındı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Alıcı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "Alıcılar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Önerilen Aktivite"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "Kayıt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Kayıt Başlık ID"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr "Kayıtlar:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Redirect to another email address"
msgstr "Başka bir e-posta adresine yönlendirme"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Referanslar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Refuse"
msgstr "Reddet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "Saygılarımızla,"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Register new key"
msgstr "Yeni anahtar kaydetme"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Reject"
msgstr "Red"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "İlişkili Şirket"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "İlgili Döküman ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "İlgili Döküman Modeli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "İlgili Doküman Model Adı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "Related Mail Template"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "İlişkili Mesaj"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "İlişkili İş Ortağı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "İlişkili alanı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Remove"
msgstr "Kaldır"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "Remove email from blacklist wizard"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr "İlgili belgelerde bu şablonu kullanmak için içeriksel eylemi kaldır"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Remove this follower"
msgstr "Bu takipçiyi kaldır"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "İşleme Modeli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "Cevaplar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Cevapla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "Yanıtla"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Yanıt e-posta adresi. Reply_to ayarı, otomatik iş parçacığı oluşturmayı "
"atlar."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "Yanıtlama"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Replying to"
msgstr "Yanıtlama"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_name
msgid "Report Filename"
msgstr "Çıktı Dosyası Adı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "Requesting Partner"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__res_users_settings_ids
msgid "Res Users Settings"
msgstr "Res Kullanıcı Ayarları"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Resend mail"
msgstr "Postayı yeniden gönder"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Resend to selected"
msgstr "Seçilene tekrar gönder"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "Yeniden gönderme sihirbazı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Reset Zoom"
msgstr "Yakınlaştırmayı Sıfırla"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Reset Zoom (0)"
msgstr "Yakınlaştırmayı Sıfırla (0)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
msgid "Responsible"
msgstr "Sorumlu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Sorumlu Kullanıcı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "Şablon İşlemeyi Kısıtlama"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates and Jinja rendering."
msgstr "Posta şablonlarını ve Jinja işlemeyi kısıtlayın."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr ""
"Posta şablonları sürümünü ve QWEB yer tutucularının kullanımını kısıtlayın."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Yinele"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Rich-text Contents"
msgstr "Zengin-metin İçerik"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Zengin-metin/HTML mesajı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_inviting_session_id
msgid "Ringing session"
msgstr "Oturumu zil sesi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Rotate"
msgstr "Döndür"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Rotate (r)"
msgstr "Döndür (r)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "Rtc Oturumu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP Sunucusu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Satış Temsilcisi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Save"
msgstr "Kaydet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Yeni bir şablon olarak kaydetme"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr "Yeni Şablon olarak kaydet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Planla"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/activity/activity.js:0
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Schedule Activity"
msgstr "Aktivite Planla"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule activities to help you get things done."
msgstr "Schedule activities to help you get things done."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Schedule activity"
msgstr "Aktivite Planla"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "Aktivite Planla"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "Aktivite Planla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "Planlanan Tarih"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Planlanmış Gönderi Tarihi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr "Rumuz Ara"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr "Grupları Ara"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "RTC oturumunda arama yapma"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.js:0
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#, python-format
msgid "Search user..."
msgstr "Arama kullanıcısı..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr "Arama: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s"
msgstr "%s tarafından görüldü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s and %s"
msgstr "%s ve %s tarafından görüldü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s, %s and more"
msgstr "%s, %s ve daha fazlası tarafından görüldü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by Everyone"
msgstr "Herkes tarafından görüldü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Select a user..."
msgstr "Bir kullanıcı seçin..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"İlgili belge modelinden hedef dosyayı seç.\n"
"Eğer bir ilişki alanı ise ilişkinin varışında bir hedef alan seçebilmelisiniz ."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"Select the action to do on each mail and correct the email address if "
"needed. The modified address will be saved on the corresponding contact."
msgstr ""
"Her bir postada yapılacak işlemi seçin ve gerekirse e-posta adresini "
"düzeltin. Değiştirilen adres ilgili kişiye kaydedilir."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__groups
#, python-format
msgid "Selected group of users"
msgstr "Seçilen kullanıcı grubu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Selected users:"
msgstr "Seçilen kullanıcılar:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Gönder"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Send Again"
msgstr "Tekrar gönderin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__send_mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__email
msgid "Send Email"
msgstr "E-posta Gönder"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "İletiyi Gönder (%s)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Şimdi Gönder"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send a message"
msgstr "Bir mesaj gönder"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Send a message to followers..."
msgstr "Takipçilere mesaj gönderin..."

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "E-posta Gönder"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send message"
msgstr "Mesaj Gönder"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "Sender address"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Gönderici adresi (burada yer tutucular kullanılabilir). Ayarlanmamışsa, "
"öntanımlı değer yazanın eposta rumuzu (eğer yapılandırılmışsa) ya da e-posta"
" adresidir."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "Gönderildi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Sıra"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Sunucu Eylemi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "Set active to false to hide the channel without removing it."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Settings"
msgstr "Ayarlar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Share screen"
msgstr "Ekranı paylaş"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift left"
msgstr "Sola kaydırın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift right"
msgstr "Sağa kaydırın"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__description
#: model:ir.model.fields,field_description:mail.field_mail_message__description
msgid "Short description"
msgstr "Kısa Açıklama"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Kısa kodlar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Kısayol"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show All"
msgstr "Tümünü Göster"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#, python-format
msgid "Show Followers"
msgstr "Takipçileri Göster"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Show Member List"
msgstr "Üye Listesini Göster"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Show a helper message"
msgstr "Show a helper message"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "Bir sonraki eylem tarihi bugünden önce olan tüm kayıtları göster"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show less"
msgstr "Daha az göster"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show more"
msgstr "Daha fazla göster"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show only video"
msgstr "Yalnızca videoyu göster"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Showing"
msgstr "Gösterilen"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Sidebar"
msgstr "Kenar Çubuğu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Kenar Çubuğu işlemi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Kenar çubuğu bu şablonun ilgili belge modellerinde kullanılmasını sağlar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "Kaynak"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "Belirli Kullanıcı"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"Aktivite bir modele özgü olması ve diğer modellerin aktivitelerini "
"yönetirken kullanılamaması durumunda bir model belirtin."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Spotlight"
msgstr "Ana Sayfa"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Yıldızlı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Call"
msgstr "Arama Başlat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Video Call"
msgstr "Görüntülü Arama Başlat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Start a conversation"
msgstr "Bir konuşma başlatın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Start a meeting"
msgstr "Toplantı başlatma"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "State"
msgstr "Durum"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Durumu"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Aktivite aşamalar\n"
"Zamanı Geçmiş: Tarihi geçmiş \n"
"Bugün: Aktivite günü bugün\n"
"Planlanan: Gelecek aktivite."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Stop adding users"
msgstr "Kullanıcı eklemeyi durdur"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop camera"
msgstr "Kamerayı durdur"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Stop replying"
msgstr "Yanıtlamayı durdur"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop screen sharing"
msgstr "Ekran paylaşımını durdur"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""
"Şu deseni izleyen değiştiricilere sahip bir anahtarı temsil edecek şekilde "
"biçimlendirilmiş dize: shift.ctrl.alt.key, örn. truthy.1.true.b"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_model_object_field
msgid "Sub-field"
msgstr "Alt-alan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_object
msgid "Sub-model"
msgstr "Alt-model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "Konu"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr "Konu (burada yer tutucular kullanılabilir)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr "Konu..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Subject:"
msgstr "Konu:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Değişiklik"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Alt-Tür"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Alt-Türler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "Gönder"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "Sonraki Etkinliği Öner"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr ""
"Geçerli olan tamamlandı olarak işaretlendikten sonra bu etkinlikleri önerin."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
msgid "Summary"
msgstr "Özet"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "Sistem Parametreleri"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
#, python-format
msgid "System notification"
msgstr "Sistem bildirimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "Targeted model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "VKN/TCKN"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__has_recommended_activities
msgid "Technical field for UX purpose"
msgstr "UX için teknik alan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_change
msgid "Technical field for UX related behaviour"
msgstr "UX ile ilgili davranışlar için teknik alan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__can_write
msgid "Technical field to hide buttons if the current user has no access."
msgstr ""
"Geçerli kullanıcının erişimi yoksa düğmeleri gizlemek için teknik alan."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr "Kaydın üzerindeki kullanıcının teknik adı"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "Şablon Önizleme"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "Şablon Önizleme Dili"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering for language should be called with a list of IDs."
msgstr "Template rendering for language should be called with a list of IDs."

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called on a valid record IDs."
msgstr "Şablon işleme, geçerli bir kayıt kimliğinde çağrılmalıdır."

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called only using on a list of IDs."
msgstr "Template rendering should be called only using on a list of IDs."

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw)."
msgstr ""
"Şablon oluşturma yalnızca inline_template, qweb veya qweb_view (view veya "
"raw) destekler."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Şablonlar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Text file"
msgstr "Metin dosyası"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "Bu"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr "'Bitiş Tarihi Giriş' değeri negatif olamaz."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "The FullScreen mode was denied by the browser"
msgstr "Tam Ekran modu tarayıcı tarafından reddedildi"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""
"Vergi Kimlik Numarası. İlgili kişi devlet vergilerine tabi ise bu işlemi "
"tamamlayın. Bazı yasal beyanlarda kullanılmaktadır."

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "%s eki mevcut değil veya buna erişim hakkınız yok."

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "The attachment %s does not exist."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "UUID kanalı benzersiz olmalıdır"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already linked with "
"%(alias_model_name)s. Choose another alias or change it on the linked model."
msgstr ""
"%(matching_alias_name)s e-posta diğer adı zaten %(alias_model_name)s ile "
"bağlantılıdır. Başka bir diğer ad seçin veya bağlantılı modelde değiştirin."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used as "
"%(alias_duplicate)s alias. Please choose another alias."
msgstr ""
"%(matching_alias_name)s e-posta diğer adı zaten %(alias_duplicate)s diğer "
"adı olarak kullanılıyor. Lütfen başka bir takma ad seçin."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used by the "
"%(document_name)s %(model_name)s. Choose another alias or change it on the "
"other document."
msgstr ""
"%(matching_alias_name)s e-posta diğer adı, %(document_name)s %(model_name)s "
"tarafından zaten kullanılıyor. Başka bir diğer ad seçin veya diğer belgede "
"değiştirin."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "Adresine gönderilen e-posta"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid "The escaped html code replacing the shortcut"
msgstr "Kısayolun yerine geçen çıkış kodu"

#. module: mail
#: code:addons/mail/models/mail_composer_mixin.py:0
#, python-format
msgid "The field %s does not exist on the model %s"
msgstr "%s alanı %s modelinde mevcut değil"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "Bu kişiden sorumlu dahili kullanıcı."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Diğer isimle uyuşan model (Odoo belge gibi). Herhangi gelen cevaplanmamış "
"bir e-posta  bu modelin yeni kaydını oluşturmaya sebep olur. (örn. bir Proje"
" Görevi)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_channel__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "E-posta rumuz adı, örn. e-postaları yakalamak istiyorsanız 'kariyer'"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Kayıtların sahibi, bu rumuza e-postalar alındıktan sonra oluşturulmuştur. Bu"
" alan ayarlanmamışsa, sistem gönderenin (Kimden) adresinden doğru sahibini "
"bulmaya çalışacaktır ya da o adres için hiç sistem kullanıcısı bulunamazsa "
"Administrator hesabını kullanacaktır."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr "The queue manager will send the email after the date"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"Güvenlik sınırlamalarından dolayı istenen işlem tamamlanamıyor. Lütfen sistem yöneticinize danışın.\n"
"\n"
"(Belge tipi: %s, İşlem: %s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr "Konuşma Mesajlarında, değişmesi gereken kısayollar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/follower/follower.js:0
#, python-format
msgid "The subscription preferences were successfully applied."
msgstr "Abonelik tercihleri başarıyla uygulandı."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__model_id
#: model:ir.model.fields,help:mail.field_mail_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "Bu şablon ile birlikte kullanılabilen belge türünü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "There are no messages in this conversation."
msgstr "Bu görüşmede mesaj yok."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_rtc_session_channel_partner_unique
msgid "There can only be one rtc session per channel partner"
msgstr "Kanal iş ortağı başına yalnızca bir rtc oturumu olabilir"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Bu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "This action will send an email."
msgstr "Bu işlem bir e-posta gönderecek."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr "This email is blacklisted for mass mailings. Click to unblacklist."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "Bu alan büyük / küçük harfe duyarlı değildir."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Birincil e-posta alanı kesinlikle bir e-posta adresinden fazlasını "
"içerebileceğinden, bu alan e-posta adresinde arama yapmak için kullanılır."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr ""
"Bu grup üye olmayanlar tarafından görülebilir. Görünmez gruplar, davet "
"butonuyla üye ekleyebilir."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "This is their first connection. Wish them luck."
msgstr "Bu onların ilk bağlantısı. Onlara şans dileyin."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"Bu seçenek, Odoo veritabanınızın depolama alanını korumak için Ayarlar'daki "
"Teknik menü de dahil olmak üzere gönderildikten sonra herhangi bir e-posta "
"parçasını kalıcı olarak kaldırır."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "This record has an exception activity."
msgstr "Bu kaydın bir istisna aktivitei var."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Save the record before scheduling an activity!"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "This user can not be added in this channel"
msgstr "Bu kullanıcı bu kanala eklenemez"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Konu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Thread Image"
msgstr "Konu Resmi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Tiled"
msgstr "Döşenmiş"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "Saat Dilimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
msgid "To"
msgstr "Bitiş"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Kime (Epostalar)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "Kime (İş Ortakları)"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To Do"
msgstr "Yapılacak"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "To:"
msgstr "Kime:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#, python-format
msgid "Today"
msgstr "Bugün"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Bugünkü Aktiviteler"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Today:"
msgstr "Bugün:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Tomorrow"
msgstr "Yarın"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Tomorrow:"
msgstr "Yarın:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Bu grupta tartışılan başlıklar..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Takip edilen değerler ayrı modellerde depolanır. Bu alan, takip etmeyi "
"yeniden oluşturmaya ve model üzerinde istatistik oluşturmanıza imkan verir."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "İzleme"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "İzleme Değeri"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "İzleme Değerleri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__tracking_sequence
msgid "Tracking field sequence"
msgstr "İzleme alanı sırası"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "İzleme Değerleri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "Tetik"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "Sonraki Aktiviteyi Tetikle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Turn camera on"
msgstr "Kamerayı aç"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "Twilio Hesabı Kimlik Doğrulama Anahtarı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "Twilio Hesap SID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Tür"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "Gecikme türü"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""
"Sunucu eylemi türü. Aşağıdaki değerler mevcuttur:\n"
"- 'Python Kodunu Yürüt': yürütülecek bir python kodu bloğu\n"
"- 'Oluştur': yeni değerlerle yeni bir kayıt oluştur\n"
"- 'Bir Kaydı Güncelle': bir kaydın değerlerini günceller\n"
"- 'Birkaç işlemi yürüt': diğer birkaç sunucu işlemini tetikleyen bir eylem tanımlayın\n"
"- 'E-posta Gönder': otomatik olarak bir e-posta gönder (Tartışma)\n"
"- 'Takipçi Ekle': bir kayda takipçi ekleyin (Tartışın)\n"
"- 'Sonraki Aktivite Oluştur': aktivite oluştur (Tartışma)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kayıttaki istisna aktivite türü."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Type the name of a person"
msgstr "Bir kişinin adını yazın"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "SMTP Sunucusuna bağlanılamıyor"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to log message, please configure the sender's email address."
msgstr "Mesaj kaydedilemedi, lütfen gönderenin e-posta adresini yapılandırın."

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Mesaj gönderilemiyor, lütfen gönderenin e-posta adresini yapılandırın."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "Kara listeyi kaldır"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Unblacklisting Reason: %s"
msgstr "Unblacklisting Reason: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Undeafen"
msgstr "Sağır Olmayan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Unfollow"
msgstr "Takibi Bırak"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_alias_unique
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr ""
"Ne yazık ki bu eposta rumuzu zaten kullanılıyor, lütfen bir eşsiz olanı "
"seçin"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "Gecikme birimi"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "Bilinmeyen hata"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Unmute"
msgstr "Sesi aç"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Unpin Conversation"
msgstr "Konuşmanın Sabitlemesini Kaldır"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread
msgid "Unread Messages"
msgstr "Okunmamış Mesajlar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Okunmamış Mesaj Sayacı"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "Okunmamış mesajlar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Unstar all"
msgstr "Tüm Yıldızları Kaldır"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "Desteklenmeyen rapor türü %s bulundu."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
#, python-format
msgid "Upload Document"
msgstr "Belgeyi Yükle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Upload file"
msgstr "Dosya yükleme"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#, python-format
msgid "Uploaded"
msgstr "Yüklendi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Uploading"
msgstr "Yükleniyor"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""
"Gelecek aktiviteyi aynı kullanıcıyı atamak için her zaman 'Özel Kullanıcı'yı"
" kullanın. Kayıtta seçilecek kullanıcının alan adını belirlemek için "
"'Kayıttan Genel Kullanıcı' seçeneğini kullanın."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Use Push-to-talk"
msgstr "Bas-konuş'u kullan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "Twilio ICE sunucularını kullan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr "Kullanımı etkin etki alanı"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Şablonu Kullan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "Bas-konuş özelliğini kullan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "Parasal değerleri izlerken para birimini görüntülemek için kullanılır"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Alt-Türler."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "Kullanıcı"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "Kullanıcı Durumu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "Kullanıcı ayarları"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "Kullanıcı ayarları"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "Kullanıcı Ayarları Birimleri"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "Kullanıcıya Özel Bildirim"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User field name"
msgstr "Kullanıcı alan adı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is a bot"
msgstr "Kullanıcı bir bottur"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is idle"
msgstr "Kullanıcı boşta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is offline"
msgstr "Kullanıcı çevrimdışı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is online"
msgstr "Kullanıcı çevrimiçi"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "Kullanıcı:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "Kullanıcı ismi"

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr "Kullanıcılar"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Users in this channel: %(members)s %(dots)s and you."
msgstr "Users in this channel: %(members)s %(dots)s and you."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"Kullanıcılar şablonları işlemeye devam edebilir.\n"
"Ancak yalnızca Posta Şablonu Düzenleyicileri yeni dinamik şablonlar oluşturabilir veya mevcut şablonları değiştirebilir."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"Odoo'da bir E-Posta sunucusu ayarlarsanız müşteri ve tedarikçilerinizle olan"
" iletişiminizi de sistem içinden yönetebilirsiniz. Otomatik "
"bilgilendirmeler, otomatik hatırlatmalar ve konu bazında yazışmalar için de "
"E-Posta sunucusu tanımlamanız gerekir."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Value for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Video"
msgstr "Video"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr "Görüntüle"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "Görüntüle %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Görünüm Türü"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "View image"
msgstr "Resmi görüntüle"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category/discuss_sidebar_category.xml:0
#, python-format
msgid "View or join channels"
msgstr "Kanalları görüntüleme veya kanallara katılma"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Viewer"
msgstr "Görüntüleyici"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "Ses"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "Hacim"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "İş ortağı başına hacim"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "Diğer ortakların hacimleri"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Warning"
msgstr "Uyarı"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Hafta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "What's your name?"
msgstr "Adınız ne?"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"İlk alan olarak ilişki alanı seçilmişse, bu alan, varış belge modeli içinden"
" hedef alanını seçmenizi sağlar."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"İlk alan olarak bir ilişki alanı seçldiğinde, bu alan ilişkinin gideceği "
"belge modelini gösterir."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr "Mesaj bir iç notu olsun (comment modu)"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_activity
msgid "Whether this model supports activities."
msgstr "Bu modelin faaliyetleri destekleyip desteklemediği."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_blacklist
msgid "Whether this model supports blacklist."
msgstr "Bu modelin kara listeyi destekleyip desteklemediği."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr "Bu modelin ileti ve bildirimleri destekleyip desteklemediğidir."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr "Grubun faaliyetlerini kim takip edebilir?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Write Feedback"
msgstr "Geribildirim Yaz"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Wrong operation name (%s)"
msgstr "Yanlış işlem adı (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "YYYY-AA-GG SS:DD:SS"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Yesterday"
msgstr "Dün"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Yesterday:"
msgstr "Dün:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"Bu grup konuşmasından ayrılmak üzeresiniz ve tekrar davet edilmediğiniz "
"sürece artık bu konuşmaya erişemezsiniz. Devam etmek istediğinizden emin "
"misiniz?"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "Bu kanalda yalnızsın."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr "<b>@%s</b> ile özel sohbettesiniz."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr "<b>#%s</b> kanalındasınız."

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "Buraya bir ek yüklemenize izin verilmiyor."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "Bu kanalın yöneticisi sizsiniz. Ayrılmak istediğinden emin misin?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"Herhangi bir mesajı 'yıldız işaretli' olarak işaretleyebilirsiniz ve bu "
"posta kutusunda görünür."

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "You can not write on %(field_name)s."
msgstr "%(field_name)s üzerine yazamazsınız."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only chat with existing users."
msgstr "Yalnızca mevcut kullanıcılarla sohbet edebilirsiniz."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/partner/partner.js:0
#, python-format
msgid "You can only chat with partners that have a dedicated user."
msgstr ""
"Yalnızca özel bir kullanıcısı olan iş ortaklarıyla sohbet edebilirsiniz."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging/messaging.js:0
#, python-format
msgid "You can only open the profile of existing channels."
msgstr "Yalnızca mevcut kanalların profilini açabilirsiniz."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only open the profile of existing users."
msgstr "Yalnızca mevcut kullanıcıların profilini açabilirsiniz."

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""
"Burada. \n"
" yeni bir kullanıcı oluşturmak için yeni bir kullanıcı oluşturamazsınız lütfen yapılandırma Masası'na gidin."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Bütün şirketin diğer modüller tarafından gerektiği şekilde bu gruplar "
"silinemez."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address (%s)."
msgstr ""
"Takma ad adresinde (%s) aksansız latin karakterlerden başka bir şey "
"kullanamazsınız."

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""
"E-postaları kara listeden çıkarma hakkına sahip değilsiniz. Lütfen "
"yöneticinize başvurun."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "%s adresine atandınız"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Size atanan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You have been invited to #%s"
msgstr "#%s'a davet edildiniz"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Bu şablona dosya eleyebilirsiniz, bu şablonda oluşturulan tüm epostalara "
"eklenmek üzere"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unpinned your conversation with %s."
msgstr "%s ile yaptığınız konuşmanın sabitlemesini kaldırdınız."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unsubscribed from %s."
msgstr "%s aboneliğinizi iptal ettiniz."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a chat!"
msgstr "Bir sohbete davet edildiniz!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a meeting!"
msgstr "Bir toplantıya davet edildiniz!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_author_prefix/message_author_prefix.xml:0
#, python-format
msgid "You:"
msgstr "Sen:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr "Sizin"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Your browser does not support videoconference"
msgstr "Tarayıcınız video konferansı desteklemiyor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "Your browser does not support voice activation"
msgstr "Tarayıcınız sesle etkinleştirmeyi desteklemiyor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Your browser does not support webRTC."
msgstr "Tarayıcınız webRTC'yi desteklemiyor."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Your name"
msgstr "Adınız"

#. module: mail
#: code:addons/mail/controllers/home.py:0
#, python-format
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""
"Şifreniz varsayılan (admin)! Bu sistem güvenilmeyen kullanıcılara maruz "
"kalırsa, güvenlik nedenleriyle derhal değiştirilmesi önemlidir. Bu konuda "
"seni rahatsız etmeye devam edeceğim!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom In"
msgstr "Yakınlaştır"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom In (+)"
msgstr "Yakınlaştır"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Uzaklaştır"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom Out (-)"
msgstr "Uzaklaştır"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered partners"
msgstr "kayıtlı ortaklarla bağlantılı adresler"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "tamamlanma tarihinden sonra"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "önceki aktivite süresinden sonra"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %(name)s: %(error)s"
msgstr "alias %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "assigned you an activity"
msgstr "sana bir aktivite atandı"

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr "yönetim kurulu toplantıları"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "bounce"
msgstr "sıçrama"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "talebi karşılayan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "cancel"
msgstr "iptal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"işlenemiyor. Bu adres\n"
"   yanıt toplamak için kullanılır ve doğrudan iletişim kurmak için kullanılmamalıdır"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "catchall"
msgstr "catchall"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "channel"
msgstr "kanal"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "gün"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "deaf"
msgstr "sağır"

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_thread.py:0
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "document"
msgstr "belge"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "ok"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. \"mycompany.com\""
msgstr "örn. \"mycompany.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "örn. 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "örn. ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Calendar: Reminder"
msgstr "örn. Takvim: Hatırlatıcı"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "örn. Teklifi tartışın"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "örn. Toplantı planlama"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Users"
msgstr "örn. Kullanıcılar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "e.g. support"
msgstr "örn. destek"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "örn. true.true.true.. f"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "escape to"
msgstr "kaçmak"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "for %s"
msgstr "için %s"

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr "genel"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "has been created from:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "has been modified from:"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "yanlış yapılandırılmış rumuz"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "yanlış yapılandırılmış takma ad (bilinmeyen referans kaydı)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "live"
msgstr "yayında"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support model and res_id parameters anymore. Please "
"call message_post on record."
msgstr ""
"message_post artık model ve res_id parametrelerini desteklememektedir. "
"Lütfen message_post kayıtlı olarak arayın."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support subtype parameter anymore. Please give a valid"
" subtype_id or subtype_xmlid value instead."
msgstr ""
"message_post artık alt tür parametresini desteklemiyor. Lütfen bunun yerine "
"geçerli bir subtype_id veya subtype_xmlid değeri verin."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "message_post partner_ids and must be integer list, not commands."
msgstr ""
"message_post partner_ids ve komutlar değil, tamsayı listesi olmalıdır."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "model %s belge oluşturmayı kabul etmiyor"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "ay"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "ms"
msgstr "mili saniye"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "muted"
msgstr "Sessiz"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/utils.js:0
#, python-format
msgid "now"
msgstr "şimdi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "tarihi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "on:"
msgstr "üzerinde:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "or"
msgstr "veya"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "other members."
msgstr "diğer üyeler."

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr "rd"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read less"
msgstr "daha az okuma"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read more"
msgstr "daha fazla"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "record:"
msgstr "kayıt:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""
"belge güncellemesini kabul etmeyen %smodeline yanıt verin, belge oluşturmaya"
" geri dönün"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "restricted to channel members"
msgstr "kanal üyeleri ile sınırlı"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to followers"
msgstr "takipçiler ile sınırlı"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to known authors"
msgstr "bilinen yazarlar ile sınırlı"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "results out of"
msgstr "sonuçları"

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr "satışlar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "save"
msgstr "kaydet"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "some specific addresses"
msgstr "bazı özel adresler"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "stun:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr "hedef model belirtilmemiş"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "takım."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "this document"
msgstr "bu belgede"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "to close for"
msgstr "kapatmak"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "toggle push-to-talk"
msgstr "bas-konuş özelliğini değiştirme"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "dönüş:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "bilinmeyen hata"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "bilinmeyen hedef model %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "using"
msgstr "kullanma"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/many2one_avatar_user.xml:0
#, python-format
msgid "value"
msgstr "değer"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "haftalar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"
