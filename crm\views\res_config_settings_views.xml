<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.crm</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="5"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" data-string="CRM" string="CRM" data-key="crm" groups="sales_team.group_sale_manager">
                    <h2>CRM</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="group_use_recurring_revenues"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_use_recurring_revenues"/>
                                <div class="text-muted">
                                    Define recurring plans and revenues on Opportunities
                                </div>
                                <div attrs="{'invisible': [('group_use_recurring_revenues', '=', False)]}">
                                    <button type="action" name="crm.crm_recurring_plan_action"
                                            string="Manage Recurring Plans" icon="fa-arrow-right" class="oe_link"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box" id="crm_lead"
                            title="Use leads if you need a qualification step before creating an opportunity or a customer. It can be a business card you received, a contact form filled in your website, or a file of unqualified prospects you import, etc. Once qualified, the lead can be converted into a business opportunity and/or a new customer in your address book.">
                            <div class="o_setting_left_pane">
                                <field name="group_use_lead"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_use_lead"/>
                                <div class="text-muted">
                                    Add a qualification step before the creation of an opportunity
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="is_membership_multi"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="is_membership_multi"/>
                                <div class="text-muted">
                                    Assign salespersons into multiple Sales Teams.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt16 o_settings_container">
                        <div class="col-lg-6 o_setting_box"
                            title="This can be used to compute statistical probability to close a lead"
                            name="predictive_lead_setting_container">
                            <field name="predictive_lead_scoring_fields_str" invisible="1"/>
                            <field name="predictive_lead_scoring_start_date_str" invisible="1"/>
                            <div class="o_setting_left_pane"></div>
                            <div class="o_setting_right_pane">
                                <b>Predictive Lead Scoring</b>
                                <div class="text-muted">
                                    The success rate is computed based on <b>
                                        <field name="predictive_lead_scoring_field_labels" class="d-inline"/>
                                    </b>
                                    for the leads created as of the
                                    <b><field name="predictive_lead_scoring_start_date" class="oe_inline" readonly="1"/></b>.
                                 </div>
                                <div class="mt16" groups="base.group_erp_manager">
                                    <button name="%(crm_lead_pls_update_action)d" type="action"
                                        string="Update Probabilities"
                                        class="btn-primary"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box"
                            title="This can be used to automatically assign leads to sales persons based on rules">
                            <div class="o_setting_left_pane">
                                <field name="crm_use_auto_assignment"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="crm_use_auto_assignment"/>
                                <a href="https://www.odoo.com/documentation/15.0/applications/sales/crm/track_leads/lead_scoring.html#assign-leads"
                                    title="Assign Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    <span>Periodically assign leads based on rules</span><br />
                                    <span attrs="{'invisible': [('crm_use_auto_assignment', '=', False)]}">
                                        All sales teams will use this setting by default unless
                                        specified otherwise.
                                    </span>
                                </div>
                                <div class="row mt16" attrs="{'invisible': [('crm_use_auto_assignment', '=', False)]}">
                                    <label string="Running" for="crm_auto_assignment_action" class="col-lg-3 o_light_label"/>
                                    <field name="crm_auto_assignment_action"
                                        attrs="{'required': [('crm_use_auto_assignment', '=', True)]}"/>
                                    <button name="action_crm_assign_leads" type="object" class="btn-link">
                                        <i title="Update now" role="img" aria-label="Update now" class="fa fa-fw fa-refresh"></i>
                                    </button>
                                </div>
                                <div class="row mt16" attrs="{'invisible': ['|', ('crm_use_auto_assignment', '=', False), ('crm_auto_assignment_action', '=', 'manual')]}">
                                    <label string="Repeat every" for="crm_auto_assignment_interval_type" class="col-lg-3 o_light_label"/>
                                    <field name="crm_auto_assignment_interval_number"
                                        class="oe_inline mr-2"
                                        attrs="{'required': [('crm_use_auto_assignment', '=', True), ('crm_auto_assignment_action', '=', 'auto')]}"/>
                                    <field name="crm_auto_assignment_interval_type"
                                        class="oe_inline"
                                        attrs="{'required': [('crm_use_auto_assignment', '=', True), ('crm_auto_assignment_action', '=', 'auto')]}"/>
                                </div>
                                <div class="row" attrs="{'invisible': ['|', ('crm_use_auto_assignment', '=', False), ('crm_auto_assignment_action', '=', 'manual')]}">
                                    <label string="Next Run" for="crm_auto_assignment_run_datetime" class="col-lg-3 o_light_label"/>
                                    <field name="crm_auto_assignment_run_datetime"/>
                                </div>

                            </div>
                        </div>
                    </div>

                    <h2>Lead Generation</h2>
                    <div class="row mt16 o_settings_container" name="convert_visitor_setting_container">
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="module_crm_iap_enrich"/>
                            </div>

                            <div class="o_setting_right_pane" id="crm_iap_enrich_settings">
                                <label string="Lead Enrichment" for="module_crm_iap_enrich"/>
                                <div class="text-muted">
                                    Enrich your leads automatically with company data based on their email address
                                </div>
                                <div class="mt8" attrs="{'invisible': [('module_crm_iap_enrich','=',False)]}">
                                    <field name="lead_enrich_auto" class="o_light_label" widget="radio" required="True"/>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="module_crm_iap_mine"/>
                            </div>

                            <div class="o_setting_right_pane" id="crm_iap_mine_settings">
                                <label string="Lead Mining" for="module_crm_iap_mine"/>
                                <a href="https://www.odoo.com/documentation/15.0/applications/sales/crm/acquire_leads/lead_mining.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Generate new leads based on their country, industry, size, etc.
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row mt16 o_settings_container" name="generate_lead_setting_container">
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="module_website_crm_iap_reveal"/>
                            </div>

                            <div class="o_setting_right_pane" id="website_crm_iap_reveal_settings">
                                <label string="Visits to Leads" for="module_website_crm_iap_reveal"/>
                                <div class="text-muted">
                                    Convert visitors of your website into leads and perform data enrichment based on their IP address
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="crm_config_settings_action" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_id" ref="res_config_settings_view_form"/>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'crm', 'bin_size': False}</field>
    </record>

</odoo>
