# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_maintenance
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:54+0000\n"
"PO-Revision-Date: 2017-09-20 09:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Afrikaans (https://www.transifex.com/odoo/teams/41243/af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment_department_id
msgid "Assigned to Department"
msgstr ""

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment_employee_id
msgid "Assigned to Employee"
msgstr ""

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "Created By"
msgstr ""

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request_department_id
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
#: selection:maintenance.equipment,equipment_assign_to:0
msgid "Department"
msgstr ""

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request_employee_id
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
#: selection:maintenance.equipment,equipment_assign_to:0
msgid "Employee"
msgstr ""

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_equipment
msgid "Equipment"
msgstr ""

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_request
msgid "Maintenance Requests"
msgstr ""

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "My Requests"
msgstr ""

#. module: hr_maintenance
#: selection:maintenance.equipment,equipment_assign_to:0
msgid "Other"
msgstr "Ander"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment_equipment_assign_to
msgid "Used By"
msgstr ""
