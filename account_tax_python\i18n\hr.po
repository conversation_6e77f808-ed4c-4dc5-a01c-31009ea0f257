# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <PERSON> <oluji<PERSON>.vlad<PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: Bo<PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"    - Grupa poreza: Šprez se računa iz podređenih poreza.\n"
"    - Fiksno: Iznos poreza je uvijek isti bez obzira na cijenu.\n"
"    - Postotak cijene: Iznos poreza je postotak cijene:\n"
"        npr. 100 * (1 + 10%) = 110 (porez nije uključen u cijeni)\n"
"        npr. 110 / (1 + 10%) = 100 (porez je uključen u cijenu)\n"
"    - Postotak cijene sa uključenim porezom: porez je iznos dijeljenja postotkom:\n"
"        npr. 180 / (1 - 10%) = 200 (porez nije uključen u cijenu)\n"
"        npr. 200 * (1 - 10%) = 180 (porez je uključen u cijenu)\n"
"        "

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_applicable
msgid "Applicable Code"
msgstr "Primjenjiva šifra"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Izračun iznosa poreza korištenjem varijable 'result'.\n"
"\n"
":parametar base_amount: decimalni broj, iznos osnovice na koju se primjenjuje porez\n"
":parametar price_unit: decimalni broj\n"
":parametar quantity: decimalni broj\n"
":parametar company: res.company zapis tablice\n"
":parametar product: product.product zapis tablice ili  None\n"
":parametar partner: res.partner zapis tablice ili None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Izračun poreza korištenjem varijable 'result'.\n"
"\n"
":parametar base_amount: decimalni broj, iznos osnovice na koju se primjenjuje porez\n"
":parametar price_unit: decimalni broj\n"
":parametar quantity: decimalni broj\n"
":parametar product: product.product zaps tablice ili None\n"
":parametar partner: res.partner zapis tablice ili None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Odlučuje hoće li se porez primjeniti postavljanjem varijable 'result' na True ili False.\n"
"\n"
":parametar price_unit: decimalni broj\n"
":parametar: quantity: decimalni broj\n"
":parametar company: res.company zapis tablice\n"
":parametar product: product.product zapis tablice ili None\n"
":parametar partner: res.partner zapis tablice ili None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Odlučuje hoće li se porez primjeniti postavljanjem varijable 'resutl' na True ili False.\n"
"\n"
":parametar price_unit: decimalni broj\n"
":parametar quantity: decimalni broj\n"
":parametar product: product.product zapis tablice ili None\n"
":parametar partner: res.partner zapis tablice ili None"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax_template__amount_type__code
msgid "Python Code"
msgstr "Python kod"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Porez"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Izračun poreza"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Predlošci poreza"
