<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
    <style>
        @keyframes rotate {
            from {transform: rotate(0deg);}
            to   {transform: rotate(360deg);}
        }
        #lines path {
            transform-box: fill-box;
            transform-origin: center;
        }
        #line_1 {animation: rotate 52s cubic-bezier(.56, .37, .43, .58) infinite;}
        #line_2 {animation: rotate 54s cubic-bezier(.56, .37, .43, .58) infinite;}
        #line_3 {animation: rotate 56s cubic-bezier(.56, .37, .43, .58) infinite;}
        #line_4 {animation: rotate 58s cubic-bezier(.56, .37, .43, .58) infinite;}
        #line_5 {animation: rotate 60s cubic-bezier(.56, .37, .43, .58) infinite;}
    </style>
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M.7461.2174c.0794.0405.1403.1538.1271.2672c-.0133.1106-.1033.2213-.2171.2753c-.1138.054-.2515.054-.3521-.0027c-.1006-.0567-.1668-.17-.1774-.2807c-.0106-.1079.0344-.2132.1006-.251c.0662-.0378.1562-.0054.2488-.0081c.0953-.0027.1906-.0378.2701 0z">
            <animate dur="60s" repeatCount="indefinite" attributeName="d" attributeType="XML"
            values="
            M.7461.2174c.0794.0405.1403.1538.1271.2672c-.0133.1106-.1033.2213-.2171.2753c-.1138.054-.2515.054-.3521-.0027c-.1006-.0567-.1668-.17-.1774-.2807c-.0106-.1079.0344-.2132.1006-.251c.0662-.0378.1562-.0054.2488-.0081c.0953-.0027.1906-.0378.2701 0z;
            M.8462.216c.0583.047.0185.2117-.0344.3265c-.0557.1177-.1272.1853-.2067.2236c-.0795.0382-.1696.047-.2491.0118c-.0795-.0323-.1537-.1088-.1961-.2206c-.0425-.1118-.053-.253.008-.3029c.0557-.0441.1855.0059.3259-.0029c.1405-.0118.2942-.0824.3525-.0353z;
            M.7205.3445c.0955.0828.1825.1804.1459.2377c-.0365.0594-.1937.0806-.3285.1231c-.1348.0446-.2471.1103-.3144.0913c-.0702-.0191-.0955-.121-.0983-.208c-.0028-.0849.0112-.155.0618-.2293c.0505-.0743.1404-.1528.2358-.1592c.0983-.0043.2021.0616.2976.1444z;
            M.7461.2174c.0794.0405.1403.1538.1271.2672c-.0133.1106-.1033.2213-.2171.2753c-.1138.054-.2515.054-.3521-.0027c-.1006-.0567-.1668-.17-.1774-.2807c-.0106-.1079.0344-.2132.1006-.251c.0662-.0378.1562-.0054.2488-.0081c.0953-.0027.1906-.0378.2701 0z"
            calcMode="spline"
            keySplines=".56 .37 .43 .58;.56 .37 .43 .58;.56 .37 .43 .58"/>
        </path>
    </defs>
    <svg viewBox="0 0 28.35 28.35" preserveAspectRatio="none">
        <g id="lines" style="transform-box: fill-box">
            <path id="line_1" d="M23.69,14.17c0,1.45-3.35,2-4,3.17s.47,4.38-.74,5.08-3.31-1.9-4.76-1.9-3.59,2.57-4.76,1.9,0-3.87-.73-5.08-4-1.72-4-3.17,3.35-2,4-3.17-.47-4.37.73-5.07,3.32,1.9,4.76,1.9,3.6-2.58,4.76-1.9,0,3.87.74,5.07S23.69,12.73,23.69,14.17Z" fill="none" stroke="#7C6576" stroke-miterlimit="10" stroke-width="0.25"/>
            <path id="line_2" d="M24.8,14.17c0,1.62-3.73,2.24-4.49,3.55s.53,4.88-.82,5.66-3.7-2.12-5.32-2.12-4,2.87-5.31,2.12,0-4.32-.83-5.66-4.49-1.93-4.49-3.55S7.28,11.93,8,10.63,7.51,5.74,8.86,5s3.7,2.12,5.31,2.12,4-2.88,5.32-2.12,0,4.32.82,5.66S24.8,12.56,24.8,14.17Z" fill="none" stroke="#7C6576" stroke-miterlimit="10" stroke-width="0.25" opacity=".8"/>
            <path id="line_3" d="M25.92,14.17c0,1.79-4.13,2.48-5,3.92s.58,5.39-.9,6.25S16,22,14.17,22,9.74,25.18,8.3,24.34s-.05-4.77-.91-6.25-5-2.13-5-3.92,4.13-2.47,5-3.91S6.81,4.86,8.3,4s4.09,2.35,5.87,2.35S18.61,3.17,20.05,4s0,4.78.9,6.26S25.92,12.39,25.92,14.17Z" fill="none" stroke="#7C6576" stroke-miterlimit="10" stroke-width="0.25" opacity=".6"/>
            <path id="line_4" d="M27,14.17c0,2-4.52,2.71-5.43,4.29s.63,5.91-1,6.85-4.48-2.57-6.43-2.57-4.85,3.48-6.43,2.57,0-5.23-1-6.85-5.43-2.34-5.43-4.29,4.52-2.7,5.43-4.28S6.12,4,7.74,3s4.48,2.57,6.43,2.57S19,2.13,20.6,3s.06,5.22,1,6.85S27,12.22,27,14.17Z" fill="none" stroke="#7C6576" stroke-miterlimit="10" stroke-width="0.25" opacity=".4"/>
            <path id="line_5" d="M28.14,14.17c0,2.12-4.91,2.94-5.9,4.66s.69,6.42-1.08,7.44-4.87-2.79-7-2.79-5.27,3.78-7,2.79-.06-5.68-1.08-7.44S.21,16.29.21,14.17s4.91-2.94,5.9-4.65S5.42,3.1,7.19,2.08s4.86,2.78,7,2.78,5.28-3.77,7-2.78.06,5.67,1.08,7.44S28.14,12.05,28.14,14.17Z" fill="none" stroke="#7C6576" stroke-miterlimit="10" stroke-width="0.25" opacity=".2"/>
        </g>
    </svg>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
