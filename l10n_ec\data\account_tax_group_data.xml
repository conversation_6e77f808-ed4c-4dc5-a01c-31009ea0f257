<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="tax_group_vat_12" model="account.tax.group">
            <field name="name">VAT 12%</field>
            <field name="l10n_ec_type">vat12</field>
            <field name="sequence">10</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_vat14" model="account.tax.group">
            <field name="name">VAT 14%</field>
            <field name="l10n_ec_type">vat14</field>
            <field name="sequence">20</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_vat0" model="account.tax.group">
            <field name="name">VAT 0%</field>
            <field name="l10n_ec_type">zero_vat</field>
            <field name="sequence">30</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_vat_not_charged" model="account.tax.group">
            <field name="name">VAT Not Charged</field>
            <field name="l10n_ec_type">not_charged_vat</field>
            <field name="sequence">40</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_vat_excempt" model="account.tax.group">
            <field name="name">VAT Excempt</field>
            <field name="l10n_ec_type">exempt_vat</field>
            <field name="sequence">50</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_ice" model="account.tax.group">
            <field name="name">Special Consumptions (ICE)</field>
            <field name="l10n_ec_type">ice</field>
            <field name="sequence">60</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_irbpnr" model="account.tax.group">
            <field name="name">Plastic Bottles (IRBPNR)</field>
            <field name="l10n_ec_type">irbpnr</field>
            <field name="sequence">70</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_withhold_vat" model="account.tax.group">
            <field name="name">VAT Withhold</field>
            <field name="l10n_ec_type">withhold_vat</field>
            <field name="sequence">80</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_withhold_income" model="account.tax.group">
            <field name="name">Profit Withhold</field>
            <field name="l10n_ec_type">withhold_income_tax</field>
            <field name="sequence">90</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_outflows" model="account.tax.group">
            <field name="name">Exchange Outflows</field>
            <field name="l10n_ec_type">outflows_tax</field>
            <field name="sequence">100</field>
            <field name="country_id" ref="base.ec"/>
        </record>

        <record id="tax_group_others" model="account.tax.group">
            <field name="name">Others</field>
            <field name="l10n_ec_type">other</field>
            <field name="sequence">110</field>
            <field name="country_id" ref="base.ec"/>
        </record>

    </data>
</odoo>
