.pos .close-pos-popup {
    max-width: 800px !important;
    max-height: 800px;
}

.pos .close-pos-popup .body{
    margin: 0;
    padding: 15px 50px;
}

.pos .close-pos-popup .session-overview {
    display: flex;
    margin-bottom: 30px;
}

.pos .close-pos-popup .overview-info {
    width: 50%;
    display: flex;
}

.pos .close-pos-popup .overview-info .info-value {
    margin-left: 50px;
}

.pos .close-pos-popup .overview-info span {
    margin-bottom: 10px;
}

.pos .close-pos-popup .column-left {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.pos .close-pos-popup .session-overview .opening-note {
    margin-left: 70px;
    color: darkgrey;
    text-align: left;
    font-size: 16px;
    width: 250px;
    max-height: 90px;
    padding-left: 10px;
    border-left: solid 3px darkgrey;
    overflow-y: auto;
    word-break: break-word;
}

.pos .close-pos-popup .payment-methods-overview {
    max-height: 320px;
    overflow: auto;
}

.pos .close-pos-popup .payment-methods-overview table {
    width: 100%;
    text-align: left;
}

.pos .close-pos-popup .payment-methods-overview table tr {
    height: 35px
}

.pos .close-pos-popup .flex {
    display: flex;
}

.pos .close-pos-popup .payment-methods-overview table .pos-input {
    width: 85px;
}

.pos .close-pos-popup .payment-methods-overview table .warning {
    color: red;
    font-weight: bold;
}

.pos .close-pos-popup .body .button.icon {
    margin: 0;
    float: unset;
}

.pos .close-pos-popup .payment-methods-overview table .cash-sign {
    width: 10px;
    margin-right: 5px;
}

.pos .close-pos-popup .payment-methods-overview .cash-overview {
    border-left: solid 2px #555555;
}

.pos .close-pos-popup .payment-methods-overview .cash-overview tr td:first-child {
    padding-left: 10px;
}

.pos .close-pos-popup .closing-notes {
    font-style: italic;
    font-weight: 350;
    width: calc(100% - 20px); /* textarea has a padding of 10px */
    line-height: 20px;
    resize: none;
    height: 70px;
    margin: 20px 0;
}

.pos .close-pos-popup .accept-closing {
    text-align: left;
}

.pos .close-pos-popup .invisible {
    visibility: hidden;
}

.pos .close-pos-popup .accept-closing label, #accept {
    cursor: pointer;
}

.pos .close-pos-popup .accept-closing label.disabled {
    cursor: default;
    color: grey;
}

.pos .close-pos-popup #accept {
    all: revert;
    margin-right: 10px;
}

.pos .close-pos-popup .accept-closing .warning {
    color: red;
    font-style: italic;
}

@media screen and (max-width: 768px) {
    .pos .close-pos-popup {
        overflow-y: auto;
    }

    .pos .close-pos-popup .footer {
        display: flex;
        flex-direction: column;
        height: unset;
    }

    .pos .close-pos-popup .footer .button {
        width: 90% !important;
        margin-left: auto;
        margin-right: auto;
    }

    .pos .close-pos-popup .footer .button:last-of-type {
        margin-bottom: 10px;
    }
}

.pos .close-pos-popup .footer .button {
    width: 150px;
}

.pos .close-pos-popup .footer .button.disabled {
    cursor: default;
    background: bottom;
    color: unset;
    border: solid 1px rgba(60, 60, 60, 0.1);
}

.pos .close-pos-popup .footer .button.disabled:active {
    border: solid 1px rgba(60, 60, 60, 0.1);
}
