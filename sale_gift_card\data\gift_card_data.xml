<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- the product used to deduce on sale order -->
        <record id="gift_card.pay_with_gift_card_product" model="product.product">
            <field name="taxes_id" eval="False"/>
            <field name="supplier_taxes_id" eval="False"/>
        </record>

        <record id="gift_card.gift_card_product_50" model="product.product">
            <field name="taxes_id" eval="False"/>
        </record>
    </data>
</odoo>
