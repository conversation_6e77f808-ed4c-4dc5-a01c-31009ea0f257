<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="ControlButtonPopup" owl="1">
        <div role="dialog" class="modal-dialog">
            <Draggable>
                <div class="popup popup-control-buttons">
                    <div class="control-buttons">
                        <t t-foreach="controlButtons" t-as="cb" t-key="cb.name">
                            <t t-component="cb.component" t-key="cb.name"/>
                        </t>
                    </div>
                    <footer class="footer">
                        <div class="button cancel" t-on-click="cancel">
                            <t t-esc="props.cancelText" />
                        </div>
                    </footer>
                </div>
            </Draggable>
        </div>
    </t>

</templates>
