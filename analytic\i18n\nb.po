# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* analytic
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: <PERSON>l (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction
msgid "Action Needed"
msgstr "Handling påkrevd"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__active
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active
msgid "Active"
msgstr "Aktiv"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_account_analytic_account_form
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Add a new analytic account"
msgstr "Opprett en ny analytisk konto"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_tag_action
msgid "Add a new tag"
msgstr "Opprett en ny etikett"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr "Beløp"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__account_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Analytic Account"
msgstr "Analytisk konto"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_distribution
msgid "Analytic Account Distribution"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_group_action
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_tree_view
msgid "Analytic Account Groups"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_res_config_settings__group_analytic_accounting
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr "Analytisk regnskap"

#. module: analytic
#: model:res.groups,name:analytic.group_analytic_tags
msgid "Analytic Accounting Tags"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__analytic_distribution_ids
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr "Analytiske konti"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_group
msgid "Analytic Categories"
msgstr "Analytiske kategorier"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active_analytic_distribution
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
msgid "Analytic Distribution"
msgstr "Analytisk distribusjon"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Entries"
msgstr "Analytiske poster"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Entry"
msgstr "Analytiske registrering"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
msgid "Analytic Items"
msgstr "Analytiske registreringer"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytisk linje"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__line_ids
msgid "Analytic Lines"
msgstr "Analytiske linjer"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__name
msgid "Analytic Tag"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_tag_action
#: model:ir.model,name:analytic.model_account_analytic_tag
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_tree_view
msgid "Analytic Tags"
msgstr "Analytiske etiketter"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr "Arkivert"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr "Samarbeidspartner"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_attachment_count
msgid "Attachment Count"
msgstr "Antall vedlegg"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__balance
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Balance"
msgstr "Balanse"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr "Balanse:"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__category
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Category"
msgstr "Kategori"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr "Analytiske konti"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__children_ids
msgid "Childrens"
msgstr "Barn"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "Click to add a new analytic account group."
msgstr "Klikk for å opprette en analtysk kontogruppe"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__color
msgid "Color Index"
msgstr "Fargeindeks"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__company_id
msgid "Company"
msgstr "Firma"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__complete_name
msgid "Complete Name"
msgstr "Fullstendig navn"

#. module: analytic
#: model:ir.model,name:analytic.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Konvertering mellom enheter fungerer kun når de tilhører samme kategori. "
"Konverteringen gjøres basert på forholdet mellom enhetene."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__credit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Credit"
msgstr "Kreditt"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__currency_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__partner_id
msgid "Customer"
msgstr "Kunde"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__date
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Date"
msgstr "Dato"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__debit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Debit"
msgstr "Debit"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__description
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__name
msgid "Description"
msgstr "Beskrivelse"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Gross Margin"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__group_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__group_id
msgid "Group"
msgstr "Gruppe"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr "Grupper etter..."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__has_message
msgid "Has Message"
msgstr "Har melding"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__id
msgid "ID"
msgstr "ID"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread
msgid "If checked, new messages require your attention."
msgstr "Hvis haket av, vil nye meldinger kreve din oppmerksomhet."

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis haket av, har enkelte meldinger leveringsfeil."

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__active
msgid ""
"If the active field is set to False, it will allow you to hide the account "
"without removing it."
msgstr ""
"Dersom det aktive feltet er satt til False, vil det tillate deg å skjule "
"kontoen uten å fjerne det."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hovedvedlegg"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error
msgid "Message Delivery error"
msgstr "Melding ved leveringsfeil"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_ids
msgid "Messages"
msgstr "Meldinger"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__name
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Name"
msgstr "Navn"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of Actions"
msgstr "Antall handlinger"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of errors"
msgstr "Antall feil"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antall meldinger som krever handling"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antall meldinger med leveringsfeil"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread_counter
msgid "Number of unread messages"
msgstr "Antall uleste meldinger"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_line__category__other
msgid "Other"
msgstr "Annen"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_id
msgid "Parent"
msgstr "Overordnet"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_path
msgid "Parent Path"
msgstr "Overordnet sti"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__tag_id
msgid "Parent tag"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "Partner"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__percentage
msgid "Percentage"
msgstr "Prosent"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "Antall"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__code
msgid "Reference"
msgstr "Referanse"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr "Søk analytiske linjer"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
msgid "Search Analytic Tags"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_tag__active
msgid "Set active to false to hide the Analytic Tag without removing it."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__tag_ids
msgid "Tags"
msgstr "Etiketter"

#. module: analytic
#: model:ir.model.constraint,message:analytic.constraint_account_analytic_distribution_check_percentage
msgid ""
"The percentage of an analytic distribution should be between 0 and 100."
msgstr ""

#. module: analytic
#: code:addons/analytic/models/analytic_account.py:0
#, python-format
msgid ""
"The selected account belongs to another company than the one you're trying "
"to create an analytic item for"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "This allows you to classify your analytic accounts."
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "Total"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_id
msgid "Unit of Measure"
msgstr "Enhet"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread
msgid "Unread Messages"
msgstr "Uleste meldinger"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Antall uleste meldinger"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_category_id
msgid "UoM Category"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__user_id
msgid "User"
msgstr "Bruker"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr "for eksempel Prosjekt XYZ"
