# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_in
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-06 14:12+0000\n"
"PO-Revision-Date: 2023-01-06 14:12+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "<strong>Total (In Words): </strong>"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_l10n_in_account_invoice_report
msgid "Account Invoice Statistics"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__account_move_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__account_move_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__account_move_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__account_move_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__account_move_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__account_move_id
msgid "Account Move"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__date
msgid "Accounting Date"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_l10n_in_advances_payment_adjustment_report
msgid "Advances Payment Adjustment Analysis"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_l10n_in_advances_payment_report
msgid "Advances Payment Analysis"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__amount
msgid "Amount"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__b2b_type
msgid "B2B Invoice Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__b2cl_is_ecommerce
msgid "B2CL Is E-commerce"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__b2cs_is_ecommerce
msgid "B2CS Is E-commerce"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.invoice_form_inherit_l10n_in
msgid "Bill of Entry Date"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.invoice_form_inherit_l10n_in
msgid "Bill of Entry Number"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__cess_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__cess_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__cess_amount
msgid "CESS amount"
msgstr ""

#. module: l10n_in
#: model:account.tax.group,name:l10n_in.cgst_group
msgid "CGST"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__cgst_amount
msgid "CGST Amount"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__cgst_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__cgst_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__cgst_amount
msgid "CGST amount"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Cancelled"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__cgst_amount
msgid "Central Tax Amount"
msgstr ""

#. module: l10n_in
#: model:account.tax.group,name:l10n_in.cess_group
msgid "Cess"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__cess_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__cess_amount
msgid "Cess Amount"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__company_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__company_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__company_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__company_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__company_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__company_id
msgid "Company"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_l10n_in_advances_payment_adjustment_report__company_id
#: model:ir.model.fields,help:l10n_in.field_l10n_in_advances_payment_report__company_id
#: model:ir.model.fields,help:l10n_in.field_l10n_in_payment_report__company_id
msgid "Company related to this journal"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__consumer
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__l10n_in_gst_treatment__consumer
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__consumer
msgid "Consumer"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_res_country_state
msgid "Country state"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__create_date
msgid "Created on"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Credit Note"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__currency_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__currency_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__partner_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__partner_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__partner_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__partner_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__partner_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__partner_id
msgid "Customer"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__move_type__out_refund
msgid "Customer Credit Note"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__partner_vat
msgid "Customer GSTIN"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__move_type__out_invoice
msgid "Customer Invoice"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__date
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__date
msgid "Date"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__deemed_export
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__l10n_in_gst_treatment__deemed_export
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__deemed_export
msgid "Deemed Export"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Destination of supply:"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__display_name
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__display_name
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__display_name
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__display_name
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__display_name
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__refund_invoice_type
msgid "Document Type"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Draft"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__ecommerce_partner_id
msgid "E-commerce"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__ecommerce_vat
msgid "E-commerce GSTIN"
msgstr ""

#. module: l10n_in
#: model:account.tax.group,name:l10n_in.exempt_group
msgid "Exempt"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__exempted_amount
msgid "Exempted"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_l10n_in_exempted_report
msgid "Exempted Gst Supplied Statistics"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.invoice_form_inherit_l10n_in
msgid "Export India"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__export_type
msgid "Export Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__gst_format_date
msgid "Formated Date"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__gst_format_refund_date
msgid "Formated Refund Date"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__gst_format_shipping_bill_date
msgid "Formated Shipping Bill Date"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_l10n_in_account_invoice_report__reversed_entry_id
msgid "From where this Refund is created"
msgstr ""

#. module: l10n_in
#: model:account.tax.group,name:l10n_in.gst_group
msgid "GST"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_gst_treatment
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_gst_treatment
#: model:ir.model.fields,field_description:l10n_in.field_account_payment__l10n_in_gst_treatment
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__l10n_in_gst_treatment
#: model:ir.model.fields,field_description:l10n_in.field_res_partner__l10n_in_gst_treatment
#: model:ir.model.fields,field_description:l10n_in.field_res_users__l10n_in_gst_treatment
msgid "GST Treatment"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_gstin
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_gstin
#: model:ir.model.fields,field_description:l10n_in.field_account_payment__l10n_in_gstin
msgid "GSTIN"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_journal__l10n_in_gstin_partner_id
msgid "GSTIN Unit"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_account_journal__l10n_in_gstin_partner_id
msgid ""
"GSTIN related to this journal. If empty then consider as company GSTIN."
msgstr ""

#. module: l10n_in
#: code:addons/l10n_in/models/account_invoice.py:0
#, python-format
msgid "Go to Company configuration"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__gross_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__gross_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__gross_amount
msgid "Gross advance"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_search_view
msgid "Group By"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__hsn_code
msgid "HSN"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__hsn_description
msgid "HSN description"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "HSN/SAC"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_product_product__l10n_in_hsn_code
#: model:ir.model.fields,field_description:l10n_in.field_product_template__l10n_in_hsn_code
msgid "HSN/SAC Code"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_product_product__l10n_in_hsn_description
#: model:ir.model.fields,field_description:l10n_in.field_product_template__l10n_in_hsn_description
msgid "HSN/SAC Description"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_product_product__l10n_in_hsn_description
#: model:ir.model.fields,help:l10n_in.field_product_template__l10n_in_hsn_description
msgid "HSN/SAC description is required if HSN/SAC code is not provided."
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_product_product__l10n_in_hsn_code
#: model:ir.model.fields,help:l10n_in.field_product_template__l10n_in_hsn_code
msgid "Harmonized System Nomenclature/Services Accounting Code"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__id
msgid "ID"
msgstr ""

#. module: l10n_in
#: model:account.tax.group,name:l10n_in.igst_group
msgid "IGST"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__igst_amount
msgid "IGST Amount"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__igst_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__igst_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__igst_amount
msgid "IGST amount"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.invoice_form_inherit_l10n_in
msgid "Import India"
msgstr ""

#. module: l10n_in
#: model:ir.ui.menu,name:l10n_in.account_reports_in_statements_menu
msgid "India"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_form_view
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_search_view
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_tree_view
msgid "India Port Code"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_uom_uom__l10n_in_code
msgid "Indian GST UQC"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_l10n_in_payment_report
msgid "Indian accounting payment report"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_l10n_in_port_code
msgid "Indian port code"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__igst_amount
msgid "Integrated Tax Amount"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__name
msgid "Invoice Number"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__total
msgid "Invoice Total"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__in_supply_type
msgid "Inward Supply Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__is_ecommerce
msgid "Is E-commerce"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__is_pre_gst
msgid "Is Pre GST"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_journal
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__journal_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__journal_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__journal_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__journal_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__journal_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__journal_id
msgid "Journal"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_move
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__move_type__entry
msgid "Journal Entry"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report____last_update
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report____last_update
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report____last_update
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report____last_update
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code____last_update
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_state_id
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_state_id
#: model:ir.model.fields,field_description:l10n_in.field_account_payment__l10n_in_state_id
msgid "Location of supply"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_config_settings__group_l10n_in_reseller
#: model:res.groups,name:l10n_in.group_l10n_in_reseller
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Manage Reseller(E-Commerce)"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__move_type
msgid "Move Type"
msgstr ""

#. module: l10n_in
#: model:account.tax.group,name:l10n_in.nil_rated_group
msgid "Nil Rated"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__nil_rated_amount
msgid "Nil rated supplies"
msgstr ""

#. module: l10n_in
#: model:account.tax.group,name:l10n_in.non_gst_supplies_group
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__non_gst_supplies
msgid "Non GST Supplies"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_account_bank_statement_line__l10n_in_reseller_partner_id
#: model:ir.model.fields,help:l10n_in.field_account_move__l10n_in_reseller_partner_id
#: model:ir.model.fields,help:l10n_in.field_account_payment__l10n_in_reseller_partner_id
msgid "Only Registered Reseller"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_exempted_report__out_supply_type
msgid "Outward Supply Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__overseas
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__l10n_in_gst_treatment__overseas
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__overseas
msgid "Overseas"
msgstr ""

#. module: l10n_in
#: code:addons/l10n_in/models/account_invoice.py:0
#, python-format
msgid ""
"Partner %(partner_name)s (%(partner_id)s) GSTIN is required under GST "
"Treatment %(name)s"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__payment_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__payment_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__payment_id
msgid "Payment"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__payment_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__payment_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__payment_amount
msgid "Payment Amount"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__date
msgid "Payment Date"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__payment_type
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__payment_type
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__payment_type
msgid "Payment Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__place_of_supply
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__place_of_supply
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__place_of_supply
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__place_of_supply
msgid "Place of Supply"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Place of supply:"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__name
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_search_view
msgid "Port"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__code
msgid "Port Code"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_shipping_port_code_id
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_shipping_port_code_id
#: model:ir.model.fields,field_description:l10n_in.field_account_payment__l10n_in_shipping_port_code_id
msgid "Port code"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__state__posted
msgid "Posted"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__product_id
msgid "Product"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_l10n_in_product_hsn_report
msgid "Product HSN Statistics"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__quantity
msgid "Product Qty"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_product_template
msgid "Product Template"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__move_type__in_receipt
msgid "Purchase Receipt"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__tax_rate
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__tax_rate
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__tax_rate
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__tax_rate
msgid "Rate"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_advances_payment_adjustment_report__payment_type__inbound
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_advances_payment_report__payment_type__inbound
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_payment_report__payment_type__inbound
msgid "Receive Money"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__date
msgid "Reconcile Date"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__reconcile_amount
msgid "Reconcile amount in Payment month"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__reversed_entry_id
msgid "Refund Invoice"
msgstr ""

#. module: l10n_in
#: model:res.partner.category,name:l10n_in.res_partner_category_registered
msgid "Registered"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__composition
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__l10n_in_gst_treatment__composition
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__composition
msgid "Registered Business - Composition"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__regular
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__l10n_in_gst_treatment__regular
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__regular
msgid "Registered Business - Regular"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_reseller_partner_id
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_reseller_partner_id
#: model:ir.model.fields,field_description:l10n_in.field_account_payment__l10n_in_reseller_partner_id
#: model:res.partner.category,name:l10n_in.res_partner_category_reseller
msgid "Reseller"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__is_reverse_charge
msgid "Reverse Charge"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_tax__l10n_in_reverse_charge
#: model:ir.model.fields,field_description:l10n_in.field_account_tax_template__l10n_in_reverse_charge
msgid "Reverse charge"
msgstr ""

#. module: l10n_in
#: model:account.tax.group,name:l10n_in.sgst_group
msgid "SGST"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__sgst_amount
msgid "SGST Amount"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__sgst_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__sgst_amount
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__sgst_amount
msgid "SGST amount"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__move_type__out_receipt
msgid "Sales Receipt"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_advances_payment_adjustment_report__payment_type__outbound
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_advances_payment_report__payment_type__outbound
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_payment_report__payment_type__outbound
msgid "Send Money"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__shipping_bill_date
msgid "Shipping Bill Date"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__shipping_bill_number
msgid "Shipping Bill Number"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_shipping_bill_date
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_shipping_bill_date
#: model:ir.model.fields,field_description:l10n_in.field_account_payment__l10n_in_shipping_bill_date
msgid "Shipping bill date"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__l10n_in_shipping_bill_number
#: model:ir.model.fields,field_description:l10n_in.field_account_move__l10n_in_shipping_bill_number
#: model:ir.model.fields,field_description:l10n_in.field_account_payment__l10n_in_shipping_bill_number
msgid "Shipping bill number"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__shipping_port_code_id
msgid "Shipping port code"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__special_economic_zone
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__l10n_in_gst_treatment__special_economic_zone
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__special_economic_zone
msgid "Special Economic Zone"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_port_code__state_id
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_port_code_search_view
msgid "State"
msgstr ""

#. module: l10n_in
#: code:addons/l10n_in/models/account_invoice.py:0
#, python-format
msgid ""
"State is missing from address in '%s'. First set state after post this "
"invoice again."
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__sgst_amount
msgid "State/UT Tax Amount"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__state
msgid "Status"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__supply_type
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__supply_type
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__supply_type
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__supply_type
msgid "Supply Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_res_country_state__l10n_in_tin
msgid "TIN Number"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_res_country_state__l10n_in_tin
msgid "TIN number-first two digits"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_tax
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__tax_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_adjustment_report__l10n_in_tax_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_advances_payment_report__l10n_in_tax_id
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_payment_report__l10n_in_tax_id
msgid "Tax"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__price_total
msgid "Taxable Value"
msgstr ""

#. module: l10n_in
#: model:ir.model,name:l10n_in.model_account_tax_template
msgid "Templates for Taxes"
msgstr ""

#. module: l10n_in
#: model:ir.model.constraint,message:l10n_in.constraint_l10n_in_port_code_code_uniq
msgid "The Port Code must be unique!"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_account_tax__l10n_in_reverse_charge
#: model:ir.model.fields,help:l10n_in.field_account_tax_template__l10n_in_reverse_charge
msgid "Tick this if this tax is reverse charge. Only for Indian accounting"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_account_bank_statement_line__amount_total_words
#: model:ir.model.fields,field_description:l10n_in.field_account_move__amount_total_words
#: model:ir.model.fields,field_description:l10n_in.field_account_payment__amount_total_words
msgid "Total (In Words)"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__total
msgid "Total Value"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__price_total
msgid "Total Without Tax"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__uom_id
msgid "UOM"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_product_hsn_report__l10n_in_uom_code
msgid "UQC"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,field_description:l10n_in.field_l10n_in_account_invoice_report__refund_export_type
msgid "UR Type"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields,help:l10n_in.field_uom_uom__l10n_in_code
msgid "Unique Quantity Code (UQC) under GST"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__state__draft
msgid "Unposted"
msgstr ""

#. module: l10n_in
#: model:res.partner.category,name:l10n_in.res_partner_category_unregistered
msgid "Unregistered"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__account_move__l10n_in_gst_treatment__unregistered
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__l10n_in_gst_treatment__unregistered
#: model:ir.model.fields.selection,name:l10n_in.selection__res_partner__l10n_in_gst_treatment__unregistered
msgid "Unregistered Business"
msgstr ""

#. module: l10n_in
#: model_terms:ir.ui.view,arch_db:l10n_in.res_config_settings_view_form_inherit_l10n_in
msgid "Use this if setup with Reseller(E-Commerce)."
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__move_type__in_invoice
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Vendor Bill"
msgstr ""

#. module: l10n_in
#: model:ir.model.fields.selection,name:l10n_in.selection__l10n_in_account_invoice_report__move_type__in_refund
#: model_terms:ir.ui.view,arch_db:l10n_in.l10n_in_report_invoice_document_inherit
msgid "Vendor Credit Note"
msgstr ""

#. module: l10n_in
#: code:addons/l10n_in/models/account_invoice.py:0
#, python-format
msgid ""
"Your company %s needs to have a correct address in order to validate this invoice.\n"
"Set the address of your company (Don't forget the State field)"
msgstr ""
