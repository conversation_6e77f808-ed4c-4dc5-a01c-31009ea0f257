<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Stock rules -->
        <record id="view_stock_rule_form_stock_inherit_purchase_stock" model="ir.ui.view">
            <field name="name">stock.rule.form.stock.inherit.purchase_stock</field>
            <field name="model">stock.rule</field>
            <field name="inherit_id" ref="stock.view_stock_rule_form"/>
            <field name="arch" type="xml">
                <field name="location_src_id" position="attributes">
                    <attribute name="attrs">{'required': [('action', 'in', ['pull', 'push', 'pull_push'])], 'invisible': [('action', '=', 'buy')]}</attribute>
                </field>
            </field>
        </record>

    </data>
</odoo>
