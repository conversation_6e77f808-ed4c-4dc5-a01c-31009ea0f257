<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>

        <record id="id_tax03" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">VAT 15% rated sales</field>
            <field name="description">tax03</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_vat_15"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_sale_vat_rated_15')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et3007'),
                    'minus_report_line_ids': [ref('account_tax_report_net_sale_vat_15')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_sale_vat_rated_15')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et3007'),
                    'plus_report_line_ids': [ref('account_tax_report_net_sale_vat_15')],
                }),
            ]"/>
        </record>

        <record id="id_tax04" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">VAT 0% rated sales</field>
            <field name="description">tax04</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_vat_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_sale_vat_rated_0')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_sale_vat_rated_0')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="id_tax06" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">VAT Exempt rated sales</field>
            <field name="description">tax06</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_vat_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_sale_vat_exmpt')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_sale_vat_exmpt')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="id_tax11" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">VAT Out of Scope rated sales</field>
            <field name="description">tax11</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_vat_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_sale_vat_out_scope')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_sale_vat_out_scope')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="id_tax02" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">Withholding 2% rated sales</field>
            <field name="amount">-2</field>
            <field name="amount_type">percent</field>
            <field name="description">tax02</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_withh_2"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_sale_withholding_2')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et3006'),
                    'minus_report_line_ids': [ref('account_tax_report_net_sale_witheld_2')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_sale_withholding_2')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et3006'),
                    'plus_report_line_ids': [ref('account_tax_report_net_sale_witheld_2')],
                }),
            ]"/>
        </record>

        <record id="id_tax13" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">Withholding 35% rated sales</field>
            <field name="description">tax13</field>
            <field name="amount">-35</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_withh_35"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_sale_withholding_35')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et3006'),
                    'plus_report_line_ids': [ref('account_tax_report_net_sale_witheld_35')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_sale_withholding_35')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et3006'),
                    'minus_report_line_ids': [ref('account_tax_report_net_sale_witheld_35')],
                }),
            ]"/>
        </record>

        <record id="id_tax14" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">Withholding VAT 15% rated sales</field>
            <field name="description">tax14</field>
            <field name="amount">-15</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_withh_15"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_sale_vat_withholding')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et3006'),
                    'plus_report_line_ids': [ref('account_tax_report_net_sale_vat_witheld')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_sale_vat_withholding')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et3006'),
                    'minus_report_line_ids': [ref('account_tax_report_net_sale_vat_witheld')],
                }),
            ]"/>
        </record>

        <record id="id_tax08" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">VAT 15% rated purchases</field>
            <field name="description">tax08</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_vat_15"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_purch_vt_ratd_15')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et2212'),
                    'plus_report_line_ids': [ref('account_tax_report_purch_vat_rated_15')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_purch_vt_ratd_15')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et2212'),
                    'minus_report_line_ids': [ref('account_tax_report_purch_vat_rated_15')],
                }),
            ]"/>
        </record>

        <record id="id_tax07" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">VAT 0% rated purchases</field>
            <field name="description">tax07</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_vat_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_purch_vt_ratd_0')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_purch_vt_ratd_0')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="id_tax10" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">VAT Exempt rated purchases</field>
            <field name="description">tax10</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_vat_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_purch_vt_exmpt')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_purch_vt_exmpt')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="id_tax09" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">VAT Out of Scope rated purchases</field>
            <field name="description">tax09</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_vat_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_purch_vt_out_scope')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_purch_vt_out_scope')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="id_tax05" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">Withholding 2% rated purchases</field>
            <field name="description">tax05</field>
            <field name="amount">-2</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_withh_2"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_purch_withholding_2')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et2213'),
                    'plus_report_line_ids': [ref('account_tax_report_net_purch_witholding_2')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_purch_withholding_2')],
                }),
                    (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et2213'),
                    'minus_report_line_ids': [ref('account_tax_report_net_purch_witholding_2')],
                }),
            ]"/>
        </record>

        <record id="id_tax12" model="account.tax.template">
            <field name="chart_template_id" ref="l10n_et"/>
            <field name="name">Withholding 35% rated purchases</field>
            <field name="description">tax12</field>
            <field name="amount">-35</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_withh_35"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_purch_withholding_35')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et2213'),
                    'plus_report_line_ids': [ref('account_tax_report_net_purch_witholding_35')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_purch_withholding_35')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_et2213'),
                    'minus_report_line_ids': [ref('account_tax_report_net_purch_witholding_35')],
                }),
            ]"/>
        </record>

    </data>
</odoo>
