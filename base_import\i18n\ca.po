# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# <PERSON><PERSON><PERSON>, 2021
# jabe<PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Harcogourmet, 2022
# oscaryuu, 2022
# CristianCruzParra, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: CristianCruzParra, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "%d records successfully imported"
msgstr "%dregistres importats correctament "

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect"
msgstr ""
"Només s'ha trobat una columna en l'arxiu. Això significa sovint que el "
"separador d'arxius és incorrecte."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Additional Fields"
msgstr "Camps addicionals"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Advanced"
msgstr "Avançat"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Allow matching with subfields"
msgstr "Permet la concordança amb subcamps"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"S'ha produït un problema desconegut durant la importació (possiblement s'ha "
"perdut la connexió, s'ha superat el límit de dades o s'han excedit els "
"límits de memòria). Torni a intentar-ho en cas que el problema sigui "
"transitori. Si el problema persisteix, intenti dividir l'arxiu en lloc "
"d'importar-lo d'una vegada."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "Base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "Import base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "Mapes d'importació base"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Batch Import"
msgstr "Importació per lots"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Batch limit"
msgstr "Límit de lots"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Cancel"
msgstr "Cancel·lar"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Click 'Resume' to proceed with the import, resuming at line"
msgstr "Feu clic a \"Reprèn\" per continuar amb la importació, reprèn a la línia"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values (value: %s)"
msgstr "Columna %s conté valors incorrectes (valor: %s)"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values. Error in line %d: %s"
msgstr "Columna %s conté valors incorrectes. Error en la línia %d: %s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Nom de la columna"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Comma"
msgstr "Coma"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Comments"
msgstr "Comentaris"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""
"No pot recuperar-se la URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Create new values"
msgstr "Crear nous valors"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__create_date
msgid "Created on"
msgstr "Creat el"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__currency_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Database ID"
msgstr "Id de base de dades"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Date Format:"
msgstr "Format de data:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Datetime Format:"
msgstr "Format de data i hora:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Decimal Separator:"
msgstr "Separador de decimals:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Dot"
msgstr "Punt"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Download"
msgstr "Descarregar"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Download Template"
msgstr "Descarrega la plantilla"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__dt
msgid "Dt"
msgstr "Dt"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Encoding:"
msgstr "Codificació:"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Error Parsing Date [%s:L%d]: %s"
msgstr "Error en analitzar la data [%s:L%d]: %s"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Estimated time left:"
msgstr "Temps estimat restant:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Everything seems valid."
msgstr "Tot sembla correcte."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Excel files are recommended as formatting is automatic."
msgstr "Es recomanen fitxers Excel ja que el format és automàtic."

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "External ID"
msgstr "ID externa "

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Nom de camp"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "Fitxer"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "File Column"
msgstr "Columna del fitxer"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Nom del fitxer"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Tipus d'arxiu"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "La grandària de l'arxiu supera el màxim configurat (%s bytes)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr "Arxiu a comprovar i/o importar, binari en brut (no base64)"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Finalizing current batch before interrupting..."
msgstr "Finalitzant el lot actual abans d'interrompre..."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "For CSV files, you may need to select the correct separator."
msgstr ""
"Per a arxius CSV, és possible que hàgiu de seleccionar el separador "
"correcte."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Formatting"
msgstr "Format"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"Trobades dades d'imatge no vàlides, les imatges s'han d'importar com a URL o"
" dades codificades en base64."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Go to Import FAQ"
msgstr "Anar al FAQ d'importació"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Help"
msgstr "Ajuda"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Here is the start of the file we could not import:"
msgstr "Aquest és el començament de l'arxiu que no s'ha pogut importar:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"Si el fitxer conté\n"
"                    els noms de les columnes, Odoo pot provar de detectar automàticament el\n"
"                    camp corresponent a la columna. Això fa importacions\n"
"                    més senzill sobretot quan el fitxer té moltes columnes."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"Si el model utilitza OpenChatter, el registre de l'historial establirà "
"subscripcions i enviarà notificacions durant la importació, cosa que farà "
"més lenta la importació."

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"La mida de la imatge és excessiva, les imatges penjades han de ser inferiors"
" a 42 milions de píxels."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Import"
msgstr "Importa"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Import FAQ"
msgstr "Preguntes freqüents sobre la importació"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Import a File"
msgstr "Importar un arxiu"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Import file has no content or is corrupt"
msgstr "L'arxiu d'importació no té contingut o està corrupte"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Import preview failed due to:"
msgstr "La previsualització de la importació ha fallat a causa de:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/import_records/import_records.xml:0
#, python-format
msgid "Import records"
msgstr "Importar registres"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid ""
"Import timed out. Please retry. If you still encounter this issue, the file "
"may be too big for the system's configuration, try to split it (import less "
"records per file)."
msgstr ""
"S'ha esgotat el temps d'importació. Si us plau, torna-ho a provar. Si encara"
" us trobeu amb aquest problema, és possible que el fitxer sigui massa gran "
"per a la configuració del sistema, proveu de dividir-lo (importeu menys "
"registres per fitxer)."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Imported file"
msgstr "Fitxer importat"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Importing"
msgstr "Importació"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr ""
"El valor de la cel·la a la fila no és vàlid %(row)s,columna %(col)s: "
"%(cell_value)s"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Label"
msgstr "Etiqueta"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Load File"
msgstr "Carregar fitxer"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Loading file..."
msgstr "Carregant fitxer..."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
#, python-format
msgid "Model"
msgstr "Model"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Multiple errors occurred"
msgstr "S'han produït múltiples errors"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__name
#, python-format
msgid "Name"
msgstr "Nom"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Need Help?"
msgstr "Necessites ajuda?"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "No Separator"
msgstr "Sense separador"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "No matching records found for"
msgstr "No s'han trobat registres coincidents per a"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "No matching records found for the following"
msgstr "No s'han trobat registres coincidents per al següent"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Odoo Field"
msgstr "Camp Odoo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__othervalue
msgid "Other Variable"
msgstr "Altres variables"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__parent_id
msgid "Parent"
msgstr "Pare"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Prevent import"
msgstr "Evitar la importació"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Preview"
msgstr "Previsualitza"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Relation Fields"
msgstr "Camps de relació"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Res Model"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Resume"
msgstr "Continua"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Search for a field..."
msgstr "Busca un camp..."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "See possible values"
msgstr "Veure possibles valors"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Selected Sheet:"
msgstr "Full seleccionat:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Semicolon"
msgstr "Punt i coma"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Separator:"
msgstr "Separador:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set to:"
msgstr "Ajustat a:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set to: False"
msgstr "Ajustat a: Fals"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set to: True"
msgstr "Set to: Verdader"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set value as empty"
msgstr "Estableix el valor com a buit"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Sheet:"
msgstr "Full:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Skip record"
msgstr "Omet el registre"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__somevalue
msgid "Some Value"
msgstr "Algun valor"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Space"
msgstr "Espai"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Standard Fields"
msgstr "Camps estàndard"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Start at line"
msgstr "Comença a la línia"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Stop Import"
msgstr "Atura la importació"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Suggested Fields"
msgstr "Camps suggerits"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Tab"
msgstr "Tabulació"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Test"
msgstr "Test"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Testing"
msgstr "En proves"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_preview
msgid "Tests : Base Import Model Preview"
msgstr "Proves : Vista prèvia del model base d'importació"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char
msgid "Tests : Base Import Model, Character"
msgstr "Tests : Base Importa Model, Caràcter"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_noreadonly
msgid "Tests : Base Import Model, Character No readonly"
msgstr "Proves: model d'importació base, caràcters sense només lectura"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_readonly
msgid "Tests : Base Import Model, Character readonly"
msgstr "Proves: model d'importació base, només lectura de caràcters"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_required
msgid "Tests : Base Import Model, Character required"
msgstr "Proves: Model base d'importació, caràcter obligatori"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_states
msgid "Tests : Base Import Model, Character states"
msgstr "Proves: model d'importació base, estats de caràcters"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_stillreadonly
msgid "Tests : Base Import Model, Character still readonly"
msgstr "Proves: model d'importació base, caràcter encara només de lectura"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o
msgid "Tests : Base Import Model, Many to One"
msgstr "Test: Base importat del model, molts a un"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_related
msgid "Tests : Base Import Model, Many to One related"
msgstr "Proves: model d'importació base, relacionat amb molts a un"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required
msgid "Tests : Base Import Model, Many to One required"
msgstr "Proves: model d'importació base, calen molts a un"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required_related
msgid "Tests : Base Import Model, Many to One required related"
msgstr "Proves: model d'importació base, relacionat amb molts a un"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m
msgid "Tests : Base Import Model, One to Many"
msgstr "Test: Base importat del model, un a molts"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m_child
msgid "Tests : Base Import Model, One to Many child"
msgstr "Proves: Model base d'importació, un a molts nens"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_complex
msgid "Tests: Base Import Model Complex"
msgstr "Proves: Complex Model Base Import"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_float
msgid "Tests: Base Import Model Float"
msgstr "Proves: Base Import Model Float"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Text Delimiter:"
msgstr "Delimitador de text:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "The file contains blocking errors (see below)"
msgstr "El fitxer conté errors de bloqueig (vegeu més avall)"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "The file contains non-blocking warnings (see below)"
msgstr "El fitxer conté advertències que no bloquegen (vegeu més avall)"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "The file will be imported by batches"
msgstr "El fitxer s'importarà per lots"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "This column will be concatenated in field"
msgstr "Aquesta columna es concatenarà al camp"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "This file has been successfully imported up to line %d."
msgstr "Aquest fitxer s'ha importat correctament fins a la línia %d."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Thousands Separator:"
msgstr "Separador de milers:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "To import multiple values, separate them by a comma"
msgstr "Per a importar diversos valors, separi'ls amb una coma"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "To import, select a field..."
msgstr "Per importar, seleccioneu un camp..."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Track history during import"
msgstr "Registrar historial durant la importació"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Type"
msgstr "Tipus"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""
"No és possible carregar l'arxiu \"{extension}\": requereix el mòdul de "
"Python \"{modname}\""

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr "Format no suportat \"{}\", importar només arxius CSV, ODF, XLS i XLSX"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Untitled"
msgstr "Sense títol"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Upload File"
msgstr "Pujar fitxers"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Upload an Excel or CSV file to import"
msgstr "Carregueu un fitxer Excel o CSV per importar"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Use first row as header"
msgstr "Utilitzeu la primera fila com a capçalera"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "Users"
msgstr "Usuaris"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__value
msgid "Value"
msgstr "Valor"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__value2
msgid "Value2"
msgstr "Value2"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr ""
"Avís: ignora la línia d'etiquetes, les línies buides i les línies compostes "
"només per cel·les buides"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "When a value cannot be matched:"
msgstr "Quan no es pot fer coincidir un valor:"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"No podeu importar imatges a través del Enllaç, consulteu el motiu amb el "
"vostre administrador o assistència."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "You can test or reload your file before resuming the import."
msgstr ""
"Podeu provar o tornar a carregar el vostre fitxer abans de reprendre la "
"importació."

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "You must configure at least one field to import"
msgstr "Ha de configurar almenys un camp a importar"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "at multiple rows"
msgstr "en múltiples files"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "at row"
msgstr "a la fila"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "batch"
msgstr "lot"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "in field"
msgstr "al camp"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "minutes"
msgstr "minuts"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "more)"
msgstr "més)"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "out of"
msgstr "fora de"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "unknown error code %s"
msgstr "codi d'error desconegut %s"
