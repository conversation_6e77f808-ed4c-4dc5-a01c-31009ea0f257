# -*- coding:utf-8 -*-

from dateutil.relativedelta import relativedelta
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from datetime import date, datetime




class PurchaseSukukReportWizard(models.TransientModel):
    _name = "purchase.sukuk.wizard.report2"


    type = fields.Selection([('move','مشتريات'),('expense','مصروفات')], default='move', string='النوع')
    move_id = fields.Many2one('account.move', string='مشتريات', domain="[('move_type', '=', 'in_invoice')]")
    expense_id = fields.Many2one('hr.expense', string='مصروفات')

    move_partner_id = fields.Many2one('res.partner', related='move_id.partner_id', string='اسم المدفوع له')
    expense_partner_id = fields.Many2one('res.partner', string='اسم المدفوع له')

    amount = fields.Float(string='القيمة', compute='_get_amount')

    bank_id = fields.Many2one('res.bank', string='اسم المصرف',required=True)
    branch_id = fields.Many2one('bank.branch', string="أسم الفرع", domain="[('bank_id','=',bank_id)]",required=True)
    account_no_id = fields.Many2one('account.account', string="رقم الحساب",required=True)
    dialog_box = fields.Text()
    user_id = fields.Many2one('res.users', string='مقدم الطلب', default=lambda self: self.env.uid)
    sukuk_page_ids = fields.One2many('purchase.sukuk.wizard.page','sukuk_wizard_id')

    sukuk_computed = fields.Boolean()


    @api.onchange('type')
    def change_type(self):
        if self.type == 'move':
            self.expense_id = False
        if self.type == 'expense':
            self.move_id = False

    @api.depends('move_id','type','expense_id')
    def _get_amount(self):
        for elem in self:
            elem.amount = 0
            if elem.move_id:
                elem.amount = elem.move_id.amount_total
            if elem.expense_id:
                elem.amount = elem.expense_id.total_amount

    def get_available_suke(self):
        sukuk_books = self.env['sukuk.management.item'].search([('state', '=', 'confirmed'),
                                                                ('bank_id', '=', self.bank_id.id),
                                                                ('branch_id', '=', self.branch_id.id),
                                                                ('account_no_id','=',self.account_no_id.id)])
        for each_book in sukuk_books:
            serial_no_from = each_book.serial_no_from
            serial_no_to = each_book.serial_no_to
            while serial_no_from <= serial_no_to:
                sukuk_page = self.env['sukuk.management.page'].search([('sukuk_book_id','=',each_book.id),
                                                                       ('serial_no','=',serial_no_from)])
                if sukuk_page:
                    serial_no_from+=1
                else:
                    suke = {
                        'sukuk_book_id':each_book.id,
                        'state':'draft',
                        'bank_id':each_book.bank_id.id,
                        'branch_id':each_book.branch_id.id,
                        'partner_paid_to':self.move_partner_id.id or self.expense_partner_id.id,
                        'user_id':self.env.uid,
                        'person_signature':each_book.person_signature.id,
                        'suke_book_number':each_book.suke_book_number,
                        'serial_no':serial_no_from,
                        'time_stamp':str(fields.Date.to_string(date.today())),
                        'amount':self.amount,
                        'note':''
                        }
                    return suke
        return {'partner_paid_to':self.move_partner_id.id or self.expense_partner_id.id,
                'note':'لا يوجد صك متاح'}


    def generate_payslips_sukuk(self):
        sukuk_pages = []
        suke = self.get_available_suke()
        sukuk_pages.append((0, 0, suke))
        self.sukuk_page_ids = False
        self.sukuk_page_ids = sukuk_pages
        self.sukuk_computed = True
        return {"type": "ir.actions.act_window",
                "view_mode":"form",
                "res_model":"purchase.sukuk.wizard.report2",
                "target":"new",
                "res_id":self.id}


    def get_confirm(self):
        for elem in self.sukuk_page_ids:
            wallet_name = self.move_id.name or 'Expense-'+self.expense_id.name
            suke_id = self.env['sukuk.management.page'].create({
                "sukuk_book_id": elem.sukuk_book_id.id,
                "bank_id":elem.bank_id.id,
                "branch_id":elem.branch_id.id,
                "partner_paid_to":elem.partner_paid_to.id,
                "state": "draft",
                "wallet_name":wallet_name,
                "user_id": elem.user_id.id,
                "person_signature": elem.person_signature.id,
                "suke_book_number": elem.suke_book_number,
                "serial_no": elem.serial_no,
                "time_stamp": elem.time_stamp,
                "amount": elem.amount,
            })
            if self.move_id:
                if self.move_id.line_ids:
                    for line in self.move_id.line_ids:
                        line.name += ' check' + '-' + str(suke_id.bank_id.name) + '-' + str(suke_id.branch_id.name) + '- Serial No:' + str(suke_id.serial_no)
            if self.expense_id:
                self.expense_id.suke_id = suke_id.id

    @api.onchange('bank_id','branch_id','account_no_id')
    def get_dialog_box(self):
        if self.bank_id and self.branch_id and self.account_no_id:
            sukuk_books = self.env['sukuk.management.item'].search([('bank_id', '=', self.bank_id.id), ('branch_id', '=', self.branch_id.id),('account_no_id', '=', self.account_no_id.id)])
            if sukuk_books:
                self.dialog_box = str(len(sukuk_books))+" دفاتر متاحة "
            else:
                self.dialog_box = "لا يوجد دفاتر متاحة بالمعلومات المرفقة"
        else:
            self.dialog_box = ""



class PurchaseSukukReporPagetWizard(models.TransientModel):
    _name = "purchase.sukuk.wizard.page"

    sukuk_wizard_id = fields.Many2one('purchase.sukuk.wizard.report2')

    sukuk_book_id = fields.Many2one('sukuk.management.item', ondelete='cascade')
    state = fields.Selection([('draft', 'مسودة'), ('confirmed', 'مؤكدة'), ('cancel', 'ملغية')], default="draft")
    bank_id = fields.Many2one('res.bank', string='اسم المصرف')
    branch_id = fields.Many2one('bank.branch', string="أسم الفرع")

    partner_paid_to = fields.Many2one('res.partner', string="أسم المدفوع له")

    user_id = fields.Many2one('res.users', string='مقدم الطلب')
    person_signature = fields.Many2one('sukuk.management.signature',string="اسم المخول بالتوقيع")
    suke_book_number = fields.Integer( string='رقم الدفتر')
    serial_no = fields.Integer(string="رقم تسلسلي")
    time_stamp = fields.Date(string="التاريخ")
    amount = fields.Float(string="القيمة")
    note=fields.Text(string='ملاحظة')

    @api.onchange('sukuk_book_id')
    def change_suke_book_number(self):
        for elem in self:
            if elem.sukuk_book_id:
                elem.suke_book_number = elem.sukuk_book_id.suke_book_number
                elem.serial_no = False
                elem.note = False
