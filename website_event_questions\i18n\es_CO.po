# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_event_questions
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-11-09 21:04+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.registration_attendee_details_questions
msgid "<strong>Last questions</strong>"
msgstr "<strong>Últimas preguntas</strong>"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_question_2
msgid "Accommodation"
msgstr "Alojamiento"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report_answer_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Answer"
msgstr "Respuesta"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer_ids
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_registration_search_inherit_question
msgid "Answers"
msgstr "Respuestas"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_is_individual
msgid "Ask each attendee"
msgstr "Preguntar a cada asistente"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration
msgid "Attendee"
msgstr "Asistente"

#. module: website_event_questions
#: model:event.answer,name:website_event_questions.event_suggestion_0_2
msgid "Bronze"
msgstr "Bronce"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer_create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer_create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer_create_date
msgid "Created on"
msgstr "Creado el"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer_display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report_display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_event
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report_event_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Event"
msgstr "Evento"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer_event_answer_id
msgid "Event answer id"
msgstr "Id respuesta"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_event_id
msgid "Event id"
msgstr "Id evento"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer_event_registration_id
msgid "Event registration id"
msgstr "Id de inscripción"

#. module: website_event_questions
#: model:event.answer,name:website_event_questions.event_suggestion_0_0
msgid "Gold"
msgstr "Oro"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer_id
msgid "ID"
msgstr "ID"

#. module: website_event_questions
#: model:ir.model.fields,help:website_event_questions.field_event_question_is_individual
msgid ""
"If True, this question will be asked for every attendee of a reservation. If "
"not it will be asked only once and its value propagated to every attendees."
msgstr ""
"Si se marca, esta pregunta será hecha a todos los asistentes de una reserva. "
"Si no, se preguntará una sola vez y su valor propagado a todos los "
"asistentes. "

#. module: website_event_questions
#: model:event.answer,name:website_event_questions.event_suggestion_2_1
msgid "In Charge"
msgstr "A Cargo"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_question_0
msgid "Kind of partner"
msgstr "Clase de asociado"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer___last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_question___last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report___last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer_write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer_write_uid
msgid "Last Updated by"
msgstr "Actualizado por"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer_write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer_write_date
msgid "Last Updated on"
msgstr "Actualizado"

#. module: website_event_questions
#: model:event.answer,name:website_event_questions.event_suggestion_1_1
msgid "No"
msgstr "No"

#. module: website_event_questions
#: model:event.answer,name:website_event_questions.event_suggestion_2_0
msgid "Personal"
msgstr "Personal"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report_question_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_answer_simplified_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Question"
msgstr "Pregunta"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer_question_id
msgid "Question id"
msgstr "Id pregunta"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event_general_question_ids
#: model:ir.model.fields,field_description:website_event_questions.field_event_event_question_ids
#: model:ir.model.fields,field_description:website_event_questions.field_event_event_specific_question_ids
#: model:ir.ui.menu,name:website_event_questions.menu_report_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_form_inherit_question
msgid "Questions"
msgstr "Preguntas"

#. module: website_event_questions
#: model:ir.actions.act_window,name:website_event_questions.action_event_question_report
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_graph
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_pivot
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Questions Analysis"
msgstr "Análisis de las Preguntas"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_report_attendee_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.view_event_question_report_search
msgid "Registration"
msgstr "Inscripción"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_answer_sequence
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_event_questions
#: model:event.answer,name:website_event_questions.event_suggestion_0_1
msgid "Silver"
msgstr "Plata"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_title
msgid "Title"
msgstr "Título"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_question_1
msgid "Veggie"
msgstr "Vegetariano/a"

#. module: website_event_questions
#: model:event.answer,name:website_event_questions.event_suggestion_1_0
msgid "Yes"
msgstr "Sí"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_answer
msgid "event.answer"
msgstr "event.answer"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question
msgid "event.question"
msgstr "event.question"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question_report
msgid "event.question.report"
msgstr "event.question.report"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration_answer
msgid "event.registration.answer"
msgstr "event.registration.answer"
