# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase
# 
# Translators:
# <PERSON><PERSON><PERSON> <neman<PERSON><PERSON><PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
msgid ""
"\n"
"<p>Dear\n"
"% if object.partner_id.is_company and object.child_ids:\n"
"    ${object.partner_id.child_ids[0].name}\n"
"% else :\n"
"    ${object.partner_id.name}\n"
"% endif\n"
",</p><p>\n"
"Here is, in attachment, a ${object.state in ('draft', 'sent') and 'request for quotation' or 'purchase order confirmation'} <strong>${object.name}</strong>\n"
"% if object.partner_ref:\n"
"    with reference: ${object.partner_ref}\n"
"% endif\n"
"% if object.origin:\n"
"    (RFQ origin: ${object.origin})\n"
"% endif\n"
"amounting in <strong>${object.amount_total} ${object.currency_id.name}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"<p>Do not hesitate to contact us, further you have any question.</p>\n"
"<p>Best regards,</p>\n"
"<p style=\"color:#888888;\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"\n"
"<p>Dear ${object.partner_id.name} \n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>\n"
"Please find in attachment a <strong>${object.state in ('draft', 'sent') and 'request for quotation' or 'purchase order confirmation'} ${object.name}</strong>\n"
"% if object.partner_ref:\n"
"    with reference: ${object.partner_ref}\n"
"% endif\n"
"% if object.origin:\n"
"    (RFQ origin: ${object.origin})\n"
"% endif\n"
"amounting <strong>${object.amount_total} ${object.currency_id.name}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"<p>You can reply to this email if you have any questions.</p>\n"
"<p>Thank you,</p>\n"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/stock.py:79
#, python-format
msgid " Buy"
msgstr "Kupi"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_count
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_count
msgid "# Purchases"
msgstr "# Nabavki"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_supplier_invoice_count
#: model:ir.model.fields,field_description:purchase.field_res_users_supplier_invoice_count
msgid "# Vendor Bills"
msgstr "# Faktura dobavljača"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_count
msgid "# of Bills"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_nbr_lines
msgid "# of Lines"
msgstr "# linija"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_users_purchase_order_count
msgid "# of Purchase Order"
msgstr "# Narudžbenica"

#. module: purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase_done
msgid "${object.company_id.name} Order (Ref ${object.name or 'n/a' })"
msgstr ""

#. module: purchase
#: model:mail.template,subject:purchase.mail_template_data_notification_email_purchase_order
msgid "${object.subject}"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "3-way matching"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_module_account_3way_match
msgid "3-way matching: purchases, receptions and bills"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.mail_template_data_notification_email_purchase_order
msgid ""
"<html>\n"
"                <head></head>\n"
"                <body style=\"margin: 0; padding: 0;\">\n"
"                <table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed\" summary=\"o_mail_notification\">\n"
"                    <tbody>\n"
"\n"
"                      <!-- HEADER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\">\n"
"                                  <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                                      ${object.record_name}\n"
"                                  </span>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\">\n"
"                                  <img src=\"/logo.png\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id.name}\">\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- CONTENT -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px;\">\n"
"                            <tbody>\n"
"                              <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                                ${object.body | safe}\n"
"                              </td>\n"
"                            </tbody>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- FOOTER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                ${user.company_id.name}<br/>\n"
"                                ${user.company_id.phone or ''}\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                % if user.company_id.email:\n"
"                                <a href=\"mailto:${user.company_id.email}\" style=\"text-decoration:none; color: white;\">${user.company_id.email}</a><br/>\n"
"                                % endif\n"
"                                % if user.company_id.website:\n"
"                                    <a href=\"${user.company_id.website}\" style=\"text-decoration:none; color: white;\">\n"
"                                        ${user.company_id.website}\n"
"                                    </a>\n"
"                                % endif\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <tr>\n"
"                        <td align=\"center\">\n"
"                            Powered by <a href=\"https://www.odoo.com\">Odoo</a>.\n"
"                        </td>\n"
"                      </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                </body>\n"
"                </html>\n"
"            "
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/> "
"Cancelled</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Amount</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Date Req.</strong>"
msgstr "<strong>Datum zahtjeva</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>Date:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr "<strong>Opis</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Expected Date</strong>"
msgstr "<strong>Očekivani datum</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Datum narudžbe:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Our Order Reference:</strong>"
msgstr "<strong>Naša oznaka narudžbenice:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>Product</strong>"
msgstr "<strong>Proizvod</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr "<strong>Količina</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>Quantity</strong>"
msgstr "<strong>Količina</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Adresa isporuke:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr "<strong>Porezi</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "<strong>The ordered quantity has been updated.</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>Total:</strong>"
msgstr "<strong>Ukupno:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Total</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Jedinična cijena</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference:</strong>"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_warning_purchase
msgid "A warning can be set on a product or a customer (Purchase)"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_invoice_purchase_id
msgid "Add Purchase Order"
msgstr "Dodaj narudžbenicu"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:53
#, python-format
msgid "All"
msgstr "Sve"

#. module: purchase
#: selection:res.company,po_lock:0
msgid "Allow to edit purchase orders"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_account
msgid "Allows you to specify an analytic account on purchase order lines."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_account_analytic_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_account_analytic_id
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: purchase
#: model:res.groups,name:purchase.group_analytic_accounting
msgid "Analytic Accounting for Purchases"
msgstr "Analitičko računovodstvo za nabavke"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_analytic_tag_ids
msgid "Analytic Tags"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_group_analytic_account_for_purchases
msgid "Analytic accounting for purchases"
msgstr "Analitičko računovodstvo za nabavke"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_date_approve
msgid "Approval Date"
msgstr "Datum odobrenja"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Approve Order"
msgstr "Odobri narudžbu"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_invoice_line_purchase_id
msgid ""
"Associated Purchase Order. Filled in automatically when a PO is chosen on "
"the vendor bill."
msgstr ""

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_average_delivery_time
msgid "Average Delivery Time"
msgstr "Prosječno vrijeme dostave"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_average
msgid "Average Price"
msgstr "Srednja cijena"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_default_purchase_method
msgid "Bill Control"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_invoice_lines
msgid "Bill Lines"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_qty_invoiced
msgid "Billed Qty"
msgstr "Fakturisana količina"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Billed Quantity:"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_status
msgid "Billing Status"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_ids
msgid "Bills"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Bills Received"
msgstr ""

#. module: purchase
#: selection:product.template,purchase_line_warn:0
#: selection:res.partner,purchase_warn:0
msgid "Blocking Message"
msgstr "Blokirajuća Poruka"

#. module: purchase
#: code:addons/purchase/models/stock.py:73
#: model:stock.location.route,name:purchase.route_warehouse0_buy
#, python-format
msgid "Buy"
msgstr "Kupi"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_warehouse_buy_pull_id
msgid "Buy rule"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"By default, vendor prices can be set manually in the product detail form. If"
" your vendors provide you with pricelist files, this option allows you to "
"easily import them into the system from ‘Purchase > Vendor Pricelists’ menu."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_calendar
msgid "Calendar View"
msgstr "Kalendarski pogled"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Calls for tenders are used when you want to generate requests for quotations"
" to several vendors for a given set of products. You can configure per "
"product if you directly do a Request for Quotation to one vendor or if you "
"want a Call for Tenders to compare offers from several vendors."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/stock.py:76
#, python-format
msgid "Can't find any generic Buy route."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Cancel"
msgstr "Odustani"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:55
#: selection:purchase.order,state:0 selection:purchase.report,state:0
#, python-format
msgid "Cancelled"
msgstr "Poništeno"

#. module: purchase
#: code:addons/purchase/models/purchase.py:737
#, python-format
msgid "Cannot delete a purchase order line which is in state '%s'."
msgstr "Nemoguće je obrisati narudžbenicu čiji je status '%s'."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Click here to record a vendor bill."
msgstr "Klikni ovdje da zabilježiš fakturu dobavljača."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Click to create a quotation that will be converted into a purchase order."
msgstr "Klikni ovdje da kreiraš nacrt koji će postati narudžbenica."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "Click to create a request for quotation."
msgstr "Klikni da kreiraš zahtjev za narudžbenicom."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_invoice_pending
msgid "Click to create a vendor bill."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid "Click to define a new product."
msgstr "Klikni da dodaš novi proizvod."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_open_invoice
msgid "Click to record a vendor bill related to this purchase."
msgstr "Klikni da zabilježiš fakturu dobavljača vezanu za ovu nabavku."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Preduzeća"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Preduzeće"

#. module: purchase
#: code:addons/purchase/models/purchase.py:313
#, python-format
msgid "Compose Email"
msgstr "Sastavi e-mail"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Postavka"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr "Potvrdi narudžbu"

#. module: purchase
#: selection:res.company,po_double_validation:0
msgid "Confirm purchase orders in one step"
msgstr "Potvrdi narudžbenice u jednom koraku"

#. module: purchase
#: selection:res.company,po_lock:0
msgid "Confirmed purchase orders are not editable"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_control
msgid "Control"
msgstr "Kontrola"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_method
msgid "Control Policy"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_move_created_purchase_line_id
msgid "Created Purchase Order Line"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_company_currency_id
msgid "Currency"
msgstr "Valuta"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_date_approve
msgid "Date Approved"
msgstr "Datum odobrenja"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report_date_order
msgid "Date on which this document has been created"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_delay_pass
msgid "Days to Deliver"
msgstr "Dana do dostave"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_delay
msgid "Days to Validate"
msgstr "Dana do validacije"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Define your terms and conditions ..."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_type_id
msgid "Deliver To"
msgstr "Dostavi prema"

#. module: purchase
#: selection:res.config.settings,default_purchase_method:0
msgid "Delivered quantities"
msgstr "Dostavljene količine"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Deliveries & Invoices"
msgstr "Dostavnice i fakture"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_date_order
#: model:ir.model.fields,help:purchase.field_purchase_order_line_date_order
msgid ""
"Depicts the date where the Quotation should be validated and converted into "
"a purchase order."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_name
msgid "Description"
msgstr "Opis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Description for Vendors"
msgstr "Opis za dobavljače"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_default_location_dest_id_usage
msgid "Destination Location Type"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_report_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: purchase
#: selection:purchase.report,state:0
msgid "Done"
msgstr "Završeno"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_double_validation_amount
msgid "Double validation amount"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_move_dest_ids
msgid "Downstream Moves"
msgstr ""

#. module: purchase
#: selection:purchase.report,state:0
msgid "Draft RFQ"
msgstr "U nacrtu"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_dest_address_id
msgid "Drop Ship Address"
msgstr "Adresa dostave"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_module_stock_dropshipping
msgid "Dropshipping"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_invoice_purchase_id
msgid ""
"Encoding help. When selected, the associated purchase order lines are added "
"to the vendor bill. Several PO can be selected."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Expected Date"
msgstr "Očekivani datum"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Extended Filters"
msgstr "Prošireni filteri"

#. module: purchase
#: code:addons/purchase/models/purchase.py:577
#, python-format
msgid "Extra line with %s "
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_fiscal_position_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_fiscal_position_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Fiscal Position"
msgstr "Fiskalna pozicija"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Future Activities"
msgstr ""

#. module: purchase
#: selection:res.company,po_double_validation:0
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr "Forsiraj odobravanje narudžbenica u 2 nivoa"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Get warnings in orders for products or vendors"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_weight
msgid "Gross Weight"
msgstr "Ukupna težina"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Grupiši po"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr "Sakrij poništene linije"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "How to import"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_id
msgid "ID"
msgstr "ID"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If enabled, activates 3-way matching on vendor bills : the items must be "
"received in order to pay the invoice."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Import vendor pricelists"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:206
#, python-format
msgid "In order to delete a purchase order, you must cancel it first."
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_action_picking_tree_in_move
msgid "Incoming  Products"
msgstr "Proizvodi u pristizanju"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Incoming Shipments"
msgstr "Isporuke u pristizanju"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_incoterm_id
msgid "Incoterm"
msgstr "Incoterm"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: purchase
#: model:ir.model,name:purchase.model_account_invoice_line
msgid "Invoice Line"
msgstr "Stavka računa"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices and Incoming Shipments"
msgstr "Fakture i pristižuće isporuke"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Invoicing"
msgstr "Fakturisanje"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_is_shipped
msgid "Is Shipped"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_is_installed_sale
msgid "Is the Sale Module Installed"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_report___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late Activities"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_double_validation
msgid "Levels of Approvals"
msgstr "Nivoi odobravanja"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_po_double_validation
msgid "Levels of Approvals *"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Lock"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_lock_confirmed_po
msgid "Lock Confirmed Orders"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:56
#: selection:purchase.order,state:0
#, python-format
msgid "Locked"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Logistics"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Make sure you only pay bills for which you received the goods you ordered"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_manage_vendor_price
msgid "Manage Vendor Price"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Manage your purchase agreements (call for tenders, blanket orders)"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Manager"
msgstr "Nadzor"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Managers must approve orders"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Manual Invoices"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_lead
#: model:ir.model.fields,help:purchase.field_res_config_settings_po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for procuring products, they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings_use_po_lead
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_stock
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_purchase_warn_msg
#: model:ir.model.fields,field_description:purchase.field_res_users_purchase_warn_msg
msgid "Message for Purchase Order"
msgstr "Poruka za Zahtev za Nabavku"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_line_warn_msg
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr "Poruka Stavke Trebovanja"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_po_double_validation_amount
msgid "Minimum Amount"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Pravila Minimalnog Inventara"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_double_validation_amount
#: model:ir.model.fields,help:purchase.field_res_config_settings_po_double_validation_amount
msgid "Minimum amount for which a double validation is required"
msgstr "Minimalni iznos za koji je potrebna dupla validacija"

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_monthly_purchases
msgid "Monthly Purchases"
msgstr "Mjesečne nabavke"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Most propositions of purchase orders are created automatically\n"
"                by Odoo based on inventory needs."
msgstr ""
"Većina prijedloga za narudžbenice je kreirana automatski\n"
"od strane Odoo sistema na osnovu potreba skladišta."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "My Activities"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:44
#, python-format
msgid "Name"
msgstr "Naziv"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:43
#, python-format
msgid "Newest"
msgstr ""

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "No Bill to Receive"
msgstr ""

#. module: purchase
#: selection:product.template,purchase_line_warn:0
#: selection:res.partner,purchase_warn:0
msgid "No Message"
msgstr "Nema Poruke"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "No longer edit orders once confirmed"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line_product_image
msgid ""
"Non-stored related field to allow portal user to see the image of the "
"product he has ordered"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Zabilješke"

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "Nothing to Bill"
msgstr ""

#. module: purchase
#: selection:product.template,purchase_method:0
msgid "On ordered quantities"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_purchase_method
#: model:ir.model.fields,help:purchase.field_product_template_purchase_method
msgid ""
"On ordered quantities: control bills based on ordered quantities.\n"
"On received quantities: control bills based on received quantity."
msgstr ""

#. module: purchase
#: selection:product.template,purchase_method:0
msgid "On received quantities"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_po_order_approval
msgid "Order Approval"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_report_date_order
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Order Date"
msgstr "Datum naloga"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_order_line
msgid "Order Lines"
msgstr "Stavke narudžbe"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order Month"
msgstr "Mjesec naloga"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_name
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Reference"
msgstr "Oznaka naloga"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_state
msgid "Order Status"
msgstr "Status narudžbe"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order of Day"
msgstr "Narudžba dana"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Ordered Quantity:"
msgstr ""

#. module: purchase
#: selection:res.config.settings,default_purchase_method:0
msgid "Ordered quantities"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_orderpoint_id
msgid "Orderpoint"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Orders"
msgstr "Narudžbe"

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase_done
msgid "PO_${(object.name or '').replace('/','_')}"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_partner_id
msgid "Partner"
msgstr "Partner"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_country_id
msgid "Partner Country"
msgstr "Država partnera"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Partner's Country"
msgstr "Država partnera"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_payment_term_id
msgid "Payment Terms"
msgstr "Uslovi plaćanja"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_category_property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase.field_product_product_property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase.field_product_template_property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "Konto razlike u cijeni"

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_price_per_supplier
msgid "Price Per Vendor"
msgstr "Cijena po dobavljaču"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr "Štampaj RFQ"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_group_id
msgid "Procurement Group"
msgstr "Grupa nabavke"

#. module: purchase
#: model:ir.model,name:purchase.model_procurement_group
msgid "Procurement Requisition"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_procurement_rule
msgid "Procurement Rule"
msgstr "Pravilo nabave"

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Product"
msgstr "Proizvod"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_category_config_purchase
msgid "Product Categories"
msgstr "Kategorije proizvoda"

#. module: purchase
#: model:ir.model,name:purchase.model_product_category
#: model:ir.model.fields,field_description:purchase.field_purchase_report_category_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product Category"
msgstr "Grupa proizvoda"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_image
msgid "Product Image"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_unit_quantity
msgid "Product Quantity"
msgstr "Količina proizvoda"

#. module: purchase
#: model:ir.model,name:purchase.model_product_template
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_tmpl_id
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_uom
msgid "Product Unit of Measure"
msgstr "JM proizvoda"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_product_action
#: model:ir.ui.menu,name:purchase.product_product_menu
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Products"
msgstr "Proizvodi"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_standard
msgid "Products Value"
msgstr "Vrijednost proizvoda"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_double_validation
#: model:ir.model.fields,help:purchase.field_res_config_settings_po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase"
msgstr "Nabavka"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_module_purchase_requisition
msgid "Purchase Agreements"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_order_report_all
msgid "Purchase Analysis"
msgstr "Analiza nabavke"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid ""
"Purchase Analysis allows you to easily check and analyse your company "
"purchase history and performance. From this menu you can track your "
"negotiation performance, the delivery performance of your vendors, etc."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_account
msgid "Purchase Analytics"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase Bills"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_lead
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_po_lead
msgid "Purchase Lead Time"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:54
#: model:ir.actions.report,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_invoice_line_purchase_id
#: model:ir.model.fields,field_description:purchase.field_res_partner_purchase_warn
#: model:ir.model.fields,field_description:purchase.field_res_users_purchase_warn
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: selection:purchase.order,state:0 selection:purchase.report,state:0
#: model:res.request.link,name:purchase.req_link_purchase_order
#, python-format
msgid "Purchase Order"
msgstr "Narudžbenica"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order Confirmation #"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Order Fiscal Position"
msgstr "Fiskalna pozicija"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase.field_account_invoice_line_purchase_line_id
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_line_warn
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_line_warn
#: model:ir.model.fields,field_description:purchase.field_stock_move_purchase_line_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Purchase Order Line"
msgstr "Stavka narudžbenice"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
msgid "Purchase Order Lines"
msgstr "Stavke narudžbenice"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_lock
msgid "Purchase Order Modification"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_po_lock
msgid "Purchase Order Modification *"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_lock
#: model:ir.model.fields,help:purchase.field_res_config_settings_po_lock
msgid ""
"Purchase Order Modification used when you want to purchase order editable "
"after confirm"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.actions.act_window,name:purchase.purchase_order_action_generic
#: model:ir.model.fields,field_description:purchase.field_stock_picking_purchase_id
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Orders"
msgstr "Narudžbenice"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid "Purchase Orders #"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Orders Statistics"
msgstr "Statistika narudžbenice"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Purchase orders that have been invoiced."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Purchase orders that include lines not invoiced."
msgstr "Narudžbenica koja uključuje stavke koje nisu fakturisane."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_warehouse_buy_to_resupply
msgid "Purchase to resupply this warehouse"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_negociation
msgid "Purchase-Standard Price"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_line_product_tree
#: model:ir.ui.menu,name:purchase.menu_purchase_root
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
#: model_terms:ir.ui.view,arch_db:purchase.view_warehouse_orderpoint_purchase_form
msgid "Purchases"
msgstr "Nabavke"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_report
msgid "Purchases Orders"
msgstr "Narudžbenice"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Quantities billed by vendors"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_qty
msgid "Quantity"
msgstr "Količina"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Quotations"
msgstr "Ponude"

#. module: purchase
#: selection:purchase.order,state:0
msgid "RFQ"
msgstr ""

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_approved
msgid "RFQ Approved"
msgstr ""

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_confirmed
msgid "RFQ Confirmed"
msgstr ""

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_done
msgid "RFQ Done"
msgstr ""

#. module: purchase
#: selection:purchase.order,state:0 selection:purchase.report,state:0
msgid "RFQ Sent"
msgstr ""

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase
msgid "RFQ_${(object.name or '').replace('/','_')}"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Send RFQ by Email"
msgstr "Ponovo pošalji RFQ mejlom"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Receive Products"
msgstr "Primi proizvode"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_qty_received
msgid "Received Qty"
msgstr "Primljena količina"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Received Quantity:"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_open_picking
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_count
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_ids
msgid "Receptions"
msgstr "Prijemnice"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Reference"
msgstr "Šifra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_uom
msgid "Reference Unit of Measure"
msgstr "Jedinica mjere"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a"
" sales order)"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_partner_ref
msgid ""
"Reference of the sales order or bid sent by the vendor. It's used to do the "
"matching when you receive the products as this reference is usually written "
"on the delivery order sent by your vendor."
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.purchase_report
msgid "Reporting"
msgstr "Izvještavanje"

#. module: purchase
#: model:ir.actions.report,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Request for Quotation"
msgstr "Zahtjev za ponudu"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr "Zahtjev za ponudu #"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Request your vendors to deliver to your customers"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
msgid "Requests for Quotation"
msgstr "Zahtjevi za ponudu"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_move_ids
msgid "Reservation"
msgstr "Rezervacija"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_report_user_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Responsible"
msgstr "Odgovoran"

#. module: purchase
#: model:ir.model,name:purchase.model_stock_return_picking
msgid "Return Picking"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_stock
msgid "Schedule receivings earlier to avoid delays"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_date_planned
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_date_planned
msgid "Scheduled Date"
msgstr "Planirani datum"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr "Pretraži narudžbenice"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_use_po_lead
msgid "Security Lead Time for Purchase"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_purchase_line_warn
#: model:ir.model.fields,help:purchase.field_product_template_purchase_line_warn
#: model:ir.model.fields,help:purchase.field_res_partner_purchase_warn
#: model:ir.model.fields,help:purchase.field_res_users_purchase_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Izbor opcije \"Upozorenje\" ce obavestiti korisnika poruko, Izbor "
"\"Blokiranje poruke\" ce generisati gresku sa porukom i blokirati tok. "
"Poruka mora da bude napisana u sledecem polju"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr "Pošalji narudžbenicu mejlom"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send RFQ by Email"
msgstr "Pošalji zahtjev za ponudu mejlom"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_account
msgid "Set analytic accounts in purchase orders"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set date to all order lines"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set to Draft"
msgstr "Postavi na nacrt"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Podešavanja"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Shipment"
msgstr "Isporuke"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_origin
msgid "Source Document"
msgstr "Izvorni dokument"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Status"

#. module: purchase
#: model:ir.model,name:purchase.model_stock_move
msgid "Stock Move"
msgstr "Skladišni prenosi"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Stock Moves"
msgstr "Prenosnice"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_subtotal
msgid "Subtotal"
msgstr "Međuzbir"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_property_purchase_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_users_property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Valuta dobavljača"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_tax
msgid "Tax"
msgstr "Porez"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_taxes_id
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Taxes"
msgstr "Porezi"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Taxes:"
msgstr "Porezi:"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_notes
msgid "Terms and Conditions"
msgstr "Uslovi i dogovori"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"The product form contains detailed information to improve the\n"
"                purchase process: prices, procurement logistics, accounting data,\n"
"                available vendors, etc."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"The product form contains detailed information to improve the\n"
"            purchase process: prices, procurement logistics, accounting data,\n"
"            available vendors, etc."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:649
#, python-format
msgid ""
"The quantities on your purchase order indicate less than billed. You should "
"ask for a refund. "
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"The quotation contains the history of the discussion/negotiation\n"
"                you had with your vendor. Once confirmed, a request for\n"
"                quotation is converted into a purchase order."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid ""
"The request for quotation is the first step of the purchases flow. Once\n"
"                    converted into a purchase order, you will be able to control the receipt\n"
"                    of the products and the vendor bill."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:878
#, python-format
msgid ""
"There is no vendor associated to the product %s. Please define a vendor for "
"this product."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_category_property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase.field_product_template_property_account_creditor_price_difference
msgid ""
"This account will be used to value price difference between purchase price "
"and cost price."
msgstr ""
"Ovaj konto biće upotrebljen u svrhu određivanja vrednosti razlike između "
"nabavne i prodajne cene."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "This changes the scheduled date of all order lines to the given date"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner_property_purchase_currency_id
#: model:ir.model.fields,help:purchase.field_res_users_property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings_default_purchase_method
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "This note will show up on purchase orders."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/account_invoice.py:203
#, python-format
msgid "This vendor bill has been created from: %s"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/account_invoice.py:217
#, python-format
msgid "This vendor bill has been modified from: %s"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Click to create a new RfQ."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_picking_type_id
msgid "This will determine operation type of incoming shipment"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: selection:purchase.order,state:0 selection:purchase.report,state:0
msgid "To Approve"
msgstr "Čeka odobrenje"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Today Activities"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:45
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_total
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#, python-format
msgid "Total"
msgstr "Ukupno"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_total
msgid "Total Price"
msgstr "Ukupna cijena"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Total Untaxed amount"
msgstr "Ukupno bez poreza"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Total amount"
msgstr "Ukupan iznos"

#. module: purchase
#: model:ir.model,name:purchase.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: purchase
#: code:addons/purchase/models/purchase.py:362
#, python-format
msgid ""
"Unable to cancel purchase order %s as some receptions have already been "
"done."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:365
#, python-format
msgid ""
"Unable to cancel this purchase order. You must first cancel related vendor "
"bills."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_unit
msgid "Unit Price"
msgstr "Jed. cena"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr "Kategorija jedinice mjere"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_form_action
msgid "Units of Measure"
msgstr "Jedinice mjere"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unlock"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Untaxed"
msgstr "Bez poreza"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_untaxed
msgid "Untaxed Amount"
msgstr "Iznos bez poreza"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Untaxed Amount:"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_invoice_pending
msgid ""
"Use this menu to control the invoices to be received from your\n"
"            vendors. When registering a new bill, set the purchase order\n"
"            and Odoo will fill the bill automatically according to ordered\n"
"            or received quantities."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Use this menu to search within your purchase orders by\n"
"                references, vendor, products, etc. For each purchase order,\n"
"                you can track the related discussion with the vendor, control\n"
"                the products received and control the vendor bills."
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Korisnik"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings_company_currency_id
msgid "Utility field to express amount currency"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_partner_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr "Dobavljač"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_supplier_invoices
#: model:ir.actions.act_window,name:purchase.action_invoice_pending
#: model:ir.actions.act_window,name:purchase.purchase_open_invoice
#: model:ir.ui.menu,name:purchase.menu_procurement_management_pending_invoice
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_account_buttons
msgid "Vendor Bills"
msgstr "Fakture dobavljača"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_group_manage_vendor_price
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Vendor Pricelists"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_partner_ref
msgid "Vendor Reference"
msgstr "Oznaka dobavljača"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management_supplier_name
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Vendors"
msgstr "Dobavljači"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid ""
"Vendors bills can be pre-generated based on purchase\n"
"                    orders or receipts. This allows you to control bills\n"
"                    you receive from your vendor according to the draft\n"
"                    document in Odoo."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_volume
msgid "Volume"
msgstr "Zapremina"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: selection:purchase.order,invoice_status:0
msgid "Waiting Bills"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase.field_purchase_report_picking_type_id
msgid "Warehouse"
msgstr "Skladište"

#. module: purchase
#: selection:product.template,purchase_line_warn:0
#: selection:res.partner,purchase_warn:0
msgid "Warning"
msgstr "Upozorenje"

#. module: purchase
#: code:addons/purchase/models/purchase.py:268
#: code:addons/purchase/models/purchase.py:808
#, python-format
msgid "Warning for %s"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Warning on the Purchase Order"
msgstr "Upozorenje za Zahtev za Nabavku"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Warning when Purchasing this Product"
msgstr "Upozorenje kada Kupujete ovaj Proizvod"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_group_warning_purchase
msgid "Warnings"
msgstr "Upozorenja"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_website_url
msgid "Website URL"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_stock_warehouse_buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_open_invoice
msgid ""
"You can control the invoice from your vendor according to\n"
"            what you purchased (services) or received (products)."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"You must define a product for everything you purchase, whether\n"
"                it's a physical product, a consumable or services you buy to\n"
"                subcontractors."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"You must define a product for everything you purchase, whether\n"
"            it's a physical product, a consumable or services you buy to\n"
"            subcontractors."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:395
#, python-format
msgid "You must set a Vendor Location for this partner %s"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_stock
msgid "days"
msgstr "dana"

#. module: purchase
#: model:ir.model,name:purchase.model_res_config_settings
msgid "res.config.settings"
msgstr ""
