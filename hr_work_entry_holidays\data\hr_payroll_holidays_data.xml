<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <record id="hr_holidays.holiday_status_comp" model="hr.leave.type">
            <field name="work_entry_type_id" ref="hr_work_entry_contract.work_entry_type_compensatory"></field>
        </record>

        <record id="hr_holidays.holiday_status_unpaid" model="hr.leave.type">
            <field name="work_entry_type_id" ref="hr_work_entry_contract.work_entry_type_unpaid_leave"></field>
        </record>

        <record id="hr_holidays.holiday_status_sl" model="hr.leave.type">
            <field name="work_entry_type_id" ref="hr_work_entry_contract.work_entry_type_sick_leave"></field>
        </record>

        <record id="hr_holidays.holiday_status_cl" model="hr.leave.type">
            <field name="work_entry_type_id" ref="hr_work_entry_contract.work_entry_type_legal_leave"></field>
        </record>
    </data>
</odoo>
