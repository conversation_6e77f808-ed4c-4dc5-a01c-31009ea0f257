<?xml version="1.0" encoding='UTF-8'?>
<odoo>
    <record id="website_visitor_cron" model="ir.cron">
        <field name="name">Website Visitor : Archive old visitors</field>
        <field name="model_id" ref="model_website_visitor"/>
        <field name="state">code</field>
        <field name="code">model._cron_archive_visitors()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="False"/>
    </record>
</odoo>
