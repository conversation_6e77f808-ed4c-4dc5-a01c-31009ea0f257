<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="crm_quotation_partner_view_form" model="ir.ui.view">
        <field name="name">crm.quotation.partner.view.form</field>
        <field name="model">crm.quotation.partner</field>
        <field name="arch" type="xml">
            <form string="New Quotation">
                <group>
                    <group>
                        <field name="action" widget="radio"/>
                        <field name="lead_id" invisible="1"/>
                    </group>
                </group>
                <group attrs="{'invisible': [('action','!=','exist')], 'required':[('action', '=','exist')]}">
                    <group>
                        <field name="partner_id" attrs="{'invisible': [('action','!=','exist')], 'required':[('action', '=','exist')]}"
                               context="{'res_partner_search_mode': 'customer'}" />
                    </group>
                </group>
                <footer>
                    <button name="action_apply" string="Confirm" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="crm_quotation_partner_action" model="ir.actions.act_window">
        <field name="name">New Quotation</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">crm.quotation.partner</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="crm_quotation_partner_view_form"/>
        <field name="target">new</field>
    </record>
</odoo>
