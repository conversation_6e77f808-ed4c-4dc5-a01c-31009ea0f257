<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"></use>
        </clipPath>
        <path id="filterPath"
            d="M0.0442,0.7361c-0.0096-0.0683-0.0258-0.1816-0.0417-0.2932a0.3598,0.2985,0,0,1,0.3573-0.3335h0.1806a0.3599,0.2985,0,0,1,0.3539,0.2443l0.049,0.2205a0.3599,0.2985,0,0,1-0.2006,0.3244L0.555,0.9715A0.3598,0.2985,0,0,1,0.0941,0.8564h0A0.3593,0.2981,0,0,1,0.0442,0.7361Z">
        </path>
    </defs><svg viewBox="59.51575469970703 40.560211181640625 181.27603149414062 201.9112548828125"
        preserveAspectRatio="none">
        <path class="background"
            d="M129.19,49.73c-70,34.82-101.39,77.6-26.43,153.1,58.35,58.77,88,35,91.3,34.33C278.42,219.07,243.66-7.23,129.19,49.73Z"
            fill="#383E45"></path>
    </svg><svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"></use>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"></image>
</svg>
