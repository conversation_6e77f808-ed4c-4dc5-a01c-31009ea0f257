<?xml version="1.0" encoding="UTF-8"?>
<odoo>
        <record id="res_partner_grade_platinium" model="res.partner.grade">
            <field name="name">Platinum</field>
            <field name="sequence">4</field>
        </record>
        <record id="res_partner_grade_gold" model="res.partner.grade">
            <field name="name">Gold</field>
            <field name="sequence">3</field>
        </record>
        <record id="res_partner_grade_silver" model="res.partner.grade">
            <field name="name">Silver</field>
            <field name="sequence">2</field>
        </record>
        <record id="res_partner_grade_bronze" model="res.partner.grade">
            <field name="name">Bronze</field>
            <field name="sequence">1</field>
        </record>

        <record id="base.res_partner_3" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_bronze"/>
            <field name="partner_weight">10</field>
        </record>
        <record model="res.partner" id="base.res_partner_2">
            <field name="assigned_partner_id" ref="base.res_partner_3"/>
        </record>
       <record id="base.res_partner_4" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_bronze"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_12" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_bronze"/>
            <field name="partner_weight">10</field>
        </record>

        <record id="base.res_partner_3" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_silver"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_12" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_silver"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_12" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_silver"/>
            <field name="partner_weight">10</field>
        </record>

        <record id="base.res_partner_10" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_gold"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_18" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_gold"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_1" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_gold"/>
            <field name="partner_weight">10</field>
        </record>

        <record id="base.res_partner_12" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_platinium"/>
            <field name="partner_weight">10</field>
        </record>
        <record id="base.res_partner_4" model="res.partner">
            <field name="grade_id" ref="res_partner_grade_platinium"/>
            <field name="partner_weight">10</field>
        </record>

        <record model="res.partner" id="base.res_partner_10">
            <field name="assigned_partner_id" ref="base.res_partner_4"/>
        </record>
        <record model="res.partner" id="base.res_partner_12">
            <field name="assigned_partner_id" ref="base.res_partner_10"/>
        </record>
        <record model="res.partner" id="base.res_partner_4">
            <field name="assigned_partner_id" ref="base.res_partner_4"/>
        </record>
        <record model="res.partner" id="base.res_partner_10">
            <field name="assigned_partner_id" ref="base.res_partner_12"/>
        </record>
        <record model="res.partner" id="base.res_partner_3">
            <field name="assigned_partner_id" ref="base.res_partner_3"/>
        </record>
        <record model="res.partner" id="base.res_partner_2">
            <field name="assigned_partner_id" ref="base.res_partner_1"/>
        </record>
        <record model="res.partner" id="base.res_partner_4">
            <field name="assigned_partner_id" ref="base.res_partner_18"/>
        </record>
        <record model="res.partner" id="base.res_partner_4">
            <field name="assigned_partner_id" ref="base.res_partner_3"/>
        </record>
        <record model="res.partner" id="base.res_partner_1">
            <field name="assigned_partner_id" ref="base.res_partner_12"/>
        </record>
        <record model="res.partner" id="base.res_partner_1">
            <field name="assigned_partner_id" ref="base.res_partner_2"/>
        </record>
        <record model="res.partner" id="base.res_partner_1">
            <field name="assigned_partner_id" ref="base.res_partner_2"/>
        </record>
        <record model="res.partner" id="base.res_partner_12">
            <field name="assigned_partner_id" ref="base.res_partner_12"/>
        </record>
</odoo>
