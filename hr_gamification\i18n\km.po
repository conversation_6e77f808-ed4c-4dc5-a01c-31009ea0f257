# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_gamification
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid ""
"A goal is defined by a user and a goal type.\n"
"                    Goals can be created automatically by using challenges."
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__badge_ids
msgid ""
"All employee badges, linked to the employee either directly or through the "
"user"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                    The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                    The goals are created for the specified users or member of the group."
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:hr_gamification.gamification_badge_menu_hr
msgid "Badges"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid ""
"Badges are rewards of good work. Give them to people you believe deserve it."
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__direct_badge_ids
msgid "Badges directly linked to the employee"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Cancel"
msgstr "លុបចោល"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.challenge_list_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_challenge_menu_hr
#: model:ir.ui.menu,name:hr_gamification.menu_hr_gamification
msgid "Challenges"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid "Create a new challenge"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid "Create a new goal"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Describe what they did and why it matters (will be public)"
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__direct_badge_ids
msgid "Direct Badge"
msgstr ""

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_hr_employee
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user__employee_id
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__employee_id
msgid "Employee"
msgstr "បុគ្គលិក"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__badge_ids
msgid "Employee Badges"
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__goal_ids
msgid "Employee HR Goals"
msgstr ""

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr ""

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr ""

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__goal_ids
msgid "Goal"
msgstr ""

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.goals_menu_groupby_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_goal_menu_hr
msgid "Goals History"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant a Badge"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant this employee his first badge"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_badge_form_view
msgid "Granted"
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge__granted_employees_count
msgid "Granted Employees Count"
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__has_badges
msgid "Has Badges"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Received Badges"
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "Related user name for the resource to manage its access."
msgstr ""

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.action_reward_wizard
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee"
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee with"
msgstr ""

#. module: hr_gamification
#: code:addons/hr_gamification/models/gamification.py:18
#, python-format
msgid "The selected employee does not correspond to the selected user."
msgstr ""

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "User"
msgstr ""

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_res_users
msgid "Users"
msgstr "អ្នកប្រើ"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "What are you thankful for?"
msgstr ""

#. module: hr_gamification
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:22
#, python-format
msgid "You can not send a badge to yourself."
msgstr ""

#. module: hr_gamification
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:19
#, python-format
msgid "You can send badges only to employees linked to a user."
msgstr ""

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "to reward this employee for a good action"
msgstr ""
