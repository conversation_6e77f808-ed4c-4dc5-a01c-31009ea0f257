# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sale
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# whenwesober, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> Manyo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid ""
".\n"
"            <span>Manual actions may be needed.</span>"
msgstr ""
".\n"
"            <span>Action manual mungkin dibutuhkan.</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Sales</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "<span>Registration modification for attendee:</span>"
msgstr "<span>Modifikasi pendaftaran untuk peserta:</span>"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,help:event_sale.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr "Keterangan tiket yang Anda ingin komunikasikan ke pelanggan Anda."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:event_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Produk yang bisa disimpan adalah produk yang mana Anda mengelola stoknya. Aplikasi Inventaris harus diinstal.\n"
"Produk yang bisa dipakai adalah produk yang stoknya tidak akan dikelola.\n"
"Layanan adalah produk non-material yang Anda sediakan."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_order_lines_ids
msgid "All sale order lines pointing to this event"
msgstr "Semua baris sale order menunjuk pada acara ini"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order__attendee_count
msgid "Attendee Count"
msgstr "Jumlah Peserta"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.sale_order_view_form
msgid "Attendees"
msgstr "Peserta"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before confirming"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_campaign_id
msgid "Campaign"
msgstr "Kampanye"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Cancel"
msgstr "Batal"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr ""
"Pilih acara dan secara otomatis akan membuat pendaftaran untuk acara ini."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr ""
"Pilih tiket acara dan secara otomatis akan membuat pendaftaran untuk tiket "
"acara ini."

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_configurator_action
msgid "Configure an event"
msgstr "Konfigurasikan acara"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Confirm"
msgstr "Konfirmasi"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__description
msgid "Description"
msgstr "Deskripsi"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr "Edit Detail Peserta pada Konfirmasi Sales"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr "Edit Baris Peserta pada Konfirmasi Sales"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr "Editor"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr "Email"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
msgid "Event"
msgstr "Acara"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_configurator
msgid "Event Configurator"
msgstr "Konfigurator Acara"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ok
msgid "Event Ok"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:product.product,name:event_sale.product_product_event
msgid "Event Registration"
msgstr "Pendaftaran acara"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr "Pendaftaran Acara"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_type_ticket
msgid "Event Template Ticket"
msgstr "Templat Tiket Acara"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
#: model:ir.model.fields.selection,name:event_sale.selection__product_template__detailed_type__event
msgid "Event Ticket"
msgstr "Tiket acara"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Tiket acara"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Exception:"
msgstr "Pengecualian:"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__free
msgid "Free"
msgstr "Bebas"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr "ID"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__is_paid
msgid "Is Paid"
msgstr "Apakah Berbayar"

#. module: event_sale
#: model:ir.model,name:event_sale.model_account_move
msgid "Journal Entry"
msgstr "Entri Jurnal"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_medium_id
msgid "Medium"
msgstr "Media"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__mobile
msgid "Mobile"
msgstr "Ponsel"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
msgid "Name"
msgstr "Nama"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__to_pay
msgid "Not Paid"
msgstr "Belum Lunas"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Ok"
msgstr "Ok"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Date"
msgstr "Tanggal Order"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Ref"
msgstr "Referensi Order"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr "Pendaftaran Original"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__paid
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Paid"
msgstr "Lunas"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__payment_status
msgid "Payment Status"
msgstr "Payment Status"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr "Telepon"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Price"
msgstr "Harga"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price_reduce
msgid "Price Reduce"
msgstr "Pengurangan Harga"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Pengurangan Harga Termasuk Pajak"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_product
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__product_id
msgid "Product"
msgstr "Produk"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
msgid "Product Template"
msgstr "Templete Produk"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:event_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "Tipe Produk"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Registration"
msgstr "Registrasi rawat inap"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr "Pendaftaran untuk Diedit"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Sale Order"
msgstr "Order Penjualan"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Sales"
msgstr "Penjualan"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_price_subtotal
msgid "Sales (Tax Excluded)"
msgstr "Pajak (Tidak Termasuk Pajak)"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales End"
msgstr "Penjualan Akhir"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
msgid "Sales Order"
msgstr "Order Penjualan"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr "Detail Order Penjualan"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales Start"
msgstr "Mulai Sales"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_source_id
msgid "Source"
msgstr "Sumber"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Ticket changed from"
msgstr "Tiket diganti dari"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Total sales for this event"
msgstr "Total sales untuk acara ini"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Transaction"
msgstr "Transaksi"

#. module: event_sale
#: model:product.product,uom_name:event_sale.product_product_event
msgid "Units"
msgstr "Unit"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please give details about the registrations"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "to"
msgstr "kepada"
