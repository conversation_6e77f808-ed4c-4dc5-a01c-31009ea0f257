from odoo.tests.common import TransactionCase
import odoo_firebase_core as odoo_firebase

class TestOdooFirebaseAuth(TransactionCase):

    def setUp(self):
        super().setUp()
        self.partner_firebase = self.env['res.partner.firebase']

    def test_partner_firebase_sync(self):
        # Crea un nuevo usuario en Firebase
        email = "<EMAIL>"
        password = "password123"
        user_id = self.firebase_controller.create_user(email, password)

        # Crea un partner en Odoo
        partner_data = {
            'name': 'Partner de prueba',
            'email': email,
        }
        partner = self.env['res.partner'].create(partner_data)

        # Sincronizar el partner con Firebase
        self.partner_firebase.sync_partner_with_firebase(partner)

        # Verificar que el partner se sincronizó correctamente
        partner_firebase = self.partner_firebase.search([('partner_id', '=', partner.id)])
        self.assertIsNotNone(partner_firebase)
        self.assertEqual(partner_firebase.user_id, user_id)
