# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_meet
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_aside
msgid "<b>&amp;#9900;&amp;nbsp;</b>"
msgstr "<b>&amp;#9900;&amp;nbsp;</b>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<i class=\"fa fa-spin fa-circle-o-notch mr-3\"/>\n"
"                        <span>Loading your room...</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "<span class=\"badge badge-danger\">Unpublished</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_aside
msgid "<span class=\"h5 mb-0 pt-0 pt-md-3 pb-0 pb-md-2\">Other Rooms</span>"
msgstr "<span class=\"h5 mb-0 pt-0 pt-md-3 pb-0 pb-md-2\">その他のルーム</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "<span>Create a Room</span>"
msgstr "<span>ルームを作成</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "<span>Join a room</span>"
msgstr "<span>ルームに参加</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<span>Oops! This room is full !</span><br/>Come back later to have a chat "
"with us!"
msgstr "<span>！！この部屋は満室です！</span><br/>後ほど戻ってきてチャットしましょう！"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"<span>This room is not open right now!</span><br/>\n"
"                        Join us here on the"
msgstr ""
"<span>このルームは現在開いていません！</span><br/>\n"
"                        以下に参加して下さい:"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "A chat among"
msgstr "以下の間のチャット:"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__active
msgid "Active"
msgstr "有効"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "All Languages"
msgstr "全ての言語"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/customize_options.xml:0
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,field_description:website_event_meet.field_event_type__meeting_room_allow_creation
#, python-format
msgid "Allow Room Creation"
msgstr "ルームの作成を許可"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
#, python-format
msgid "Are you sure you want to close this room ?"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
#, python-format
msgid "Are you sure you want to duplicate this room ?"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__target_audience
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Audience"
msgstr "聴衆"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid ""
"Be sure you are ready to spend at least 10 minutes in the room if you want "
"to initiate a new topic."
msgstr "新しいトピックを始めたい場合は、必ず最低10分はルームに滞在する心構えでいましょう。"

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_0
msgid "Best wood for furniture"
msgstr "家具に最適な木材"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__can_publish
msgid "Can Publish"
msgstr "公開可能"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Capacity"
msgstr "能力"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__chat_room_id
msgid "Chat Room"
msgstr "チャットルーム"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid ""
"Choose a topic that interests you and start talking with the community. "
"<br/> Don't forget to setup your camera and microphone."
msgstr "興味のあるトピックを選択して、コミュニティとの会話を始めましょう。 カメラとマイクのセットアップをお忘れなく。"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Close"
msgstr "閉じる"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Create"
msgstr "作成"

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Create a Room"
msgstr "ルームを作成"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Create one to get conversations going"
msgstr "会話を続けるために１つ作成する"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_uid
msgid "Created by"
msgstr "作成者"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_date
msgid "Created on"
msgstr "作成日"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Discard"
msgstr "破棄"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__display_name
msgid "Display Name"
msgstr "表示名"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Dropdown menu"
msgstr "ドロップダウンメニュー"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Duplicate"
msgstr "複製"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_event
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__event_id
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Event"
msgstr "イベント"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_meeting_room
msgid "Event Meeting Room"
msgstr "イベント ミーティングルーム"

#. module: website_event_meet
#: model:ir.model.fields.selection,name:website_event_meet.selection__website_event_menu__menu_type__meeting_room
msgid "Event Meeting Room Menus"
msgstr "イベント ミーティングルーム メニュー"

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.action_meeting_room_from_event
msgid "Event Rooms"
msgstr "イベントルーム"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_type
msgid "Event Template"
msgstr "イベントテンプレート"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Full"
msgstr "フル"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Group By"
msgstr "グループ化"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__id
msgid "ID"
msgstr "ID"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_pinned
msgid "Is Pinned"
msgstr "ピンされた"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_published
msgid "Is Published"
msgstr "公開済"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us next time to chat about"
msgstr "次回は以下について話しましょう:"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us there to chat about"
msgstr "以下について話しましょう:"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_lang_id
#, python-format
msgid "Language"
msgstr "言語"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Languages Menu"
msgstr "言語メニュー"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_last_activity
msgid "Last activity"
msgstr "最後のアクティビティ"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Launch a new topic"
msgstr "新しいトピックを開始"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,help:website_event_meet.field_event_type__meeting_room_allow_creation
msgid "Let Visitors Create Rooms"
msgstr "訪問者にルーム作成を許可"

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_0
msgid "Let's talk about wood types for furniture"
msgstr "家具用の木材タイプについて話しましょう"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_capacity
msgid "Max capacity"
msgstr "最大数"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr "ルームに同時に参加した最大人数"

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_tree
msgid "Meeting Room"
msgstr "ミーティングルーム"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_ids
msgid "Meeting rooms"
msgstr "ミーティングルーム"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "メニュータイプ"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "No Room Open"
msgstr "オープンしているルームがありません"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_participant_count
msgid "Participant count"
msgstr "参加者数"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Peak participants"
msgstr "ピーク参加者"

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_1
msgid "Reducing the ecological footprint with wood ?"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
msgid "Reporting"
msgstr "レポーティング"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_is_full
msgid "Room Is Full"
msgstr "ルームは満室です"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_name
msgid "Room Name"
msgstr "ルーム名"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Room Topic"
msgstr "ルームトピック"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_count
msgid "Room count"
msgstr "ルーム数"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Room creation will be available when event starts at"
msgstr "ルーム作成はイベントが以下で開始時に有効です:"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_event_view_form
msgid "Rooms"
msgstr "ルーム"

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
msgid ""
"Rooms allow your event attendees to meet up and chat on different topics."
msgstr "ルームでは、イベントの参加者が集まって、さまざまなトピックについて話すことができます。"

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_1
msgid "Share your tips to reduce your ecological footprint using wood."
msgstr "木材を使ってエコロジカルフットプリントを減らすヒントを共有しましょう。"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Short Summary"
msgstr "要約"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Start a topic"
msgstr "トピックを始める"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__summary
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Summary"
msgstr "サマリ"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Target People"
msgstr "ターゲット"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "The event"
msgstr "イベント"

#. module: website_event_meet
#: code:addons/website_event_meet/controllers/website_event_main.py:0
#, python-format
msgid ""
"The event %s starts on %s (%s). \n"
"Join us there to chat about \"%s\" !"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__website_url
msgid "The full URL to access the document through the website."
msgstr "ウェブサイトを介してドキュメントにアクセスするために完全なURLです。"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__name
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Topic"
msgstr "トピック"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Unpublished"
msgstr "未公開"

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_2
msgid ""
"Venez partager vos meubles préférés et l'utilisation que vous en faites."
msgstr ""
"Venez partager vos meubles préférés et l'utilisation que vous en faites."

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_published
msgid "Visible on current website"
msgstr "現在のウェブサイトで見える状態"

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_2
msgid "Vos meubles préférés ?"
msgstr "Vos meubles préférés ?"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Want to create your own discussion room ?"
msgstr ""

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_website_event_menu
msgid "Website Event Menu"
msgstr "ウェブサイトイベントメニュー"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_url
msgid "Website URL"
msgstr "ウェブサイトURL"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_2
msgid "client(s)"
msgstr "顧客"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Accountants"
msgstr "例: 会計士"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Finance"
msgstr "例: 財務"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Let's talk about Corporate Finance"
msgstr "例: 会社財務について話そう"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_1
msgid "ecologist(s)"
msgstr "エコロジスト"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"is over.\n"
"                        <br/>"
msgstr ""
"は終了しました。\n"
"                        <br/>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""
"は終了しました。\n"
"                <br/>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "participant(s)"
msgstr "参加者"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts in"
msgstr "で始まる"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts on"
msgstr "に始まります"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "to have a chat with us!"
msgstr "私たちとチャットで話しましょう！"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_0
msgid "wood expert(s)"
msgstr "木の専門家"
