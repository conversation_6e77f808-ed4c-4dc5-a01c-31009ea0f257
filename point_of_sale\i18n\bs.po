# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * point_of_sale
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:48+0000\n"
"PO-Revision-Date: 2018-10-08 06:48+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:910
#: code:addons/point_of_sale/models/pos_order.py:922
#, python-format
msgid " REFUND"
msgstr "POVRAT"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "$ 3.12"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "$ 4.40"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "$ 4.50"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "$ 8.50"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1477
#, python-format
msgid "% discount"
msgstr "% popust"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1606
#: code:addons/point_of_sale/static/src/xml/pos.xml:1646
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:89
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "(update)"
msgstr "(osvježi)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "+ Transactions"
msgstr "+ Transakcije"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_snippets
msgid "<b>Set Custom Image...</b>"
msgstr "<b>Postavi sliku...</b>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_snippets
msgid "<i class=\"fa fa-eye-slash\"/>Hide"
msgstr "<i class=\"fa fa-eye-slash\"/>Sakri"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to manage tax-included prices"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tour.js:24
#, python-format
msgid ""
"<p>Click to start the point of sale interface. It <b>runs on tablets</b>, "
"laptops, or industrial hardware.</p><p>Once the session launched, the system"
" continues to run without an internet connection.</p>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Journal Entries</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Order Reference</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Payment Methods</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid ""
"<span class=\"o_form_label\">The system will open all cash registers, so "
"that you can start recording payments. We suggest you to control the opening"
" balance of each register, using their CashBox tab.</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid ""
"<span class=\"o_stat_text\">Put</span>\n"
"                                <span class=\"o_stat_text\">Money In</span>"
msgstr ""
"<span class=\"o_stat_text\">Stavi</span>\n"
"                                <span class=\"o_stat_text\">depozit</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Set Closing Balance</span>"
msgstr "<span class=\"o_stat_text\">Unesi zaključni saldo blagajne</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Set Opening Balance</span>"
msgstr "<span class=\"o_stat_text\">Unesi početni saldo blagajne</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid ""
"<span class=\"o_stat_text\">Take</span>\n"
"                                <span class=\"o_stat_text\">Money Out</span>"
msgstr ""
"<span class=\"o_stat_text\">Izvadi</span>\n"
"                                <span class=\"o_stat_text\">depozit</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>Preskoči ekran pregleda</b></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "<span class=\"pos-change_amount\">$ 0.86</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "<span class=\"pos-change_title\">Change</span>"
msgstr "<span class=\"pos-change_title\">Kusur</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "<span class=\"total-amount-formatting\">TOTAL</span>"
msgstr "<span class=\"total-amount-formatting\">UKUPNO</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "<span id=\"total-amount\" class=\"pos_total-amount\">$ 469.14</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "<span>$ 470.00</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "<span>Cash (USD):</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Cash Balance</span>"
msgstr "<span>Saldo blagajne</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Date</span>"
msgstr "<span>Zadnji zaključni datum</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Izvještavanje</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>Pogledaj</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Company</strong>:<br/>"
msgstr "<strong>Kompanija</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Ending Balance</strong>:<br/>"
msgstr "<strong>Završni saldo</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Journal</strong>:<br/>"
msgstr "<strong>Dnevnik knjiženja</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Opening Date</strong>:<br/>"
msgstr "<strong>Datum otvaranja</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Starting Balance</strong>:<br/>"
msgstr "<strong>Početni saldo</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Statement Name</strong>:<br/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Total</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>User</strong>:<br/>"
msgstr "<strong>Korisnik</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "= Theoretical Closing Balance"
msgstr "= Teoretski saldo zatvaranja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2035
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "? Klikom na \"Potvrdi\" verifikovati će te plaćanje."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1275
#, python-format
msgid "A Customer Name Is Required"
msgstr "Ime kupca je obavezno"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_security_pin
msgid ""
"A Security PIN used to protect sensible functionality in the Point of Sale"
msgstr ""
"Sigurnosni PIN koji se koristi za zaštitu osjetljivih funkcionalnosti  Point"
" of Sale-a"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "Jedinstveni sesijski broj za narudžbe"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr "Kratki tekst koji će biti umetnut u podnožje štampanih računa."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr "Kratki tekst koji će biti umetnut kao zaglavlje u štampanim računima."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1645
#, python-format
msgid "ABC"
msgstr "ABC"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a credit card reader"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Accounting"
msgstr "Računovodstvo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting Information"
msgstr "Računovodstvene informacije"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "Prodajni dnevnik koršćen za kreiranje faktura."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
msgid "Accounting journal used to post sales entries."
msgstr "Knjigovodstveni dnevnik korišten za knjiženje stavki prodaje."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
msgid "Active"
msgstr "Aktivan"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1885
#, python-format
msgid "Add Tip"
msgstr "Dodaj bakšiš"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add a custom message to header and footer"
msgstr "Dodaj prilagođenu poruku u zaglavlju i podnožju"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:487
#, python-format
msgid "Add a customer"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_account_journal_form
msgid "Add a new payment method"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:414
#: code:addons/point_of_sale/static/src/xml/pos.xml:511
#, python-format
msgid "Address"
msgstr "Adresa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Advanced Currencies Setup"
msgstr ""

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Alias"
msgstr "Alias"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:244
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:250
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "Sve stavke prodaje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow cashier to reprint receipts"
msgstr "Dozvoli kaseru da ponovno odštampa račun"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow global discounts on orders"
msgstr "Dozvoli globalne popuste na narudžbama"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "Amount"
msgstr "Iznos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "Autorizovani iznos razlike"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "Uk. iznos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:1244
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "Interna identifikacija POS-a."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:161
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "Druga sesisija je već otvorena za ovaj POS."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2027
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "Dali ste sigurni da kupac ovo želi da plati"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_variants_action
msgid "Attribute Values"
msgstr "Vrijednost atributa"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
msgid "Attributes"
msgstr "Atributi"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr ""
"Automatski generisana sesija za narudžbe bez sesije, ignorisano u "
"ograničenjima"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "Automatsko štampanje računa"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "Automatski otvori ladicu za novac"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__journal_ids
msgid "Available Payment Methods"
msgstr "Raspoloživi načini plaćanja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Available Pricelists"
msgstr "Dostupni cijenovnici"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_search_inherit_point_of_sale
msgid "Available for Point of Sale"
msgstr "Dostupno za POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "Dostupno u POS-u"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "Prosječna Cijena"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:296
#: code:addons/point_of_sale/static/src/xml/pos.xml:646
#, python-format
msgid "Back"
msgstr "Natrag"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:179
#: code:addons/point_of_sale/static/src/xml/pos.xml:625
#: code:addons/point_of_sale/static/src/xml/pos.xml:1192
#, python-format
msgid "Backspace"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_ids
msgid "Bank Statement"
msgstr "Izvod banke"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_cashbox
msgid "Bank Statement Cashbox"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Stavka izvoda banke"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:380
#: code:addons/point_of_sale/static/src/xml/pos.xml:443
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
#, python-format
msgid "Barcode"
msgstr "Barkod"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenklatura bakodova"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1345
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__barcode_scanner
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Barcode Scanner"
msgstr "Barkod čitač"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "Osnovica"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Bills &amp; Receipts"
msgstr "Fakture i Računi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:964
#, python-format
msgid "Button"
msgstr "Dugme"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:847
#, python-format
msgid "CHANGE"
msgstr "KUSUR"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:479
#: code:addons/point_of_sale/static/src/xml/pos.xml:1062
#: code:addons/point_of_sale/static/src/xml/pos.xml:1079
#: code:addons/point_of_sale/static/src/xml/pos.xml:1108
#: code:addons/point_of_sale/static/src/xml/pos.xml:1125
#: code:addons/point_of_sale/static/src/xml/pos.xml:1145
#: code:addons/point_of_sale/static/src/xml/pos.xml:1197
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "Otkaži"

#. module: point_of_sale
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Cancelled"
msgstr "Otkazan"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2016
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "Nemogu vratiti kusur bez metode plaćanja"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_in
msgid "Cash Box In"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_out
msgid "Cash Box Out"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:318
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
#, python-format
msgid "Cash Control"
msgstr "Kontrola gotovine"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "Gotovinski dnevnik"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_id
msgid "Cash Register"
msgstr "Kasa"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:142
#, python-format
msgid "Cash control can only be applied to cash journals."
msgstr "Kontrola gotovine se može samo primjeniti na gotovinski dnevnik."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cashbox_line
msgid "CashBox Line"
msgstr "Stavka blagajne"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.account_cashbox_line_view_tree
msgid "Cashbox balance"
msgstr "Saldo kase"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Cashdrawer"
msgstr "Ladica za novac"

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Cashier"
msgstr "Kaser"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Categories"
msgstr "Kategorije"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:187
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "Kategorija"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Category Pictures"
msgstr "Slike kategorija"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:552
#, python-format
msgid "Change"
msgstr "Promjeni"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:105
#, python-format
msgid "Change Cashier"
msgstr "Promjena kasera"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1220
#, python-format
msgid "Change Customer"
msgstr "Promjena kupca"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1885
#, python-format
msgid "Change Tip"
msgstr "Promjena bakšiša"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:708
#: code:addons/point_of_sale/static/src/xml/pos.xml:1535
#: code:addons/point_of_sale/static/src/xml/pos.xml:1696
#, python-format
msgid "Change:"
msgstr "Za vratiti:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr ""
"Označite ako želite da grupišete proizvode ove kategorije na narudžbama "
"POS-a"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_journal__journal_user
msgid ""
"Check this box if this journal define a payment method that can be used in a"
" point of sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_by
msgid ""
"Check this if you want to group the Journal Items by Product while closing a"
" Session."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2076
#, python-format
msgid "Check your internet connection and try again."
msgstr "Provjerite internet konekciju i pokušajte ponovno."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "Podređene kategorije"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Choose a pricelist for the Point Of Sale"
msgstr "Odabir cijenovnika na POS-u"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Choose a specific tax regime at the order depending on the kind of customer "
"(tax exempt, onsite vs. takeaway, etc.)."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Choose among several tax regimes when processing an order"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:351
#: code:addons/point_of_sale/static/src/xml/pos.xml:352
#, python-format
msgid "City"
msgstr "Grad"

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Client"
msgstr "Klijent"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:105
#, python-format
msgid "Client Screen Connected"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:102
#, python-format
msgid "Client Screen Disconnected"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:463
#, python-format
msgid "Client Screen Unsupported. Please upgrade the IoT Box"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:99
#, python-format
msgid "Client Screen Warning"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:824
#: code:addons/point_of_sale/static/src/js/chrome.js:832
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: point_of_sale
#: selection:pos.session,state:0
msgid "Closed & Posted"
msgstr "Zatvoreno i proknjiženo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:346
#, python-format
msgid "Closing ..."
msgstr "Zatvaranje ..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: selection:pos.session,state:0
msgid "Closing Control"
msgstr "Kontrolni obračun"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
msgid "Closing Date"
msgstr "Datum zaključenja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "Kompanija"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Konfiguracija"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Configuration for journal entries of PoS orders"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid ""
"Configure at least one Point of Sale to be able to sell through the PoS "
"interface."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:829
#: code:addons/point_of_sale/static/src/xml/pos.xml:1059
#, python-format
msgid "Confirm"
msgstr "Portvrdi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:456
#, python-format
msgid "Connected, Not Owned"
msgstr "Spojeno, niste vlasnik"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:76
#, python-format
msgid "Connecting to Proxy"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:121
#, python-format
msgid "Connecting to the IoT Box"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Continue Selling"
msgstr "Nastavi prodavati"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Control cash box at opening and closing"
msgstr "Kontroliši kasu prilikom zatvaranja i otvaranja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1378
#, python-format
msgid "Could Not Read Image"
msgstr "Slika se ne može učitati"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:359
#, python-format
msgid "Country"
msgstr "Država"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Create a new PoS config"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Credit Card"
msgstr "Kreditna kartica"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Credit Card Reader"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Currencies"
msgstr "Valute"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "Trenutna sesija"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "Trenutni status sesije"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1896
#: code:addons/point_of_sale/static/src/xml/pos.xml:141
#: code:addons/point_of_sale/static/src/xml/pos.xml:145
#: code:addons/point_of_sale/static/src/xml/pos.xml:671
#: code:addons/point_of_sale/static/src/xml/pos.xml:676
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "Kupac"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Customer Display"
msgstr "Displej kupca"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Customer Facing Display"
msgstr "Displej za kupca"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:586
#: code:addons/point_of_sale/models/pos_order.py:637
#, python-format
msgid "Customer Invoice"
msgstr "Faktura kupca"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__customer_facing_display_html
msgid "Customer facing display content"
msgstr "Sadržaj displeja za kupca"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "Kupci"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "Kontrolna ploča"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1335
#, python-format
msgid "Debug Window"
msgstr "Prozor za ispravljanje grešaka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_cashbox_lines_ids
msgid "Default Balance"
msgstr "Zadani saldo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__account_id
msgid "Default Debit Account"
msgstr "Zadani dugovni konto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "Zadana fiskalna pozicija"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Default Opening"
msgstr "Zadano otvaranje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Default Pricelist"
msgstr "Zadani cjenovnik"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Zadani porez prodaje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "Zadani prodajni PDV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_snippets
msgid "Default company logo"
msgstr "Zadani logo kompanije"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__barcode_nomenclature_id
msgid ""
"Defines what kind of barcodes are available and how they are assigned to "
"products, customers and cashiers."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "Odgoda ovjere"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:576
#: code:addons/point_of_sale/static/src/xml/pos.xml:589
#: code:addons/point_of_sale/static/src/xml/pos.xml:1393
#: code:addons/point_of_sale/static/src/xml/pos.xml:1405
#, python-format
msgid "Delete"
msgstr "Obriši"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1355
#, python-format
msgid "Delete Paid Orders"
msgstr "Obriši plaćene narudžbe"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:264
#, python-format
msgid "Delete Paid Orders ?"
msgstr "Obriši plaćene narudžbe ?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1356
#, python-format
msgid "Delete Unpaid Orders"
msgstr "Obriši ne plaćene narudžbe"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:274
#, python-format
msgid "Delete Unpaid Orders ?"
msgstr "Obriši ne plaćene narudžbe ?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1436
#, python-format
msgid "Delete order"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1223
#, python-format
msgid "Deselect Customer"
msgstr "Ukloni kupca"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
#: model:product.product,name:point_of_sale.desk_organizer
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_pad
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:56
#, python-format
msgid "Destroy Current Order ?"
msgstr "Obriši trenutnu narudžbu ?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Difference"
msgstr "Razlika"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr ""
"Razlika između teoretskog salda zatvaranja i stvarnog salda zatvaranja."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:168
#, python-format
msgid "Disc"
msgstr "Pop."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "Pop:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:461
#, python-format
msgid "Disconnected"
msgstr "Diskonektovan"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "Popust (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "Obavještenje popusta"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:790
#: code:addons/point_of_sale/static/src/xml/pos.xml:929
#: code:addons/point_of_sale/static/src/xml/pos.xml:1507
#, python-format
msgid "Discount:"
msgstr "Popust:"

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Discounted Product"
msgstr "Sniženi proizvod"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:856
#, python-format
msgid "Discounts"
msgstr "Popusti"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1336
#, python-format
msgid "Dismiss"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_display_categ_images
msgid "Display Category Pictures"
msgstr "Prikaži slike kategorija"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_invoice__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_saledetails__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Display pictures of product categories"
msgstr "Prikaži slike kategorija proizvoda"

#. module: point_of_sale
#: code:addons/point_of_sale/models/digest.py:16
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid "Do you want to open cash registers?"
msgstr "Da li želite da otvorite kasu?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Don't turn this option on if you take orders on smartphones or tablets. Such"
" devices already benefit from a native keyboard."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Done"
msgstr "Gotovo"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_vkeyboard
msgid ""
"Don’t turn this option on if you take orders on smartphones or tablets. \n"
" Such devices already benefit from a native keyboard."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1023
#, python-format
msgid "Download"
msgstr "Preuzimanje"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1358
#, python-format
msgid "Download Paid Orders"
msgstr "Preuzmi plaćene narudžbe"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1360
#, python-format
msgid "Download Unpaid Orders"
msgstr "Preuzmi neplaćene narudžbe"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1020
#, python-format
msgid "Download error"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:550
#, python-format
msgid "Due"
msgstr "Krajnji rok"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:409
#, python-format
msgid "Edit"
msgstr "Uredi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1338
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Electronic Scale"
msgstr "Elektronska vaga"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:372
#: code:addons/point_of_sale/static/src/xml/pos.xml:423
#, python-format
msgid "Email"
msgstr "E-Mail"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1998
#, python-format
msgid "Empty Order"
msgstr "Prazna narudžba"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:453
#, python-format
msgid "Empty Serial/Lot Number"
msgstr "Prazan Serijsk/Lot broj"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid "Enable barcode scanning with a remotely connected barcode scanner."
msgstr "Omogući barkod skeniranje sa udaljeno spojenim skenerom."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "Omogući integraciju sa elektronskom vagom"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_payment_terminal
msgid "Enables Payment Terminal integration."
msgstr "Omogući integraciju sa terminalom za plaćanje"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,help:point_of_sale.field_pos_order__invoice_group
msgid "Enables invoice generation from the Point of Sale."
msgstr "Omogući generisanje faktura iz POS-a"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "Datum Završetka"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "End of Session"
msgstr "Kraj sesije"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "Završni saldo"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:13
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "Greška! Ne možete kreirati rekurzivne kategorije."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1307
#, python-format
msgid "Error: Could not Save Changes"
msgstr "Greška: nemogu se sačuvati izmjene"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:244
#, python-format
msgid ""
"Error: The Point of Sale User must belong to the same company as the Point "
"of Sale. You are probably trying to load the point of sale as an "
"administrator in a multi-company setup, with the administrator account set "
"to the wrong company."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1357
#, python-format
msgid "Export Paid Orders"
msgstr "Izvezi plaćene narudžbe"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1359
#, python-format
msgid "Export Unpaid Orders"
msgstr "Izvezi ne plaćene narudžbe"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Dodatne informacije"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1211
#, python-format
msgid "Finished Importing Orders"
msgstr "Završen uvoz narudžbi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position"
msgstr "Fiskalna pozicija"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position per Order"
msgstr "Fiskalna pozicija po narudžbi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Positions"
msgstr "Fiskalne pozicije"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Footer"
msgstr "Podnožje"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "Za neprecizne industrijske ekrane na dodir."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "General Information"
msgstr "Opšte informacije"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Generation of your order references"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Get one journal item per product rather than one journal item per receipt "
"line. This works for any anonymous order. If the customer is set on the "
"order, one journal item is created for each receipt line. This option is "
"recommended for an easy review of your journal entries when managing lots of"
" orders."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Give customer rewards, free samples, etc."
msgstr "Dajte kupcima nagrade, besplatne primjerke, itd."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Daje redosljed sekvenci kada se prikazuje lista kategorija proizvoda."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
msgid "Global Discounts"
msgstr "Globalni popust"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Grupiši po"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_by
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Group Journal Items"
msgstr "Grupiši stavke dnevnika"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "Grupiši proizvode u POS-u"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1372
#, python-format
msgid "Hardware Events"
msgstr "Hardverski događaji"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1367
#, python-format
msgid "Hardware Status"
msgstr "Status hardwera"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "Koristi kontrolu novčanica"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Header"
msgstr "Zaglavlje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
msgid "Header & Footer"
msgstr "Zaglavlje i Podnožje"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:207
#, python-format
msgid "Home"
msgstr "Početna"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_invoice__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_saledetails__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/popups.js:115
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "BITNO: Izvještaj greške iz Odoo POS-a"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IP Address"
msgstr "IP Adresa"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image
msgid "Image"
msgstr "Slika"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1361
#, python-format
msgid "Import Orders"
msgstr "Uvezi narudžbe"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "Unaprijedi navigaciju za neprecizne industrijske ekrane na dodir."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: selection:pos.session,state:0
msgid "In Progress"
msgstr "U Toku"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:568
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "Da bi ste obrisali prodaju, mora biti nova ili otkazana."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Inactive"
msgstr "Neaktivan"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:277
#, python-format
msgid "Incorrect Password"
msgstr "Netačna lozinka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
msgid "Initial Category"
msgstr "Početna kategorija"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "Integrisano kartično plaćanje"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "Interne kategorije"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "Interne zabilješke"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1273
#, python-format
msgid "Invalid product lot"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Inventory"
msgstr "Skladište"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:684
#: model:ir.actions.report,name:point_of_sale.pos_invoice_report
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__invoice_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "Faktura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Invoice Journal"
msgstr "Dnevnik fakturisanja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Invoiced"
msgstr "Fakturisano"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__invoice_group
msgid "Invoicing"
msgstr "Fakturisanje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IotBox / Hardware Proxy"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Je Bar/Restoran"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_bank_statement__account_id
msgid "It acts as a default account for debit amount"
msgstr "Uobičajeni konto za dugovni iznos"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_tax.py:23
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
msgid "Journal"
msgstr "Dnevnik knjiženja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
msgid "Journal Entry"
msgstr "Dnevnički zapis"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.led_lamp
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "Veliki klizači"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_invoice____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_saledetails____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "Iznos gotovine zadnje sesije"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "Datum zatvaranja zadnje sesije"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "Led Lamp"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.letter_tray
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "R.br."

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:33
#, python-format
msgid "List of Cash Registers"
msgstr "Popis gotovinskih kasa"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:532
#: code:addons/point_of_sale/static/src/xml/pos.xml:38
#, python-format
msgid "Loading"
msgstr "Učitavanje"

#. module: point_of_sale
#: selection:barcode.rule,type:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__location_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__location_id
msgid "Location"
msgstr "Lokacija"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "Broj sekvence prijave"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:304
#, python-format
msgid "Login as a Manager"
msgstr "Prijavi se kao menadžer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:8
#: code:addons/point_of_sale/static/src/xml/pos.xml:736
#: code:addons/point_of_sale/static/src/xml/pos.xml:901
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Lot"
msgstr "Lot"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "Naziv lota"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:2383
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "Lot/Serijski broj je neophodan"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "Lot/Serijski broj"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_loyalty
msgid "Loyalty Program"
msgstr "Program lojalnosti"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Loyalty program to use for this point of sale."
msgstr "Program lojalnosti koji se koristi za ovaj POS."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.magnetic_board
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Napravi plaćanje"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Manager"
msgstr "Upravitelj"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_medium
msgid "Medium-sized image"
msgstr "Slika srednje veličine"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__image_medium
msgid ""
"Medium-sized image of the category. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""
"Slika kategorije srednje veličine. Automatski je promijenjena veličina na "
"128x128px sliku, sa zadržanim proporcijama, Ovo polje koristite u pogledima "
"forme ili kanban pogledima."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:553
#, python-format
msgid "Method"
msgstr "Metoda"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
#: model:product.product,name:point_of_sale.product_product_consumable
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
msgid "Miscellaneous"
msgstr "Ostalo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
#: model:product.product,name:point_of_sale.monitor_stand
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_sales_price
msgid "Multiple Product Prices"
msgstr ""

#. module: point_of_sale
#: selection:res.config.settings,pos_pricelist_setting:0
msgid "Multiple prices per product (e.g. customer segments, currencies)"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "My Sales"
msgstr "Moje prodaje"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:419
#: code:addons/point_of_sale/static/src/xml/pos.xml:428
#: code:addons/point_of_sale/static/src/xml/pos.xml:437
#: code:addons/point_of_sale/static/src/xml/pos.xml:448
#: code:addons/point_of_sale/static/src/xml/pos.xml:457
#: code:addons/point_of_sale/static/src/xml/pos.xml:466
#, python-format
msgid "N/A"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:339
#: code:addons/point_of_sale/static/src/xml/pos.xml:510
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#, python-format
msgid "Name"
msgstr "Naziv:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "New"
msgstr "Novi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "Nova sesija"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1433
#, python-format
msgid "New order"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.newspaper_rack
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:709
#, python-format
msgid "Next Order"
msgstr "Sljedeća narudžba"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:1132
#, python-format
msgid "No Taxes"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:147
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr "Nije pronađen nijedan zapis gotovine. Nemogu snimiti povrat novca."

#. module: point_of_sale
#: code:addons/point_of_sale/report/pos_invoice.py:25
#, python-format
msgid "No link to an invoice for %s."
msgstr "Nema veze do fakture za %s."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "No orders found"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:24
#, python-format
msgid "No sequence defined on the journal"
msgstr "Sekvenca nije definisana za dnevnk knjiženja"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid "No sessions found"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2129
#: code:addons/point_of_sale/static/src/xml/pos.xml:361
#, python-format
msgid "None"
msgstr "Ništa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "Nije fakturisano"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Notes"
msgstr "Zabilješke"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "Broj kopija"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "Odoo Logo"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:403
#, python-format
msgid "Offline"
msgstr "Van mreže"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:328
#, python-format
msgid "Offline Orders"
msgstr "Offline narudžbe"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:988
#: code:addons/point_of_sale/static/src/xml/pos.xml:1002
#: code:addons/point_of_sale/static/src/xml/pos.xml:1016
#: code:addons/point_of_sale/static/src/xml/pos.xml:1045
#: code:addons/point_of_sale/static/src/xml/pos.xml:1076
#: code:addons/point_of_sale/static/src/xml/pos.xml:1105
#: code:addons/point_of_sale/static/src/xml/pos.xml:1122
#: code:addons/point_of_sale/static/src/xml/pos.xml:1200
#: code:addons/point_of_sale/static/src/xml/pos.xml:1236
#, python-format
msgid "Ok"
msgstr "U redu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:454
#, python-format
msgid "One or more product(s) required serial/lot number."
msgstr "Jedan ili više proizvoda zahtjevaju unos serijskog/lot broja."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1364
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Open"
msgstr "Otvori"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_open_statement
msgid "Open Cash Register"
msgstr "Otvori registar kasu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:695
#: code:addons/point_of_sale/static/src/xml/pos.xml:1374
#, python-format
msgid "Open Cashbox"
msgstr "Otvori kasu"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Open POS Menu"
msgstr "Otvori POS meni"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid "Open Registers"
msgstr "Otvorene kase"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Open Session"
msgstr "Otvori sesiju"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.act_pos_open_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid "Open Statements"
msgstr "Otvorene stavke"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Opening Balance"
msgstr "Početni saldo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: selection:pos.session,state:0
msgid "Opening Control"
msgstr "Kontrola otvaranja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
msgid "Opening Date"
msgstr "Datum otvaranja"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.account_cashbox_line_action
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Opening/Closing Values"
msgstr "Vrijednosti Otvaranja/Zatvaranja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation Type"
msgstr "Tip operacije"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation type used to record product pickings"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:311
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "Nalog"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:1958
#: code:addons/point_of_sale/static/src/js/models.js:1998
#, python-format
msgid "Order "
msgstr "Narudžba"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "Datum narudžbe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
msgid "Order IDs Sequence"
msgstr "Sekvenca ID-ova narudžbe"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Order Interface"
msgstr "Interfejs narudžbe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "Stavke naloga"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
msgid "Order Ref"
msgstr "Ref. narudžbe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:598
#, python-format
msgid "Order is not paid."
msgstr "Narudžba nije plaćena."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "Stavke narudžbe"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1352
#: model:ir.actions.act_window,name:point_of_sale.act_pos_session_orders
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Orders"
msgstr "Narudžbe"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "Analiza narudžbi"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.account_journal_action_point_of_sale
msgid "POS Journals"
msgstr "POS Dnevnici"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:348
#, python-format
msgid "POS Order %s"
msgstr "POS Narudžba %s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "Stavka pos računa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "Stavke prodaje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "POS Stavke"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "POS Stavke narudžbe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_pricelist_setting
msgid "POS Pricelists"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "POS Sales"
msgstr "POS Prodaje"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:357
#, python-format
msgid "POS order line %s"
msgstr "POS stavka narudžbe %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_statement_id
msgid "POS statement"
msgstr "POS Izvodi"

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Package"
msgstr "Paket"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Paid"
msgstr "Plaćeno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "Izvorna kategorija"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:335
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:405
#, python-format
msgid "Partner logo"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:274
#, python-format
msgid "Password ?"
msgstr "Šifra ?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:151
#, python-format
msgid "Pay"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "Naplati naručeno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:150
#: code:addons/point_of_sale/static/src/xml/pos.xml:649
#: code:addons/point_of_sale/wizard/pos_payment.py:68
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "Plaćanje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "Datum plaćanja"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_account_journal_form
#: model:ir.ui.menu,name:point_of_sale.menu_action_account_journal_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Payment Methods"
msgstr "Metoda plaćanja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__journal_id
msgid "Payment Mode"
msgstr "Način plaćanja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "Referenca plaćanja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_payment_terminal
msgid "Payment Terminal"
msgstr "Platni terminal"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_account_journal_form
msgid ""
"Payment methods are defined by accounting journals having the\n"
"                field <i>PoS Payment Method</i> checked. In order to be useable\n"
"                from the touchscreen interface, you must set the payment method\n"
"                on the <i>Point of Sale</i> configuration."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Payment methods available"
msgstr "Dostupne metode plaćanja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__statement_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Payments"
msgstr "Plaćanja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:939
#, python-format
msgid "Payments:"
msgstr "Plaćanja:"

#. module: point_of_sale
#: model:ir.filters,name:point_of_sale.filter_orders_per_session
msgid "Per session"
msgstr "Po sesiji"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:376
#: code:addons/point_of_sale/static/src/xml/pos.xml:432
#: code:addons/point_of_sale/static/src/xml/pos.xml:512
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1453
#, python-format
msgid "Phone:"
msgstr "Telefon:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_id
msgid "Picking"
msgstr "Prikupljanje proizvoda"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "Broj prikupljanja"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Picking Errors"
msgstr "Greške prikupljanja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:332
#, python-format
msgid "Picture"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2026
#, python-format
msgid "Please Confirm Large Amount"
msgstr "Molimo da potvrdite veliki iznos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:1874
#, python-format
msgid "Please configure a payment method in your POS."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:363
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr "Molimo definirajte konto prihoda za ovaj proizvod \"%s\" (id:%d)."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:614
#, python-format
msgid "Please provide a partner for the sale."
msgstr "Molimo da odaberete partnera za prodaju."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:533
#, python-format
msgid "Please select a payment method."
msgstr "Molimo izaberite način plaćanja."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2067
#, python-format
msgid "Please select the Customer"
msgstr "Molimo odaberite kupca"

#. module: point_of_sale
#: model:product.category,name:point_of_sale.product_category_pos
msgid "PoS"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
msgid "PoS Categories"
msgstr "POS Kategorije"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pos_categ_id
msgid "PoS Category"
msgstr "POS Kategorija"

#. module: point_of_sale
#: model:stock.picking.type,name:point_of_sale.picking_type_posout
msgid "PoS Orders"
msgstr "PoS Naružbe"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
msgid "Point Of Sale"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_pos
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model:ir.ui.menu,name:point_of_sale.menu_pos_config_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_users_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_bank_journal_form_inherited_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale"
msgstr "POS Kasa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "Point of Sale Analiza"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_id
msgid "Point of Sale Category"
msgstr "Kategorije POS-a"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "Postavke prodajnog mjesta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "Postavke prodajnog mjesta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "Point of Sale menadžerska grupa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
msgid "Point of Sale Name"
msgstr "Naziv prodajnog mjesta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_open_statement
msgid "Point of Sale Open Statement"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "Narudžbe POS-a"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Payment"
msgstr "POS plaćanje"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "Sesija POS-a"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "Point of Sale korisnička grupa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Categories"
msgstr "Pos Kategorije"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "POS Postavke"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
msgid "Pos Order Count"
msgstr "POS broj narudžbi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "POS stavke narudžbe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "POS status sesije"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "POS Korisničko ime sesije"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
msgid "PosBox"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:355
#, python-format
msgid "Postcode"
msgstr "Broj pošte"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Posted"
msgstr "Proknjižen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_precompute_cash
msgid "Prefill Cash Payment"
msgstr "Popuni gotovinsko plaćanje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Prefill amount paid with the exact due amount"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:173
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
#, python-format
msgid "Price"
msgstr "Cijena"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Price Control"
msgstr "Kontrola cijena"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Price Unit"
msgstr "Jedinica cijene"

#. module: point_of_sale
#: selection:res.config.settings,pos_pricelist_setting:0
msgid "Price computed from formulas (discounts, margins, roundings)"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:134
#, python-format
msgid "Price list"
msgstr ""

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Priced Product"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2217
#: code:addons/point_of_sale/static/src/xml/pos.xml:388
#: code:addons/point_of_sale/static/src/xml/pos.xml:461
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Pricelist"
msgstr "Cijenovnik"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Cjenovnik"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Cijene"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:90
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "Ispis"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:716
#: code:addons/point_of_sale/static/src/xml/pos.xml:1375
#, python-format
msgid "Print Receipt"
msgstr "Štampaj račun"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print invoices on customer request"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Štampaj putem Proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:391
#, python-format
msgid "Printer"
msgstr "Štampač"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/devices.js:432
#, python-format
msgid "Printing Error: "
msgstr "Greška štampanja:"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "Proizvod"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product Prices"
msgstr "Cijene proizvoda"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "Količina proizvoda"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Jedinica mjere proizvoda"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "Kategorije jedinica mjera"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1247
#, python-format
msgid "Product image"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product prices on receipts"
msgstr "Cijene proizvoda na računima"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
msgid "Product tips"
msgstr "Bakšiši proizvoda"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Proizvodi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:73
#, python-format
msgid "Proxy Connected"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:82
#, python-format
msgid "Proxy Disconnected"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:79
#, python-format
msgid "Proxy Warning"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_in
msgid "Put Money In"
msgstr "Stavi novac unutra"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:163
#, python-format
msgid "Qty"
msgstr "Količina"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
msgid "Quantity"
msgstr "Količina"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1376
#, python-format
msgid "Read Weighing Scale"
msgstr "Očitaj vagu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tour.js:14
#: code:addons/point_of_sale/static/src/js/tour.js:19
#, python-format
msgid "Ready to launch your <b>point of sale</b>? <i>Click here</i>."
msgstr "Spremni ste da pokrenete svoj <b>POS</b>? <i>Kliknite ovdje</i>."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Real Closing Balance"
msgstr "Stvarni saldo zaključka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
msgid "Receipt Footer"
msgstr "Podnožje računa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
msgid "Receipt Header"
msgstr "Zaglavlje računa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Receipt Printer"
msgstr "Štampač računa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
msgid "Receipt Ref"
msgstr "Ref. Računa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "Spašena sesija"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1370
#, python-format
msgid "Refresh Display"
msgstr "Osvježi displej"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1100
#, python-format
msgid "Remove"
msgstr "Ukloni"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "Izvještavanje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_reprint
msgid "Reprint Receipt"
msgstr "Oštampaj račun ponovno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1342
#, python-format
msgid "Reset"
msgstr "Resetuj"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
msgid "Responsible"
msgstr "Odgovoran"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "Ograniči izmjene cijena na upravitelje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Restrict price modification to managers"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Resume"
msgstr "Nastavi"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:931
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "Vrati proizvode"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "Vraćeno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "Stavka prodaje"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "Detalji prodaje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sales Journal"
msgstr "Dnevnik prodaje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sales Orders"
msgstr "Prodajni nalozi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Salesperson"
msgstr "Prodavač(ica)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:342
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Save"
msgstr "Sačuvaj"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Save this configuration to see and edit the customer display"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:399
#, python-format
msgid "Scale"
msgstr "Vaga"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1348
#, python-format
msgid "Scan"
msgstr "Skeniraj"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1349
#, python-format
msgid "Scan EAN-13"
msgstr "Skeniraj EAN-13"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Skeniraj putem Proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:382
#, python-format
msgid "Scanner"
msgstr "Skener"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:483
#, python-format
msgid "Search Customers"
msgstr "Pretraga kupaca"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:220
#, python-format
msgid "Search Products"
msgstr "Pretraga proizvoda"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "Pretraži prodajne narudžbe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_security_pin
msgid "Security PIN"
msgstr "Sigurnosni PIN"

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_users.py:15
#, python-format
msgid "Security PIN can only contain digits"
msgstr "Sigurnosni pin može da sadrži samo brojeve"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:491
#, python-format
msgid "Select Customer"
msgstr "Odaberi kupca"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:247
#, python-format
msgid "Select User"
msgstr "Odaberi korisnika"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2205
#, python-format
msgid "Select pricelist"
msgstr "Odaberite cijenovnik"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2140
#, python-format
msgid "Select tax"
msgstr "Odaberi porez"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:260
#, python-format
msgid "Selected orders do not have the same session!"
msgstr "Odabrane narudžbe nisu iz iste sesije!"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Sell in several currencies"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1026
#, python-format
msgid "Send by email"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "Broj sekvence"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1099
#, python-format
msgid "Serial/Lot Number"
msgstr "Serijski/Lot broj"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:768
#, python-format
msgid "Served by"
msgstr "Poslužio"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2080
#, python-format
msgid "Server Error"
msgstr "Serverska greška"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:461
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Session"
msgstr "Sesija"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "ID sesija"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1225
#, python-format
msgid "Session ids:"
msgstr "ID-ovi sesije:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Session:"
msgstr "Sesija:"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.act_pos_config_sessions
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "Sesije"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1218
#, python-format
msgid "Set Customer"
msgstr "Postavi kupca"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
msgid "Set Start Category"
msgstr "Postavite početnu kategoriju"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1341
#, python-format
msgid "Set Weight"
msgstr "Postavi težinu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set barcodes to scan products, customer cards, etc."
msgstr "Postavite barkodove da skenirate proizvode, kartice kupaca, itd."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:128
#, python-format
msgid "Set fiscal position"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set shop-specific prices, seasonal discounts, etc."
msgstr "Postavite izdvojene cijene po prodavnici, sezonske popuste, itd."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/pos.web_editor.js:33
#, python-format
msgid "Set your customized advertisement here"
msgstr "Postavite svoje prilagođene reklame ovdje"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Settings"
msgstr "Postavke"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1311
#, python-format
msgid "Shopping cart"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "Prikaži završetak narudžbe kupcima sa udaljeno spojenim ekranima."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:42
#, python-format
msgid "Skip"
msgstr "Preskoči"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "Preskoči pregledni ekran"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:212
#, python-format
msgid "Slash"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.small_shelf
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_small
msgid "Small-sized image"
msgstr "Male slike"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__image_small
msgid ""
"Small-sized image of the category. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:333
#, python-format
msgid "Some orders could not be submitted to"
msgstr "Neke narudžbe nisu mogle biti predate na"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Start Date"
msgstr "Datum početka"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Start selling from a default product category"
msgstr "Započni prodaju sa zadane kategorije proizvoda"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "Početni saldo"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.action_report_account_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "Statement"
msgstr "Izvod"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Statement lines"
msgstr "Stavke izvoda"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Statements"
msgstr "Izvodi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Status"
msgstr "Status"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__stock_location_id
msgid "Stock Location"
msgstr "Lokacije zalihe"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Stock location used for the inventory"
msgstr "Lokacija zalihe koja se koristi za popis"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:347
#: code:addons/point_of_sale/static/src/xml/pos.xml:348
#, python-format
msgid "Street"
msgstr "Ulica"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:818
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
#, python-format
msgid "Subtotal"
msgstr "Podukupno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "Ukupno bez poreza"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "Ukupno bez popusta"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1492
#, python-format
msgid "Subtotal:"
msgstr "Međuzbroj:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1217
#, python-format
msgid "Successfully  imported"
msgstr "Uspješno uvezeno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1216
#, python-format
msgid "Successfully imported"
msgstr "Uspješno uvezeno"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Sum of opening balance and transactions."
msgstr "Suma otvorenih salda i transakcija"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "Ukupna suma"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Summary by Payment Methods"
msgstr "Sažetak po načinima plaćanja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:55
#, python-format
msgid "Synchronisation Connected"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:58
#, python-format
msgid "Synchronisation Connecting"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:61
#, python-format
msgid "Synchronisation Disconnected"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:64
#, python-format
msgid "Synchronisation Error"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:831
#, python-format
msgid "TOTAL"
msgstr "UKUPNO"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_out
msgid "Take Money Out"
msgstr "Izdavanje novca"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_order.py:395
#: code:addons/point_of_sale/static/src/js/screens.js:2159
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "Porez"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "Iznos poreza"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
msgid "Tax Display"
msgstr "Prikaz PDV-a"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:384
#: code:addons/point_of_sale/static/src/xml/pos.xml:452
#, python-format
msgid "Tax ID"
msgstr "Porez"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime
msgid "Tax Regime"
msgstr "Porezni režim"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "Vrijednost izbora poreznog režima"

#. module: point_of_sale
#: selection:pos.config,iface_tax_included:0
msgid "Tax-Excluded Price"
msgstr ""

#. module: point_of_sale
#: selection:pos.config,iface_tax_included:0
msgid "Tax-Included Price"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "Porezi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:953
#: code:addons/point_of_sale/static/src/xml/pos.xml:1321
#, python-format
msgid "Taxes:"
msgstr "Porezi:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:748
#, python-format
msgid "Tel:"
msgstr "Tel:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:551
#, python-format
msgid "Tendered"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:351
#, python-format
msgid "The POS order must have lines when calling this method"
msgstr "POS narudžba mora sadržavati stavke kada pozivate ovu metodu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1040
#, python-format
msgid ""
"The Point of Sale could not find any product, client, employee\n"
"                    or action associated with the scanned barcode."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:242
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:232
#, python-format
msgid ""
"The invoice journal and the point of sale must belong to the same company."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:248
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:284
#, python-format
msgid "The journal type for your payment method should be bank or cash."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:237
#, python-format
msgid ""
"The method payments and the point of sale must belong to the same company."
msgstr ""

#. module: point_of_sale
#: sql_constraint:pos.session:0
msgid "The name of this POS Session must be unique !"
msgstr "Naziv POS sesije mora biti jedinstven!"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "Broj POS narudžbi povezanih sa ovim kupcem"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2075
#, python-format
msgid "The order could not be sent"
msgstr "Narudžba nije mogla bit poslana"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2086
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr "Narudžba nije poslana na server zbog nepoznate greške"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_precompute_cash
msgid ""
"The payment input will behave similarily to bank payment input, and will be "
"prefilled with the exact due amount."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,help:point_of_sale.field_pos_session__config_id
msgid "The physical point of sale you will use."
msgstr "Fizički POS koji ćete koristiti."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_display_categ_images
msgid "The product categories will be displayed with pictures."
msgstr "Kategorije proizvoda će biti prikazane sa slikama"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1379
#, python-format
msgid "The provided file could not be read due to an unknown error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:227
#, python-format
msgid ""
"The sales journal and the point of sale must belong to the same company."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:255
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2081
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "Na serveru se dogodila greška prilikom prijema vaše narudžbe."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:222
#, python-format
msgid ""
"The stock location and the point of sale must belong to the same company."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment journal."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "Teoretski saldo zatvaranja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:117
#, python-format
msgid ""
"There are pending operations that could not be saved into the database, are "
"you sure you want to exit?"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"There are two ways to manage pricelists: 1) Multiple prices per product "
"(e.g. quantity, shop-specific) : must be set in the Sales tab of the product"
" detail form. 2) Price computed from formulas (discounts, margins, rounding)"
" : must be set in the pricelist form."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2017
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_box.py:21
#, python-format
msgid "There is no cash register for this PoS Session"
msgstr "Nema kase za ovu POS Sesiju"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:865
#, python-format
msgid ""
"There is no receivable account defined to make payment for the partner: "
"\"%s\" (id:%d)."
msgstr ""
"Nije definisan konto prihoda da izvršim plaćanje za partnera: \"%s\" "
"(id:%d)."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:863
#, python-format
msgid "There is no receivable account defined to make payment."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1999
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated"
msgstr ""
"Mora postojati barem jedan proizvod kako bi ste mogli verifikovati narudžbu."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This adds the choice of a currency on pricelists."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_cashbox_line__default_pos_id
msgid ""
"This cashbox line is used by default when opening or closing a balance for "
"this point of sale"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_journal__amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__image
msgid ""
"This field holds the image used as image for the cateogry, limited to "
"1024x1024px."
msgstr ""
"ovo polje sadrži sliku koja se koristi za prikazivanje kategorije, "
"ograničeno na 1024x1024px."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:622
#, python-format
msgid ""
"This invoice has been created from the point of sale session: <a href=# "
"data-oe-model=pos.order data-oe-id=%d>%s</a>"
msgstr ""
"Ova faktura je bila kreirana iz POS Sesije: <a href=# data-oe-"
"model=pos.order data-oe-id=%d>%s</a>"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:275
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:265
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
msgid "This product is used as reference on customer receipts."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:726
#, python-format
msgid ""
"This transfer has been created from the point of sale session: <a href=# "
"data-oe-model=pos.order data-oe-id=%d>%s</a>"
msgstr ""
"Prenos je bio kreiran na osnovu POS sesije: <a href=# data-oe-"
"model=pos.order data-oe-id=%d>%s</a>"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:690
#, python-format
msgid "Tip"
msgstr "Bakšiš"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
msgid "Tip Product"
msgstr "Proizvod bakšiša"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_tip
#: model:product.template,name:point_of_sale.product_product_tip_product_template
msgid "Tips"
msgstr "Bakšiš"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "Za zatvoriti"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "Vaganje sa vagom"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:906
#, python-format
msgid ""
"To return product(s), you need to open a session that will be used to "
"register the refund."
msgstr ""
"Da bi ste vratili proizvod(e), prvo morate otvoriti sesiju koju će te "
"koristiti za povrat."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Total"
msgstr "Ukupno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "Ukupno transakcija gotovine"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "Ukupni popust"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "Ukupna Cijena"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:868
#, python-format
msgid "Total Taxes"
msgstr "Ukupno poreza"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total of all paid sales orders"
msgstr "Ukupan iznos svih plaćenih prodajnih narudžbi"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Total of closing cash control lines."
msgstr "Ukupno kontrolnih stavki zatvaranja gotovine."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Total of opening cash control lines."
msgstr "Ukupno kontrolnih stavki otvaranja gotovine."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "Uk. količina"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:967
#: code:addons/point_of_sale/static/src/xml/pos.xml:1320
#: code:addons/point_of_sale/static/src/xml/pos.xml:1514
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "Ukupno:"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:416
#, python-format
msgid "Trade Receivables"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
msgid "Type"
msgstr "Tip"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:179
#, python-format
msgid ""
"Unable to open the session. You have to assign a sales journal to your point"
" of sale."
msgstr ""
"Ne može se otvoriti sesija. Morate prvo dodjeliti prodajni dnevnik vašem "
"POS-u."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:341
#, python-format
msgid "Undo"
msgstr "Vrati"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Unit Product"
msgstr ""

#. module: point_of_sale
#: model:product.product,uom_name:point_of_sale.product_product_consumable
#: model:product.product,uom_name:point_of_sale.product_product_tip
#: model:product.template,uom_name:point_of_sale.product_product_consumable_product_template
#: model:product.template,uom_name:point_of_sale.product_product_tip_product_template
msgid "Unit(s)"
msgstr "kom (komad)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1036
#, python-format
msgid "Unknown Barcode"
msgstr "Nepoznat barkod"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2085
#, python-format
msgid "Unknown Error"
msgstr "Nepoznata greška"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1363
#, python-format
msgid "Unsupported File Format"
msgstr "Nepodržan format datoteke"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Unused"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "Koristi postojeće Lotove/Serijske brojeve"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use a default specific tax regime"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
msgid "Use a pricelist."
msgstr "Koristi cijenovnik."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use a virtual keyboard for touchscreens"
msgstr "Koristi virtuelnu tastaturu za ekran na dodir."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use an integrated hardware setup like"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__journal_user
msgid "Use in Point of Sale"
msgstr "Koristi u POS-u"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model:res.groups,name:point_of_sale.group_pos_user
msgid "User"
msgstr "Korisnik"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "Korisničke oznake"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1456
#, python-format
msgid "User:"
msgstr "Korisnik:"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
msgid "Uuid"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:751
#, python-format
msgid "VAT:"
msgstr "PDV:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1271
#, python-format
msgid "Valid product lot"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:650
#, python-format
msgid "Validate"
msgstr "Odobri"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Validate Closing & Post Entries"
msgstr "Ovjeri zatvaranje i proknjiži stavke"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_vkeyboard
msgid "Virtual KeyBoard"
msgstr "Virtuelna tastatura"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.wall_shelf
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1369
#, python-format
msgid "Weighing"
msgstr "Vaganje"

#. module: point_of_sale
#: selection:barcode.rule,type:0
msgid "Weighted Product"
msgstr "Proizvod za vaganje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.customer_facing_display_html
#: model_terms:pos.config,customer_facing_display_html:point_of_sale.pos_config_main
#: model:product.product,name:point_of_sale.whiteboard_pen
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1293
#: code:addons/point_of_sale/static/src/xml/pos.xml:1476
#, python-format
msgid "With a"
msgstr "Sa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid ""
"You can define another list of available currencies on the\n"
"                                    <i>Cash Registers</i> tab of the"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:558
#, python-format
msgid ""
"You cannot change the partner of a POS order for which an invoice has "
"already been issued."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:34
#, python-format
msgid ""
"You cannot confirm all orders of this session, because they have not the 'paid' status.\n"
"{reference} is in state {state}, total amount: {total}, paid: {paid}"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:152
#, python-format
msgid "You cannot create two active sessions with the same responsible."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/product.py:21
#: code:addons/point_of_sale/models/product.py:38
#, python-format
msgid ""
"You cannot delete a product saleable in point of sale while a session is "
"still opened."
msgstr ""
"Ne možete obrisati proizvod koji se može prodaviti putem POS-a dok još "
"uvijek postoji aktivna otvorena sesija."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:300
#, python-format
msgid ""
"You cannot use the session of another user. This session is owned by %s. "
"Please first close this one to use this point of sale."
msgstr ""
"Ne možete koristiti sesiju drugog korisnika. Ovu sesiju koristi korisnik %s."
" Molimo vas da prvo zatvorite ovu sesiju kako biste mogli koristiti POS."

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:18
#, python-format
msgid ""
"You have to define which payment method must be available in the point of "
"sale by reusing existing bank and cash through \"Accounting / Configuration "
"/ Journals / Journals\". Select a journal and check the field \"PoS Payment "
"Method\" from the \"Point of Sale\" tab. You can also create new payment "
"methods directly from menu \"PoS Backend / Configuration / Payment "
"Methods\"."
msgstr ""
"Morate definisati koji načini plaćanja će biti dostupni na prodajnom mjestu,"
" korištenjem postojećih dnevnika gotovine i bankovnih računa kroz "
"\"Računovodstvo / Postavke /Dnevnici /Dnevnici\". Odaberite dnevnik i "
"označite polje \"POS metoda plaćanja\" na kartici \"Prodajna mjesta\". "
"Također možete kreirati novi način plaćanja direktno iz izbornika \"POS "
"Backend/ Postavke / Načini plaćanja\"."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:879
#, python-format
msgid "You have to open at least one cashbox."
msgstr "Morate otvoriti barem jednu kasu"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:1026
#, python-format
msgid ""
"You have to select a pricelist in the sale form !\n"
"Please set one before choosing a product."
msgstr ""
"Morate odabrati cjenovnik u formi prodaje!\n"
"Molimo postavite jedan prije odabira proizvoda."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:1040
#, python-format
msgid "You have to select a pricelist in the sale form."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:27
#, python-format
msgid "You have to set a Sale Journal for the POS:%s"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_details.py:48
#, python-format
msgid "You have to set a logo or a layout for your company."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_details.py:50
#, python-format
msgid "You have to set your reports's header and footer layout."
msgstr "Morate da postavite zaglavlje i podnožje za izvještaj"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"Morate da definišete proizvod za sve što prodajete preko\n"
"interfejsa POS."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2068
#, python-format
msgid "You need to select the customer before you can invoice an order."
msgstr "Morate prvo da odaberete kupca prije nogo što fakturišete narudžbu."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:167
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "Trebate dodijeliti prodajno mjesto za vašu smjenu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:57
#, python-format
msgid "You will lose any data associated with the current order"
msgstr "Izgubićete podatke vezane za trenutnu narudžbu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1301
#, python-format
msgid "Your Internet connection is probably down."
msgstr "Vjerovatno nemate internet konekciju."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:282
#, python-format
msgid ""
"Your ending balance is too different from the theoretical cash closing "
"(%.2f), the maximum allowed is: %.2f. You can contact your manager to force "
"it."
msgstr ""
"Vaš krajnji saldo zatvaranja je previše različit od teoretskog zatvaranja "
"gotovine (%.2f), maksimalno dozvoljeno je: %.2f. Možete kontaktirati svog "
"menadžera da zatvori."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1312
#, python-format
msgid "Your shopping cart is empty"
msgstr "Vaša korpa je prazna"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:356
#, python-format
msgid "ZIP"
msgstr "Broj pošte"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1285
#, python-format
msgid "at"
msgstr "kod"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1222
#, python-format
msgid "belong to another session:"
msgstr "pripada drugoj sesiji:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1581
#, python-format
msgid "caps lock"
msgstr "caps lock"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1608
#: code:addons/point_of_sale/static/src/xml/pos.xml:1650
#, python-format
msgid "close"
msgstr "zatvori"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1566
#: code:addons/point_of_sale/static/src/xml/pos.xml:1643
#, python-format
msgid "delete"
msgstr "obriši"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1295
#, python-format
msgid "discount"
msgstr "popust"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Company Address, Website"
msgstr "npr.: Adresa kompanije, Web stranica"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "npr.: Pravilo povrata, Hvala na povjerenju!"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/popups.js:109
#, python-format
msgid "error"
msgstr "greška"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:2031
#, python-format
msgid "for an order of"
msgstr "za narudžbu"

#. module: point_of_sale
#: model:product.product,uom_name:point_of_sale.desk_organizer
#: model:product.product,uom_name:point_of_sale.desk_pad
#: model:product.product,uom_name:point_of_sale.led_lamp
#: model:product.product,uom_name:point_of_sale.letter_tray
#: model:product.product,uom_name:point_of_sale.magnetic_board
#: model:product.product,uom_name:point_of_sale.monitor_stand
#: model:product.product,uom_name:point_of_sale.newspaper_rack
#: model:product.product,uom_name:point_of_sale.small_shelf
#: model:product.product,uom_name:point_of_sale.wall_shelf
#: model:product.product,uom_name:point_of_sale.whiteboard
#: model:product.product,uom_name:point_of_sale.whiteboard_pen
#: model:product.product,weight_uom_name:point_of_sale.desk_organizer
#: model:product.product,weight_uom_name:point_of_sale.desk_pad
#: model:product.product,weight_uom_name:point_of_sale.led_lamp
#: model:product.product,weight_uom_name:point_of_sale.letter_tray
#: model:product.product,weight_uom_name:point_of_sale.magnetic_board
#: model:product.product,weight_uom_name:point_of_sale.monitor_stand
#: model:product.product,weight_uom_name:point_of_sale.newspaper_rack
#: model:product.product,weight_uom_name:point_of_sale.product_product_consumable
#: model:product.product,weight_uom_name:point_of_sale.product_product_tip
#: model:product.product,weight_uom_name:point_of_sale.small_shelf
#: model:product.product,weight_uom_name:point_of_sale.wall_shelf
#: model:product.product,weight_uom_name:point_of_sale.whiteboard
#: model:product.product,weight_uom_name:point_of_sale.whiteboard_pen
#: model:product.template,uom_name:point_of_sale.desk_organizer_product_template
#: model:product.template,uom_name:point_of_sale.desk_pad_product_template
#: model:product.template,uom_name:point_of_sale.led_lamp_product_template
#: model:product.template,uom_name:point_of_sale.letter_tray_product_template
#: model:product.template,uom_name:point_of_sale.magnetic_board_product_template
#: model:product.template,uom_name:point_of_sale.monitor_stand_product_template
#: model:product.template,uom_name:point_of_sale.newspaper_rack_product_template
#: model:product.template,uom_name:point_of_sale.small_shelf_product_template
#: model:product.template,uom_name:point_of_sale.wall_shelf_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_pen_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_product_template
#: model:product.template,weight_uom_name:point_of_sale.desk_organizer_product_template
#: model:product.template,weight_uom_name:point_of_sale.desk_pad_product_template
#: model:product.template,weight_uom_name:point_of_sale.led_lamp_product_template
#: model:product.template,weight_uom_name:point_of_sale.letter_tray_product_template
#: model:product.template,weight_uom_name:point_of_sale.magnetic_board_product_template
#: model:product.template,weight_uom_name:point_of_sale.monitor_stand_product_template
#: model:product.template,weight_uom_name:point_of_sale.newspaper_rack_product_template
#: model:product.template,weight_uom_name:point_of_sale.product_product_consumable_product_template
#: model:product.template,weight_uom_name:point_of_sale.product_product_tip_product_template
#: model:product.template,weight_uom_name:point_of_sale.small_shelf_product_template
#: model:product.template,weight_uom_name:point_of_sale.wall_shelf_product_template
#: model:product.template,weight_uom_name:point_of_sale.whiteboard_pen_product_template
#: model:product.template,weight_uom_name:point_of_sale.whiteboard_product_template
msgid "kg"
msgstr "kg"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:336
#, python-format
msgid "not used"
msgstr "ne koristi se"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:294
#: code:addons/point_of_sale/static/src/xml/pos.xml:1216
#, python-format
msgid "paid orders"
msgstr "plaćene narudžbe"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "payment method."
msgstr "način plaćanja."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_order.py:152
#: code:addons/point_of_sale/static/src/xml/pos.xml:1593
#: code:addons/point_of_sale/static/src/xml/pos.xml:1648
#, python-format
msgid "return"
msgstr "vrati"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1594
#: code:addons/point_of_sale/static/src/xml/pos.xml:1605
#, python-format
msgid "shift"
msgstr "pomjeri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1567
#, python-format
msgid "tab"
msgstr "tab"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:286
#: code:addons/point_of_sale/static/src/xml/pos.xml:1217
#, python-format
msgid "unpaid orders"
msgstr "neplaćene narudžbe"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1219
#, python-format
msgid "unpaid orders could not be imported"
msgstr "neplaćene narudžbe nisu mogle biti uvežene"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1221
#, python-format
msgid "were duplicates of existing orders"
msgstr "su duplikati postojeće narudžbe"
