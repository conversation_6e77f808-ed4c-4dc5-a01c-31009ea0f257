# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# xu <PERSON>aro<PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__state
msgid ""
" * Draft: The MO is not confirmed yet.\n"
" * Confirmed: The MO is confirmed, the stock rules and the reordering of the components are trigerred.\n"
" * In Progress: The production has started (on the MO or on the WO).\n"
" * To Close: The production is done, the MO has to be closed.\n"
" * Done: The MO is closed, the stock moves are posted. \n"
" * Cancelled: The MO has been cancelled, can't be confirmed anymore."
msgstr ""
" * 草稿：尚未确认MO。\n"
"* 已确认：已确认MO，将触发库存规则和组件的重订购规则。\n"
"* 进行中：生产已经开始（MO或WO）。\n"
"* 关闭：生产完成后，必须关闭MO。\n"
"* 完成：MO已关闭，库存移动已过帐。\n"
"* 已取消：MO已被取消，无法再确认。"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid " <br/><br/> The components will be taken from <b>%s</b>."
msgstr " <br/><br/>组件将取自<b>%s</b>."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__all_available
msgid " When all components are available"
msgstr " 当所有组件都可用时"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_count
msgid "# Bill of Material"
msgstr "# BOM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__used_in_bom_count
msgid "# BoM Where Used"
msgstr "# BOM 使用"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_ready_count
msgid "# Read Work Orders"
msgstr "# 读取工单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_count
msgid "# Work Orders"
msgstr "# 工单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template__used_in_bom_count
msgid "# of BoM Where is Used"
msgstr "# BOM 使用"

#. module: mrp
#: code:addons/mrp/models/mrp_routing.py:0
#, python-format
msgid "%i work orders"
msgstr "%i 工单"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "%s %s unbuilt in"
msgstr "%s%s拆解于"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "%s (new) %s"
msgstr "%s (新的) %s"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "%s Child MO's"
msgstr "%s 下级 MO's"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "%s cannot be deleted. Try to cancel them before."
msgstr "%s无法被删除。 尝试先取消。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "&amp; Cost"
msgstr "&amp;成本"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "&gt;"
msgstr "&gt;"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_bom_structure
msgid "'Bom Structure - %s' % object.display_name"
msgstr "'Bom结构 - %s' % object.display_name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_finished_product
msgid "'Finished products - %s' % object.name"
msgstr "'完工产品- %s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_production_order
msgid "'Production Order - %s' % object.name"
msgstr "'制造单 - %s' % object.name"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d天(s)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"           可能需要手动动作。"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_leg
#: model_terms:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr "方桌桌脚规格18“x2½”"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ": Insufficient Quantity To Unbuild"
msgstr "：数量不足以拆解"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"管理\" title=\"管理\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"
msgstr "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"暂停\" title=\"暂停\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-play\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"
msgstr "<i class=\"fa fa-play\" role=\"img\" aria-label=\"运行\" title=\"运行\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"
msgstr "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"停止\" title=\"停止\"/>"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your product\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                    上传文件到产品\n"
"                   </p><p>\n"
"                    使用此功能存储任何文件，如图纸或规格。\n"
"                    </p>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"font-weight-bold\">To Produce</span>"
msgstr "<span class=\"font-weight-bold\">生产</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Backorders</span>"
msgstr "<span class=\"o_stat_text\">欠单</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Child MO</span>"
msgstr "<span class=\"o_stat_text\">子MO</span>子"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Load</span>"
msgstr "<span class=\"o_stat_text\">负载</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr "<span class=\"o_stat_text\">损失</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "<span class=\"o_stat_text\">Manufactured</span>"
msgstr "<span class=\"o_stat_text\">制造</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr "<span class=\"o_stat_text\">OEE</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr "<span class=\"o_stat_text\">效能</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">Routing<br/>Performance</span>"
msgstr "<span class=\"o_stat_text\">工艺<br/>效能  </span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr "<span class=\"o_stat_text\">报废</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Source MO</span>"
msgstr "<span class=\"o_stat_text\">源 MO</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "<span><strong>Unit Cost</strong></span>"
msgstr "<span><strong>单位成本</strong></span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr "<span>动作</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "<span>Generate</span>"
msgstr "<span>生成</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_operation_line
msgid "<span>Minutes</span>"
msgstr "<span>分钟</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr "<span>新建</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>Orders</span>"
msgstr "<span>订单</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr "<span>安排订单</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_report_delivery_no_kit_section
msgid "<span>Products not associated with a kit</span>"
msgstr "<span>产品与套件无关联</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr "<span>报告</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr "<span>工单</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>minutes</span>"
msgstr "<span>分钟</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<strong class=\"mr8 oe_inline\">to</strong>"
msgstr "<strong class=\"mr8 oe_inline\">到</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Description:</strong><br/>"
msgstr "<strong>描述：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr "<strong>有效性类别: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Finished Product:</strong><br/>"
msgstr "<strong>完工产品:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr "<strong>是阻塞原因? </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>No. Of Minutes</strong>"
msgstr "<strong>分钟数</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Operation</strong>"
msgstr "<strong>作业</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity to Produce:</strong><br/>"
msgstr "<strong>待生产数量:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr "<strong>原因: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Responsible:</strong><br/>"
msgstr "<strong>负责人:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source Document:</strong><br/>"
msgstr "<strong>源文档:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Start Date: </strong>"
msgstr "<strong>开始日期: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>停止日期: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr "<strong>工作中心</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Workcenter: </strong>"
msgstr "<strong>工作中心:</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "？这可能会导致库存不一致。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "A BoM of type kit is used to split the product into its components."
msgstr "BoM类型套件用于将产品拆分为其组件。"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "A Manufacturing Order is already done or cancelled."
msgstr "制造订单已经完成或者被取消."

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid ""
"A product with a kit-type bill of materials can not have a reordering rule."
msgstr "具有套件类型BOM的产品不能具有重新订购规则。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__access_token
msgid "Access Token"
msgstr "访问令牌"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_rule__action
msgid "Action"
msgstr "动作"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction
msgid "Action Needed"
msgstr "需要采取动作"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__active
#: model:ir.model.fields,field_description:mrp.field_mrp_document__active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__active
msgid "Active"
msgstr "启用"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_ids
msgid "Activities"
msgstr "活动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常装饰"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr "添加描述…"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Add by-products to bills of materials. This can be used to get several "
"finished products as well. Without this option you only do: A + B = C. With "
"the option: A + B = C + D."
msgstr "添加副产品到BOM。 这也可以用来获得几个完工产品。 没有这个选项，您只能这样做：A + B = C。选项：A + B = C + D."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add quality checks to your work orders"
msgstr "增加质检到工单"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_tag
msgid "Add tag for the workcenter"
msgstr "为工作中心添加标签"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Administrator"
msgstr "管理员"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr "全部"

#. module: mrp
#: code:addons/mrp/controller/main.py:0
#, python-format
msgid "All files uploaded"
msgstr "已上载所有文件"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_line_bom_qty_zero
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs !"
msgstr ""
"所有产品数量必须大于或等于0。\n"
"数量为0的行可以用作可选行。\n"
"如果要管理BOM上的额外产品，则应安装mrp_byproduct模块！"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Allow manufacturing users to modify quantities to consume, without the need "
"for prior approval"
msgstr "允许生产用户修改消耗的数量，而不需要事先批准"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr "允许为组件创建新的批号/序列号"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__flexible
msgid "Allowed"
msgstr "允许"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reserve_visible
msgid "Allowed to Reserve Production"
msgstr "允许预留生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr "允许取消预留生产"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__warning
msgid "Allowed with warning"
msgstr "允许但警告"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative Workcenters"
msgstr "替代工作中心"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid ""
"Alternative workcenters that can be substituted to this one in order to "
"dispatch production"
msgstr "可以替代此工作中心进行生产的其他工作中心"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid ""
"An unbuild order is used to break down a finished product into its "
"components."
msgstr "拆解单用于将成品分解为其组件。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Apply"
msgstr "应用"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "Apply on Variants"
msgstr "应用于变体"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Approve"
msgstr "批准"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr "已归档"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_assign_serial_numbers_production
msgid "Assign Serial Numbers"
msgstr "分配序列号"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "At the creation of a Manufacturing Order."
msgstr "在创建制造订单时。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "At the creation of a Stock Transfer."
msgstr "在库存调拨创建时。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Attached To"
msgstr "附加于"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__local_url
msgid "Attachment URL"
msgstr "附件网址"

#. module: mrp
#. openerp-web
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/static/src/js/mrp_bom_report.js:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
#, python-format
msgid "Attachments"
msgstr "附件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__attachments_count
msgid "Attachments Count"
msgstr "附件数"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__availability
msgid "Availability"
msgstr "可用性"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr "可用性损失"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__available
#, python-format
msgid "Available"
msgstr "可用"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "Avatar"
msgstr "形象"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_product__produce_delay
#: model:ir.model.fields,help:mrp.field_product_template__produce_delay
msgid ""
"Average lead time in days to manufacture this product. In the case of multi-"
"level BOM, the manufacturing lead times of the components will be added."
msgstr "制造此产品的平均交货时间（以天计）。在多级BOM的情况下，将添加组件的交货时间会一并计算。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__variant_bom_ids
msgid "BOM Product Variants"
msgstr "BOM产品变体"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "BOM Product Variants needed to apply this line."
msgstr "BOM产品变体需要应用到此行。"

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_bom_structure
msgid "BOM Structure Report"
msgstr "BOM结构报告"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_line_ids
msgid "BOM lines of the referred bom"
msgstr "参考BOM中的BOM行"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder_line
msgid "Backorder Confirmation Line"
msgstr "欠单确认行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_backorder_line_ids
msgid "Backorder Confirmation Lines"
msgstr "欠单确认明细行"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Backorder MO"
msgstr "欠单MO"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Backorder MO's"
msgstr "欠单MO的"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__backorder_sequence
msgid "Backorder Sequence"
msgstr "欠单序号"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__backorder_sequence
msgid ""
"Backorder sequence, if equals to 0 means there is not related backorder"
msgstr "欠单顺序，如果等于0表示没有相关的欠单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Barcode"
msgstr "条码"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode_batch
msgid "Based on"
msgstr "基于"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Bill of Material"
msgstr "BOM"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "BOM明细行"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr "BOM明细行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_bom_id
msgid "Bill of Material used on the Production Order"
msgstr "生产单上使用的BOM"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_ids
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Bill of Materials"
msgstr "BOM"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__mo_bom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_bom_id
msgid ""
"Bill of Materials allow you to define the list of required components to "
"make a finished product."
msgstr "材料明细表允许您定义制造成品所需的组件清单。"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr "BOM"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""
"BOM可定义制造完成产品所需原的材料，\n"
"包括生产订单或产品包装。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr "阻塞"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr "阻塞工作中心"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__blocked
msgid "Blocked"
msgstr "阻塞"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked Time"
msgstr "阻塞时间"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked hours over the last month"
msgstr "过去一月阻塞的时数"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__name
msgid "Blocking Reason"
msgstr "阻塞原因"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM"
msgstr "BOM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_line_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "BoM Components"
msgstr "BOM组件"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Cost"
msgstr "BOM成本"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__bom_line_id
msgid "BoM Line"
msgstr "BOM行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__bom_line_ids
msgid "BoM Lines"
msgstr "BOM明细行"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "BoM Structure"
msgstr "BOM结构"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#: model:ir.actions.client,name:mrp.action_report_mrp_bom
#, python-format
msgid "BoM Structure & Cost"
msgstr "BOM结构与成本"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__type
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "BoM Type"
msgstr "BOM类型"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "BoM line product %s should not be the same as BoM product."
msgstr "BoM明细中的产品%s不应与BoM产品相同。"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_bolt
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr "螺栓"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
msgid "By-Products"
msgstr "副产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_id
msgid "By-product"
msgstr "副产品"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "By-product %s should not be the same as BoM product."
msgstr "副产品%s不应该与 BoM 产品相同。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__byproduct_id
msgid "By-product line that generated the move in a manufacturing order"
msgstr "在制造订单中产生库存移动的副产品明细"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__byproduct_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__byproduct_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "By-products"
msgstr "副产品"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "By-products cost shares must be positive."
msgstr "副产品的成本分摊必须是正数。"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_byproduct
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_byproduct_form_view
msgid "Byproduct"
msgstr "副产品"

#. module: mrp
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Byproducts"
msgstr "副产品"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any production location."
msgstr "找不到任何制造位置 。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Cancel"
msgstr "取消"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__cancel
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__cancel
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Cancelled"
msgstr "已取消"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Cannot delete a manufacturing order in done state."
msgstr "无法删除状态为完成的制造订单。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__capacity
msgid "Capacity"
msgstr "产能"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__loss_type
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Category"
msgstr "类别"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr "变更产品数量"

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Production Qty"
msgstr "变更生产数量"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr "变更待生产数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Check availability"
msgstr "检查可用性"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__checksum
msgid "Checksum/SHA1"
msgstr "校验和/SHA1"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_stop
msgid "Cleanup Time"
msgstr "清理时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__code
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view_kanban
msgid "Code"
msgstr "代号"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__color
msgid "Color"
msgstr "颜色"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__color
msgid "Color Index"
msgstr "颜色索引"

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "公司"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "公司"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Component"
msgstr "组件"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Component Lots must be unique for mass production. Please review reservation"
" for:\n"
msgstr "对于批量生产，组件批次必须是唯一的。 请审查以下预订:\n"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability
msgid "Component Status"
msgstr "组件状态"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Component of Draft MO"
msgstr "草稿状态MO的组件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Components"
msgstr "组件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability_state
msgid "Components Availability State"
msgstr "组件可用性状态"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_src_id
msgid "Components Location"
msgstr "组件位置"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__priority
msgid ""
"Components will be reserved first for the MO with the highest priorities."
msgstr "将首先为具有最高优先级的MO预留组件。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__auto
msgid "Compute based on tracked time"
msgstr "根据跟踪时间进行计算"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_computed_on
msgid "Computed on last"
msgstr "最后计算"

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Configuration"
msgstr "配置"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Confirm"
msgstr "确认"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__confirm_cancel
msgid "Confirm Cancel"
msgstr "确认取消"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Confirmed"
msgstr "已确认"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "Consume"
msgstr "消耗"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_consumed_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Consumed"
msgstr "已消耗"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr "消耗的拆解明细行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr "消耗的拆解单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Consumed Products"
msgstr "消耗的产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__operation_id
msgid "Consumed in Operation"
msgstr "消耗在作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_production__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__consumption
msgid "Consumption"
msgstr "消耗"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_consumption_warning
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Consumption Warning"
msgstr "消耗警告"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_production__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "计量单位转换只能基于转换比例对同一个类别进行转换。"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_field_one2many_with_copy.js:0
#, python-format
msgid "Copy Existing Operations"
msgstr "复制现有的作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_copy_to_bom_tree_view
msgid "Copy selected operations"
msgstr "复制选定的作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__cost_share
#: model:ir.model.fields,field_description:mrp.field_stock_move__cost_share
msgid "Cost Share (%)"
msgstr "成本分摊(%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__costs_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__costs_hour
msgid "Cost per hour"
msgstr "每小时成本"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr "成本信息"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_backorder_count
msgid "Count of linked backorder"
msgstr "缺货的欠单数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Create Backorder"
msgstr "创建欠单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr "创建组件的新批号/序列号"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a Backorder"
msgstr "创建一个欠单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid ""
"Create a backorder if you expect to process the remaining products later. Do"
" not create a backorder if you will not process the remaining products."
msgstr "如果您希望以后再处理剩余产品，请创建一个欠单。 如果您将不处理剩余产品，请不要创建欠单。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "Create a new manufacturing order"
msgstr "创建制造订单"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Create a new operation"
msgstr "创建一个新作业"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Create a new work center"
msgstr "创建新的工作中心"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_workcenter_report
msgid "Create a new work orders performance"
msgstr "创建工单效能"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create backorder"
msgstr "创建欠单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "为您的质量检查创建可定制的工作表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__created_production_id
msgid "Created Production Order"
msgstr "创建生产订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_uid
msgid "Created by"
msgstr "创建人"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_date
msgid "Created on"
msgstr "创建时间"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Creates a new serial/lot number"
msgstr "创建新序列号/批次号码"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Creation"
msgstr "创建日期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"当前产品数量。 \n"
"对单一库存位置来说，包括了此位置或其任何子位置所存储的产品。 \n"
"对单一库存来说，包括了此仓位置置或其任何子位置所存储的产品。  \n"
"另外，这包括了所有'内部'类型的任何库存位置所存储的产品。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_producing
msgid "Currently Produced Quantity"
msgstr "当前的已生产数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_description_variants
msgid "Custom Description"
msgstr "自定义说明"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__db_datas
msgid "Database Data"
msgstr "数据库数据"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Date"
msgstr "日期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_planned_finished
msgid "Date at which you plan to finish the production."
msgstr "您安排完成生产的日期。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_planned_start
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_date
msgid "Date at which you plan to start the production."
msgstr "您安排开始生产的日期。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_start
msgid "Date of the WO"
msgstr "WO的日期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_finished
msgid "Date when the MO has been close"
msgstr "MO关闭日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_deadline
msgid "Deadline"
msgstr "截止日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr "默认时长"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr "默认制造提前期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_consumption_warning_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "所有库存作业的默认单位。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"                bill of materials and manufacturing orders."
msgstr ""
"定义您希望使用的组件和完工产品\n"
"                BOM和制造订单。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "定义资源调度"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__consumption
msgid ""
"Defines if you can consume more or less components than the quantity defined on the BoM:\n"
"  * Allowed: allowed for all manufacturing users.\n"
"  * Allowed with warning: allowed for all manufacturing users with summary of consumption differences when closing the manufacturing order.\n"
"  * Blocked: only a manager can close a manufacturing order when the BoM consumption is not respected."
msgstr ""
"定义您可以消耗的物料比BOM上定义的数量多还是少：\n"
"*允许：允许所有制造用户。\n"
"*允许但警告：允许所有制造用户在关闭制造订单时汇总消耗差异。\n"
"*阻止：在不遵守BoM消耗的情况下，只有经理可以关闭制造订单。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_type
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_type
msgid "Defines if you want to use a PDF or a Google Slide as work sheet."
msgstr "定义是否要将PDF或Google幻灯片用作工作表。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__ready_to_produce
msgid ""
"Defines when a Manufacturing Order is considered as ready to be started"
msgstr "定义制造订单启动时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delay_alert_date
msgid "Delay Alert Date"
msgstr "延迟警报日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Delete"
msgstr "删除"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delivery_count
msgid "Delivery Orders"
msgstr "交货单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__description
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__description
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "说明"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__note
msgid "Description of the Work Center."
msgstr "工作中心的说明。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr "工作中心说明..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_dest_id
msgid "Destination Location"
msgstr "目的位置"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unbuild_id
msgid "Disassembly Order"
msgstr "拆解单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Discard"
msgstr "丢弃"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_document__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_serial_mass_produce
msgid "Display the serial mass product wizard action moves"
msgstr "显示序列号批量产品向导的动作"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lot_ids
msgid "Display the serial number shortcut on the moves"
msgstr "显示库存移动的序列号快捷方式"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "Do you confirm you want to unbuild"
msgstr "您确认要拆解吗"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Document"
msgstr "文件"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Documentation"
msgstr "文档"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__is_done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Done"
msgstr "完成"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Download"
msgstr "下载"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__draft
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__draft
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Draft"
msgstr "草稿"

#. module: mrp
#: model:product.product,name:mrp.product_product_drawer_drawer
#: model:product.template,name:mrp.product_product_drawer_drawer_product_template
msgid "Drawer Black"
msgstr "黑色抽屉"

#. module: mrp
#: model:product.product,name:mrp.product_product_drawer_case
#: model:product.template,name:mrp.product_product_drawer_case_product_template
msgid "Drawer Case Black"
msgstr "黑色抽屉柜"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_drawer_drawer
#: model_terms:product.template,description:mrp.product_product_drawer_drawer_product_template
msgid "Drawer on casters for great usability."
msgstr "可用性极佳的脚轮抽屉。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Dropdown menu"
msgstr "下拉菜单"

#. module: mrp
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "Duplicate Serial Numbers (%s)"
msgstr "重复的序列号（%s）。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr "时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Duration (minutes)"
msgstr "时长（分钟）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode
msgid "Duration Computation"
msgstr "时长计算"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_percent
msgid "Duration Deviation (%)"
msgstr "时长偏差(%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_unit
msgid "Duration Per Unit"
msgstr "每单元时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Edit"
msgstr "编辑"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_type
msgid "Effectiveness"
msgstr "有效性"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_type
msgid "Effectiveness Category"
msgstr "有效性类别"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_end
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_finished
msgid "End Date"
msgstr "终止日期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "确保可追溯到仓库中的产品。"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_documents_controller_mixin.js:0
#, python-format
msgid "Error"
msgstr "错误"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s) occurred on the manufacturing order(s):"
msgstr "制造订单(多个)发生异常情况(多个)："

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s):"
msgstr "异常(多个):"

#. module: mrp
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "Existing Serial Numbers (%s)"
msgstr "现有序列号（%s）"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Exp %s"
msgstr "预计%s"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__expected
msgid "Expected"
msgstr "预计"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_duration_expected
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_expected
msgid "Expected Duration"
msgstr "预计时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Expected Duration (minutes)"
msgstr "预计持续时间（分钟）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__expected_qty
msgid "Expected Quantity"
msgstr "预计的数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__duration_expected
msgid "Expected duration (in minutes)"
msgstr "预计时长(分钟)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__datas
msgid "File Content (base64)"
msgstr "文件内容(base64)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__raw
msgid "File Content (raw)"
msgstr "文件内容(raw)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__file_size
msgid "File Size"
msgstr "文件大小"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Files attached to the product"
msgstr "附加到产品的文件"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr "筛选"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__done
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Finished"
msgstr "已完工"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__order_finished_lot_ids
msgid "Finished Lot/Serial Number"
msgstr "完工批次/序列号"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_finished_ids
msgid "Finished Moves"
msgstr "完工移动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__finished_move_line_ids
msgid "Finished Product"
msgstr "完工产品"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_finished_product
msgid "Finished Product Label (PDF)"
msgstr "完工产品标签(PDF)"

#. module: mrp
#: model:ir.actions.report,name:mrp.label_manufacture_template
msgid "Finished Product Label (ZPL)"
msgstr "完工产品标签(ZPL）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_finished_ids
msgid "Finished Products"
msgstr "完工产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_dest_id
msgid "Finished Products Location"
msgstr "完工产品位置"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__consumption
msgid "Flexible Consumption"
msgstr "灵活消耗"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "字体完美的图标，例如fa-tasks"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Force"
msgstr "强制"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"预测数量(计算为手上数量 - 出库 + 入库) \n"
"对于单一库存位置来说，这包括了存储在此位置及其子位置的货物。\n"
"对于单一库存来说，这包括了存储在此库存的库存位置及其子位置的货物。\n"
"否则，这包括存储在任何“内部”类型的任何库存位置的货物。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Forecasted"
msgstr "预测"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__forecasted_issue
msgid "Forecasted Issue"
msgstr "预测的问题"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "From"
msgstr "从"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr "全部生产力"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr "未来活动"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr "基本信息"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Generate Serial Numbers"
msgstr "生成序列号"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
msgid "Get statistics about the work orders duration related to this routing."
msgstr "获取有关与此工艺路线相关的工单工期的统计信息。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__priority
msgid "Gives the sequence order when displaying a list of MRP documents."
msgstr "在显示MRP文件列表时给出序号顺序。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__sequence
msgid "Gives the sequence order when displaying a list of bills of material."
msgstr "输入序号用于BOM列表排序."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr "指定编号，用于显示多个工作中心时排序。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr "显示工作中心列表时显示顺序。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__sequence
msgid "Gives the sequence order when displaying."
msgstr "用于显示顺序."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__google_slide
msgid "Google Slide"
msgstr "谷歌幻灯片"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Google Slide Link"
msgstr "谷歌幻灯片链接"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Group By"
msgstr "分组"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr "分组于..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr "分组于..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_produced
msgid "Has Been Produced"
msgstr "已生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__has_kits
msgid "Has Kits"
msgstr "有套件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_production__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_message
msgid "Has Message"
msgstr "有消息"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__2
msgid "High"
msgstr "高"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "History"
msgstr "历史"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Hours"
msgstr "小时"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__id
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__id
msgid "ID"
msgstr "ID"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "指示异常活动的图标."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr "如果定义了产品变体，那么BOM仅用于本产品。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_unread
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_production__message_unread
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__propagate_cancel
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr "如果选中这里。当(从下一个需求单生成的)工序被取消或分割，与从工序相关的其他工序也会被取消或分割"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__active
msgid ""
"If the active field is set to False, it will allow you to hide the bills of "
"material without removing it."
msgstr "如设为未启用，可以不删除就隐藏BOM。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr "如设置为未启用，不需删除就可以隐藏。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_height
msgid "Image Height"
msgstr "图像高度"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_src
msgid "Image Src"
msgstr "图像来源"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_width
msgid "Image Width"
msgstr "图像宽度"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Image is a link"
msgstr "图像为链接"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_immediate_production
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__immediate_production_id
msgid "Immediate Production"
msgstr "立即生产"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_immediate_production_line
msgid "Immediate Production Line"
msgstr "立即生产明细"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__immediate_production_line_ids
msgid "Immediate Production Lines"
msgstr "立即生产明细"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Immediate Production?"
msgstr "立即生产？"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Immediate production?"
msgstr "立即生产？"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Impacted Transfer(s):"
msgstr "受影响的调拨:"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "Import Template for Bills of Materials"
msgstr "BOM的导入模板"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Impossible to plan the workorder. Please check the workcenter "
"availabilities."
msgstr "无法安排工单。 请检查工作中心的可用性。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__progress
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__progress
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "In Progress"
msgstr "进行中"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__index_content
msgid "Indexed Content"
msgstr "已索引的内容"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_deadline
msgid ""
"Informative date allowing to define when the manufacturing order should be "
"processed at the latest to fulfill delivery on time."
msgstr "可以定义何时最迟应处理制造订单以按时交货的日期信息。"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
msgid "Inventory Moves"
msgstr "库存移动"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr "库存移动，您必须在此工单扫描批次号码"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__is_kits
#: model:ir.model.fields,field_description:mrp.field_product_template__is_kits
msgid "Is Kits"
msgstr "是套件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_locked
msgid "Is Locked"
msgstr "是锁定"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__manual
msgid "Is a Blocking Reason"
msgstr "是阻塞理由"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "Is a Blocking Reason?"
msgstr "是阻塞原因?"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__public
msgid "Is public document"
msgstr "这是公开文件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_user_working
msgid "Is the Current User Working"
msgstr "当前用户正在工作吗"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "It has already been unblocked."
msgstr "已经解除阻塞."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"It is not possible to unplan one single Work Order. You should unplan the "
"Manufacturing Order instead in order to unplan all the linked operations."
msgstr "无法取消安排一个工单。您应该取消安排制造订单，以便取消安排所有链接的作业。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_planned
msgid "Its Operations are Planned"
msgstr "已安排其作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__json_popover
msgid "JSON data for the popover widget"
msgstr "弹出式窗口小部件的JSON数据"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__key
msgid "Key"
msgstr "键"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__description_bom_line
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__phantom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Kit"
msgstr "套件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_document____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder____last_update
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__last_working_user_id
msgid "Last user that worked on this work order."
msgstr "上一个在此工单工作的用户."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__late
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Late"
msgstr "迟到"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr "最近的活动"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late MO or Late delivery of components"
msgstr "延迟MO或延迟交付组件"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__components_availability
msgid ""
"Latest component availability status for this MO. If green, then the MO's "
"readiness status is ready, as per BOM configuration."
msgstr "该MO的最新组件可用性状态。如果是绿色，那么根据BOM配置，该MO的就绪状态为就绪。"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_wood_ply
#: model_terms:product.template,description:mrp.product_product_wood_ply_product_template
msgid "Layers that are stick together to assemble wood panels."
msgstr "粘在一起组装木板的层。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__leave_id
msgid "Leave"
msgstr "离开"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr "消耗申报明细"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Lines need to be deleted, but can not as you still have some quantities to "
"consume in them. "
msgstr "有消耗数量存在，无法删除数据. "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__location_id
msgid "Location"
msgstr "位置"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_id
msgid "Location where the product you want to unbuild is."
msgstr "您想要拆解产品的库位是。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_src_id
msgid "Location where the system will look for components."
msgstr "系统将寻找组件的位置。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "系统存储成品的位置."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_dest_id
msgid ""
"Location where you want to send the components resulting from the unbuild "
"order."
msgstr "您要将拆解订单产生的组件发送到的位置。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lock"
msgstr "锁定"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Lock the manufacturing order to prevent changes to what has been consumed or"
" produced."
msgstr "锁定制造订单以防止更改已消耗或生产的产品。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr "损失原因"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_production_lot
msgid "Lot/Serial"
msgstr "批次/序列号"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__lot_producing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__finished_lot_id
msgid "Lot/Serial Number"
msgstr "批次/序列号码"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__lot_id
msgid "Lot/Serial Number of the product to unbuild."
msgstr "要拆解的产品批号/序列号。"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Lots/Serial Numbers"
msgstr "批次/序列号码"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__1
msgid "Low"
msgstr "低"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_backorder_id
msgid "MO Backorder"
msgstr "MO欠单"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "MO Generated by %s"
msgstr "%s生成的MO"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reservation_state
msgid "MO Readiness"
msgstr "MO 准备就绪"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr "MRP 工单"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss_type
msgid "MRP Workorder productivity losses"
msgstr "MRP工单生产力损失"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_main_attachment_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_main_attachment_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "按订单生成"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Make sure enough quantities of these components are reserved to carry on "
"production:\n"
msgstr "确保保留足够数量的这些部件以继续生产。\n"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Makes confirmed manufacturing orders locked rather than unlocked by default."
" This only applies to new manufacturing orders, not previously created ones."
msgstr "默认情况下使已确认的制造订单锁定而不是解锁。 仅适用于新的制造订单，不适用于以前创建的订单。"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr "管理工单作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Manual Duration"
msgstr "人工时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "Manuf. Lead Time"
msgstr "制造前置时间"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_steps
#: model:ir.model.fields.selection,name:mrp.selection__stock_rule__action__manufacture
#: model:stock.location.route,name:mrp.route_warehouse0_manufacture
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_stock_rule
#, python-format
msgid "Manufacture"
msgstr "制造"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__mrp_one_step
#, python-format
msgid "Manufacture (1 step)"
msgstr "制造(1 步)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_mto_pull_id
msgid "Manufacture MTO Rule"
msgstr "制造MTO规则"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_pull_id
msgid "Manufacture Rule"
msgstr "制造规则"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "Manufacture Security Lead Time"
msgstr "制造安全提前期"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__normal
msgid "Manufacture this product"
msgstr "制造此产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "Manufacture to Resupply"
msgstr "制造补给"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__mrp_product_qty
#: model:ir.model.fields,field_description:mrp.field_product_template__mrp_product_qty
msgid "Manufactured"
msgstr "已生产"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "Manufactured Products"
msgstr "制造的产品"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufactured in the last 365 days"
msgstr "在过去一年内制造的"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__code__mrp_operation
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#, python-format
msgid "Manufacturing"
msgstr "制造"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#: model:ir.model.fields,field_description:mrp.field_product_product__produce_delay
#: model:ir.model.fields,field_description:mrp.field_product_template__produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__manufacturing_lead
#, python-format
msgid "Manufacturing Lead Time"
msgstr "制造前置时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manu_type_id
msgid "Manufacturing Operation Type"
msgstr "制造作业类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Manufacturing Order"
msgstr "制造订单"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.actions.act_window,name:mrp.mrp_production_report
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model:ir.ui.menu,name:mrp.menu_mrp_production_report
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr "制造订单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr "确认状态的制造订单."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr "制造准备就绪"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr "制造参考"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""
"所有的制造订单作业都可以排到工作中心，安排制造安排到工作中心里去。 这工作中心是什么意思呢？工作中心它可以是\n"
" 工人和/或机器的统称，意思是工作也可以叫一个工作中心，一台车床可以叫一个工作中心，它们用来做成本核算，排安排，产能安排等。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                They can be defined via the configuration menu."
msgstr ""
"所有的制造订单作业都可以排到工作中心，安排制造安排到工作中心里去。 这工作中心是什么意思呢？工作中心它可以是\n"
" 工人和/或机器的统称，意思是工作也可以叫一个工作中心，一台车床可以叫一个工作中心，它们用来做成本核算，排安排，产能安排等。\n"
"您可以通过配置菜单来设置并定义它。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reservation_state
msgid ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"            * Ready: The material is available to start the production.\n"
"            * Waiting: The material is not available to start the production.\n"
msgstr ""
"根据材料清单的配置，该MO的制造准备就绪。\n"
"            * 准备好了。该材料可用于开始生产。\n"
"            * 等待中。该材料不能开始生产。\n"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_mark_done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mark as Done"
msgstr "标记为完成"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mass Produce"
msgstr "批量生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr "主生产调度"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Material Availability"
msgstr "物料可用性"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_ids
msgid "Messages"
msgstr "消息"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__mimetype
msgid "Mime Type"
msgstr "MIME 类型"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "最小库存规则"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Minutes"
msgstr "分钟"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Miscellaneous"
msgstr "杂项"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__mo_ids
msgid "Mo"
msgstr "Mo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_byproduct_ids
msgid "Move Byproduct"
msgstr "移动副产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "待追踪的产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_consumption_warning_line_ids
msgid "Mrp Consumption Warning Line"
msgstr "Mrp消耗警告明细"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_group__mrp_production_ids
msgid "Mrp Production"
msgstr "MRP生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_count
msgid "Mrp Production Count"
msgstr "Mrp生产计数"

#. module: mrp
#: model:ir.actions.server,name:mrp.production_order_server_action
msgid "Mrp: Plan Production Orders"
msgstr "Mrp：安排生产订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__name
msgid "Name"
msgstr "名称"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "New"
msgstr "新建"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Next Activity"
msgstr "下一个活动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一个活动日历事件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__next_work_order_id
msgid "Next Work Order"
msgstr "下一工单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "No Backorder"
msgstr "没有欠单"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "No bill of materials found. Let's create one!"
msgstr "还没有BOM。 让我们创建一个！"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "No data available."
msgstr "无数据可用."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "No data yet!"
msgstr "还没有数据耶！"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "No manufacturing order found. Let's create one."
msgstr "还没有制造订单。 让我们创建一个。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid "No product found. Let's create one!"
msgstr "现在还没有产品， 我们先创建一个!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment"
msgstr "该设备无生产力损失"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "No unbuild order found"
msgstr "没有拆解单"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid "No work orders to do!"
msgstr "没有待办的工单！"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid ""
"No workorder currently in progress. Click to mark work center as blocked."
msgstr "没有生产的工单。单击以将工作中心标记为阻塞。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__normal
msgid "Normal"
msgstr "正常"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Not Available"
msgstr "不可用"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_scrap__workorder_id
msgid "Not to restrict or prefer quants, but informative."
msgstr "不限制或偏好数量，但内容丰富。"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid ""
"Note that archived work center(s): '%s' is/are still linked to active Bill "
"of Materials, which means that operations can still be planned on it/them. "
"To prevent this, deletion of the work center is recommended instead."
msgstr "请注意，已归档的工作中心：'%s'仍然与活动的BOM相联系，这意味着仍然可以对它/它们进行作业安排。为了防止这种情况，建议删除该工作中心。"

#. module: mrp
#: code:addons/mrp/models/product.py:0 code:addons/mrp/models/product.py:0
#, python-format
msgid ""
"Note that product(s): '%s' is/are still linked to active Bill of Materials, "
"which means that the product can still be used on it/them."
msgstr "注意!! 产品: '%s' 仍然链接到有效的物料清单, 这意味著该产品仍可用于它/它们."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of Actions"
msgstr "动作数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr "延迟的制造订单数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr "制造订单等待的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr "制造订单的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_child_count
msgid "Number of generated MO"
msgstr "生成的MO数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要动作消息数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__capacity
msgid ""
"Number of pieces (in product UoM) that can be produced in parallel  (at the "
"same time) at this work center. For example: the capacity is 5 and you need "
"to produce 10 units, then the operation time listed on the BOM will be "
"multiplied by two. However, note that both time before and after production "
"will only be counted once. "
msgstr ""
"此工作中心可并行（同时）生产的件数（以产品计量单位计）。 例如：产能为 5，需要生产 10 台，则 BOM 上列出的作业时间将乘以 2。 "
"但是请注意，生产前后的时间都只会计算一次. "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_qty
msgid ""
"This should be the smallest quantity that this product can be produced in. "
"If the BOM contains operations, make sure the work center capacity is "
"accurate. "
msgstr "这应该是该产品可以生产的最小数量.。如果BOM包含工序，请确保工作中心产能准确. "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_source_count
msgid "Number of source MO"
msgstr "源MO数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_unread_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_unread_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr "OEE"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee_target
msgid "OEE Target"
msgstr "OEE 目标"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee
msgid "Oee"
msgstr "OEE"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "On Hand"
msgstr "在手"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Operation"
msgstr "作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__operation_id
msgid "Operation To Consume"
msgstr "待加工的作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_type_id
msgid "Operation Type"
msgstr "作业类型"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Operation define that need to be done to realize a Work Order.\n"
"                Each operation is done at a specific Work Center and has a specific duration."
msgstr ""
"作业定义实现工单需要完成的工作。\n"
" 每个作业都在特定的工作中心完成，并且具有特定的持续时间。"

#. module: mrp
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__allowed_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
#, python-format
msgid "Operations"
msgstr "作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Done"
msgstr "完成作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Planned"
msgstr "作业已安排"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Operations Search Filters"
msgstr "作业搜索过滤器"

#. module: mrp
#: model:ir.actions.report,name:mrp.label_production_order
msgid "Order Label"
msgstr "订单标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__orderpoint_id
msgid "Orderpoint"
msgstr "订货点"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__order_ids
msgid "Orders"
msgstr "订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "原始（未优化，未调整大小）附件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_production
msgid "Original Production Quantity"
msgstr "原始生产数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee_target
msgid "Overall Effective Efficiency Target in percentage"
msgstr "总体有效效率目标百分比"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr "整体设备效率"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr "整体设备效率，基于最近月份"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time"
msgstr "整体设备效率：无工作或阻塞时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_id
msgid "Parent BoM"
msgstr "父级 BoM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__parent_product_tmpl_id
msgid "Parent Product Template"
msgstr "父产品模板"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_consumption_warning_id
msgid "Parent Wizard"
msgstr "父向导"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr "粘贴您的Google幻灯片的网址。 确保对文档的访问是公开的。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Pause"
msgstr "暂停"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Pending"
msgstr "待定"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__performance
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Performance"
msgstr "效能"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr "效能损失"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__performance
msgid "Performance over the last month"
msgstr "最近一月效能"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pick Components"
msgstr "捡取组件"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pick components and then manufacture"
msgstr "捡取组件并制造"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm
msgid "Pick components and then manufacture (2 steps)"
msgstr "选择组件然后制造（2个步骤）"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm_sam
#, python-format
msgid "Pick components, manufacture and then store products (3 steps)"
msgstr "捡取组件制造，然后储存产品 (3 步)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_mto_pull_id
msgid "Picking Before Manufacturing MTO Rule"
msgstr "在按MTO制造订单规则之前拣货"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_type_id
msgid "Picking Before Manufacturing Operation Type"
msgstr "在制造前拣货的作业类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_route_id
msgid "Picking Before Manufacturing Route"
msgstr "在制造路线前拣货"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "拣货类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_ids
msgid "Picking associated to this manufacturing order"
msgstr "与此制造订单相关的拣货"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_loc_id
msgid "Picking before Manufacturing Location"
msgstr "在制造位置前拣货"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Plan"
msgstr "安排"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr "安排订单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr "根据预测安排制造或采购订单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planned"
msgstr "已安排"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Planned Date"
msgstr "安排的日期"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Planned at the same time as other workorder(s) at %s"
msgstr "与其他工单安排时间重复:%s"

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr "计划"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planning Issues"
msgstr "计划问题"

#. module: mrp
#: model:product.product,name:mrp.product_product_plastic_laminate
#: model:product.template,name:mrp.product_product_plastic_laminate_product_template
msgid "Plastic Laminate"
msgstr "塑料层压板"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_field_one2many_with_copy.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr "请先点击“保存”按钮"

#. module: mrp
#: model:product.product,name:mrp.product_product_wood_ply
#: model:product.template,name:mrp.product_product_wood_ply_product_template
msgid "Ply Layer"
msgstr "层层"

#. module: mrp
#: model:product.product,name:mrp.product_product_ply_veneer
#: model:product.template,name:mrp.product_product_ply_veneer_product_template
msgid "Ply Veneer"
msgstr "单板层"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__json_popover
msgid "Popover Data JSON"
msgstr "JSON数据弹窗"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__possible_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__possible_bom_product_template_attribute_value_ids
msgid "Possible Product Template Attribute Value"
msgstr "可能的产品模板属性值"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Post-Production"
msgstr "制造后"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pre-Production"
msgstr "制造前"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Print"
msgstr "打印"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Print All Variants"
msgstr "打印所有变体"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Print Unfolded"
msgstr "打印展开部分"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__priority
#: model:ir.model.fields,field_description:mrp.field_mrp_production__priority
msgid "Priority"
msgstr "优先级"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process operations at specific work centers"
msgstr "特定工作中心的流程作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__produce_line_ids
msgid "Processed Disassembly Lines"
msgstr "已处理的拆解明细"

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_group
#: model:ir.model.fields,field_description:mrp.field_mrp_production__procurement_group_id
msgid "Procurement Group"
msgstr "补货组"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_steps
msgid ""
"Produce : Move the components to the production location        directly and start the manufacturing process.\n"
"Pick / Produce : Unload        the components from the Stock to Input location first, and then        transfer it to the Production location."
msgstr ""
"制造：将组件直接移动到制造位置并开始制造过程。\n"
"拣配/制造：首先将组件从“库存”卸载到“输入”位置，然后将其转移到“制造”位置。"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_byproducts
msgid "Produce residual products"
msgstr "生产残留产品"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -&gt; C + D)"
msgstr "产生残留产物(A + B -&gt; C + D)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "Produced"
msgstr "生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__produced_qty
msgid "Produced Quantity"
msgstr "生产的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__serial_numbers
msgid "Produced Serial Numbers"
msgstr "生产的序列号"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__operation_id
msgid "Produced in Operation"
msgstr "生产的作业"

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Product"
msgstr "产品"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Product Attachments"
msgstr "产品文档附件"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Product Cost"
msgstr "产品成本"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_virtual_available
msgid "Product Forecasted Quantity"
msgstr "产品预测数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr "产品生命周期管理 (PLM)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Product Moves"
msgstr "产品移动"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "产品移动(移库明细)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_qty_available
msgid "Product On Hand Quantity"
msgstr "产品在手数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Product Quantity"
msgstr "产品数量"

#. module: mrp
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tmpl_id
msgid "Product Template"
msgstr "产品模板"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_id
msgid "Product Unit of Measure"
msgstr "产品计量单位"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_id
msgid "Product Variant"
msgstr "产品变体"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_product_variant_action
#: model:ir.ui.menu,name:mrp.product_variant_mrp
msgid "Product Variants"
msgstr "产品变体"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__production_id
msgid "Production"
msgstr "生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_date
msgid "Production Date"
msgstr "生产日期"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_document
msgid "Production Document"
msgstr "生产文件"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Production Information"
msgstr "生产信息"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_location_id
msgid "Production Location"
msgstr "生产位置"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__production_id
msgid "Production Order"
msgstr "生产订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__raw_material_production_id
msgid "Production Order for components"
msgstr "组件的生产订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__production_id
msgid "Production Order for finished products"
msgstr "成品的生产订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_state
msgid "Production State"
msgstr "生产状态"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Production Workcenter"
msgstr "生产工作中心"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Production of Draft MO"
msgstr "草稿状态MO的生产"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Production started late"
msgstr "生产开始延迟"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__productive
msgid "Productive"
msgstr "生产力"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__productive_time
msgid "Productive Time"
msgstr "生产时间"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__productive_time
msgid "Productive hours over the last month"
msgstr "生产性时数最近月份"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Productivity"
msgstr "生产力"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
msgid "Productivity Losses"
msgstr "生产力损失"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr "产品"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Products to Consume"
msgstr "未投料数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__progress
msgid "Progress Done (%)"
msgstr "完成进度 (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__propagate_cancel
msgid "Propagate cancel and split"
msgstr "传播取消以及拆分"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__quality
msgid "Quality"
msgstr "质量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr "质量损失"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "质量工作表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quant_ids
msgid "Quant"
msgstr "量化"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_produced
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quantity
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Quantity"
msgstr "数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Quantity Produced"
msgstr "已生产数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_producing
msgid "Quantity Producing"
msgstr "正在生产的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_remaining
msgid "Quantity To Be Produced"
msgstr "将被生产的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__should_consume_qty
msgid "Quantity To Consume"
msgstr "待消耗的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_qty
msgid "Quantity To Produce"
msgstr "待生产数量"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Quantity:"
msgstr "数量："

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_raw_ids
msgid "Raw Moves"
msgstr "原材料移动"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__assigned
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__ready
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Ready"
msgstr "就绪"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_real_duration
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration
msgid "Real Duration"
msgstr "实际时长"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"Recursion error!  A product with a Bill of Material should not have itself "
"in its BoM or child BoMs!"
msgstr "递归错误！一个BOM的产品不应该在本身BOM或子BOM中！"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__code
#: model:ir.model.fields,field_description:mrp.field_mrp_production__name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__name
msgid "Reference"
msgstr "参考"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_name_uniq
msgid "Reference must be unique per Company!"
msgstr "必须在公司内唯一！"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__origin
msgid ""
"Reference of the document that generated this production order request."
msgstr "生成本生产订单的前置文档编号."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Reference:"
msgstr "参照："

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__ir_attachment_id
msgid "Related attachment"
msgstr "相关的附件"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Replan"
msgstr "重新安排"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Report:"
msgstr "报告："

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
msgid "Reporting"
msgstr "报告"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Reserve"
msgstr "预留"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Reserved"
msgstr "已预留"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_id
msgid "Resource"
msgstr "资源"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_field
msgid "Resource Field"
msgstr "资源字段"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_id
msgid "Resource ID"
msgstr "资源ID"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_model
msgid "Resource Model"
msgstr "资源模型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_name
msgid "Resource Name"
msgstr "资源名称"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__user_id
msgid "Responsible"
msgstr "负责人"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_user_id
msgid "Responsible User"
msgstr "负责用户"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__routing_line_ids
msgid "Routing Lines"
msgstr "工艺明细行"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr "工艺工作中心"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_procurement_compute_mrp
msgid "Run Scheduler"
msgstr "运行调度器"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr "提前安排制造订单以避免延误"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_planned_start
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Scheduled Date"
msgstr "安排的日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Scheduled Date by Month"
msgstr "按安排日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_planned_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_planned_finished
msgid "Scheduled End Date"
msgstr "安排结束日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_planned_start
msgid "Scheduled Start Date"
msgstr "安排开始日期"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"Scheduled before the previous work order, planned from %(start)s to %(end)s"
msgstr "安排在上一个工单之前，安排从%(start)s到%(end)s"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_workorder_popover.js:0
#, python-format
msgid "Scheduling Information"
msgstr "调度信息"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#: model:ir.model,name:mrp.model_stock_scrap
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#, python-format
msgid "Scrap"
msgstr "报废"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_count
msgid "Scrap Move"
msgstr "报废移动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_ids
msgid "Scraps"
msgstr "报废"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_screw
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr "螺丝"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr "搜索"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr "搜索BOM"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr "搜索生产"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Search Work Orders"
msgstr "搜索工作单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr "MRP工作中心的搜索"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr "安全提前时间"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr "每个制造作业的确保天数."

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_field_one2many_with_copy.js:0
#, python-format
msgid "Select Operations to Copy"
msgstr "选择要复制的作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__sequence
msgid "Sequence"
msgstr "序号"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence picking before manufacturing"
msgstr "制造前拣货序号"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence production"
msgstr "制造序号"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence stock after manufacturing"
msgstr "制造后库存序号"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Serial Mass Produce"
msgstr "序列号批量生产"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__manual
msgid "Set duration manually"
msgstr "手动设置时长"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Settings"
msgstr "设置"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_start
msgid "Setup Time"
msgstr "设置时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_apply
msgid "Show Apply"
msgstr "显示应用"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_backorders
msgid "Show Backorders"
msgstr "显示滞销品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__show_bom
msgid "Show BoM column"
msgstr "显示BOM列"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_final_lots
msgid "Show Final Lots"
msgstr "显示最后一批"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lock
msgid "Show Lock/unlock buttons"
msgstr "显示锁定/解锁按钮"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__show_json_popover
msgid "Show Popover?"
msgstr "显示弹窗？"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__show_productions
msgid "Show Productions"
msgstr "显示制造"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr "显示所有的在今天之前的下一个动作日期的记录"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__show_backorder_lines
msgid "Show backorder lines"
msgstr "显示制造欠单明细"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__leave_id
msgid "Slot into workcenter calendar once planned"
msgstr "一旦安排就插入工作中心日历"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_head
#: model_terms:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr "实木是一种耐用的天然材料。"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk
#: model_terms:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr "实木桌子。"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"Some of your byproducts are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct byproducts."
msgstr "某些副产品已被跟踪，您必须指定制造订单才能检索正确的副产品。"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"Some of your components are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct components."
msgstr "跟踪了一些组件，必须指定制造订单才能检索正确的组件。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Some product moves have already been confirmed, this manufacturing order "
"can't be completely cancelled. Are you still sure you want to process ?"
msgstr "已经确认了一些产品移库，无法完全取消此制造订单。 您还确定要处理吗？"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Some work orders are already done, you cannot unplan this manufacturing "
"order."
msgstr "一些工单已经完成，您不能取消该制造订单的安排。"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Some work orders have already started, you cannot unplan this manufacturing "
"order."
msgstr "一些工单已经开始，您不能取消该制造订单的安排计划."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__origin
msgid "Source"
msgstr "来源"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_id
msgid "Source Location"
msgstr "源位置"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__costs_hour
msgid "Specify cost of work center per hour."
msgstr "指定每小时工作中心的成本。"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_screw
#: model_terms:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr "不锈钢螺丝"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_bolt
#: model_terms:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr "不锈钢螺丝满（直径－5毫米，长度－10毫米）"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Starred"
msgstr "星标消息"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Start"
msgstr "开始"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_start
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Start Date"
msgstr "开始日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__state
msgid "State"
msgstr "状态"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__state
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Status"
msgstr "状态"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态 \n"
" 逾期：已经超过截止日期 \n"
" 现今：活动日期是当天 \n"
" 安排：未来活动。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_type_id
msgid "Stock After Manufacturing Operation Type"
msgstr "制造后入库存的作业类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_rule_id
msgid "Stock After Manufacturing Rule"
msgstr "制造规则后的库存"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_assign_serial
msgid "Stock Assign Serial Numbers"
msgstr "库存指定序列号码"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_availability
msgid "Stock Availability"
msgstr "库存可用性"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr "库存移动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr "生产货物的库存移动"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
msgid "Stock Moves"
msgstr "库存移动"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "库存接收报告"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "库存补货报告"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_rule
msgid "Stock Rule"
msgstr "库存规则"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_loc_id
msgid "Stock after Manufacturing Location"
msgstr "制造地点后的库存"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "库存规则报告"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Store Finished Product"
msgstr "存储完工产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__store_fname
msgid "Stored Filename"
msgstr "存储的文件名"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Structure & Cost"
msgstr "结构&成本"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_bom_id
msgid "Sub BoM"
msgstr "子 BOM"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Subcontract the production of some products"
msgstr "外包一些产品的生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr "外包"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr "桌台"

#. module: mrp
#: model:product.product,name:mrp.product_product_table_kit
#: model:product.template,name:mrp.product_product_table_kit_product_template
msgid "Table Kit"
msgstr "桌台套件"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_leg
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr "桌腿"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_head
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Top"
msgstr "桌面"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_table_kit
#: model_terms:product.template,description:mrp.product_product_table_kit_product_template
msgid "Table kit"
msgstr "桌台套件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tag_ids
msgid "Tag"
msgstr "标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__name
msgid "Tag Name"
msgstr "标签名"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__is_done
msgid "Technical Field to order moves"
msgstr "技术领域的订单移动"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__is_user_working
msgid "Technical field indicating whether the current user is working. "
msgstr "表示当前用户是否正在工作的技术字段。 "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr "检查何时可以保留数量的技术字段"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "技术领域来检查何时可以保留"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_assign_serial__show_apply
msgid "Technical field to show the Apply button"
msgstr "显示应用按钮的技术字段"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_assign_serial__show_backorders
msgid "Technical field to show the Create Backorder and No Backorder buttons"
msgstr "显示创建退货订单和无退货订单按钮的技术字段"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__costs_hour
msgid ""
"Technical field to store the hourly cost of workcenter at time of work order"
" completion (i.e. to keep a consistent cost)."
msgstr "技术字段，用于存储工单完成时工作中心的小时成本（即保持成本的一致性）。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr "用于在多网站环境中解析多个附件的技术字段。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_availability
msgid "Technical: used in views and domains only."
msgstr "技术: 仅用于视图和域."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__working_state
msgid "Technical: used in views only"
msgstr "技术: 仅用于视图"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_state
msgid "Technical: used in views only."
msgstr "技术: 仅用于视图."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__text
msgid "Text"
msgstr "文本"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,help:mrp.field_mrp_workorder__operation_note
msgid "Text worksheet description"
msgstr "文字工作表说明"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_id
msgid "The Bill of Material this operation is linked to"
msgstr "此作业链接到的BOM"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0 code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr "选择的计量单位跟产品表单的计量单位在不同的类别。"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "The Workorder (%s) cannot be started twice!"
msgstr "工单 (%s) 无法启动两次!"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The attribute value %(attribute)s set on product %(product)s does not match "
"the BoM product %(bom_product)s."
msgstr "产品%(product)s上设置的属性值%(attribute)s与BoM产品%(bom_product)s不匹配。"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "The capacity must be strictly positive."
msgstr "容量必须严格为正数."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "The component %s should not be the same as the product to produce."
msgstr "组件%s不应与待生产产品相同。"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The current configuration is incorrect because it would create a cycle "
"between these products: %s."
msgstr "当前的配置是不正确的，因为它会在这些产品之间创建一个循环：%s。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__res_model
msgid "The database object this attachment will be attached to."
msgstr "该附件将附加到的数据库对象。"

#. module: mrp
#: code:addons/mrp/models/stock_orderpoint.py:0
#, python-format
msgid "The following replenishment order has been generated"
msgstr "已生成以下补货订单"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_produced
msgid "The number of products already handled by this work order"
msgstr "此工单已处理的产品数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__operation_id
msgid ""
"The operation where the components are consumed, or the finished products "
"created."
msgstr "作业，在此组件消耗或完工产品创建"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__cost_share
msgid ""
"The percentage of the final production cost for this by-product line "
"(divided between the quantity produced).The total of all by-products' cost "
"share must be less than or equal to 100."
msgstr "该副产品生产线的最终生产成本的百分比（除以生产数量）。所有副产品的成本份额总和必须小于或等于100。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__cost_share
msgid ""
"The percentage of the final production cost for this by-product. The total "
"of all by-products' cost share must be smaller or equal to 100."
msgstr "该副产品的最终生产成本的百分比。所有副产品的成本份额之和必须小于或等于100。"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"The planned end date of the work order cannot be prior to the planned start "
"date, please correct this to save the work order."
msgstr "工作订单的安排结束日期不能早于安排开始日期，请更正此日期以保存工单。"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The product has already been used at least once, editing its structure may "
"lead to undesirable behaviours. You should rather archive the product and "
"create a new one with a new bill of materials."
msgstr "该产品已至少使用过一次，编辑其结构可能会导致不良行为。您应该归档产品并使用新的BOM创建一个新产品."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "The quantity produced of by-products must be positive."
msgstr "副产品的生产数量必须是正数。"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_qty_positive
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_qty_positive
#, python-format
msgid "The quantity to produce must be positive!"
msgstr "待生产的数量必须是正数!"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__res_id
msgid "The record id this is attached to."
msgstr "这是附加到记录ID。"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"The selected serial number does not correspond to the one used in the "
"manufacturing order, please select another one."
msgstr "选择的序列号与制造订单中使用的序列号不对应，请选择另一个序列号."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The serial number %(number)s used for byproduct %(product_name)s has already"
" been produced"
msgstr "用于副产品%(product_name)s的序列号%(number)s已经生产"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The serial number %(number)s used for component %(component)s has already "
"been consumed"
msgstr "用于组件%(component)s的序列号%(number)s已经消耗"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_tag_tag_name_unique
msgid "The tag name must be unique."
msgstr "标签名称必须唯一."

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "The total cost share for a BoM's by-products cannot exceed 100."
msgstr "一个BoM的副产品的总成本分别他吧不能超过100。"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The total cost share for a manufacturing order's by-products cannot exceed "
"100."
msgstr "一个制造订单的副产品的总成本分摊不能超过100。"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "The work order should have already been processed."
msgstr "工单应该已经被处理。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__theme_template_id
msgid "Theme Template"
msgstr "主题模板"

#. module: mrp
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "There are more Serial Numbers than the Quantity to Produce"
msgstr "序列号的数量多于生产的数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"There are no components to consume. Are you still sure you want to continue?"
msgstr "没有要消耗的组件。 您仍然确定要继续吗？"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid "There's no product move yet"
msgstr "还没有产品移动"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr "此字段用于定义资源工作的时区。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"该字段用于计算该工作中心的工单的预期工期。 例如，如果工单花费一小时且效率系数是100％，则预期持续时间将是一小时。 "
"如果效率因子为200％，则预期持续时间将为30分钟。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
msgid "This is a BoM of type Kit!"
msgstr "这是套件BOM！"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid ""
"This is the cost based on the BoM of the product. It is computed by summing "
"the costs of the components and operations needed to build the product."
msgstr "这是基于产品的BOM（BOM）的成本。 它是通过汇总构建产品所需的组件和作业的成本来计算的。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "This is the cost defined on the product."
msgstr "这是产品的定义成本。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                You can filter on the product to see all the past movements for the product."
msgstr ""
"此菜单使您可以完全跟踪特定产品上的库存作业。\n"
" 您可以过滤产品以查看产品过去的所有移动。"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "This production order has been created from Replenishment Report."
msgstr "这个生产订单是由补货报告创建的。"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "This serial number for product %s has already been produced"
msgstr "产品%s的该序列号已经产生"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__time_ids
msgid "Time"
msgstr "时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_efficiency
msgid "Time Efficiency"
msgstr "时间效率"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_ids
msgid "Time Logs"
msgstr "时间日志"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Time Tracking"
msgstr "时间跟踪"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Time Tracking: %(user)s"
msgstr "时间跟踪：%(user)s"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_stop
msgid "Time in minutes for the cleaning."
msgstr "清理的时间，以分钟计."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_start
msgid "Time in minutes for the setup."
msgstr "用于设置的时间，以分钟计."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid ""
"Time in minutes:- In manual mode, time used- In automatic mode, supposed "
"first time when there aren't any work orders yet"
msgstr "以分钟为单位的时间：-在手动模式下为使用时间-在自动模式下，尚无任何工单的假设第一次计时"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tz
msgid "Timezone"
msgstr "时区"

#. module: mrp
#: model:digest.tip,name:mrp.digest_tip_mrp_0
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "Tip: Use tablets in the shop to control manufacturing"
msgstr "提示：在商店中使用平板电脑来控制生产"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "To"
msgstr "至"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__to_backorder
msgid "To Backorder"
msgstr "创建欠单"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__to_close
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Close"
msgstr "待关闭"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_expected_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "To Consume"
msgstr "待消耗"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr "待办"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr "待启动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__to_immediate
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "To Process"
msgstr "待处理"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "To Produce"
msgstr "待生产"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr "今天的活动"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_wood_wear
#: model_terms:product.template,description:mrp.product_product_wood_wear_product_template
msgid "Top layer of a wood panel."
msgstr "木板的顶层."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Total Duration"
msgstr "总时长"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_late_count
msgid "Total Late Orders"
msgstr "延迟的订单合计"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_pending_count
msgid "Total Pending Orders"
msgstr "待定的订单合计"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr "总数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_qty
msgid "Total Quantity"
msgstr "数量总计"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_progress_count
msgid "Total Running Orders"
msgstr "运行的订单合计"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Total To Consume"
msgstr "待消耗总计"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Total duration"
msgstr "总时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total expected duration"
msgstr "预计总工期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_duration_expected
msgid "Total expected duration (in minutes)"
msgstr "预计总时间（分钟）"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total real duration"
msgstr "实际总时间"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_real_duration
msgid "Total real duration (in minutes)"
msgstr "实际总时间（分钟）"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Traceability"
msgstr "追溯"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "Traceability Report"
msgstr "追溯报告"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_tracking
msgid "Tracking"
msgstr "追溯"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking
msgid "Transfer"
msgstr "调拨"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Transfers"
msgstr "物料流转"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__type
msgid "Type"
msgstr "类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "作业类型"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录的异常活动类型。"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Unable to split with more than the quantity to produce."
msgstr "无法以超过生产数量的方式进行分割。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Unblock"
msgstr "解锁"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild"
msgstr "拆解"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild Order"
msgstr "拆解单"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "Unbuild Order product quantity has to be strictly positive."
msgstr "拆解单产品数量必须严格为正数。"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr "拆解单"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Unbuild: %s"
msgstr "拆解：%s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
msgid "Unfold"
msgstr "展开"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unit_factor
msgid "Unit Factor"
msgstr "单位因子"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_uom_name
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Unit of Measure"
msgstr "计量单位"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr "计量单位是库存控制的量度单位"

#. module: mrp
#: model:product.product,uom_name:mrp.product_product_computer_desk
#: model:product.product,uom_name:mrp.product_product_computer_desk_bolt
#: model:product.product,uom_name:mrp.product_product_computer_desk_head
#: model:product.product,uom_name:mrp.product_product_computer_desk_leg
#: model:product.product,uom_name:mrp.product_product_computer_desk_screw
#: model:product.product,uom_name:mrp.product_product_drawer_case
#: model:product.product,uom_name:mrp.product_product_drawer_drawer
#: model:product.product,uom_name:mrp.product_product_plastic_laminate
#: model:product.product,uom_name:mrp.product_product_ply_veneer
#: model:product.product,uom_name:mrp.product_product_table_kit
#: model:product.product,uom_name:mrp.product_product_wood_panel
#: model:product.product,uom_name:mrp.product_product_wood_ply
#: model:product.product,uom_name:mrp.product_product_wood_wear
#: model:product.template,uom_name:mrp.product_product_computer_desk_bolt_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_head_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_leg_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_screw_product_template
#: model:product.template,uom_name:mrp.product_product_drawer_case_product_template
#: model:product.template,uom_name:mrp.product_product_drawer_drawer_product_template
#: model:product.template,uom_name:mrp.product_product_plastic_laminate_product_template
#: model:product.template,uom_name:mrp.product_product_ply_veneer_product_template
#: model:product.template,uom_name:mrp.product_product_table_kit_product_template
#: model:product.template,uom_name:mrp.product_product_wood_panel_product_template
#: model:product.template,uom_name:mrp.product_product_wood_ply_product_template
#: model:product.template,uom_name:mrp.product_product_wood_wear_product_template
msgid "Units"
msgstr "件"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unlock"
msgstr "解锁"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_unlocked_by_default
msgid "Unlock Manufacturing Orders"
msgstr "解锁制造订单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Unlock the manufacturing order to adjust what has been consumed or produced."
msgstr "解锁制造订单以调整已消耗物料或产成品。"

#. module: mrp
#: model:res.groups,name:mrp.group_unlocked_by_default
msgid "Unlocked by default"
msgstr "默认情况下是解锁的"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unplan"
msgstr "取消安排"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_unread
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_unread
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_unread
msgid "Unread Messages"
msgstr "未读消息"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_unread_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_unread_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息数"

#. module: mrp
#: model:ir.actions.server,name:mrp.mrp_production_action_unreserve_tree
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Unreserve"
msgstr "取消保留"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "UoM"
msgstr "计量单位"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp_document_template.xml:0
#, python-format
msgid "Upload"
msgstr "上传"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Upload your PDF file."
msgstr "上传PDF文件."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__1
msgid "Urgent"
msgstr "紧急"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__url
msgid "Url"
msgstr "Url"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr "用于"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__user_id
#: model:res.groups,name:mrp.group_mrp_user
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "User"
msgstr "用户"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Using a MPS report to schedule your reordering and manufacturing operations "
"is useful if you have long lead time and if you produce based on sales "
"forecasts."
msgstr "如果交货时间过长，并根据销售预测进行生产，那么使用MPS报告来安排重新排序和制造作业是非常有用的。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Validate"
msgstr "验证"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Variant:"
msgstr "变体："

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__3
msgid "Very High"
msgstr "非常高"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Waiting"
msgstr "正在等待"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__waiting
msgid "Waiting Another Operation"
msgstr "等待其它作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr "等待可用量"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp.js:0
#, python-format
msgid "Waiting Materials"
msgstr "材料等待中"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__pending
msgid "Waiting for another WO"
msgstr "等待其他工单"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__waiting
msgid "Waiting for components"
msgstr "等待组件"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Waiting the previous work order, planned from %(start)s to %(end)s"
msgstr "等待上一个工单，安排从%(start)s到%(end)s"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse
msgid "Warehouse"
msgstr "仓库"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "Warn Insufficient Unbuild Quantity"
msgstr "拆解数量短缺警告"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0 code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/stock_scrap.py:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Warnings"
msgstr "警告"

#. module: mrp
#: model:product.product,name:mrp.product_product_wood_wear
#: model:product.template,name:mrp.product_product_wood_wear_product_template
msgid "Wear Layer"
msgstr "耐磨层"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__website_id
msgid "Website"
msgstr "网站"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__website_url
msgid "Website URL"
msgstr "网站网址"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__website_message_ids
msgid "Website communication history"
msgstr "网上沟通记录"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__picking_type_id
msgid ""
"When a procurement has a ‘produce’ route with a operation type set, it will "
"try to create a Manufacturing Order for that product using a BoM of the same"
" operation type. That allows to define stock rules which trigger different "
"manufacturing orders with different BoMs."
msgstr ""
"当采购具有设定作业类型的“生产”路线时，它将尝试使用相同作业类型的BoM为该产品创建制造订单。 这允许定义触发与不同BOM的不同制造订单的采购规则。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__asap
msgid "When components for 1st operation are available"
msgstr "当第1次作业的组件是可用的"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr "当产品为制造品,由此仓库制造."

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> a manufacturing order is "
"created to fulfill the need."
msgstr "当需要产品在<b>%s</b>时，<br/>创建制造订单以满足需要。"

#. module: mrp
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid ""
"With the Odoo work center control panel, your worker can start work orders "
"in the shop and follow instructions of the worksheet. Quality tests are "
"perfectly integrated into the process. Workers can trigger feedback loops, "
"maintenance alerts, scrap products, etc."
msgstr ""
"使用Odoo工作中心控制面板，您的工人可以在商店中启动工作订单并遵循工作表中的说明。 质量测试已完美集成到流程中。 "
"工人可以触发反馈流程，维护警报，报废产品等。"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr "如果在警告/严格状态下使用向导，并且MO使用了更多组件（与BOM标准用量比较）"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr "标记为已完成或创建欠单的向导"

#. module: mrp
#: model:product.product,name:mrp.product_product_wood_panel
#: model:product.template,name:mrp.product_product_wood_panel_product_template
msgid "Wood Panel"
msgstr "木板"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Work Center"
msgstr "工作中心"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workcenter_load
msgid "Work Center Load"
msgstr "工作中心负载"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr "工作中心负载"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr "工作中心名称"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "工作中心使用情况"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr "工作中心负载"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr "工作中心"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Overview"
msgstr "工作中心概览"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Work Instruction"
msgstr "工作指令"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__workorder_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Work Order"
msgstr "工单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required components."
msgstr "工单作业允许您创建和管理在生产中心应遵循的制造作业。 它们附在定义所需组件的BOM上。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__workorder_id
msgid "Work Order To Consume"
msgstr "待消耗的工单"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_workorder
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Orders"
msgstr "工单"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr "工单效能"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
msgid "Work Orders Planning"
msgstr "工单计划"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_type
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr "工作表"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Work center"
msgstr "工作中心"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"                    Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"工单是制造订单中要执行的作业。\n"
"作业在BOM中定义或直接在制造订单中添加。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"工单是作为制造订单一部分执行的作业。\n"
"作业在BOM中定义或直接在制造订单中添加。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr "工单进行中。点击阻塞工作中心."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr "工作中心"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "Workcenter %s cannot be an alternative of itself."
msgstr "工作中心%s不能成为其自身的替代品。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr "工作中心生产力"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "工作中心生产力日志"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr "工作中心生产力损失"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "Workcenter Productivity Losses"
msgstr "工作中心生产力损失"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_state
msgid "Workcenter Status"
msgstr "工作中心状态"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr "工作中心已阻塞，点击解除阻塞."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Working Hours"
msgstr "工作时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_user_ids
msgid "Working user on this work order."
msgstr "在此工单工作的用户."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet
msgid "Worksheet"
msgstr "工作记录表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_type
msgid "Worksheet Type"
msgstr "工作表类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Worksheet URL"
msgstr "工作表格URL"

#. module: mrp
#: code:addons/mrp/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to create or edit a lot or serial number for the "
"components with the operation type \"Manufacturing\". To change this, go on "
"the operation type and tick the box \"Create New Lots/Serial Numbers for "
"Components\"."
msgstr "不允许为使用作业类型为“制造”的组创建或编辑件批号或序列号。 要更改此设置，请在作业类型中，勾选“为组件创建新的批号/序列号”框。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr "您可以从您的电脑上传一个文件或者复制/粘贴一个 Internet 链接到您的文件."

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not create a kit-type bill of materials for products that have at "
"least one reordering rule."
msgstr "不能为至少具有一个重新订购规则的产品创建套件类型的BOM表。"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr "正在生产中的BOM不可删除,请先关闭或取消制造订单."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You cannot change the workcenter of a work order that is in progress or "
"done."
msgstr "您无法更改用正在执行或已完成工单的工作中心。"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "You cannot create a new Bill of Material from here."
msgstr "您不能从此处创建新的BOM。"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You cannot delete an unbuild order if the state is 'Done'."
msgstr "无法删除状态为“完成”的拆解单。"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You cannot have %s  as the finished product and in the Byproducts"
msgstr "您不能将%s作为完工产品和副产品"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You cannot link this work order to another manufacturing order."
msgstr "您不能将此工单链接到另一个制造订单。"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You cannot move a manufacturing order once it is cancelled or done."
msgstr "您不能移动取消或完成状态的制造订单。"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You cannot produce the same serial number twice."
msgstr "不能重复生产相同的序列号。"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You cannot unbuild a undone manufacturing order."
msgstr "无法取消未完成的制造订单。"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You cannot use the 'Apply on Variant' functionality and simultaneously "
"create a BoM for a specific variant."
msgstr "您不能在应用 \"应用于变体 \"功能的同时，为一个特定的变体创建一个BoM。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid ""
"You consumed a different quantity than expected for the following products.\n"
"                        <b attrs=\"{'invisible': [('consumption', '=', 'strict')]}\">\n"
"                            Please confirm it has been done on purpose.\n"
"                        </b>\n"
"                        <b attrs=\"{'invisible': [('consumption', '!=', 'strict')]}\">\n"
"                            Please review your component consumption or ask a manager to validate \n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '!=', 1)]}\">this manufacturing order</span>\n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '=', 1)]}\">these manufacturing orders</span>.\n"
"                        </b>"
msgstr ""
"以下产品您消耗了与标准用量不同的数量。\n"
"<b attrs=\"{'invisible': [('consumption', '=', 'strict')]}\">\n"
"请验证您的操作。\n"
"</b>\n"
"<b attrs=\"{'invisible': [('consumption', '!=', 'strict')]}\">\n"
"请检查您的组件消耗量或请经理进行验证\n"
"<span attrs=\"{'invisible':[('mrp_production_count', '!=', 1)]}\">这个制造订单</span>\n"
"<span attrs=\"{'invisible':[('mrp_production_count', '=', 1)]}\">这些制造订单</span>。\n"
"</b>"

#. module: mrp
#: code:addons/mrp/wizard/change_production_qty.py:0
#, python-format
msgid ""
"You have already processed %(quantity)s. Please input a quantity higher than"
" %(minimum)s "
msgstr "您已经处理了 %(quantity)s. 请输入比 %(minimum)s 更高的数量 "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid ""
"You have entered less serial numbers than the quantity to produce.<br/>\n"
"                        Create a backorder if you expect to process the remaining quantities later.<br/>\n"
"                        Do not create a backorder if you will not process the remaining products."
msgstr ""
"您输入的序列号比要生产的数量少。<br/>\n"
"                        如果您希望以后能处理剩余的数量，请创建一个后备订单。<br/>\n"
"                        如果您不打算处理剩余的产品，就不要创建后备订单。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid ""
"You have not recorded <i>produced</i> quantities yet, by clicking on "
"<i>apply</i> Odoo will produce all the finished products and consume all "
"components."
msgstr "您尚未记录<i>生产量</i>，通过单击<i>应用</i>Odoo将生产所有完工产品并消耗所有组件。"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"You must indicate a non-zero amount consumed for at least one of your "
"components"
msgstr "您必须指明至少一个组件消耗的非零数量金额"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr "需在“绩效”类别中至少定义一个生产力损失。 从制造应用程序创建一个菜单：配置/生产力损失。"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Productivity'. Create one from the Manufacturing app, menu: Configuration /"
" Productivity Losses."
msgstr "需在“生产力”类别中至少定义一个生产力损失。 从制造应用程序创建一个菜单：配置/生产力损失。"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one unactive productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr "需在“绩效”类别中定义至少一个未启用的生产力损失。 从制造应用程序创建一个菜单：配置/生产力损失。"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You need to provide a lot for the finished product."
msgstr "需为完工产品提供批号."

#. module: mrp
#: code:addons/mrp/wizard/mrp_immediate_production.py:0
#, python-format
msgid "You need to supply Lot/Serial Number for products:"
msgstr "您需要提供产品的批号/序列号："

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_backorder
msgid "You produced less than initial demand"
msgstr "您生产的数量少于初始需求"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You should provide a lot number for the final product."
msgstr "需为成品提供批号。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "and build finished products using"
msgstr "并且制造完工产品，使用"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "bills of materials"
msgstr "BOM"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "cancelled"
msgstr "已取消"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "components"
msgstr "组件"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "copy paste a list and/or use Generate"
msgstr "复制粘贴列表和/或使用 \"生成\""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "days"
msgstr "天"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "expected duration"
msgstr "预计期限"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "from location"
msgstr "来自位置"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr "最后的"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "manufacturing order"
msgstr "制造订单"

#. module: mrp
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#, python-format
msgid "minutes"
msgstr "分钟"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "of"
msgstr "的"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "on"
msgstr "on"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "ordered instead of"
msgstr "已订购，替换"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr "数量已更新."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "real duration"
msgstr "实际时长"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_documents_controller_mixin.js:0
#, python-format
msgid "status code: %s, message: %s"
msgstr "状态代码:%s, 消息:%s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr "工单"
