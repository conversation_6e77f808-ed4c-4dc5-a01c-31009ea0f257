# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <erial<PERSON><PERSON>@gmail.com>, 2021
# R<PERSON> Consulting <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# A<PERSON>u <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# jabe<PERSON><PERSON>, 2021
# Harcogourmet, 2022
# ma<PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> Fonseca <<EMAIL>>, 2022
# <PERSON>, 2022
# martioodo hola, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: martioodo hola, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr "Utilitzar una impressora connectada al IoT Box"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "!props.isBill"
msgstr "!props.isBill"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid ""
"<span class=\"fa fa-lg fa-cutlery\" title=\"For bars and restaurants\" "
"role=\"img\" aria-label=\"For bars and restaurants\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-cutlery\" title=\"For bars and restaurants\" "
"role=\"img\" aria-label=\"For bars and restaurants\"/>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>Nom de la Sala: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sale: </strong>"
msgstr "<strong>Punt de Venda: </strong>"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_image
msgid ""
"A background image used to display a floor layout in the point of sale "
"interface"
msgstr ""
"Una imatge de fons utilitzada per a mostrar un disseny de sala a la "
"interfície del punt de venda"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""
"Una sala del restaurant representa el lloc on es serveixen els clients, es allí on podeu\n"
"definir i situar les taules. "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Actiu"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Add"
msgstr "Afegir"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.js:0
#, python-format
msgid "Add Internal Note"
msgstr "Afegeix una nota interna"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "Afegir una nova planta de restaurant"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid "Add a new restaurant order printer"
msgstr "Afegeix una impressora de comanda de restaurant"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Add a tip"
msgstr "Afegeix un consell"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "Add button"
msgstr "Afegeix un botó"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Add internal notes on order lines"
msgstr "Afegeix notes internes a les línies de comandes"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Add tip after payment (North America specific)"
msgstr ""
"Afegeix un consell després del pagament (especificat a Amèrica del Nord)"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Adjust Amount"
msgstr "Ajusta la quantitat"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr ""
"Ajusta la quantitat autoritzada per terminals de pagament per afegir un "
"consell després que els clients marxin o al final del dia."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Allow custom internal notes on Orderlines."
msgstr "Permet notes internes personalitzades a les línies de comandes."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Allow to print bill before payment"
msgstr "Permetre imprimir factures després del pagament"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr "Permetre imprimir la Factura abans del pagament"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Amount"
msgstr "Import"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr "Una identificació interna de la taula "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__name
msgid "An internal identification of the printer"
msgstr "Una identificació interna de la impressora "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__name
msgid "An internal identification of the restaurant floor"
msgstr "Una identificació interna de la sala del restaurant"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr " Aparició "

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "Arxivat"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Are you sure ?"
msgstr "Estàs segur?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Back"
msgstr "Enrere"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/ChromeWidgets/BackToFloorButton.xml:0
#: code:addons/pos_restaurant/static/src/xml/ChromeWidgets/BackToFloorButton.xml:0
#, python-format
msgid "Back to floor"
msgstr "Torna al terra"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Color de fons"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Imatge de fons "

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_bacon
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "Hamburguesa de bacon"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/PrintBillButton.xml:0
#, python-format
msgid "Bill"
msgstr "Factura "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr "Impressió de Factura "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr "Divisió de Factura "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Blocked action"
msgstr "Acció bloquejada"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Blue"
msgstr "Blau"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "CANCELLED"
msgstr "CANCEL·LAT "

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_cheeseburger
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr "Hamburguesa de formatge"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_chicken
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr "Pollastre de Curry Sandwich"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Close"
msgstr "Tancar"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Close Tab"
msgstr "Tanca la pestanya"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_club
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr "Club Sandwich"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.coke
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "Coca-Cola"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Color"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#, python-format
msgid "Connection Error"
msgstr "Error de connexió"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Creat el"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Design floors and assign orders to tables"
msgstr "Dissenya els pisos i assigna ordres a les taules"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "Begudes"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#, python-format
msgid "Due to a connection error, the orders are not synchronized."
msgstr "A causa d'un error de connexió, les comandes no estan sincronitzades."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Duplicate"
msgstr "Duplicar"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"                Proxy where the printer can be found, and a list of product categories.\n"
"                An Order Printer will only print updates for products belonging to one of\n"
"                its categories."
msgstr ""
"Cada impressora de comandes té una adreça IP que defineix la bústia/maquinari de la caixa\n"
"               Proxy on es pot trobar la impressora i una llista de categories de productes.\n"
"                Una impressora de comandes només imprimirà les actualitzacions dels productes que pertanyin a una de\n"
"                les seves categories."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr "Habilita la divisió de la factura al punt de venda."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/SubmitOrderButton.js:0
#, python-format
msgid "Failed in printing the changes in the order"
msgstr "Fallada en la impressió dels canvis en l'ordre"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "Sala"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "Nom de la Sala"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Disseny de sales "

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "Floor: %s - PoS Config: %s \n"
msgstr "Pis: %s - PoS Config: %s \n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Floors"
msgstr "Sales"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_table_management
msgid "Floors & Tables"
msgstr "Terres & Taules"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "Menjar"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr ""
"Per conveniència, estem proporcionant els següents càlculs de gratuïtat:"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_funghi
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr "Funghi"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Green"
msgstr "Verd"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Grey"
msgstr "Gris"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TableGuestsButton.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
#, python-format
msgid "Guests"
msgstr "Comensals"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Guests ?"
msgstr "Comensals?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Guests:"
msgstr "Convidats:"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Altura"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "Posició horitzontal "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""
"Si és fals, la taula serà desactivada i no estarà disponible al punt de "
"venda"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.xml:0
#, python-format
msgid "Internal Note"
msgstr "Nota interna"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__note
msgid "Internal Note added by the waiter."
msgstr "Nota interna afegida pel cambrer."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Internal Notes"
msgstr "Notes internes"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "És un Bar/Restaurant"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Keep Open"
msgstr "Mantenir obert"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Light grey"
msgstr "Gris Clar"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_maki
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr "Esmorzar Maki 18pc"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_salmon
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr "Menjar Salmó 20pc"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_temaki
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr "Menjar Temaki mix 3pc"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_margherita
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Margherita"
msgstr "Margherita"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.minute_maid
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "Minute Maid"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_mozza
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr "Mozzarella Sandwich"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__mp_dirty
msgid "Mp Dirty"
msgstr "Mp brut"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__multiprint_resume
msgid "Multiprint Resume"
msgstr "Reprendre multiimpressió"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NEW"
msgstr "NOU"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NOTE"
msgstr "NOTA "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "No Tip"
msgstr "Sense punt"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Note"
msgstr "Nota"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/PrintBillButton.js:0
#, python-format
msgid "Nothing to Print"
msgstr "Res a imprimir"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Number of Seats ?"
msgstr "Nombre de comensals?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Offline"
msgstr "Fora de línia"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Open"
msgstr "Obre"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Orange"
msgstr "Taronja"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/SubmitOrderButton.xml:0
#, python-format
msgid "Order"
msgstr "Comanda"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr "Demanar Impressora"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_printer_form
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__printer_ids
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_printer_all
msgid "Order Printers"
msgstr "Impressores de comanda "

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"                order updates in the kitchen/bar when the waiter updates the order."
msgstr ""
"Les impressores de comandes són utilitzades pels bars i restaurants per imprimir les\n"
"comandes dels clients a la cuina i/o bar mentre el cambrer agafa la comanda."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer_form
msgid "POS Printer"
msgstr "Impresora POS"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "PRO FORMA"
msgstr "PRO FORMA"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_4formaggi
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 formaggi "
msgstr "Pasta 4 formaggi "

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_bolo
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr "Pasta Bolognese"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#, python-format
msgid "Payment"
msgstr "Pagament"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_id
msgid "Point of Sale"
msgstr "Punt de Venda"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuració del Punt de Venda"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Línies de tiquet de punt de venda"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "Comandes del Punt de Venda"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Pagaments de punt de venda"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "Imprimir comandes a la cuina, al bar, etc."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "Categories de producte impreses "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__name
msgid "Printer Name"
msgstr "Nom d'impressora "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__printer_type
msgid "Printer Type"
msgstr "Tipus d'impressora"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Printers"
msgstr "Impressores"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/SubmitOrderButton.js:0
#, python-format
msgid "Printing failed"
msgstr "Ha fallat la impressió"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "La impressió no està suportada en alguns navegadors"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid ""
"Printing is not supported on some browsers due to no default printing "
"protocol is available. It is possible to print your tickets by making use of"
" an IoT Box."
msgstr ""
"La impressió no està suportada en alguns navegadors perquè no hi ha "
"disponible un protocol d'impressió per defecte. És possible imprimir els "
"tiquets utilitzant una IoT Box."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "Adreça IP proxy "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Purple"
msgstr "Morat"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Red"
msgstr "Vermell"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Removing a table cannot be undone"
msgstr "L'eliminació d'una taula no és pot desfer "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Rename"
msgstr "Canvia el nom"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Reprint receipts"
msgstr "Reimprimir els rebuts"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Sala del Restaurant "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Sales del restaurant "

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer
msgid "Restaurant Order Printers"
msgstr "Impressores de comandes del restaurant"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_printer
msgid "Restaurant Printer"
msgstr "Impressora del restaurant"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Taula de restaurant "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse"
msgstr "Revertir"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "Retrocedir pagament"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "Arrodonir "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Round Shape"
msgstr "Forma rodona"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_chirashi
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr "Salmó i alvocat"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr "Comensals  "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "Servit per "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr "Estableix el consell després del pagament"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Settle"
msgstr "Settle"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "Forma "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Signature"
msgstr "Signatura"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__mp_skip
msgid "Skip line when sending ticket to kitchen printers."
msgstr "Omet la línia quan s'enviï el tiquet a les impressores de cuina."

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_tuna
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr "Sandvitx de tonyina picant"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/SplitBillButton.xml:0
#, python-format
msgid "Split"
msgstr "Divideix"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Split total or order lines"
msgstr "Divideix les línies totals o d'ordre"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "Quadrat "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Square Shape"
msgstr "Forma quadrada"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Subtotal"
msgstr "Subtotal"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
#, python-format
msgid "Table"
msgstr "Taula"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr "Nom de la Taula "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Table Name ?"
msgstr "Nom de la taula? "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Taules "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "Telf: "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "L'adreça IP o nom del proxy del maquinari de la impressora "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr "La quantitat de clients servits des d'aquesta comanda. "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid ""
"The background color of the floor layout, (must be specified in a html-"
"compatible format)"
msgstr ""
"El color de fons de la distribució de sala, (ha de ser especificat en un "
"format compatible amb html) "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "El nombre predeterminat de clients que es serveixen en aquesta taula."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__table_ids
msgid "The list of tables in this floor"
msgstr "Lllista de taules en aquesta sala"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr "Els pisos del restaurant atesos per aquest punt de venda."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr "La taula on és servirà aquesta comanda "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr ""
"El color de la taula, expressat com un valor vàlid de la propietat CSS "
"'background' "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr "L'alçada de la taula en píxels "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""
"La posició horitzontal de la taula des del costat esquerre fins a al centre "
"de la taula, en píxels "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr ""
"La posició vertical de la taula des de la part superior fins al centre de la"
" taula, en píxels "

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "L'amplada de la taula amb píxels "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/PrintBillButton.js:0
#, python-format
msgid "There are no order lines"
msgstr "No hi ha línies de comanda"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "This floor has no tables yet, use the"
msgstr "Aquesta sala no te més taules, utilitza el "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"Aquesta comanda encara no està sincronitzada amb el servidor. Assegureu-vos "
"que està sincronitzat i torneu-ho a provar."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Tint"
msgstr "Tint"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#, python-format
msgid "Tip"
msgstr "Propina"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tip:"
msgstr "Consell:"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Tipping"
msgstr "Aparença"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TransferOrderButton.xml:0
#, python-format
msgid "Transfer"
msgstr "Transferència"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Turquoise"
msgstr "Turquesa"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to change background color"
msgstr "No s'ha pogut canviar el color de fons"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to create table because you are offline."
msgstr "No s'ha pogut crear la taula perquè esteu fora de línia."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to delete table"
msgstr "No s'ha pogut suprimir la taula"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to get orders count"
msgstr "No s'ha pogut obtenir el nombre de comandes"

#. module: pos_restaurant
#: model:product.product,uom_name:pos_restaurant.coke
#: model:product.product,uom_name:pos_restaurant.minute_maid
#: model:product.product,uom_name:pos_restaurant.pos_food_4formaggi
#: model:product.product,uom_name:pos_restaurant.pos_food_bacon
#: model:product.product,uom_name:pos_restaurant.pos_food_bolo
#: model:product.product,uom_name:pos_restaurant.pos_food_cheeseburger
#: model:product.product,uom_name:pos_restaurant.pos_food_chicken
#: model:product.product,uom_name:pos_restaurant.pos_food_chirashi
#: model:product.product,uom_name:pos_restaurant.pos_food_club
#: model:product.product,uom_name:pos_restaurant.pos_food_funghi
#: model:product.product,uom_name:pos_restaurant.pos_food_maki
#: model:product.product,uom_name:pos_restaurant.pos_food_margherita
#: model:product.product,uom_name:pos_restaurant.pos_food_mozza
#: model:product.product,uom_name:pos_restaurant.pos_food_salmon
#: model:product.product,uom_name:pos_restaurant.pos_food_temaki
#: model:product.product,uom_name:pos_restaurant.pos_food_tuna
#: model:product.product,uom_name:pos_restaurant.pos_food_vege
#: model:product.product,uom_name:pos_restaurant.water
#: model:product.template,uom_name:pos_restaurant.coke_product_template
#: model:product.template,uom_name:pos_restaurant.minute_maid_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_4formaggi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_bacon_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_bolo_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_cheeseburger_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_chicken_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_chirashi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_club_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_funghi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_maki_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_margherita_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_mozza_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_salmon_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_temaki_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_tuna_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_vege_product_template
#: model:product.template,uom_name:pos_restaurant.water_product_template
msgid "Units"
msgstr "Unitats"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr "Ordre no sincronizada"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__sequence
msgid "Used to sort Floors"
msgstr "Utilitzat per a ordenar sales "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "IVA:"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_vege
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Vegetarian"
msgstr "Vegetarià"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "Posició Vertical"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.water
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Aigua"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Amplada"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "With a"
msgstr "Amb un "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Yellow"
msgstr "Groc"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "You cannot put a number that exceeds %s "
msgstr "No podeu posar un número que excedeixi %s"

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""
"No es pot treure un terra que s'utilitza en una sessió de PoS, tancar primer"
" les session(s):\n"

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr ""
"No es pot eliminar una taula que s'utilitza en una sessió de PoS, tancar "
"primer les session(s)."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "________________________"
msgstr "________________________"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "______________________________________________"
msgstr "______________________________________________"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "at"
msgstr "a les"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "at table"
msgstr "a la taula "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "button in the editing toolbar to create new tables."
msgstr "botó a la barra d'edició per a crear noves taules."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "discount"
msgstr "descompte "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "env.pos.config.set_tip_after_payment and !currentOrder.is_paid()"
msgstr "env.pos.config.set_tip_after_payment and !currentOrder.is_paid()"

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Cash Bar"
msgstr "Barra de Caixa"
