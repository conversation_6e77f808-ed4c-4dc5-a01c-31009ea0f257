<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_partner_property_form" model="ir.ui.view">
            <field name="name">res.partner.purchase.property.form.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="priority">36</field>
            <field name="arch" type="xml">
                <group name="purchase" position="inside">
                    <div name="receipt_reminder" colspan="2" class="o_checkbox_optional_field" groups='purchase.group_send_reminder'>
                        <label for="receipt_reminder_email"/>
                        <field name="receipt_reminder_email"/>
                        <div attrs="{'invisible': [('receipt_reminder_email', '=', False)]}">
                            <field name="reminder_date_before_receipt" class="oe_inline"/>
                            <span> day(s) before</span>
                        </div>
                    </div>
                    <field name="property_purchase_currency_id" options="{'no_create': True, 'no_open': True}" groups='base.group_multi_currency'/>
                </group>
            </field>
    </record>

    <record id="act_res_partner_2_purchase_order" model="ir.actions.act_window">
            <field name="name">RFQs and Purchases</field>
            <field name="res_model">purchase.order</field>
            <field name="view_mode">tree,form,graph</field>
            <field name="context">{'search_default_partner_id': active_id, 'default_partner_id': active_id}</field>
            <field name="groups_id" eval="[(4, ref('purchase.group_purchase_user'))]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    This vendor has no purchase order. Create a new RfQ
                </p><p>
                    The request for quotation is the first step of the purchases flow. Once
                    converted into a purchase order, you will be able to control the receipt
                    of the products and the vendor bill.
                </p>
            </field>
        </record>

        <!-- Partner kanban view inherited -->
        <record model="ir.ui.view" id="purchase_partner_kanban_view">
            <field name="name">res.partner.kanban.purchaseorder.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.res_partner_kanban_view"/>
            <field name="groups_id" eval="[(4, ref('purchase.group_purchase_user'))]"/>
            <field name="arch" type="xml">
                <field name="mobile" position="after">
                    <field name="purchase_order_count"/>
                </field>
                <xpath expr="//span[hasclass('oe_kanban_partner_links')]" position="inside">
                    <span t-if="record.purchase_order_count.value>0" class="badge badge-pill"><i class="fa fa-fw fa-credit-card" role="img" aria-label="Purchases" title="Purchases"/><t t-esc="record.purchase_order_count.value"/></span>
                </xpath>
            </field>
        </record>

    <record id="act_res_partner_2_supplier_invoices" model="ir.actions.act_window">
            <field name="name">Vendor Bills</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,form,graph</field>
            <field name="domain">[('move_type','in',('in_invoice', 'in_refund'))]</field>
            <field name="context">{'search_default_partner_id': active_id, 'default_move_type': 'in_invoice', 'default_partner_id': active_id}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Record a new vendor bill
                </p><p>
                    Vendors bills can be pre-generated based on purchase
                    orders or receipts. This allows you to control bills
                    you receive from your vendor according to the draft
                    document in Odoo.
                </p>
            </field>
        </record>

        <record id="res_partner_view_purchase_buttons" model="ir.ui.view">
            <field name="name">res.partner.view.purchase.buttons</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form" />
            <field name="priority" eval="9"/>
            <field name="groups_id" eval="[(4, ref('purchase.group_purchase_user'))]"/>
            <field name="arch" type="xml">
                <div name="button_box" position="inside">
                    <button class="oe_stat_button" name="%(purchase.act_res_partner_2_purchase_order)d" type="action"
                        groups="purchase.group_purchase_user"
                        icon="fa-credit-card">
                        <field string="Purchases" name="purchase_order_count" widget="statinfo"/>
                    </button>
                </div>
                <page name="internal_notes" position="inside">
                    <group colspan="2" col="2" groups="purchase.group_warning_purchase">
                        <separator string="Warning on the Purchase Order" colspan="4"/>
                        <field name="purchase_warn" nolabel="1" />
                        <field name="purchase_warn_msg" colspan="3" nolabel="1"
                            attrs="{'required':[('purchase_warn','!=', False), ('purchase_warn','!=','no-message')], 'invisible':[('purchase_warn','in',(False,'no-message'))]}"/>
                    </group>
                </page>
            </field>
        </record>

        <record id="res_partner_view_purchase_account_buttons" model="ir.ui.view">
            <field name="name">res.partner.view.purchase.account.buttons</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form" />
            <field name="priority" eval="12"/>
            <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="arch" type="xml">
                <div name="button_box" position="inside">
                    <button class="oe_stat_button" name="%(purchase.act_res_partner_2_supplier_invoices)d" type="action"
                        icon="fa-pencil-square-o" help="Vendor Bills">
                        <field string="Vendor Bills" name="supplier_invoice_count" widget="statinfo"/>
                    </button>
                </div>
            </field>
        </record>
</odoo>
