<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="payment.payment_acquirer_alipay" model="payment.acquirer">
        <field name="provider">alipay</field>
        <field name="redirect_form_view_id" ref="redirect_form"/>
        <field name="support_authorization">False</field>
        <field name="support_fees_computation">True</field>
        <field name="support_refund"></field>
        <field name="support_tokenization">False</field>
    </record>

    <record id="payment_method_alipay" model="account.payment.method">
        <field name="name">Alipay</field>
        <field name="code">alipay</field>
        <field name="payment_type">inbound</field>
    </record>

</odoo>
