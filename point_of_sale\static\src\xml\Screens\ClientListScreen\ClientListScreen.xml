<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="ClientListScreen" owl="1">
        <div class="clientlist-screen screen" t-on-activate-edit-mode="activateEditMode">
            <div class="screen-content">
                <div class="top-content">
                    <div class="button back" t-on-click="back">
                        <i class="fa fa-angle-double-left"/>
                        <t t-if="!env.isMobile"> Discard</t>
                    </div>
                    <div t-if="!state.detailIsShown &amp;&amp; !state.selectedClient" class="button new-customer" role="img" aria-label="Add a customer"
                            t-on-click="trigger('activate-edit-mode', { isNewClient: true })"
                            title="Add a customer">
                        <i class="fa fa-plus"/>
                        <t t-if="!env.isMobile"> Create</t>
                    </div>
                    <div t-if="isNextButtonVisible" t-on-click="clickNext"
                          class="button next highlight">
                        <t t-if="!env.isMobile">
                            <t t-esc="nextButton.text" />
                        </t>
                        <t t-else="">
                            <i t-if="nextButton.command === 'deselect'" class="fa fa-trash"/>
                            <i t-elif="nextButton.command === 'set'" class="fa fa-check"/>
                        </t>
                    </div>
                    <div class="button" t-if="state.detailIsShown" t-on-click="trigger('click-save')">
                        <i class="fa fa-floppy-o"/>
                        <t t-if="!env.isMobile">
                            <span> Save</span>
                        </t>
                    </div>
                    <div t-if="!state.detailIsShown" class="searchbox-client top-content-center">
                        <input placeholder="Search Customers" size="1" t-on-keyup="updateClientList" />
                        <span class="search-clear-client"></span>
                    </div>
                    <div class="button back" t-on-click="searchClient()" t-if="!state.detailIsShown">
                        <span class="database-icon">
                            <i class="fa fa-database"/>
                        </span>
                        <t t-if="!env.isMobile">
                            <span class="load-customer-search">
                                Load Customers
                            </span>
                        </t>
                    </div>
                </div>
                <section class="full-content">
                    <div class="client-window">
                        <section class="subwindow collapsed">
                            <div class="subwindow-container collapsed">
                                <div t-if="state.detailIsShown" class="client-details-contents subwindow-container-fix">
                                    <ClientDetailsEdit t-props="state.editModeProps"
                                                       t-on-cancel-edit="cancelEdit"/>
                                </div>
                            </div>
                        </section>
                        <section class="subwindow list">
                            <div class="subwindow-container">
                                <div t-if="!state.detailIsShown" class="subwindow-container-fix scrollable-y">
                                    <table class="client-list">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th t-if="!env.isMobile">Address</th>
                                                <th t-if="!env.isMobile">Phone</th>
                                                <th t-if="env.isMobile">ZIP</th>
                                                <th class="client-line-email">Email</th>
                                                <th class="client-line-last-column-placeholder oe_invisible"></th>
                                            </tr>
                                        </thead>
                                        <tbody class="client-list-contents">
                                            <t t-foreach="clients" t-as="partner"
                                               t-key="partner.id">
                                                <ClientLine partner="partner"
                                                            selectedClient="state.selectedClient"
                                                            detailIsShown="state.detailIsShown"
                                                            t-on-click-client="clickClient" />
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </section>
                    </div>
                </section>
            </div>
        </div>
    </t>

</templates>
