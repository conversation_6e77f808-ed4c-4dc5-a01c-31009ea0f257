# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_vat
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Basque (https://www.transifex.com/odoo/teams/41243/eu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: eu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Enpresak"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr ""

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, you will not be able to save a contact if its "
"VAT number cannot be verified by the European VIES service."
msgstr ""

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:134
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] does not seem to be valid. \n"
"Note: the expected format is %s"
msgstr ""

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:133
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] either failed the VIES VAT validation "
"check or did not respect the expected format %s."
msgstr ""

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_form
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_short_form
msgid "VAT"
msgstr ""

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company_vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings_vat_check_vies
msgid "Verify VAT Numbers"
msgstr ""

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr ""

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "res.config.settings"
msgstr ""
