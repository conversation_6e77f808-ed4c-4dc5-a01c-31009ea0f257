<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="ProductList" owl="1">
        <div class="product-list-container">
            <div t-if="props.products.length != 0" class="product-list">
                <t t-foreach="props.products" t-as="product" t-key="product.id">
                    <ProductItem product="product" />
                </t>
            </div>
            <div t-else="" class="product-list-empty">
                <div class="product-list-empty">
                    <t t-if="props.searchWord !== ''">
                        <p>
                            No results found for "
                            <b t-esc="props.searchWord"></b>
                            ".
                        </p>
                    </t>
                    <t t-else="">
                        <p>There are no products in this category.</p>
                    </t>
                </div>
            </div>
        </div>
    </t>

</templates>
