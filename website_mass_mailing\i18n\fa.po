# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_mass_mailing
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: ghasem yaghoubi <<EMAIL>>, 2022\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid ", .o_newsletter_popup"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "Always <b>First</b>."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block
msgid "Always First."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block
msgid "Be the first to find out all the latest news, products, and trends."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid ""
"Be the first to find out all the latest news,<br/> products, and trends."
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/xml/website_mass_mailing.xml:0
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
#, python-format
msgid "Close"
msgstr "بستن"

#. module: website_mass_mailing
#: model:ir.model,name:website_mass_mailing.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social_left
msgid "Contact"
msgstr "مخاطب"

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:0
#, python-format
msgid "Display Thanks Button"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:0
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:0
#, python-format
msgid "Error"
msgstr "خطا"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid "Newsletter"
msgstr "خبرنامه"

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:0
#, python-format
msgid ""
"No mailing list found, do you want to create a new one? This will save all "
"your changes, are you sure you want to proceed?"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid "Show reCaptcha Policy"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Subscribe"
msgstr "عضویت"

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:0
#, python-format
msgid "Success"
msgstr "موفق"

#. module: website_mass_mailing
#: code:addons/website_mass_mailing/controllers/main.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Thanks"
msgstr "با تشکر"

#. module: website_mass_mailing
#: code:addons/website_mass_mailing/controllers/main.py:0
#, python-format
msgid "Thanks for subscribing!"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "your email..."
msgstr ""
