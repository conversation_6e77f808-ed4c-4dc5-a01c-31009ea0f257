<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="payroll_allowances_withoutaccount_form_wizard" model="ir.ui.view">
            <field name="name">Payroll Allowances WithoutAccount</field>
            <field name="model">payroll.allowances.withoutaccount</field>
            <field name="arch" type="xml">
                <form string="Payroll allowances WithoutAccount Report">
                    <group>
                        <group>
                            <field name="work_location" class="oe_inline"/>
                        </group>
                    </group>

                    <group>
                        <group>
                            <field name="date_from" class="oe_inline"/>
                        </group>
                        <group>
                            <field name="date_to" class="oe_inline"/>
                        </group>
                    </group>


                    <footer>
                        <button name="generate_payroll_allowances" type="object" string="Print" class="oe_highlight"/>
                        <button type="object" string="Cancel" special="cancel"/>
                    </footer>

                </form>
            </field>
        </record>

        <record id="payroll_allowances_withoutaccount_action_wizard" model="ir.actions.act_window">
            <field name="name"> كشف بالمرتب وتفصيل الإضافات بدون حساب مصرفي</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">payroll.allowances.withoutaccount</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="payroll_allowances_withoutaccount_form_wizard"/>
            <field name="target">new</field>
        </record>

        <!-- This Menu Item Must have a parent -->
        <menuitem id="create_payroll_allowances_withoutaccount" name="كشف بالمرتب وتفصيل الإضافات بدون حساب مصرفي"
                  parent="payroll_reports_categ"
                  action="payroll_allowances_withoutaccount_action_wizard"
                  sequence="1"/>


    </data>
</odoo>