.oe_website_sale {
    .td-wish-btn {
        width: 140px;
    }

    div.css_not_available .o_add_wishlist_dyn {
        display: none;
    }

    .btn.o_add_wishlist_dyn {
        line-height: 1rem;
        transform: scale(0.9);
        transition: transform 300ms;

        &:hover:not(.disabled) {
            background-color: transparent;
            color: theme-color('primary');
            transform: scale(1);
        }
        &.disabled i::before {
            content: "\f004";
        }
    }
}

// XS size
@include media-breakpoint-down(sm) {
    .oe_website_sale {
        .td-wish-btn {
            width: 100px;
        }
    }
}

table.table-comparator .td-img img {
    // allows sizing the placeholder image to the "image" size of 100px
    max-height: 100px;
}