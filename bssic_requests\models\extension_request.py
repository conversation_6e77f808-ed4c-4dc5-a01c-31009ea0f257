from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BSSICExtensionRequest(models.Model):
    """Device Extension Request Model - Proxy to bssic.request"""
    _name = 'bssic.extension.request'
    _description = 'BSSIC Device Extension Request'
    _inherit = 'bssic.request'
    _auto = False  # Don't create database table
    _table = 'bssic_request'  # Use the same table as bssic.request

    # Extension request specific fields
    extension_duration = fields.Char('Required Extension Period', tracking=True)
    extension_reason = fields.Text('Reason for Extension', tracking=True)

    @api.constrains('extension_duration', 'extension_reason', 'state', 'show_extension_fields')
    def _check_required_extension_fields(self):
        """Validate required fields for extension requests"""
        for record in self:
            if record.show_extension_fields and record.state != 'draft':
                if not record.extension_duration:
                    raise UserError(_('Required Extension Period is required for Extension requests.'))
                if not record.extension_reason:
                    raise UserError(_('Reason for Extension is required for Extension requests.'))

    @api.model
    def create(self, vals):
        """Redirect create to bssic.request with extension defaults"""
        # Set request type code for extension
        if not vals.get('request_type_code'):
            vals['request_type_code'] = 'extension'

        # Find and set the extension request type
        if not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'extension')
            ], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        return self.env['bssic.request'].create(vals)

    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """Redirect search to bssic.request with extension filter"""
        request_model = self.env['bssic.request']
        extension_args = args + [('request_type_id.code', '=', 'extension')]
        return request_model.search(extension_args, offset=offset, limit=limit, order=order, count=count)

    def write(self, vals):
        """Redirect write to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).write(vals)

    def unlink(self):
        """Redirect unlink to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).unlink()

    @api.model
    def browse(self, ids):
        """Redirect browse to bssic.request"""
        return self.env['bssic.request'].browse(ids)

    def read(self, fields=None, load='_classic_read'):
        """Redirect read to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).read(fields=fields, load=load)

    @api.model
    def default_get(self, fields_list):
        """Set default values for extension requests"""
        res = super(BSSICExtensionRequest, self).default_get(fields_list)
        
        # Set default request type for extension
        if 'request_type_code' in fields_list:
            res['request_type_code'] = 'extension'
        
        if 'request_type_id' in fields_list:
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'extension')
            ], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id

        return res
