<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="stock_move_purchase" model="ir.ui.view">
            <field name="name">stock.move.form</field>
            <field name="model">stock.move</field>
            <field name="inherit_id" ref="stock.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='origin_grp']" position="inside">
                    <field name="purchase_line_id"/>
                </xpath>
            </field>
        </record>

        <record id="view_warehouse_inherited" model="ir.ui.view">
            <field name="name">Stock Warehouse Inherited</field>
            <field name="model">stock.warehouse</field>
            <field name="inherit_id" ref="stock.view_warehouse"/>
            <field name="arch" type="xml">
                 <xpath expr="//field[@name='resupply_wh_ids']" position="before">
                    <field name="buy_to_resupply"/>
                </xpath>
                <xpath expr="//group[@name='group_resupply']" position="attributes">
                    <attribute name="groups">stock.group_adv_location,stock.group_stock_multi_warehouses</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_warehouse_orderpoint_tree_editable_inherited_mrp" model="ir.ui.view">
            <field name="name">stock.warehouse.orderpoint.tree.editable.inherit.mrp</field>
            <field name="model">stock.warehouse.orderpoint</field>
            <field name="inherit_id" ref="stock.view_warehouse_orderpoint_tree_editable"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_stock_replenishment_info']" position="before">
                    <field name="show_supplier" invisible="1"/>
                    <field name="supplier_id" string="Vendor" optional="show" attrs="{'invisible': [('show_supplier', '=', False)]}" options="{'no_create': True}"/>
                </xpath>
            </field>
        </record>

</odoo>
