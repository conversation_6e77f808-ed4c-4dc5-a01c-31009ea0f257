<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <path id="path-1" d="M0 1v27a1 1 0 0 0 1 1h40V1a1 1 0 0 0-1-1H1a1 1 0 0 0-1 1zm1 27.188V1h39v13.797l1 1.217V29H23l-1.5-.812H1z"/>
    <filter id="filter-2" width="102.4%" height="106.9%" x="-1.2%" y="-1.7%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-3" d="M17 11v1h-4v-1h4zm6-3v1H13V8h10zm7-3v1H13V5h17z"/>
    <filter id="filter-4" width="105.9%" height="128.6%" x="-2.9%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-5" width="16" height="2" x="13" y="0"/>
    <filter id="filter-6" width="106.2%" height="200%" x="-3.1%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-7" width="10.353" height="13.349" x="0" y="0"/>
    <linearGradient id="linearGradient-9" x1="72.875%" x2="40.332%" y1="46.142%" y2="32.596%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-10" x1="88.517%" x2="50%" y1="38.363%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <linearGradient id="linearGradient-11" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <path id="path-12" d="M44 20.781a7.843 7.843 0 0 1-1.85 1.957 11.013 11.013 0 0 1-.423 3.533 11.124 11.124 0 0 1-1.319 2.913c-.59.93-1.293 1.752-2.107 2.466-.815.715-1.797 1.286-2.947 1.711-1.15.426-2.38.639-3.689.639-2.063 0-3.952-.566-5.665-1.7.266.032.563.048.89.048 1.714 0 3.24-.54 4.58-1.618a3.539 3.539 0 0 1-2.146-.755 3.701 3.701 0 0 1-1.302-1.87c.25.04.483.06.696.06.328 0 .651-.044.971-.13a3.614 3.614 0 0 1-2.119-1.306c-.56-.692-.839-1.495-.839-2.409v-.047a3.59 3.59 0 0 0 1.667.48 3.747 3.747 0 0 1-1.199-1.347 3.764 3.764 0 0 1-.445-1.804c0-.688.167-1.325.502-1.91a10.515 10.515 0 0 0 3.364 2.794c1.321.7 2.735 1.088 4.243 1.166a4.31 4.31 0 0 1-.091-.867c0-1.047.36-1.94 1.079-2.678.72-.738 1.59-1.107 2.61-1.107 1.066 0 1.964.398 2.695 1.195a7.123 7.123 0 0 0 2.341-.914 3.66 3.66 0 0 1-1.621 2.086A7.204 7.204 0 0 0 44 20.781z"/>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_twitter_scroll">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(19 13)">
        <g class="shape">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
        </g>
        <g class="group_2" transform="translate(5 6)">
          <g class="combined_shape">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
          <g class="rectangle">
            <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-5"/>
          </g>
          <g class="image_1_border">
            <rect width="11" height="14" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.324 .326)">
              <mask id="mask-8" fill="#fff">
                <use xlink:href="#path-7"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-7"/>
              <ellipse cx="6.632" cy="3.419" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="2.426" ry="2.442"/>
              <ellipse cx="10.515" cy="14.651" fill="url(#linearGradient-9)" class="oval" mask="url(#mask-8)" rx="7.603" ry="4.884"/>
              <ellipse cx="-5.662" cy="14.814" fill="url(#linearGradient-10)" class="oval" mask="url(#mask-8)" rx="12.132" ry="7.651"/>
            </g>
            <path fill="#FFF" d="M11 0v14H0V0h11zm-1 1H1v12h9V1z" class="rectangle_2"/>
          </g>
        </g>
        <mask id="mask-13" fill="#fff">
          <use xlink:href="#path-12"/>
        </mask>
        <use fill="url(#linearGradient-11)" class="twitter" xlink:href="#path-12"/>
      </g>
    </g>
  </g>
</svg>
