<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="ri_tax_percepcion_iva_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IVA Aplicada</field>
        <field name="description">Perc IVA A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_percepcion_iva_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_percepcion_iva_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iva"/>
    </record>

    <record id="ri_tax_percepcion_ganancias_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción Ganancias Aplicada</field>
        <field name="description">Perc Ganancias A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_percepcion_ganancias_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_percepcion_ganancias_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_ganancias"/>
    </record>

    <record id="ri_tax_percepcion_ganancias_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción Ganancias Sufrida</field>
        <field name="description">Perc Ganancias S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_ganancias_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_ganancias_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_ganancias"/>
    </record>

    <record id="ri_tax_percepcion_iibb_caba_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB CABA Sufrida</field>
        <field name="description">Perc IIBB CABA S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_caba_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_caba_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_caba"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ba_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB ARBA Sufrida</field>
        <field name="description">Perc IIBB ARBA S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ba_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ba_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ba"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ca_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Catamarca Sufrida</field>
        <field name="description">Perc IIBB Catamarca S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ca_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ca_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ca"/>
    </record>

    <record id="ri_tax_percepcion_iibb_co_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Córdoba Sufrida</field>
        <field name="description">Perc IIBB Córdoba S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_co_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_co_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_co"/>
    </record>

    <record id="ri_tax_percepcion_iibb_rr_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Corrientes Sufrida</field>
        <field name="description">Perc IIBB Corrientes S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_rr_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_rr_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_rr"/>
    </record>

    <record id="ri_tax_percepcion_iibb_er_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Entre Ríos Sufrida</field>
        <field name="description">Perc IIBB Entre Ríos S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_er_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_er_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_er"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ju_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Jujuy Sufrida</field>
        <field name="description">Perc IIBB Jujuy S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ju_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ju_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ju"/>
    </record>

    <record id="ri_tax_percepcion_iibb_za_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Mendoza Sufrida</field>
        <field name="description">Perc IIBB Mendoza S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_za_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_za_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_za"/>
    </record>

    <record id="ri_tax_percepcion_iibb_lr_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB La Rioja Sufrida</field>
        <field name="description">Perc IIBB La Rioja S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_lr_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_lr_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_lr"/>
    </record>

    <record id="ri_tax_percepcion_iibb_sa_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Salta Sufrida</field>
        <field name="description">Perc IIBB Salta S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_sa_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_sa_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_sa"/>
    </record>

    <record id="ri_tax_percepcion_iibb_nn_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB San Juan Sufrida</field>
        <field name="description">Perc IIBB San Juan S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_nn_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_nn_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_nn"/>
    </record>

    <record id="ri_tax_percepcion_iibb_sl_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB San Luis Sufrida</field>
        <field name="description">Perc IIBB San Luis S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_sl_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_sl_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_sl"/>
    </record>

    <record id="ri_tax_percepcion_iibb_sf_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Santa Fe Sufrida</field>
        <field name="description">Perc IIBB Santa Fe S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_sf_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_sf_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_sf"/>
    </record>

    <record id="ri_tax_percepcion_iibb_se_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Santiago del Estero Sufrida</field>
        <field name="description">Perc IIBB Santiago del Estero S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_se_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_se_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_se"/>
    </record>

    <record id="ri_tax_percepcion_iibb_tn_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Tucumán Sufrida</field>
        <field name="description">Perc IIBB Tucumán S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_tn_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_tn_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_tn"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ha_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Chaco Sufrida</field>
        <field name="description">Perc IIBB Chaco S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ha_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ha_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ha"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ct_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Chubut Sufrida</field>
        <field name="description">Perc IIBB Chubut S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ct_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ct_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ct"/>
    </record>

    <record id="ri_tax_percepcion_iibb_fo_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Formosa Sufrida</field>
        <field name="description">Perc IIBB Formosa S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_fo_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_fo_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_fo"/>
    </record>

    <record id="ri_tax_percepcion_iibb_mi_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Misiones Sufrida</field>
        <field name="description">Perc IIBB Misiones S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_mi_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_mi_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_mi"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ne_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Neuquén Sufrida</field>
        <field name="description">Perc IIBB Neuquén S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ne_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_ne_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ne"/>
    </record>

    <record id="ri_tax_percepcion_iibb_lp_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB La Pampa Sufrida</field>
        <field name="description">Perc IIBB La Pampa S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_lp_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_lp_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_lp"/>
    </record>

    <record id="ri_tax_percepcion_iibb_rn_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Río Negro Sufrida</field>
        <field name="description">Perc IIBB Río Negro S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_rn_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_rn_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_rn"/>
    </record>

    <record id="ri_tax_percepcion_iibb_az_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Santa Cruz Sufrida</field>
        <field name="description">Perc IIBB Santa Cruz S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_az_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_az_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_az"/>
    </record>

    <record id="ri_tax_percepcion_iibb_tf_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_base_chart_template"/>
        <field name="name">Percepción IIBB Tierra del Fuego Sufrida</field>
        <field name="description">Perc IIBB Tierra del Fuego S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_tf_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('base_percepcion_iibb_tf_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_tf"/>
    </record>

    <record id="ri_tax_percepcion_iibb_caba_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB CABA Aplicada</field>
        <field name="description">Perc IIBB CABA A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_caba_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_caba_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_caba"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ba_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB ARBA Aplicada</field>
        <field name="description">Perc IIBB ARBA A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ba_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ba_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ba"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ca_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Catamarca Aplicada</field>
        <field name="description">Perc IIBB Catamarca A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ca_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ca_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ca"/>
    </record>

    <record id="ri_tax_percepcion_iibb_co_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Córdoba Aplicada</field>
        <field name="description">Perc IIBB Córdoba A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_co_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_co_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_co"/>
    </record>

    <record id="ri_tax_percepcion_iibb_rr_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Corrientes Aplicada</field>
        <field name="description">Perc IIBB Corrientes A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_rr_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_rr_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_rr"/>
    </record>

    <record id="ri_tax_percepcion_iibb_er_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Entre Ríos Aplicada</field>
        <field name="description">Perc IIBB Entre Ríos A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_er_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_er_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_er"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ju_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Jujuy Aplicada</field>
        <field name="description">Perc IIBB Jujuy A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ju_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ju_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ju"/>
    </record>

    <record id="ri_tax_percepcion_iibb_za_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Mendoza Aplicada</field>
        <field name="description">Perc IIBB Mendoza A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_za_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_za_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_za"/>
    </record>

    <record id="ri_tax_percepcion_iibb_lr_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB La Rioja Aplicada</field>
        <field name="description">Perc IIBB La Rioja A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_lr_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_lr_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_lr"/>
    </record>

    <record id="ri_tax_percepcion_iibb_sa_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Salta Aplicada</field>
        <field name="description">Perc IIBB Salta A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_sa_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_sa_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_sa"/>
    </record>

    <record id="ri_tax_percepcion_iibb_nn_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB San Juan Aplicada</field>
        <field name="description">Perc IIBB San Juan A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_nn_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_nn_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_nn"/>
    </record>

    <record id="ri_tax_percepcion_iibb_sl_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB San Luis Aplicada</field>
        <field name="description">Perc IIBB San Luis A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_sl_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_sl_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_sl"/>
    </record>

    <record id="ri_tax_percepcion_iibb_sf_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Santa Fe Aplicada</field>
        <field name="description">Perc IIBB Santa Fe A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_sf_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_sf_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_sf"/>
    </record>

    <record id="ri_tax_percepcion_iibb_se_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Santiago del Estero Aplicada</field>
        <field name="description">Perc IIBB Santiago del Estero A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_se_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_se_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_se"/>
    </record>

    <record id="ri_tax_percepcion_iibb_tn_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Tucumán Aplicada</field>
        <field name="description">Perc IIBB Tucumán A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_tn_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_tn_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_tn"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ha_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Chaco Aplicada</field>
        <field name="description">Perc IIBB Chaco A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ha_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ha_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ha"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ct_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Chubut Aplicada</field>
        <field name="description">Perc IIBB Chubut A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ct_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ct_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ct"/>
    </record>

    <record id="ri_tax_percepcion_iibb_fo_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Formosa Aplicada</field>
        <field name="description">Perc IIBB Formosa A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_fo_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_fo_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_fo"/>
    </record>

    <record id="ri_tax_percepcion_iibb_mi_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Misiones Aplicada</field>
        <field name="description">Perc IIBB Misiones A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_mi_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_mi_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_mi"/>
    </record>

    <record id="ri_tax_percepcion_iibb_ne_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Neuquén Aplicada</field>
        <field name="description">Perc IIBB Neuquén A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ne_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_ne_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_ne"/>
    </record>

    <record id="ri_tax_percepcion_iibb_lp_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB La Pampa Aplicada</field>
        <field name="description">Perc IIBB La Pampa A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_lp_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_lp_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_lp"/>
    </record>

    <record id="ri_tax_percepcion_iibb_rn_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Río Negro Aplicada</field>
        <field name="description">Perc IIBB Río Negro A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_rn_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_rn_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_rn"/>
    </record>

    <record id="ri_tax_percepcion_iibb_az_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Santa Cruz Aplicada</field>
        <field name="description">Perc IIBB Santa Cruz A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_az_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_az_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_az"/>
    </record>

    <record id="ri_tax_percepcion_iibb_tf_aplicada" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ex_chart_template"/>
        <field name="name">Percepción IIBB Tierra del Fuego Aplicada</field>
        <field name="description">Perc IIBB Tierra del Fuego A</field>
        <field name="sequence">4</field>
        <field name="active" eval="False"/>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_tf_aplicada'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_ar.ri_percepcion_iibb_tf_aplicada'),
            }),
        ]"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iibb_tf"/>
    </record>

    <!-- Only for Responsable Inscription account.chart.template -->

    <!-- Remove this record in V14 -->
    <record id="ri_tax_vat_no_corresponde_ventas" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA No Corresponde</field>
        <field name="name">IVA No Corresponde</field>
        <field name="active" eval="False"/>
        <field name="sequence">2</field>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_no_corresponde"/>
        <field name="type_tax_use">sale</field>
    </record>

    <record id="ri_tax_vat_no_corresponde_compras" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA No Corresponde</field>
        <field name="name">IVA No Corresponde</field>
        <field name="sequence">2</field>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_no_corresponde"/>
        <field name="type_tax_use">purchase</field>
    </record>

    <record id="ri_tax_vat_no_gravado_ventas" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA No Gravado</field>
        <field name="name">IVA No Gravado</field>
        <field name="sequence">2</field>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_no_gravado"/>
        <field name="type_tax_use">sale</field>
    </record>

    <record id="ri_tax_vat_no_gravado_compras" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA No Gravado</field>
        <field name="name">IVA No Gravado</field>
        <field name="sequence">2</field>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_no_gravado"/>
        <field name="type_tax_use">purchase</field>
    </record>

    <record id="ri_tax_vat_exento_ventas" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA Exento</field>
        <field name="name">IVA Exento</field>
        <field name="sequence">2</field>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_exento"/>
        <field name="type_tax_use">sale</field>
    </record>

    <record id="ri_tax_vat_exento_compras" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA Exento</field>
        <field name="name">IVA Exento</field>
        <field name="sequence">2</field>
        <field name="amount_type">fixed</field>
        <field eval="0.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_exento"/>
        <field name="type_tax_use">purchase</field>
    </record>

    <record id="ri_tax_vat_0_ventas" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA 0%</field>
        <field name="name">IVA 0%</field>
        <field name="sequence">2</field>
        <field eval="0.0" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_0"/>
        <field name="type_tax_use">sale</field>
    </record>

    <record id="ri_tax_vat_0_compras" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA 0%</field>
        <field name="name">IVA 0%</field>
        <field name="sequence">2</field>
        <field eval="0.0" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_0"/>
        <field name="type_tax_use">purchase</field>
    </record>

    <record id="ri_tax_vat_10_ventas" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA 10.5%</field>
        <field name="name">IVA 10.5%</field>
        <field name="sequence">2</field>
        <field eval="10.5" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_105"/>
        <field name="type_tax_use">sale</field>
    </record>

    <record id="ri_tax_vat_10_compras" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="description">IVA 10.5%</field>
        <field name="name">IVA 10.5%</field>
        <field name="sequence">2</field>
        <field eval="10.5" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_105"/>
        <field name="type_tax_use">purchase</field>
    </record>

    <record id="ri_tax_vat_21_ventas" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">IVA 21%</field>
        <field name="description">IVA 21%</field>
        <field name="sequence">1</field>
        <field eval="21" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_21"/>
        <field name="type_tax_use">sale</field>
    </record>

    <record id="ri_tax_vat_21_compras" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">IVA 21%</field>
        <field name="description">IVA 21%</field>
        <field name="sequence">1</field>
        <field eval="21" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_21"/>
        <field name="type_tax_use">purchase</field>
    </record>

    <record id="ri_tax_vat_27_ventas" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">IVA 27%</field>
        <field name="description">IVA 27%</field>
        <field name="sequence">3</field>
        <field eval="27" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_27"/>
        <field name="type_tax_use">sale</field>
    </record>

    <record id="ri_tax_vat_27_compras" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">IVA 27%</field>
        <field name="description">IVA 27%</field>
        <field name="sequence">3</field>
        <field eval="27" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_27"/>
        <field name="type_tax_use">purchase</field>
    </record>

    <record id="ri_tax_vat_25_ventas" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">IVA 2,5%</field>
        <field name="description">IVA 2,5%</field>
        <field name="sequence">9</field>
        <field name="active" eval="False"/>
        <field eval="2.5" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_025"/>
        <field name="type_tax_use">sale</field>
    </record>

    <record id="ri_tax_vat_25_compras" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">IVA 2,5%</field>
        <field name="description">IVA 2,5%</field>
        <field name="sequence">9</field>
        <field name="active" eval="False"/>
        <field eval="2.5" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_025"/>
        <field name="type_tax_use">purchase</field>
    </record>

    <record id="ri_tax_vat_5_ventas" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">IVA 5%</field>
        <field name="description">IVA 5%</field>
        <field name="sequence">10</field>
        <field name="active" eval="False"/>
        <field eval="5" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_5"/>
        <field name="type_tax_use">sale</field>
    </record>

    <record id="ri_tax_vat_5_compras" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">IVA 5%</field>
        <field name="description">IVA 5%</field>
        <field name="sequence">10</field>
        <field name="active" eval="False"/>
        <field eval="5" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_credito_fiscal'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_iva_debito_fiscal'),
            }),
        ]"/>
        <field name="tax_group_id" ref="l10n_ar.tax_group_iva_5"/>
        <field name="type_tax_use">purchase</field>
    </record>

    <record id="ri_tax_percepcion_iva_sufrida" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">Percepción IVA Sufrida</field>
        <field name="description">Perc IVA S</field>
        <field name="sequence">4</field>
        <field name="amount_type">fixed</field>
        <field eval="1.0" name="amount"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_percepcion_iva_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_percepcion_iva_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iva"/>
    </record>

    <record id="ri_tax_ganancias_iva_adicional" model="account.tax.template">
        <field name="chart_template_id" ref="l10nar_ri_chart_template"/>
        <field name="name">IVA Adicional 20%</field>
        <field name="description">IVA Adicional 20%</field>
        <field name="sequence">4</field>
        <field eval="20.0" name="amount"/>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_percepcion_iva_sufrida'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('ri_percepcion_iva_sufrida'),
            }),
        ]"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="l10n_ar.tax_group_percepcion_iva"/>
    </record>
</odoo>
