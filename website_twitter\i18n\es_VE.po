# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_twitter
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-05-15 18:50+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Venezuela) (http://www.transifex.com/odoo/odoo-9/"
"language/es_VE/)\n"
"Language: es_VE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.website_twitter_snippet
msgid "<span class=\"oe_snippet_thumbnail_title\">Twitter Scroller</span>"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "<strong>Callback URL: </strong> leave it blank"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "<strong>Description: </strong> Odoo Tweet Scroller"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "<strong>Name: </strong> Odoo Tweet Scroller"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "<strong>Website: </strong>"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Accept terms of use and click on the Create button at the bottom"
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:38
#, python-format
msgid ""
"Authentication credentials were missing or incorrect. Maybe screen name "
"tweets are protected."
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Copy/Paste API Key and Secret below"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Create a new Twitter application on"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_display_name
msgid "Display Name"
msgstr "Mostrar nombre"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid ""
"Enter the screen name from which you want to load favorite Tweets (does not "
"need to be the same as the API keys)"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_config_settings_twitter_screen_name
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_screen_name
msgid "Get favorites from this screen name"
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:49
#, python-format
msgid "HTTP Error: Something is misconfigured"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "How to configure the Twitter API access"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_id
msgid "ID"
msgstr "ID"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:62
#, python-format
msgid "Internet connection refused"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet___last_update
msgid "Last Modified on"
msgstr "Modificada por última vez"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_write_uid
msgid "Last Updated by"
msgstr "Última actualización realizada por"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_write_date
msgid "Last Updated on"
msgstr "Ultima actualizacion en"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:64
#: code:addons/website_twitter/models/twitter_config.py:65
#, python-format
msgid "Please double-check your Twitter API Key and Secret!"
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:25
#, python-format
msgid ""
"Please set a Twitter screen name to load favorites from, in the Website "
"Settings (it does not have to be yours)"
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:21
#, python-format
msgid "Please set the Twitter API Key and Secret in the Website Settings."
msgstr ""

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:35
#, python-format
msgid "Reload"
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:40
#, python-format
msgid ""
"Request cannot be served due to the applications rate limit having been "
"exhausted for the resource."
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_screen_name
msgid "Screen Name"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website_config_settings_twitter_screen_name
msgid ""
"Screen Name of the Twitter Account from which you want to load favorites.It "
"does not have to match the API Key/Secret."
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid ""
"Set your Twitter API access below to be able to use the Twitter Scroller "
"Website snippet.<br/>\n"
"                             You can get your API credentials from"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_config_settings_twitter_tutorial
msgid "Show me how to obtain the Twitter API Key and Secret"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Switch to the API Keys tab: <br/>"
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:43
#, python-format
msgid ""
"The Twitter servers are up, but overloaded with requests. Try again later."
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:44
#, python-format
msgid ""
"The Twitter servers are up, but the request could not be serviced due to "
"some failure within our stack. Try again later."
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:39
#, python-format
msgid ""
"The request is understood, but it has been refused or access is not allowed. "
"Please check your Twitter API Key and Secret."
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:37
#, python-format
msgid ""
"The request was invalid or cannot be otherwise served. Requests without "
"authentication are considered invalid and will yield this response."
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:36
#, python-format
msgid "There was no new data to return."
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_tweet_id
msgid "Tweet ID"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_tweet
msgid "Tweets"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Twitter API"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_config_settings_twitter_api_key
#: model:ir.model.fields,help:website_twitter.field_website_twitter_api_key
msgid "Twitter API Key"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website_twitter_api_secret
msgid "Twitter API Secret"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_api_key
msgid "Twitter API key"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website_config_settings_twitter_api_key
msgid "Twitter API key you can get it from https://apps.twitter.com/app/new"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_config_settings_twitter_api_secret
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_api_secret
msgid "Twitter API secret"
msgstr ""

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website_config_settings_twitter_api_secret
msgid "Twitter API secret you can get it from https://apps.twitter.com/app/new"
msgstr ""

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:41
#, python-format
msgid "Twitter Configuration"
msgstr ""

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_twitter_tweet
msgid "Twitter Tweets"
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:65
#, python-format
msgid "Twitter authorization error!"
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:42
#, python-format
msgid "Twitter is down or being upgraded."
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:41
#, python-format
msgid ""
"Twitter seems broken. Please retry later. You may consider posting an issue "
"on Twitter forums to get help."
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:36
#, python-format
msgid ""
"Twitter user @%(username)s has less than 12 favorite tweets. Please add more "
"or choose a different screen name."
msgstr ""

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:61
#: code:addons/website_twitter/models/twitter_config.py:62
#, python-format
msgid "We failed to reach a twitter server."
msgstr ""

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_website_id
msgid "Website"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "https://apps.twitter.com/app/new"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "https://www.odoo.com"
msgstr ""

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_config_settings
msgid "website.config.settings"
msgstr ""

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "with the following values:"
msgstr ""
