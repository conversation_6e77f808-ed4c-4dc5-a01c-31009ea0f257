<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="mrp_production_subcontracting_form_view" model="ir.ui.view">
        <field name="name">mrp.production.subcontracting.form.view</field>
        <field name="model">mrp.production</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="inherit_id" ref="mrp.mrp_production_form_view" />
        <field name="arch" type="xml">
            <xpath expr="//header" position="replace">
                <field name="state" invisible="1"/>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="replace"/>
            <xpath expr="//div[hasclass('oe_title')]" position="replace"/>
            <xpath expr="//group[@name='group_extra_info']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='operations']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='lot_producing_id']" position="attributes">
                <attribute name="attrs">{'invisible': [('product_tracking', 'in', ('none', False))], 'required': [('product_tracking', 'not in', ('none', False))]}</attribute>
            </xpath>
            <xpath expr="//page[@name='miscellaneous']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='bom_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='move_byproduct_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='action_generate_serial']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath> 
            <xpath expr="//field[@name='move_raw_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
                <attribute name="readonly">1</attribute>
            </xpath>
            <xpath expr="//field[@name='move_raw_ids']" position="after">
                <field name="move_line_raw_ids" force_save="1"
                    context="{'tree_view_ref': 'mrp_subcontracting.mrp_subcontracting_stock_move_line_tree_view', 'default_company_id': company_id, 'default_location_id': location_src_id, 'default_location_dest_id': production_location_id}"
                    />
            </xpath>
            <xpath expr="//sheet" position="inside">
                <footer>
                    <button name="subcontracting_record_component" attrs="{'invisible': ['|', ('state', '=', 'to_close'), ('qty_producing', '&lt;', 0.001)]}" string="Continue" type="object" class="oe_highlight" data-hotkey="q"/>
                    <button name="subcontracting_record_component" attrs="{'invisible': [('state', '!=', 'to_close')]}" string="Record Production" type="object" class="oe_highlight" data-hotkey="q"/>
                    <button string="Discard" special="cancel" data-hotkey="z" />
                </footer>
            </xpath>
        </field>
    </record>
</odoo>

