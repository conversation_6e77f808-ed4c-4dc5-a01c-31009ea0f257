# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_links
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid " clicks"
msgstr "نقرات"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid " countries"
msgstr "الدول"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "# of clicks"
msgstr "عدد النقرات "

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.share_page_menu
msgid "<span title=\"Track this page to count clicks\">Link Tracker</span>"
msgstr "<span title=\"Track this page to count clicks\">متتبع الروابط</span>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Campaign</strong>"
msgstr "<strong>حملة</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Medium</strong>"
msgstr "<strong>متوسط </strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Original URL</strong>"
msgstr "<strong>الرابط الأصلي</strong> "

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Redirected URL</strong>"
msgstr "<strong>الرابط المعاد توجيهه</strong> "

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Source</strong>"
msgstr "<strong>المصدر</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Tracked Link</strong>"
msgstr "<strong>الرابط المتتبع</strong> "

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "All Time"
msgstr "كل الوقت"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Campaign <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the context of your link. It might be an event you want to promote or a "
"special promotion.\"/>"
msgstr ""
"حملة <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"يحدد سياق"
" رابطك. قد يكون فعالية ترغب في الترويج لها أو عرضاً خاصاً. \"/> "

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Copied"
msgstr "تم النسخ "

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Copy"
msgstr "نسخ"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "Edit code"
msgstr "تحرير الكود "

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Generating link..."
msgstr "إنشاء رابط ... "

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Get tracked link"
msgstr "احصل على رابط متتبع "

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Icon"
msgstr "الأيقونة"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Month"
msgstr "الشهر الماضي"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Week"
msgstr "الأسبوع الماضي"

#. module: website_links
#: model:ir.model,name:website_links.model_link_tracker
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Link Tracker"
msgstr "متتبع الرابط"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Medium <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the medium used to share your link. It might be an email, or a Facebook Ads "
"for instance.\"/>"
msgstr ""
"الوسط <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"يحدد "
"الوسط المستخدم لمشاركة رابطك. قد يكون بريداً إلكترونياً أو إعلاناً على "
"Facebook على سبيل المثال. \"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Most Clicked"
msgstr "الأكثر نقرا"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Newest"
msgstr "الأحدث"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "No data"
msgstr "لا توجد بيانات"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Recently Used"
msgstr "تم استخدامه حديثاً "

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Share this page with a <strong>short link</strong> that includes "
"<strong>analytics trackers</strong>."
msgstr ""
"شارك هذه الصفحة باستخدام <strong>رابط مختصر</strong> والذي يحتوي "
"على<strong>أدوات التتبع التحليلية</strong>. "

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Source <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the source from which your traffic will come from, Facebook or Twitter for "
"instance.\"/>"
msgstr ""
"المصدر <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"يحدد مصدر"
" الزيارات، من Facebook أو Twitter على سبيل المثال. \"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#: model_terms:ir.ui.view,arch_db:website_links.link_tracker_view_tree
msgid "Statistics"
msgstr "الإحصائيات"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Stats"
msgstr "الإحصائيات"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
#, python-format
msgid "The code cannot be left empty"
msgstr "لا يمكن ترك الكود فارغاً"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "There is no data to show"
msgstr "لا توجد بيانات لعرضها"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
#, python-format
msgid "This code is already taken"
msgstr "هذا الكود مُستخدَم بالفعل"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Those trackers can be used in Google Analytics to track clicks and visitors,"
" or in Odoo reports to track opportunities and related revenues."
msgstr ""
"يمكن استخدام أدوات التتبع هذه في Google Analytics لتتبع النقرات والزوار، أو "
"في تقارير أودو لتتبع الفرص والإيرادات ذات الصلة."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "URL"
msgstr "رابط URL "

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Unable to get recent links"
msgstr "تعذر الحصول على الروابط الحديثة "

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "Undefined"
msgstr "غير محدد"

#. module: website_links
#: code:addons/website_links/models/link_tracker.py:0
#, python-format
msgid "Visit Webpage Statistics"
msgstr "مشاهدة إحصائيات صفحة الويب"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "You don't have any recent links."
msgstr "ليس لديك أي روابط حديثة. "

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Your tracked links"
msgstr "روابطك المتتبعة "

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "cancel"
msgstr "إلغاء"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "clicks"
msgstr "النقرات "

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "copy"
msgstr "نسخ"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Newsletter, Social Network, .."
msgstr "على سبيل المثال النشرة الإخبارية، الشبكة الاجتماعية، .."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Promotion of June, Winter Newsletter, .."
msgstr ""
"على سبيل المثال الترويج للنشرة الإخبارية لشهر يونيو، النشرة البريدية "
"الشتوية، .."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Search Engine, Website page, .."
msgstr "على سبيل المثال محرك بحث، صفحة موقع إلكتروني، .."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "e.g. https://www.odoo.com/contactus"
msgstr "على سبيل المثال https://www.odoo.com/contactus"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "ok"
msgstr "حسناً"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "or"
msgstr "أو"
