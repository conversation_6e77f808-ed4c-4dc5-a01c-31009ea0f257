$notification-width: 320px;
$notification-text-color: #212529;
$notification-title-text-color: #6c757d;

// Colors of the notification are not managed by this file.
// It is handled by the other classes defining a background. (like danger, warning, etc.)
// The notification just add overlays.

.o_notification_manager {
  @include o-position-absolute(3.5rem, 0);
  position: fixed;
  z-index: ($zindex-modal + $zindex-popover) / 2;
  width: $notification-width;
  margin-right: 0.5rem;
  max-width: 100%;

  .o_notification {
    width: 100%;
    margin-bottom: 0.375rem;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.7);
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    border-radius: 0.25rem;

    .o_notification_body {
      padding: 0.5rem 0.625rem;
      background-color: rgba(255, 255, 255, 0.78) !important;
      color: $notification-text-color;

      .o_notification_close {
        margin-left: 1rem;
      }
    }

    .o_notification_header {
      display: flex;
      align-items: center;
      padding: 0.5rem 1.5rem;
      color: $notification-title-text-color;
      background-color: rgba(255, 255, 255, 0.7);
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
  }
}

@include owl-fade(0.5s, "o_notification");
