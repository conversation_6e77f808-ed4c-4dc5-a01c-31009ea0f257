# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_geolocalize
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "(refresh)"
msgstr "(更新)"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid ""
"<br/>\n"
"                                    <span attrs=\"{'invisible': [('date_localization', '=', False)]}\">Updated on: </span>"
msgstr ""
"<br/>\n"
"                                    <span attrs=\"{'invisible': [('date_localization', '=', False)]}\">更新於: </span>"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span>, Long: </span>"
msgstr "<span>, Long: </span>"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span>Lat : </span>"
msgstr "<span>Lat : </span>"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_id
msgid "API"
msgstr "API"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid ""
"API key for GeoCoding (Places) required.\n"
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key for more information."
msgstr ""
"需要用於地理編碼（地點）的 API 金鑰。\n"
"有關詳細資訊，請訪問 https://developers.google.com/maps/documentation/geocoding/get-api-key"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "API:"
msgstr "API:"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_uid
msgid "Created by"
msgstr "創立者"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_date
msgid "Created on"
msgstr "建立於"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid "Error with geolocation server:"
msgstr "地理定位伺服器錯誤："

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geocoder
msgid "Geo Coder"
msgstr "地理定位編碼器"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geo Location"
msgstr "地理位置"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geo_provider
msgid "Geo Provider"
msgstr "地理定位供應商"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocation"
msgstr "地理位置"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner__date_localization
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users__date_localization
msgid "Geolocation Date"
msgstr "地理定位日期"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid "Google Map API Key"
msgstr "Google Map API 金鑰"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__id
msgid "ID"
msgstr "ID"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "Key:"
msgstr "金鑰"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__name
msgid "Name"
msgstr "名稱"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Partner Assignment"
msgstr "合作夥伴分配"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid "Provider %s is not implemented for geolocation service."
msgstr "供應商%s 未用於地理定位服務"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__tech_name
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_techname
msgid "Tech Name"
msgstr "技術名稱"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid ""
"Unable to geolocate, received the error:\n"
"%s\n"
"\n"
"Google made this a paid feature.\n"
"You should first enable billing on your Google account.\n"
"Then, go to Developer Console, and enable the APIs:\n"
"Geocoding, Maps Static, Maps Javascript.\n"
msgstr ""
"無法進行地理定位，收到錯誤：\n"
"%s\n"
"\n"
"Google將這個功能作為付費功能。\n"
"您應首先在您的 Google 帳戶上啟用計費。\n"
"然後，轉到開發人員主控台，並啟用 API：\n"
"地理編碼，靜態地圖，地圖JAVA腳本。\n"

#. module: base_geolocalize
#: model:ir.model.fields,help:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid ""
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key"
" for more information."
msgstr ""
"有關詳細資訊，請訪問 https://developers.google.com/maps/documentation/geocoding/get-"
"api-key"
