# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/components/activity/activity.xml:0
#, python-format
msgid "!activity.calendar_event_id"
msgstr "!activity.calendar_event_id"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_count
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_count
msgid "# Meetings"
msgstr "عدد الاجتماعات"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"%(date_start)s at %(time_start)s To\n"
" %(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""
"%(date_start)s في %(time_start)s إلى\n"
" %(date_end)s إلى %(time_end)s (%(timezone)s)"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "%(day)s at (%(start)s To %(end)s) (%(timezone)s)"
msgstr "%(day)s في (%(start)s إلى %(end)s) (%(timezone)s) "

#. module: calendar
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has accepted invitation"
msgstr "لقد قَبِل %s الدعوة "

#. module: calendar
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has declined invitation"
msgstr "لقد رفض %s الدعوة "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_changedate
msgid ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Ready Mat</t>,<br/><br/>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                The date of your appointment with <t t-out=\"customer.name or ''\">Jesse Brown</t> has been updated.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment has been updated.\n"
"            </t>\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            The date of your appointment with <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> has been updated.\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\"/> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            The date of the meeting has been updated.\n"
"            The meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> created by <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>Meeting URL: <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        مرحباً <t t-out=\"object.common_name or ''\">ريدي مات</t>،<br/><br/>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                تم تحديث تاريخ موعدك مع <t t-out=\"customer.name or ''\">جيس براون</t>.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                تم تحديث موعدك.\n"
"            </t>\n"
"            أصبح موعد <strong t-out=\"object.event_id.appointment_type_id.name or ''\">جدولة الجلسة التجريبية</strong> الآن في الساعة\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 (11:00:00 إلى 11:30:00) (أوروبا/براسيلز)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            تم تحديث تاريخ موعدك مع <t t-out=\"object.event_id.user_id.partner_id.name or ''\">كولين دياز</t>.\n"
"            أصبح وقت الموعد <strong t-out=\"object.event_id.appointment_type_id.name or ''\"/> الآن في الساعة\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021  (11:00:00 إلى 11:30:00) (أوروبا/براسيلز)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            تم تحديث تاريخ الاجتماع.\n"
"            لقد تم تغيير تاريخ <strong t-out=\"object.event_id.name or ''\">المتابعة لمقترح المشروع</strong> الذي تم إنشاؤه من قِبَل <t t-out=\"object.event_id.user_id.partner_id.name or ''\">كولين دياز</t> ليصبح في\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021  (11:00:00 إلى 11:30:00) (أوروبا/براسيلز)</t>.\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            قَبول</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            رفض</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            عرض</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">الثلاثاء</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">مايو 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 صباحاً</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">أوروبا/براسيلز</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>تفاصيل الفعالية</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>الموقع: <t t-out=\"object.event_id.location or ''\">بروكسيلز</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">عرض الخريطة</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>الزمان: <t t-out=\"object.recurrence_id.name or ''\">كل 1 أسبوع، لثلاث 3 فعاليات</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>المدة: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0 ساعة 30 دقيقة</t></li>\n"
"                </t>\n"
"                <li>الحاضرين\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">ميتشل آدمن</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">أنت</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>رابط الاجتماع: <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>وصف الفعالية:\n"
"                    <t t-out=\"object.event_id.description\">اجتماع داخلي لمناقشة التسعيرات الجديدة للمنتجات والخدمات.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    شكراً لك،\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>ميتشل آدمن</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_update
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online = 'appointment_type_id' in object and object.appointment_type_id\"/>\n"
"    <t t-set=\"target_responsible = object.partner_id == object.partner_id\"/>\n"
"    <t t-set=\"target_customer = object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent = object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence'\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\">\n"
"                    <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \"/>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\"/>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\"/>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\"/>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"/>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"/>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>Details of the event</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>Description:\n"
"                            <t t-out=\"object.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>Meeting URL:\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\">\n"
"                                    <t t-out=\"object.videocall_location or ''\"/>\n"
"                                </a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>Location: <t t-out=\"object.location or ''\"/>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">View Map</a>)\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>When: <t t-out=\"object.recurrence_id.name or ''\"/></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>Duration:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\"/>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr/>\n"
"        <p placeholder=\"Enter your message here\"><br/></p>\n"
"\n"
"    </div>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online = 'appointment_type_id' in object and object.appointment_type_id\"/>\n"
"    <t t-set=\"target_responsible = object.partner_id == object.partner_id\"/>\n"
"    <t t-set=\"target_customer = object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent = object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence'\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\">\n"
"                    <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \"/>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\"/>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\"/>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\"/>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"/>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"/>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>تفاصيل الفعالية</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>الوصف:\n"
"                            <t t-out=\"object.description\">اجتماع داخلي لمناقشة التسعيرات الجديدة للمنتجات والخدمات.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>رابط URL للاجتماع:\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\">\n"
"                                    <t t-out=\"object.videocall_location or ''\"/>\n"
"                                </a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>الموقع: <t t-out=\"object.location or ''\"/>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">عرض الخريطة</a>)\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>الزمان: <t t-out=\"object.recurrence_id.name or ''\"/></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>المدة:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\"/>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr/>\n"
"        <p placeholder=\"Enter your message here\"><br/></p>\n"
"\n"
"    </div>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_invitation
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br/><br/>\n"
"\n"
"        <t t-if=\"is_online and target_customer\">\n"
"            Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> <t t-if=\"object.event_id.appointment_type_id.category != 'custom'\"> with <t t-out=\"object.event_id.user_id.name or ''\">Ready Mat</t></t> has been booked.\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                <t t-out=\"customer.name or ''\"/> scheduled the following appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> with you.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> has been booked.\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"not target_responsible\">\n"
"            <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> invited you for the <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> meeting.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Your meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> has been booked.\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"not is_online or object.state != 'accepted'\">\n"
"            <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accept</a>\n"
"            <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Decline</a>\n"
"        </t>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\"><t t-out=\"'Reschedule' if is_online and target_customer else 'View'\">View</t></a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"is_online\">\n"
"                    <li>Appointment Type: <t t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</t></li>\n"
"                </t>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>Meeting URL: <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        مرحباً <t t-out=\"object.common_name or ''\">Wood Corner</t>،<br/><br/>\n"
"\n"
"        <t t-if=\"is_online and target_customer\">\n"
"            لقد تم حجز موعدك <strong t-out=\"object.event_id.appointment_type_id.name or ''\">جدولة العرض التوضيحي</strong> <t t-if=\"object.event_id.appointment_type_id.category != 'custom'\"> مع <t t-out=\"object.event_id.user_id.name or ''\">ريدي مات</t></t>.\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                <t t-out=\"customer.name or ''\"/> قام بجدولة الموعد التالي <strong t-out=\"object.event_id.appointment_type_id.name or ''\">جدولة عرض توضيحي</strong> معك.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                لقد تم حجز موعدك <strong t-out=\"object.event_id.appointment_type_id.name or ''\">جدولة عرض توضيحي</strong>.\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"not target_responsible\">\n"
"            <t t-out=\"object.event_id.user_id.partner_id.name or ''\">تدعوك كولين دياز</t> إلى اجتماع <strong t-out=\"object.event_id.name or ''\">المتابعة بشأن مقترح المشروع</strong>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            لقد تم حجز موعدك <strong t-out=\"object.event_id.name or ''\">المتابعة بشأن مقترح المشروع</strong>.\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"not is_online or object.state != 'accepted'\">\n"
"            <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                قبول</a>\n"
"            <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                رفض</a>\n"
"        </t>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\"><t t-out=\"'Reschedule' if is_online and target_customer else 'View'\">عرض</t></a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">الثلاثاء</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">مايو 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 صباحاً</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">أوروبا/براسيلز</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>تفاصيل الفعالية</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"is_online\">\n"
"                    <li>نوع الموعد: <t t-out=\"object.event_id.appointment_type_id.name or ''\">جدولة عرض توضيحي</t></li>\n"
"                </t>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>الموقع: <t t-out=\"object.event_id.location or ''\">براسيلز</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">عرض الخريطة</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>الزمان: <t t-out=\"object.recurrence_id.name or ''\">كل أسبوع 1 لمدة 3 فعاليات</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>المدة: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0ساعات30</t></li>\n"
"                </t>\n"
"                <li>الحاضرون\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">ميتشل آدمن</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">أنت</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>رابط URL للاجتماع: <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>وصف الفعالية:\n"
"                    <t t-out=\"object.event_id.description\">اجتماع داخلي لمناقشة التسعيرات الجديدة للمنتجات والخدمات.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    شكراً لك،\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>ميتشل آدمن</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_reminder
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Gemini Furniture</t>,<br/><br/>\n"
"        This is a reminder for the below event :\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>Meeting URL: <a t-attf-href=\"{{ object.event_id.videocall_location }}\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <p>\n"
"        مرحباً <t t-out=\"object.common_name or ''\">Gemini Furniture</t>،<br/><br/>\n"
"        هذا تذكير للفعالية المذكورة أدناه:\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            قَبول</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            رفض</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            عرض</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">الثلاثاء</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">مايو 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 صباحاً</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">أوروبا/براسيلز</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>تفاصيل الفعالية</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>الموقع: <t t-out=\"object.event_id.location or ''\">بروكسيلز</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">عرض الخريطة</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>الزمان: <t t-out=\"object.recurrence_id.name or ''\">كل 1 أسبوع، لمدة 3 أسابيع</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>المدة: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0 ساعات 30 دقيقة</t></li>\n"
"                </t>\n"
"                <li>الحاضرين\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">ميتشل آدمن</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">أنت</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>رابط الاجتماع: <a t-attf-href=\"{{ object.event_id.videocall_location }}\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>وصف الفعالية:\n"
"                    <t t-out=\"object.event_id.description\">اجتماع داخلي لمناقشة التسعيرات الجديدة للمنتجات والخدمات.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    شكراً لك،\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>ميتشل آدمن</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"Meetings\" role=\"img\" "
"title=\"Meetings\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"Meetings\" role=\"img\" "
"title=\"الاجتماعات \"/>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> hours</span>"
msgstr "<span> ساعات</span>"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_filters_user_id_partner_id_unique
msgid "A user cannot have the same contact twice."
msgstr "لا يمكن أن يكون للمستخدم نفس جهة الاتصال مرتين. "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Accept"
msgstr "قبول"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__accepted
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__accepted
msgid "Accepted"
msgstr "مقبول"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__category
msgid "Action"
msgstr "إجراء"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction
msgid "Action Needed"
msgstr "يتطلب اتخاذ إجراء "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"قد تؤدي الإجراءات إلى سلوك معين مثل فتح طريقة عرض التقويم أو وضع علامة "
"\"تم\" عليها تلقائياً عند رفع مستند "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__active
msgid "Active"
msgstr "نشط"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity
msgid "Activity"
msgstr "النشاط"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "مجموعة مخصصات الأنشطة "

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_type
msgid "Activity Type"
msgstr "نوع النشاط"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#, python-format
msgid "Add"
msgstr "إضافة"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__body
msgid "Additional Message"
msgstr "رسالة إضافية "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__body
msgid ""
"Additional message that would be sent with the notification for the reminder"
msgstr "رسالة إضافية سوف يتم إرسالها مع الإشعار للتذكير "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_event__allday
#, python-format
msgid "All Day"
msgstr "طوال اليوم"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "All Day, %(day)s"
msgstr "طوال اليوم، %(day)s "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__all_events
#, python-format
msgid "All events"
msgstr "كافة الفعاليات "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Archived"
msgstr "مؤرشف"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__partner_id
msgid "Attendee"
msgstr "الحاضر"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_status
msgid "Attendee Status"
msgstr "حالة الحاضر"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Attendees"
msgstr "الحاضرين "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Availability"
msgstr "التوافر"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__free
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__free
msgid "Available"
msgstr "متوفر"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__availability
msgid "Available/Busy"
msgstr "متاح/مشغول "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__base_event_id
msgid "Base Event"
msgstr "الفعالية الأساسية "

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__busy
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__busy
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
#, python-format
msgid "Busy"
msgstr "مشغول"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__byday
msgid "By day"
msgstr "باليوم"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__byday
msgid "Byday"
msgstr "Byday"

#. module: calendar
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
#: model:ir.ui.menu,name:calendar.menu_calendar_configuration
#: model_terms:ir.ui.view,arch_db:calendar.res_users_view_form
msgid "Calendar"
msgstr "التقويم"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_alarm
#: model:ir.ui.menu,name:calendar.menu_calendar_alarm
#: model_terms:ir.ui.view,arch_db:calendar.calendar_alarm_view_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_alarm_tree
msgid "Calendar Alarm"
msgstr "تنبيه التقويم"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "معلومات حاضري التقويم "

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__calendar_event_ids
msgid "Calendar Event"
msgstr "فعالية التقويم "

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_filters
msgid "Calendar Filters"
msgstr "عوامل تصفية التقويم "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Calendar Invitation"
msgstr "دعوة التقويم "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity__calendar_event_id
msgid "Calendar Meeting"
msgstr "اجتماع التقويم"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_changedate
msgid "Calendar: Date updated"
msgstr "التقويم: تم تحديث التاريج "

#. module: calendar
#: model:ir.actions.server,name:calendar.ir_cron_scheduler_alarm_ir_actions_server
#: model:ir.cron,cron_name:calendar.ir_cron_scheduler_alarm
#: model:ir.cron,name:calendar.ir_cron_scheduler_alarm
msgid "Calendar: Event Reminder"
msgstr "التقويم: التذكير بالفعالية"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_update
msgid "Calendar: Event Update"
msgstr "التقويم: تحديث الفعالية "

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_invitation
msgid "Calendar: Meeting Invitation"
msgstr "التقويم: دعوة الاجتماع "

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_reminder
msgid "Calendar: Reminder"
msgstr "التقويم: تذكير "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__is_organizer_alone
msgid ""
"Check if the organizer is alone in the event, i.e. if the organizer is the only one that hasn't declined\n"
"        the event (only if the organizer is not the only attendee)"
msgstr ""
"تحقق إذا ما كان المنظم وحده في الفعالية. على سبيل المثال، إذا كان المنظم الوحيد الذي لم يقم برفض\n"
"        الفعالية (فقط إن لم يكن المنظم الحاضر الوحيد) "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_checked
msgid "Checked"
msgstr "تم تحديده "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrence_update
msgid ""
"Choose what to do with other events in the recurrence. Updating All Events "
"is not allowed when dates or time is modified"
msgstr ""
"اختر ما تود فعله بشأن الفعاليات الأخرى عند تكرارها. لا يُسمح بتحديث كافة "
"الفعاليات عندما تكون التواريخ أو الأوقات معدلة "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__color
msgid "Color"
msgstr "اللون"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__common_name
msgid "Common name"
msgstr "الاسم الشائع"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_menu_config
msgid "Configuration"
msgstr "التهيئة "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/calendar_controller.js:0
#, python-format
msgid "Confirm"
msgstr "تأكيد"

#. module: calendar
#: model:ir.model,name:calendar.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Contact Attendees"
msgstr "التواصل مع الحاضرين "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__count
msgid "Count"
msgstr "العدد"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Date"
msgstr "التاريخ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__date
msgid "Date of month"
msgstr "اليوم من الشهر"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__day
msgid "Day"
msgstr "اليوم"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Day of Month"
msgstr "اليوم من الشهر"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__day
msgid "Day of month"
msgstr "اليوم من الشهر"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__days
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__daily
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__daily
msgid "Days"
msgstr "الأيام"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Decline"
msgstr "رفض"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__declined
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__declined
msgid "Declined"
msgstr "مرفوض"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__description
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Description"
msgstr "الوصف"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "Details"
msgstr "التفاصيل"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_description
msgid "Display Description"
msgstr "عرض الوصف "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Document"
msgstr "المستند"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_id
msgid "Document ID"
msgstr "معرف المستند"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_id
msgid "Document Model"
msgstr "نموذج المستند"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model
msgid "Document Model Name"
msgstr "اسم نموذج المستند "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__dtstart
msgid "Dtstart"
msgstr "Dtstart"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__duration
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Duration"
msgstr "المدة"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration_minutes
#: model:ir.model.fields,help:calendar.field_calendar_alarm__duration_minutes
msgid "Duration in minutes"
msgstr "المدة بالدقائق"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "EMAIL"
msgstr "البريد الإلكتروني "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/calendar_controller.js:0
#, python-format
msgid "Edit Recurrent event"
msgstr "تحرير الفعالية المتكررة "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Edit recurring event"
msgstr "تحرير الفعالية المتكررة "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__email
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__email
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_1
msgid "Email - 3 Hours"
msgstr "البريد الإلكتروني - 3 ساعات "

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_2
msgid "Email - 6 Hours"
msgstr "البريد الإلكتروني - 6 ساعات "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__mail_template_id
msgid "Email Template"
msgstr "قالب البريد الإلكتروني"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__email
msgid "Email of Invited Person"
msgstr "البريد الإلكتروني للمدعو"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_id
msgid "Employee"
msgstr "الموظف"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "End Date"
msgstr "تاريخ النهاية "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__end_type
msgid "End Type"
msgstr "نوع النهاية "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__end_date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__end_date
msgid "End date"
msgstr "تاريخ النهاية"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Ending At"
msgstr "ينتهي في "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Ending at"
msgstr "تنتهي في"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm
msgid "Event Alarm"
msgstr "تنبيه الفعالية"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "إدارة تنبيه الفعالية"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event_type
msgid "Event Meeting Type"
msgstr "نوع اجتماع الفعالية"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "قاعدة تكرار الفعالية "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_time
msgid "Event Time"
msgstr "وقت الفعالية "

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(count)s %(period)s"
msgstr "كل %(count)s %(period)s "

#. module: calendar
#: code:addons/calendar/models/mail_activity.py:0
#, python-format
msgid "Feedback: %(feedback)s"
msgstr "ملاحظات: %(feedback)s "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__1
msgid "First"
msgstr "الأول"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "First you have to specify the date of the invitation."
msgstr "عليك تحديد تاريخ الدعوة أولًا."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__follow_recurrence
msgid "Follow Recurrence"
msgstr "متابعة التكرار "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_follower_ids
msgid "Followers"
msgstr "المتابعين "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__forever
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__forever
msgid "Forever"
msgstr "للأبد"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__4
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__4
msgid "Fourth"
msgstr "رابعًا"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Free"
msgstr "مجاني"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__fri
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__fri
msgid "Fri"
msgstr "الجمعة"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__fri
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__fri
msgid "Friday"
msgstr "الجمعة"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Grouping by %s is not allowed on private events."
msgstr "لا يُسمح بالتجميع حسب %s في الفعاليات الخاصة. "

#. module: calendar
#: model:ir.model,name:calendar.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__hours
msgid "Hours"
msgstr "الساعات"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__id
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__id
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__id
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__id
msgid "ID"
msgstr "المُعرف"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"إذا تم تحويل قيمة الحقل نشط إلى خطأ، يمكنك إخفاء معلومات التنبيه للفعالية "
"دون إزالتها."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__show_as
msgid ""
"If the time is shown as 'busy', this event will be visible to other people with either the full         information or simply 'busy' written depending on its privacy. Use this option to let other people know         that you are unavailable during that period of time. \n"
" If the event is shown as 'free', other users know         that you are available during that period of time."
msgstr ""
"إذا تم إظهار الوقت كـ \"مشغول\"، سوف تكون هذه الفعالية مرئية للآخرين، إما مع كافة المعلومات أو أن تكون كلمة \"مشغول\" فقط مكتوبة بناءً على الخصوصية. استخدم هذا الخيار لإخطار الآخرين بأنك غير متوفر خلال تلك الفترة الزمنية. \n"
"إذا ظهر الوقت كـ \"متاح\" لن تكون هذه الفعالية مرئية للآخرين على الإطلاق. إذا ظهرت الفعالية كـ \"متاحة\"، سيعلم الآخرون بأنك متوفر خلال تلك الفترة الزمنية. "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__interval
msgid "Interval"
msgstr "الفترة"

#. module: calendar
#: model:mail.message.subtype,name:calendar.subtype_invitation
msgid "Invitation"
msgstr "الدعوة"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__access_token
msgid "Invitation Token"
msgstr "رمز الدعوة"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitation details"
msgstr "تفاصيل الدعوة"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Invitation for"
msgstr "الدعوة إلى "

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_invitation
msgid "Invitation to {{ object.event_id.name }}"
msgstr "دعوة إلى {{ object.event_id.name }} "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitations"
msgstr "الدعوات"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_highlighted
msgid "Is the Event Highlighted"
msgstr "هل الفعالية مبرَزة "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_organizer_alone
msgid "Is the Organizer Alone"
msgstr "هل المنظم بمفرده "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__-1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__-1
msgid "Last"
msgstr "الأخير"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_filters____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__calendar_last_notif_ack
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr "تم تحديد آخر رسالة كمقروءة من التقويم الأساسي"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type
msgid "Let the event automatically repeat at that interval"
msgstr "تكرار الفعالية تلقائياً عند مرور هذه الفترة "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__location
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Location"
msgstr "الموقع "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__location
msgid "Location of Event"
msgstr "موقع الفعالية "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Logo"
msgstr "الشعار"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__mail_tz
msgid "Mail Tz"
msgstr "المنطقة الزمنية للبريد "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__user_id
msgid "Me"
msgstr "أنا"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__mail_activity_type__category__meeting
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Meeting"
msgstr "الاجتماع"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"Meeting '%(name)s' starts '%(start_datetime)s' and ends '%(end_datetime)s'"
msgstr ""
"الاجتماع '%(name)s' يبدأ في '%(start_datetime)s' وينتهي '%(end_datetime)s' "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Meeting Details"
msgstr "تفاصيل الاجتماع"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__name
msgid "Meeting Subject"
msgstr "موضوع الاجتماع"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event_type
#: model:ir.ui.menu,name:calendar.menu_calendar_event_type
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_type_tree
msgid "Meeting Types"
msgstr "أنواع الاجتماعات"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_location
msgid "Meeting URL"
msgstr "رابط الاجتماع "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__event_id
msgid "Meeting linked"
msgstr "الاجتماع المرتبط "

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event
#: model:ir.actions.act_window,name:calendar.action_calendar_event_notify
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_ids
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_ids
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
#: model_terms:ir.ui.view,arch_db:calendar.view_partners_form
msgid "Meetings"
msgstr "الاجتماعات"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__minutes
msgid "Minutes"
msgstr "الدقائق"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__mon
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__mon
msgid "Mon"
msgstr "الاثنين"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__mon
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__mon
msgid "Monday"
msgstr "الاثنين"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__month_by
msgid "Month By"
msgstr "الشهر "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__monthly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__monthly
msgid "Months"
msgstr "شهور"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "My Meetings"
msgstr "اجتماعاتي"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__name
msgid "Name"
msgstr "الاسم"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__needsaction
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__needsaction
msgid "Needs Action"
msgstr "يتطلب اتخاذ إجراء "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_mixin__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_partner__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_users__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No I'm not going."
msgstr "لا، لن أحضر. "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No feedback yet"
msgstr "لا توجد ملاحظات بعد "

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid "No meetings found. Let's schedule one!"
msgstr "لم يتم إيجاد أي اجتماعات. فلنقم بجدولة واحد! "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__notification
msgid "Notification"
msgstr "الإشعارات "

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_5
msgid "Notification - 1 Days"
msgstr "إشعار - 1 يوم "

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_3
msgid "Notification - 1 Hours"
msgstr "إشعار - 1 ساعة "

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_1
msgid "Notification - 15 Minutes"
msgstr "إشعار - 15 دقيقة "

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_4
msgid "Notification - 2 Hours"
msgstr "إشعار - 2 ساعتان "

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_2
msgid "Notification - 30 Minutes"
msgstr "إشعار - 30 دقيقة "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__alarm_ids
msgid "Notifications sent to all attendees to remind of the meeting."
msgstr "تم إرسال إشعارات لكافة الحاضرين لتذكيرهم بالاجتماع. "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__count
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__count
msgid "Number of repetitions"
msgstr "مرات التكرار"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "OK"
msgstr "موافق"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Only Internal Users"
msgstr "فقط المستخدمين الداخليين "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__confidential
msgid "Only internal users"
msgstr "المستخدمين الداخليين فقط"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_view_form_popup
msgid "Open Calendar"
msgstr "فتح التقويم"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__month_by
msgid "Option"
msgstr "الخيار"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Options"
msgstr "خيارات"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
msgid "Organizer"
msgstr "المنظِّم"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_ids
msgid "Participant"
msgstr "المشارك"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__partner_id
msgid "Partner-related data of the user"
msgstr "بيانات المستخدم المرتبطة بالشريك"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__privacy
msgid "People to whom this event will be visible."
msgstr "الأشخاص الذين سوف تكون هذه الفعالية مرئية بالنسبة لهم. "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__phone
msgid "Phone"
msgstr "رقم الهاتف"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__phone
msgid "Phone number of Invited Person"
msgstr "رقم هاتف الشخص المدعو "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__privacy
msgid "Privacy"
msgstr "الخصوصية"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__private
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Private"
msgstr "خاص"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Private Event Excluded"
msgstr "الفعالية الخاصة مستثناة "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__public
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Public"
msgstr "عام"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type
msgid "Recurrence"
msgstr "التكرار "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__recurrence_id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_id
msgid "Recurrence Rule"
msgstr "قاعدة التكرار "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__end_type
msgid "Recurrence Termination"
msgstr "إنهاء التكرار "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_update
msgid "Recurrence Update"
msgstr "تحديث التكرار "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrency
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Recurrent"
msgstr "التكرار"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule
msgid "Recurrent Rule"
msgstr "قاعدة التكرار"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration
msgid "Remind Before"
msgstr "تذكير قبل"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__alarm_ids
#: model:ir.ui.menu,name:calendar.calendar_submenu_reminders
msgid "Reminders"
msgstr "التذكيرات"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__count
msgid "Repeat"
msgstr "تكرار"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__interval
msgid "Repeat Every"
msgstr "التكرار كل "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__until
msgid "Repeat Until"
msgstr "التكرار حتى "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__interval
msgid "Repeat every (Days/Week/Month/Year)"
msgstr "تكرر كل (يوم/أسبوع/شهر/سنة)"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__count
msgid "Repeat x times"
msgstr "تكرار × مرات "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/components/activity/activity.xml:0
#, python-format
msgid "Reschedule"
msgstr "إعادة جدولة "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Responsible"
msgstr "المسؤول "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule
msgid "Rrule"
msgstr " قاعدة التكرار "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule_type
msgid "Rrule Type"
msgstr "نوع قاعدة التكرار "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sat
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sat
msgid "Sat"
msgstr "السبت"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sat
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sat
msgid "Saturday"
msgstr "السبت"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_id
msgid "Scheduled by"
msgstr "تمت الجدولة من قِبَل "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Search Meetings"
msgstr "بحث عن الاجتماعات"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__2
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__2
msgid "Second"
msgstr "الثانية"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Select attendees..."
msgstr "اختر الحاضرين.. "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Email to attendees"
msgstr "إرسال بريد إلكتروني للحاضرين "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Invitations"
msgstr "إرسال دعوات "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Send Mail"
msgstr "إرسال بريد إلكتروني "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__show_as
msgid "Show as"
msgstr "الإظهار كـ "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "Snooze"
msgstr "غفوة"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start
msgid "Start"
msgstr "بدء"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__start
msgid "Start date of an event, without time for full days events"
msgstr ""
"تاريخ بداية الفعالية، دون ساعة معينة للفعاليات التي تستغرق أياماً كاملة "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Starting at"
msgstr "ابتداء من"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__state
msgid "Status"
msgstr "الحالة"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__state
msgid "Status of the attendee's participation"
msgstr "حالة مشاركة الحاضرين "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Status:"
msgstr "الحالة: "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop
msgid "Stop"
msgstr "إيقاف"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__stop
msgid "Stop date of an event, without time for full days events"
msgstr ""
"تاريخ انتهاء الفعالية، دون ساعة معينة للفعاليات التي تستغرق أياماً كاملة "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Subject"
msgstr "الموضوع "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sun
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sun
msgid "Sun"
msgstr "الأحد"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sun
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sun
msgid "Sunday"
msgstr "الأحد"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_event_type_name_uniq
msgid "Tag name already exists !"
msgstr "اسم علامة التصنيف موجود بالفعل! "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__categ_ids
msgid "Tags"
msgstr "علامات التصنيف "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__mail_template_id
msgid "Template used to render mail reminder content."
msgstr "قالب مُستخدَم لتكوين محتوى رسائل البريد الإلكتروني التذكيرية. "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Tentative"
msgstr "مبدأي "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "The"
msgstr " "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/components/activity/activity.js:0
#, python-format
msgid ""
"The activity is linked to a meeting. Deleting it will remove the meeting as "
"well. Do you want to proceed ?"
msgstr ""
"هذا النشاط مرتبط باجتماع. سيؤدي حذفه لحذف الاجتماع أيضًا. هل ترغب في "
"الاستمرار؟"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid ""
"The calendar is shared between employees and fully integrated with\n"
"            other applications such as the employee leaves or the business\n"
"            opportunities."
msgstr ""
"يشترك الموظفون في استخدام التقويم وهو مترابط تمامًا\n"
"            مع التطبيقات الأخرى مثل إجازات الموظفين\n"
"            وفرص العمل."

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_recurrence_month_day
msgid "The day must be between 1 and 31"
msgstr "يجب أن يكون اليوم ما بين 1 و 31 "

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"The ending date and time cannot be earlier than the starting date and time."
msgstr "لا يمكن أن يكون تاريخ ووقت الإنتهاء سابقين لتاريخ ووقت البدء."

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "The ending date cannot be earlier than the starting date."
msgstr "لا يمكن أن يكون تاريخ الإنتهاء سابقًا لتاريخ البدء."

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The interval cannot be negative."
msgstr "لا يمكن أن تكون الفترة سلبية. "

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The number of repetitions cannot be negative."
msgstr "لا يمكن أن يكون عدد التكرارات سلبياً. "

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "There are no attendees on these events"
msgstr "لا يوجد أي حاضرين في هذه الفعاليات "

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__3
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__3
msgid "Third"
msgstr "ثالثًا"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__future_events
#, python-format
msgid "This and following events"
msgstr "هذه الفعالية والفعاليات التالية "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__self_only
#, python-format
msgid "This event"
msgstr "هذه الفعالية "

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_filters__partner_checked
msgid ""
"This field is used to know if the partner is checked in the filter of the "
"calendar view for the user_id."
msgstr ""
"هذا الحقل مُستخدَم لمعرفة إذا ما كان الشريك محدداً في عامل تصفية عرض "
"التقويم، لـ user_id. "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__thu
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__thu
msgid "Thu"
msgstr "الخميس"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__thu
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__thu
msgid "Thursday"
msgstr "الخميس"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__event_tz
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__event_tz
msgid "Timezone"
msgstr "المنطقة الزمنية"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__mail_tz
msgid "Timezone used for displaying time in the mail template"
msgstr "منطقة زمنية مُستخدَمة لعرض الوقت في قالب البريد الإلكتروني "

#. module: calendar
#: code:addons/calendar/models/res_users.py:0
#, python-format
msgid "Today's Meetings"
msgstr "اجتماعات اليوم"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tue
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__tue
msgid "Tue"
msgstr "الثلاثاء"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__tue
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__tue
msgid "Tuesday"
msgstr "الثلاثاء"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "النوع"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__tentative
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__tentative
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Uncertain"
msgstr "غير واثق"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__interval
msgid "Unit"
msgstr "الوحدة"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل غير المقروءة "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__until
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Until"
msgstr "حتى"

#. module: calendar
#: model:ir.model,name:calendar.model_res_users
msgid "Users"
msgstr "المستخدمين "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__wed
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__wed
msgid "Wed"
msgstr "الأربعاء"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__wed
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__wed
msgid "Wednesday"
msgstr "الأربعاء"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__weekday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__weekday
msgid "Weekday"
msgstr "يوم العمل"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__weekly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__weekly
msgid "Weeks"
msgstr "أسابيع"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__yearly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__yearly
msgid "Years"
msgstr "سنوات"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Yes I'm going."
msgstr "أجل، سوف أحضر. "

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "You can't update a recurrence without base event."
msgstr "لا يمكنك تحديث تكرار الفعالية دون الفعالية الأساسية. "

#. module: calendar
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "You cannot duplicate a calendar attendee."
msgstr "لا يمكنك استنساخ أحد الحضور في التقويم."

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "You have to choose at least one day in the week"
msgstr "عليك اختيار يوم واحد على الأقل من أيام الأسبوع "

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "day %s"
msgstr "اليوم %s "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g. Business Lunch"
msgstr "مثال: غداء عمل"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "for %s events"
msgstr "لـ %s فعاليات "

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "on %s"
msgstr "في %s "

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "on the %(position)s %(weekday)s"
msgstr "في %(position)s %(weekday)s "

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "until %s"
msgstr "حتى %s "

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_reminder
msgid "{{ object.event_id.name }} - Reminder"
msgstr "{{ object.event_id.name }} - تذكير "

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_changedate
msgid "{{ object.event_id.name }}: Date updated"
msgstr "{{ object.event_id.name }}: تم تحديث التاريخ "

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_update
msgid "{{object.name}}: Event update"
msgstr "{{object.name}}: تحديث الفعالية "
