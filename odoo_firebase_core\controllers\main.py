from odoo import http, api, SUPERUSER_ID
from odoo.http import request, Response
from odoo.exceptions import AccessError, ValidationError


class MainController(http.Controller):
    @http.route('/odoo-firebase-core/execute', methods=['GET'], auth='public', type='http', cors='*')
    def odoo_firebase_core_execute(self):
        http.request.env['firebase.account'].sudo().cron_import_data()
        return "ok"

    @http.route('/odoo-firebase-core/odoo-import', methods=['POST'], auth='public', type='json')
    def odoo_firebase_core_odoo_import(self, access_token, account_id, res_model, res_method, res_params={},
                                       res_id=None,
                                       res_ids=None,
                                       res_lang=None,
                                       user_id=SUPERUSER_ID
                                       ):
        account = request.env['firebase.account'].sudo().browse(account_id)
        if not account:
            raise ValidationError("Not valid account")
        if account.token != access_token:
            raise AccessError("Permission not allowed")
        context = {}
        args = []
        if res_ids:
            args.append(res_ids)
        elif res_id:
            args.append([res_id])
        if res_lang:
            search_lang = request.env['res.lang'].sudo().search([
                ('iso_code', 'ilike', res_lang),
                ('active', '=', True),
            ])
            if search_lang:
                context['lang'] = search_lang.code
        params = account._transform_doc_data(res_params)
        params['context'] = context
        result = api.call_kw(request.env[res_model].with_user(user_id).with_context(context), res_method, args, params)
        Response.status = '200'
        return result
