# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Duy B<PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: Duy BQ <<EMAIL>>, 2022\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "\"%s\" added to dashboard"
msgstr "\"%s\" đã thêm vào dashboard"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"\"Add to\n"
"                Dashboard\""
msgstr ""
"\"Thêm vào\n"
"                     bảng thông tin\""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "'%s' added to dashboard"
msgstr "'%s' được thêm vào bảng thông tin"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add"
msgstr "Thêm"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my Dashboard"
msgstr "Thêm vào Bảng thông tin của tôi"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my dashboard"
msgstr "Thêm vào Bảng thông tin của tôi"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Are you sure you want to remove this item?"
msgstr "Bạn có chắc mình muốn xóa mục này?"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr "Bảng thông tin"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout"
msgstr "Thay đổi bố cục"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout.."
msgstr "Thay đổi bố cục..."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Choose dashboard layout"
msgstr "Chọn bố cục bảng thông tin"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Could not add filter to dashboard"
msgstr "Không thể thêm bộ lọc vào bảng thông tin"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Edit Layout"
msgstr "Sửa bố cục"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Layout"
msgstr "Bố cục"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
#, python-format
msgid "My Dashboard"
msgstr "Bảng thông tin của Tôi"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr "Vui lòng tải lại trình duyệt của bạn để thấy được những sự thay đổi."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                menu, switch to list or graph view, and click"
msgstr ""
"Để thêm báo cáo đầu tiên của bạn vào bảng thông tin, hãy đến bất kỳ\n"
"menu nào, chuyển sang chế độ xem danh sách hoặc biểu đồ, và bấm"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                dashboard using the search options."
msgstr ""
"Bạn có thể lọc và nhóm dữ liệu trước khi thêm vào\n"
"                 bảng thông tin bằng cách sử dụng các tùy chọn tìm kiếm."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Your personal dashboard is empty"
msgstr "Bản thông tin cá nhân của bạn hiện đang trống"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "in the extended search options."
msgstr "trong các tùy chọn tìm kiếm mở rộng."
