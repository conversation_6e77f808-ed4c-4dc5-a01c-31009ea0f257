<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="payment.payment_acquirer_authorize" model="payment.acquirer">
        <field name="provider">authorize</field>
        <field name="inline_form_view_id" ref="inline_form"/>
        <field name="support_authorization">True</field>
        <field name="support_fees_computation">False</field>
        <field name="support_refund"></field>
        <field name="support_tokenization">True</field>
        <field name="allow_tokenization">True</field>
    </record>

    <record id="payment_method_authorize" model="account.payment.method">
        <field name="name">Authorize.Net</field>
        <field name="code">authorize</field>
        <field name="payment_type">inbound</field>
    </record>

</odoo>
