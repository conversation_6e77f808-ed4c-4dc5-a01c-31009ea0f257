<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="EditableTable" owl="1">
        <Draggable limitArea="'.floor-map'">
            <Resizeable limitArea="'.floor-map'">
                <div class="table selected" t-on-click.stop="">
                    <span class="label drag-handle">
                        <t t-esc="props.table.name" />
                    </span>
                    <span class="table-seats">
                        <t t-esc="props.table.seats" />
                    </span>
                    <t t-if="props.table.shape === 'round'">
                        <div class="table-handle top resize-handle-n"></div>
                        <div class="table-handle bottom resize-handle-s"></div>
                        <div class="table-handle left resize-handle-w"></div>
                        <div class="table-handle right resize-handle-e"></div>
                    </t>
                    <t t-if="props.table.shape === 'square'">
                        <span class='table-handle top right resize-handle-ne'></span>
                        <span class='table-handle top left resize-handle-nw'></span>
                        <span class='table-handle bottom right resize-handle-se'></span>
                        <span class='table-handle bottom left resize-handle-sw'></span>
                    </t>
                </div>
            </Resizeable>
        </Draggable>
    </t>

</templates>
