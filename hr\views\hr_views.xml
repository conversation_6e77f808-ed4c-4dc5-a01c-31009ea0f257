<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <menuitem
            id="menu_hr_root"
            name="Employees"
            groups="group_hr_manager,group_hr_user,base.group_user"
            web_icon="hr,static/description/icon.png"
            sequence="185"/>

        <menuitem
            id="menu_hr_main"
            name="Human Resources"
            parent="menu_hr_root"
            sequence="0"/>

        <menuitem
            id="menu_hr_employee_payroll"
            name="Employees"
            parent="menu_hr_root"
            groups="group_hr_user"
            sequence="3"/>

            <menuitem
                id="menu_hr_employee_user"
                name="Employees"
                action="open_view_employee_list_my"
                parent="menu_hr_employee_payroll"
                sequence="1"/>

        <menuitem
            id="menu_hr_employee"
            name="Directory"
            action="hr_employee_public_action"
            parent="menu_hr_root"
            sequence="4"/>

        <menuitem
            id="hr_menu_hr_reports"
            name="Reporting"
            parent="menu_hr_root"
            sequence="95"/>

        <menuitem
           id="menu_hr_reporting_timesheet"
           name="Reporting"
           parent="menu_hr_root"
           groups="group_hr_manager,group_hr_user"
           sequence="99"/>

        <menuitem
            id="menu_human_resources_configuration"
            name="Configuration"
            parent="menu_hr_root"
            groups="hr.group_hr_user"
            sequence="100"/>

            <menuitem
                id="menu_view_hr_job"
                action="action_hr_job"
                parent="menu_human_resources_configuration"
                sequence="1"/>

            <menuitem
                id="menu_human_resources_configuration_employee"
                name="Employee"
                parent="menu_human_resources_configuration"
                groups="base.group_no_one"
                sequence="1"/>

                <menuitem
                    id="menu_view_employee_category_form"
                    name="Tags"
                    action="open_view_categ_form"
                    parent="menu_human_resources_configuration_employee"
                    groups="base.group_no_one"
                    sequence="1"/>

            <menuitem
                id="menu_hr_department_tree"
                action="hr_department_tree_action"
                parent="menu_human_resources_configuration"
                sequence="2"
                groups="group_hr_user"/>

            <menuitem
                id="menu_hr_department_kanban"
                action="hr_department_kanban_action"
                parent="menu_hr_root"
                groups="group_hr_user"/>

            <menuitem
                id="menu_hr_work_location_tree"
                action="hr_work_location_action"
                parent="menu_human_resources_configuration"
                sequence="5"
                groups="group_hr_user"/>

            <menuitem
                id="menu_hr_departure_reason_tree"
                action="hr_departure_reason_action"
                parent="menu_human_resources_configuration"
                sequence="5"
                groups="group_hr_user"/>

            <menuitem
                id="menu_config_plan"
                name="Activity Planning"
                parent="menu_human_resources_configuration"
                groups="group_hr_manager"
                sequence="100"/>

                <menuitem
                    id="menu_config_plan_types"
                    name="Planning Types"
                    action="hr_plan_activity_type_action"
                    parent="menu_config_plan"
                    groups="base.group_no_one"
                    sequence="99"/>

                <menuitem
                    id="menu_config_plan_plan"
                    name="Plans"
                    action="hr_plan_action"
                    parent="menu_config_plan"
                    groups="group_hr_manager"
                    sequence="100"/>

    </data>
</odoo>
