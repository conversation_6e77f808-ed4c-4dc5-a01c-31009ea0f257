<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data>
        <template id="report_pos_hash_integrity">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="company">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <div class="row" id="hash_header">
                                <div class="col-12">
                                    <br/>
                                    <h2>Résultat du test d'intégrité - <span t-esc="data['printing_date']"/></h2>
                                    <br/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12" id="hash_config_review">
                                    <h6>
                                        Selon l’article 286 du code général des impôts français, toute livraison de bien ou prestation
                                        de services ne donnant pas lieu à facturation et étant enregistrée au moyen d’un logiciel ou
                                        d’un système de caisse doit satisfaire à des conditions d’inaltérabilité et de sécurisation des
                                        données en vue d’un contrôle de l’administration fiscale.
                                        <br/>
                                        <br/>
                                        Ces conditions sont respectées via une fonction de hachage des ventes du Point de Vente.
                                        <br/>
                                        <br/>
                                    </h6>
                                </div>
                            </div>
                            <t t-if="data['result'] != 'None'">
                                <div class="row">
                                    <div class="col-12" id="hash_data_consistency">
                                        <br/>
                                        <h3>Contrôle des données du point de vente</h3>
                                        <br/>
                                        <t t-if="data['result'] != 'None' and data['corrupted_orders'] == 'None'">
                                            <h5>
                                                Toutes les ventes effectuées via le Point de Vente
                                                sont bien dans la chaîne de hachage.
                                            </h5>
                                            <br/>
                                        </t>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12" id="hash_data_consistency_table">
                                        <table class="table table-bordered" style="table-layout: fixed">
                                            <thead style="display: table-row-group">
                                                <tr>
                                                    <th class="text-center" style="width: 25%" scope="col">First Hash</th>
                                                    <th class="text-center" style="width: 25%" scope="col">First Entry</th>
                                                    <th class="text-center" style="width: 25%" scope="col">Last Hash</th>
                                                    <th class="text-center" style="width: 25%" scope="col">Last Entry</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-if="data['result'] != 'None'">
                                                    <t t-if="data['result']['first_order_hash'] != 'None'">
                                                        <tr>
                                                            <td><span t-esc="data['result']['first_order_hash']"/></td>
                                                            <td>
                                                                <span t-esc="data['result']['first_order_name']"/> <br/>
                                                                <span t-esc="data['result']['first_order_date']"/>
                                                            </td>
                                                            <td><span t-esc="data['result']['last_order_hash']"/></td>
                                                            <td>
                                                                <span t-esc="data['result']['last_order_name']"/> <br/>
                                                                <span t-esc="data['result']['last_order_date']"/>
                                                            </td>
                                                        </tr>
                                                    </t>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div>
                                    <t t-if="data['corrupted_orders'] != 'None'">
                                        <h5>
                                            Données corrompues sur la commande du point de vente:
                                        </h5>
                                        <span t-esc="data['corrupted_orders']"/> <br/>
                                    </t>
                                </div>
                                <div class="row" id="hash_last_div">
                                    <div class="col-12" id="hash_chain_compliant">
                                        <br/>
                                        <h6>
                                            La chaîne de hachage est conforme: il n’est pas possible d’altérer les données
                                            sans casser la chaîne de hachage pour les pièces ultérieures.
                                        </h6>
                                        <br/>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>
