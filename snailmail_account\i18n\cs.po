# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail_account
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <jan.<PERSON><PERSON><PERSON><PERSON>@centrum.cz>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# ka<PERSON><PERSON><PERSON> <karolina.schustero<PERSON>@vdp.sk>, 2021
# <PERSON><PERSON><PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2021\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: snailmail_account
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#, python-format
msgid "%s of the selected invoice(s) had an invalid address and were not sent"
msgstr ""

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_invoice_send_inherit_account_wizard_form
msgid ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" title=\"Make sure you have enough Stamps on your account.\"/>\n"
"                                )"
msgstr ""

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_invoice_send_inherit_account_wizard_form
msgid ""
"<span class=\"text-danger\">\n"
"                                    Some customer addresses are incomplete.\n"
"                                </span>"
msgstr ""

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_invoice_send_inherit_account_wizard_form
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('invalid_addresses', '!=', 0)]}\"> to: </span>\n"
"                                <span class=\"text-danger\" attrs=\"{'invisible': [('invalid_addresses', '=', 0)]}\"> The customer's address is incomplete: </span>"
msgstr ""

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_account_invoice_send
msgid "Account Invoice Send"
msgstr "Zaslat fakturu na účet"

#. module: snailmail_account
#: model:ir.model.fields,help:snailmail_account.field_account_invoice_send__snailmail_is_letter
msgid ""
"Allows to send the document by Snailmail (conventional posting delivery "
"service)"
msgstr ""
"Umožňuje odeslat dokument prostřednictvím klasické pošty (běžná poštovní "
"doručovací služba)."

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_invoice_send_inherit_account_wizard_form
msgid "Contacts"
msgstr "Kontakty"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__id
msgid "ID"
msgstr "ID"

#. module: snailmail_account
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__invalid_partner_ids
#, python-format
msgid "Invalid Addresses"
msgstr "Neplatné adresy"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__invalid_addresses
msgid "Invalid Addresses Count"
msgstr "Neplatný počet adres"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__invalid_invoices
msgid "Invalid Invoices Count"
msgstr "Počet chybných faktur"

#. module: snailmail_account
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#, python-format
msgid "Invoice"
msgstr "Faktura"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__invoice_send_id
msgid "Invoice Send"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__model_name
msgid "Model Name"
msgstr "Název modelu"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__partner_id
msgid "Partner"
msgstr "Partner"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__snailmail_is_letter
#: model:ir.model.fields,field_description:snailmail_account.field_res_company__invoice_is_snailmail
#: model:ir.model.fields,field_description:snailmail_account.field_res_config_settings__invoice_is_snailmail
msgid "Send by Post"
msgstr "Odeslat klasickou poštou"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_snailmail_confirm_invoice
msgid "Snailmail Confirm Invoice"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__snailmail_cost
msgid "Stamp(s)"
msgstr "Známka(y)"

#. module: snailmail_account
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#, python-format
msgid "You cannot send an invoice which has no partner assigned."
msgstr "Nelze odeslat fakturu, která nemá přiřazeného partnera."
