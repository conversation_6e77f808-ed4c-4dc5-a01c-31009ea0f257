<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_production_type_kanban" model="ir.ui.view">
        <field name="name">stock.picking.type.kanban</field>
        <field name="model">stock.picking.type</field>
        <field name="inherit_id" ref="stock.stock_picking_type_kanban"/>
        <field name="arch" type="xml">
            <field name="code" position="after">
                <field name="count_mo_todo"/>
                <field name="count_mo_waiting"/>
                <field name="count_mo_late"/>
            </field>

            <xpath expr='//div[@name="stock_picking"]' position="after">
                <div t-if="record.code.raw_value == 'mrp_operation'" t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
                    <div>
                        <div t-attf-class="o_kanban_card_header">
                            <div class="o_kanban_card_header_title">
                                <div class="o_primary" t-if="!selection_mode">
                                    <a type="object" name="get_mrp_stock_picking_action_picking_type">
                                        <field name="name"/>
                                    </a>
                                </div>
                                <span class="o_primary" t-if="selection_mode"><field name="name"/></span>
                                <div class="o_secondary"><field class="o_secondary"  name="warehouse_id" readonly="1" groups="stock.group_stock_multi_warehouses"/></div>
                            </div>
                            <div class="o_kanban_manage_button_section" t-if="!selection_mode">
                                <a class="o_kanban_manage_toggle_button" href="#"><i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/></a>
                            </div>
                        </div>
                        <div class="container o_kanban_card_content" t-if="!selection_mode">
                            <div class="row">
                                <div class="col-6 o_kanban_primary_left">
                                    <button class="btn btn-primary" name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_todo': 1, 'default_picking_type_id': active_id}">
                                        <span t-if="record.code.raw_value =='mrp_operation'"><t t-esc="record.count_mo_todo.value"/> To Process</span>
                                    </button>
                                </div>
                                <div class="col-6 o_kanban_primary_right">
                                    <div t-if="record.count_mo_waiting.raw_value > 0" class="row">
                                        <div class="col-9">
                                            <a name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_waiting': 1}">
                                                Waiting
                                            </a>
                                        </div>
                                        <div class="col-3">
                                            <field name="count_mo_waiting"/>
                                        </div>
                                    </div>
                                    <div t-if="record.count_mo_late.raw_value > 0" class="row">
                                        <div class="col-9">
                                            <a class="oe_kanban_stock_picking_type_list" name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_planning_issues': 1, 'default_picking_type_id': active_id}">
                                                Late
                                            </a>
                                        </div>
                                        <div class="col-3">
                                            <field name="count_mo_late"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div><div class="container o_kanban_card_manage_pane dropdown-menu" role="menu">
                            <div class="row">
                                <div class="col-6 o_kanban_card_manage_section o_kanban_manage_view" name="picking_left_manage_pane">
                                    <div role="menuitem" class="o_kanban_card_manage_title">
                                        <span>Orders</span>
                                    </div>
                                    <div role="menuitem">
                                        <a name="%(mrp_production_action_picking_deshboard)d" type="action">All</a>
                                    </div>
                                    <div role="menuitem">
                                        <a name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_inprogress': 1}">In Progress</a>
                                    </div>
                                    <div role="menuitem">
                                        <a name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_planned': 1}">Planned</a>
                                    </div>
                                </div>
                                <div class="col-6 o_kanban_card_manage_section o_kanban_manage_new">
                                    <div role="menuitem" class="o_kanban_card_manage_title">
                                        <span>New</span>
                                    </div>
                                    <div role="menuitem">
                                        <a name="%(action_mrp_production_form)d" context="{'default_picking_type_id': active_id}" type="action">Manufacturing Order</a>
                                    </div>
                                </div>
                            </div>

                            <div t-if="widget.editable" class="o_kanban_card_manage_settings row">
                                <div role="menuitem" aria-haspopup="true" class="col-8">
                                    <ul role="menu" class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                                <div class="col-4">
                                    <a class="dropdown-item" role="menuitem" type="edit">Configuration</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="view_picking_form_inherit_mrp" model="ir.ui.view">
        <field name="name">view.picking.form.inherit.mrp</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='use_create_lots']" position="after">
                <field name="has_kits" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='description_picking']" position="after">
                <field name="description_bom_line" optional="show" attrs="{'column_invisible': [('parent.has_kits', '=', False)]}"/>
            </xpath>
        </field>
    </record>

    <record id="view_stock_move_line_detailed_operation_tree_mrp" model="ir.ui.view">
        <field name="name">stock.move.line.operations.tree.mrp</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="description_bom_line" optional="show" attrs="{'column_invisible': [('parent.has_kits', '=', False)]}"/>
            </xpath>
        </field>
    </record>

    <record id="view_picking_type_form_inherit_mrp" model="ir.ui.view">
        <field name="name">Operation Types</field>
        <field name="model">stock.picking.type</field>
        <field name="inherit_id" ref="stock.view_picking_type_form"/>
        <field name="arch" type="xml">
            <field name="show_operations" position="attributes">
                <attribute name="attrs">{"invisible": [("code", "=", "mrp_operation")]}</attribute>
            </field>
            <xpath expr="//group[@name='stock_picking_type_lot']" position="after">
                <group attrs='{"invisible": [("code", "!=", "mrp_operation")]}' string="Traceability" groups="stock.group_production_lot">
                    <field name="use_create_components_lots"/>
                </group>
            </xpath>
        </field>
    </record>
</odoo>
