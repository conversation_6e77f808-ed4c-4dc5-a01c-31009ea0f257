# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payulatam
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_payulatam
#: model:ir.model.fields.selection,name:payment_payulatam.selection__payment_acquirer__provider__payulatam
msgid "PayU Latam"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__payulatam_api_key
msgid "PayU Latam API Key"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__payulatam_account_id
msgid "PayU Latam Account ID"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__payulatam_merchant_id
msgid "PayU Latam Merchant ID"
msgstr ""

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment.py:0
#, python-format
msgid "PayU Latam: received data for reference %s; multiple orders found"
msgstr ""

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment.py:0
#, python-format
msgid "PayU Latam: received data for reference %s; no order found"
msgstr ""

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment.py:0
#, python-format
msgid ""
"PayU Latam: received data with missing reference (%s) or transaction id (%s)"
" or sign (%s)"
msgstr ""

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__provider
msgid "Provider"
msgstr ""
