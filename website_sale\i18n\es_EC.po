# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale
#
# Translators:
# <PERSON> <aju<PERSON><EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-02-22 05:10+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "(extra fees apply)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
#, fuzzy
msgid ", go to the 'Sales' tab"
msgstr ", vaya a la pestaña de 'Ventas'"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ", go to the 'Variants' tab"
msgstr ", y vaya a la pestaña 'Variantes'"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "-- Create a new address --"
msgstr "-- Crear nueva dirección --"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"30-day money-back guarantee<br/>\n"
"                    Free Shipping in U.S.<br/>\n"
"                    Buy now, get in 2 days"
msgstr ""
"Garantía de devolución del dinero 30 días<br/>\n"
"Envío gratuito en U.S.<br/>\n"
"Compre ahora, lo tendrá en 2 días."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<br/>\n"
"                                <small>(don't forget to apply the changes)</"
"small>"
msgstr ""
"<br/>\n"
"<small>(no olvide aplicar los cambios)</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<br/>\n"
"                                <small>(you'll have to subscribe directly on "
"each of the payment companies' websites)</small>"
msgstr ""
"<br/>\n"
"<small>(va a tener que suscribirse directamente en cada uno de los sitios "
"web de las compañías de pago)</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid ""
"<br/>\n"
"                                <strong>Payment Status:</strong>"
msgstr ""
"<br/>\n"
"<strong>Estado del Pago:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "<i class=\"fa fa-cog\"/> Configure Transfer Details"
msgstr "<i class=\"fa fa-cog\"/> Configurar los Detalles de la Transferencia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<i class=\"fa fa-comment-o\"/> Website Live Chat on"
msgstr "<i class=\"fa fa-comment-o\"/> Sitio Chat en vivo en"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<i class=\"fa fa-envelope-o\"/> Email Our Website Expert"
msgstr ""
"<i class=\"fa fa-envelope-o\"/> Envíe un email a nuestro experto del sitio "
"web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Imprimir"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header
msgid ""
"<i class=\"fa fa-shopping-cart\"/>\n"
"              My cart"
msgstr ""
"<i class=\"fa fa-shopping-cart\"/>\n"
"Mi Cesta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<span class=\"col-xs-6 text-right h4\">Total:</span>"
msgstr "<span class=\"col-xs-6 text-right h4\">Total:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid ""
"<span class=\"col-xs-6 text-right text-muted\" title=\"Taxes may be updated "
"after providing shipping address\"> Taxes:</span>"
msgstr ""
"<span class=\"col-xs-6 text-right text-muted\" title=\"Taxes may be updated "
"after providing shipping address\"> Impuestos:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<span class=\"col-xs-6 text-right text-muted\">Subtotal:</span>"
msgstr "<span class=\"col-xs-6 text-right text-muted\">Subtotal:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<span class=\"fa fa-arrow-right\"/> Change Address"
msgstr "<span class=\"fa fa-arrow-right\"/> Cambiar Dirección"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "<span class=\"fa fa-arrow-right\"/> change"
msgstr "<span class=\"fa fa-arrow-right\"/> cambiar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<span class=\"fa fa-comment-o\"/> Website Live Chat on"
msgstr "<span class=\"fa fa-comment-o\"/> Chat en Vivo en"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        At cost price is a good option for heavy or "
"oversized packages."
msgstr ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"A precio de costo es una buena opción para los paquetes pesados o de gran "
"tamaño."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        Offering free delivery with a minimum amount or "
"minimum number of items should drive up your average order value and help to "
"compensate for the delivery costs."
msgstr ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"Ofreciendo entrega gratuita con una compra mínima o un número mínimo de "
"elementos debería impulsar su promedio de pedidos y ayudar a compensar los "
"gastos de envío."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        You can also create different rates based on order "
"amount ranges, for example 10€ up to a 50€ order, then 5€ after."
msgstr ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"También puede crear diferentes tarifas basadas en los rangos de la cantidad "
"del pedido, por ejemplo 10€ para pedidos de hasta 50€, y 5€ de ahí en "
"adelante."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.continue_shopping
msgid ""
"<span class=\"fa fa-long-arrow-left\"/> <span class=\"hidden-xs\">Continue "
"Shopping</span><span class=\"visible-xs-inline\">Continue</span>"
msgstr ""
"<span class=\"fa fa-long-arrow-left\"/> <span class=\"hidden-xs\">Continuar "
"Comprando</span><span class=\"visible-xs-inline\">Continuar</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "<span class=\"fa fa-long-arrow-left\"/> Return to Cart"
msgstr "<span class=\"fa fa-long-arrow-left\"/> Volver a la cesta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"hidden-xs\">Process Checkout</span><span class=\"visible-xs-"
"inline\">Checkout</span> <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""
"<span class=\"hidden-xs\">Procesar Pago</span><span class=\"visible-xs-inline"
"\">Pagar</span> <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-shopping-cart\"/>\n"
"                            <strong>2. On Add to Cart window:</strong> Show "
"accessories, services\n"
"                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"<span class=\"fa fa-shopping-cart\"/>\n"
"<strong>2. En la pantalla de Añadir a la cesta:</strong> Mostrar accesorios, "
"servicios\n"
"</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-shopping-cart\"/>\n"
"                            <strong>3. On Check-out page:</strong> Show "
"optional products\n"
"                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"<span class=\"fa fa-shopping-cart\"/>\n"
"<strong>3. En la página de Pago:</strong> Mostrar productos opcionales\n"
"</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-tag\"/>\n"
"                            <strong> 1. On Product pages:</strong> Show "
"suggested products\n"
"                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"<span class=\"fa fa-tag\"/>\n"
"<strong> 1. En las páginas de Producto:</strong> Mostrar productos "
"sugeridos\n"
"</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-cc-paypal\"/><strong> "
"Paypal</strong> (Recommended for starters)</span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-cc-paypal\"/><strong> "
"Paypal</strong> (Recomendado para principiantes)</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-credit-card\"/><strong> "
"Ogone, Adyen, Authorize.net, Buckaroo...</strong></span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-credit-card\"/><strong> "
"Ogone, Adyen, Authorize.net, Buckaroo...</strong></span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-lock\"/><strong>Wire "
"transfer</strong> (Slow and inefficient)</span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-lock\"/"
"><strong>Transferencia bancaria</strong> (Lenta e ineficiente)</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-pencil-square-o\"/><strong> "
"Web-Services</strong><br/>scripts development</span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-pencil-square-o\"/> <strong> "
"Servicios Web</strong><br/>desarrollo de scripts</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-shopping-cart\"/><strong> At "
"cost price</strong> (customer pay what you pay)</span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-shopping-cart\"/><strong> A "
"precio de costo</strong> (el cliente paga lo que usted paga)</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-sitemap\"/><strong> "
"Importation</strong><br/>by using a CSV file</span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-sitemap\"/><strong> "
"Importación</strong><br/>usando un archivo CSV</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-smile-o\"/><strong> Free "
"delivery</strong> (risky, but has best potential)</span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-smile-o\"/><strong> Entrega "
"gratuita</strong> (arriesgada, pero tiene mejor potencial)</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-table\"/><strong> Flat "
"rates</strong> (everybody pays the same)</span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-table\"/><strong> Tarifa "
"plana</strong> (todos pagan lo mismo)</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"text-danger\">* </span>Field 2"
msgstr "<span class=\"text-danger\">* </span>Campo 2"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "<strong>Add to Cart</strong>"
msgstr "<strong>Añadir a la cesta</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Bonuses:</strong> what you get on top of the offer"
msgstr "<strong>Extras:</strong> lo que se adiciona a la oferta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Call to action</strong> short and clear: (Add to Cart, Ask for "
"quote,...)"
msgstr ""
"<strong>Llamado a la acción</strong> corto y claro: (Añádalo al Carro, Pida "
"una Cotización,...)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Cons:</strong>"
msgstr "<strong>Contras:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> The delivery cost may be discouraging for your "
"cheapest items."
msgstr ""
"<strong>Contras:</strong> El costo de entrega puede ser desalentador para "
"los productos más baratos."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> customers have to wait until checkout to find out the "
"delivery price."
msgstr ""
"<strong>Contras:</strong> los clientes tienen que esperar hasta la pantalla "
"de pago para ver el precio de entrega."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> will require you to either absorb the cost or "
"slightly increase your prices to cover it."
msgstr ""
"<strong>Contras:</strong> va a necesitar absorver el costo o incrementar un "
"poco sus precios para cubrirla."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Contact us now:</strong><br/>"
msgstr "<strong>Contáctenos ahora:</strong><br/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Customers review:</strong> what do the customers think of the product"
msgstr ""
"<strong>Opiniones de los clientes:</strong> lo que los clientes piensan del "
"producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Features and benefits:</strong> what the product does and why that "
"is good"
msgstr ""
"<strong>Características y beneficios:</strong> lo que hace el producto y por "
"qué es tan bueno"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>High-quality picture</strong>"
msgstr "<strong>Imagen de alta calidad</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Key features, emotional and commercial content</strong><br/>\n"
"                            Recommended for at least for your top products, "
"because it can have a big impact on your sales and conversion rates."
msgstr ""
"<strong>Características principales, contenido emocional y comercial</"
"strong><br/>\n"
"Recomendado por lo menos para sus principales productos, ya que puede tener "
"un gran impacto en sus ventas y las tasas de conversión."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Mandatory content</strong><br/>"
msgstr "<strong>Contenido obligatorio</strong><br/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Name</strong> of your product"
msgstr "<strong>Nombre</strong> de su producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Need help to import your products?</strong>"
msgstr "<strong>Necesita ayuda para importar sus productos?</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Order Details:</strong>"
msgstr "<strong>Detalles del Pedido:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "<strong>Payment Method:</strong>"
msgstr "<strong>Método de Pago:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Payment information:</strong>"
msgstr "<strong>Información del pago:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pictures gallery of the product:</strong> all angles, detailed view, "
"package,etc."
msgstr ""
"<strong>Galería de imágenes del producto:</strong> todos los ángulos, vista "
"detallada, paquete, etc."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Price</strong> with currency"
msgstr "<strong>Precio</strong> con divisa"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Product variants</strong><br/>\n"
"                            Product variants are used to offer variations of "
"the same product to your customers on the products page.<br/>\n"
"                            For example, the customer choose a T-shirt and "
"then select its size or color."
msgstr ""
"<strong>Variantes del producto</strong><br/>\n"
"Las variantes del producto son usadas para ofrecer a sus clientes "
"variaciones del mismo producto en la página del producto.<br/>\n"
"Por ejemplo, el cliente escoje una camiseta y luego selecciona su tamaño y "
"color."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Pros:</strong>"
msgstr "<strong>Pros:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pros:</strong> being transparent about your charges can win you the "
"trust of your customers."
msgstr ""
"<strong>Pros:</strong> siendo transparente acerca de sus cargas puede ganar "
"la confianza de sus clientes."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pros:</strong> gives you a significant advantage over any "
"competitors that don't offer the same perk."
msgstr ""
"<strong>Pros:</strong> le da una ventaja significativa sobre cualquier "
"competidor que no ofrece el mismo beneficio."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Pros:</strong> simple for your customers to understand."
msgstr "<strong>Pros:</strong> simple de entender para sus clientes."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Reassurance arguments</strong><br/>\n"
"                            Anticipate your customers questions &amp; "
"worries on practical details like Shipping rates &amp; policies, Return "
"&amp; replacement policies, Payment methods &amp; security, General "
"Conditions, etc."
msgstr ""
"<strong>Argumentos tranquilizantes</strong><br/>\n"
"Anticipe las preguntas de sus clientes y preocupaciones en detalles "
"prácticos como tasas y políticas de envío, políticas de devolución y "
"reemplazo, seguridad y métodos de pago, condiciones generales, etc."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Recommended action:</strong>"
msgstr "<strong>Acción recomendada:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Recommended actions:</strong>"
msgstr "<strong>Acciones recomendadas:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>See it in action</strong><br/>"
msgstr "<strong>Véalas en acción</strong><br/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Short description</strong> of the product or service"
msgstr "<strong>Descripción corta</strong> del producto o servicio"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Technical information:</strong> what do you get and how does it work?"
msgstr ""
"<strong>Información técnica:</strong> qué obtiene usted y cómo funciona?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>Total:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Value proposition:</strong> what’s the end-benefit of this product "
"and who is it for?"
msgstr ""
"strong>Propuesta de valor:</strong> cual es el beneficio final de este "
"producto y para quien está hecho?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Variants</strong> of the product like size or color (see below)"
msgstr ""
"<strong>Variantes</strong> del producto como tamaño o color (mire abajo)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_accessory_product_ids
msgid "Accessory Products"
msgstr "Productos accesorios"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Activate 'Suggested products' from the 'Customize' menu."
msgstr "Activar 'Productos sugeridos' desde el menú 'Personalizar'."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Activate the 'Support multiple variants per products' option in"
msgstr "Activar la opción 'Soportar múltiples variantes por producto' en"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Activate the payment options you want to use"
msgstr "Active las opciones de pago que usted quiere usar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Add as many variants as you need from 3 different types: radio buttons, drop-"
"down menu or color buttons."
msgstr ""
"Añada tantas variantes como necesite de 3 diferentes tipos: selección única, "
"selección desplegable o botones de color."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Add to Cart"
msgstr "Añadir al carro"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_products_categories
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid "All Products"
msgstr "Todos los productos"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_pricelist_selectable
msgid "Allow the end user to choose this price list"
msgstr "Permitirle al usuario escoger esta lista de precios"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template_alternative_product_ids
msgid "Appear on the product page"
msgstr "Aparecer en la página de producto"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template_accessory_product_ids
msgid "Appear on the shopping cart"
msgstr "Aparecer en el carro de compra"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Apply"
msgstr "Aplicar"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux Pricelist"
msgstr "Tarifa Benelux"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Bill To:"
msgstr "Facturar a:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing Information"
msgstr "Información de facturación"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Billing<span class=\"chevron\"/>"
msgstr "Facturación<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Can take up to several days for you to receive the money"
msgstr "Puede tomar varios días para que usted reciba el dinero"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_cart_quantity
msgid "Cart Quantity"
msgstr "Cantidad del carro"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""
"Las categorías son usadas para explorar sus productos a\n"
"través de la interfaz táctil."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:59
#, python-format
msgid "Change the price"
msgstr "Cambiar el precio"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_child_id
msgid "Children Categories"
msgstr "Categorías hijas"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:72
#, python-format
msgid "Choose an image"
msgstr "Escoja una imagen"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:73
#, python-format
msgid "Choose an image from the library."
msgstr "Escoja una imagen de la colección."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:39
#, python-format
msgid "Choose name"
msgstr "Escoja nombre"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas Pricelist"
msgstr "Listado de Precios Navideños"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "City"
msgstr "Ciudad"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:25
#, python-format
msgid "Click here to add a new product."
msgstr "Pulse aquí para añadir un nuevo producto."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:67
#, python-format
msgid "Click here to set an image describing your product."
msgstr "Pulse aquí para establecer una imagen describiendo su producto."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:47
#, python-format
msgid "Click on <em>Continue</em> to create the product."
msgstr "Haga clic en <em>Continuar</em> para crear el producto."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:106
#, python-format
msgid "Click on <em>Publish</em> your product so your customers can see it."
msgstr ""
"Haga clic en <em>Publicar</em> su producto para que sus clientes puedan "
"verlo."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:84
#, python-format
msgid "Click on <em>Save</em> to add the image to the product description."
msgstr ""
"Haga clic en <em>Guardar</em> para añadir la imagen a la descripción del "
"producto."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Click to define a new category."
msgstr "Haga clic aquí para crear una nueva categoría."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:112
#, python-format
msgid "Close Tutorial"
msgstr "Cerrar tutorial"

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Color"
msgstr "Color"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Company Name"
msgstr "Nombre de la compañía"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Configure your bank account(s)"
msgstr "Configure su(s) cuenta(s) bancaria(s)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Configure your delivery prices"
msgstr "Configure sus precios de entrega"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Confirm <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Confirmar <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm Order <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Confirmar Pedido <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Confirmation<span class=\"chevron\"/>"
msgstr "Confirmación<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Confirmed"
msgstr "Confirmado"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:110
#, python-format
msgid "Congratulations"
msgstr "Enhorabuena"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:111
#, python-format
msgid "Congratulations! You just created and published your first product."
msgstr "¡Enhorabuena! Acaba de crear y publicar su primer producto."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:53
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
#, python-format
msgid "Continue"
msgstr "Siguiente"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Country"
msgstr "País"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country_group
msgid "Country Group"
msgstr "Grupo de países"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_country_group_ids
msgid "Country Groups"
msgstr "Grupos de países"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Country..."
msgstr "País..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Coupon Code"
msgstr "Código de descuento"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:46
#, python-format
msgid "Create Product"
msgstr "Crear producto"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:31
#, python-format
msgid "Create a new product"
msgstr "Crear un nuevo producto"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:14
#, python-format
msgid "Create a product"
msgstr "Crear un producto"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:24
#, python-format
msgid "Create your first product"
msgstr "Cree su primer producto"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style_create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_create_date
#: model:ir.model.fields,field_description:website_sale.field_product_style_create_date
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Customize your Payment message"
msgstr "Personalice su mensaje de Pago"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_dhl
msgid "DHL integration"
msgstr "Integración con DHL"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_currency_id
msgid "Default Currency"
msgstr "Moneda por defecto"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_id
msgid "Default Pricelist"
msgstr "Tarifa por defecto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Default Sales Team"
msgstr "Equipo de Ventas Predeterminado"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Default Salesperson"
msgstr "Vendedor Predeterminado"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Defining a good delivery strategy is difficult: you don't want to cut into "
"your margins, but you want to be attractive to customers."
msgstr ""
"Definir una buena estrategia de entrega es difícil: usted no quiere reducir "
"sus ingresos, pero a la vez quiere ser atractivo para los clientes."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Delivery Strategy"
msgstr "Estrategia de Entrega"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_quote_description
msgid "Description for the quote"
msgstr "Descripción para el presupuesto"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_description
msgid "Description for the website"
msgstr "Descripción para el sitio web"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template_website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr ""
"Determine el orden de visualización en el sitio web de comercio electrónico"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line_discounted_price
msgid "Discounted price"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_display_name
#: model:ir.model.fields,field_description:website_sale.field_product_style_display_name
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:90
#, python-format
msgid "Drag & Drop a block"
msgstr "Arrastre y suelte un bloque"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:91
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr "Arrastre este bloque y suéltelo en su página."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist_code
msgid "E-commerce Promotional Code"
msgstr "Código Promocional"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:60
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr "Edite el precio de este producto pulsando en el importe."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Electronic payments have the advantage of being integrated into the buying "
"process. This means your customers will be less likely to drop out before "
"the payment, resulting in a higher conversion rate at the end."
msgstr ""
"Los pagos electrónicos tienen la ventaja de estar integrados en el proceso "
"de compra. Esto quiere decir que sus clientes serán menos propensos a irse "
"antes de pagar, resultando en una tasa más alta de conversión al final."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Email"
msgstr "Email"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:40
#, python-format
msgid "Enter a name for your new product"
msgstr "Escriba un nombre para su nuevo producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Enter their identification credentials"
msgstr "Introduzca sus credenciales de identificación"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Enter your existing products into this CSV file, respecting its structure."
msgstr ""
"Introduzca sus productos existentes en este archivo CSV, respetando su "
"estructura."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_split_method
msgid ""
"Equal : Cost will be equally divided.\n"
"By Quantity : Cost will be divided according to product's quantity.\n"
"By Current cost : Cost will be divided according to product's current cost.\n"
"By Weight : Cost will be divided depending on its weight.\n"
"By Volume : Cost will be divided depending on its volume."
msgstr ""

#. module: website_sale
#: constraint:product.public.category:0
msgid "Error ! You cannot create recursive categories."
msgstr "¡Error! No puede crear categorías recursivas"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Example of Good Product Page"
msgstr "Ejemplo de una Buena Página de Producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Example: you sell T-shirts in the US only. You can offer free delivery "
"because your items are medium priced and the delivery costs are limited and "
"well defined."
msgstr ""
"Ejemplo: usted vende camisetas sólo en Colombia. Usted puede ofrecer entrega "
"gratuita porque sus productos tienen un precio medio y los costos de entrega "
"son limitados y bien definidos."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Example: you sell cheap specialized electronic components. You choose flat "
"rates because the price of an item is sometimes lower than the delivery "
"costs."
msgstr ""
"Ejemplo: usted vende componentes electrónicos especializados baratos. Usted "
"elige tarifas planas debido a que el precio de un artículo es a veces más "
"bajos que los costos de entrega."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Example: you sell custom-made wood sculptures, and because your customers "
"are all over the world, each delivery is different and at cost price."
msgstr ""
"Ejemplo: usted vende esculturas de madera a la medida, y como sus clientes "
"están en todo el mundo, cada entrega es diferente y a precio de costo."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
#, fuzzy
msgid ""
"Export the 3 products you have already created by checking them and choosing "
"'Export' from the 'Action' menu"
msgstr ""
"Exporte 3 de los productos que ya ha creado seleccionándolos y usando la "
"opción 'Exportar' en el menú 'Acción'"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
#, fuzzy
msgid "Extra Info<span class=\"chevron\"/>"
msgstr "Pago<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra Step"
msgstr "Paso Extra"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_fedex
msgid "Fedex integration"
msgstr "Integración con Fedex"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field 1"
msgstr "Campo 1"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field 3"
msgstr "Campo 3"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field not custom"
msgstr "Campo no personalizado"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field not required"
msgstr "Campo no requerido"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field required"
msgstr "Campo requerido"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Focus on adding content and improving the pages for your best-selling "
"products: don't try to create complete pages for all your products at first!"
msgstr ""
"Enfóquese en añadir contenido y mejorar las páginas de sus productos más "
"vendidos: no trate de crear páginas completas para todos sus productos al "
"principio!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Free and easy to setup"
msgstr "Gratuita y fácil de configurar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "From a"
msgstr "Desde un"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de categorías de "
"producto."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Go to the"
msgstr "Vaya al"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style_html_class
msgid "HTML Classes"
msgstr "Clases HTML"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute_value_html_color
msgid "HTML Color Index"
msgstr "Índice de color HTML"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP routing"
msgstr "Enrutado HTTP"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Have a coupon code? Fill in this field and apply."
msgstr "¿Tiene un código de descuento? Rellénelo en este campo y aplíquelo."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Here are <strong>some pros and cons</strong> to help you decide:"
msgstr ""
"Aquí tiene <strong>algunos pros y contras</strong> para ayudarle a decidir:"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_attribute_value_html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color on the website if the attibute type is 'Color'."
msgstr ""
"Aquí puede especificar un índice de color HTML (por ejemplo, #ff0000) para "
"mostrar ese color en el sitio web si el tipo de atributo es 'Color'."

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Hidden"
msgstr "Oculto"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_id
#: model:ir.model.fields,field_description:website_sale.field_product_style_id
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_id_11000
msgid "ID"
msgstr "ID (identificación)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"If you have an eCommerce, one of your objectives is of course to grow your "
"revenues by\n"
"                    selling more and pricier products. Luckily for you, Odoo "
"integrates three powerful\n"
"                    customizations for that."
msgstr ""
"Si usted tiene una Tienda Virtual, uno de sus objetivos es incrementar sus "
"ingresos\n"
"vendiendo más productos y más caros. Por suerte para usted, Odoo integra "
"tres\n"
"poderosas personalizaciones para eso."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image
msgid "Image"
msgstr "Imagen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Imagine a new customer who comes to your website, finds the product they "
"want and add it to their cart.<br/>\n"
"                        Then they get to the checkout page and are hit with "
"the delivery and handling charges.<br/>\n"
"                        Suddenly, a product that looked like it was a fair "
"price seems little expensive, and the customer leaves your website "
"disappointed."
msgstr ""
"Imagine a un cliente nuevo que llega a su sitio web, encuentra el producto "
"que quiere y lo añade a la cesta.<br/>\n"
"Luego va a pagarlo y es sorprendido con los cargos de entrega y transacción."
"<br/>\n"
"Entonces, un producto que parecía que tenía un precio razonable parece ahora "
"un poco caro, y el cliente se va de su tienda decepcionado."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Import Your Products"
msgstr "Importe Sus Productos"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"In order to take money from customers, you need a way of accepting payments."
"<br/>\n"
"                        That's what a payment gateway is for: it helps you "
"make money, but that does cost money.<br/>\n"
"                        That why it's important to choose the right provider "
"for your online payments."
msgstr ""
"Para recibir el dinero de sus clientes, usted necesita una manera de aceptar "
"pagos.<br/>\n"
"Para eso son las pasarelas de pagos: le ayudan a hacer dinero, pero eso "
"cuesta dinero.<br/>\n"
"Por eso es importante escoger el proveedor correcto para sus pagos en línea."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Increase your average cart amount by proposing complementary products to "
"your visitors."
msgstr ""
"Aumente la cantidad promedio de compras proponiendo productos "
"complementarios a sus visitantes."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Increase your chances to make a sale by displaying suggested products."
msgstr ""
"Aumente sus posibilidades de hacer una venta sugiriendo productos "
"relacionados."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:567
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "Correo no válido! Por favor proporcione un correo electrónico válido!"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:86
#, python-format
msgid "It is forbidden to modify a sale order which is not in draft status"
msgstr "Es prohibido modificar una orden de venta que ya no es un borrador"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"It's difficult to recommend one over the others. So, simply pick the one "
"that is more popular in your country!"
msgstr ""
"Es difícil recomendar uno sobre otros. Así que, simplemente escoja el que es "
"más popular en su región!"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_landed_cost_ok
msgid "Landed Costs"
msgstr "Costes en destino"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category___last_update
#: model:ir.model.fields,field_description:website_sale.field_product_style___last_update
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner_last_website_so_id
msgid "Last Online Sale Order"
msgstr "Última Orden de Venta en Línea"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style_write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_write_date
#: model:ir.model.fields,field_description:website_sale.field_product_style_write_date
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image_medium
msgid "Medium-sized image"
msgstr "Imagen mediana"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image_medium
msgid ""
"Medium-sized image of the category. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""
"Imagen mediana de la categoría. Se redimensiona automáticamente a 128x128 "
"px, con el ratio de aspecto preservado. Use este campo en las vistas de "
"formulario o en algunas vistas kanban."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Merchant Connectors"
msgstr "Conectores de Tiendas en línea"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr "Mensaje para la línea del pedido de compra"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Mensaje para la línea de pedido de venta"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.js:15
#, python-format
msgid "My Cart"
msgstr "Mi cesta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_complete_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_name
msgid "Name"
msgstr "Nombre"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Name (Shipping)"
msgstr "Nombre (envío)"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/controllers/main.py:976
#: code:addons/website_sale/static/src/js/website_sale.editor.js:18
#: model_terms:ir.ui.view,arch_db:website_sale.content_new_product
#, python-format
msgid "New Product"
msgstr "Nuevo producto"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:51
#, python-format
msgid "New product created"
msgstr "Nuevo producto creado"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "No monthly fees for standard offer"
msgstr "Sin cuotas mensuales para la oferta estándar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined."
msgstr "No se ha definido producto."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Now you can also <strong>import your existing products:</strong>"
msgstr ""
"Ahora usted también puede <strong>importar sus productos existentes:</strong>"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale.xml:12
#, python-format
msgid "OK"
msgstr "Aceptar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Odoo offers an importation service to handle the whole process for you!"
msgstr ""
"Odoo ofrece un servicio de importación para manejar el proceso completo por "
"usted!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Odoo's web-services allows developers to create scripts that will load data "
"automatically into the system."
msgstr ""
"Los servicios-web de Odoo le permiten a los desarrolladores crear scripts "
"que carguen los datos automáticamente en el sistema."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"On your website, go to the product page where you want to add suggested "
"products."
msgstr ""
"En su sitio web, vaya a la página del producto donde quiere añadir productos "
"sugeridos."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:98
#, python-format
msgid "Once you click on <em>Save</em>, your product is updated."
msgstr "Una vez haga clic en <em>Guardar</em>, su producto es actualizado."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_only_services
msgid "Only Services"
msgstr "Sólo Servicios"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Order"
msgstr "Pedido"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_website_order_line
msgid "Order Lines displayed on Website"
msgstr "Líneas de pedido mostradas en el sitio web"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order_website_order_line
msgid ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."
msgstr ""
"Líneas de pedido a ser mostradas en el sitio web. No se deben usar con "
"propósito de cálculo."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_parent_id
msgid "Parent Category"
msgstr "Categoría padre"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Partner"
msgstr "Empresa"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:779
#: code:addons/website_sale/controllers/main.py:859
#, python-format
msgid "Pay Now"
msgstr "Pagar ahora"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Pagar Ahora <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_payment_acquirer_id
msgid "Payment Acquirer"
msgstr "Método de pago"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "Payment Information"
msgstr "Información de pago"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Payment Method:"
msgstr "Método de pago:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Payment<span class=\"chevron\"/>"
msgstr "Pago<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Phone"
msgstr "Teléfono"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Policies"
msgstr "Políticas"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Price"
msgstr "Precio"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_website_pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "Lista de precios disponible para esta Tienda Virtual/Sitio Web"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_pricelist_id
msgid "Pricelist"
msgstr "Tarifa"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_name
msgid "Pricelist Name"
msgstr "Nombre tarifa"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Product"
msgstr "Producto"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "Atributo de producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Product Name"
msgstr "Nombre del producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product Pages"
msgstr "Páginas de Producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
#, fuzzy
msgid "Product Public Categories"
msgstr "Categorías del producto"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template
msgid "Product Template"
msgstr "Plantilla producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product detail form"
msgstr "Formulario de producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.product_price
msgid "Product not available"
msgstr "Producto no disponible"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Product not found!"
msgstr "Producto no encontrado"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Products"
msgstr "Productos"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Products list"
msgstr "lista"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Products list view"
msgstr "Vista de la lista de productos"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Promote"
msgstr "Promover"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:105
#, python-format
msgid "Publish your product"
msgstr "Publique su producto"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_rating_rating_website_published
msgid "Published"
msgstr "Publicado"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_purchase_line_warn
msgid "Purchase Order Line"
msgstr "Línea pedido de compra"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push down"
msgstr "Mover hacia abajo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to bottom"
msgstr "Empujar hacia abajo"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to top"
msgstr "Empujar hacia arriba"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push up"
msgstr "Move hacia arriba"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Put the practical details (shipping, payment options,...) as links in the "
"footer; that way, they will be accessible from all your product pages."
msgstr ""
"Ponga los detalles prácticos (envío, opciones de pago,...) como enlaces en "
"el pie de página; de esa manera, serán accesibles desde todas las páginas de "
"productos."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Qty:"
msgstr "Ctdad:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Quantity"
msgstr "Cantidad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Quick and easy to set up"
msgstr "Rápida y fácil de configurar"

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Radio"
msgstr "Radio"

#. module: website_sale
#: model:ir.model,name:website_sale.model_rating_rating
#: model:ir.model.fields,field_description:website_sale.field_product_product_rating_ids
msgid "Rating"
msgstr "Calificación"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Read the"
msgstr "Lea la"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Return to the product list."
msgstr "Volver a la lista de producto."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Review Order<span class=\"chevron\"/>"
msgstr "Revisar el Pedido<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Sale"
msgstr "Oferta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sales / Settings"
msgstr "Ventas / Configuración"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "Aviso para pedido de venta"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
#: model:ir.model.fields,field_description:website_sale.field_product_product_sale_line_warn
msgid "Sales Order Line"
msgstr "Línea pedido de venta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website_salesteam_id
msgid "Sales Team"
msgstr "Equipo de ventas"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website_salesperson_id
msgid "Salesperson"
msgstr "Comercial"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:83
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Save and import the modified CSV file from the 'More' menu of the"
msgstr "Guarde e importe el archivo CSV modificado desde el menú 'Más' de la"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:97
#, python-format
msgid "Save your modifications"
msgstr "Guardar los cambios"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Secure Payment"
msgstr "Pago seguro"

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Select"
msgstr "Seleccionar"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:32
#, python-format
msgid ""
"Select <em>New Product</em> to create it and manage its properties to boost "
"your sales."
msgstr ""
"Seleccione <em>Nuevo Producto</em> para crearlo y manejar sus propiedades "
"para incrementar sus ventas."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Select a product from the"
msgstr "Seleccione un producto de la"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_selectable
msgid "Selectable"
msgstr "Seleccionable"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_purchase_line_warn
#: model:ir.model.fields,help:website_sale.field_product_product_sale_line_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Si selecciona la opción \"Aviso\" se notificará a los usuarios con el "
"mensaje, si selecciona \"Mensaje de bloqueo\" se lanzará una excepción con "
"el mensaje y se bloqueará el flujo. El mensaje debe escribirse en el "
"siguiente campo."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sell"
msgstr "Vender"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sell More"
msgstr "Vender Más"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Ship To:"
msgstr "Enviar a:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Ship to the same address"
msgstr "Enviar a la misma dirección"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping"
msgstr "Envío"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Shipping &amp;"
msgstr "Envío &amp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Shipping Connectors"
msgstr "Conectores de Envío"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping Information"
msgstr "Información de envio"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model:website.menu,name:website_sale.menu_shop
msgid "Shop"
msgstr "Tienda"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "Tienda - Compra"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "Tienda - Confirmado"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Method"
msgstr "Tienda - Seleccione el Método de Pago"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Shopping Cart"
msgstr "Carro de compras"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Sign in"
msgstr "Registrar entrada"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Simply add one or more products as an <strong>'Accessory Product'</strong>."
msgstr ""
"Simplemente añada uno o más productos como <strong>'Productos accesorios'</"
"strong>."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Simply add the product you want as an <strong>'Optional Product'</strong>"
msgstr ""
"Simplemente añada el producto que quiere como un <strong>'Producto "
"Opcional'</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Size"
msgstr "Tamaño"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_size_x
msgid "Size X"
msgstr "Tamaño X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_size_y
msgid "Size Y"
msgstr "Tamaño Y"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:19
#, python-format
msgid "Skip It"
msgstr "Saltar este paso"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image_small
msgid "Small-sized image"
msgstr "Imagen de tamaño pequeño"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image_small
msgid ""
"Small-sized image of the category. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."
msgstr ""
"Imagen pequeña de la categoría. Se redimensiona automáticamente a 64x64 px, "
"con el ratio de aspecto preservado. Use este campo donde se requiera una "
"imagen pequeña."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Some customers prefer to pay this way"
msgstr "Algunos clientes prefieren pagar de esta manera"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:589
#, python-format
msgid "Some required fields are empty."
msgstr "Algunos campos requeridos están vacíos."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Sorry, this product is not available anymore."
msgstr "Lo sentimos. Este producto ya no está disponible."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_split_method
msgid "Split Method"
msgstr "Método de división"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:19
#, python-format
msgid "Start Tutorial"
msgstr "Iniciar tutorial"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "State / Province"
msgstr "Estado / Provincia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "State / Province..."
msgstr "Estado / Provincia..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Street"
msgstr "Calle"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style_name
msgid "Style Name"
msgstr "Nombre de estilo"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_style_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_style_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Styles"
msgstr "Estilos"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Subtotal"
msgstr "Subtotal"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_alternative_product_ids
msgid "Suggested Products"
msgstr "Productos sugeridos"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.recommended_products
msgid "Suggested alternatives:"
msgstr "Alternativas sugeridas:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested products:"
msgstr "Productos sugeridos:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Taxes"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_temando
#, fuzzy
msgid "Temando integration"
msgstr "Integración con Fedex"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "Gracias por su pedido."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"The best way to start your online shop is by creating 3 products pages "
"directly in the website.<br/>\n"
"                        To help you, here are some guidelines that will "
"convert customers:"
msgstr ""
"La mejor manera de comenzar su tienda en línea es creando 3 páginas de "
"productos directamente en el sitio web.<br/>\n"
"Para ayudarlo, aquí hay algunas pautas que convertirán a los clientes:"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "The payment seems to have been canceled."
msgstr "El pago parece haber sido cancelado."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "There seems to be an error with your request."
msgstr "Parece haber un error con su petición."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image
msgid ""
"This field holds the image used as image for the category, limited to "
"1024x1024px."
msgstr ""
"Este campo contiene la imagen usada como imagen para la categoría, limitada "
"a 1024x1024 px."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:52
#, python-format
msgid "This page contains all the information related to the new product."
msgstr ""
"Esta página contiene toda la información relacionada con el nuevo producto."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "This promo code is not available"
msgstr "Este código promocional no está disponible"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template_public_categ_ids
msgid "Those categories are used to group similar products for e-commerce."
msgstr ""
"Estas categorías se usan para agrupar productos similares para el comercio "
"electrónico."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "To use them:"
msgstr "Para usarlas:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "Total"
msgstr "Total"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_payment_tx_id
msgid "Transaction"
msgstr "Transacción"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Try to apply what you've learned above by manually creating three Product "
"pages from the Content menu."
msgstr ""
"Trate de aplicar lo que ha aprendido anteriormente creando manualmente tres "
"páginas de Productos desde el menú de Contenido."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute_type
msgid "Type"
msgstr "Tipo"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_ups
msgid "UPS integration"
msgstr "Integración con UPS"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_usps
msgid "USPS integration"
msgstr "Integración con USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Unit Price"
msgstr "Precio unidad"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:66
#, python-format
msgid "Update image"
msgstr "Actualizar imagen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Use the <i>'Content'</i> top menu to create a new product."
msgstr "Use el menú superior <i>'Contenido'</i> para crear un nuevo producto."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Users will buy more accessories and services if they can add them to their "
"cart in one click."
msgstr ""
"Los usuarios comprarán más accesorios y servicios si ellos pueden añadirlos "
"a su cesta en un clic."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "VAT Number"
msgstr "Identificación"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Validate Order"
msgstr "Validar pedido"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "View Cart ("
msgstr "Ver cesta ("

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_published
msgid "Visible in Website"
msgstr "Visible en el sitio web"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_rating_rating_website_published
msgid "Visible on the website as a comment"
msgstr "Visible en el sitio web como un comentario"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Web Service technical documentation."
msgstr "Documentación técnica de los Servicios Web."

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_website_id
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Website"
msgstr "Sitio web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Website Categories"
msgstr "Categorías del Sitio Web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_country_group_website_pricelist_ids
msgid "Website Price Lists"
msgstr "Listas de Precios del Sitio Web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_pricelist_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.website_pricelist_tree_view
msgid "Website PriceLists"
msgstr "Listas de Precios"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_pricelists_by_website
#: model:ir.model,name:website_sale.model_website_pricelist
#: model:ir.ui.menu,name:website_sale.menu_website_sale_pricelists
msgid "Website Pricelist"
msgstr "Lista de Precios"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:427
#, python-format
msgid "Website Pricelist for %s"
msgstr "Lista de precios para %s"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_product_public_category
msgid "Website Product Categories"
msgstr "Categorías de Productos"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product_public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_public_categ_ids
msgid "Website Product Category"
msgstr "Categoría del Producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "Categorías Públicas"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "Tienda del sitio web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_description
msgid "Website meta description"
msgstr "Meta descripción del sitio web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta palabras clave del sitio web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_title
msgid "Website meta title"
msgstr "Meta título del sitio web"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:17
#, python-format
msgid "Welcome to your shop"
msgstr "Bienvenido a su tienda"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"You can also define different prices for the variants you created by "
"activating the 'Use pricelists to adapt your price per customers' option in"
msgstr ""
"Usted también puede definir diferentes precios para cada variante activando "
"la opción 'Diferentes precios por segmentos de clientes' en"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "You can setup 3 types of <strong>payment methods in Odoo:</strong>"
msgstr ""
"Usted puede configurar 3 tipos de <strong>métodos de pago en Odoo:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "You have to reconcile the payment manually"
msgstr "Usted tiene que conciliar el pago manualmente"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:18
#, python-format
msgid ""
"You successfully installed the e-commerce. This guide will help you to "
"create your product and promote your sales."
msgstr ""
"Ha instalado correctamente el comercio electrónico. Esta guía le ayudará a "
"crear su producto y promover sus ventas."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Your Address"
msgstr "Su dirección"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Your Name"
msgstr "Su nombre"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Your Order"
msgstr "Su pedido"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Your cart is empty!"
msgstr "¡Su carro está vacío!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your order has been confirmed, thank you for your loyalty."
msgstr "Su pedido ha sido confirmado, gracias por su fidelidad."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your payment has been received."
msgstr "Se ha recibido su pago."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your transaction is waiting a manual confirmation."
msgstr "Su transacción está esperando una confirmación manual."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your transaction is waiting confirmation."
msgstr "Su transacción está esperando confirmación."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Zip / Postal Code"
msgstr "Código postal"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "and fill in one or more <strong>'Suggested Products'</strong>."
msgstr "y añada uno o más <strong>'Productos sugeridos'</strong>."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "code..."
msgstr "código..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "comment"
msgstr "comentario"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "comments"
msgstr "comentarios"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_sale_ebay
msgid "eBay connector"
msgstr "conector con eBay"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "eCommerce"
msgstr "Tienda del sitio web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "items)"
msgstr "elementos)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "of the Sales module"
msgstr "del módulo de Ventas"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "or"
msgstr "o"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "pagination form-inline"
msgstr "paginación en línea"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute_value
msgid "product.attribute.value"
msgstr "product.attribute.value"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_style
msgid "product.style"
msgstr "product.style"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "select..."
msgstr "seleccionar..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "using one or several of the strategies above"
msgstr "usando una o varias de las estrategias anteriores"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_config_settings
msgid "website.config.settings"
msgstr "website.config.settings"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ 256 bit encryption"
msgstr "☑ Con encriptación de 256 bit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ 30-days money-back guarantee"
msgstr "☑ Garantía de devolución de 30 días"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ Invoice sent by e-Mail"
msgstr "☑ Factura enviada por correo electrónico"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ Processed by Ogone"
msgstr "☑ Procesado po Ogone"

#~ msgid ", go in the 'Sales' tab"
#~ msgstr ", vaya a la pestaña de 'Ventas'"

#~ msgid "<span class=\"fa fa-envelope-o\"/> Email Our Website Expert"
#~ msgstr ""
#~ "<span class=\"fa fa-envelope-o\"/> Escríbale a Nuestro Experto Del Sitio "
#~ "Web"

#~ msgid "Available in the Point of Sale"
#~ msgstr "Disponible en el POS"

#~ msgid ""
#~ "Check if the product should be weighted using the hardware scale "
#~ "integration"
#~ msgstr ""
#~ "Marque si el producto debe ser pesado usando la integración hardware de "
#~ "la balanza"

#~ msgid "Check if you want this product to appear in the Point of Sale"
#~ msgstr "Marque esta casilla si quiere que este producto aparezca en el POS"

#~ msgid ""
#~ "Check this box to generate Call for Tenders instead of generating "
#~ "requests for quotation from procurement."
#~ msgstr ""
#~ "Marque esta casilla para generar una Licitación en vez de generar una "
#~ "solicitud de cotización desde abastecimiento."

#~ msgid "Extra Info"
#~ msgstr "Información extra"

#~ msgid "Payment"
#~ msgstr "Pagos"

#~ msgid "Point of Sale Category"
#~ msgstr "Categoría del POS"

#~ msgid "Procurement"
#~ msgstr "Abastecimiento"

#~ msgid "Project"
#~ msgstr "Proyecto"

#~ msgid ""
#~ "Those categories are used to group similar products for point of sale."
#~ msgstr ""
#~ "Estas categorías se usan para agrupar productos similares para el POS."

#~ msgid "To Weigh With Scale"
#~ msgstr "Para pesar con balanza"

#~ msgid "Website Comments"
#~ msgstr "Comentarios del sitio web"
