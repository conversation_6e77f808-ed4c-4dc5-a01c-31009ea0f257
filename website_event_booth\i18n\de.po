# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>Nessel<PERSON>ch, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                    <span>Sorry, several booths are now sold out. Please change your choices before validating again.</span>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                    <span>Es tut uns leid, mehrere Stände sind bereits ausverkauft. Bitte ändern Sie Ihre Auswahl, bevor Sie erneut validieren.</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event booths\"/><em>Configure Booths</em>"
msgstr ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Veranstaltungsstände konfigurieren\"/><em>Stände konfigurieren</em>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span class=\"text-nowrap\">Sold Out</span>"
msgstr "<span class=\"text-nowrap\">Ausverkauft</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span>Book my Booths</span>"
msgstr "<span>Meine Stände buchen</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>E-Mail</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>Name</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid ""
"<span>This event is finished. It's no longer possible to book a "
"booth.</span>"
msgstr ""
"<span>Diese Veranstaltung ist beendet. Es ist nicht mehr möglich einen Stand"
" zu buchen.</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "<strong>Contact Details</strong>"
msgstr "<strong>Kontaktdaten</strong>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Book my Booths"
msgstr "Meine Stände buchen"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_type_view_form
msgid "Booth Menu Item"
msgstr "Menüpunkt für Stände"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu
msgid "Booth Register"
msgstr "Standregistrierung"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_complete
msgid "Booth Registration completed!"
msgstr "Standregistrierung abgeschlossen!"

#. module: website_event_booth
#: code:addons/website_event_booth/controllers/event_booth.py:0
#, python-format
msgid "Booth registration failed. Please try again."
msgstr "Stand-Anmeldung fehlgeschlagen. Bitte versuchen Sie es erneut."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Booths"
msgstr "Stände"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_type__booth_menu
msgid "Booths on Website"
msgstr "Stände auf Website"

#. module: website_event_booth
#: code:addons/website_event_booth/controllers/event_booth.py:0
#, python-format
msgid "Booths should belong to the same category."
msgstr "Stände sollten derselben Kategorie angehören."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Contact Us"
msgstr "Kontaktieren Sie uns"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_event
msgid "Event"
msgstr "Veranstaltung"

#. module: website_event_booth
#: model:ir.model.fields.selection,name:website_event_booth.selection__website_event_menu__menu_type__booth
msgid "Event Booth Menus"
msgstr "Menüs für Veranstaltungsstand"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu_ids
msgid "Event Booths Menus"
msgstr "Menüs für Veranstaltungsstände"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_type
msgid "Event Template"
msgstr "Veranstaltungsvorlage"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__exhibition_map
msgid "Exhibition Map"
msgstr "Ausstellungsplan"

#. module: website_event_booth
#: code:addons/website_event_booth/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
#, python-format
msgid "Get A Booth"
msgstr "Sichern Sie sich einen Stand"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Menüart"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Mobile"
msgstr "Mobil"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Phone"
msgstr "Telefon"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Sorry, all the booths are sold out."
msgstr "Es tut uns leid, alle Stände sind ausgebucht."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "This event is not open to exhibitors registration,"
msgstr ""
"Diese Veranstaltung ist nicht für die Registrierung von Ausstellern "
"geöffnet,"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "View Floor Plan"
msgstr "Grundriss ansehen"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_website_event_menu
msgid "Website Event Menu"
msgstr "Website-Veranstaltungsmenü"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "check our"
msgstr "konsultieren Sie unsere"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "for this event."
msgstr "für diese Veranstaltung."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "if you have any question."
msgstr ", falls Sie Fragen haben."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "list of future events"
msgstr "Liste zukünftiger Veranstaltungen"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "you can"
msgstr "Sie können"
