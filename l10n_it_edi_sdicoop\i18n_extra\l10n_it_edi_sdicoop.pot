# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_it_edi_sdicoop
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-30 10:14+0000\n"
"PO-Revision-Date: 2022-03-30 10:14+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_it_edi_sdicoop
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_sdicoop.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">\n"
"                                        Fattura Elettronica mode\n"
"                                    </span>"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_sdicoop.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Allow Odoo to process invoices</span>"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_sdicoop.res_config_settings_view_form
msgid "A Demo service is in use."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_sdicoop.res_config_settings_view_form
msgid "An Official or Test service has been registered."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid "Attached file is empty"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_sdicoop.res_config_settings_view_form
msgid "By checking this box, I accept that Odoo may process my invoices."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model,name:l10n_it_edi_sdicoop.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields.selection,name:l10n_it_edi_sdicoop.selection__res_config_settings__l10n_it_edi_sdicoop_demo_mode__demo
msgid "Demo"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_edi_format__display_name
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_res_config_settings__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model,name:l10n_it_edi_sdicoop.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_sdicoop.res_config_settings_view_form
msgid "Electronic Document Invoicing"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_bank_statement_line__l10n_it_edi_attachment_id
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_move__l10n_it_edi_attachment_id
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_payment__l10n_it_edi_attachment_id
msgid "FatturaPA Attachment"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_bank_statement_line__l10n_it_edi_transaction
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_move__l10n_it_edi_transaction
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_payment__l10n_it_edi_transaction
msgid "FatturaPA Transaction"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.actions.server,name:l10n_it_edi_sdicoop.ir_cron_receive_fattura_pa_invoice_ir_actions_server
#: model:ir.cron,cron_name:l10n_it_edi_sdicoop.ir_cron_receive_fattura_pa_invoice
#: model:ir.cron,name:l10n_it_edi_sdicoop.ir_cron_receive_fattura_pa_invoice
msgid "FatturaPA: Receive invoices from the exchange system"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_edi_format__id
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_move__id
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_res_config_settings__id
msgid "ID"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model_terms:ir.ui.view,arch_db:l10n_it_edi_sdicoop.res_config_settings_view_form
msgid ""
"In demo mode Odoo will just simulate the sending of invoices to the government.<br/>\n"
"                                        In test mode (experimental) Odoo will send the invoices to a non-production service.\n"
"                                        Saving this change will direct all companies on this database to this use this configuration.\n"
"                                        Once registered for testing or official, the mode cannot be changed."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"Invoices for PA are not managed by Odoo, you can download the document and "
"send it on your own."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_res_config_settings__is_edi_proxy_active
msgid "Is Edi Proxy Active"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid "Italian invoice: %s"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model,name:l10n_it_edi_sdicoop.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_res_config_settings__l10n_it_edi_proxy_current_state
msgid "L10N It Edi Proxy Current State"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_res_config_settings__l10n_it_edi_sdicoop_demo_mode
msgid "L10N It Edi Sdicoop Demo Mode"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_res_config_settings__l10n_it_edi_sdicoop_register
msgid "L10N It Edi Sdicoop Register"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_edi_format____last_update
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_account_move____last_update
#: model:ir.model.fields,field_description:l10n_it_edi_sdicoop.field_res_config_settings____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields.selection,name:l10n_it_edi_sdicoop.selection__res_config_settings__l10n_it_edi_sdicoop_demo_mode__prod
msgid "Official"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"Please fill your codice fiscale to be able to receive invoices from "
"FatturaPA"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid "Service momentarily unavailable"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: model:ir.model.fields.selection,name:l10n_it_edi_sdicoop.selection__res_config_settings__l10n_it_edi_sdicoop_demo_mode__test
msgid "Test (experimental)"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/res_config_settings.py:0
#, python-format
msgid ""
"The company has already registered with the service as 'Test' or 'Official',"
" it cannot change."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"The filename is duplicated. Try again (or adjust the FatturaPA Filename "
"sequence). Original message from the SDI: %s"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"The invoice has been issued, but the delivery to the Addressee has failed. "
"You will be required to send a courtesy copy of the invoice to your customer"
" through another channel, outside of the Exchange System, and promptly "
"notify him that the original is deposited in his personal area on the portal"
" \"Invoices and Fees\" of the Revenue Agency."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"The invoice has been issued, but the delivery to the Public Administration "
"has failed. The Exchange System will contact them to report the problem and "
"request that they provide a solution. During the following 10 days, the "
"Exchange System will try to forward the FatturaPA file to the Public "
"Administration in question again. Should this also fail, the System will "
"notify Odoo of the failed delivery, and you will be required to send the "
"invoice to the Administration through another channel, outside of the "
"Exchange System."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid "The invoice has been refused by the Exchange System"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"The invoice has been succesfully transmitted. The addressee has 15 days to "
"accept or reject it."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid "The invoice was refused by the addressee."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"The invoice was sent to FatturaPA, but we are still awaiting a response. "
"Click the link above to check for an update."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"The invoice was successfully transmitted to the Public Administration and we"
" are waiting for confirmation"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"The invoice was successfully transmitted to the Public Administration and we"
" are waiting for confirmation."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"This invoice number had already been submitted to the SdI, so it is set as Sent. Please verify that the system is correctly configured, because the correct flow does not need to send the same invoice twice for any reason.\n"
" Original message from the SDI: %s"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid "Unauthorized user"
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid "You are not allowed to check the status of this invoice."
msgstr ""

#. module: l10n_it_edi_sdicoop
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#: code:addons/l10n_it_edi_sdicoop/models/account_edi_format.py:0
#, python-format
msgid ""
"You must accept the terms and conditions in the settings to use FatturaPA."
msgstr ""
