# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
# 
# Translators:
# <PERSON><PERSON>, 2021
# <PERSON>, 2021
# krnkris, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# Szabolcs Rádi, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Szabolcs Rádi, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""
".\n"
"               Manuális akciók lehetnek szükségesek."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Preparation</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/><b>Előkészítés</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> <b>Cancelled</b>"
msgstr "<i class=\"fa fa-fw fa-times\"/> <b>Törölve</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> <b>Shipped</b>"
msgstr "<i class=\"fa fa-fw fa-truck\"/><b>Szállítva</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Értékesítések</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Értékesítve</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference:</strong>"
msgstr "<strong>Ügyfél hivatkozás:</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Delivery Orders</strong>"
msgstr "<strong>Szállítási rendelések</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Incoterm: </strong>"
msgstr "<strong>Incoterm: </strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_invoice_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Incoterm:</strong>"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"A készletezett termék olyan materiális termék, mely készletre kerül. Ehhez telepíteni kell a Készlet app-ot.\n"
"A fogyóeszköz termék olyan materiális termék, mely nem kerül készletre.\n"
"A szolgálatás immateriális termék."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"A termék konfigurációjától függően a kiszállított mennyiség az alábbi módokon számítható:\n"
"  - Manuális: a mennyiség kézi megadása a soron\n"
"  - Analitikus kiadás: a mennyiség a könyvelésre küldött kiadások összesítéséből számítódik\n"
"  - Időnyilvántartás: a mennyiség az értékesítési rendelés soraihoz kapcsolt feladatokra rögzített órák összege\n"
"  - Készletmozgások: a mennyiség a jóváhagyott kiszedések mennyisége\n"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_aftersale
msgid "After-Sale"
msgstr "Értékesítés utáni"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "All planned operations included"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "Amilyen gyorsan csak lehet"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/js/qty_at_date_widget.js:0
#, python-format
msgid "Availability"
msgstr "Elérhetőség"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available"
msgstr "Elérhető"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available in stock"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr "Értékesítési rendelés sorain specifikus útvonal alkalmazása"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Vállalatok"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr "Az első szállítási rendelés elvégzésének dátuma"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Beállítások módosítása"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "Dátum:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Szállítás"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "Szállítólevelek"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr ""

#. module: sale_stock
#: model:res.groups,name:sale_stock.group_display_incoterm
msgid "Display incoterms on Sales Order and related invoices"
msgstr "Értékesítési rendelésen és kapcsolódó számláin Incoterm megjelenítése"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Display incoterms on orders &amp; invoices"
msgstr "Incoterm megjelenítése rendeléseken és számlákon"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr ""
"Ne felejtse el megváltoztatni a partner a következő szállítási rendeléseken:"
" %s"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Documentation"
msgstr "Dokumentáció"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "Tényleges dátum"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr "Kivétel(ek) merült(ek) fel a kiszedés kapcsán:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Kivétel merült fel értékesítési rendelés kapcsán:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "Kivételek:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Várható dátum"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Expected Delivery"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr "Várható:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Forecasted Stock"
msgstr "Előrejelzett készlet"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Ha minden terméket egyszerre szállít, akkor a szállítási rendelés a "
"legnagyobb átfutási idejű termékhez lesz igazítva. Egyéb esetben a "
"legrövidebbhez kerül igazításra."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr "Érintett transzfer(ek):"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "Nemzetközi Kereskedelmi Feltétel"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__group_display_incoterm
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Incoterms"
msgstr "Incoterm"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"A Nemzetközi Kereskedelmi Feltételek olyan előredefiniált üzleti feltételek,"
" melyeket nemzetközi ügyleteknél használnak."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "Készlet"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Készletútvonalak"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_invoiced
msgid "Invoicing"
msgstr "Számlázás"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "Könyvelési tétel"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "Könyvelési tételsor"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Lot/szériaszám"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Make To Order"
msgstr "Rendelésre készítés"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "Kézi műveletek lehetnek szükségesek"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""
"Vevőknek ígért határidőkkel kapcsolatos hiba határa. A termékek szállítási "
"dátuma az ígért dátumnál ennyi nappal előbbre kerül ütemezésre, abból a "
"célból, hogy kezeljük a beszállítási lánc előre nem látható késéseit."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised"
" date, to cope with unexpected delays in the supply chain."
msgstr ""
"Vevőknek ígért határidőkkel kapcsolatos hiba határa. A termékek beszerzési "
"és szállítási dátuma az ígért dátumnál ennyi nappal előbbre kerül "
"ütemezésre, abból a célból, hogy kezeljük a beszállítási lánc előre nem "
"látható késéseit."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Kiszállított mennyiség frissítésének módszere"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr "Várható szállítási dátumok előre mozgatása"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No enough future availaibility"
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No future availaibility"
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "On"
msgstr "Ekkor"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Ordered quantity decreased!"
msgstr "Rendelt mennyiség csökkentve!"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "Kiszedési politika"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "Beszerzési csoport"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product Template"
msgstr "Terméksablon"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__product_type
msgid "Product Type"
msgstr "Terméktípus"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Szállítandó mennyiség"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_report_product_product_replenishment
msgid "Quotations"
msgstr "Árajánlatok"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Remaining demand available at"
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Reserved"
msgstr "Lefoglalt"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr "Útvonal"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr "Értékesítés sor"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "Értékesítési rendelés"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "Értékesítési rendelések"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_count
msgid "Sale order count"
msgstr "Értékesítési rendelések száma"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "Értékesítési elemzés riport"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "Értékesítési rendelés"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Rendelés törlése"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Értékesítési rendelés sor"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Értékesítési rendelés sorok"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_ids
msgid "Sales Orders"
msgstr "Értékesítési rendelések"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr "Értékesítés biztonsági nap"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr "Szállítások korábbi időzítése a csúszások elkerüléséhez"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "Ütemezett dátum"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr "Biztonsági átfutási idő"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr "Biztonsági átfutási idő értékesítéshez"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_location_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "Kiválasztható értékesítési rendelési soron"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr "Termékek szállítása egyszerre"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr "Szállítás amint lehetséges, visszamaradt rendelésekkel együtt"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "Szállítási politika"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "Sold in the last 365 days"
msgstr "Elmúlt 365 napban értékesített"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"Some products have already been delivered. Returns can be created from the "
"Delivery Orders."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Készletmozgás"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "Készletmozgások"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "Készletfeltöltés kimutatás"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Készletszabály"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Készletszabály riport"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Készletszabály riport"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "The delivery"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%s\"</strong> To <strong>\"%s\"</strong>,\n"
"                        You should probably update the partner on this document."
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "This product is replenished on demand."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Szállítás"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "Transzferek"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "Users"
msgstr "Felhasználók"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "View Forecast"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "Raktár"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Warning!"
msgstr "Figyelem!"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "Amikor minden termék rendelkezésre áll"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr "Mikor kezdődjön a kiszállítás"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the delivery order if needed."
msgstr ""
"Lecsökkenti a rendelt mennyiséget! Ne felejtse el kézzel frissíteni a "
"szállítási rendelést, ha szükséges."

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You cannot decrease the ordered quantity of a sale order line below its delivered quantity.\n"
"Create a return in your inventory first."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "visszavont"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "nap"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "ebből"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "rendelt ehelyett:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr "feldolgozott ehelyett: "

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "will be late."
msgstr ""
