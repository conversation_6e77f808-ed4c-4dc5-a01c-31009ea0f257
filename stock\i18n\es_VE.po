# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-05-15 18:54+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Venezuela) (http://www.transifex.com/odoo/odoo-9/"
"language/es_VE/)\n"
"Language: es_VE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_state
msgid ""
"\n"
"                * Draft: not confirmed yet and will not be scheduled until "
"confirmed\n"
"\n"
"                * Waiting Another Operation: waiting for another move to "
"proceed before it becomes automatically available (e.g. in Make-To-Order "
"flows)\n"
"\n"
"                * Waiting Availability: still waiting for the availability "
"of products\n"
"\n"
"                * Partially Available: some products are available and "
"reserved\n"
"\n"
"                * Ready to Transfer: products reserved, simply waiting for "
"confirmation.\n"
"\n"
"                * Transferred: has been processed, can't be modified or "
"cancelled anymore\n"
"\n"
"                * Cancelled: has been cancelled, can't be confirmed anymore"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_stock_dropshipping
msgid ""
"\n"
"Creates the dropship route and add more complex tests\n"
"-This installs the module stock_dropshipping."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1873
#, python-format
msgid " (%s reserved)"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1876
#, python-format
msgid " (reserved)"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3548
#, python-format
msgid " MTO"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:211 code:addons/stock/product.py:384
#, python-format
msgid " On Hand"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3685 code:addons/stock/stock.py:3978
#, python-format
msgid " Sequence in"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3689 code:addons/stock/stock.py:3986
#, python-format
msgid " Sequence internal"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3686 code:addons/stock/stock.py:3980
#, python-format
msgid " Sequence out"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3687 code:addons/stock/stock.py:3982
#, python-format
msgid " Sequence packing"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3688 code:addons/stock/stock.py:3984
#, python-format
msgid " Sequence picking"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "#Products"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2801
#, python-format
msgid "%s %s %s has been <b>moved to</b> scrap."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3365
#, python-format
msgid "%s: Supply Product from %s"
msgstr ""

#. module: stock
#: code:addons/stock/res_company.py:25
#, python-format
msgid "%s: Transit Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_state
msgid ""
"* New: When the stock move is created and not yet confirmed.\n"
"* Waiting Another Move: This state can be seen when a move is waiting for "
"another one, for example in a chained flow.\n"
"* Waiting Availability: This state is reached when the procurement "
"resolution is not straight forward. It may need the scheduler to run, a "
"component to be manufactured...\n"
"* Available: When products are reserved, it is set to 'Available'.\n"
"* Done: When the shipment is processed, the state is 'Done'."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_usage
msgid ""
"* Vendor Location: Virtual location representing the source location for "
"products coming from your vendors\n"
"                       \n"
"* View: Virtual location used to create a hierarchical structures for your "
"warehouse, aggregating its child locations ; can't directly contain "
"products\n"
"                       \n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"                       \n"
"* Customer Location: Virtual location representing the destination location "
"for products sent to your customers\n"
"                       \n"
"* Inventory Loss: Virtual location serving as counterpart for inventory "
"operations used to correct stock levels (Physical inventories)\n"
"                       \n"
"* Procurement: Virtual location serving as temporary counterpart for "
"procurement operations when the source (vendor or production) is not known "
"yet. This location should be empty when the procurement scheduler has "
"finished running.\n"
"                       \n"
"* Production: Virtual counterpart location for production operations: this "
"location consumes the raw material and produces finished products\n"
"                       \n"
"* Transit Location: Counterpart location that should be used in inter-"
"companies or inter-warehouses operations\n"
"                      "
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ", if accounting or purchase is installed"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "- The Odoo Team"
msgstr ", actualizado:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">'Available'</"
"span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Click on <span "
"class=\"fa fa-truck\"/> Delivery</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Click on <span "
"class=\"fa fa-truck\"/> Shipment</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Confirm Order</"
"span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Confirm Sale</"
"span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Find Incoming "
"Shipments</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Process the "
"products</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Validate the "
"Delivery</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Validate the "
"Receipt Order</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min :</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57408;\"/>\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if "
"&gt;100 products</span>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57408;\"/>\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 50 "
"vendors</span>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57440;\"/>\n"
"                                        <strong> Create manually</strong><br/"
">\n"
"                                        <span class=\"small\">&lt; 50 "
"vendors</span>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57440;\"/>\n"
"                                        <strong> Create manually</strong><br/"
">\n"
"                                        <span class=\"small\">Recommended if "
"&lt;100 products</span>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>From the Inventory "
"application</strong>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>From the Purchase "
"application</strong>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>From the Sales application</"
"strong>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Customer Address:</strong></span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Vendor Address:</strong></span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Warehouse Address:</strong></span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>New</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>View</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span>You need to install the Accounting or Purchases app to manage vendors."
"</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span>You need to install the Purchases Management app for this flow.</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span>You need to install the Sales Management app for this flow.</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Activate <i>Track lots or serial numbers</i></strong> in your"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Automated flows</strong>: from sale to delivery, and purchase to "
"reception"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Automated replenishment rules</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Available products</strong> are currently available for use in "
"filling a new order for purposes such as production or distribution. This "
"quantity does not include items already allocated to other orders or items "
"that are in transit from a supplier"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Barcode</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Buy:</strong> the product is bought from a vendor through a Purchase "
"Order"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Commitment Date</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Consumable products</strong> are always assumed to be in sufficient "
"quantity in your stock, therefore their available quantities are not tracked"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Date</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Destination</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Enjoy your Inventory management with Odoo!</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Import data</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Inventory</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Location</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Make to Order:</strong> the product is acquired only as demand "
"requires, each time a Sales Order is confirmed. This does not modify stock "
"in the medium term because you restock with the exact amount that was ordered"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Make to Stock:</strong> your customers are supplied from available "
"stock. If the quantities in stock are too low to fulfill the order, a "
"Purchase Order is generated according the minimum stock rules in order to "
"get the products required"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Manufacture:</strong> the product is manufactured internally or the "
"service is supplied from internal resources"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No address defined on the supplier partner:</strong> you have to "
"complete an address for the default supplier for the product concerned."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No bill of materials defined for production:</strong> you need to "
"create a BoM or indicate that the product can be purchased instead."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No quantity available in stock:</strong> you have to create a "
"reordering rule and put it in the order, or manually procure it."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No supplier available for a purchase:</strong> you have to define a "
"supplier in the Procurements tab of the product form."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>On Hand products</strong> are physically located in the warehouse "
"location at the current time. This includes items that are already allocated "
"to fulfilling production needs or sales orders"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order (Origin)</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Package</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Production Lot</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Serial Number</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Service products</strong> are non-material products provided by a "
"company or an individual"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Source</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>State</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Status</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Stockable products</strong> are subject to the full inventory "
"management system: minimum stock rules, automatic procurement, etc."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Total Quantity</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Warehouse Locations</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>When you process an incoming shipment, internal transfer or "
"delivery</strong>, assign a lot number or different lot numbers or serial "
"numbers to a product by clicking on the <span class=\"fa fa-list\"/> icon"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2941
#, python-format
msgid "A Pack"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_barcode_nomenclature_id
msgid "A barcode nomenclature"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "A classic purchase flow looks like the following:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "A classic sales flow looks like the following:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"A good inventory management aims to optimize stock levels: not too low (or\n"
"                        you may find yourself out of stock) and not too high "
"(your products occupy\n"
"                        space and may lose value)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "API Documentation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Accurate visibility on all your operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_active
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_active
#: model:ir.model.fields,field_description:stock.field_stock_location_active
#: model:ir.model.fields,field_description:stock.field_stock_location_path_active
#: model:ir.model.fields,field_description:stock.field_stock_location_route_active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_active
msgid "Active"
msgstr "Activo"

#. module: stock
#: selection:stock.config.settings,module_stock_calendar:0
msgid "Adapt lead times using the suppliers' open days calendars (advanced)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add an internal note..."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_partner_id
msgid "Address"
msgstr "Dirección"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_claim_from_delivery
msgid ""
"Adds a Claim link to the delivery order.\n"
"-This installs the module claim_from_delivery."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Advanced"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_stock_adv_location:0
msgid "Advanced routing of products using rules"
msgstr ""

#. module: stock
#: selection:stock.move,procure_method:0
msgid "Advanced: Apply Procurement Rules"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "Todos"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
msgid "All Operations"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
#: model:ir.ui.menu,name:stock.all_picking
msgid "All Transfers"
msgstr ""

#. module: stock
#: selection:stock.picking,move_type:0
msgid "All at once"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2927
#, python-format
msgid "All products"
msgstr "Todos los productos"

#. module: stock
#: selection:stock.config.settings,group_stock_tracking_owner:0
msgid "All products in your warehouse belong to your company"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_returned_move_ids
msgid "All returned moves"
msgstr ""

#. module: stock
#: code:addons/stock/procurement.py:360
#, python-format
msgid "All stock moves have been cancelled for this procurement."
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_claim_from_delivery:0
msgid "Allow claims on deliveries"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_show_entire_packs
msgid "Allow moving packs"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_stock_dropshipping:0
msgid "Allow suppliers to deliver directly to your customers"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_procurement_jit
msgid ""
"Allows you to automatically reserve the available\n"
"            products when confirming a sale order.\n"
"                This installs the module procurement_jit."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_packaging
msgid ""
"Allows you to create and manage your packaging dimensions and types you want "
"to be maintained in your system."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_uom
msgid ""
"Allows you to select and maintain different units of measure for products."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_product_selectable
msgid "Applicable on Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_product_categ_selectable
msgid "Applicable on Product Category"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_warehouse_selectable
msgid "Applicable on Warehouse"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form_stock_inherit
msgid "Applied On"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Apply"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_decimal_precision
msgid ""
"As an example, a decimal precision of 2 will allow weights like: 9.99 kg, "
"whereas a decimal precision of 4 will allow weights like:  0.0231 kg."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_make_procurment_wizard
msgid "Ask New Products"
msgstr "Solicitar nuevos productos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Assign Owner"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_auto
#: selection:stock.location.path,auto:0
msgid "Automatic Move"
msgstr "Movimiento automático"

#. module: stock
#: selection:stock.location.path,auto:0
msgid "Automatic No Step Added"
msgstr "Automático paso no añadido"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_procurement
msgid "Automatic Procurements"
msgstr "Abastecimientos automáticos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_string_availability_info
msgid "Availability"
msgstr "Disponibilidad"

#. module: stock
#: selection:stock.move,state:0 selection:stock.pack.operation,state:0
#: selection:stock.picking,state:0
msgid "Available"
msgstr "Disponible"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
msgid "Available Products"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_backorder_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_backorder_id
msgid "Back Order of"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Back Orders"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_backorder_confirmation.py:33
#, python-format
msgid "Back order <em>%s</em> <b>cancelled</b>."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1127
#, python-format
msgid "Back order <em>%s</em> <b>created</b>."
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4801
#, python-format
msgid "Backorder exists"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_barcode
msgid "Barcode"
msgstr "Código de barras"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Barcode Interface"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_barcode_nomenclature_id
msgid "Barcode Nomenclature"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_stock_barcode
msgid "Barcode scanner support"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Before creating your products, here are a few concepts your should "
"understand:"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_4
msgid "Big Vendors"
msgstr ""

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid "Bring goods to output location before shipping (Pick + Ship)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Bulk Content"
msgstr ""

#. module: stock
#: selection:product.template,tracking:0
msgid "By Lots"
msgstr ""

#. module: stock
#: selection:product.template,tracking:0
msgid "By Unique Serial Number"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2218
#, python-format
msgid ""
"By changing this quantity here, you accept the new quantity as complete: "
"Odoo will not automatically generate a back order."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"By default, Odoo measures products by 'units', which are generic and "
"represent just about anything"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_incoterms_active
msgid ""
"By unchecking the active field, you may hide an INCOTERM you will not use."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "Vista calendario"

#. module: stock
#: code:addons/stock/stock.py:3424
#, python-format
msgid "Can't find any customer or supplier location."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3532
#, python-format
msgid "Can't find any generic Make To Order route."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_make_procurment_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_scrap_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Cancel"
msgstr "Cancelar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Cancel Inventory"
msgstr ""

#. module: stock
#: selection:stock.inventory,state:0 selection:stock.move,state:0
#: selection:stock.pack.operation,state:0 selection:stock.picking,state:0
msgid "Cancelled"
msgstr "Cancelada"

#. module: stock
#: code:addons/stock/stock.py:2048
#, python-format
msgid "Cannot unreserve a done move"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Carriers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template_route_from_categ_ids
msgid "Category Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_move_dest_exists
msgid "Chained Move Exists"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_pack_operation_exist
msgid "Check the existance of pack operation on the picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_quant_reserved_exist
msgid "Check the existance of quants linked to this picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_move_ids_exist
msgid "Check the existance of stock moves linked to this inventory"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_return_location
msgid "Check this box to allow using this location as a return location."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_scrap_location
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_qty
msgid "Checked Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_partially_available
msgid "Checks if the move has some stock reserved"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_claim_from_delivery
msgid "Claims"
msgstr "Reclamaciones"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done_grouped
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "Click here to create a new transfer."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_deliver_move
msgid "Click to add a delivery order for this product."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Click to add a location."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_form
msgid "Click to add a reordering rule."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Click to add a route."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Click to add a serial number."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Click to create a new picking type."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_move_form2
msgid "Click to create a stock movement."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree
msgid "Click to create a stock operation."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
msgid "Click to define a new transfer."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Click to define a new warehouse."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receipt_picking_move
msgid "Click to register a product receipt."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receive_move
msgid "Click to register a receipt for this product."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_inventory_form
msgid "Click to start an inventory."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_code
msgid "Code"
msgstr "Código"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_color
msgid "Color"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_company_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_company_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route_company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Company"
msgstr "Compañía"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_date_done
msgid "Completion Date of Transfer"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_procurement_orderpoint_compute
msgid "Compute Minimum Stock Rules"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Compute Stock"
msgstr "Calcular stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Conditions"
msgstr "Condiciones"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
msgid "Configuration"
msgstr "Configuración"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Configure Warehouse"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Confirmed"
msgstr "Confirmada"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Confirmed Moves"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Congratulations!"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "Consumable"
msgstr "Consumible"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_children_ids
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Contained Packages"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_child_ids
msgid "Contains"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "Contenido"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_posx
msgid "Corridor (X)"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_immediate_transfer.py:31
#, python-format
msgid ""
"Could not reserve all requested products. Please use the 'Mark as Todo' "
"button to handle the reservation manually."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1601
#, python-format
msgid "Create Backorder?"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_use_create_lots
msgid "Create New Lots"
msgstr ""

#. module: stock
#: selection:procurement.rule,procure_method:0
msgid "Create Procurement"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create Vendors"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create a Quotation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create a RFQ"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder, if you expect to process the remaining\n"
"                        products later.  Do not create a backorder if you "
"will not\n"
"                        supply the remaining products."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create an Inventory Adjustment"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create your products"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_move_ids
msgid "Created Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_procurement_ids
msgid "Created Procurements"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_create_uid
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_create_uid
#: model:ir.model.fields,field_description:stock.field_product_putaway_create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_path_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_create_date
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_create_date
#: model:ir.model.fields,field_description:stock.field_product_putaway_create_date
#: model:ir.model.fields,field_description:stock.field_product_removal_create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_create_date
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_create_date
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_create_date
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_path_create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route_create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_create_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_create_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_create_date
msgid "Created on"
msgstr "Creado en"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form_stock_inherit
msgid "Creates"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation"
msgstr "Creación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_create_date
msgid "Creation Date"
msgstr "Fecha de creación"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_date
msgid "Creation Date, usually the time of the order"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3872
#, python-format
msgid "Cross-Dock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_crossdock_route_id
msgid "Crossdock Route"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_cumulative_quantity
msgid "Cumulative Quantity"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at "
"this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the "
"Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its "
"children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' "
"type."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4897
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_order_partner_dest_id
msgid "Customer Address"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template_sale_delay
msgid "Customer Lead Time"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner_property_stock_customer
#: selection:stock.location,usage:0
msgid "Customer Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_customers
#: selection:stock.picking,picking_type_code:0
#: selection:stock.picking.type,code:0
msgid "Customers"
msgstr "Clientes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_dhl
msgid "DHL integration"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Daily Operations"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Dashboard"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_date
#: model:ir.model.fields,field_description:stock.field_stock_move_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_date
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
msgid "Date"
msgstr "Fecha"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
msgid "Date Expected"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_date_done
msgid "Date of Transfer"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_date
msgid "Date of latest Inventory"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_move_date
msgid "Date of latest Stock Move"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_tree
msgid "Dates of Inventories"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_form
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "Dates of Inventories & Moves"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_report_stock_lines_date
msgid "Dates of Inventories and latest Moves"
msgstr ""

#. module: stock
#: selection:stock.warehouse.orderpoint,lead_type:0
msgid "Day(s) to get the products"
msgstr ""

#. module: stock
#: selection:stock.warehouse.orderpoint,lead_type:0
msgid "Day(s) to purchase"
msgstr ""

#. module: stock
#: model:res.company,overdue_msg:stock.res_company_1
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. "
"Please find details below.\n"
"If the amount has already been paid, please disregard this notice. "
"Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_decimal_precision
msgid "Decimal precision on weight"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_default_location_dest_id
msgid "Default Destination Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_owner_id
msgid "Default Owner"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_default_resupply_wh_id
msgid "Default Resupply Warehouse"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_default_location_src_id
msgid "Default Source Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_reception_steps
msgid "Default incoming route to follow"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_delivery_steps
msgid "Default outgoing route to follow"
msgstr ""

#. module: stock
#: selection:stock.move,procure_method:0
msgid "Default: Take From Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_route_ids
msgid "Defaults routes through the warehouse"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_product_expiry:0
msgid "Define Expiration Date on serial numbers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Define routes within your warehouse according to business needs, such as "
"Quality Control, After Sales Services or Supplier Returns"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"                organization. Odoo is able to manage physical locations\n"
"                (warehouses, shelves, bin, etc), partner locations "
"(customers,\n"
"                vendors) and virtual locations which are the counterpart of\n"
"                the stock operations like the manufacturing orders\n"
"                consumptions, inventories, etc."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_putaway_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) "
"where to store the products. This method can be enforced at the product "
"category level, and a fallback is made on the parent locations if none is "
"set here."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) "
"where to take the products from, which lot etc. for this location. This "
"method can be enforced at the product category level, and a fallback is made "
"on the parent locations if none is set here."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form_stock_inherit
msgid "Delay"
msgstr "Retraso"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_delay
msgid "Delay (days)"
msgstr "Retraso (días)"

#. module: stock
#: code:addons/stock/product.py:307
#, python-format
msgid "Delivered Qty"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_deliver_move
#: model_terms:ir.ui.view,arch_db:stock.product_kanban_stock_view
msgid "Deliveries"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_delivery_count
#: model_terms:ir.ui.view,arch_db:stock.product_kanban_stock_view
msgid "Delivery"
msgstr "Envío"

#. module: stock
#: code:addons/stock/stock.py:3733
#: model:stock.picking.type,name:stock.chi_picking_type_out
#: model:stock.picking.type,name:stock.picking_type_out
#, python-format
msgid "Delivery Orders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_delivery_route_id
msgid "Delivery Route"
msgstr ""

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_delivery
msgid "Delivery Slip"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_type
#, fuzzy
msgid "Delivery Type"
msgstr "Envío"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_route_ids
#: model:ir.model.fields,help:stock.field_product_template_route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, MTO/MTS,..."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Deployment"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_name
msgid "Description"
msgstr "Descripción"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Pickings"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Pickings (Rack, Row and Case Information)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_description_picking
#: model:ir.model.fields,field_description:stock.field_product_template_description_picking
msgid "Description on Picking"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Destination"
msgstr "Destino"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_partner_id
msgid "Destination Address "
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_move_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_location_dest_id
msgid "Destination Location"
msgstr "Ubicación destino"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Destination Location Zone"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_order_move_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_move_move_dest_id
msgid "Destination Move"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_result_package_id
msgid "Destination Package"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_route_ids
msgid "Destination route"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
msgid "Details"
msgstr "Detalles"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_procure_method
msgid ""
"Determines the procurement method of the stock move that will be generated: "
"whether it will need to 'take from the available stock' in its source "
"location or needs to ignore its stock and create a procurement over there."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form_save
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_lot_form
msgid "Discard"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.location_dispatch_zone
msgid "Dispatch Zone"
msgstr "Zona de expedición"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_display_name
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_display_name
#: model:ir.model.fields,field_description:stock.field_product_putaway_display_name
#: model:ir.model.fields,field_description:stock.field_product_removal_display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_display_name
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_display_name
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_display_name
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_path_display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_route_display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_display_name
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_display_name
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_display_name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_display_name
msgid "Display Name"
msgstr "Mostrar nombre"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_line_date
msgid ""
"Display the latest Inventories and Moves done on your products and easily "
"sort them with specific filtering criteria. If you do frequent and partial "
"inventories, you need this report in order to ensure that the stock of each "
"product is controlled at least once a year. This also lets you find out "
"which products have seen little move lately and may deserve special measures "
"(discounted sale, quality control...)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Do not hesitate to send us an email to describe your experience or to "
"suggest improvements!"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_claim_from_delivery:0
msgid "Do not manage claims"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_stock_packaging:0
#: selection:stock.config.settings,group_stock_tracking_lot:0
msgid "Do not manage packaging"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_stock_production_lot:0
msgid "Do not track individual product items"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_product_expiry:0
msgid "Do not use Expiration Date on serial numbers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_qty
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_processed_boolean
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_qty_done
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.move,state:0 selection:stock.pack.operation,state:0
#: selection:stock.picking,state:0
msgid "Done"
msgstr "Realizado"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_done
msgid "Done Transfers"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_done_grouped
msgid "Done Transfers by Date"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Download the"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.inventory,state:0 selection:stock.pack.operation,state:0
#: selection:stock.picking,state:0
msgid "Draft"
msgstr "Borrador"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_stock_dropshipping
msgid "Dropshipping"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Edit its details or add new ones"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "End"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_7
msgid "European Customers"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"                location to another one.  For instance, if you receive "
"products\n"
"                from a vendor, Odoo will move products from the Vendor\n"
"                location to the Stock location. Each report can be performed "
"on\n"
"                physical, partner or virtual locations."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4253
#, python-format
msgid "Everything inside a package should be in the same location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Excel template"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
msgid "Exhausted Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_date_expected
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Expected Date"
msgstr "Fecha prevista"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_product_expiry
msgid "Expiration Dates"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1525
#, python-format
msgid "Extra Move: "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal_method
msgid "FIFO, LIFO..."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_fedex
msgid "Fedex integration"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Filters"
msgstr "Filtro"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway
msgid "Fixed Locations Per Categories"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway_fixed_location_ids
msgid "Fixed Locations Per Product Category"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Force Availability"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category_removal_strategy_id
msgid "Force Removal Strategy"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_virtual_available
msgid "Forecast Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in "
"this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the "
"Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' "
"type."
msgstr ""

#. module: stock
#: code:addons/stock/product.py:301
#: model:ir.model.fields,field_description:stock.field_product_template_virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move_availability
#, python-format
msgid "Forecasted Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_from_loc
msgid "From"
msgstr "Desde"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_complete_name
msgid "Full Location Name"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:305
#, python-format
msgid "Future Deliveries"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:311
#, python-format
msgid "Future P&L"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:323
#, python-format
msgid "Future Productions"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:317
#, python-format
msgid "Future Qty"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:295
#, python-format
msgid "Future Receipts"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.location_gate_a
msgid "Gate A"
msgstr "Puerta A"

#. module: stock
#: model:stock.location,name:stock.location_gate_b
msgid "Gate B"
msgstr "Puerta B"

#. module: stock
#: model:stock.location,name:stock.stock_location_5
msgid "Generic IT Vendors"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_fixed_putaway_strat_sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top "
"of the list."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
msgid "Global"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_procurement_rules
msgid "Global Procurement Rules"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_location_path
msgid "Global Push Rules"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_default_resupply_wh_id
msgid "Goods will always be resupplied from this warehouse"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
msgid "Graph"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "Agrupar por"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Group by..."
msgstr "Agrupar por..."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_form_stock_inherit
msgid "Group's Pickings"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_pack_operation_exist
msgid "Has Pack Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_move_ids_exist
msgid "Has Stock Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_quant_reserved_exist
msgid "Has quants already reserved"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_posz
msgid "Height (Z)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Help rental management, by generating automated return moves for rented "
"products"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Here are some usual problems and their solutions:"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receipt_picking_move
msgid ""
"Here you can receive individual products, no matter what\n"
"                purchase order or picking order they come from. You will "
"find\n"
"                the list of all products you are waiting for. Once you "
"receive\n"
"                an order, you can filter based on the name of the vendor or\n"
"                the purchase order reference. Then you can confirm all "
"products\n"
"                received using the buttons on the right of each line."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_deliver_move
msgid ""
"Here you will find the history of all past deliveries related to\n"
"                this product, as well as all the products you must deliver "
"to\n"
"                customers."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receive_move
msgid ""
"Here you will find the history of all receipts related to\n"
"                this product, as well as all future receipts you are "
"waiting\n"
"                from your suppliers."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "History"
msgstr "Historial"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "How to use Lot Tracking:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_id
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_id
#: model:ir.model.fields,field_description:stock.field_product_putaway_id
#: model:ir.model.fields,field_description:stock.field_product_removal_id
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_id
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_id
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_id
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_id
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_id
#: model:ir.model.fields,field_description:stock.field_stock_location_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route_id
#: model:ir.model.fields,field_description:stock.field_stock_move_id
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_id_8069
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_id
msgid "ID"
msgstr "ID"

#. module: stock
#: code:addons/stock/stock.py:3243
#, python-format
msgid "INV:"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:101
#, python-format
msgid "INV: %s"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_3
msgid "IT Vendors"
msgstr ""

#. module: stock
#: model:product.product,name:stock.product_icecream
#: model:product.template,name:stock.product_icecream_product_template
msgid "Ice Cream"
msgstr ""

#. module: stock
#: model:product.product,description:stock.product_icecream
#: model:product.template,description:stock.product_icecream_product_template
msgid ""
"Ice cream can be mass-produced and thus is widely available in developed "
"parts of the world. Ice cream can be purchased in large cartons (vats and "
"squrounds) from supermarkets and grocery stores, in smaller quantities from "
"ice cream shops, convenience stores, and milk bars, and in individual "
"servings from small carts or vans at public events."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_name
msgid "Identifier"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid ""
"If a product is not at the right place, set the checked quantity to 0 and "
"create a new line with correct location."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_show_entire_packs
msgid ""
"If checked, this shows the packs to be moved as a whole in the Operations "
"tab all the time, even if there was no entire pack reserved."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_propagate
msgid ""
"If checked, when the previous move is cancelled or split, the move generated "
"by this move will too"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_propagate
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_propagate
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""
"Si está marcado, cuando este movimiento se cancela, también cancela el "
"movimiento relacionado."

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_route_id
msgid "If route_id is False, the rule is global"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_result_package_id
msgid "If set, the operations are packed into this package"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"Si el campo activo se desmarca, permite ocultar la regla de stock mínimo sin "
"eliminarla."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
msgid "If the route is global"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_negative_move_id
msgid ""
"If this is a negative quant, this will be the move that caused this negative "
"quant."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Serial "
"Numbers / Lots, so you can provide them in a text field. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Serial Number / Lots. You "
"can also decide to not put lots in this picking type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"If you are a developer <strong>you can use our\n"
"                        API</strong> to load data automatically through\n"
"                        scripts: take a look at our"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"If you configured automatic procurement, Odoo automatically generates\n"
"                        Procurements Orders. You usually don't need to worry "
"about them, but\n"
"                        sometimes the system can remain blocked without "
"generating a\n"
"                        corresponding document, usually due to a "
"configuration problem."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_filter
msgid ""
"If you do an entire inventory, you can choose 'All Products' and it will "
"prefill the inventory with the current stock.  If you only do some products  "
"(e.g. Cycle Counting) you can choose 'Manual Selection of Products' and the "
"system won't propose anything.  You can also let the system propose for a "
"single product / lot /... "
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"If you have less than 50 vendors, we recommend you\n"
"                                        to create them manually."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "If you want to do it yourself:"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer
msgid "Immediate Transfer"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1584
#, python-format
msgid "Immediate Transfer?"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "Immediate transfer?"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Import using the top left button in"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"In Odoo, <strong>Reordering Rules</strong> are used to replenish your "
"products.\n"
"                        Odoo will automatically propose a procurement to buy "
"new products if you are\n"
"                        running out of stock."
msgstr ""

#. module: stock
#: selection:stock.inventory,state:0
msgid "In Progress"
msgstr "En progreso"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_in_type_id
msgid "In Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_partner_dest_id
msgid ""
"In case of dropshipping, we need to know the destination address more "
"precisely"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"In case of unique serial numbers, each serial number corresponds\n"
"                        to exactly one piece.  In case of lots, you need to "
"supply the quantity\n"
"                        for each lot when you move that product."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"In short, you will get a more efficient warehouse management that leads\n"
"                        to inventory reduction and better efficiencies in "
"your daily operations."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template_incoming_qty
msgid "Incoming"
msgstr "Entrante"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_receipt_picking_move
msgid "Incoming  Products"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_in_date
msgid "Incoming Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_reception_steps
msgid "Incoming Shipments"
msgstr "Albaranes de entrada"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_incoterms_code
msgid "Incoterm Standard Code"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_incoterms_tree
#: model:ir.model,name:stock.model_stock_incoterms
#: model:ir.ui.menu,name:stock.menu_action_incoterm_open
#: model_terms:ir.ui.view,arch_db:stock.stock_incoterms_form
#: model_terms:ir.ui.view,arch_db:stock.view_incoterms_tree
msgid "Incoterms"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_incoterms_name
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-the-"
"art transportation practices."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2217
#, python-format
msgid "Information"
msgstr "Información"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Initial Demand"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Initial Inventory"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3821
#: model:stock.location,name:stock.stock_location_company
#, python-format
msgid "Input"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_input_stock_loc_id
msgid "Input Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_stock_picking_wave
msgid ""
"Install the picking wave module which will help you grouping your pickings "
"and processing them in batch"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_inter_wh
msgid "Inter Company Transit"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: selection:stock.picking,picking_type_code:0
#: selection:stock.picking.type,code:0
msgid "Internal"
msgstr "Interno"

#. module: stock
#: selection:stock.location,usage:0
msgid "Internal Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_ref
msgid "Internal Reference"
msgstr "Referencia interna"

#. module: stock
#: code:addons/stock/stock.py:3746
#: model:stock.picking.type,name:stock.picking_type_internal
#, python-format
msgid "Internal Transfers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company_internal_transit_location_id
msgid "Internal Transit Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_int_type_id
msgid "Internal Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot_ref
msgid ""
"Internal reference number in case it differs from the manufacturer's serial "
"number"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_location_id
msgid "Inventoried Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_lot_id
msgid "Inventoried Lot/Serial Number"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_partner_id
msgid "Inventoried Owner"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_package_id
msgid "Inventoried Pack"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_product_id
msgid "Inventoried Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_ids
msgid "Inventories"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventories Month"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
#: model:ir.actions.report.xml,name:stock.action_report_inventory
#: model:ir.model,name:stock.model_stock_inventory
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_inventory_id
#: model:ir.model.fields,field_description:stock.field_stock_move_inventory_id
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Adjustment"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_form
#: model:ir.ui.menu,name:stock.menu_action_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Adjustments"
msgstr ""

#. module: stock
#: model:web.planner,tooltip_planner:stock.planner_inventory
msgid "Inventory Configuration: a step-by-step guide."
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
msgid "Inventory Control"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_date
msgid "Inventory Date"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Details"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_line
msgid "Inventory Line"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_line_ids
msgid "Inventory Lines."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template_property_stock_inventory
msgid "Inventory Location"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr ""

#. module: stock
#: selection:stock.location,usage:0
msgid "Inventory Loss"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_move_ids
msgid "Inventory Moves."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_name
msgid "Inventory Name."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_name
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventory Reference"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_location_route
msgid "Inventory Routes"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Inventory Settings"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.quantsact
#: model:ir.ui.menu,name:stock.menu_quants
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_graph_value
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "Inventory Valuation"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_inventory_value
msgid "Inventory Value"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid ""
"Inventory adjustments will be made by comparing the theoretical and the "
"checked quantities."
msgstr ""

#. module: stock
#: model:stock.location,name:stock.location_inventory
msgid "Inventory loss"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory of"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_return_location
msgid "Is a Return Location?"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_scrap_location
msgid "Is a Scrap Location?"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"It is also possible to import your initial inventory from an Excel or CSV "
"file.\n"
"                        If you want to do that, contact your Odoo project "
"manager."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"It is therefore a good idea to check and try to resolve those procurement\n"
"                        exceptions. These are accessible from the Schedulers "
"menu (you need the Stock\n"
"                        Manager role to see it)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "It is time to make your initial Inventory. In order to do so:"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_packaging
msgid ""
"It specifies attributes of packaging like type, quantity of packaging,etc."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_last_done_picking
msgid "Last 10 Done Pickings"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement___last_update
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute___last_update
#: model:ir.model.fields,field_description:stock.field_product_putaway___last_update
#: model:ir.model.fields,field_description:stock.field_product_removal___last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast___last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date___last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation___last_update
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty___last_update
#: model:ir.model.fields,field_description:stock.field_stock_config_settings___last_update
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat___last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer___last_update
#: model:ir.model.fields,field_description:stock.field_stock_incoterms___last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory___last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line___last_update
#: model:ir.model.fields,field_description:stock.field_stock_location___last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_path___last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_route___last_update
#: model:ir.model.fields,field_description:stock.field_stock_move___last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link___last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap___last_update
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation___last_update
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot___last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking___last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking_type___last_update
#: model:ir.model.fields,field_description:stock.field_stock_production_lot___last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant___last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant_package___last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking___last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line___last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse___last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint___last_update
msgid "Last Modified on"
msgstr "Modificada por última vez"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_write_uid
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_write_uid
#: model:ir.model.fields,field_description:stock.field_product_putaway_write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_path_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_write_uid
msgid "Last Updated by"
msgstr "Última actualización realizada por"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_write_date
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_write_date
#: model:ir.model.fields,field_description:stock.field_product_putaway_write_date
#: model:ir.model.fields,field_description:stock.field_product_removal_write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_write_date
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_write_date
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_write_date
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_path_write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route_write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_write_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_write_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_write_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_write_date
msgid "Last Updated on"
msgstr "Ultima actualizacion en"

#. module: stock
#: code:addons/stock/stock.py:4799
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#, python-format
msgid "Late"
msgstr "Retrasado"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_line_date
#: model:ir.ui.menu,name:stock.menu_report_stock_line_date
msgid "Latest Inventories & Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_launch_pack_operations
msgid "Launch Pack Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_lead_days
msgid "Lead Time"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_lead_type
msgid "Lead Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_parent_left
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_parent_left
msgid "Left Parent"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Like with the sales flow, Odoo inventory management is\n"
"                        fully integrated with the purchase app."
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_move_operation_link
msgid "Link between stock moves and pack operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_linked_move_operation_ids
msgid "Linked Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_linked_move_operation_ids
msgid "Linked Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_propagated_from_id
msgid "Linked Quant"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Localization"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4963
#: model:ir.model.fields,field_description:stock.field_product_product_location_id
#: model:ir.model.fields,field_description:stock.field_product_template_location_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_location_id
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_fixed_location_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_location_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_location_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_location_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
#, python-format
msgid "Location"
msgstr "Lugar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Location & Warehouse"
msgstr ""

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_location_barcode
msgid "Location BarCode"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_location_name
#: model:ir.model.fields,field_description:stock.field_stock_location_name
msgid "Location Name"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_tree
msgid "Location Paths"
msgstr "Rutas de ubicaciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_lot_stock_id
msgid "Location Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_usage
msgid "Location Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Ubicación donde el sistema almacenará los productos finalizados."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4964
#: model:ir.model.fields,field_description:stock.field_stock_move_restrict_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_restrict_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_lot_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#, python-format
msgid "Lot"
msgstr "Lote"

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_lot_barcode
msgid "Lot BarCode"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4542
#, python-format
msgid "Lot Details"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_tree
msgid "Lot Inventory"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_lot_name
msgid "Lot Name"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Lot Split"
msgstr ""

#. module: stock
#: constraint:stock.pack.operation.lot:0
msgid "Lot is required"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_lot_id
msgid "Lot/Serial Number"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_lot_ids
msgid "Lots"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_pack_lot_ids
msgid "Lots Used"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_production_lot
msgid "Lots and Serial Numbers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Lots can be encoded on incoming shipments, internal transfers and\n"
"                        outgoing deliveries according to the settings in the "
"picking type.\n"
"                        The tracking can be configured on every product: not "
"any tracing at\n"
"                        all, tracking by lot, or tracking by unique serial "
"number."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_mto_pull_id
msgid "MTO rule"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_make_procurement
msgid "Make Procurements"
msgstr "Realizar abastecimientos"

#. module: stock
#: code:addons/stock/stock.py:3529
#: model:stock.location.route,name:stock.route_warehouse0_mto
#, python-format
msgid "Make To Order"
msgstr ""

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid ""
"Make packages into a dedicated location, then bring them to the output "
"location for shipping (Pick + Pack + Ship)"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_stock_packaging:0
msgid "Manage available packaging options per products"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_stock_tracking_owner:0
msgid "Manage consignee stocks (advanced)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Manage default locations per product"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage multiple stock_locations"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage multiple warehouses"
msgstr ""

#. module: stock
#: selection:stock.config.settings,warehouse_and_location_usage_level:0
msgid "Manage only 1 Warehouse with only 1 stock location"
msgstr ""

#. module: stock
#: selection:stock.config.settings,warehouse_and_location_usage_level:0
msgid "Manage only 1 Warehouse, composed by several stock locations"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_stock_picking_wave:0
msgid "Manage picking in batch per worker"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_stock_picking_wave:0
msgid "Manage pickings one at a time"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Manage product manufacturing chains"
msgstr ""

#. module: stock
#: selection:stock.config.settings,warehouse_and_location_usage_level:0
msgid "Manage several Warehouses, each one composed by several stock locations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_multi_locations
msgid "Manage several stock locations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_multi_warehouses
msgid "Manage several warehouses"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Manager"
msgstr "Responsable"

#. module: stock
#: selection:stock.location.path,auto:0
msgid "Manual Operation"
msgstr "Operación manual"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_max_date
msgid "Max. Expected Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_max_qty
msgid "Maximum Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway_method
#: model:ir.model.fields,field_description:stock.field_product_removal_method
msgid "Method"
msgstr "Método"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company_propagation_minimum_delta
msgid "Minimum Delta for Propagation of a Date Change on moves linked together"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Regla de inventario mínimo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_min_qty
msgid "Minimum Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_order_orderpoint_id
msgid "Minimum Stock Rule"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_stock_calendar
msgid "Minimum Stock Rules"
msgstr "Reglas de stock mínimo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_propagation_minimum_delta
msgid ""
"Minimum days to trigger a propagation of date change in pushed/pull flows."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Misc"
msgstr "Varios"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Modify"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "More <i class=\"fa fa-caret-down\"/>"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree
msgid ""
"Most operations are prepared automatically by Odoo according\n"
"                to your preconfigured logistics rules, but you can also "
"record\n"
"                manual stock movements."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_move_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_move_id
msgid "Move"
msgstr "Movimiento"

#. module: stock
#: code:addons/stock/procurement.py:25
#, python-format
msgid "Move From Another Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_lines_related
msgid "Move Lines"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_negative_move_id
msgid "Move Negative Quant"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_split_from
msgid "Move Split From"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_procure_method
msgid "Move Supply Method"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_date
msgid ""
"Move date: scheduled date until move is done, then date of actual move "
"processing"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_move_dest_id
msgid "Move which caused (created) the procurement"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Moved Quants"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_product_stock_move_open
#: model:ir.model.fields,field_description:stock.field_procurement_order_move_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_history_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_move_ids
msgid "Moves created by the procurement"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group. "
"If none is given, the moves generated by procurement rules will be grouped "
"into one big picking."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_linked_move_operation_ids
msgid ""
"Moves impacted by this operation for the computation of the remaining "
"quantities"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_history_ids
msgid "Moves that operate(d) on this quant"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway_name
#: model:ir.model.fields,field_description:stock.field_product_removal_name
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_complete_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_name
msgid "Name"
msgstr "Nombre"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_negative_dest_location_id
msgid "Negative Destination Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
msgid "Negative Stock"
msgstr ""

#. module: stock
#: selection:stock.move,state:0
msgid "New"
msgstr "Nuevo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_new_quantity
msgid "New Quantity on Hand"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_fresh_record
msgid "Newly created pack operation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "No Inventory yet"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "No Stock Move yet"
msgstr ""

#. module: stock
#: selection:product.template,tracking:0
msgid "No Tracking"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_stock_adv_location:0
msgid "No automatic routing of products"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1614
#, python-format
msgid "No negative quantities allowed"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:75
#, python-format
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)!"
msgstr ""

#. module: stock
#: code:addons/stock/procurement.py:313
#, python-format
msgid "No source location defined!"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_product_variant:0
msgid "No variants on products"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_8
msgid "Non European Customers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_pack_operation_product_ids
msgid "Non pack"
msgstr ""

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Normal"
msgstr "Normal"

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Not urgent"
msgstr "No urgente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_note
#: model:ir.model.fields,field_description:stock.field_stock_picking_note
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Notes"
msgstr "Notas"

#. module: stock
#: code:addons/stock/stock.py:1063
#, python-format
msgid "Nothing to check the availability for."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Now, all your product quantities are correctly set."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_delay
msgid "Number of Days"
msgstr "Número de días"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_lead_days
msgid ""
"Number of days after the orderpoint is triggered to receive the products or "
"to order to the vendor"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_delay
msgid "Number of days needed to transfer the goods"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4803
#, python-format
msgid "OK"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Odoo handles <strong>advanced push/pull routes configuration</strong>, for "
"example:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Odoo has preconfigured <strong>one Warehouse</strong> for you."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Odoo inventory management is fully integrated with sales and\n"
"                        invoicing process. Everything is automated from the "
"initial\n"
"                        quotation to the delivery and the final invoice."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Odoo is able to run advanced traceability by using Product Lots and Serial\n"
"                        Numbers, usually identified by bar codes stuck on "
"the products."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Of course, feel free to add your own. Please note that Odoo is able to "
"convert units within the same category, for example, liters to gallons in "
"the volume category"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "On Hand"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "On Hand / Available Quantities"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Once it's fully working, give us some feedback: we love to hear from our "
"customer. It would be great if you could send us a photo of your warehouse to"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2939
#, python-format
msgid "One Lot/Serial Number"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2936
#, python-format
msgid "One owner only"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2937
#, python-format
msgid "One product for a specific owner"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2927
#, python-format
msgid "One product only"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_operation_id
msgid "Operation"
msgstr "Operación"

#. module: stock
#: code:addons/stock/stock.py:4559
#, python-format
msgid "Operation Details"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_name
msgid "Operation Name"
msgstr "Nombre operación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#, fuzzy
msgid "Operation Types"
msgstr "Operaciones"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Operations"
msgstr "Operaciones"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_linked_move_operation_ids
msgid ""
"Operations that impact this move for the computation of the remaining "
"quantities"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_posx
#: model:ir.model.fields,help:stock.field_stock_location_posy
#: model:ir.model.fields,help:stock.field_stock_location_posz
msgid "Optional localization details, for information purpose only"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_move_dest_id
msgid "Optional: next stock move when chaining them"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Order Date"
msgstr "Fecha entrega"

#. module: stock
#: model:stock.location,name:stock.location_order
msgid "Order Processing"
msgstr "Procesando pedido"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Orders processed Today or planned for Today"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Origin"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_origin_returned_move_id
msgid "Origin return move"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_move_orig_ids
msgid "Original Move"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:577
#, python-format
msgid "Otherwise make sure the right stock/owner is set."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_out_type_id
msgid "Out Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template_outgoing_qty
msgid "Outgoing"
msgstr "Saliente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_delivery_steps
msgid "Outgoing Shippings"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3823
#: model:stock.location,name:stock.stock_location_output
#, python-format
msgid "Output"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_output_stock_loc_id
msgid "Output Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_location_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Owner"
msgstr "Propietario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_restrict_partner_id
msgid "Owner "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_partner_id
msgid "Owner of the location if not internal"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_owner_id
msgid "Owner of the quants"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:313
#, python-format
msgid "P&L Qty"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3758
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_package_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_pack_operation_pack_ids
#, python-format
msgid "Pack"
msgstr "Paquete"

#. module: stock
#: model:ir.actions.act_window,name:stock.pack_details
msgid "Pack Details"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_pack_type_id
msgid "Pack Type"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4965
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
#, python-format
msgid "Package"
msgstr ""

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_quant_package_barcode_small
msgid "Package BarCode"
msgstr ""

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_quant_package_barcode
msgid "Package BarCode with Contents"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_complete_name
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_name
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Package To Move"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_packaging_id
msgid "Package Type"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_packagings
msgid "Package Types"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model:ir.ui.menu,name:stock.menu_packages_config
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form
msgid "Packages"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created by pack operations made on transfers and can "
"contains several different products. You can then reuse a package to move "
"its whole content somewhere else, or to pack it into another bigger package. "
"A package can also be unpacked, allowing the disposal of its former content "
"as single units again."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Packaging"
msgstr "Empaquetado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_packaging
msgid "Packaging Methods"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_pack_stock_loc_id
msgid "Packing Location"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_pack_operation
msgid "Packing Operation"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3824
#: model:stock.location,name:stock.location_pack_zone
#, python-format
msgid "Packing Zone"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packs and Lots"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Parameters"
msgstr "Parámetros"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_parent_id
msgid "Parent Package"
msgstr ""

#. module: stock
#: selection:stock.picking,move_type:0
msgid "Partial"
msgstr "Parcial"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_partially_available
#: selection:stock.pack.operation,state:0 selection:stock.picking,state:0
msgid "Partially Available"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_procurement_group_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "Empresa"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_partner_address_id
msgid "Partner Address"
msgstr "Dirección empresa"

#. module: stock
#: model:stock.location,name:stock.stock_location_locations_partner
msgid "Partner Locations"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_inventory_form
msgid ""
"Periodical Inventories are used to count the number of products\n"
"                available per location. You can use it once a year when you "
"do\n"
"                the general inventory or whenever you need it, to adapt the\n"
"                current inventory level of a product."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Periodical Tasks"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Physical Inventories by Month"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_locations
msgid "Physical Locations"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_quant_package
msgid "Physical Packages"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3770
#, python-format
msgid "Pick"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3875
#, python-format
msgid "Pick + Pack + Ship"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3874
#, python-format
msgid "Pick + Ship"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_pick_type_id
msgid "Pick Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_pick_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_pick_id
msgid "Pick id"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "Albarán"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking List"
msgstr "Albarán"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr ""

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_picking
msgid "Picking Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Picking Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_name
msgid "Picking Type Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_picking_type_id
msgid ""
"Picking Type determines the way the picking should be shown in the view, "
"reports, ..."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_return_picking_type_id
msgid "Picking Type for Returns"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_stock_picking_wave
msgid "Picking Waves"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "Orden de Despacho"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view_herited
msgid "Pickings"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings already processed"
msgstr "La orden de despacho se encuentra procesada."

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Pickings for Groups"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings that are late on scheduled time"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
msgid "Pivot"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_date_planned
msgid "Planned Date"
msgstr "Fecha planificada"

#. module: stock
#: model:ir.model,name:stock.model_web_planner
msgid "Planner"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1572
#, python-format
msgid ""
"Please create some Initial Demand or Mark as Todo and create some "
"Operations. "
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1733
#, python-format
msgid "Please process some quantities to put in the pack first!"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2774
#, python-format
msgid "Please provide a positive quantity to scrap."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:158
#, python-format
msgid "Please specify at least one non-zero quantity."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Positive"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_route_ids
#: model:ir.model.fields,field_description:stock.field_procurement_order_route_ids
msgid "Preferred Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_route_ids
msgid "Preferred route to be followed by the procurement order"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_route_ids
msgid ""
"Preferred route to be followed by the procurement order. Usually copied from "
"the generating document (SO) but could be set up manually."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_printed
msgid "Printed"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_sequence
#: model:ir.model.fields,field_description:stock.field_stock_move_priority
#: model:ir.model.fields,field_description:stock.field_stock_picking_priority
msgid "Priority"
msgstr "Prioridad"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_priority
msgid ""
"Priority for this picking. Setting manually a value here would set it as "
"priority for all the moves"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_procurement_order
#: model:ir.model.fields,field_description:stock.field_stock_move_procurement_id
#: selection:stock.location,usage:0
msgid "Procurement"
msgstr "Abastecimiento"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_procurement_action
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Procurement Exceptions"
msgstr "Excepciones abastecimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_group_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Procurement Group"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_order_location_id
#: model:ir.model.fields,field_description:stock.field_procurement_rule_location_id
#: model:ir.model.fields,field_description:stock.field_product_product_property_stock_procurement
#: model:ir.model.fields,field_description:stock.field_product_template_property_stock_procurement
msgid "Procurement Location"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_make_procurement
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_make_procurment_wizard
msgid "Procurement Request"
msgstr "Solicitud de abastecimiento"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
msgid "Procurement Requisition"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_procurement_rule
#: model:ir.model.fields,field_description:stock.field_stock_move_rule_id
msgid "Procurement Rule"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.procrules
#: model:ir.model.fields,field_description:stock.field_stock_location_route_pull_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Procurement Rules"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_procurement_op
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_procurement_jit
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model:stock.location,name:stock.location_procurement
msgid "Procurements"
msgstr "Abastecimientos"

#. module: stock
#: code:addons/stock/product.py:325
#, python-format
msgid "Produced Qty"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_product
#: model:ir.model.fields,field_description:stock.field_make_procurement_product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_product_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_product_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_product_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_lot_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Product"
msgstr "Producto"

#. module: stock
#: code:addons/stock/stock.py:254
#: model:ir.model.fields,field_description:stock.field_stock_location_route_categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#, python-format
msgid "Product Categories"
msgstr "Categorías de producto"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_category_id
msgid "Product Category"
msgstr "Categoría de producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_code
msgid "Product Code"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_name
msgid "Product Name"
msgstr "Nombre producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_tracking_owner
msgid "Product Owners"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_stock_move_product_tmpl_id
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Product Types"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_product_variant
msgid "Product Variants"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.location_production
#: selection:stock.location,usage:0
msgid "Production"
msgstr "Producción"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template_property_stock_production
msgid "Production Location"
msgstr "Ubicación de producción"

#. module: stock
#: code:addons/stock/stock.py:244
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.model.fields,field_description:stock.field_stock_location_route_product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
#, python-format
msgid "Products"
msgstr "Productos"

#. module: stock
#: selection:stock.config.settings,group_product_variant:0
msgid ""
"Products can have several attributes, defining variants (Example: size, "
"color,...)"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_uom:0
msgid "Products have only one unit of measure (easier)"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:43
#, python-format
msgid "Products: "
msgstr "Productos: "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_propagate
#: model:ir.model.fields,field_description:stock.field_stock_location_path_propagate
#: model:ir.model.fields,field_description:stock.field_stock_move_propagate
msgid "Propagate cancel and split"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Purchase Flow"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_push_rule_id
msgid "Push Rule"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.stolocpath
#: model:ir.model.fields,field_description:stock.field_stock_location_route_push_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Push Rules"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_location_path
msgid "Pushed Flows"
msgstr "Flujos empujados"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_putaway_id
msgid "Put Away Method"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_putaway
#: model:ir.model.fields,field_description:stock.field_stock_location_putaway_strategy_id
msgid "Put Away Strategy"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway
msgid "Putaway"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Qty"
msgstr "Ctdad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_qty_multiple
msgid "Qty Multiple"
msgstr "Ctdad múltiple"

#. module: stock
#: sql_constraint:stock.warehouse.orderpoint:0
msgid "Qty Multiple must be greater than or equal to zero."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3822
#, python-format
msgid "Quality Control"
msgstr "Control calidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2146
#, python-format
msgid ""
"Quantities, Units of Measure, Products and Locations cannot be modified on "
"stock moves that have already been processed (except by the Administrator)."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_qty
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_quantity
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_product_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_product_uom_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_product_qty
#: model:ir.model.fields,field_description:stock.field_stock_quant_qty
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Quantity"
msgstr "Cantidad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_qty_available
#: model:ir.model.fields,field_description:stock.field_product_template_qty_available
msgid "Quantity On Hand"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_reserved_availability
msgid "Quantity Reserved"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:90
#, python-format
msgid "Quantity cannot be negative."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_qty
msgid "Quantity in the default UoM of the product"
msgstr ""

#. module: stock
#: sql_constraint:stock.pack.operation.lot:0
msgid "Quantity must be greater than or equal to 0.0!"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_qty
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_incoming_qty
msgid ""
"Quantity of products that are planned to arrive.\n"
"In a context with a single Stock Location, this includes goods arriving to "
"this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the "
"Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with "
"'internal' type."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_outgoing_qty
msgid ""
"Quantity of products that are planned to leave.\n"
"In a context with a single Stock Location, this includes goods leaving this "
"Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock "
"Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' "
"type."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_operation_link_qty
msgid ""
"Quantity of products to consider when talking about the contribution of this "
"pack operation towards the remaining quantity of the move (and inverse). "
"Given in the product main uom."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_reserved_availability
msgid "Quantity that has already been reserved for this move"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Quants"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "Preparado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Real Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_reception_count
#: model_terms:ir.ui.view,arch_db:stock.product_kanban_stock_view
msgid "Receipt"
msgstr "Recibo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_reception_route_id
msgid "Receipt Route"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3869
#, python-format
msgid "Receipt in 1 step"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3870
#, python-format
msgid "Receipt in 2 steps"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3871
#, python-format
msgid "Receipt in 3 steps"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3722
#: model:ir.actions.act_window,name:stock.action_receive_move
#: model_terms:ir.ui.view,arch_db:stock.product_kanban_stock_view
#: model:stock.picking.type,name:stock.chi_picking_type_in
#: model:stock.picking.type,name:stock.picking_type_in
#, python-format
msgid "Receipts"
msgstr ""

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid "Receive goods directly in stock (1 step)"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:297
#, python-format
msgid "Received Qty"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Recompute"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_recompute_pack_op
msgid "Recompute pack operation?"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_stock_tracking_lot:0
msgid "Record packages used on packing: pallets, boxes, ..."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_name
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Reference"
msgstr "Referencia"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_sequence_id
msgid "Reference Sequence"
msgstr ""

#. module: stock
#: sql_constraint:stock.picking:0
msgid "Reference must be unique per company!"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_origin
msgid "Reference of the document"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_pack_operation_ids
msgid "Related Packing Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_remaining_qty
msgid "Remaining Qty"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_remaining_qty
msgid "Remaining Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_remaining_qty
msgid ""
"Remaining Quantity in default UoM according to operations matched with this "
"move"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_remaining_qty
msgid ""
"Remaining quantity in default UoM according to moves matched with this "
"operation. "
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location_removal_strategy_id
msgid "Removal Strategy"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:533
#, python-format
msgid "Removal strategy %s not implemented."
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_warehouse_2_stock_warehouse_orderpoint
#: model:ir.actions.act_window,name:stock.action_orderpoint_form
#: model:ir.actions.act_window,name:stock.product_open_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product_nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template_nbr_reordering_rules
#: model:ir.ui.menu,name:stock.menu_reordering_rules_config
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
msgid "Reports"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_res_model
msgid "Res Model"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Reserve"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_procurement_jit:0
msgid "Reserve products immediately after the sale order confirmation"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_procurement_jit:0
msgid "Reserve products manually or based on automatic scheduler"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Reserved"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_reserved_quant_id
msgid "Reserved Quant"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Reserved Quants"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_reservation_id
msgid "Reserved for Move"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_reserved_quant_ids
msgid "Reserved quants"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Reset Operations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Resolve Procurement Exceptions"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_resupply_from_wh
msgid "Resupply From Other Warehouses"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_resupply_route_ids
msgid "Resupply Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_resupply_wh_ids
msgid "Resupply Warehouses"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_location_id
msgid "Return Location"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:181
#, python-format
msgid "Returned Picking"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Reverse"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
msgid "Reverse Transfer"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_parent_right
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_parent_right
msgid "Right Parent"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_route_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Route"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_name
msgid "Route Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_route_sequence
#: model:ir.model.fields,field_description:stock.field_stock_location_path_route_sequence
msgid "Route Sequence"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.model.fields,field_description:stock.field_product_category_route_ids
#: model:ir.model.fields,field_description:stock.field_product_product_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template_route_ids
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_adv_location
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model:ir.ui.menu,name:stock.menu_stock_routes
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Routes Management"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them "
"on products and product categories"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Rules"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_procurement_compute
#: model:ir.ui.menu,name:stock.menu_procurement_compute
msgid "Run Reordering Rules"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_proc_schedulers
msgid "Run Schedulers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Sales Flow"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form_save
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_lot_form
msgid "Save"
msgstr "Guardar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_min_date
msgid "Scheduled Date"
msgstr "Fecha programada"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_date_expected
msgid "Scheduled date for the processing of this move"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_min_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_max_date
msgid "Scheduled time for the last part of the shipment to be processed"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_sched
msgid "Schedulers"
msgstr "Planificaciones"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_scrap_wizard
msgid "Scrap"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_scrap_wizard
msgid "Scrap Location"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.move_scrap
msgid "Scrap Move"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_move_scrap
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_scrap_wizard
msgid "Scrap Products"
msgstr "Productos de desecho"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_scrapped
#: model:stock.location,name:stock.stock_location_scrapped
msgid "Scrapped"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Search Inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
msgid "Search Stock Location Paths"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2927
#, python-format
msgid "Select products manually"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_sequence
#: model:ir.model.fields,field_description:stock.field_stock_location_route_sequence
#: model:ir.model.fields,field_description:stock.field_stock_move_sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Sequence"
msgstr "Secuencia"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_prod_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_name
#: model:res.request.link,name:stock.req_link_tracking
msgid "Serial Number"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Serial Number / Lots"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_prodlot_name
msgid "Serial Number Name"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
msgid "Serial Numbers / Lots"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_warehouse_id
msgid "Served Warehouse"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category_removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source "
"location for this product category"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_stock_calendar:0
msgid "Set lead times in calendar days (easy)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Set the <i>Real Quantity</i> for each Product and <i>Validate the Inventory</"
"i>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Set the products you want to track with lots or serial numbers by setting "
"the Tracking field on the product form"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Set to Draft"
msgstr "Cambiar a borrador"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_location_id
msgid ""
"Sets a location if you produce at a fixed location. This can be a partner "
"location if you subcontract the manufacturing operations."
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Settings"
msgstr "Configuración"

#. module: stock
#: model:stock.location,name:stock.stock_location_components
msgid "Shelf 1"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_14
msgid "Shelf 2"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_posy
msgid "Shelves (Y)"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3873
#, python-format
msgid "Ship Only"
msgstr ""

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid "Ship directly from stock (Ship only)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Shipping Connectors"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_code
msgid "Short Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_code
msgid "Short name used to identify your warehouse"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_string_availability_info
msgid "Show various information on stock availability for this move"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.location_refrigerator_small
msgid "Small Refrigerator"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_uom:0
msgid ""
"Some products may be sold/purchased in different units of measure (advanced)"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1580
#, python-format
msgid "Some products require lots, so you need to specify those first!"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Source"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_origin
#: model:ir.model.fields,field_description:stock.field_stock_picking_origin
msgid "Source Document"
msgstr "Documento origen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_location_from_id
#: model:ir.model.fields,field_description:stock.field_stock_move_location_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_location_id
msgid "Source Location"
msgstr "Ubicación origen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Source Location Zone"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_package_id
msgid "Source Package"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_location_src_id
msgid "Source location is action=move"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_pack_operation_lot
msgid "Specifies lot/serial number for pack operations that need it"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_lot_id
msgid ""
"Specify Lot/Serial Number to focus your inventory on a particular Lot/Serial "
"Number."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_partner_id
msgid "Specify Owner to focus your inventory on a particular Owner."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_package_id
msgid "Specify Pack to focus your inventory on a particular Pack."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_product_id
msgid "Specify Product to focus your inventory on a particular Product."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form_save
msgid "Split"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Start Inventory"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_state
#: model:ir.model.fields,field_description:stock.field_stock_inventory_state
#: model:ir.model.fields,field_description:stock.field_stock_move_state
#: model:ir.model.fields,field_description:stock.field_stock_picking_state
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "Estado"

#. module: stock
#: code:addons/stock/stock.py:3820
#: model:stock.location,name:stock.stock_location_shop0
#: model:stock.location,name:stock.stock_location_stock
#, python-format
msgid "Stock"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Stock Inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree
msgid "Stock Inventory Lines"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_level_forecast_report_product
#: model:ir.actions.act_window,name:stock.action_stock_level_forecast_report_template
msgid "Stock Level Forecast"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_graph
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_pivot
msgid "Stock Level forecast"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_move
msgid "Stock Move"
msgstr "Movimiento stock"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_move_form2
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_lines
#: model:ir.ui.menu,name:stock.menu_action_move_form2
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Stock Moves"
msgstr "Movimientos de stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.product_open_quants
#: model:ir.actions.act_window,name:stock.product_template_open_quants
msgid "Stock On Hand"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
msgid "Stock Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_picking_id
msgid "Stock Picking"
msgstr "Albarán de stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "Stockable"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:400
#, python-format
msgid "Stockable Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_supplied_wh_id
msgid "Supplied Warehouse"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_stock_dropshipping:0
msgid "Suppliers always deliver to your warehouse(s)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Supply Chain"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_procure_method
msgid "Supply Method"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_supplier_wh_id
msgid "Supplying Warehouse"
msgstr ""

#. module: stock
#: selection:procurement.rule,procure_method:0
msgid "Take From Stock"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_operation_link_reserved_quant_id
msgid ""
"Technical field containing the quant that created this link between an "
"operation and a stock move. Used at the stock_move_obj.action_done() time to "
"avoid seeking a matching quant again"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_warehouse_id
msgid ""
"Technical field depicting the warehouse to consider for the route selection "
"on the next procurement (if any)."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company_internal_transit_location_id
msgid ""
"Technical field used for resupply routes between warehouses that belong to "
"this company"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_restrict_lot_id
msgid ""
"Technical field used to depict a restriction on the lot of quants to "
"consider when marking this move as 'done'"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_restrict_partner_id
msgid ""
"Technical field used to depict a restriction on the ownership of quants to "
"consider when marking this move as 'done'"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_return_picking_move_dest_exists
msgid "Technical field used to hide help tooltip if not needed"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_negative_dest_location_id
msgid ""
"Technical field used to record the destination location of a move that "
"created a negative quant"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_price_unit
msgid ""
"Technical field used to record the product cost set by the user during a "
"picking confirmation (when costing method used is 'average price' or "
"'real'). Value given in company currency and in product uom."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_split_from
msgid ""
"Technical field used to track the origin of a split move, which can be "
"useful in case of debug"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_temando
msgid "Temando integration"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_product_tmpl_id
msgid "Template"
msgstr "Plantilla"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_auto
msgid ""
"The 'Automatic Move' / 'Manual Operation' value will create a stock move "
"after the current one.  With 'Automatic No Step Added', the location is "
"replaced in the original move."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "The RFQ becomes a Purchase Order and a Transfer Order is created"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_sale_delay
#: model:ir.model.fields,help:stock.field_product_template_sale_delay
msgid ""
"The average delay in days between the confirmation of the customer order and "
"the delivery of the finished products. It's the time you promise to your "
"customers."
msgstr ""

#. module: stock
#: sql_constraint:stock.location:0
msgid "The barcode for a location must be unique per company !"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4383
#, python-format
msgid ""
"The chosen quantity for product %s is not compatible with the UoM rounding. "
"It will be automatically converted at confirmation"
msgstr ""

#. module: stock
#: sql_constraint:stock.warehouse:0
msgid "The code of the warehouse must be unique per company!"
msgstr ""

#. module: stock
#: sql_constraint:stock.production.lot:0
msgid "The combination of serial number and product must be unique !"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_company_id
msgid "The company is automatically set from your user preferences."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_company_id
msgid "The company to which the quants belong"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_date
msgid ""
"The date that will be used for the stock level check of the products and the "
"validation of the stock move related to this inventory."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4006
#, python-format
msgid ""
"The default resupply warehouse should be different than the warehouse itself!"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1215
#, python-format
msgid ""
"The destination location must be the same for all the moves of the picking."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
msgid ""
"The following routes will apply to the products in this category taking into "
"account parent categories:"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_reservation_id
msgid "The move the quant is reserved for"
msgstr ""

#. module: stock
#: sql_constraint:stock.warehouse:0
msgid "The name of the warehouse must be unique per company!"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_propagated_from_id
msgid "The negative quant this is coming from"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_location_dest_id
msgid "The new location where the goods need to go"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package_parent_id
msgid "The package containing this item"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package_id
msgid "The package containing this quant"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "The picking type determines the picking view"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The picking type system allows you to assign each stock\n"
"                operation a specific type which will alter its views "
"accordingly.\n"
"                On the picking type you could e.g. specify if packing is "
"needed by default,\n"
"                if it should show the customer."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_qty_multiple
msgid ""
"The procurement quantity will be rounded up to this multiple.  If it is 0, "
"the exact quantity will be used.  "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_rule_id
msgid "The procurement rule that created this stock move"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_push_rule_id
msgid "The push rule that created this stock move"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4488
#, python-format
msgid "The quantity to split should be smaller than the quantity To Do.  "
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "The quotation becomes a Sale Order and a Transfer Order is created"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1910
#, python-format
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2703
#, python-format
msgid ""
"The roundings of your unit of measure %s on the move vs. %s on the product "
"don't allow to do these operations or you are not transferring the picking "
"at once. "
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4376
#, python-format
msgid ""
"The selected UoM for product %s is not compatible with the UoM set on the "
"product form. \n"
"Please choose an UoM within the same UoM category."
msgstr ""

#. module: stock
#: constraint:stock.inventory:0
msgid "The selected inventory options are not coherent."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:577
#, python-format
msgid "The serial number %s is already in stock."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1218
#, python-format
msgid "The source location must be the same for all the moves of the picking."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_picking_id
msgid "The stock operation where the packing has been made"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_warehouse_id
msgid "The warehouse this rule is for"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_theoretical_qty
msgid "Theoretical Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_tracking_lot
msgid ""
"This allows to manipulate packages.  You can put something in, take "
"something from a package, but also move entire packages and put them even in "
"another package.  "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_production_lot
msgid ""
"This allows you to assign a lot (or serial number) to the pickings and "
"moves.  This can make it possible to know which production lot was sent to a "
"certain client, ..."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_stock_calendar
msgid ""
"This allows you to handle minimum stock rules differently by the possibility "
"to take into account the purchase and delivery calendars \n"
"-This installs the module stock_calendar."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.quantsact
msgid ""
"This analysis gives you a fast overview on the current stock level of your "
"products and their current inventory value."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package_packaging_id
msgid ""
"This field should be completed only if everything inside the package share "
"the same product, otherwise it doesn't really makes sense."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"This guide helps getting started with Odoo Inventory.\n"
"                        Once you are done, you will benefit from:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"This is quite complex to set up, so <strong>contact your Project Manager</"
"strong> to address this."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_default_location_dest_id
msgid ""
"This is the default destination location when you create a picking manually "
"with this picking type. It is possible however to change it or that the "
"routes put another location. If it is empty, it will check for the customer "
"location on the partner. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_default_location_src_id
msgid ""
"This is the default source location when you create a picking manually with "
"this picking type. It is possible however to change it or that the routes "
"put another location. If it is empty, it will check for the supplier "
"location on the partner. "
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"This is the list of all the production lots you recorded. When\n"
"                you select a lot, you can get the traceability of the "
"products contained in lot."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_owner_id
msgid "This is the owner of the quant"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_picking_type_id
msgid "This is the picking type that will be put on the stock moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_uom_qty
msgid ""
"This is the quantity of products from an inventory point of view. For moves "
"in the state 'done', this is the quantity of products that were actually "
"moved. For other moves, this is the quantity of product that is planned to "
"be moved. Lowering this quantity does not generate a backorder. Changing "
"this quantity on assigned moves affects the product reservation, and should "
"be done with care."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_move_form2
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the "
"product\n"
"                to see all the past or future movements for the product."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_adv_location
msgid ""
"This option supplements the warehouse application by effectively "
"implementing Push and Pull inventory flows through Routes."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid ""
"This picking appears to be chained with another operation. Later, if you "
"receive the goods you are returning now, make sure to <b>reverse</b> the "
"returned picking in order to avoid logistic rules to be applied again (which "
"would create duplicated operations)"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty_new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_location_from_id
msgid ""
"This rule can be applied when a move is confirmed that has this location as "
"destination location"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner_property_stock_customer
msgid ""
"This stock location will be used, instead of the default one, as the "
"destination location for goods you send to this partner"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner_property_stock_supplier
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for goods you receive from the current partner"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_property_stock_production
#: model:ir.model.fields,help:stock.field_product_template_property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_property_stock_procurement
#: model:ir.model.fields,help:stock.field_product_template_property_stock_procurement
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by procurements."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template_property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_tracking_owner
msgid "This way you can receive products attributed to a certain owner. "
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_to_loc
msgid "To"
msgstr "Hasta"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_qty_todo
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_product_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "To Do"
msgstr "Por hacer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Receive"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To better organize your stock, you can create\n"
"                            subdivisions of your Warehouse called "
"<strong>Locations</strong> (ex:\n"
"                            Shipping area, Merchandise return, Shelf 34 "
"etc).\n"
"                            Do not use Locations if you do not manage "
"inventory per zone."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To check the trajectory of a lot, find it back in <strong><i>Inventory "
"Control &gt; Serial Numbers / lots</i></strong>.\n"
"                       Choose a lot in the list and click on "
"<i>Traceability</i>i&gt;. You may also\n"
"                       filter the Quantitative Valuation of a product with a "
"certain lot."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To create them, click on <strong><span class=\"fa fa-refresh\"/> Reordering</"
"strong> on"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To use more precise units like pounds or kilograms, activate<i> Some "
"products may be sold/purchased in different unit of measures (advanced)</i> "
"in the"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Today"
msgstr "Hoy"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category_total_route_ids
msgid "Total routes"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1773
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
#, python-format
msgid "Traceability"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_product_expiry
msgid ""
"Track different dates on products and serial numbers.\n"
"                    The following dates can be tracked:\n"
"                    - end of life\n"
"                    - best before date\n"
"                    - removal date\n"
"                    - alert date.\n"
"                    This installs the module product_expiry."
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_stock_production_lot:0
msgid "Track lots or serial numbers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_tracking
#: model:ir.model.fields,field_description:stock.field_product_template_tracking
msgid "Tracking"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2895
#: model:ir.model,name:stock.model_stock_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Transfer"
msgstr "Transferir"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_partner_id
msgid "Transfer Destination Address"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_id
msgid "Transfer Reference"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Transfers"
msgstr ""

#. module: stock
#: selection:stock.location,usage:0
msgid "Transit Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_recompute_pack_op
msgid ""
"True if reserved quants changed, which mean we might need to recompute the "
"package operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_code
msgid "Type of Operation"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_packaging_type_id
msgid "Type of packaging"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_ups
msgid "UPS integration"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_usps
msgid "USPS integration"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:731
#, python-format
msgid "Under no circumstances should you delete or change quants yourselves!"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot_name
msgid "Unique Serial Number"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_cost
msgid "Unit Cost"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Unit Of Measure"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_price_unit
msgid "Unit Price"
msgstr "Precio unidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_product_uom
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_product_uom_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_uom
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Units of Measure"
msgstr "Unidades de medida"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Units of Measures"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4236
#, python-format
msgid "Unknown Pack"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Unless you are starting a new business, you probably have a list of vendors "
"you would like to import."
msgstr ""

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid "Unload in input location then go to stock (2 steps)"
msgstr ""

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid ""
"Unload in input location, go through a quality control before being admitted "
"in stock (3 steps)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:319
#, python-format
msgid "Unplanned Qty"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unreserve"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_resupply_from_wh
msgid "Unused field"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "UoM"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Update Qty On Hand"
msgstr ""

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Urgent"
msgstr "Urgente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_use_existing_lots
msgid "Use Existing Lots"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_make_procurment_wizard
msgid ""
"Use this assistant to generate a procurement request for this\n"
"                        product. According to the product configuration, "
"this may\n"
"                        trigger a draft purchase order, a manufacturing "
"order or\n"
"                        a new task."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_user
msgid "User"
msgstr "Usuario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "VAT:"
msgstr "RIF"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "Validar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Validate Inventory"
msgstr ""

#. module: stock
#: selection:stock.inventory,state:0
msgid "Validated"
msgstr "Validado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_product_variant_count
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_product_variant_count
msgid "Variant Number"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner_property_stock_supplier
#: selection:stock.location,usage:0
msgid "Vendor Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_suppliers
#: selection:stock.picking,picking_type_code:0
#: selection:stock.picking.type,code:0
msgid "Vendors"
msgstr ""

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Very Urgent"
msgstr "Muy urgente"

#. module: stock
#: selection:stock.location,usage:0
msgid "View"
msgstr "Vista"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "View Contained Packages content"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_view_location_id
msgid "View Location"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_locations_virtual
msgid "Virtual Locations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Waiting"
msgstr "En espera"

#. module: stock
#: selection:stock.move,state:0
msgid "Waiting Another Move"
msgstr ""

#. module: stock
#: selection:stock.pack.operation,state:0 selection:stock.picking,state:0
msgid "Waiting Another Operation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.move,state:0 selection:stock.pack.operation,state:0
#: selection:stock.picking,state:0
msgid "Waiting Availability"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_make_procurement_warehouse_id
#: model:ir.model.fields,field_description:stock.field_procurement_order_warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_product_warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "Almacén"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_name
msgid "Warehouse Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_warehouse_id
msgid "Warehouse to consider for the route selection"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4043
#, python-format
msgid "Warehouse's Routes"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route_warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Warehouses"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_warehouse_and_location_usage_level
msgid "Warehouses and Locations usage level"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:340
#, python-format
msgid "Warning!"
msgstr "¡Aviso!"

#. module: stock
#: code:addons/stock/stock.py:4375
#, python-format
msgid "Warning: wrong UoM!"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4382
#, python-format
msgid "Warning: wrong quantity!"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"We handle the whole import process\n"
"                                        for you: simply send your Odoo "
"project\n"
"                                        manager a CSV file containing all "
"your\n"
"                                        data."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"We handle the whole import process\n"
"                                        for you: simply send your Odoo "
"project\n"
"                                        manager a CSV file containing all "
"your\n"
"                                        products."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "We hope this guide helped you implement Odoo Inventory."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4962
#, fuzzy, python-format
msgid "Weighted Product"
msgstr "Producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Welcome"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse.  This behaviour "
"can be overridden by the routes on the Product/Product Categories or by the "
"Preferred Routes on the Procurement"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form.  It will take priority over the Warehouse route. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_product_categ_selectable
msgid ""
"When checked, the route will be selectable on the Product Category.  It will "
"take priority over the Warehouse route. "
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "When everything is set, click on <i>Start Inventory</i>"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_putaway_fixed_location_ids
msgid ""
"When the method is fixed, this location will be used to store the products"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field, "
"Odoo generates a procurement to bring the forecasted quantity to the Max "
"Quantity."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity to the Quantity specified as "
"Max Quantity."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid ""
"When you select a serial number (lot), the quantity is corrected with "
"respect to\n"
"                            the quantity of that serial number (lot) and not "
"to the total quantity of the product."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_wizard_id
msgid "Wizard"
msgstr "Asistente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid ""
"Wizard checks all the stock minimum rules and generate procurement order."
msgstr ""
"El asistente comprobará todas las reglas de stock mínimo y generará orden de "
"abastecimiento."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                    your warehouses and that define the flows of your "
"products. These\n"
"                    routes can be assigned to a product, a product category "
"or be fixed\n"
"                    on procurement or sales order."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_form
msgid ""
"You can define your minimum stock rules, so that Odoo will automatically "
"create draft manufacturing orders or request for quotations according to the "
"stock level. Once the virtual stock of a product (= stock on hand minus all "
"confirmed orders and reservations) is below the minimum quantity, Odoo will "
"generate a procurement request to increase the stock up to the maximum "
"quantity."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "You can delete lines to ignore some products."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done_grouped
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid ""
"You can either do it immediately or mark it as Todo for future processing. "
"Use your scanner to validate the transferred quantity quicker."
msgstr ""

#. module: stock
#: code:addons/stock/product.py:519
#, python-format
msgid ""
"You can not change the unit of measure of a product that has already been "
"used in a done stock move. If you need to change the unit of measure, you "
"may deactivate this product."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4499
#, python-format
msgid "You can not delete pack operations of a done picking"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:385
#, python-format
msgid "You can not reserve a negative quantity or a negative quant."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2758
#, python-format
msgid "You can only delete draft moves."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "You can review and edit the predefined units via the"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2492
#, python-format
msgid "You cannot cancel a stock move that has been set to 'Done'."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3196
#, python-format
msgid ""
"You cannot have two inventory adjustements in state 'in Progess' with the "
"same product(%s), same location(%s), same package, same owner and same lot. "
"Please first validate the first inventory adjustement with this product "
"before creating another one."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:725
#, python-format
msgid "You cannot move to a location of type view %s."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:3006
#, python-format
msgid ""
"You cannot set a negative product quantity in an inventory line:\n"
"\t%s - qty: %s"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2832
#, python-format
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2828
#, python-format
msgid "You cannot split a move done"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"You do not have any products reserved for this picking.  Please click the "
"'Reserve' button\n"
"                                to check if products are available."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2654
#, python-format
msgid ""
"You have a difference between the quantity on the operation and the "
"quantities specified for the lots. "
msgstr ""

#. module: stock
#: sql_constraint:stock.pack.operation.lot:0
msgid "You have already mentioned this lot in another line"
msgstr ""

#. module: stock
#: sql_constraint:stock.pack.operation.lot:0
msgid "You have already mentioned this lot name in another line"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:132
#, python-format
msgid "You have manually created product lines, please delete them to proceed"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr ""

#. module: stock
#: code:addons/stock/product.py:341
#, python-format
msgid ""
"You have products in stock that have no lot number.  You can assign serial "
"numbers by doing an inventory.  "
msgstr ""

#. module: stock
#: constraint:stock.warehouse.orderpoint:0
msgid ""
"You have to select a product unit of measure in the same category than the "
"default unit of measure of the product"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid ""
"You haven't set processed quantities. If you click <i>apply</i>,\n"
"                        Odoo will process all quantities to do."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:48
#, python-format
msgid "You may only return one picking at a time!"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:58
#, python-format
msgid "You may only return pickings that are Done!"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2376 code:addons/stock/stock.py:4510
#, python-format
msgid "You need to provide a Lot/Serial Number for product %s"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:572
#, python-format
msgid "You should only receive by the piece with the same serial number"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:4514
#, python-format
msgid "You should provide a different serial number for each piece"
msgstr ""

#. module: stock
#: constraint:stock.move:0
msgid ""
"You try to move a product using a UoM that is not compatible with the UoM of "
"the product moved. Please use an UoM in the same UoM category."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Products"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Situation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Vendors"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Warehouse"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "_Apply"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "_Cancel"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "a stockable Product"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "and simply enter a minimum and maximum quantity."
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "barcode.rule"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "configuration menu"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form_stock_inherit
msgid "days"
msgstr "días"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "e.g. Annual inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"either by manually updating the Done quantity on the product lines, or let "
"Odoo do it automatically while validating"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"either by manually updating the Done quantity on the product lines, or scan "
"them with the Odoo Barcode app, or let Odoo do it automatically while "
"validating"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<EMAIL>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "for a customer and add products"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"for more\n"
"                        information."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "from your vendor with the products and the requested quantities"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"is displayed on the transfer if your products supply chain is properly "
"configured. Otherwise, <strong>Check the availability</strong> manually"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_origin_returned_move_id
msgid "move that created the return move"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_lot_form
msgid "of"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "on"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"on the purchase order form or click on <i>Receive Products</i> to see the "
"Transfer Order"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "on the sale order form to see Transfer Order"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_product_packaging
msgid "preferred Packaging"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_report_stock_forecast
msgid "report.stock.forecast"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_config_settings
msgid "stock.config.settings"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_fixed_putaway_strat
msgid "stock.fixed.putaway.strat"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "stock.return.picking.line"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "the list of products"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "the list of vendors"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "to mark the products as transferred to your stock location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_product_reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template_reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template_reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_stock_inventory_total_qty
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_operation_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_plus_visible
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lots_visible
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_picking_destination_location_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_picking_source_location_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_state
#: model:ir.model.fields,field_description:stock.field_stock_picking_picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_backorders
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_draft
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_late
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_ready
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_waiting
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_rate_picking_backorders
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_rate_picking_late
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_original_location_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_parent_location_id
msgid "unknown"
msgstr "desconocido"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "via the"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "when you receive the ordered products"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "with the <i>Validate</i> button"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "↳Put in Pack"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "⇒ Set quantities to 0"
msgstr ""

#~ msgid "Delivery Method"
#~ msgstr "Método de envío"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Suppliers"
#~ msgstr "Proveedores"
