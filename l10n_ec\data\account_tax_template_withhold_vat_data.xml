<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- 
        PURCHASE WITHHOLDS COMPUTED OVER VAT *RETENCIONES IVA COMPRAS
        -->
        <record id="tax_withhold_vat_10" model="account.tax.template">
            <field name="name">Retencion iva 10%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">50</field>
            <field name="amount">-10.0</field>
            <field name="description">RET IVA 10%</field>
            <field name="l10n_ec_code_applied">721</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_10'),
                    'plus_report_line_ids': [ref('tax_report_line_104_721')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_10'),
                    'minus_report_line_ids': [ref('tax_report_line_104_721')],
                }),]"/>
        </record>
        <record id="tax_withhold_vat_20" model="account.tax.template">
            <field name="name">Retencion iva 20%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">50</field>
            <field name="amount">-20.0</field>
            <field name="description">RET IVA 20%</field>
            <field name="l10n_ec_code_applied">723</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_20'),
                    'plus_report_line_ids': [ref('tax_report_line_104_723')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_20'),
                    'minus_report_line_ids': [ref('tax_report_line_104_723')],
                }),]"/>
        </record>
        <record id="tax_withhold_vat_30" model="account.tax.template">
            <field name="name">Retencion iva 30%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">50</field>
            <field name="amount">-30.0</field>
            <field name="description">RET IVA 30%</field>
            <field name="l10n_ec_code_applied">725</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_30'),
                    'plus_report_line_ids': [ref('tax_report_line_104_725')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_30'),
                    'minus_report_line_ids': [ref('tax_report_line_104_725')],
                }),]"/>
        </record>
        <record id="tax_withhold_vat_50" model="account.tax.template">
            <field name="name">Retencion iva 50%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">50</field>
            <field name="amount">-50.0</field>
            <field name="description">RET IVA 50%</field>
            <field name="l10n_ec_code_applied">727</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_50'),
                    'plus_report_line_ids': [ref('tax_report_line_104_727')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_50'),
                    'minus_report_line_ids': [ref('tax_report_line_104_727')],
                }),]"/>
        </record>
        <record id="tax_withhold_vat_70" model="account.tax.template">
            <field name="name">Retencion iva 70%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">50</field>
            <field name="amount">-70.0</field>
            <field name="description">RET IVA 70%</field>
            <field name="l10n_ec_code_applied">729</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_70'),
                    'plus_report_line_ids': [ref('tax_report_line_104_729')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_70'),
                    'minus_report_line_ids': [ref('tax_report_line_104_729')],
                }),]"/>
        </record>
        <record id="tax_withhold_vat_100" model="account.tax.template">
            <field name="name">Retencion iva 100%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">50</field>
            <field name="amount">-100.0</field>
            <field name="description">RET IVA 100%</field>
            <field name="l10n_ec_code_applied">731</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_100'),
                    'plus_report_line_ids': [ref('tax_report_line_104_731')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_vat_withhold_100'),
                    'minus_report_line_ids': [ref('tax_report_line_104_731')],
                }),]"/>
        </record>
        <!-- 
        SALES WITHHOLDS COMPUTED OVER VAT *RETENCIONES IVA VENTAS
        -->
        <record id="tax_sale_withhold_vat_10" model="account.tax.template">
            <field name="name">Retencion iva 10%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">60</field>
            <field name="amount">-10.0</field>
            <field name="description">RET IVA 10%</field>
            <field name="l10n_ec_code_applied">609</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'plus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'minus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
        </record>
        <record id="tax_sale_withhold_vat_20" model="account.tax.template">
            <field name="name">Retencion iva 20%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">60</field>
            <field name="amount">-20.0</field>
            <field name="description">RET IVA 20%</field>
            <field name="l10n_ec_code_applied">609</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'plus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'minus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
        </record>
        <record id="tax_sale_withhold_vat_30" model="account.tax.template">
            <field name="name">Retencion iva 30%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">60</field>
            <field name="amount">-30.0</field>
            <field name="description">RET IVA 30%</field>
            <field name="l10n_ec_code_applied">609</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'plus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'minus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
        </record>
        <record id="tax_sale_withhold_vat_50" model="account.tax.template">
            <field name="name">Retencion iva 50%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">60</field>
            <field name="amount">-50.0</field>
            <field name="description">RET IVA 50%</field>
            <field name="l10n_ec_code_applied">609</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'plus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'minus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
        </record>
        <record id="tax_sale_withhold_vat_70" model="account.tax.template">
            <field name="name">Retencion iva 70%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">60</field>
            <field name="amount">-70.0</field>
            <field name="description">RET IVA 70%</field>
            <field name="l10n_ec_code_applied">609</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'plus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'minus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
        </record>
        <record id="tax_sale_withhold_vat_100" model="account.tax.template">
            <field name="name">Retencion iva 100%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">60</field>
            <field name="amount">-100.0</field>
            <field name="description">RET IVA 100%</field>
            <field name="l10n_ec_code_applied">609</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_vat"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'plus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 12,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_vat_outstanding_withholds'),
                    'minus_report_line_ids': [ref('tax_report_line_104_609')],
                }),]"/>
        </record>
    </data>
</odoo>
