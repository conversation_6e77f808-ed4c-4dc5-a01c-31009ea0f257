// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_AttachmentViewer {
    display: flex;
    width: map-get($sizes, 100);
    height: map-get($sizes, 100);
    flex-flow: column;
    align-items: center;
    z-index: -1;
}

.o_AttachmentViewer_buttonNavigation {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    top: 50%;
    transform: translateY(-50%);
}

.o_AttachmentViewer_buttonNavigationNext {
    right: 15px;

    > .fa {
        margin: 1px 0 0 1px; // not correctly centered for some reasons
    }
}

.o_AttachmentViewer_buttonNavigationPrevious {
    left: 15px;

    > .fa {
        margin: 1px 1px 0 0; // not correctly centered for some reasons
    }
}

.o_AttachmentViewer_header {
    display: flex;
    height: $o-navbar-height;
    width: 100%;
}

.o_AttachmentViewer_headerItem {
    display: flex;
    align-items: center;
}

.o_AttachmentViewer_headerItemButton {
    padding: 0 map-get($spacers, 3);
}

.o_AttachmentViewer_headerItemButtonIcon.o-hasLabel {
    margin-right: map-get($spacers, 2);
}

.o_AttachmentViewer_icon {
    margin-inline-start: map-get($spacers, 4);
    margin-inline-end: map-get($spacers, 2);
}

.o_AttachmentViewer_loading {
    position: absolute;
}

.o_AttachmentViewer_main {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: -1;
    padding: 45px 0;

    &.o_with_img {
        overflow: hidden;
    }
}

.o_AttachmentViewer_name {
    margin: 0 map-get($spacers, 2);
    min-width: 0;
}

.o_AttachmentViewer_toolbar {
    position: absolute;
    bottom: 45px;
    transform: translateY(100%);
    display: flex;
}

.o_AttachmentViewer_toolbarButton {
    padding: 8px;
}

.o_AttachmentViewer_viewImage {
    max-height: 100%;
    max-width: 100%;
}

.o_AttachmentViewer_viewIframe {
    width: 90%;
    height: 100%;

    &.o-isMobile {
        width: map-get($sizes, 100);
    }
}

.o_AttachmentViewer_viewVideo {
    width: 75%;
    height: 75%;

    &.o-isMobile {
        width: map-get($sizes, 100);
        height: map-get($sizes, 100);
    }
}

.o_AttachmentViewer_zoomer {
    position: absolute;
    padding: 45px 0;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_AttachmentViewer {
    outline: none;
}

.o_AttachmentViewer_buttonNavigation {
    color: gray('400');
    background-color: lighten($black, 15%);
    border-radius: 100%;
    cursor: pointer;

    &:hover {
        color: lighten(gray('400'), 15%);
        background-color: $black;
    }
}

.o_AttachmentViewer_header {
    background-color: rgba($black, 0.7);
    color: gray('400');
}

.o_AttachmentViewer_headerItemButton {
    cursor: pointer;

    &:hover {
        background-color: rgba($white, 0.1);
        color: lighten(gray('400'), 15%);
    }
}

.o_AttachmentViewer_headerItemButtonClose {
    cursor: pointer;
    font-size: 1.3rem;
}

.o_AttachmentViewer_toolbar {
    cursor: pointer;
}

.o_AttachmentViewer_toolbarButton {
    background-color: lighten($black, 15%);

    &.o_disabled {
        cursor: not-allowed;
        filter: brightness(1.3);
    }

    &:not(.o_disabled) {
        color: gray('400');
        cursor: pointer;

        &:hover {
            background-color: $black;
            color: lighten(gray('400'), 15%);
        }
    }
}

.o_AttachmentViewer_view {
    background-color: $black;
    box-shadow: 0 0 40px $black;
    outline: none;
    border: none;

    &.o_text {
        background-color: $white;
    }
}

// ------------------------------------------------------------------
// Animation
// ------------------------------------------------------------------

.o_AttachmentViewer_viewImage {
    transition: transform 0.3s ease;
}
