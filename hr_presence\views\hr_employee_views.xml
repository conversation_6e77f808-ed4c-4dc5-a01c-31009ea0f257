<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_employee_view_search" model="ir.ui.view">
        <field name="name">hr.employee.view.search</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="groups_id" eval="[(4, ref('hr.group_hr_manager'))]"/>
        <field name="arch" type="xml">
            <filter name="group_manager" position="after">
                <filter name="group_hr_presence_state" string="Presence" domain="[]" context="{'group_by':'hr_presence_state_display'}"/>
            </filter>
        </field>
    </record>

    <record id="hr_employee_view_presence_search" model="ir.ui.view">
        <field name="name">hr.employee.view.search.presence</field>
        <field name="model">hr.employee</field>
        <field name="arch" type="xml">
            <search string="Employees">
                <filter name="group_hr_presence_state" string="Presence" domain="[]" context="{'group_by':'hr_presence_state_display'}"/>
                <searchpanel>
                    <field name="company_id" groups="base.group_multi_company" icon="fa-building"/>
                    <field name="hr_presence_state_display" string="Absence/Presence" />
                    <field name="department_id" icon="fa-users"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="hr_employee_view_kanban" model="ir.ui.view">
        <field name="name">hr.employee.view.kanban</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.hr_kanban_view_employees"/>
        <field name="groups_id" eval="[(4, ref('hr.group_hr_manager'))]"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//kanban" position="attributes">
                <attribute name="create">0</attribute>
            </xpath>
            <xpath expr="//div[hasclass('oe_kanban_details')]" position="inside">
                <div class="oe_kanban_content">
                    <div class="oe_kanban_row">
                        <field name="hr_presence_state" invisible="1"/>
                        <button name="action_set_present" type="object" class="btn btn-primary btn-sm mt8 fa fa-check" title="Set as present" attrs="{'invisible': [('hr_presence_state', '=', 'present')]}"> </button>
                        <button name="action_set_absent" type="object" class="btn btn-primary btn-sm mt8 fa fa-times" title="Set as absent" attrs="{'invisible': [('hr_presence_state', '!=', 'present')]}"> </button>
                        <button name="action_open_leave_request" type="object" class="btn btn-primary btn-sm mt8">Time Off</button>
                    </div>
                    <div class="oe_kanban_row">
                        <button name="action_send_sms" type="object" class="btn btn-primary btn-sm mt8">SMS</button>
                        <button name="action_send_mail" type="object" class="btn btn-primary btn-sm mt8">Log</button>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
