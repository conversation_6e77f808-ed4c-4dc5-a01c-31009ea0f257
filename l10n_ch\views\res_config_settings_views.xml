<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.l10n.ch</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="account.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@id='invoicing_settings']" position="inside">
                    <div class="col-12 col-lg-6 o_setting_box" id="l10n_ch-isr_print_bank" attrs="{'invisible': [('country_code', '!=', 'CH')]}">
                        <div class="o_setting_left_pane">
                            <field name="l10n_ch_isr_print_bank_location"/>
                        </div>
                        <div class="o_setting_right_pane">
                            <label for="l10n_ch_isr_print_bank_location"/>
                            <div class="text-muted">
                                Print the coordinates of your bank under the &apos;Payment for&apos; title of the ISR.
                                Your address will be moved to the &apos;in favour of&apos; section.
                            </div>
                            <div class="content-group" attrs="{'invisible': [('l10n_ch_isr_print_bank_location', '=', False)]}">
                                <div class="row mt16">
                                    <label for="l10n_ch_isr_preprinted_bank" class="col-lg-4 o_light_label"/>
                                    <field name="l10n_ch_isr_preprinted_bank"/>
                                </div>
                                <div class="row">
                                    <label for="l10n_ch_isr_preprinted_account" class="col-lg-4 o_light_label"/>
                                    <field name="l10n_ch_isr_preprinted_account"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-6 o_setting_box" id="l10n_ch-isr_print_scanline_offset" attrs="{'invisible': [('country_code', '!=', 'CH')]}">
                        <div class="o_setting_left_pane"/>
                        <div class="o_setting_right_pane">
                            <span class="o_form_label">ISR scan line offset</span>
                            <div class="text-muted">
                                Offset to move the scan line in mm
                            </div>
                            <div class="content-group">
                                <div class="row mt16">
                                    <label for="l10n_ch_isr_scan_line_top" class="col-lg-4 o_light_label"/>
                                    <field name="l10n_ch_isr_scan_line_top"/>
                                </div>
                                <div class="row">
                                    <label for="l10n_ch_isr_scan_line_left" class="col-lg-4 o_light_label"/>
                                    <field name="l10n_ch_isr_scan_line_left"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
