<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_lu" model="res.partner">
        <field name="name">LU Company</field>
        <field name="vat">LU75425064</field>
        <field name="street">A</field>
        <field name="city">Clervaux</field>
        <field name="country_id" ref="base.lu"/>
        
        <field name="zip">9839</field>
        <field name="phone">+*********** 456</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.luexample.com</field>
    </record>

    <record id="demo_company_lu" model="res.company">
        <field name="name">LU Company</field>
        <field name="partner_id" ref="partner_demo_company_lu"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_lu')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_lu.demo_company_lu'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_lu.lu_2011_chart_1')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_lu.demo_company_lu')"/>
    </function>
</odoo>
