<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="payment.payment_acquirer_mollie" model="payment.acquirer">
        <field name="provider">mollie</field>
        <field name="redirect_form_view_id" ref="redirect_form"/>
        <field name="support_authorization">False</field>
        <field name="support_fees_computation">False</field>
        <field name="support_refund"></field>
        <field name="support_tokenization">False</field>
    </record>

    <record id="payment_method_mollie" model="account.payment.method">
        <field name="name"><PERSON>llie</field>
        <field name="code">mollie</field>
        <field name="payment_type">inbound</field>
    </record>

</odoo>
