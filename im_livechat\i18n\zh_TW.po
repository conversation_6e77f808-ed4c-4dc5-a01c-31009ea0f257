# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 01:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "# Messages"
msgstr "# 訊息"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "評分數目"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "# 聊天視窗"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "講解人"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% 高興"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr "% 滿意度"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s 和%s 在打字..."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s 正在輸入..."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s 及更多人在打字..."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Display the button' displays the chat button on the pages.\n"
"* 'Auto popup' displays the button and automatically open the conversation pane.\n"
"* 'Hide the button' hides the chat button on the pages."
msgstr ""
"*「顯示按鈕」在頁面上顯示聊天按鈕。\n"
"*「自動彈出」顯示按鈕並自動打開對話窗口。\n"
"*「隱藏按鈕」在頁面上隱藏聊天按鈕。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ",在"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- 顯示更舊的消息 --------"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""
"<i class=\"fa fa-smile-o text-success\" title=\"滿意評分百分比\" role=\"img\" aria-"
"label=\"開心笑臉\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"<span class=\"text-muted\">Define rules for your live support channel. You "
"can apply an action for the given URL, and per country.<br/>To identify the "
"country, GeoIP must be installed on your server, otherwise, the countries of"
" the rule will not be taken into account.</span>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr "<span style=\"font-size: 10px;\">線上客服對話串</span><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr "<span>順祝 安康</span><br/><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr "<span>您好</span><br/>以下為您與本公司之討論過程"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""
"如果操作員沒有應答，會話將無人應答。\n"
"                                       如果訪問者也是操作員，則會話將始終得到應答。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Action"
msgstr "動作"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "管理員"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Anonymous"
msgstr "匿名"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__anonymous_name
msgid "Anonymous Name"
msgstr "匿名使用者姓名"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "您是否在矩陣中？"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Ask something ..."
msgstr "要求點什麼..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Attendees"
msgstr "參加者"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Auto popup"
msgstr "自動彈出"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Auto popup timer"
msgstr "自動彈出計時器"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Avatar"
msgstr "頭像"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "平均時長"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "平均消息"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr "首度回應訪客的平均時間，以秒計"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "首次回覆訪客的平均時間"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bad"
msgstr "糟糕"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bounced"
msgstr "被退回"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "按鈕背景顏色"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "按鈕文字顏色"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Canceled"
msgstr "已取消"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_canned_response_action
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "預設回應"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""
"預設回覆允許您通過輸入 <i>：快捷方式</i>在\n"
"消息中插入預先寫好的回覆。快捷方式在消息中被\n"
"直接替換，這樣您將仍能在消息發送之前進行編輯\n"
"。"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Changed"
msgstr "已修改"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Channel"
msgstr "群組"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr "頻道標題顏色"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "頻道名稱"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "頻道規則"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "頻道規則"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "群組類型"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
msgid "Channels"
msgstr "頻道"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "聊天輸入預留位置"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr "2 人之間的聊天是私密且獨特的。群組在受邀者之間是私人的。頻道可以自由加入（取決於其配置）。"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Chat with one of our collaborators"
msgstr "與我們其中一個合作者聊天"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close"
msgstr "關閉"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close chat window"
msgstr "關閉聊天視窗"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close conversation"
msgstr "關閉視窗"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "代號"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "配置"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "對話"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Conversation Sent"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr "使用 %s 的對話"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr "對話已處理"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr "將此代碼複製並粘貼到您的網站的 &lt;head&gt; 標籤內："

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__country_id
msgid "Country"
msgstr "國家"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "遊客所在國家/地區"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "此頻道訪客的國家/地區"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr "建立一個頻道，開始聊天就可以生成交談記錄。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid "Create a new canned response"
msgstr "建立罐頭回應"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "創立者"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "建立於"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation Date"
msgstr "建立日期"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "建立日期"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "建立日期(小時)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "客戶點評"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "日子數字"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day number of the session (1 is Monday, 7 is Sunday)"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "活動天數"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "即時聊天按鈕的預設背景顏色"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr "開啟後，頻道標題的預設背景顏色"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "即時聊天按鈕的預設文字顏色"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "線上客服按鈕上顯示的預設內容"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr "開啟後，頻道的預設標題顏色"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "定義一個新的線上客服頻道網站"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Auto popup' otherwise this parameter will not be "
"taken into account."
msgstr "自動打開對話窗口的延遲（以秒計）。注意：所選操作必須為「自動彈出」，否則將不會考慮此參數。"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Delete"
msgstr "刪除"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Did we correctly answer your question ?"
msgstr "我們是否正確回答了您的問題?"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "摘要"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr "討論群組"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Display the button"
msgstr "顯示按鈕"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Document not downloadable"
msgstr "文件無法下載"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Download"
msgstr "下載"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "對話持續時間(秒)"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Error"
msgstr "錯誤"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Explain your note"
msgstr "解釋您的備註"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr "對於用Odoo CMS建立的網站，進入網站 &gt; 配置 &gt; 設定，選擇您想添加到網站的網站即時聊天頻道。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr "幫助訂單找到匹配規則。如果2條規則同時匹配給定的網址l/國家，會選擇低序列的一個。"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Good"
msgstr "好"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Group By..."
msgstr "分組依據..."

#. module: im_livechat
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
#, python-format
msgid "Have a Question? Chat with us."
msgstr "有問題？請與我們聯絡。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr "頁首背景顏色"

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr "你好！我可以如何幫助你？"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide the button"
msgstr "隱藏按鈕"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "History"
msgstr "歷史"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "對話視窗開始的小時數"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "How may I help you?"
msgstr "請問有什麼可以幫到您？"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "如何使用網站線上交談掛件?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Idle"
msgstr "閒置"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
#, python-format
msgid "Image"
msgstr "圖像"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Invalid email address"
msgstr "無效的電子郵件地址"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr "類別實時聊天是否開放"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "線上討論是否正在進行？"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "訪客是否匿名"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_3
msgid "Joel Willis, Marc Demo"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_2
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_6
msgid "Joel Willis, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "加入"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "加入頻道"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr "KPI 實時聊天對話值"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr "KPI 實時聊天評價值"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr "KPI 實時聊天回應值"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "過去24小時"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "退出"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "離開頻道"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "頻道的收聽者"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "線上客服"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "實時聊天頻道搜尋"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/components/discuss/discuss.js:0
#: code:addons/im_livechat/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr "線上客服"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr "實時聊天按鈕"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "實時聊天按鈕顏色"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "線上客服頻道"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "線上客服頻道規則"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__mail_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "線上對話"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_mail_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "實時聊天類型的頻道需要有實時聊天操作員識別碼。"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "線上客服支援頻道報告"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audiance, the duration of a session, etc."
msgstr "線上客服支援頻道統計允許您輕鬆檢查和分析公司對話視窗的表現。擷取關於錯過的對話視窗、觀眾、對話視窗持續時間等的資訊"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "線上客服支援客服人員報告"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "線上客服支援統計"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
msgid "Livechat Username"
msgstr "實時聊天用戶名"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr "實時聊天視窗"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_active
msgid "Livechat session is active until visitor leave the conversation."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading"
msgstr "正在上載"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading older messages..."
msgstr "正在加載歷史消息"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Read"
msgstr "標記為已讀"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "標記為待辦"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as todo"
msgstr "標記為待辦"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "匹配的訂單"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
msgid "Message"
msgstr "訊息"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "錯過的對話視窗"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
msgid "Name"
msgstr "名稱"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "New messages"
msgstr "新消息"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Next"
msgstr "下一個"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr "沒有可用的協作者，請稍後再試。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr "在線上客服對話視窗中還沒有客戶評分"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "暫無資料！"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "No history found"
msgstr "未找到歷史記錄"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Note by"
msgstr "備註由"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "Number of conversation"
msgstr "對話次數"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "自操作員的第一次對話以來的天數"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "其他發言人數"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "對話中的消息數量"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Offline"
msgstr "離線"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Online"
msgstr "線上"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "線上聊天名稱"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Oops! Something went wrong."
msgstr "哎呀！出了一點問題~"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_operator_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "客服人員"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "客服人員分析"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_operator_id
msgid "Operator for this specific channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "客服人員"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"Operators\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"
msgstr ""
"客服人員\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr "在 Odoo 中未顯示任何活動超過 30 分鐘的操作人員將被視為已斷開連接。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "選項"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "PDF file"
msgstr "PDF"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "快樂百分比評分"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please check your internet connection."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait"
msgstr "請稍候"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait..."
msgstr "請稍候..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "官方技術支援"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Previous"
msgstr "前一個"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Print"
msgstr "列印"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_form_livechat
msgid "Rating"
msgstr "點評"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_avg
msgid "Rating Average"
msgstr "平均評分"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "最新回饋評分"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_image
msgid "Rating Last Image"
msgstr "最新評分圖像"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_value
msgid "Rating Last Value"
msgstr "最新評分值"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "評級滿意度"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_count
msgid "Rating count"
msgstr "評分數"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Rating: %s"
msgstr "評級: %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
msgid "Ratings"
msgstr "點評"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "線上客服頻道的評價"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Ready"
msgstr "準備好"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "評分的理由"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Receive a copy of this conversation"
msgstr "若您欲取得本次對話副本，請填入您的email"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by Everyone"
msgstr "每個人都收到的"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by:"
msgstr "收件人:"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr "指定此規則將在網頁上應用的正則表達式。"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reply"
msgstr "回覆"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "報表"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reset Zoom"
msgstr "重置縮放"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Reset to default colors"
msgstr "重設為預設顏色"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Rotate"
msgstr "旋轉"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "規則"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "滿意率"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "儲存您的頻道以獲得您的配置小工具。"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Say something"
msgstr "說些什麼..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "腳本（外部）"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Search history"
msgstr "搜尋歷史"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "搜尋報表"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr "查看最近訪問的 15 個頁面"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by Everyone"
msgstr "所有人都看到了"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by:"
msgstr "通過："

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Send"
msgstr "發送"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Sent"
msgstr "已發送"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Session Date"
msgstr "對話視窗日期"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Session Form"
msgstr "對話視窗表格"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "對話統計"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Session expired... Please refresh and try again."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "未評分對話"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "沒有回應的對話"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "對話視窗"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "對話視窗歷程"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "對話視窗的開始日期"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "對話時段開始時間"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start date of the conversation"
msgstr "對話窗口的開始日期"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start hour of the conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "按鈕的文本"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "提示使用者發起聊天的文本。"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Thank you for your feedback"
msgstr "感謝您對本次客服的評分回饋"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "規則的頻道"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__name
msgid "The name of the channel"
msgstr "頻道名稱"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide Button', "
"the chat button will be hidden on the specified URL from the visitors "
"located in these 2 countries. This feature requires GeoIP installed on your "
"server."
msgstr ""
"該規則將僅適用於這些國家/地區。示例：如果您選擇「比利時」和「美國」，然後將操作設定為「隱藏按鈕」，將會對位於這兩個國家/地區的訪客的指定 URL "
"隱藏聊天按鈕。此功能的使用需要在伺服器上安裝 GeoIP。"

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "允許使用者刪除服務支援頻道。"

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "使用者能夠加入支援的頻道。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "這個頻道目前沒有評分"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "本周"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr "這是一個自動的『歡迎』訊息，當初始化新的對話窗口時訪問者會看到。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr "這個用戶名將被用作你在實時聊天頻道中的名字。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "是時候回答了"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr "回應時間（秒）"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer the user in second."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer(sec)"
msgstr ""

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr "提示：使用預設回覆，聊天速度更快"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr "標題顏色"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Today"
msgstr "今天"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "已處理的對話視窗"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Try again"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Type <b>:shortcut</b> to insert a canned response in your message.<br>"
msgstr "輸入 <b>:shortcut</b> 以插入預設回應至你的訊息中。<br>"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "網址的正則表達式"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr "您的客戶可以在靜態網頁的網址和頻道操作者進行討論。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Undefined"
msgstr "未定義的"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploaded"
msgstr "已上傳"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploading"
msgstr "上傳中"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr "使用預製回覆來定義實時聊天應用程式中的訊息範本。要載入預設回覆，請以「:」開始您的句子並選擇範本。"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "使用者"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr "用戶實時聊天用戶名"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "使用者設定"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is idle"
msgstr "使用者待機"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is offline"
msgstr "使用者離線"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is online"
msgstr "使用者上線"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
msgid "Users"
msgstr "使用者"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Video"
msgstr "影片"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Viewer"
msgstr "查看者"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Visitor"
msgstr "訪問者"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_0
msgid "Visitor #234, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_1
msgid "Visitor #323, Marc Demo"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_4
msgid "Visitor #532, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_5
msgid "Visitor #649, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_7
msgid "Visitor #722, Marc Demo"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Visitor has left the conversation."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr "訪客很滿意"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.mail_channel_livechat_1
msgid "Visitor, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "網頁"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "網站線上客服頻道"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "歡迎訊息"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "小工具"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Yesterday"
msgstr "昨天"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""
"您可以為每個想要\n"
" 整合網站線上客服小工具的網站建立頻道，這樣您的網站訪客便能夠與操作人員及時交流。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Your chatter history is empty"
msgstr "您的聊天歷史是空的"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom In"
msgstr "放大"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom Out"
msgstr "縮小"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr "例：/contactus"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "例如：您好！請問有什麼可以協助您的嗎？"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "例如：您的網站地址 "

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "from"
msgstr "從"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "on"
msgstr "在"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr "或者拷貝此網址並且通過信件發送給您的客戶、供應商："

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read less"
msgstr "更少"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read more"
msgstr "閱讀全文"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "秒"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "unnamed"
msgstr "未命名"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr "{{author_name}}"
