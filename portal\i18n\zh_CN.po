# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* portal
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 深圳市耀影科技有限公司_QQ1006399173, 2021
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON> (sile), 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:55+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON> (si<PERSON>), 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:0
#, python-format
msgid "%1d days overdue"
msgstr "逾期%1d天"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "%s is not the reference of a report"
msgstr "%s 不是报告的编码"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "<i class=\"fa fa-arrow-right mr-1\"/>Back to edit mode"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>返回编辑模式"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid ""
"<i class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr "<i class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"前一\" title=\"前一\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid "<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr "<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"下一\" title=\"下一\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "<i class=\"fa fa-pencil mx-1\"/>Edit Security Settings"
msgstr "个性账号设置"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr "<i class=\"fa fa-pencil\"/> 编辑"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">国家/地区...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "<option value=\"\">select...</option>"
msgstr "<option value=\"\">选择</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. <br/>Please"
" contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-"
"muted\">一旦为您的账户签发了文件，就不允许更改公司名称或增值税号码。<br/>，请直接与我们联系这项操作。</small>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Filter By:</span>"
msgstr "<span class=\"small mr-1 navbar-text\">过滤：</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Group By:</span>"
msgstr "<span class=\"small mr-1 navbar-text\">分组：</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Sort By:</span>"
msgstr "<span class=\"small mr-1 navbar-text\">排序：</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "<strong>Open </strong>"
msgstr "<strong>打开 </strong>"

#. module: portal
#: model:mail.template,body_html:portal.mail_template_data_portal_welcome
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.user_id.name or ''\">Marc Demo</t>,<br/> <br/>\n"
"                        Welcome to <t t-out=\"object.user_id.company_id.name\">YourCompany</t>'s Portal!<br/><br/>\n"
"                        An account has been created for you with the following login: <t t-out=\"object.user_id.login\">demo</t><br/><br/>\n"
"                        Click on the button below to pick a password and activate your account.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.signup_url\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Activate Account</strong>\n"
"                            </a>\n"
"                            <a href=\"/web/login\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px;\">\n"
"                                <strong>Log in</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or ''\">Welcome to our company's portal.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">您的账户</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">马克演示</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        亲爱的<t t-out=\"object.user_id.name or ''\">Marc Demo</t>。<br/> <br/>\n"
"                        欢迎来到<t t-out=\"object.user_id.company_id.name\">YourCompany</t>'s Portal!<br/><br/>\n"
"                        我们已经为您创建了一个账户，登录名如下：<t t-out=\"object.user_id.login\">Demo</t><br/><br/>\n"
"                        请点击下面的按钮，选择密码并激活您的账户。\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.signup_url\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>激活账户</strong>\n"
"                            </a>\n"
"                            <a href=\"/web/login\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px;\">\n"
"                                <strong>登录</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                       <t t-out=\"object.wizard_id.welcome_message or ''\">欢迎来到我们公司的门户网站。</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">您的公司</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        |<a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        |<a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        由<a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>提供技术提供\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "API Key Ready"
msgstr "API 密钥就绪"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_signature.js:0
#, python-format
msgid "Accept & Sign"
msgstr "同意 & 签名"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_warning
#: model:ir.model.fields,field_description:portal.field_portal_share__access_warning
msgid "Access warning"
msgstr "访问警告"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "Account Security"
msgstr "帐户安全"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add a note"
msgstr "新建笔记"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Add attachment"
msgstr "添加附件"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add contacts to share the document..."
msgstr "添加联系人来共享此文档…"

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_share__note
msgid "Add extra content to display in the email"
msgstr "添加额外内容以显示在EMail中"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Added On"
msgstr "已添加"

#. module: portal
#: code:addons/portal/controllers/mail.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "必须为每个附件提供一个访问令牌。"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Avatar"
msgstr "形象"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Change Password"
msgstr "更改密码"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
#, python-format
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr "一旦您的账户开具发票，不可更改增值税号码。如需要此操作，请直接与我们联系。"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr "为您的帐户签发文件后，不允许更改公司名称。请直接联系我们进行此操作。"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Check failed"
msgstr "检查失败"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "City"
msgstr "城市"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:0
#, python-format
msgid "Click here to see your document."
msgstr "单击此处查看您的文档。"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
#, python-format
msgid "Close"
msgstr "关闭"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Company Name"
msgstr "公司名称"

#. module: portal
#: model:ir.model,name:portal.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Confirm"
msgstr "确认"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"Confirm\n"
"                                <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""
"确认\n"
"<span class=\"fa fa-long-arrow-right\"/>"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Confirm Password"
msgstr "确认密码"

#. module: portal
#: model:ir.model,name:portal.model_res_partner
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__partner_id
msgid "Contact"
msgstr "联系人"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Contact Details"
msgstr "联系人细节"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Contacts"
msgstr "联系人"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_composer.js:0
#, python-format
msgid "Could not save file <strong>%s</strong>"
msgstr "无法保存文件 <strong>%s</strong>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Country"
msgstr "国家"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_uid
msgid "Created by"
msgstr "创建人"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_date
msgid "Created on"
msgstr "创建时间"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr "目前每个人都可以视图此文件，点击限制内部员工使用。"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr "目前仅限于内部员工使用，点击可使每个人都能视图此文件。"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_res_config_settings__portal_allow_api_keys
msgid "Customer API Keys"
msgstr "客户 API 密钥"

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_mixin__access_url
msgid "Customer Portal URL"
msgstr "客户门户网址"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "Dear"
msgstr "尊敬的"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Delete"
msgstr "删除"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Description"
msgstr "描述"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "Details"
msgstr "细节"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Developer API Keys"
msgstr "开发者 API 密钥"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Discard"
msgstr "丢弃"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Documents"
msgstr "文档"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:0
#, python-format
msgid "Due in %1d days"
msgstr "在%1d天内到期"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:0
#, python-format
msgid "Due today"
msgstr "今天到期"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Email"
msgstr "Email"

#. module: portal
#: model:ir.model,name:portal.model_mail_thread
msgid "Email Thread"
msgstr "邮件主题"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Employees Only"
msgstr "仅限员工"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Enter a description of and purpose for the key."
msgstr "输入密钥的描述和用途."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Forgot password?"
msgstr "忘记密码？"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Grant Access"
msgstr "授予访问权限"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard
msgid "Grant Portal Access"
msgstr "授予门户访问"

#. module: portal
#: model:ir.actions.act_window,name:portal.partner_wizard_action
#: model:ir.actions.server,name:portal.partner_wizard_action_create_and_open
msgid "Grant portal access"
msgstr "授权门户访问"

#. module: portal
#: model:ir.model,name:portal.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid ""
"Here is your new API key, use it instead of a password for RPC access.\n"
"                Your login is still necessary for interactive usage."
msgstr ""
"这是您的新 API 密钥，使用它代替密码进行 RPC 访问.\n"
"                            您的登录仍然是交互式使用所必需的."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Home"
msgstr "首页"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__id
msgid "ID"
msgstr "ID"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Important:"
msgstr "重要提示:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Internal User"
msgstr "内部用户"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Internal Note"
msgstr "内部说明"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Internal notes are only displayed to internal users."
msgstr "内部注释仅向内部用户显示."

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "不合规EMail！ 请输入有效的EMail地址。"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "Invalid report type: %s"
msgstr "报告类型错误：%s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__welcome_message
msgid "Invitation Message"
msgstr "邀请消息"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_internal
msgid "Is Internal"
msgstr "是内部"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_portal
msgid "Is Portal"
msgstr "是门户"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid ""
"It is very important that this description be clear\n"
"                and complete,"
msgstr ""
"明确此描述非常重要\n"
"                并完成,"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share____last_update
#: model:ir.model.fields,field_description:portal.field_portal_wizard____last_update
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__login_date
msgid "Latest Authentication"
msgstr "最新认证"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Leave a comment"
msgstr "留下评论"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.res_config_settings_view_form
msgid "Let your customers create developer API keys"
msgstr "让您的客户创建开发人员 API 密钥"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__share_link
msgid "Link"
msgstr "链接"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid "Logout"
msgstr "注销"

#. module: portal
#: model:ir.model,name:portal.model_mail_message
msgid "Message"
msgstr "消息"

#. module: portal
#: code:addons/portal/models/mail_thread.py:0
#, python-format
msgid ""
"Model %(model_name)s does not support token signature, as it does not have "
"%(field_name)s field."
msgstr "模型%(model_name)s不支持token签名，因为它没有%(field_name)s字段。"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.my_account_link
msgid "My Account"
msgstr "我的账号"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Name"
msgstr "名称"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Name your key"
msgstr "命名您的密钥"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
#, python-format
msgid "New API Key"
msgstr "新 API 密钥"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New Password:"
msgstr "新密码："

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: model_terms:ir.ui.view,arch_db:portal.pager
#, python-format
msgid "Next"
msgstr "下一页"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__note
msgid "Note"
msgstr "笔记"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Odoo Logo"
msgstr "Odoo 徽标"

#. module: portal
#: code:addons/portal/models/res_users_apikeys_description.py:0
#, python-format
msgid "Only internal and portal users can create API keys"
msgstr "只有内部用户和门户用户才能创建 API 密钥"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "出错。请尝试重新加载页面并登录。"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__partner_ids
msgid "Partners"
msgstr "合作伙伴"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password Updated!"
msgstr "密码已更新！"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password:"
msgstr "密码："

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Phone"
msgstr "电话"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Please enter your password to confirm you own this account"
msgstr "请输入您的密码以确认您拥有此帐户"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
#, python-format
msgid "Portal Access Management"
msgstr "门户访问管理"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_url
msgid "Portal Access URL"
msgstr "门户访问网址"

#. module: portal
#: model:ir.model,name:portal.model_portal_mixin
msgid "Portal Mixin"
msgstr "门户Mixin"

#. module: portal
#: model:ir.model,name:portal.model_portal_share
msgid "Portal Sharing"
msgstr "门户分享"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard_user
msgid "Portal User Config"
msgstr "门户用户配置"

#. module: portal
#: model:mail.template,name:portal.mail_template_data_portal_welcome
msgid "Portal: User Invite"
msgstr "门户:用户邀请"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Powered by"
msgstr "技术提供"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid "Prev"
msgstr "上一个"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Previous"
msgstr "上一页"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_chatter.js:0
#, python-format
msgid "Published on %s"
msgstr "已在 %s 上发布"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Re-Invite"
msgstr "邀请"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__partner_ids
msgid "Recipients"
msgstr "收件人"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__resource_ref
msgid "Related Document"
msgstr "相关文档"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_id
msgid "Related Document ID"
msgstr "相关单据编号"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_model
msgid "Related Document Model"
msgstr "相关的单据模型"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Revoke Access"
msgstr "取消访问"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Scope"
msgstr "作用范围"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Search"
msgstr "搜索"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Security"
msgstr "安全"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Security Control"
msgstr "安全控制"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_token
msgid "Security Token"
msgstr "安全令牌"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"Select which contacts should belong to the portal in the list below.\n"
"                        The email address of each selected contact must be valid and unique.\n"
"                        If necessary, you can fix any contact's email address directly in the list."
msgstr ""
"请从下面的列表选择需要显示在门户页面的联系人。\n"
"    请确认每个联系人的email地址是有效且唯一的。\n"
"    如果需要，可以直接在列表中修改联系人的email地址。"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
#, python-format
msgid "Send"
msgstr "发送"

#. module: portal
#: model:ir.actions.act_window,name:portal.portal_share_action
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Share Document"
msgstr "共享文档"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_ir_ui_view__customize_show
msgid "Show As Optional Inherit"
msgstr "作为可选继承显示"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_sign_in
msgid "Sign in"
msgstr "登录"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_composer.js:0
#, python-format
msgid ""
"Some fields are required. Please make sure to write a message or attach a "
"document"
msgstr "有些字段是必须的。请务必写下信息或附上文件"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "必填字段不能为空。"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "State / Province"
msgstr "州/省"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Street"
msgstr "街道"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:0
#, python-format
msgid "Thank You!"
msgstr "谢谢！"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "The attachment %s cannot be removed because it is linked to a message."
msgstr "附件 %s 不可以删除，因为有消息在使用它。"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The attachment %s cannot be removed because it is not in a pending state."
msgstr "附件 %s 不可以删除，因为其状态为 等待处理。"

#. module: portal
#: code:addons/portal/controllers/mail.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "附件%s不存在或者您没有权限访问它。"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The attachment does not exist or you do not have the rights to access it."
msgstr "该附件不存在，或者您没有权利访问它。"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The contact \"%s\" does not have a valid email."
msgstr "联系人\"%s\"没有一个有效的电子邮件。"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The contact \"%s\" has the same email has an existing user (%s)."
msgstr "联系人\"%s\"的电子邮件与现有用户（%s）相同。"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr "该文档不存在或您无权访问。"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "The key cannot be retrieved later and provides"
msgstr "以后无法检索密钥，并提供"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "新密码与确认密码必须相同。"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "您提供的原密码是错误的，密码没有改变。"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The partner \"%s\" already has the portal access."
msgstr "伙伴\"%s\"已经拥有门户访问权。"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The partner \"%s\" has no portal access."
msgstr "伙伴\"%s\"没有门户访问权。"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid ""
"The template \"Portal: new user\" not found for sending email to the portal "
"user."
msgstr "没有找到用于向门户用户发送电子邮件的模板 \"门户：新用户\"。"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "There are no comments for now."
msgstr "目前没有任何评论。"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "This document does not exist."
msgstr "文件不存在。"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "This is a preview of the customer portal."
msgstr "这是客户门户的预览。"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This partner is linked to an internal User and already has access to the "
"Portal."
msgstr "这个合作伙伴与一个内部用户相联系，并且已经有了进入门户网站的权限。"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This text is included at the end of the email sent to new portal users."
msgstr "这段文字包括在发给新的门户用户的电子邮件的末尾。"

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_wizard__welcome_message
msgid "This text is included in the email sent to new users of the portal."
msgstr "这段文字会被包含在发给门户网站的新用户的email中。"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Toggle filters"
msgstr "切换筛选"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__user_id
msgid "User"
msgstr "用户"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__user_ids
msgid "Users"
msgstr "用户"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "VAT Number"
msgstr "增值税号码"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Verify New Password:"
msgstr "验证新密码："

#. module: portal
#: model:ir.model,name:portal.model_ir_ui_view
msgid "View"
msgstr "视图"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Visible"
msgstr "可见"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_department__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_employee__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_job__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_leave__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_leave_allocation__website_message_ids
#: model:ir.model.fields,field_description:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_channel__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,field_description:portal.field_note_note__website_message_ids
#: model:ir.model.fields,field_description:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_product__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_template__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_users__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: portal
#: model:ir.model.fields,help:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,help:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_department__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_employee__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_job__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_leave__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_leave_allocation__website_message_ids
#: model:ir.model.fields,help:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_channel__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,help:portal.field_note_note__website_message_ids
#: model:ir.model.fields,help:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_product__website_message_ids
#: model:ir.model.fields,help:portal.field_product_template__website_message_ids
#: model:ir.model.fields,help:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,help:portal.field_res_users__website_message_ids
msgid "Website communication history"
msgstr "网上沟通记录"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "What's this key for?"
msgstr "这个密钥(口令)用于?"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__wizard_id
msgid "Wizard"
msgstr "向导"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Write a message..."
msgstr "写点什么呢。。。"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Write down your key"
msgstr "写下您的密钥(口令)"

#. module: portal
#: code:addons/portal/wizard/portal_share.py:0
#: code:addons/portal/wizard/portal_share.py:0
#, python-format
msgid "You are invited to access %s"
msgstr "您被邀请访问  %s"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "您不能让任何密码为空。"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "You have been invited to access the following document:"
msgstr "您已被邀请访问以下文档："

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "You must be"
msgstr "您应该"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "You should first grant the portal access to the partner \"%s\"."
msgstr "您应该首先授予合作伙伴\"%s\"的门户权限。"

#. module: portal
#: model:mail.template,subject:portal.mail_template_data_portal_welcome
msgid "Your account at {{ object.user_id.company_id.name }}"
msgstr "您在{{ object.user_id.company_id.name }}的账户。"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_contact
msgid "Your contact"
msgstr "联系方式"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Zip / Postal Code"
msgstr "邮政代码"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "avatar"
msgstr "形象"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "comment"
msgstr "评论"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "comments"
msgstr "评论"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "full access"
msgstr "完全访问"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid ""
"it will be the only way to\n"
"                identify the key once created"
msgstr ""
"这将是唯一的途径\n"
"                创建密钥后识别密钥"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "logged in"
msgstr "已登录"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "odoo"
msgstr "odoo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "password"
msgstr "密码"

#. module: portal
#: model:ir.model,name:portal.model_res_users_apikeys_description
msgid "res.users.apikeys.description"
msgstr "res.users.apikeys.description"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "to post a comment."
msgstr "发表评论。"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "to your user account, it is very important to store it securely."
msgstr "对于您的用户帐户，安全地存储它非常重要。"
