<odoo>
    <template id="hr_timesheet.timesheet_table">
        <div class="row" style="margin-top:10px;">
            <div class="col-lg-12">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th class="align-middle"><span>Date</span></th>
                            <th class="align-middle"><span>Responsible</span></th>
                            <th class="align-middle"><span>Description</span></th>
                            <th class="align-middle" t-if="show_project"><span>Project</span></th>
                            <th class="align-middle" t-if="show_task"><span>Task</span></th>
                            <th class="text-right">
                                <span t-if="is_uom_day">Time Spent (Days)</span>
                                <span t-else="">Time Spent (Hours)</span>
                            </th>
                        </tr>
                   </thead>
                   <tbody>
                        <tr t-foreach="lines" t-as="line">
                            <td>
                               <span t-field="line.date"/>
                            </td>
                            <td>
                               <span t-field="line.user_id.partner_id.name"/>
                               <span t-if="not line.user_id.partner_id.name" t-field="line.employee_id"/>
                            </td>
                            <td >
                                <span t-field="line.name" t-options="{'widget': 'text'}"/>
                            </td>
                            <td t-if="show_project">
                                <span t-field="line.project_id.sudo().name"/>
                            </td>
                            <td t-if="show_task">
                                <t t-if="line.task_id"><span t-field="line.task_id.sudo().name"/></t>
                            </td>
                            <td class="text-right">
                                <span t-if="is_uom_day" t-esc="line._get_timesheet_time_day()" t-options="{'widget': 'timesheet_uom'}"/>
                                <span t-else="" t-field="line.unit_amount" t-options="{'widget': 'duration', 'digital': True, 'unit': 'hour', 'round': 'minute'}"/>
                            </td>
                        </tr>
                        <tr>
                            <t t-set="nbCols" t-value="4"/>
                            <t t-if="show_project" t-set="nbCols" t-value="nbCols + 1"/>
                            <t t-if="show_task" t-set="nbCols" t-value="nbCols + 1"/>
                            <td class="text-right" t-attf-colspan="{{nbCols}}">
                                <strong t-if="is_uom_day">
                                    <span style="margin-right: 15px;">Total (Days)</span>
                                    <t t-esc="lines._convert_hours_to_days(sum(lines.mapped('unit_amount')))" t-options="{'widget': 'timesheet_uom'}"/>
                                </strong>
                                <strong t-else="">
                                    <span style="margin-right: 15px;">Total (Hours)</span>
                                    <t t-esc="sum(lines.mapped('unit_amount'))" t-options="{'widget': 'duration', 'digital': True, 'unit': 'hour', 'round': 'minute'}"/>
                                </strong>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </template>
    <template id="report_timesheet">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="company" t-value="docs.mapped('project_id')[0].company_id if len(docs.mapped('project_id')) == 1 else docs.env.company"/>
                <t t-set="show_task" t-value="bool(docs.mapped('task_id'))"/>
                <t t-set="show_project" t-value="len(docs.mapped('project_id')) > 1"/>
                <div class="page">
                    <div class="oe_structure"/>
                    <div class="row" style="margin-top:10px;">
                        <div class="col-lg-12">
                            <h2>
                                <span>Timesheet Entries
                                    <t t-if="len(docs.mapped('project_id')) == 1">
                                        for the <t t-esc="docs.mapped('project_id')[0].name"/> Project
                                    </t>
                                </span>
                            </h2>
                        </div>
                    </div>
                    <t t-set='is_uom_day' t-value='docs._is_timesheet_encode_uom_day()'/>
                    <t t-set='lines' t-value='docs'/>
                    <t t-call="hr_timesheet.timesheet_table"/>
                    <div class="oe_structure"/>
                </div>
            </t>
        </t>
    </template>

    <record id="timesheet_report" model="ir.actions.report">
        <field name="name">Timesheet Entries</field>
        <field name="model">account.analytic.line</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">hr_timesheet.report_timesheet</field>
        <field name="report_file">report_timesheet</field>
        <field name="binding_model_id" ref="model_account_analytic_line"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Project Task Timesheet Report -->
    <template id="report_project_task_timesheet">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="show_Task" t-value="len(docs.mapped('id')) == 1"/>
                <div class="page">
                    <t t-foreach="docs" t-as="doc">
                        <div class="oe_structure"/>
                        <div class="row" style="margin-top:10px;">
                            <div class="col-lg-12">
                                <t t-if="doc.allow_timesheets and doc.timesheet_ids">
                                    <h1 class="mt-4 mb-4">
                                        <t t-if="not show_Task">
                                            Task: <span t-field="doc.name"/>
                                        </t>
                                    </h1>
                                    <h2>
                                        <span>Timesheet Entries
                                            <t t-if="show_Task">
                                                for the <t t-esc="doc.name"/> Task
                                            </t>
                                        </span>
                                    </h2>
                                    <t t-set='is_uom_day' t-value='doc.encode_uom_in_days'/>
                                    <t t-set='lines' t-value='doc.timesheet_ids'/>
                                    <t t-call="hr_timesheet.timesheet_table"/>
                                </t>
                            </div>
                        </div>
                    </t>
                </div>
            </t>
        </t>
    </template>

    <record id="timesheet_report_task" model="ir.actions.report">
        <field name="name">Timesheet Entries</field>
        <field name="model">project.task</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">hr_timesheet.report_project_task_timesheet</field>
        <field name="report_file">report_timesheet_task</field>
        <field name="binding_model_id" ref="model_project_task"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Project Timesheet Report -->
    <template id="report_timesheet_project">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="company" t-value="docs.company_id[0] if len(docs) == 1 else docs.env.company"/>
                <t t-set="show_task" t-value="bool(docs.task_ids)"/>
                <t t-set="show_project" t-value="len(docs) > 1"/>

                <div class="page">
                    <div class="oe_structure"/>
                    <div class="row" style="margin-top:10px;">
                        <div class="col-lg-12">
                            <h2>
                                <span>Timesheet Entries
                                    <t t-if="not show_project">
                                        for the <t t-esc="docs.name"/> Project
                                    </t>
                                </span>
                            </h2>
                        </div>
                    </div>
                    <t t-set="is_uom_day" t-value="docs.timesheet_ids and docs.timesheet_ids[0]._is_timesheet_encode_uom_day()"/>
                    <t t-set="lines" t-value="docs.timesheet_ids"/>
                    <t t-call="hr_timesheet.timesheet_table"/>
                    <div class="oe_structure"/>
                </div>
            </t>
        </t>
    </template>

    <record id="timesheet_report_project" model="ir.actions.report">
        <field name="name">Timesheet Entries</field>
        <field name="model">project.project</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">hr_timesheet.report_timesheet_project</field>
        <field name="report_file">report_timesheet_project</field>
        <field name="binding_model_id" ref="model_project_project"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
