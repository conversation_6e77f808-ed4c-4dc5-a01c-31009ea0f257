# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_de_sale
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-22 09:41+0000\n"
"PO-Revision-Date: 2021-07-22 09:41+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Customer Reference"
msgstr ""

#. module: l10n_de_sale
#: model:ir.model.fields,field_description:l10n_de_sale.field_sale_order__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Expiration"
msgstr ""

#. module: l10n_de_sale
#: model:ir.model.fields,field_description:l10n_de_sale.field_sale_order__id
msgid "ID"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Incoterm"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Invoicing Address:"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Invoicing and Shipping Address:"
msgstr ""

#. module: l10n_de_sale
#: model:ir.model.fields,field_description:l10n_de_sale.field_sale_order__l10n_de_addresses
msgid "L10N De Addresses"
msgstr ""

#. module: l10n_de_sale
#: model:ir.model.fields,field_description:l10n_de_sale.field_sale_order__l10n_de_document_title
msgid "L10N De Document Title"
msgstr ""

#. module: l10n_de_sale
#: model:ir.model.fields,field_description:l10n_de_sale.field_sale_order__l10n_de_template_data
msgid "L10N De Template Data"
msgstr ""

#. module: l10n_de_sale
#: model:ir.model.fields,field_description:l10n_de_sale.field_sale_order____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Order Date"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Order No."
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Pro Forma Invoice"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Quotation"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Quotation Date"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Quotation No."
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#: model:ir.model,name:l10n_de_sale.model_sale_order
#, python-format
msgid "Sales Order"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Salesperson"
msgstr ""

#. module: l10n_de_sale
#: code:addons/l10n_de_sale/models/sale.py:0
#, python-format
msgid "Shipping Address:"
msgstr ""
