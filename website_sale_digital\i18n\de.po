# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_digital
# 
# Translators:
# Friederi<PERSON>ling-Nesselbosch, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:0
#: code:addons/website_sale_digital/models/product.py:0
#, python-format
msgid "Add attachments for this digital product"
msgstr "Anhänge für dieses digitale Produkt hinzufügen"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_ir_attachment
msgid "Attachment"
msgstr "Dateianhang"

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:0
#: code:addons/website_sale_digital/models/product.py:0
#, python-format
msgid "Digital Attachments"
msgstr "Digitale Anhänge"

#. module: website_sale_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.product_product_view_form_inherit_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.product_template_view_form_inherit_digital
msgid "Digital Files"
msgstr "Digitale Dateien"

#. module: website_sale_digital
#: model:ir.model.fields,field_description:website_sale_digital.field_ir_attachment__product_downloadable
msgid "Downloadable from product portal"
msgstr "Kann vom Produktportal heruntergeladen werden."

#. module: website_sale_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.sale_order_portal_content_inherit_website_sale_digital
msgid "Downloads"
msgstr "Downloads"

#. module: website_sale_digital
#: model:ir.model.fields,field_description:website_sale_digital.field_product_product__attachment_count
#: model:ir.model.fields,field_description:website_sale_digital.field_product_template__attachment_count
msgid "File"
msgstr "Datei"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_account_move_line
msgid "Journal Item"
msgstr "Buchungszeile"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_product_product
msgid "Product"
msgstr "Produkt"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_product_template
msgid "Product Template"
msgstr "Produktvorlage"

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:0
#: code:addons/website_sale_digital/models/product.py:0
#, python-format
msgid ""
"The attached files are the ones that will be purchased and sent to the "
"customer."
msgstr ""
"Die angehängten Dateien sind diejenigen, die gekauft und an den Kunden "
"geschickt werden."

#. module: website_sale_digital
#: model:product.template,uom_name:website_sale_digital.product_1
msgid "Units"
msgstr "Einheiten"

#. module: website_sale_digital
#: model:product.template,name:website_sale_digital.product_1
msgid "eBook: Office Renovation for Dummies"
msgstr "eBook: Bürorenovierung für Dummies"
